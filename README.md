<!--
 * @Description: YBB采销后台管理系统
 * @Version: 1.0
 * @Author: Jeson
 * @Date: 2025-01-06
-->

# YBB-ScmAdmin 采销后台管理系统

## 项目简介
YBB-ScmAdmin 是一个基于 Vue 2.x + Ant Design Vue 的采销后台管理系统，采用前后端分离架构。

## 技术栈
- 核心框架：Vue 2.x
- UI 框架：Ant Design Vue
- 状态管理：Vuex
- 路由管理：Vue Router
- HTTP 工具：Axios
- 存储方案：vue-ls
- 富文本编辑：vue-html5-editor
- 图片预览：v-viewer

## 项目结构
  ├── src/
│ ├── api/ # API 接口
│ ├── assets/ # 静态资源
│ │ ├── iconfont/ # 图标字体
│ │ ├── js/ # JavaScript 文件
│ │ └── less/ # 样式文件
│ ├── components/ # 公共组件
│ │ └── layouts/ # 布局组件
│ ├── router/ # 路由配置
│ ├── store/ # Vuex状态管理
│ ├── utils/ # 工具函数
│ ├── views/ # 页面文件
│ │ └── user/ # 用户相关页面
│ ├── App.vue # 根组件
│ └── main.js # 入口文件
├── public/ # 公共资源
│ └── index.html # HTML 模板
└── vue.config.js # Vue 配置文件  


## 项目运行
1. 安装依赖：`npm install`
2. 启动开发环境：`npm run dev`
3. 打包项目：`npm run build`
4. 启动生产环境：`npm run start`  

## 项目部署
1. 将打包后的文件部署到服务器上
2. 配置 Nginx 反向代理，将请求转发到对应的服务器端口
3. 启动 Nginx 服务

## 项目截图
![登录页面](./public/images/login.png)
![主页](./public/images/home.png)

## 注意事项
1. 请确保在运行项目之前已经安装了 Node.js 和 npm
2. 请确保在运行项目之前已经安装了 Vue CLI
3. 请确保在运行项目之前已经安装了 Ant Design Vue
4. 请确保在运行项目之前已经安装了 Axios
5. 请确保在运行项目之前已经安装了 vue-ls
6. 请确保在运行项目之前已经安装了 vue-html5-editor
7. 请确保在运行项目之前已经安装了 v-viewer
8. 请确保在运行项目之前已经安装了 vue-router
9. 请确保在运行项目之前已经安装了 vuex
10. 请确保在运行项目之前已经安装了 webpack
11. 请确保在运行项目之前已经安装了 babel
12. 请确保在运行项目之前已经安装了 eslint
13. 请确保在运行项目之前已经安装了 stylelint
14. 请确保在运行项目之前已经安装了 postcss
15. 请确保在运行项目之前已经安装了 autoprefixer
16. 请确保在运行项目之前已经安装了 less
17. 请确保在运行项目之前已经安装了 postcss-loader
18. 请确保在运行项目之前已经安装了 css-loader
19. 请确保在运行项目之前已经安装了 style-loader
20. 请确保在运行项目之前已经安装了 file-loader
  
## 主要功能
- 用户认证
  - 登录/登出
  - 权限管理
- 系统管理
  - 用户管理
  - 角色管理
  - 菜单管理
- 业务功能
  - 采购管理
  - 销售管理
  - 库存管理

## 开发环境
- Node.js >= 12
- npm >= 6

## 快速开始
  
## 项目配置
### Vue 配置
项目的主要配置在 vue.config.js 中，包括：
- 别名配置
- 构建优化
- CSS 预处理器配置
- 开发服务器配置
- Source Map配置
  - 开发环境使用 devtool: 'source-map' 以支持断点调试
  - 生产环境设置为 none 以优化打包体积

> 特别说明：如果在开发环境下无法进入断点，请检查 vue.config.js 中的 devtool 配置是否正确设置为 'source-map'。这对于代码调试至关重要。

### 环境变量
- .env.development - 开发环境配置
- .env.production - 生产环境配置

## 代码规范
- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 遵循 Vue 官方风格指南

## 目录说明
- api: 接口请求统一管理
- assets: 静态资源文件
- components: 公共组件
- router: 路由配置
- store: 状态管理
- utils: 工具函数
- views: 页面文件

## 构建优化
项目使用了以下优化策略：
- 路由懒加载
- 组件按需加载
- 生产环境关闭 sourceMap
- 合理的 chunk 分割

## 维护者
- [@Jeson](https://github.com/Jeson)

## 版本历史
- v1.0.0 (2025-01-06)
  - 初始版本发布
  - 基础功能实现

## 许可证
[MIT](LICENSE)
  