#!/usr/bin/env node

/**
 * YYL页面构建器 - Cursor AI 任务执行器
 * 用于辅助Cursor AI执行开发任务和跟踪进度
 */

const fs = require('fs');
const path = require('path');

class TaskExecutor {
  constructor() {
    this.projectRoot = '/Users/<USER>/Documents/ybb/ybb-scmadmin';
    this.planFile = path.join(process.cwd(), 'cursor-development-plan.md');
    this.tasks = this.loadTasks();
  }

  // 加载任务列表
  loadTasks() {
    return [
      {
        id: '1.1',
        name: '创建项目基础结构',
        stage: 1,
        priority: 'P0',
        estimatedDays: 2,
        status: 'pending',
        files: [
          'src/views/page-builder/README.md',
          'src/views/page-builder/index.vue',
          'src/views/page-builder/router.js'
        ],
        directories: [
          'src/views/page-builder/components/Designer',
          'src/views/page-builder/components/PropertyEditors',
          'src/views/page-builder/components/RenderEngine',
          'src/views/page-builder/components/Common',
          'src/views/page-builder/components/PageManager',
          'src/views/page-builder/store/modules',
          'src/views/page-builder/utils',
          'src/views/page-builder/configs',
          'src/views/page-builder/mixins',
          'src/views/page-builder/styles',
          'src/views/page-builder/api',
          'src/views/page-builder/docs'
        ]
      },
      {
        id: '1.2',
        name: 'YYL组件扫描器开发',
        stage: 1,
        priority: 'P0',
        estimatedDays: 3,
        status: 'pending',
        files: [
          'src/views/page-builder/utils/componentScanner.js',
          'src/views/page-builder/configs/yyl-components.js'
        ],
        dependencies: ['1.1']
      },
      {
        id: '1.3',
        name: '设计器主界面开发',
        stage: 1,
        priority: 'P0',
        estimatedDays: 4,
        status: 'pending',
        files: [
          'src/views/page-builder/components/Designer/index.vue',
          'src/views/page-builder/components/Designer/Toolbar.vue',
          'src/views/page-builder/styles/designer.less'
        ],
        dependencies: ['1.1']
      },
      {
        id: '1.4',
        name: '状态管理配置',
        stage: 1,
        priority: 'P0',
        estimatedDays: 2,
        status: 'pending',
        files: [
          'src/views/page-builder/store/index.js',
          'src/views/page-builder/store/modules/designer.js',
          'src/views/page-builder/store/modules/components.js',
          'src/views/page-builder/store/modules/pages.js'
        ],
        dependencies: ['1.1']
      },
      {
        id: '2.1',
        name: 'YYL组件面板开发',
        stage: 2,
        priority: 'P0',
        estimatedDays: 3,
        status: 'pending',
        files: [
          'src/views/page-builder/components/Designer/ComponentPanel.vue'
        ],
        dependencies: ['1.2', '1.3', '1.4']
      },
      {
        id: '2.2',
        name: '画布区域开发',
        stage: 2,
        priority: 'P0',
        estimatedDays: 5,
        status: 'pending',
        files: [
          'src/views/page-builder/components/Designer/Canvas.vue',
          'src/views/page-builder/mixins/DragMixin.js'
        ],
        dependencies: ['1.3', '1.4']
      },
      {
        id: '2.3',
        name: 'YYL组件渲染器开发',
        stage: 2,
        priority: 'P0',
        estimatedDays: 4,
        status: 'pending',
        files: [
          'src/views/page-builder/components/RenderEngine/ComponentRenderer.vue'
        ],
        dependencies: ['1.2', '1.4']
      },
      {
        id: '2.4',
        name: '属性面板开发',
        stage: 2,
        priority: 'P1',
        estimatedDays: 4,
        status: 'pending',
        files: [
          'src/views/page-builder/components/Designer/PropertyPanel.vue',
          'src/views/page-builder/components/PropertyEditors/BasicEditor.vue',
          'src/views/page-builder/components/PropertyEditors/FormFieldsEditor.vue',
          'src/views/page-builder/components/PropertyEditors/PermissionEditor.vue'
        ],
        dependencies: ['1.3', '1.4']
      }
    ];
  }

  // 获取当前可执行的任务
  getAvailableTasks() {
    return this.tasks.filter(task => {
      if (task.status !== 'pending') return false;
      
      // 检查依赖任务是否完成
      if (task.dependencies) {
        return task.dependencies.every(depId => {
          const depTask = this.tasks.find(t => t.id === depId);
          return depTask && depTask.status === 'completed';
        });
      }
      
      return true;
    });
  }

  // 获取下一个推荐任务
  getNextTask() {
    const availableTasks = this.getAvailableTasks();
    if (availableTasks.length === 0) return null;

    // 按优先级和阶段排序
    availableTasks.sort((a, b) => {
      if (a.stage !== b.stage) return a.stage - b.stage;
      if (a.priority !== b.priority) {
        const priorityOrder = { 'P0': 0, 'P1': 1, 'P2': 2 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      }
      return a.id.localeCompare(b.id);
    });

    return availableTasks[0];
  }

  // 创建目录结构
  createDirectories(task) {
    if (!task.directories) return;

    console.log(`创建目录结构 for 任务 ${task.id}...`);
    
    task.directories.forEach(dir => {
      const fullPath = path.join(this.projectRoot, dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
        console.log(`✅ 创建目录: ${dir}`);
      } else {
        console.log(`⚠️  目录已存在: ${dir}`);
      }
    });
  }

  // 检查文件是否存在
  checkFiles(task) {
    if (!task.files) return { existing: [], missing: [] };

    const existing = [];
    const missing = [];

    task.files.forEach(file => {
      const fullPath = path.join(this.projectRoot, file);
      if (fs.existsSync(fullPath)) {
        existing.push(file);
      } else {
        missing.push(file);
      }
    });

    return { existing, missing };
  }

  // 生成任务模板文件
  generateTemplateFiles(task) {
    if (!task.files) return;

    console.log(`生成模板文件 for 任务 ${task.id}...`);

    task.files.forEach(file => {
      const fullPath = path.join(this.projectRoot, file);
      
      if (fs.existsSync(fullPath)) {
        console.log(`⚠️  文件已存在: ${file}`);
        return;
      }

      const template = this.getFileTemplate(file, task);
      if (template) {
        const dir = path.dirname(fullPath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
        
        fs.writeFileSync(fullPath, template);
        console.log(`✅ 创建文件: ${file}`);
      }
    });
  }

  // 获取文件模板
  getFileTemplate(filePath, task) {
    const fileName = path.basename(filePath);
    const ext = path.extname(filePath);

    if (ext === '.vue') {
      return this.getVueTemplate(fileName, task);
    } else if (ext === '.js') {
      return this.getJsTemplate(fileName, task);
    } else if (ext === '.less') {
      return this.getLessTemplate(fileName, task);
    } else if (ext === '.md') {
      return this.getMdTemplate(fileName, task);
    }

    return null;
  }

  // Vue组件模板
  getVueTemplate(fileName, task) {
    const componentName = fileName.replace('.vue', '');
    
    return `<template>
  <div class="${componentName.toLowerCase()}">
    <!-- ${task.name} - ${componentName} -->
    <h3>{{ title }}</h3>
    <p>这是 ${componentName} 组件的模板</p>
  </div>
</template>

<script>
export default {
  name: '${componentName}',
  data() {
    return {
      title: '${componentName}'
    }
  },
  methods: {
    // TODO: 实现具体功能
  }
}
</script>

<style lang="less" scoped>
.${componentName.toLowerCase()} {
  // TODO: 添加样式
}
</style>
`;
  }

  // JavaScript模板
  getJsTemplate(fileName, task) {
    if (fileName.includes('Scanner')) {
      return `/**
 * YYL组件扫描器
 * 任务: ${task.name}
 */

class YYLComponentScanner {
  constructor() {
    this.components = new Map()
    this.scanPath = '/src/components/yyl'
  }

  // 扫描YYL组件库
  async scanComponents() {
    const categories = ['business', 'form', 'table', 'components']
    
    for (const category of categories) {
      await this.scanCategory(category)
    }
    
    return this.components
  }

  // 扫描特定分类的组件
  async scanCategory(category) {
    // TODO: 实现组件扫描逻辑
    console.log(\`扫描分类: \${category}\`)
  }
}

export default new YYLComponentScanner()
`;
    }

    return `/**
 * ${task.name}
 * 文件: ${fileName}
 */

// TODO: 实现具体功能

export default {
  // TODO: 添加导出内容
}
`;
  }

  // Less样式模板
  getLessTemplate(fileName, task) {
    return `/**
 * ${task.name} - 样式文件
 * 文件: ${fileName}
 */

// TODO: 添加样式定义
`;
  }

  // Markdown模板
  getMdTemplate(fileName, task) {
    if (fileName === 'README.md') {
      return `# YYL页面构建器

## 概述
基于现有YYL组件库的可视化页面构建系统

## 功能特性
- 🎨 可视化拖拽设计
- 🧩 基于现有YYL组件库
- 🔐 完整的权限控制
- 📱 响应式设计支持

## 开发状态
当前任务: ${task.name}

## 使用方式
访问路径: \`/page-builder\`

## 技术栈
- Vue 2.x
- Ant Design Vue
- Vuex
- 现有YYL组件库
`;
    }

    return `# ${task.name}

## 任务描述
${task.name}

## 实现状态
- [ ] 待实现

## 相关文件
${task.files ? task.files.map(f => `- ${f}`).join('\n') : ''}
`;
  }

  // 开始任务
  startTask(taskId) {
    const task = this.tasks.find(t => t.id === taskId);
    if (!task) {
      console.error(`❌ 任务 ${taskId} 不存在`);
      return false;
    }

    if (task.status !== 'pending') {
      console.error(`❌ 任务 ${taskId} 状态不是待开始: ${task.status}`);
      return false;
    }

    // 检查依赖
    if (task.dependencies) {
      const uncompletedDeps = task.dependencies.filter(depId => {
        const depTask = this.tasks.find(t => t.id === depId);
        return !depTask || depTask.status !== 'completed';
      });

      if (uncompletedDeps.length > 0) {
        console.error(`❌ 任务 ${taskId} 的依赖任务未完成: ${uncompletedDeps.join(', ')}`);
        return false;
      }
    }

    console.log(`🚀 开始任务 ${taskId}: ${task.name}`);
    
    // 创建目录结构
    this.createDirectories(task);
    
    // 生成模板文件
    this.generateTemplateFiles(task);
    
    // 更新任务状态
    task.status = 'in-progress';
    task.startTime = new Date().toISOString();
    
    console.log(`✅ 任务 ${taskId} 初始化完成`);
    console.log(`📋 需要实现的文件:`);
    if (task.files) {
      task.files.forEach(file => console.log(`   - ${file}`));
    }
    
    return true;
  }

  // 显示项目状态
  showStatus() {
    console.log('\n📊 YYL页面构建器开发状态\n');
    
    const stages = {};
    this.tasks.forEach(task => {
      if (!stages[task.stage]) {
        stages[task.stage] = { total: 0, completed: 0, inProgress: 0, pending: 0 };
      }
      stages[task.stage].total++;
      if (task.status === 'completed') stages[task.stage].completed++;
      else if (task.status === 'in-progress') stages[task.stage].inProgress++;
      else stages[task.stage].pending++;
    });

    Object.keys(stages).forEach(stage => {
      const s = stages[stage];
      const progress = Math.round((s.completed / s.total) * 100);
      console.log(`阶段 ${stage}: ${progress}% (${s.completed}/${s.total}) - 进行中: ${s.inProgress}, 待开始: ${s.pending}`);
    });

    console.log('\n🎯 下一个推荐任务:');
    const nextTask = this.getNextTask();
    if (nextTask) {
      console.log(`   ${nextTask.id}: ${nextTask.name} (预计${nextTask.estimatedDays}天)`);
    } else {
      console.log('   暂无可执行任务');
    }
  }
}

// 命令行接口
if (require.main === module) {
  const executor = new TaskExecutor();
  const command = process.argv[2];
  const taskId = process.argv[3];

  switch (command) {
    case 'status':
      executor.showStatus();
      break;
    case 'start':
      if (!taskId) {
        console.error('请指定任务ID: node cursor-task-executor.js start 1.1');
        process.exit(1);
      }
      executor.startTask(taskId);
      break;
    case 'next':
      const nextTask = executor.getNextTask();
      if (nextTask) {
        console.log(`下一个任务: ${nextTask.id} - ${nextTask.name}`);
      } else {
        console.log('暂无可执行任务');
      }
      break;
    default:
      console.log('使用方法:');
      console.log('  node cursor-task-executor.js status    # 显示项目状态');
      console.log('  node cursor-task-executor.js start 1.1 # 开始指定任务');
      console.log('  node cursor-task-executor.js next      # 显示下一个推荐任务');
  }
}

module.exports = TaskExecutor;
