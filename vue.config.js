/*
 * @Description: <PERSON><PERSON> Create File
 * @Version: 1.0
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-21 10:43:13
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2025-01-06 13:53:47
 */
const path = require('path');
const webpack = require('webpack')

function resolve(dir) {
  return path.join(__dirname, dir)
}

function isProd() {
  return process.env.NODE_ENV === 'production'
}

module.exports = {
  publicPath: './',
  outputDir: 'dist',
  assetsDir: './static',
  productionSourceMap: false,
  
  configureWebpack: {
    resolve: {
      alias: {
        vue$: "vue/dist/vue.esm.js",
        "@api": path.resolve(__dirname, "./src/api"),
        "@utils": path.resolve(__dirname, "./src/utils"),
        '@': path.resolve(__dirname, 'src'),
        '@config': path.resolve(__dirname, 'src/config')
      }
    }
  },

  chainWebpack: config => {
    if (isProd()) {
      config.merge({
        optimization: {
          splitChunks: {
            chunks: 'all',
            minSize: 100 * 1024,
            maxSize: 900 * 1024,
            minChunks: 1,
            maxAsyncRequests: 5,
            maxInitialRequests: 3,
            automaticNameDelimiter: '~',
            name: true,
            cacheGroups: {
              viewsSrc: {
                name: 'chunk-src',
                test: (module) => /\\src\\/.test(module.context),
                priority: 1
              },
              vendors: {
                name(module) {
                  const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1]
                  return `npm.${packageName.replace('@', '')}`
                },
                test: /[\\/]node_modules[\\/]/,
                priority: -10
              },
              default: {
                name: 'chunk-other',
                minChunks: 2,
                priority: -20,
                reuseExistingChunk: true
              }
            }
          }
        }
      });
    }

    config.resolve.alias
      .set('@$', resolve('src'))
      .set('@api', resolve('src/api'))
      .set('@assets', resolve('src/assets'))
      .set('@comp', resolve('src/components'))
      .set('@views', resolve('src/views'))
      .set('@layout', resolve('src/layout'))
      .set('@static', resolve('src/static'))

    config.plugins.delete("prefetch")
  },

  css: {
    sourceMap: false,
    loaderOptions: {
      less: {
        javascriptEnabled: true
      }
    }
  },

  lintOnSave: undefined
}