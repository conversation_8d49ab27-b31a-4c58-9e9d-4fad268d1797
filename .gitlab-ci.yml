# region 通用配置
default:
  interruptible: false

# 阶段
stages:
  - install
  - build
  - deploy
  - push-images
  - deploy-k8s

# 变量
variables:
  DEPLOY_PATH: ybb-scmadmin-web

  DEPLOY_YAML_NAME: v1.ybb-test.deployment.yaml

  KUBECONFIG: /etc/deploy/config

  PROJECT_DIR: /data/wwwroot3.0
  PROJECT_DIR_PRO: /data/wwwroot-pro

  PROJECT_DIR_NEW: /data/three
  PROJECT_DIR_NEW_PRO: /data/three-pro

  STORE: yyy_frontend
  STORE_PRO: ybb_frontend_pro
# endregion

# region 安装依赖
install:
  when: "manual"
  stage: install
  # 此处的tags必须填入之前注册时自定的tag
  tags:
    - yyy3.0_frontend
  cache:
    paths:
      - node_modules/
  # 执行脚本
  script:
    - npm config get registry
    - ls
    - pwd
    - yarn install
  retry: 0
  interruptible: true
# endregion

# region 测试环境
# 打包项目
build-test:
  stage: build
  when: "manual"
  before_script:
    - echo "======== before_script  ========"
    - echo "用户的ID  $GITLAB_USER_ID"
    - echo "电子邮件  $GITLAB_USER_EMAIL"
    - echo "用户名  $GITLAB_USER_LOGIN"
    - echo "真实姓名  $GITLAB_USER_NAME"
  tags:
    - yyy3.0_frontend
  cache:
    paths:
      - node_modules/
    policy: pull
  script:
    - nvm use 16.14.0
    - node -v
    - yarn build-test
    - nvm use 14.18.0
    - node -v
    - sleep 1s
  retry: 0
  interruptible: true
  # 将此阶段产物传递至下一阶段
  artifacts:
    paths:
      - dist/

# 部署项目
deploy-test:
  stage: deploy
  needs: 
    - build-test
  before_script:
    - echo "======== before_script  ========"
    - echo "用户的ID  $GITLAB_USER_ID"
    - echo "电子邮件  $GITLAB_USER_EMAIL"
    - echo "用户名  $GITLAB_USER_LOGIN"
    - echo "真实姓名  $GITLAB_USER_NAME"
  tags:
    - yyy3.0_frontend
  script:
    # 复制打包后的文件至网站根目录，目录请根据服务器实际情况填写
    - rm -rf $PROJECT_DIR/$DEPLOY_PATH
    - mkdir -p $PROJECT_DIR/$DEPLOY_PATH
    - echo "======== build ========"
    - ls
    - pwd
    - cp -r ./dist $PROJECT_DIR/$DEPLOY_PATH
    - cd $PROJECT_DIR/$DEPLOY_PATH
    - echo "======== project ========"
    - ls
    - sleep 1s
  inherit:
    default:
      - interruptible
  dependencies:
    - build-test

# 打包镜像
push-images-test:
  stage: push-images
  needs: 
    - deploy-test
  tags:
    - yyy3.0_frontend
  before_script:
    - echo "======== before_script  ========"
    - echo "用户的ID  $GITLAB_USER_ID"
    - echo "电子邮件  $GITLAB_USER_EMAIL"
    - echo "用户名  $GITLAB_USER_LOGIN"
    - echo "真实姓名  $GITLAB_USER_NAME"
    - docker login --username $REGISTRY_USER --password $REGISTRY_PASSWORD $REGISTRY_URL #登录仓库
    ##----------------env----------------##
    - echo $CI_PIPELINE_ID #流水线ID
    - export VERSION=$CI_PIPELINE_ID
    - export BUILD=$REGISTRY_URL/$STORE/$DEPLOY_PATH:$VERSION
    - echo $BUILD
    ##----------------env----------------##
  script:
    #- rm -rf $PROJECT_DIR/$PROJECT_NAME
    #- mkdir $PROJECT_DIR/$PROJECT_NAME
    - echo "############ prepare############"
    #- ls
    #- pwd
    #- cp -r ./dist $PROJECT_DIR/$PROJECT_NAME
    - rm -rf $PROJECT_DIR_NEW/$DEPLOY_PATH #删除上一版本代码目录
    # - mkdir -p $PROJECT_DIR_NEW/$DEPLOY_PATH/dist
    - cp -rf $PROJECT_DIR/$DEPLOY_PATH $PROJECT_DIR_NEW/$DEPLOY_PATH #复制项目文件到新目录
    - cp -rf /data/images/demo/* $PROJECT_DIR_NEW/$DEPLOY_PATH #复制docker build所需文件到新目录
    - cd $PROJECT_DIR_NEW/$DEPLOY_PATH
    - pwd
    - ls
    - echo "############ build docker images############"
    - docker build -t $BUILD .
    - docker push $BUILD
    - docker images |grep "$DEPLOY_PATH"
  after_script:
    - sleep 1s
  dependencies:
    - deploy-test
  inherit:
    default:
      - interruptible

#部署到测试kubesphere集群
deploy-k8s:
  stage: deploy-k8s
  needs: 
    - push-images-test
  image: registry.cn-hangzhou.aliyuncs.com/haoshuwei24/kubectl:1.16.6
  tags:
    - k8s2-runner-ybb
  before_script:
    - echo "======== before_script  ========"
    - echo "用户的ID  $GITLAB_USER_ID"
    - echo "电子邮件  $GITLAB_USER_EMAIL"
    - echo "用户名  $GITLAB_USER_LOGIN"
    - echo "真实姓名  $GITLAB_USER_NAME"
    ##----------------env----------------##
    - export FILE=$DEPLOY_PATH-$DEPLOY_YAML_NAME
    - echo $CI_PIPELINE_ID
    - export VERSION=$CI_PIPELINE_ID
    ##----------------env----------------##
  script:
    - mkdir -p /etc/deploy
    - echo $kube2_ybb_config |base64 -d > $KUBECONFIG # 配置连接kubesphere集群的config文件
    - if [ ! -f $FILE ]; then echo "$FILE file not found" && exit 1; fi
    - sed -i "s/IMAGE_TAG/$VERSION/g" $FILE #动态替换部署文件中的镜像tag
    - cat $FILE
    - kubectl apply -f $FILE || exit 1
    - echo "部署完毕,请检查kubesphere工作负载是否正常运行!"
# endregion

# region 打包正式环境
build-pro:
  stage: build
  when: "manual"
  before_script:
    - echo "======== before_script  ========"
    - echo "用户的ID  $GITLAB_USER_ID"
    - echo "电子邮件  $GITLAB_USER_EMAIL"
    - echo "用户名  $GITLAB_USER_LOGIN"
    - echo "真实姓名  $GITLAB_USER_NAME"
  only:
    - main
  tags:
    - yyy3.0_frontend
  cache:
    paths:
      - node_modules/
    policy: pull
  script:
    - yarn build-pro-app
    - sleep 1s
  retry: 0
  interruptible: true
  # 将此阶段产物传递至下一阶段
  artifacts:
    paths:
      - dist/

# 部署项目
deploy-pro:
  stage: deploy
  needs: 
    - build-pro
  before_script:
    - echo "======== before_script  ========"
    - echo "用户的ID  $GITLAB_USER_ID"
    - echo "电子邮件  $GITLAB_USER_EMAIL"
    - echo "用户名  $GITLAB_USER_LOGIN"
    - echo "真实姓名  $GITLAB_USER_NAME"
  only:
    - main
  tags:
    - yyy3.0_frontend
  script:
    # 复制打包后的文件至网站根目录，目录请根据服务器实际情况填写
    - rm -rf $PROJECT_DIR_PRO/$DEPLOY_PATH
    - mkdir -p $PROJECT_DIR_PRO/$DEPLOY_PATH
    - echo "======== build ========"
    - ls
    - pwd
    - cp -r ./dist $PROJECT_DIR_PRO/$DEPLOY_PATH
    - cd $PROJECT_DIR_PRO/$DEPLOY_PATH
    - echo "======== project ========"
    - ls
    - sleep 1s
  inherit:
    default:
      - interruptible
  dependencies:
    - build-pro

push-images-pro:
  stage: push-images
  needs: 
    - deploy-pro
  only:
    - main
  tags:
    - yyy3.0_frontend
  before_script:
    - echo "======== before_script  ========"
    - echo "用户的ID  $GITLAB_USER_ID"
    - echo "电子邮件  $GITLAB_USER_EMAIL"
    - echo "用户名  $GITLAB_USER_LOGIN"
    - echo "真实姓名  $GITLAB_USER_NAME"
    - docker login --username $REGISTRY_USER --password $REGISTRY_PASSWORD $REGISTRY_URL #登录仓库
    ##----------------env----------------##
    - echo $CI_PIPELINE_ID #流水线ID
    - export VERSION=$CI_PIPELINE_ID
    - export BUILD=$REGISTRY_URL/$STORE_PRO/$DEPLOY_PATH:v1.0.0
    - echo $BUILD
    ##----------------env----------------##
  script:
    #- rm -rf $PROJECT_DIR/$PROJECT_NAME
    #- mkdir $PROJECT_DIR/$PROJECT_NAME
    - echo "############ prepare############"
    #- ls
    #- pwd
    #- cp -r ./dist $PROJECT_DIR/$PROJECT_NAME
    - rm -rf $PROJECT_DIR_NEW_PRO/$DEPLOY_PATH #删除上一版本代码目录
    # - mkdir -p $PROJECT_DIR_NEW/$DEPLOY_PATH/dist
    - cp -rf $PROJECT_DIR_PRO/$DEPLOY_PATH $PROJECT_DIR_NEW_PRO/$DEPLOY_PATH #复制项目文件到新目录
    - cp -rf /data/images/demo/* $PROJECT_DIR_NEW_PRO/$DEPLOY_PATH #复制docker build所需文件到新目录
    - cd $PROJECT_DIR_NEW_PRO/$DEPLOY_PATH
    - pwd
    - ls
    - echo "############ build docker images############"
    - docker build -t $BUILD .
    - docker push $BUILD
    - docker images |grep "$DEPLOY_PATH"
  after_script:
    - sleep 1s
  dependencies:
    - deploy-pro
  inherit:
    default:
      - interruptible
