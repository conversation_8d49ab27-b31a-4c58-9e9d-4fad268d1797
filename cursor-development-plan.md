# YYL页面构建器 - Cursor AI 开发规划

## 项目概述
基于现有Vue 2 + Ant Design Vue + YYL组件库项目，开发可视化页面构建系统。

## 开发环境配置
- **项目路径**: `/Users/<USER>/Documents/ybb/ybb-scmadmin`
- **目标目录**: `src/views/page-builder/`
- **技术栈**: Vue 2.5.22, Ant Design Vue 1.7.8, Vuex 3.0.1
- **现有组件库**: `src/components/yyl/`

## 阶段一：基础架构搭建 (预计2周)

### 任务 1.1: 创建项目基础结构
**状态**: ⏳ 待开始
**优先级**: P0 (最高)
**预计时间**: 2天

**具体任务**:
```bash
# 创建目录结构
mkdir -p src/views/page-builder/{components,configs,mixins,utils,store,styles,api,docs}
mkdir -p src/views/page-builder/components/{Designer,PropertyEditors,RenderEngine,Common,PageManager}
mkdir -p src/views/page-builder/store/modules
```

**文件清单**:
- [ ] `src/views/page-builder/README.md` - 项目说明
- [ ] `src/views/page-builder/index.vue` - 主入口页面
- [ ] `src/views/page-builder/router.js` - 路由配置

**验收标准**:
- 目录结构创建完成
- 基础文件创建完成
- 能够通过路由访问页面构建器入口

### 任务 1.2: YYL组件扫描器开发
**状态**: ✅ 已完成
**优先级**: P0
**预计时间**: 3天
**实际时间**: 1天

**具体任务**:
- [ ] 开发 `src/views/page-builder/utils/componentScanner.js`
- [ ] 实现自动扫描 `src/components/yyl/` 目录
- [ ] 解析组件的props、events、slots信息
- [ ] 生成组件配置模板

**关键代码位置**:
```javascript
// src/views/page-builder/utils/componentScanner.js
class YYLComponentScanner {
  scanYYLComponents() {
    // 扫描business、form、table、components四个分类
  }
}
```

**验收标准**:
- 能够自动扫描所有YYL组件
- 正确解析组件属性和事件
- 生成标准化的组件配置

### 任务 1.3: 设计器主界面开发
**状态**: ⏳ 待开始
**优先级**: P0
**预计时间**: 4天

**具体任务**:
- [ ] 开发 `src/views/page-builder/components/Designer/index.vue`
- [ ] 实现三栏布局：组件面板、画布、属性面板
- [ ] 集成Ant Design Vue组件
- [ ] 实现基础的界面交互

**文件清单**:
- [ ] `src/views/page-builder/components/Designer/index.vue`
- [ ] `src/views/page-builder/components/Designer/Toolbar.vue`
- [ ] `src/views/page-builder/styles/designer.less`

**验收标准**:
- 界面布局正确显示
- 三个面板可以正常展示
- 基础交互功能正常

### 任务 1.4: 状态管理配置
**状态**: ⏳ 待开始
**优先级**: P0
**预计时间**: 2天

**具体任务**:
- [ ] 创建 `src/views/page-builder/store/index.js`
- [ ] 开发各个store模块
- [ ] 集成到主应用的store中

**文件清单**:
- [ ] `src/views/page-builder/store/modules/designer.js`
- [ ] `src/views/page-builder/store/modules/components.js`
- [ ] `src/views/page-builder/store/modules/pages.js`

**验收标准**:
- Store模块正确注册
- 状态管理功能正常
- 与主应用store集成成功

## 阶段二：核心功能开发 (预计3周)

### 任务 2.1: YYL组件面板开发
**状态**: ⏳ 待开始
**优先级**: P0
**预计时间**: 3天

**具体任务**:
- [ ] 开发 `src/views/page-builder/components/Designer/ComponentPanel.vue`
- [ ] 实现组件分类展示
- [ ] 添加搜索和筛选功能
- [ ] 实现拖拽功能

**关键功能**:
- 按business、form、table、components分类显示
- 支持组件搜索
- 拖拽开始时设置数据

**验收标准**:
- 所有YYL组件正确分类显示
- 搜索功能正常工作
- 拖拽交互流畅

### 任务 2.2: 画布区域开发
**状态**: ⏳ 待开始
**优先级**: P0
**预计时间**: 5天

**具体任务**:
- [ ] 开发 `src/views/page-builder/components/Designer/Canvas.vue`
- [ ] 实现组件拖入功能
- [ ] 实现组件选中和移动
- [ ] 添加网格对齐功能

**关键功能**:
- 支持组件拖入放置
- 组件选中状态管理
- 拖拽移动和调整大小
- 网格对齐和参考线

**验收标准**:
- 组件可以正确拖入画布
- 选中和移动功能正常
- 网格对齐功能工作正常

### 任务 2.3: YYL组件渲染器开发
**状态**: ⏳ 待开始
**优先级**: P0
**预计时间**: 4天

**具体任务**:
- [ ] 开发 `src/views/page-builder/components/RenderEngine/ComponentRenderer.vue`
- [ ] 实现动态组件渲染
- [ ] 处理YYL组件的权限控制
- [ ] 实现事件绑定

**关键代码**:
```vue
<component
  :is="config.type"
  v-bind="computedProps"
  v-on="computedEvents"
  :style="computedStyle"
/>
```

**验收标准**:
- YYL组件正确渲染
- 权限控制功能正常
- 事件绑定工作正常

### 任务 2.4: 属性面板开发
**状态**: ⏳ 待开始
**优先级**: P1
**预计时间**: 4天

**具体任务**:
- [ ] 开发 `src/views/page-builder/components/Designer/PropertyPanel.vue`
- [ ] 实现动态属性编辑器
- [ ] 开发专用编辑器组件
- [ ] 实现实时预览

**文件清单**:
- [ ] `src/views/page-builder/components/PropertyEditors/BasicEditor.vue`
- [ ] `src/views/page-builder/components/PropertyEditors/FormFieldsEditor.vue`
- [ ] `src/views/page-builder/components/PropertyEditors/PermissionEditor.vue`

**验收标准**:
- 属性编辑器正确显示
- 属性修改实时生效
- 专用编辑器功能完整

## 阶段三：YYL组件深度集成 (预计4周)

### 任务 3.1: YYLButton组件集成
**状态**: ⏳ 待开始
**优先级**: P0
**预计时间**: 3天

**具体任务**:
- [ ] 完整支持YYLButton的所有属性
- [ ] 实现权限配置界面
- [ ] 添加事件处理

**关键属性**:
- text, menuId, type, size, icon, loading, disabled
- 权限控制集成

**验收标准**:
- 所有属性可配置
- 权限控制正常工作
- 事件处理正确

### 任务 3.2: YYLForm组件集成
**状态**: ⏳ 待开始
**优先级**: P0
**预计时间**: 5天

**具体任务**:
- [ ] 开发表单字段编辑器
- [ ] 支持动态表单配置
- [ ] 实现表单验证规则配置

**关键功能**:
- 可视化表单字段配置
- 支持多种表单控件类型
- 验证规则配置

**验收标准**:
- 表单字段可视化配置
- 所有表单控件类型支持
- 验证功能正常

### 任务 3.3: YYLTable组件集成
**状态**: ⏳ 待开始
**优先级**: P0
**预计时间**: 5天

**具体任务**:
- [ ] 开发表格列编辑器
- [ ] 实现数据绑定配置
- [ ] 支持操作按钮配置

**关键功能**:
- 表格列可视化配置
- 数据源绑定
- 操作按钮权限控制

**验收标准**:
- 表格列配置功能完整
- 数据绑定正常工作
- 操作按钮权限正确

## 阶段四：高级功能开发 (预计3周)

### 任务 4.1: 页面保存和加载
**状态**: ⏳ 待开始
**优先级**: P1
**预计时间**: 4天

**具体任务**:
- [ ] 开发页面管理API
- [ ] 实现页面保存功能
- [ ] 实现页面加载功能
- [ ] 添加页面列表管理

**API接口**:
- POST /api/page-builder/pages - 保存页面
- GET /api/page-builder/pages - 获取页面列表
- GET /api/page-builder/pages/:id - 获取页面详情

**验收标准**:
- 页面可以正确保存
- 页面可以正确加载
- 页面列表管理功能完整

### 任务 4.2: 代码生成功能
**状态**: ⏳ 待开始
**优先级**: P1
**预计时间**: 4天

**具体任务**:
- [ ] 开发代码生成器
- [ ] 生成Vue单文件组件
- [ ] 支持代码预览和下载

**生成内容**:
- Vue template
- JavaScript script
- CSS styles

**验收标准**:
- 生成的代码可以正常运行
- 代码格式规范
- 支持预览和下载

### 任务 4.3: 预览功能开发
**状态**: ⏳ 待开始
**优先级**: P1
**预计时间**: 3天

**具体任务**:
- [ ] 开发预览模态框
- [ ] 实现实时预览
- [ ] 支持多设备预览

**预览模式**:
- 桌面端预览
- 平板端预览
- 移动端预览

**验收标准**:
- 预览功能正常工作
- 多设备预览正确
- 实时预览流畅

## 进度跟踪

### 当前状态
- **总体进度**: 0% (0/16 任务完成)
- **当前阶段**: 阶段一 - 基础架构搭建
- **下一个任务**: 任务 1.1 - 创建项目基础结构

### 完成情况统计
- ✅ 已完成: 0 个任务
- 🚧 进行中: 0 个任务
- ⏳ 待开始: 16 个任务
- ❌ 已取消: 0 个任务

### 里程碑
- [ ] **里程碑 1**: 基础架构完成 (预计2周后)
- [ ] **里程碑 2**: 核心功能完成 (预计5周后)
- [ ] **里程碑 3**: YYL组件集成完成 (预计9周后)
- [ ] **里程碑 4**: 高级功能完成 (预计12周后)

## Cursor AI 开发指引

### 开发顺序
1. 严格按照任务编号顺序开发
2. 每完成一个任务，更新状态为 ✅ 已完成
3. 遇到问题时，记录在对应任务的问题记录中

### 代码规范
- 遵循项目现有的ESLint规则
- 使用现有的代码风格和命名规范
- 充分利用现有的工具函数和组件

### 测试要求
- 每个功能开发完成后进行基础测试
- 确保与现有系统的兼容性
- 记录测试结果和问题

### 文档更新
- 及时更新README.md
- 记录重要的设计决策
- 更新API文档

## 任务详细跟踪表

### 阶段一任务跟踪

#### 任务 1.1: 创建项目基础结构
- **开始时间**:
- **完成时间**:
- **实际耗时**:
- **问题记录**:
- **代码审查**:
- **测试结果**:

#### 任务 1.2: YYL组件扫描器开发
- **开始时间**:
- **完成时间**:
- **实际耗时**:
- **问题记录**:
- **代码审查**:
- **测试结果**:

#### 任务 1.3: 设计器主界面开发
- **开始时间**:
- **完成时间**:
- **实际耗时**:
- **问题记录**:
- **代码审查**:
- **测试结果**:

#### 任务 1.4: 状态管理配置
- **开始时间**:
- **完成时间**:
- **实际耗时**:
- **问题记录**:
- **代码审查**:
- **测试结果**:

### 阶段二任务跟踪

#### 任务 2.1: YYL组件面板开发
- **开始时间**:
- **完成时间**:
- **实际耗时**:
- **问题记录**:
- **代码审查**:
- **测试结果**:

#### 任务 2.2: 画布区域开发
- **开始时间**:
- **完成时间**:
- **实际耗时**:
- **问题记录**:
- **代码审查**:
- **测试结果**:

#### 任务 2.3: YYL组件渲染器开发
- **开始时间**:
- **完成时间**:
- **实际耗时**:
- **问题记录**:
- **代码审查**:
- **测试结果**:

#### 任务 2.4: 属性面板开发
- **开始时间**:
- **完成时间**:
- **实际耗时**:
- **问题记录**:
- **代码审查**:
- **测试结果**:

## Cursor AI 具体开发指令

### 开始新任务时的检查清单
```markdown
- [ ] 确认当前任务的前置任务已完成
- [ ] 检查相关文件是否存在
- [ ] 确认开发环境配置正确
- [ ] 阅读任务的具体要求和验收标准
```

### 任务完成时的检查清单
```markdown
- [ ] 功能测试通过
- [ ] 代码符合项目规范
- [ ] 相关文档已更新
- [ ] 任务状态已更新
- [ ] 提交代码并添加适当的commit信息
```

### 常用开发命令
```bash
# 启动开发服务器
npm run serve

# 运行代码检查
npm run lint

# 构建项目
npm run build-test
```

### 关键文件路径速查
```
项目根目录: /Users/<USER>/Documents/ybb/ybb-scmadmin
YYL组件库: src/components/yyl/
页面构建器: src/views/page-builder/
工具函数: src/utils/
状态管理: src/store/
路由配置: src/config/router.config.js
```

### 开发过程中的注意事项

1. **组件命名规范**
   - 页面构建器组件以 `PageBuilder` 开头
   - 文件名使用 PascalCase
   - 组件name属性与文件名保持一致

2. **状态管理规范**
   - 使用命名空间模块
   - mutation名称使用大写下划线格式
   - action名称使用camelCase格式

3. **样式规范**
   - 使用less预处理器
   - 遵循BEM命名规范
   - 充分利用Ant Design Vue的样式变量

4. **API调用规范**
   - 使用项目现有的request.js
   - 统一的错误处理
   - 保持与现有系统的一致性

## 风险控制和质量保证

### 技术风险
- **风险**: YYL组件兼容性问题
- **缓解措施**: 充分测试每个YYL组件的集成
- **应急方案**: 提供组件兼容性检查工具

### 性能风险
- **风险**: 大量组件渲染性能问题
- **缓解措施**: 实现虚拟化渲染和懒加载
- **监控指标**: 组件渲染时间、内存使用量

### 用户体验风险
- **风险**: 拖拽操作不流畅
- **缓解措施**: 优化拖拽算法，添加视觉反馈
- **测试标准**: 拖拽延迟小于100ms

## 版本发布计划

### v0.1.0 - MVP版本 (阶段一完成)
- 基础架构搭建完成
- 可以访问页面构建器界面
- YYL组件扫描功能正常

### v0.2.0 - 核心功能版本 (阶段二完成)
- 基础拖拽功能完成
- YYL组件可以正常渲染
- 属性配置功能可用

### v0.3.0 - 组件集成版本 (阶段三完成)
- 主要YYL组件深度集成
- 权限控制功能完整
- 表单和表格组件可用

### v1.0.0 - 正式版本 (阶段四完成)
- 所有核心功能完成
- 页面保存加载功能
- 代码生成功能
- 预览功能完整

## 更新日志模板

### [版本号] - 日期
#### 新增功能
- 功能描述

#### 改进优化
- 优化内容

#### 问题修复
- 修复的问题

#### 技术债务
- 需要后续处理的技术问题
