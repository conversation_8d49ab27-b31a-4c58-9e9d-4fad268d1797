# Cursor AI 快速开始指南

## 📋 开发文件说明

### 核心文件
- **`cursor-development-plan.md`** - 详细的开发规划和任务说明
- **`cursor-task-executor.js`** - 任务执行辅助脚本
- **`task-status.json`** - 任务状态跟踪文件
- **`页面拖拽生成系统设计方案.md`** - 完整的技术设计方案

## 🚀 快速开始步骤

### 1. 环境检查
```bash
# 确认项目路径
cd /Users/<USER>/Documents/ybb/ybb-scmadmin

# 检查Node.js版本
node --version  # 应该 >= 12

# 检查依赖
npm list vue ant-design-vue vuex
```

### 2. 查看当前状态
```bash
# 使用任务执行器查看状态
node cursor-task-executor.js status

# 查看下一个推荐任务
node cursor-task-executor.js next
```

### 3. 开始第一个任务
```bash
# 开始任务 1.1 - 创建项目基础结构
node cursor-task-executor.js start 1.1
```

## 📝 Cursor AI 开发工作流

### 开始新任务时
1. **检查任务状态**
   ```bash
   node cursor-task-executor.js status
   ```

2. **开始任务**
   ```bash
   node cursor-task-executor.js start [任务ID]
   ```

3. **查看任务详情**
   - 打开 `cursor-development-plan.md`
   - 找到对应任务的详细说明
   - 查看验收标准和文件清单

### 开发过程中
1. **遵循项目规范**
   - 使用现有的ESLint配置
   - 遵循Vue 2.x最佳实践
   - 充分利用Ant Design Vue组件

2. **利用现有资源**
   - YYL组件库: `src/components/yyl/`
   - 工具函数: `src/utils/`
   - 状态管理: `src/store/`

3. **测试验证**
   ```bash
   # 启动开发服务器
   npm run serve
   
   # 访问页面构建器
   # http://localhost:8080/#/page-builder
   ```

### 完成任务时
1. **更新任务状态**
   - 编辑 `task-status.json`
   - 将对应任务的 `status` 改为 `"completed"`
   - 填写 `endDate` 和 `actualDays`

2. **记录问题和笔记**
   - 在 `task-status.json` 中记录遇到的问题
   - 更新 `notes` 字段

3. **提交代码**
   ```bash
   git add .
   git commit -m "feat: 完成任务[任务ID] - [任务名称]"
   ```

## 🎯 当前开发重点

### 阶段一：基础架构 (当前阶段)
**目标**: 搭建页面构建器的基础框架

**关键任务**:
- ✅ 任务1.1: 创建项目基础结构
- ⏳ 任务1.2: YYL组件扫描器开发
- ⏳ 任务1.3: 设计器主界面开发
- ⏳ 任务1.4: 状态管理配置

**成功标准**:
- 能够访问页面构建器入口
- YYL组件扫描功能正常
- 基础界面布局完成

## 🔧 开发技巧和最佳实践

### 1. 充分利用现有代码
```javascript
// 使用现有的请求工具
import { axios } from '@/utils/request'

// 使用现有的混入
import { EditMixin } from '@/mixins/EditMixin'

// 使用现有的工具函数
import cacheUtil from '@/utils/cacheUtil'
```

### 2. 遵循命名规范
```javascript
// 组件命名
export default {
  name: 'PageBuilderDesigner'  // 以PageBuilder开头
}

// 文件命名
PageBuilderDesigner.vue      // PascalCase
componentScanner.js          // camelCase
designer.less               // kebab-case
```

### 3. 状态管理模式
```javascript
// 使用命名空间
const store = {
  namespaced: true,
  state: {},
  mutations: {
    SET_COMPONENT_LIST(state, list) {}  // 大写下划线
  },
  actions: {
    fetchComponentList() {}             // camelCase
  }
}
```

### 4. 样式组织
```less
// 使用BEM命名规范
.page-builder {
  &__designer {
    &--active {
      // 样式
    }
  }
}
```

## 🐛 常见问题解决

### 1. YYL组件扫描问题
```javascript
// 确保正确的require.context路径
const components = require.context('@/components/yyl/business', false, /\.vue$/)
```

### 2. 路由配置问题
```javascript
// 在 src/config/router.config.js 中添加路由
{
  path: '/page-builder',
  component: () => import('@/views/page-builder/index.vue')
}
```

### 3. 样式冲突问题
```vue
<style lang="less" scoped>
// 使用scoped避免样式污染
</style>
```

## 📊 进度跟踪

### 当前进度
- **总体进度**: 0% (0/16 任务完成)
- **当前阶段**: 阶段一 - 基础架构搭建
- **下一个任务**: 任务 1.1 - 创建项目基础结构

### 里程碑时间线
- **2025-02-11**: 基础架构完成
- **2025-03-04**: 核心功能完成  
- **2025-04-01**: YYL组件集成完成
- **2025-04-22**: 高级功能完成

## 🆘 需要帮助时

### 1. 查看设计文档
- 详细技术方案: `页面拖拽生成系统设计方案.md`
- 开发规划: `cursor-development-plan.md`

### 2. 检查现有代码
- YYL组件示例: `src/components/yyl/business/YYLButton.vue`
- 现有页面结构: `src/views/pages/`
- 工具函数参考: `src/utils/`

### 3. 运行辅助脚本
```bash
# 查看项目状态
node cursor-task-executor.js status

# 获取下一个任务
node cursor-task-executor.js next
```

## 🎉 开发成功标志

### 阶段一完成标志
- [ ] 可以通过 `/page-builder` 访问页面构建器
- [ ] YYL组件扫描器能够识别所有组件
- [ ] 设计器界面正确显示三栏布局
- [ ] 状态管理正常工作

### 最终成功标志
- [ ] 可以拖拽YYL组件到画布
- [ ] 可以配置组件属性
- [ ] 可以保存和加载页面
- [ ] 可以生成Vue代码
- [ ] 可以预览页面效果

---

**开始开发吧！🚀**

记住：每完成一个任务，都要更新 `task-status.json` 文件中的任务状态。
