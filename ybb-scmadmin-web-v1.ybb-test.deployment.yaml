﻿kind: Deployment
apiVersion: apps/v1
metadata:
  name: ybb-scmadmin-web-v1
  namespace: ybb-test
  labels:
    app: ybb-scmadmin-web
    app.kubernetes.io/name: ybb-frontend
    app.kubernetes.io/version: v1
    version: v1
  annotations:
    deployment.kubernetes.io/revision: '1'
    kubesphere.io/creator: project-regular
    servicemesh.kubesphere.io/enabled: 'false'
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ybb-scmadmin-web
      app.kubernetes.io/name: ybb-frontend
      app.kubernetes.io/version: v1
      version: v1
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: ybb-scmadmin-web
        app.kubernetes.io/name: ybb-frontend
        app.kubernetes.io/version: v1
        version: v1
      annotations:
        cni.projectcalico.org/ipv4pools: '["default-ipv4-ippool"]'
        kubesphere.io/creator: project-regular
        kubesphere.io/imagepullsecrets: '{"ybb-scmadmin-web":"aliyun-registry-secret"}'
        sidecar.istio.io/inject: 'false'
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
      containers:
        - name: ybb-scmadmin-web
          image: >-
            yaoyueyue-images-registry.cn-shanghai.cr.aliyuncs.com/yyy_frontend/ybb-scmadmin-web:IMAGE_TAG
          ports:
            - name: http-0
              containerPort: 80
              protocol: TCP
          resources: {}
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      imagePullSecrets:
        - name: aliyun-registry-secret
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
