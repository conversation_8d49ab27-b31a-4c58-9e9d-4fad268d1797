import Vue from 'vue'
import App from './App.vue'

// 基础插件
import Storage from 'vue-ls'
import router from './router'
import store from './store/'
import { VueAxios } from '@/utils/request'
import Antd from 'ant-design-vue'
import Viewer from 'v-viewer'
import VueHtml5Editor from 'vue-html5-editor'

// 样式文件
import 'font-awesome/css/font-awesome.min.css'
import 'ant-design-vue/dist/antd.less'
import 'viewerjs/dist/viewer.css'
import './assets/iconfont/iconfont.css'
import './assets/less/common.less'
import './assets/less/custom-tailwind.scss'

// 工具和配置
import '@/permission'
import '@/utils/filter'
import API from './api/interfaces'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import config from '@/defaultSettings'
import vueBus from '@/utils/vueBus'
import cacheUtil from './utils/cacheUtil'
import lpTools from './utils/LPTools'
import searchEnum from './components/yyl/table/SearchItemEnum'
import { editorConfig } from '@/config/editor.config'
import FingerprintJS from './assets/js/fp.min.js'
import * as directives from '@/utils/directives.js'

Object.keys(directives).forEach(key=>{
  Vue.directive(key,directives[key])
})


// 注册全局组件
const registerGlobalComponents = () => {
  const technologyCenterComponents = require.context('./components/yyl', true, /\.vue$/)
  technologyCenterComponents.keys().forEach((k) => {
    const config = technologyCenterComponents(k)
    config.default.name && Vue.component(config.default.name, config.default || config)
  })

  const components = require.context('./views', true, /\.vue$/)
  components.keys().forEach((k) => {
    const config = components(k)
    if (config.default.name !== '404') {
      config.default.name && Vue.component(config.default.name, config.default || config)
    }
  })
}

// 注册全局属性
const registerGlobalProperties = () => {
  Vue.prototype.lastUpdate = { time: new Date() }
  Vue.prototype.cacheUtil = cacheUtil
  Vue.prototype.$lpTools = lpTools
  Vue.prototype.$SEnum = searchEnum
}

// 注册插件
const registerPlugins = () => {
  Vue.use(API)
  Vue.use(Storage, config.storageOptions)
  Vue.use(Antd)
  Vue.use(VueAxios, router)
  Vue.use(vueBus)
  Vue.use(Viewer)
  Vue.use(VueHtml5Editor, editorConfig)
}

// 全局指令
const registerGlobalDirectives = () => {
  // 按钮权限
  Vue.directive('has', {
    inserted(el, binding, vnode) {
      const { value } = binding
      const buttonPermissionSet = store.state.user.buttonPermissionSet
      if (value && typeof value === 'string') {
        if (!buttonPermissionSet.has(value)) {
          el.parentNode && el.parentNode.removeChild(el)
        }
      }
    }
  })
}

// 错误处理
Vue.config.errorHandler = (error, vm) => {
  console.error('发生了错误:', error)
  return true
}

// 初始化应用
const initApp = async () => {
  const fp = await FingerprintJS.load()
  const result = await fp.get()
  window._CONFIG.DeviceNo = result.visitorId

  // 注册全局组件和属性
  registerGlobalComponents()
  registerGlobalProperties()
  registerGlobalDirectives()
  registerPlugins()

  Vue.config.productionTip = false

  const app = new Vue({
    router,
    store,
    mounted() {
      // 初始化配置
      const initConfigs = [
        ['SET_SIDEBAR_TYPE', config.sideBarType],
        ['TOGGLE_THEME', config.navTheme],
        ['TOGGLE_LAYOUT_MODE', config.layout],
        ['TOGGLE_FIXED_HEADER', config.fixedHeader],
        ['TOGGLE_FIXED_SIDERBAR', config.fixSiderbar],
        ['TOGGLE_CONTENT_WIDTH', config.contentWidth],
        ['TOGGLE_FIXED_HEADER_HIDDEN', config.autoHideHeader],
        ['TOGGLE_WEAK', config.colorWeak],
        ['TOGGLE_COLOR', config.primaryColor],
        ['SET_TOKEN', Vue.ls.get(ACCESS_TOKEN)],
        ['SET_MULTI_PAGE', config.multipage]
      ]

      initConfigs.forEach(([type, value]) => store.commit(type, value))
    },
    render: (h) => h(App)
  }).$mount('#app')

  Vue.prototype.$APP = app.$children[0]
}

initApp()
