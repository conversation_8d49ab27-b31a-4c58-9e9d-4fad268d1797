const data = {
  pageListCacheKey: 'CX_PageSearchData',
  editPageCacheKey: 'CX_EditPageData',
  fail: {
    code: -1,
    msg: '无缓存数据',
  },
}
export default {
  /**
   * 保存列表界面查询条件等数据
   * @param {*页面带路径的名称} pagePathName 
   * @param {*需要保存的数据 } cacheData 默认格式{
          key: '',
          searchParams: null,
          ipagination: null,
          tableConfig: null,
        }
   * @returns 
   */
  saveListData(pagePathName, cacheData) {
    if (!pagePathName || !cacheData) {
      return
    }
    // if (cacheData.searchParams || cacheData.tableConfig) {
    const pageSearchData = sessionStorage.getItem(data.pageListCacheKey)
    let cacheList = []
    if (pageSearchData) {
      cacheList = JSON.parse(pageSearchData)
    }
    let index = cacheList.findIndex((item) => item.key === pagePathName)
    if (index == -1) {
      cacheList.push(cacheData)
    } else {
      cacheList[index] = cacheData
    }
    sessionStorage.setItem(data.pageListCacheKey, JSON.stringify(cacheList))
    // }
  },
  /**
   * 获取列表界面查询条件等数据
   * @param {*页面带路径的名称} pagePathName
   * @returns
   */
  getListData(pagePathName) {
    return new Promise((resolve, reject) => {
      const pageSearchData = sessionStorage.getItem(data.pageListCacheKey)
      if (pagePathName && pageSearchData) {
        let cacheList = JSON.parse(pageSearchData)
        //数组中有此界面的数据
        if (cacheList.length > 0) {
          const cacheData = cacheList.find((item) => {
            return item.key == pagePathName
          })
          if (cacheData) {
            resolve({
              code: 200,
              searchParams: cacheData.searchParams || null,
              ipagination: cacheData.ipagination || null,
              tableConfig: cacheData.tableConfig || null,
            })
          } else {
            resolve(data.fail)
          }
        } else {
          resolve(data.fail)
        }
      } else {
        resolve(data.fail)
      }
    })
  },
  /**
   * 删除列表界面查询条件等数据，根据页面路径名称
   * @param {*页面带路径的名称} pagePathName
   */
  deleteListData(pagePathName) {
    if (!pagePathName) {
      return
    }
    const pageSearchData = sessionStorage.getItem(data.pageListCacheKey)
    if (pagePathName && pageSearchData) {
      let cacheList = JSON.parse(pageSearchData)
      if (cacheList.length > 0) {
        let index = cacheList.findIndex((item) => {
          return item.key == pagePathName
        })
        if (index > -1) {
          cacheList.splice(index, 1)
          sessionStorage.setItem(data.pageListCacheKey, JSON.stringify(cacheList))
        }
      }
    }
  },
  /***
   * 删除列表界面查询条件等数据，根据多个页面路径名称
   */
  deleteListDataByKeys(pagePathlist) {
    if (pagePathlist.length > 0) {
      pagePathlist.forEach((name) => {
        this.deleteListData(name)
      })
    }
  },
  /**
   * 清除所有缓存数据
   */
  clearAllData() {
    //清除列表查询条件缓存数据
    this.deleteDataByKey(data.pageListCacheKey)
    //清除table几面左边列表分类数据
    this.deleteDataByKey('sampleMonitoring')
  },
  /**
   * 缓存数据更具指定key
   * @param {*} key
   * @param {* object数据对象} data
   * @returns
   */
  saveDataByKey(key, data) {
    if (key && data) {
      sessionStorage.setItem(key, JSON.stringify(data))
      return true
    } else {
      return false
    }
  },
  /**
   * 根据指定key获取缓存数据
   * @param {*} key
   * @returns
   */
  getDataByKey(key) {
    if (key) {
      return sessionStorage.getItem(key)
    } else {
      return null
    }
  },
  /**
   * 删除指定key的缓存数据
   * @param {*} key
   */
  deleteDataByKey(key) {
    if (key) {
      sessionStorage.removeItem(key)
    }
  },

  /**
   * 保存列表界面数据
   * @param {*页面带路径的名称} pagePathName 
   * @param {*需要保存的数据 } cacheData 默认格式{
          key: '',
          clearCache:false,
        }
   * @returns 
   */
  saveEditPageData(pagePathName, cacheData) {
    if (!pagePathName || !cacheData) {
      return
    }
    cacheData['key'] = pagePathName
    const pageSearchData = sessionStorage.getItem(data.pageListCacheKey)
    let cacheList = []
    if (pageSearchData) {
      cacheList = JSON.parse(pageSearchData)
    }
    let index = cacheList.findIndex((item) => item.key === pagePathName)
    if (index == -1) {
      cacheList.push(cacheData)
    } else {
      cacheList[index] = cacheData
    }
    sessionStorage.setItem(data.editPageCacheKey, JSON.stringify(cacheList))

    console.log('saveEditPageData', pagePathName)
    // }
  },
  /**
   * 获取编辑界面数据
   * @param {*页面带路径的名称} pagePathName
   * @returns
   */
  getEditPageData(pagePathName) {
    const editPageData = sessionStorage.getItem(data.editPageCacheKey)
    if (pagePathName && editPageData) {
      let cacheList = JSON.parse(editPageData)
      //数组中有此界面的数据
      if (cacheList.length > 0) {
        const cacheData = cacheList.find((item) => {
          return item.key == pagePathName
        })

        console.log('getEditPageData', pagePathName, cacheData)
        return cacheData
      } else {
        return null
      }
    } else {
      return null
    }
  },
  /**
   * 删除编辑界面数据，根据页面路径名称
   * @param {*页面带路径的名称} pagePathName
   */
  deleteEditPageData(pagePathName, callback) {
    if (!pagePathName) {
      return
    }
    const editPageData = sessionStorage.getItem(data.editPageCacheKey)
    if (pagePathName && editPageData) {
      let cacheList = JSON.parse(editPageData)
      if (cacheList.length > 0) {
        let index = cacheList.findIndex((item) => {
          return item.key == pagePathName
        })
        if (index > -1) {
          cacheList.splice(index, 1)
          sessionStorage.setItem(data.editPageCacheKey, JSON.stringify(cacheList))
          console.log('deleteEditPageData', pagePathName)
          callback && callback()
        } else {
          callback && callback()
        }
      } else {
        callback && callback()
      }
    } else {
      callback && callback()
    }
  },
}
