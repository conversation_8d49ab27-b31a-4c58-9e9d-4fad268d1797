import Vue from 'vue'
import axios from 'axios'
import store from '@/store'
import { VueAxios } from './axios'
import { Modal, notification } from 'ant-design-vue'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { EventBus } from '@/utils/eventBus';

// 创建 axios 实例
const service = axios.create({
  timeout: 10 * 60 * 1000 // 请求超时时间
})

const err = error => {
  if (error.response) {
    let data = error.response.data
    const token = Vue.ls.get(ACCESS_TOKEN)
    console.log('------异常响应------', token)
    console.log('------异常响应------', error.response.status)
    switch (error.response.status) {
      case 403:
        notification.error({
          message: '系统提示',
          description: '拒绝访问',
          duration: 4
        })
        break
      case 500:
        //notification.error({ message: '系统提示', description:'Token失效，请重新登录!',duration: 4})
        if (token && data.Msg == '未登录') {
          // update-begin- --- author:scott ------ date:20190225 ---- for:Token失效采用弹框模式，不直接跳转----
          // store.dispatch('Logout').then(() => {
          //     window.location.reload()
          // })
          Modal.error({
            title: '登录已过期',
            content: '很抱歉，登录已过期，请重新登录',
            okText: '重新登录',
            mask: false,
            onOk: () => {
              store.dispatch('Logout').then(() => {
                Vue.ls.remove(ACCESS_TOKEN)
                window.location.reload()
              })
            }
          })
          // update-end- --- author:scott ------ date:20190225 ---- for:Token失效采用弹框模式，不直接跳转----
        }
        break
      case 404:
        notification.error({
          message: '系统提示',
          description: '很抱歉，资源未找到!',
          duration: 4
        })
        break
      case 504:
        notification.error({
          message: '系统提示',
          description: '网络超时'
        })
        break
      case 401:
        notification.error({
          message: '系统提示',
          description: '未授权，请重新登录',
          duration: 4
        })
        if (token) {
          store.dispatch('Logout').then(() => {
            setTimeout(() => {
              window.location.reload()
            }, 2000)
          })
        }
        break
      default:
        notification.error({
          message: '系统提示',
          description: data.Msg,
          duration: 4
        })
        break
    }
  }
  return Promise.reject(error)
}

// request interceptor
service.interceptors.request.use(
  config => {
    let url = config.url
    if (url.indexOf('/') == 0) {
      url = url.substring(1)
    }
    let tab = store.getters.getCurTabMenu()
    if (tab) {
      let tabId = window.localStorage.getItem('TopTabId')
      if (tab.id != tabId) {
        window.localStorage.setItem('TopTabId', tab.id)
        window.localStorage.setItem('TopTabItem', JSON.stringify(tab))
        let value = ''
        if (tab.path.indexOf('_') > -1) {
          value = tab.path.split('_')[1]
        }
        window.localStorage.setItem('HYSYYLAppId', value)
      }
    }

    // 在发送请求之前做些什么
    config.url = config.url.indexOf('{v}') != -1 ? config.url.replace(/{v}/, 'v1') : config.url
    const token = Vue.ls.get(ACCESS_TOKEN)
    if (token && config.url.indexOf('aliyuncs.com') == -1) {
      config.headers['Authorization'] = 'Bearer ' + token // 让每个请求携带自定义 token 请根据实际情况自行修改
      config.headers['MonitorId'] = window._CONFIG.MonitorId || ''
      config.headers['CustomerKey'] = window._CONFIG.CustomerKey || ''
      const menuId = Vue.ls.get('MENU_ID')
      if (config.params && config.params.noMenuId) {
        config.headers['MenuId'] = '00000000-0000-0000-0000-000000000000'
      } else {
        config.headers['MenuId'] = menuId || '00000000-0000-0000-0000-000000000000'
      }
    }
    // 加版本号
    let noAddVersionArray = ['UserPermission/Menu/ListByToken', Vue.API.P46033]
    let noAddVersion = noAddVersionArray.find(item => {
      return config.url.indexOf(item) > -1
    })
    if (!noAddVersion) {
      config.headers['Version'] = Vue.API.Version || null
    }
    if (config.method == 'get') {
      config.params = {
        _t: Date.parse(new Date()) / 1000,
        ...config.params
      }
    }

    config.retry = 3 //重试次数
    config.retryDelay = 200 //重试延时
    config.shouldRetry = () => true //重试条件，默认只要是错误都需要重试

    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(response => {
  let data = response.data
  // console.log('------响应------', data)
  if (data.ErrorCode == 67890) {
    data.Data = false
    EventBus.$emit('versionModal');
  }
  if (data.Msg && data.Msg.indexOf('相关的错误详细描述返回在Data中') > -1) {
    let errLst = data.Data
    if (errLst && errLst.length > 0) {
      let err = errLst[0]
      data.Msg = `${err.ErrorField}:${err.ErrorDescription}`
    }
  }
  return data
}, err)

const installer = {
  vm: {},
  install(Vue, router = {}) {
    Vue.use(VueAxios, router, service)
  }
}

export { installer as VueAxios, service as axios }
