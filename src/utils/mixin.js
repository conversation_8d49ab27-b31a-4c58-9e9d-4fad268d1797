import Vue from 'vue'
import { mapState } from "vuex";

// const mixinsComputed = Vue.config.optionMergeStrategies.computed
// const mixinsMethods = Vue.config.optionMergeStrategies.methods

const mixin = {
  computed: {
    ...mapState({
      layoutMode: state => state.app.layout,
      navTheme: state => state.app.theme,
      primaryColor: state => state.app.color,
      colorWeak: state => state.app.weak,
      multipage: state => state.app.multipage,//多页签设置
      fixedHeader: state => state.app.fixedHeader,
      fixSiderbar: state => state.app.fixSiderbar,
      contentWidth: state => state.app.contentWidth,
      autoHideHeader: state => state.app.autoHideHeader,
      sidebarOpened: state => state.app.sidebar.opened
    })
  }
}

const mixinDevice = {
  computed: {
    ...mapState({
      device: state => state.app.device,
    })
  },
  methods: {
    isMobile () {
      return this.device === 'mobile'
    },
    isDesktop () {
      return this.device === 'desktop'
    },
    
    _bearer(token,userId,phone) {
      var ak = token;
      var uid = userId;
      var userName = phone;


        var dtype = function(){
          var ua = navigator.userAgent.toLowerCase(),isMac= /macintosh|mac os x/i.test(ua),pcversion=''  
          if (ua.indexOf('windows nt 5.0') > -1) {
            pcversion = 'Windows 2000'
          } else if (ua.indexOf('windows nt 5.1') > -1 || ua.indexOf('windows nt 5.2') > -1) {
            pcversion = 'Windows XP'
          } else if (ua.indexOf('windows nt 6.0') > -1) {
            pcversion = 'Windows Vista'
          } else if (ua.indexOf('windows nt 6.1') > -1 || ua.indexOf('windows 7') > -1) {
            pcversion = 'Windows 7'
          } else if (ua.indexOf('windows nt 6.2') > -1 || ua.indexOf('windows 8') > -1) {
            pcversion = 'Windows 8'
          } else if (ua.indexOf('windows nt 6.3') > -1) {
            pcversion = 'Windows 8.1'
          } else if (ua.indexOf('windows nt 6.2') > -1 || ua.indexOf('windows nt 10.0') > -1) {
            pcversion = 'Windows 10'
          } else {
            pcversion = 'Unknown'
          }
          return  pcversion?pcversion:(isMac?'Mac':'Other')
      }()
      
      var liveLon=window.localStorage.getItem('Longitude'),liveLat=window.localStorage.getItem('Latitude'),gl=(liveLon&&liveLat?(liveLon+','+liveLat):((window._CONFIG.Longitude&&window._CONFIG.Latitude)?window._CONFIG.Longitude+','+window._CONFIG.Latitude:''))      
    if (ak) {
        return {
            'Authorization': 'Bearer ' + ak,
            'Platform': window._CONFIG.MonitorId||'',
            'Gps':gl,
            'location':gl,
            'DeviceInfo':window._CONFIG.DeviceInfo||'',
            'systemname':window._CONFIG.MonitorId||'',
            'deviceno':window._CONFIG.DeviceNo||'',
            'isdefaultcode':''+window._CONFIG.DefaultCodeUse,

            'UserId':uid||'',
            'UserName':userName,
            'DeviceName': 'PC',
            'OS': dtype,
            'BrowserInfo':window.navigator.userAgent,
            'MonitorId':window._CONFIG.MonitorId||'',
            'CustomerKey':window._CONFIG.CustomerKey||''
        }
      } else {
        return { 
            'Platform': window._CONFIG.MonitorId||'',
            'Gps':gl,              
            'location':gl,
            'DeviceInfo':window._CONFIG.DeviceInfo||'',
            'systemname':window._CONFIG.MonitorId||'',
            'deviceno':window._CONFIG.DeviceNo||'',
            'isdefaultcode':''+window._CONFIG.DefaultCodeUse,

            'UserId':uid||'',
            'UserName':userName,
            'DeviceName': 'PC',
            'OS': dtype,
            'BrowserInfo':window.navigator.userAgent,
            'MonitorId':window._CONFIG.MonitorId||'',
            'CustomerKey':window._CONFIG.CustomerKey||''             
        }
      }
    },
  }
}

export { mixin, mixinDevice }