import { apiHead } from '@/api/manage'

const socketUtil = {
  socket: null,
  isLinked: false,
  socketMessageListener: null,

  /**
   * 连接到socket服务器
   */
  linkToServer() {
    if (socketUtil.socket && socketUtil.isLinked) {
      return
    }
    socketUtil.socket = new WebSocket(apiHead('SOCKET'))
    socketUtil.socket.onopen = e => {
      console.log('socket onopen', e)
      socketUtil.isLinked = true
    }
    socketUtil.socket.onerror = socketUtil.onSocketError
    socketUtil.socket.onclose = socketUtil.onSocketClose
    socketUtil.socket.onmessage = socketUtil.onSocketMessage
  },
  onSocketError(e) {
    console.log('socket error', e)
  },
  onSocketClose(e) {
    console.log('socket Close', e)
    socketUtil.isLinked = false
    setTimeout(() => {
      socketUtil.reLink()
    }, 1000)
  },
  onSocketMessage(e) {
    console.log('socket message', e)
    if (e && e.data && e.data.indexOf('{') == 0) {
      let msg = JSON.parse(e.data)
      if (msg.MessageType == 'AdminPlatform') {
        if (socketUtil.socketMessageListener) {
          socketUtil.socketMessageListener(msg.Message)
        }
        // socketUtil.$bus.emit('onSocketMessageNotice', msg.Message)
      }
    }
  },
  /**
   * 重连
   */
  reLink() {
    if (!socketUtil.isLinked) {
      console.log('重连socket')
      socketUtil.socket = null
      socketUtil.linkToServer()
    }
  },
  setSocketMessageListener(listener) {
    socketUtil.socketMessageListener = listener
  }
}

export default socketUtil
