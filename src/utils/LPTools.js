//LP的工具类
export default {
  /**
   * 防抖函数
   * @param {* 传入的执行函数} func
   * @param {* 等待时间，默认500毫秒} wait
   * @returns
   */
  debounce(callback, wait = 500) {
    let timer
    return function () {
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(() => {
        callback.apply(this, arguments)
      }, wait)
    }
  },
  getString() {
    return 'NBA-CBA-CUBA_NCAA'
  },
}
