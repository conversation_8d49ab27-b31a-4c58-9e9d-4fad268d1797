import moment from 'moment'

export const PublicMixin = {
  mixins: [],
  data() {
    return {
      longTime: '2099-12-31',
      oneFileCodes: ['QCRZZL-IdCardFront', 'QCRZZL-IdCardBack'], //一张图片的项的code
    }
  },
  created() {},
  methods: {
    moment,
    log(key, groupList) {
      console.log('<!----' + key + ' start---->')
      groupList.forEach((group) => {
        if (group.Qualifications && group.Qualifications.length > 1) {
          console.log(
            group.GroupName +
              ' Name = ' +
              group.Qualifications[1].QualificationName +
              ' Value = ' +
              group.Qualifications[1].QualificationValue
          )
        } else {
          console.log(
            group.GroupName +
              ' Name = ' +
              (group.Qualifications[0] ? group.Qualifications[0].QualificationName : '') +
              ' Value = ' +
              (group.Qualifications[0] ? group.Qualifications[0].QualificationValue : '')
          )
        }
      })
      console.log('<!----' + key + ' end---->')
    },
    /**
     * 解析已有数据到基础数据中 （详情或者编辑回填数据）
     * @param {*} callback  回调 返回最终list
     * @param {*} baseList  基础数据
     * @param {*} hasList  已有数据
     * @param {*} parsingRadio  是否解析七彩 选项类型数据 true false 可不填
     * @param {*} isAddAndCGEdit  是否特殊处理企业报表  新增和采购编辑的时候传true 其余false
     * @returns
     */
    parsingDataToBasicListData(callback, baseList, hasList, parsingRadio, isAddAndCGEdit) {
      this.log('baseList', baseList)
      this.log('hasList', hasList)
      if (!baseList || !callback) {
        console.log('传入参数错误，必传参数未传')
        return []
      }
      //找出基础数据中可多次添加的group数据
      let zearoList = baseList.filter((group) => group.MaxCount == 0)

      //再找出已有数据中 基础数据指定可多次添加的group
      let addList = []
      zearoList.forEach((zGroup) => {
        let hList = hasList.filter((hGroup) => hGroup.GroupCode == zGroup.GroupCode)
        if (hList && hList.length > 0) {
          // hList.reverse()
          hList.forEach((group) => {
            if (!group.IsMust) group.IsAdd = true
            group.Qualifications.forEach((item) => {
              if (!item.IsShow) {
                item.IsShow = true
              }
              if (item.QualificationType == 1) {
                item.QualificationValue = this.filesToImages(item)
              }
            })
            addList.push(group)
          })
        }
      })
      // console.log('old Size =' + baseList.length)
      if (addList.length > 0) {
        baseList = baseList.concat(addList)
      }
      console.log('addList new Size = ' + baseList.length, addList)

      baseList.forEach((group) => {
        //新增和采购编辑的时候特殊处理企业报表  不显示效期和证号
        if (isAddAndCGEdit && group.GroupCode == 'EnterpriseAnnualReport') {
          group.Qualifications.forEach((q) => {
            if (q.QualificationType != 1) {
              q.IsShow = false
            }
          })
        }
        if (hasList) {
          //必填
          if (group.IsMust) {
            hasList.forEach((hasGroup) => {
              if (hasGroup.GroupCode == group.GroupCode) {
                group.Qualifications.forEach((gItem) => {
                  hasGroup.Qualifications.forEach((hasGItem) => {
                    if (
                      gItem.QualificationCode == hasGItem.QualificationCode &&
                      gItem.QualificationType == hasGItem.QualificationType &&
                      !gItem.QualificationValue
                    ) {
                      gItem.QualificationValue = hasGItem.QualificationValue

                      if (gItem.QualificationType == 1) {
                        gItem.QualificationValue = this.filesToImages(hasGItem)
                      } else if (gItem.QualificationType == 3) {
                        //日期
                        gItem.Checked = moment(hasGItem.QualificationValue).format('YYYY-MM-DD') == this.longTime
                      }
                    }
                  })
                })
              }
            })
          } else {
            //非必填
            hasList.forEach((hasGroup) => {
              if (hasGroup.GroupCode == group.GroupCode && !group.IsAdd) {
                let setBool = true
                let obj = addList.find((addGroup) => addGroup.GroupCode == group.GroupCode)
                if (obj) {
                  setBool = false
                }
                if (setBool) {
                  group.Qualifications.forEach((gItem) => {
                    hasGroup.Qualifications.forEach((hasGItem) => {
                      if (
                        gItem.QualificationCode == hasGItem.QualificationCode &&
                        gItem.QualificationType == hasGItem.QualificationType &&
                        !gItem.QualificationValue
                      ) {
                        gItem.QualificationValue = hasGItem.QualificationValue

                        if (gItem.QualificationType == 1) {
                          group.IsAdd = true
                          gItem.QualificationValue = this.filesToImages(hasGItem)
                        } else if (gItem.QualificationType == 3) {
                          //日期
                          gItem.Checked = moment(hasGItem.QualificationValue).format('YYYY-MM-DD') == this.longTime
                        }
                      }
                    })
                  })
                }
              }
            })
          }
        }
      })

      console.log('parsingDataToBasicListData', baseList)
      if (parsingRadio == true) {
        this.parsingRadioData(baseList, callback)
      } else {
        callback(baseList)
      }
    },
    /**
     * 解析已有数据-主要是处理图片
     * @param {*} hasList
     * @param {*} callback
     */
    parsingHasList(hasList, callback) {
      if (!hasList || !callback) {
        console.log('传入参数错误，必传参数未传')
        return []
      }
      hasList.forEach((group) => {
        group.Qualifications.forEach((item) => {
          if (item.QualificationType == 1) {
            if (!group.IsMust) {
              group.IsAdd = true
            }
            item.QualificationValue = this.filesToImages(item)
          }
        })
      })
      callback(hasList)
    },
    /**
     * 解析审核界面-首营资质数据
     * @param {*} groupList
     * @param {*} callback
     * @returns
     */
    parsingGroupList(groupList, callback) {
      if (!groupList || !callback) {
        console.log('传入参数错误，必传参数未传')
        return []
      }
      groupList.forEach((group) => {
        if (group.Qualifications) {
          group.OldQualifications = [].concat(group.Qualifications)

          let imgItemList = group.Qualifications.filter((img) => img.QualificationType == 1)
          group.ImgItemSize = imgItemList ? imgItemList.length : 0
          if (imgItemList && imgItemList.length > 1) {
            let newQualifications = []
            let imgItem = group.Qualifications.find((u) => u.QualificationType == 1)
            let newItem = JSON.parse(JSON.stringify(imgItem))
            newItem.Files = []
            imgItemList.forEach((x) => {
              newItem.Files = newItem.Files.concat(x.Files)
            })
            newQualifications.push(newItem)

            group.Qualifications.forEach((item) => {
              if (item.QualificationType != 1) {
                newQualifications.push(item)
              }
            })
            group.Qualifications = newQualifications
          }
        }
      })
      console.log('audit groupList', groupList)
      callback(groupList)
    },
    /**
     * 解析七彩 选项类型数据 true false  此方法外部不使用
     * @param {*} baseList
     * @param {*} callback
     */
    parsingRadioData(baseList, callback) {
      baseList.forEach((group) => {
        //七彩认证 -特殊处理
        if (group.GroupCode == 'QCRZZL') {
          group.GroupType = 5
          group.RadioList = [
            {
              Name: '委托人',
              Value: '委托人',
            },
            {
              Name: '法人或负责人',
              Value: '法人或负责人',
            },
          ]
          let findItem = group.Qualifications.find((x) => x.QualificationType == 5)
          group.RadioVal =
            findItem && findItem.QualificationValue ? findItem.QualificationValue : group.RadioList[0].Value
          group.Qualifications.forEach((item) => {
            if (item.IsShow) {
              if (item.QualificationCode == 'QCRZZL-WTS') {
                item.FilterList = ['委托人']
              } else {
                item.FilterList = ['委托人', '法人或负责人']
              }
              item.IsShow = item.FilterList.includes(group.RadioVal)
              //如果是身份证图片 Count = 1
              if (this.checkListIsContainCode(this.oneFileCodes, item.QualificationCode)) {
                item.Count = 1
              }
            }
          })
        }
      })
      console.log('parsingRadioData', baseList)
      callback(baseList)
    },
    /**
     * 检车某个数据组是否包含某个code
     * @param {*} list
     * @param {*} code
     */
    checkListIsContainCode(list, code) {
      if (!list || !code) {
        return false
      }
      let obj = list.find((item) => item == code)
      if (obj) {
        return true
      } else {
        return false
      }
    },
    /**
     * 文件转图片string数组
     * @param {*} hasGItem
     * @returns
     */
    filesToImages(hasGItem) {
      let images = []
      //来自客户七彩认证的资质
      if (hasGItem && hasGItem.Files && hasGItem.Files.length) {
        hasGItem.Files.forEach((file) => {
          images.push(file.FileUrl)
        })
      }
      return images
    },
    /**
     * 转换数组、重新组合一个新数组（界面展示效果是三列竖向的连续增长的编号例如 第一列 1 2 3 4 第二列 5 6 7 第三列 8 9 10）
     * @param {*} list
     */
    getNewList(list) {
      let newList = [...list]
      if (list && list.length > 3) {
        let num = Math.floor(list.length / 3)
        let yNum = list.length % 3
        let jsNum = yNum === 0 ? num : num + 1
        let oneList = list.slice(0, jsNum)
        let twoList = list.slice(jsNum, num * 2 + yNum)
        let threeList = list.slice(num * 2 + yNum)
        // console.log('oneList = ', oneList)
        // console.log('twoList = ', twoList)
        // console.log('threeList = ', threeList)
        let max = Math.max(oneList.length, twoList.length, threeList.length)
        newList = []
        for (var i = 0; i < max; i++) {
          if (i < oneList.length) {
            newList.push(oneList[i])
          }
          if (i < twoList.length) {
            newList.push(twoList[i])
          }
          if (i < threeList.length) {
            newList.push(threeList[i])
          }
        }
      }
      return newList
    },
  },
}
