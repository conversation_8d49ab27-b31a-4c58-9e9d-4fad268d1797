/**
 * 新增修改完成调用 modalFormOk方法 编辑弹框组件ref定义为modalForm
 * 高级查询按钮调用 superQuery方法  高级查询组件ref定义为superQueryModal
 * data中url定义 list为查询列表  delete为删除单条记录  deleteBatch为批量删除
 */
import { filterObj } from '@/utils/util'
import { deleteAction, getAction, downFile, postAction, putAction } from '@/api/manage'
import Vue from 'vue'
import { ACCESS_TOKEN, USER_ID } from '@/store/mutation-types'
import { mapGetters } from 'vuex'
import moment from 'moment'

const watchLinseners = {}

export const ListMixin = {
  mixins: [],
  data() {
    return {
      orgInfo: null, //登录用户机构信息
      queryCol: 6,
      labelCol: {
        xs: { span: 12 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 18 },
      },
      //token header
      tokenHeader: {
        Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
      },
      /* 查询条件-请不要在queryParam中声明非字符串值的属性 */
      queryParam: {},
      rangeDate: [],
      rangeDate1: [],
      rangeDateObj: {},
      //自定义标题
      title: '',
      /* 数据源 */
      dataSource: [],
      tabDataList: [],
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 15,
        pageSizeOptions: ['15', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      pageSizeNum: null,//自定义pageSize
      tableChangeLoadData: true,//是否table触发的change事件时候调用loadData方法
      /* 排序参数 */
      isorter: {
        column: 'createTime',
        order: 'desc',
      },
      /* 筛选参数 */
      filters: {},
      /* table加载状态 */
      loading: false,
      exportLoading: false,
      /* table选中keys*/
      selectedRowKeys: [],
      /* table选中records*/
      selectionRows: [],
      /* 查询折叠 */
      toggleSearchStatus: false,
      /* 高级查询条件生效状态 */
      superQueryFlag: false,
      /* 高级查询条件 */
      superQueryParams: '',
      //禁止排序
      disableSort: false,
      //Auto load Data
      isInitData: true,
      //SearchView组件初始化后是否调用 searchQuery
      searchViewInitData: null,
      //回收站
      isRecycleList: false,
      // 设置为true后就不触发table组件的activated
      useParentActivated: false,
      rowKey: '',
      httpHead: 'P34201',
      uploadFileList: [],
      url: {
        getWebUploadPolicy: '/Oss/GetWebUploadPolicy',
        getFileTempUrl: '/Oss/GetFileTempUrl',
      },
      //资料类型
      zlTypeList: [
        { Name: '企业资质', Value: 1 },
        { Name: '委托书', Value: 2 },
        { Name: '含特委托书', Value: 3 },
        { Name: '商品资质', Value: 4 },
        { Name: '商品报告', Value: 5 },
        { Name: '出库单', Value: 6 },
        { Name: '含特回执', Value: 7 },
      ],
    }
  },
  beforeCreate() {
    for (const key in this.queryParam) {
      watchLinseners[`queryParam.${key}`] = {
        handler(val) {
          if (val == null) {
            this.queryParam[key] = undefined
          }
        },
      }
    }
  },
  watch: watchLinseners,
  created() {
    this.initBefore()

    //初始化字典配置 在自己页面定义
    this.initDictConfig()
  },
  mounted() {
    if (this.isInitData == false) {
      this.searchViewInitData = false
    } else if (this.isInitData == true) {
      // if (this.$refs.searchView && this.$refs.tableView && !this.checkCurPageIsDialog()) {
      //   this.isInitData = false
      //   this.searchViewInitData = true
      // }
      if (this.$refs.SimpleSearchArea && this.$refs.table) {
        this.isInitData = false
        this.searchViewInitData = true
      }
    }

    if (this.isInitData === true) {
      this.loadData()
    }
  },
  methods: {
    moment,
    ...mapGetters(['organizationInfo', 'userInfo']),
    initBefore() { },
    loadBefore() { },
    loadAfter() { },
    loadTabDataAfter() { },
    /**
     * 导入excel之后调用的函数
     * @param {*} fileName 非空 导入成功  反之 失败
     */
    importExcelAfter(fileName) { },
    /**
     * 加载失败
     * @param res 接口返回数据
     */

    loadFail(res) { },
    handleLoadedData(data) {
      return data
    },
    /**
     *
     * @param {*当前页码数} arg
     * @param {*加载回调函数} callback
     * @param {*是否加载tab数据} loadTab
     * @param {*查询类型 1 查询按钮点击 2 重置按钮点击 else 其他} searchType
     * @returns
     */
    loadData(arg, callback, loadTab, searchType) {
      if (!this.linkUrl || !this.linkUrl.list) {
        if (!this.url || !this.url.list) {
          //this.$message.error("请设置url.list属性!")
          return
        }
      } else {
        this.url.listType = this.linkUrlType || 'GET'
        this.httpHead = this.linkHttpHead
        this.url.list = this.linkUrl.list
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }

      this.loading = true
      if (typeof this.loadBefore == 'function') {
        this.loadBefore()
      }
      if (this.$refs.tableView) {
        this.$refs.tableView.tableLoadBefore()
      }
      var params = this.getQueryParams() //查询条件
      if (this.pageSizeNum) {
        params.PageSize = this.pageSizeNum
      }
      if (this.ipagination.current === 1 || loadTab) {
        this.loadTabData(params)
      }
      let requestObj = null
      if (this.url && this.url.listType == 'POST') {
        requestObj = postAction(this.url.list, params, this.httpHead)
      } else {
        requestObj = getAction(this.url.list, params, this.httpHead)
      }
      requestObj.then((res) => {
        this.loadDataResult(res, callback, searchType)
      })
    },
    /**
     * 加载数据结果处理
     * @param {*} res
     * @param {*} callback
     */
    loadDataResult(res, callback) {
      if (res.IsSuccess) {
        let source = res.Data ? res.Data : []
        if (typeof source === Array) {
          source.forEach((item) => {
            item.loading = false
          })
        }
        if (callback) {
          callback(source, res.Count)
        } else {
          this.dataSource = source
          // 不理解，先注释掉（当current为1时，不会触发外层的getLoadData事件）
          // if (source && source.length >= 0) {
          //   if (this.ipagination.current > 1) {
          //     this.$emit('getLoadData', source, res.Count)
          //   }
          // } else {
          this.$emit('getLoadData', source, res.Count)
          // }
        }
        this.ipagination.total = res.Count ? res.Count : 0
        if (typeof this.loadAfter == 'function') {
          this.loadAfter(this.dataSource, res.Count)
        }
      } else {
        if (res.Msg) {
          this.$message.warning(res.Msg)
        }
        this.dataSource = []
        this.ipagination.total = 0

        if (typeof this.loadFail == 'function') {
          this.loadFail(res)
        }
      }
      this.loading = false
    },
    checkCurPageIsDialog() {
      return this.hanleOK != undefined && this.handleCancel != undefined
    },
    /***
     * 加载tab数据
     */
    loadTabData(params) {
      if (!this.url || !this.url.tabCount) {
        //this.$message.error("请设置url.list属性!")
        return
      }

      if (this.url && this.url.listType == 'POST') {
        postAction(this.url.tabCount, params, this.httpHead).then((res) => {
          if (res.IsSuccess) {
            if (this.url.tabIsList === true) {
              this.tabDataList = res.Data || []
              console.log('asdf', res.Data)
            } else {
              this.tabDataList.map((x, i, arr) => {
                x.count = res.Data[x.key || 'Count' + i] || 0
              })
              this.loadTabDataAfter(res.Data)
            }
          } else {
            this.$message.warning(res.Msg)
            if (this.url.tabIsList === true) {
              this.tabDataList = []
            }
          }
        })
      } else {
        getAction(this.url.tabCount, params, this.httpHead).then((res) => {
          if (res.IsSuccess) {
            if (this.url.tabIsList === true) {
              this.tabDataList = res.Data || []
            } else {
              this.tabDataList.map((x, i, arr) => {
                x.count = res.Data[x.key || 'Count' + i] || 0
              })
              this.tabDataList = [].concat(this.tabDataList)
              this.loadTabDataAfter(res.Data)
            }
          } else {
            this.$message.warning(res.Msg)
            if (this.url.tabIsList === true) {
              this.tabDataList = []
            }
          }
        })
      }
    },
    // 重新渲染table的Fixed高度
    // dataSource table的数据源 必传
    // tableId 需要在table组件上加id rowKey生效且不重复的话可以不传和不加 最好传值保证准确
    // tableRowKey table的rowKey 若rowKey已经声明了的话可以不传
    resetSetFixedHeight(dataSource, tableId, tableRowKey) {
      //解决左右两边fixed固定的表格行高不一致
      // if (!this.columns) {
      //   return
      // }
      // let haveFixed = this.columns.find((x) => x.fixed == 'left' || x.fixed == 'right')
      // if (!haveFixed) {
      //   return
      // }
      if (!dataSource || dataSource.length === 0) {
        return
      }
      this.$nextTick(() => {
        //表体tbody的tr高度，循环对每一行进行调整
        setTimeout(() => {
          dataSource.forEach((item, index) => {
            let rowKey = ""
            let tableIdText = ""
            if (this.tab && this.tab.rowKey) {
              rowKey = item[this.tab.rowKey]
            } else if (this.table && this.table.rowKey) {
              rowKey = item[this.table.rowKey]
            }
            if (!rowKey) {
              rowKey = index
            }
            if (tableRowKey) {
              rowKey = tableRowKey
            }
            if (tableId) {
              tableIdText = "#" + tableId
            }
            const cssSelector = `${tableIdText} table.ant-table-fixed tr[data-row-key='${rowKey}']`;
            const leftCssSelector = `${tableIdText} .ant-table-fixed-left tr[data-row-key='${rowKey}']`;
            const rightCssSelector = `${tableIdText} .ant-table-fixed-right tr[data-row-key='${rowKey}']`;
            const leftFixedTr = document.querySelector(leftCssSelector);
            const rightFixedTr = document.querySelector(rightCssSelector);

            const scrollTr = document.querySelector(cssSelector);
            const theoryTrHeight = scrollTr ? window.getComputedStyle(scrollTr).height : null;
            if (leftFixedTr) {
              if (index == 0) {
                console.log('theoryTrHeight', theoryTrHeight, leftFixedTr)
              }
              leftFixedTr.style.height = theoryTrHeight;
            }
            if (rightFixedTr) {
              if (index == 0) {
                console.log('theoryTrHeight', theoryTrHeight, rightFixedTr)
              }
              rightFixedTr.style.height = theoryTrHeight;
            }
          })

        })
      })
    },

    initDictConfig() {
      //console.log("--这是一个假的方法!")
    },
    handleSuperQuery(arg) {
      //高级查询方法
      if (!arg) {
        this.superQueryParams = ''
        this.superQueryFlag = false
      } else {
        this.superQueryFlag = true
        this.superQueryParams = JSON.stringify(arg)
      }
      this.loadData()
    },
    getQueryParams() {
      //获取查询条件
      let sup = {}
      if (this.superQueryParams) {
        sup['superQueryParams'] = encodeURI(this.superQueryParams)
      }
      let searchPar = {}
      if (this.$refs.searchView) {
        searchPar = this.$refs.searchView.getQueryParam() || this.$refs.SimpleSearchArea.queryParam || {}
      }
      var param = Object.assign({ ...sup }, this.queryParam, searchPar, this.isorter, this.filters)
      param.field = this.getQueryField()
      param.PageIndex = this.ipagination.current
      param.PageSize = this.ipagination.pageSize
      if (this.disableSort) {
        param.column = '' //禁用排序字段
      }
      let filterObjParams = filterObj(param)

      return filterObjParams
    },
    getQueryField() {
      var str = 'id,'
      if (this.columns) {
        this.columns.forEach(function (value) {
          str += ',' + value.dataIndex
        })
      }
      return str
    },
    /**
     * 用户手动选择/取消选择所有列的回调
     * @param {*} selected
     * @param {*} sRowKeys
     * @param {*} sRows
     * @param {*} rowKey
     */
    onSelectAllChange(selected, sRowKeys, sRows, rowKey) { },
    onSelectChange(selectedRowKeys, selectionRows, name) {
      this.selectedRowKeys = selectedRowKeys

      if (name) {
        let add = true
        let list = [].concat(this.selectionRows)
        //循环把当前选择加入已选择中，如果已经在已选择中，则不添加
        for (const i in selectionRows) {
          let item = selectionRows[i]
          add = true
          for (const j in this.selectionRows) {
            let obj = this.selectionRows[j]
            if (obj[name] == item[name]) {
              add = false
              break
            }
          }
          if (add) {
            list.push(item)
          }
        }

        //再循环已选的key，并信息选择的对象，判断两个数据是否匹配，selectedRowKeys是最准确的数据，所以根据selectedRowKeys最终筛选一次数据
        let newList = []
        for (const m in this.selectedRowKeys) {
          let mItem = this.selectedRowKeys[m]
          for (const n in list) {
            let nItem = list[n]
            if (nItem[name] == mItem) {
              newList.push(nItem)
              break
            }
          }
        }
        this.selectionRows = newList
      } else {
        this.selectionRows = selectionRows
      }

      console.log(this.selectionRows, this.selectedRowKeys, name)
    },
    onClearSelected() {
      console.log('onClearSelected-!!!!!!!!!!!!!')
      this.selectedRowKeys = []
      this.selectionRows = []
    },
    /**
     * 查询
     * @param {*搜索条件已填数据} searchParam
     * @param {*其他参数、例如页码数据 可扩展} param
     * @param {*查询类型 1 查询按钮点击 2 重置按钮点击 else 其他} searchType
     */
    searchQuery(searchParam, param, searchType) {
      console.log(searchParam, param, searchType)
      let curPage = 1
      let loadTab = false
      //还可扩展其他的属性
      if (searchParam && Object.keys(searchParam).length > 0 && curPage > 1) {
        loadTab = true
      }
      if (curPage == 1) {
        console.log(1111)
        this.onClearSelected()
      }
      // 非搜索和重置类型不赋值页码
      if (!param && !searchType) {
        curPage = null
      }
      this.loadData(curPage, null, loadTab, searchType)
    },
    superQuery() {
      this.$refs.superQueryModal.show()
    },
    searchReset() {
      this.rangeDate = []
      this.rangeDate1 = []
      this.rangeDateObj = {}
      // for (var a in this.queryParam) {
      //   this.queryParam[a] = null
      // }
      this.queryParam = {}
      this.loadData(1)
    },
    /**
     * @description: 时间回调
     */
    onTimeChange(dates, dateStrings, startKey, endKey, isModel) {
      const isArry = dates.length == 2
      const searchStartkey = endKey ? startKey : startKey + 'Begin'
      const searchEndkey = endKey ? endKey : startKey + 'End'
      if (isModel) {
        this.$set(this['model'], searchStartkey, isArry ? dateStrings[0] : null)
        this.$set(this['model'], searchEndkey, isArry ? dateStrings[1] : null)
      } else {
        this.queryParam[searchStartkey] = isArry ? dateStrings[0] : null
        this.queryParam[searchEndkey] = isArry ? dateStrings[1] : null
      }
    },
    getOrder(record, order) {
      if (this.ipagination.current > 1) {
        return (this.ipagination.current - 1) * this.ipagination.pageSize + (parseInt(order) + 1)
      } else {
        return parseInt(order) + 1
      }
    },
    batchDel: function () {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.deleteBatch属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认删除',
          content: '是否删除选中数据?',
          onOk: function () {
            deleteAction(
              that.url.deleteBatch,
              {
                ids: ids,
              },
              that.httpHead
            ).then((res) => {
              if (res.IsSuccess) {
                that.$message.success(res.Msg ? res.Msg : '操作成功')
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.Msg)
              }
            })
          },
        })
      }
    },
    // 删除确认框 没有的传null占位
    handleDeleteClick(id, httpType, params, title, content, callBack) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      this.$confirm({
        title: title || '提示',
        content: content || '是否确定要删除该数据？',
        onOk: function () {
          let delObj = null
          let requestParams = {}
          if (id) {
            requestParams['id'] = id
          } else if (params) {
            requestParams = params
          }
          if (httpType == 'GET') {
            delObj = getAction(that.url.delete, requestParams, that.httpHead)
          } else if (httpType == 'DELETE') {
            delObj = deleteAction(that.url.delete, requestParams, that.httpHead)
          } else if (httpType == 'PUT') {
            delObj = putAction(that.url.delete + '?id=' + id, requestParams, that.httpHead)
          } else {
            delObj = postAction(that.url.delete, requestParams, that.httpHead)
          }
          delObj.then((res) => {
            if (res.IsSuccess) {
              that.$message.success(res.Msg ? res.Msg : '操作成功')
              if (callBack) {
                callBack()
              } else {
                that.loadData()
              }
            } else {
              that.$message.warning(res.Msg)
            }
          })
        },
      })
    },
    /**
     * 删除
     * @param id 删除id参数
     * @param isAddUrl 是否把参数加载地址中
     */
    handleDelete: function (id, isAddUrl) {
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      var that = this
      let delObj = null
      if (isAddUrl) {
        delObj = deleteAction(that.url.delete + '?id=' + id, {}, that.httpHead)
      } else {
        delObj = deleteAction(that.url.delete, { id: id }, that.httpHead)
      }
      delObj.then((res) => {
        if (res.IsSuccess) {
          that.$message.success(res.Msg ? res.Msg : '操作成功')
          that.loadData()
        } else {
          that.$message.warning(res.Msg)
        }
      })
    },
    handleNewDelete: function (checked, event, id) {
      if (!this.url.deleteBatch) {
        this.$message.error('请设置url.nomarlDeleteBatch属性!')
        return
      }
      var that = this
      deleteAction(`${that.url.nomarlDeleteBatch}?ids=${id}&isDelete=${!checked}`, {}, that.httpHead).then((res) => {
        if (res.IsSuccess) {
          that.$message.success(res.Msg ? res.Msg : '操作成功')
          that.loadData()
        } else {
          that.$message.warning(res.Msg)
        }
      })
    },
    handleEdit: function (record) {
      let title = this.title
      if (!title && this.description) {
        title = ' - ' + this.description
      }
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '编辑' + title
    },
    handleAdd: function (record) {
      let title = this.title
      if (!title && this.description) {
        title = ' - ' + this.description
      }
      this.$refs.modalForm.add(record)
      this.$refs.modalForm.title = '新增' + title
    },
    handleMenuClick(e, filedName, callback) {
      switch (e.key) {
        case 1:
          break
        case 2: //批量冻结
          this.batchAudit(false)
          break
        case 3: //批量解冻
          this.batchAudit(true)
          break
        case 4: //批量物理删除
          this.batchDelete(true)
          break
        case 5: //批量启用
          this.onBatchEnableGet(true, filedName)
          break
        case 6: //批量禁用
          this.onBatchEnableGet(false, filedName)
          break
        default:
          if (!callback || typeof callback != 'function') return
          if (this.selectedRowKeys.length <= 0) {
            this.$message.warning('请选择一条数据！')
            return false
          }
          let _this = this
          let ids = _this.selectedRowKeys.join(',')
          callback && callback(ids, _this.selectedRowKeys)
          break
      }
    },
    //单个审核
    handleAudit: function (id, status) {
      let that = this
      getAction(
        that.url.auditBatch,
        {
          ids: id,
          isAudit: status,
        },
        that.httpHead
      ).then((res) => {
        if (res.IsSuccess) {
          that.$message.success(res.Msg ? res.Msg : '操作成功')
          that.loadData()
          that.onClearSelected()
        } else {
          that.$message.warning(res.Msg)
        }
      })
    },
    //批量审核
    batchAudit: function (status) {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条数据！')
        return false
      } else {
        let that = this
        let ids = that.selectedRowKeys.join(',')
        that.$confirm({
          title: '确认操作',
          content: '是否' + (status ? '解冻' : '冻结') + '选中数据?',
          onOk: function () {
            getAction(
              that.url.auditBatch,
              {
                ids: ids,
                isAudit: status,
              },
              that.httpHead
            ).then((res) => {
              if (res.IsSuccess) {
                that.$message.success(res.Msg ? res.Msg : '操作成功')
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.Msg)
              }
            })
          },
        })
      }
    },

    /**
     * 批量 禁用/启用
     * @param {Boolean} enabled true:启用,false:禁用
     */
    handlelBatchEnable: function (enabled) {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条数据！')
        return false
      } else {
        let that = this
        let ids = that.selectedRowKeys.join(',')
        that.$confirm({
          title: '确认操作',
          content: '是否' + (enabled ? '启用' : '禁用') + '选中数据?',
          onOk: function () {
            var url = that.url.enabledBatch
            postAction(url, { ids: ids, isEnabled: enabled }, that.httpHead).then((res) => {
              if (res.IsSuccess) {
                that.$message.success(res.Msg ? res.Msg : '操作成功')
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.Msg)
              }
            })
          },
        })
      }
    },
    /**
     * 批量 禁用/启用  GET请求
     * @param {Boolean} enabled true:启用,false:禁用
     */
    onBatchEnableGet: function (enabled, filedName) {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条数据！')
        return false
      } else {
        let that = this
        let ids = that.selectedRowKeys.join(',')
        that.$confirm({
          title: '确认操作',
          content: '是否' + (enabled ? '启用' : '禁用') + '选中数据?',
          onOk: function () {
            var url = that.url.enabledBatch
            var params = { ids: ids }
            if (!filedName) {
              params['isEnabled'] = enabled
            } else {
              params[filedName] = enabled
            }
            getAction(url, params, that.httpHead).then((res) => {
              if (res.IsSuccess) {
                that.$message.success(res.Msg ? res.Msg : '操作成功')
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.Msg)
              }
            })
          },
        })
      }
    },
    /**
     * 单个删除
     * @param {*} id 需要删除的ID
     */
    handlelDelete: function (id) {
      let that = this
      postAction(that.url.delete, { ids: id }, that.httpHead).then((res) => {
        if (res.IsSuccess) {
          that.$message.success(res.Msg ? res.Msg : '操作成功')
          that.loadData()
          that.onClearSelected()
        } else {
          that.$message.warning(res.Msg)
        }
      })
    },
    //批量删除 or 还原
    batchDelete: function (status) {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条数据！')
        return false
      } else {
        let that = this
        let ids = that.selectedRowKeys.join(',')
        that.$confirm({
          title: '确认操作',
          content: '是否' + (status ? (this.isRecycleList ? '彻底删除' : '删除') : '恢复') + '选中数据?',
          onOk: function () {
            var url = status ? that.url.deleteBatch : that.url.nomarlDeleteBatch
            deleteAction(
              url,
              {
                ids: ids,
                isDelete: status,
              },
              that.httpHead
            ).then((res) => {
              if (res.IsSuccess) {
                that.$message.success(res.Msg ? res.Msg : '操作成功')
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.Msg)
              }
            })
          },
        })
      }
    },
    /**
     * 批量 删除
     */
    handlelBatchDelete: function (id) {
      if (this.selectedRowKeys.length <= 0 && !id) {
        this.$message.warning('请选择一条数据！')
        return false
      } else {
        let that = this
        let ids = id ? id : that.selectedRowKeys.join(',')
        that.$confirm({
          title: '确认操作',
          content: '是否彻底删除选中数据?',
          onOk: function () {
            var url = that.url.deleteBatch
            postAction(url, { ids: ids }, that.httpHead).then((res) => {
              if (res.IsSuccess) {
                that.$message.success(res.Msg ? res.Msg : '操作成功')
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.warning(res.Msg)
              }
            })
          },
        })
      }
    },
    handleDeleteBatch: function (id) {
      this.handlelBatchDelete(id)
    },
    //回收站切换
    handleRecycleList: function (isRecycle) {
      this.isRecycleList = isRecycle
      this.url.list = isRecycle ? this.url.recycleList : this.url.nomarlList
      this.url.deleteBatch = isRecycle ? this.url.recycleDeleteBatch : this.url.nomarlDeleteBatch
      this.loadData(1)
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        this.isorter.SortFieldName = sorter.order ? sorter['columnKey'] : undefined
        if (this.sort) {
          // 订单列表 排序方式
          this.queryParam[this.sort] = 'ascend' == sorter.order ? '1' : '2'
        }
      }
      this.ipagination = pagination
      if (this.tableChangeLoadData) {
        this.loadData()
      }
    },
    handleToggleSearch() {
      this.toggleSearchStatus = !this.toggleSearchStatus
    },
    modalFormOk() {
      // 新增/修改 成功时，重载列表
      this.loadData()
    },
    handleDetail: function (record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = '详情'
      this.$refs.modalForm.disableSubmit = true
    },
    /* 导出 */
    handleExportXls2() {
      let paramsStr = encodeURI(JSON.stringify(this.getQueryParams()))
      let url = `${window._CONFIG['domianURL']}/${this.url.exportXlsUrl}?paramsStr=${paramsStr}`
      window.location.href = url
    },
    handleExportXls(fileName, method, paramUrl = null, loading, httpHead, queryParam, fileType, callBack) {
      if (!fileName || typeof fileName != 'string') {
        fileName = '导出文件'
      }
      if (loading) {
        this[loading] = true
      } else {
        this.ExportDisabled = true
      }

      let param = queryParam ? queryParam : this.getQueryParams()
      param.PageIndex = this.ipagination.current
      param.PageSize = this.ipagination.pageSize

      if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
        param['selections'] = this.selectedRowKeys.join(',')
      }

      downFile(paramUrl ? paramUrl : this.url.exportXlsUrl, param, method, httpHead, callBack).then((data) => {
        if (loading) {
          this[loading] = false
        } else {
          this.ExportDisabled = false
        }
        if (!data) {
          this.$message.warning('文件下载失败')
          return
        }
        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), fileName + (fileType ? fileType : '.xls'))
        } else {
          console.log(2, data)
          callBack && callBack()
          // 生成a标签下载
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', fileName + (fileType ? fileType : '.xls'))
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(url) //释放掉blob对象
        }
      })
    },
    /**
     * 导入
     * @param {*} info
     */
    handleImportExcel(info, callback) {
      let fList = [...info.fileList]
      if (fList && fList.length > 0) {
        this.uploadFileList = []
        this.uploadFileList.push(fList[fList.length - 1])
      }

      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList)
      }
      if (info.file.status === 'done') {
        if (info.file.response.IsSuccess) {
          this.$message.success(`${info.file.name} 文件上传成功`)
          this.loadData()
          this.importExcelAfter(info.file.name)
        } else {
          this.$message.error(`${info.file.name} ${info.file.response.Msg}.`)
          this.importExcelAfter()
        }
      } else if (info.file.status === 'error') {
        this.$message.error(`文件上传失败: ${info.file.msg} `)
        this.importExcelAfter()
      }
    },
    /* 图片预览 */
    getImgView(text) {
      if (text && text.indexOf(',') > 0) {
        text = text.substring(0, text.indexOf(','))
      }
      return text
      //return window._CONFIG['imgDomainURL'] + "/" + text
    },
    /* 文件下载 */
    uploadFile(text) {
      if (!text) {
        this.$message.warning('未知的文件')
        return
      }
      if (text.indexOf(',') > 0) {
        text = text.substring(0, text.indexOf(','))
      }
      window.open(window._CONFIG['domianURL'] + '/sys/common/download/' + text)
    },
    changeMenu1(orgMenu) {
      if (orgMenu == null || orgMenu.length <= 0) {
        return []
      }
      let tempArr = []
      try {
        orgMenu.forEach((item) => {
          var temp = item
          if (item.SubList && item.SubList.length > 0) {
            temp['children'] = this.changeMenu1(item.SubList)
          }
          tempArr.push(temp)
        })
      } catch (e) {
        console.log(e)
      }
      return tempArr
    },
    renderUserInfo(t, r, index) {
      return `${r.name ? r.name : ''} ${r.sex_dictText ? r.sex_dictText : r.sex ? (r.sex == '1' ? '女' : '男') : ''} ${r.age ? r.age + '岁' : ''
        } ${r.phone ? r.phone : ''}`
    },

    onDeleteChanged(record, e) {
      record.IsDelete = e
      console.log(e)

      if (e) {
        this.handleDeleteBatch(record.Id)
      } else {
        this.handleRestore(record.Id)
      }
    },
    /**
     * @description: 获取 OSS web 上传代理信息
     * @param {*}
     * @return {*}
     */
    getWebUploadPolicy(BucketName, Dir, callback) {
      let bName
      getAction(
        this.url.getWebUploadPolicy,
        {
          bucketName: BucketName,
          dir: Dir,
        },
        'OSS'
      ).then((alit) => {
        if (alit && alit.Data && alit.Data.signature) {
          bName = alit.Data.bucketName || BucketName

          if (window._CONFIG.AliOssConfig[bName]) {
            if (!window._CONFIG.AliOssConfig[bName][Dir]) {
              window._CONFIG.AliOssConfig[bName][Dir] = {}
            }
          } else {
            window._CONFIG.AliOssConfig[bName] = {}
            window._CONFIG.AliOssConfig[bName][Dir] = {}
          }
          window._CONFIG.AliOssConfig[bName][Dir].policy = alit.Data.policy
          window._CONFIG.AliOssConfig[bName][Dir].OSSAccessKeyId = alit.Data.accessid
          window._CONFIG.AliOssConfig[bName][Dir].callback = alit.Data.callback
          window._CONFIG.AliOssConfig[bName][Dir].signature = alit.Data.signature
          window._CONFIG.AliOssConfig[bName][Dir].host = alit.Data.host
          window._CONFIG.AliOssConfig[bName][Dir].expire = alit.Data.expire
          window._CONFIG.AliOssConfig[bName][Dir].dir = alit.Data.dir || Dir

          callback && callback(bName)
        } else {
          if (window._CONFIG.AliOssConfig[bName]) {
            window._CONFIG.AliOssConfig[bName][Dir] = undefined
          }
        }
      })
    },
    // 上传图片
    _file(
      callback,
      accept,
      BucketName = window._CONFIG.AliOssConfig['BaseBucketName'],
      Dir = window._CONFIG.AliOssConfig['CommonDir'],
      beforeUpload
    ) {
      let bName
      var hand = document.getElementById('ImageHandler')
      if (!hand) {
        hand = document.createElement('input')
        hand.style.display = 'none'
        hand.id = 'ImageHandler'
        hand.type = 'file'
        document.body.appendChild(hand)
      }
      hand.accept = accept || 'image/*'
      hand.dispatchEvent(new MouseEvent('click'))

      this.getWebUploadPolicy(BucketName, Dir, (res) => {
        bName = res
        hand.onchange = (e) => {
          var files = e.target.files
          if (beforeUpload && !beforeUpload(files)) {
            document.body.removeChild(hand)
            return
          }
          if (files.length && window._CONFIG.AliOssConfig[bName] && window._CONFIG.AliOssConfig[bName][Dir]) {
            var data = new FormData()
            data.append('OSSAccessKeyId', window._CONFIG.AliOssConfig[bName][Dir].OSSAccessKeyId)
            data.append('policy', window._CONFIG.AliOssConfig[bName][Dir].policy)
            data.append('callback', window._CONFIG.AliOssConfig[bName][Dir].callback)
            data.append('Signature', window._CONFIG.AliOssConfig[bName][Dir].signature)
            data.append(
              'key',
              window._CONFIG.AliOssConfig[bName][Dir].dir + '/' + new Date().getTime() + '_' + files[0].name
            )
            data.append('success_action_status', '200')
            data.append('file', files[0])
            this.$http.post(window._CONFIG.AliOssConfig[bName][Dir].host, data).then(
              (res) => {
                if (res && res.Status == 'OK') {
                  callback && callback(true, res.FileUrl, res)
                } else {
                  callback && callback(false, '文件上传失败' + res.Msg)
                }
                // load.hide();
              },
              (e) => {
                window._CONFIG.AliOssConfig[bName][Dir] = undefined
                callback && callback(false, '文件上传出错' + (e.Msg || ''))
                // load.hide();
              }
            )
          }
          document.body.removeChild(hand)
        }
        hand.oncancel = (e) => {
          callback && callback(false, 'cancel')
        }
      })
      // }
    },
    // 上传blob
    uploadBlob(blobData, callback, BucketName, Dir) {
      if (!BucketName) {
        BucketName = window._CONFIG.AliOssConfig['BaseBucketName']
      }
      if (!Dir) {
        Dir = window._CONFIG.AliOssConfig['CommonDir']
      }
      let bName

      this.getWebUploadPolicy(BucketName, Dir, (res) => {
        bName = res
        if (window._CONFIG.AliOssConfig[bName] && window._CONFIG.AliOssConfig[bName][Dir]) {
          var data = new FormData()
          data.append('OSSAccessKeyId', window._CONFIG.AliOssConfig[bName][Dir].OSSAccessKeyId)
          data.append('policy', window._CONFIG.AliOssConfig[bName][Dir].policy)
          data.append('callback', window._CONFIG.AliOssConfig[bName][Dir].callback)
          data.append('Signature', window._CONFIG.AliOssConfig[bName][Dir].signature)
          data.append(
            'key',
            window._CONFIG.AliOssConfig[bName][Dir].dir + '/' + new Date().getTime() // + '_' + files[0].name
          )
          data.append('success_action_status', '200')
          data.append('file', blobData)
          this.$http.post(window._CONFIG.AliOssConfig[bName][Dir].host, data).then(
            (res) => {
              if (res && res.Status == 'OK') {
                callback && callback(res.FileUrl)
              } else {
                callback && callback()
              }
            },
            (e) => {
              window._CONFIG.AliOssConfig[bName][Dir] = undefined
              callback && callback()
            }
          )
        } else {
          callback && callback()
        }
      })
      // }
    },
    /**
     * @description:  通过单个文件ID 获取文件路径
     * @param {Id}文件ID
     * @return {*}
     */
    getFileTempUrl(Id, call) {
      if (!Id) return
      if (Id && Id.indexOf('http') > -1) {
        call && call(Id)
        return
      }
      return new Promise((resolve, reject) => {
        postAction(
          this.url.getFileTempUrl,
          {
            FileIds: [Id],
          },
          'OSS'
        ).then((res) => {
          if (res.IsSuccess && res.Data && res.Data.length) {
            call && call(res.Data[0])
            resolve(res.Data[0])
          }
        })
      })
    },
    /**
     * @description:  通过多个文件ID 获取文件路径
     * @param {Id}文件ID
     * @return {*}
     */
    getMoreFileTempUrls(Ids, call) {
      let urls = []
      if (!(Ids && Ids.length)) {
        call && call(urls)
        return
      }
      let params = []
      Ids.map((item) => {
        if (item && item.indexOf('http') > -1) {
          urls.push({
            FileId: item,
            TempUrl: item,
          })
        } else {
          params.push(item)
        }
      })
      if (!(params && params.length)) {
        call && call(urls)
        return
      }
      return new Promise((resolve, reject) => {
        postAction(
          this.url.getFileTempUrl,
          {
            FileIds: params,
          },
          'OSS'
        ).then((res) => {
          if (res.IsSuccess && res.Data && res.Data.length) {
            urls = urls.concat(res.Data)
            call && call(urls)
            resolve(urls)
          }
        })
      })
    },
    //生成随机 GUID 数
    GUID() {
      let guid =
        this.S4() +
        this.S4() +
        '-' +
        this.S4() +
        '-' +
        this.S4() +
        '-' +
        this.S4() +
        '-' +
        this.S4() +
        this.S4() +
        this.S4()
      return guid
    },
    S4() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
    },
    /**
     * 获取使用规则文本
     */
    getUseRules(record) {
      let value = '默认数据'
      switch (record.CouponType) {
        case 1: //满减券 满200减50元
          value = '满' + record.FullAmount + '减' + record.ReductionAmount + '元'
          break
        case 2: //折扣券 满X元X折
          value = '满' + record.FullAmount + '元' + record.DiscountRate * 10 + '折'
          break
        case 3: //立减券  立减XX元
          value = '立减' + record.ReductionAmount + '元'
          break
        case 4: //减邮券 满X元包邮
          value = '满' + record.ReductionAmount + '元包邮'
          break
      }
      return value
    },
    /**
     * 获取有效期限是文本
     */
    getValidity(record) {
      let value = ''
      switch (record.ValidityModel) {
        case 1: //固定时间段 2021-06-01 00:09:22 至 2021-06-30 23:59:59
          value = record.EffectStartTimeStr + ' ~ ' + record.EffectEndTimeStr
          break
        case 2: //自开始起多少天  自领取时刻起7天内可使用
          value = record.GetAfterDay + '天'
          break
      }
      return value
    },
    /**
     * 获取分页数据-前端自己分页
     */
    getDataListByPage(list, pageIndex, pageSize) {
      let startIndex = pageIndex * pageSize - pageSize
      let endIndex = pageIndex * pageSize - 1
      let newList = []
      for (const i in list) {
        if (i >= startIndex && i <= endIndex) {
          newList.push(list[i])
        }
      }
      return newList
    },

    //修改价格按比例调整其他区域定价
    updateOtherRegionalPrice(priceInfoObj) {
      this.$confirm({
        title: '确认操作',
        content: '当前商品还有其他市场区域价格，是否一起修改？',
        okText: '同时修改',
        cancelText: '关闭',
        onOk: () => {
          this.$refs.OtherGoodsRegionalPriceModalRef.show(priceInfoObj)
        },
      })
    },
    // 如果有其他区域则弹出询问框
    openUpdateAreaPriceConfirm(priceInfoObj) {
      if (priceInfoObj && priceInfoObj.PriceRecordCount > 0) {
        // 如果有其他区域则弹出询问框
        this.updateOtherRegionalPrice(priceInfoObj)
      }
    },
    /**
     * 部分界面接口要求applicationid不能null，必须传1 统一处理
     */
    getAppId() {
      let appId = window.localStorage.getItem('HYSYYLAppId')
      return !appId ? 1 : appId
    },
    // 根据地区编码获取级联完整层级code
    getDefaultValueByCode(areaCode) {
      if (!areaCode) return
      let tempArray = new Array()
      for (let i = 0; i < 3; i++) {
        if (areaCode.length >= 2 * i) {
          tempArray.push(areaCode.substring(2 * i, 2 * i + 2))
        }
      }
      return tempArray
    },
    // 处理客户label树
    labelFilterHandle(groupLabelData) {
      return groupLabelData.map((item, index) => {
        return {
          ...item,
          Id: '10000' + item.Id,
          disabled: true,
          Children: item.Labels,
        }
      })
    },

    handleSortBlur(item) {
      if (!item.Id) return
      if (!this.url.sort) {
        this.$message.warning('请设置url.sort接口api地址')
        return
      }
      this.loading = true
      postAction(
        this.url.sort,
        {
          Id: item.Id,
          Sort: item.Sort,
        },
        this.httpHead
      )
        .then((res) => {
          if (res.IsSuccess) {
            this.$message.success('操作成功!')
            item.isSortEdit = false
            this.$forceUpdate()
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.loading = false

          this.$message.success('操作成功!')
          item.isSortEdit = false
          this.$forceUpdate()
        })
    },
    /**
     * 检查按钮是否有权限
     * @param {按钮id} id
     * @returns
     */
    checkBtnPermissions(id) {
      return this.$store.getters.checkButtonPermissions(id)
    },
    /**
     * 检查table某列是否有权限
     * @param {列名关键字} key
     * @returns
     */
    checkColPermissions(key) {
      return this.$store.getters.checkColumnPermissions(key)
    },
    /**
     * 设置页面标题title
     * @param {*标题内容} title
     * @param {*当前页面索引 一般不填} index
     * @param {* 当前页面路径 可不填} fullPath
     * @returns
     */
    setTitle(title, fullPath, index) {
      if (!title) {
        return
      }
      this.$root.$emit('setPageTitle', index || null, title, fullPath || this.$route.fullPath)
    },

    /**
     * 设置是否有效
     * @param {*} record 操作的数据对象
     * @param {*} checked  使用有效
     * @param {*} httpType  http请求类型
     * @param {*} idKey    参数 id的名称  默认Id
     * @param {*} checkKey 参数 是否有效的名称  默认IsValid
     */
    handleSetValid(record, checked, httpType, idKey, checkKey) {
      // record.loading = true
      let requestAction = postAction
      if (httpType == 'GET') {
        requestAction = getAction
      } else if (httpType == 'PUT') {
        requestAction = putAction
      }
      let params = {}
      params[idKey || 'Id'] = record.Id
      params[checkKey || 'IsValid'] = record.IsValid
      requestAction(this.url.setValidUrl, params, this.httpHead)
        .then((res) => {
          if (res.Data) {
            record.IsValid = checked
            this.$message.success('操作成功！')
          } else {
            record.IsValid = !checked
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          record.IsValid = !checked
          this.$message.error(err.Msg || err)
        })
        .finally(() => {
          record.loading = false
        })
    },
    getPageName(path) {
      if (path.indexOf('/') > -1) {
        let list = path.split('/')
        return list && list.length > 0 ? list[list.length - 1] : null
      } else {
        return path
      }
    },
    /***
     * 详情点击事件
     */
    onDetailClick(detailPath, queryParams) {
      if (!detailPath) {
        this.$message.warning('详情路径参数必填')
      }
      if (this.checkIsKeepAlive(detailPath)) {
        this.clearEditPageCache(detailPath)
      }
      this.$router.push({
        path: detailPath,
        query: queryParams || {},
      })
      // this.cacheUtil.saveEditPageData(this.getPageName(detailPath), { clearCache: true })
    },
    goPage(path, queryParams) {
      if (!path) {
        this.$message.warning('路径参数必填')
        return
      }

      if (this.checkIsKeepAlive(path)) {
        this.clearEditPageCache(path)
      }

      this.$router.push({
        path: path,
        query: queryParams || {},
      })
    },
    checkIsKeepAlive(path) {
      let lastSignIndex = path.lastIndexOf('/') > -1 ? path.lastIndexOf('/') : null
      let pathName = path
      if (lastSignIndex != null) {
        pathName = path.substring(lastSignIndex + 1, path.length)
      } else {
        pathName = path
      }
      let index = this.$store.state.user.excludePageList.indexOf(pathName)
      if (index >= 0) {
        return false
      } else {
        return true
      }
    },
    clearEditPageCache(path) {
      let tableView = this.$refs.tableView || this.$refs.table
      if (tableView) {
        tableView.clearEditPageChaceData(this.getPageName(path))
      }
    },
    /**
     * 页面返回
     * @param {*是否关闭当前页面} close
     * type string 返回的路径不传的话就返回上一个页面 传的话就返回到该页面
     */
    goBack(close, type) {
      switch (close) {
        case true:
          let backPath = ''
          if (typeof (type) == 'string') {
            // this.$root.$emit('getPageList', (data) => {
            //   console.log(data)
            //   if (data.length > 0) {
            //     let path = data.find(item => {

            //     })
            //   }
            // })

            backPath = type
          }
          if (!backPath) {
            this.$root.$emit('removeTabs', this.$route.fullPath)
            this.$router.go(-1)
          } else {
            this.$root.$emit('removeTabs', this.$route.fullPath, 'back', backPath)
          }
          break;
        case false:
          this.$router.go(-1)
          break;
        default:
          this.$router.go(-1)
          break;
      }
    },
    /**
     * 获取金额值，默认保留两位小数
     * @param {* 保留几位小数} size
     */
    getAmountOfMoney(money, size) {
      if (!money || money == undefined) {
        return '0.00'
      }
      if (money instanceof Number) {
        return money.toFixed(size || 2)
      } else {
        return Number(money).toFixed(size || 2)
      }
    },
    /**
     * 获取登录用户userId
     * @returns
     */
    getLoginUserId() {
      return Vue.ls.get(USER_ID)
    },
    checkLoginUserIsHasBtnPerssion(approvalModel, callback) {
      let bool = false
      let userId = this.getLoginUserId()
      let list = approvalModel ? approvalModel.ApprovalUserIds : []
      if (userId && list && list.length > 0) {
        let item = list.find((v) => v == userId)
        if (item) {
          bool = true
        }
      }
      callback && callback(bool)
    },
    /**
     * 此方法用于tableView，用来查找正确的$parent
     * @returns
     */
    IsListMixin() {
      return true
    },
    /**
     * 设置
     * @param {*} param
     */
    setTableStatusAttribute(param) {
      if (this.tab && param) {
        this.table.curStatus = param.curStatus
        this.table.tabStatusKey = param.tabStatusKey
      }
    },
    // 数字为小数保留几位小数
    numFixed(num, length) {
      length = length ? length : 2
      if (Number(num) % 1 === 0) {
        return Number(num)
      } else {
        return Number(Number(num).toFixed(length))
      }
    },
    getAuditIcon(status) {
      if (status == 3) {
        return 'close-circle' //失败红色图标
      } else if (status == 2) {
        return 'check-circle' //成功绿色图标
      } else {
        return 'clock-circle' //进行中绿色图标
      }
    },
    getAuditColor(status) {
      if (status == 3) {
        return 'red'
      } else {
        return '#52c41a'
      }
    },

    /**
     * 保存当前页数据
     * @param {*搜索条件已填数据} searchParams
     * @param {*分页器数据} ipagination
     * @param {*当前TableView的配置数据} table
     */
    saveCurPageCacheData() {
      let pagePathName = this.$route.name
      if (pagePathName) {
        let cacheData = {
          key: pagePathName,
          searchParams: null,
          ipagination: this.ipagination,
          tableConfig: null,
        }
        //获取当前SearchView的参数
        let searchParams = this.$refs.searchView
          ? this.$refs.searchView.queryParam
          : this.$root.queryParam
            ? this.$root.queryParam
            : null
        //参数不为空，并填写了查询条件,才保存
        if (searchParams && Object.keys(searchParams).length > 0) {
          cacheData.searchParams = searchParams
        }
        //获取当前TableView配置
        let table = this.table || null
        //有配置并且此界面是左上角有tab状态切换的界面，才保存
        if (table && table.tabStatusKey && this.url.tabCount) {
          let tabData = { curStatus: table.curStatus, tabStatusKey: table.tabStatusKey }
          cacheData.tableConfig = tabData
        }

        this.cacheUtil.saveListData(pagePathName, cacheData)
      }
    },
    /**
     * 获取指定页数据
     * @param {*} key == $route.name
     * @returns
     */
    getCurPageCacheData(pageName) {
      let pagePathName = pageName ? pageName : this.$route.name
      return this.cacheUtil.getListData(pagePathName)
    },
    /**
     * 清除当前页数据
     */
    clearCurPageCacheSearchData() {
      this.cacheUtil.deleteListData(this.$route.name)
    },
    /**
     * 获取显示金额 主要处理负数 减号在金额符号之前
     * @param {*} price
     * @param {*} precision
     * @returns
     */
    getShowPrice(price, precision) {
      return price == undefined || price == null
        ? '--'
        : (price < 0 ? '-' : '') +
        '￥' +
        Number(price < 0 ? String(price).replace('-', '') : price).toFixed(precision || 2)
    },
    // 设置搜索时间默认值 当月1号到今天
    setThisMonthTime(searchItems, columnKeyName, timeStart, timeEnd) {
      // 月初
      var fristDayDate = new Date();
      fristDayDate.setDate(1);
      var year = fristDayDate.getFullYear();
      var month = fristDayDate.getMonth() + 1; // 月份从0开始，所以需要加1
      var day = fristDayDate.getDate();
      var fristDay = year + "-" + month.toString().padStart(2, "0") + "-" + day.toString().padStart(2, "0");
      // 今天
      var todayDate = new Date();
      var year = todayDate.getFullYear();
      var month = todayDate.getMonth() + 1; // 月份从0开始，所以需要加1
      var day = todayDate.getDate();
      var today = year + "-" + month.toString().padStart(2, "0") + "-" + day.toString().padStart(2, "0");
      // 设置下单时间
      let timeDataIndx = searchItems.findIndex(item => {
        return item.name == columnKeyName
      })

      if (timeDataIndx > -1) {
        searchItems[timeDataIndx].rangeDate = [fristDay, today]
      }

      this.queryParam[timeStart] = fristDay
      this.queryParam[timeEnd] = today

    },
    /**
     * 获取当前时间字符串
     * @param {* 格式化默认年月日 时分秒} formart
     * @returns
     */
    getCurrentDateStr(formart) {
      return moment().format(formart || 'YYYY-MM-DD HH:mm:ss')
    },
    // 获取字符串的长度
    getStrMaxLength(str) {
      // console.log(str)
      let bytesCount = 0;
      if (!str) return bytesCount
      for (var i = 0; i < str.length; i++) {
        var c = str.charAt(i);
        if (/^[\u0000-\u00ff]$/.test(c)) //匹配双字节
        {
          bytesCount += 1;
        }
        else {
          bytesCount += 2;
        }
      }
      // console.log(' 字节长度         ', bytesCount)
      return bytesCount
    },
    // 验证字符串的长度
    validStrMaxLength(rule, value, callback, maxLength, name) {
      // console.log(rule.required)
      if (rule.required && !value) {
        return callback(new Error(`请输入${name || ''}!`))
      }
      let bytesCount = this.getStrMaxLength(value, maxLength)
      if (maxLength && bytesCount > maxLength) {
        return callback(new Error(`${name || ''}不能超过${maxLength}字符!`))
      }
      callback()
      // console.log('字节长度   ', this.getStrMaxLength(value, maxLength))
    },

  },
}
