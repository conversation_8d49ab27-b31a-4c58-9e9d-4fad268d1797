import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import { getAction, putAction, postAction } from '@/api/manage'
const mathjs = require('mathjs')
import moment from 'moment'

export const EditMixin = {
  mixins: [],
  data() {
    return {
      //自定义标题
      title: '',
      gutter: [16, 5],
      areaCodes: [], //省市区选择组件使用
      url: {
        gysUrl: '',
      },
    }
  },
  created() { },
  methods: {
    moment,
    formatToYYYYMMDD(date) {
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD')
    },
    /**
     * 获取登录用户userId
     * @returns
     */
    getLoginUserId() {
      return Vue.ls.get(USER_ID)
    },
    /**
     * 解析身份证
     * @param UUserCard
     * @param num 1 出生日期 2性别 3年龄
     * @returns {*}
     */
    parseIdCard(UUserCard, num) {
      if (UUserCard) {
        if (num === 1) {
          const value = UUserCard.substr(6, 8)
          const year = value.substr(0, 4)
          const month = value.substr(4, 2)
          const day = value.substr(6, 2)
          //获取出生日期
          return year + '-' + month + '-' + day
        }
        if (num === 2) {
          //获取性别
          if (parseInt(UUserCard.substr(16, 1)) % 2 === 1) {
            //男
            return '男'
          } else {
            //女
            return '女'
          }
        }
        if (num === 3) {
          //获取年龄
          // var myDate = new Date()
          // var month = myDate.getMonth() + 1
          // var day = myDate.getDate()
          // var age = myDate.getFullYear() - UUserCard.substring(6, 10) - 1
          // if (
          //   UUserCard.substr(10, 12) < month ||
          //   (UUserCard.substr(10, 12) === month && UUserCard.substr(12, 14) <= day)
          // ) {
          //   age++
          // }
          // return age
          return this.getAge(UUserCard)
        }
      }
    },
    /**
     * 根据身份证获取年龄
     * @param identityCard
     * @returns {number}
     */
    getAge(identityCard) {
      var age = 0
      try {
        var len = (identityCard + '').length
        if (len == 0) {
          return 0
        } else {
          if (len != 15 && len != 18) {
            //身份证号码只能为15位或18位其它不合法
            return 0
          }
        }
        var strBirthday = ''
        if (len == 18) {
          //处理18位的身份证号码从号码中得到生日和性别代码
          strBirthday = identityCard.substr(6, 4) + '/' + identityCard.substr(10, 2) + '/' + identityCard.substr(12, 2)
        }
        if (len == 15) {
          strBirthday =
            '19' + identityCard.substr(6, 2) + '/' + identityCard.substr(8, 2) + '/' + identityCard.substr(10, 2)
        }
        //时间字符串里，必须是“/”
        var birthDate = new Date(strBirthday)
        var nowDateTime = new Date()
        if (nowDateTime.getFullYear() >= birthDate.getFullYear()) {
          age = nowDateTime.getFullYear() - birthDate.getFullYear()
          //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
          if (
            nowDateTime.getMonth() < birthDate.getMonth() ||
            (nowDateTime.getMonth() == birthDate.getMonth() && nowDateTime.getDate() < birthDate.getDate())
          ) {
            age--
          }
        }
      } catch (e) {
        age = 0
      }
      console.log('age = ' + age)
      return age
    },
    //生成随机 GUID 数
    GUID() {
      let guid =
        this.S4() +
        this.S4() +
        '-' +
        this.S4() +
        '-' +
        this.S4() +
        '-' +
        this.S4() +
        '-' +
        this.S4() +
        this.S4() +
        this.S4()
      return guid
    },
    S4() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
    },
    /**
     * 验证折扣率
     * @param {*} label
     * @param {*} rule
     * @param {*} val
     * @param {*} callback
     */
    checkDiscountRate(label, rule, val, callback) {
      if (val) {
        if (isNaN(val)) {
          callback('只能输入数字')
          return
        } else if (parseFloat(val) < 0) {
          callback('只能输入大于0的数字')
          return
        } else if (parseFloat(val) > 0 && !/^(([1-9][0-9]*)|(([0]\.\d{1}|[1-9][0-9]*\.\d{1})))$/.test(val)) {
          callback('只支持一位小数')
          return
        } else if (parseFloat(val) > 10) {
          callback('只支持输入0-10的整数或小数')
          return
        } else {
          callback()
        }
      } else {
        callback('请输入' + label)
      }
    },
    /**
     * 验证金额 可设置最大值，是否能输入0，等
     * @param {*} label
     * @param {*} rule
     * @param {*} val
     * @param {数字 输入最大值} max
     * @param {布尔值 能否输入0,默认不能输入} zero
     * @param {*} callback
     */
    checkMoney(rule, val, callback, max, zero) {
      let bool = zero != undefined ? zero : false
      let expression = bool ?
        /^(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/ :
        /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
      if (val) {
        if (isNaN(val)) {
          callback('只能输入数字')
        } else if (bool ? parseFloat(val) < 0 : parseFloat(val) <= 0) {
          callback('只能输入' + (bool ? '大于等于' : '大于') + '0的数字')
        } else if (!expression.test(val)) {
          callback('最多支持两位小数')
        } else {
          if (max && val > max) {
            callback('最多只能输入' + max)
          } else {
            callback()
          }
        }
      } else {
        callback('请输入')
      }
    },
    /**
     * 验证0-1的最多两位小数的验证，不包含0 和1
     * @param {*} label
     * @param {*} rule
     * @param {*} val
     * @param {*} callback
     */
    checkZeroToOne(rule, val, callback) {
      if (val) {
        if (isNaN(val)) {
          callback('只能输入数字')
          return
        } else if (parseFloat(val) <= 0 || parseFloat(val) >= 1) {
          callback('只能输入0到1的数字')
          return
        } else if (parseFloat(val) > 0 && !/^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(val)) {
          callback('最多支持两位小数')
          return
        } else {
          callback()
        }
      } else {
        callback('请输入')
      }
    },

    /**
     * 计算价格，根据折扣率
     * @param oldPrice 原价
     * @param discountRate 折扣率
     * @returns 返回保留两位小数并四舍五入的结果
     */
    calculatePriceByDiscountRate(oldPrice, discountRate) {
      let price = ''
      if (oldPrice && discountRate && oldPrice > 0 && discountRate >= 0) {
        let result = oldPrice * (discountRate / 10)
        price = result.toFixed(2)
      }
      console.log('oldPrice = ' + oldPrice + ' discountRate = ' + discountRate + ' price = ' + price)
      return price
    },
    // 数字为小数保留几位小数
    numFixed(num, length) {
      length = length ? length : 2
      if (Number(num) % 1 === 0) {
        return Number(num)
      } else {
        return Number(Number(num).toFixed(length))
      }
    },
    // 数字减法处理精度
    numReduce(num1, num2) {
      var baseNum, baseNum1, baseNum2;
      var precision;// 精度
      try {
        baseNum1 = num1.toString().split(".")[1].length;
      } catch (e) {
        baseNum1 = 0;
      }
      try {
        baseNum2 = num2.toString().split(".")[1].length;
      } catch (e) {
        baseNum2 = 0;
      }
      baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
      precision = (baseNum1 >= baseNum2) ? baseNum1 : baseNum2;
      let result = Number(((num1 * baseNum - num2 * baseNum) / baseNum).toFixed(precision));
      return result
    },
    // 数字运算mathjs处理
    // num1 值为数字 可单传 单传只处理数字 做四舍五入
    // num2 值为数字 单传num1且symbol不传运算符时为保留几位小数
    // symbol 运算符 值为toFixed的情况下做toFixed处理
    // length 值为数字 保留几位小数
    numMath(num1, num2, symbol, length) {
      let result = 0
      if (num1 && (!symbol || symbol == 'toFixed')) {
        length = num2
        num2 = undefined
      }
      if (num1 != undefined && num2 != undefined && symbol) {
        let a = mathjs.bignumber(num1);
        let b = mathjs.bignumber(num2);
        let jisuanNum = 0
        switch (symbol) {
          case "+":
            jisuanNum = mathjs.add(a, b)//加
            break;
          case "-":
            jisuanNum = mathjs.subtract(a, b)//减
            break;
          case "*":
            jisuanNum = mathjs.multiply(a, b)//乘
            break;
          case "/":
            jisuanNum = mathjs.divide(a, b)//除
            break;
          default:
            break;
        }
        if (length) {
          // 运算结果做四舍五入处理,不是最终运算时不建议加length,避免精度误差
          result = mathjs.format(jisuanNum, { notation: 'fixed', precision: length })
        } else {
          // 结果遵守数学规则,不会出现很多小数,不四舍五入
          result = mathjs.format(jisuanNum)
        }
      } else if (num1 && num2 == undefined) {
        // Math严格遵守四舍五入处理
        length = length ? length : 2
        if (symbol == 'toFixed') {
          result = num1.toFixed(length)
        } else {
          let str = '1'
          for (let i = 0; i < length; i++) {
            str += '0'
          }
          str = Number(str)
          result = Math.round(num1 * str) / str
        }
      }
      result = Number(result)
      return result
    },
    /**
     * 计算价格，根据折扣率
     * @param oldPrice 原价
     * @param nowPrice 现价
     * @returns 返回折扣率 保留一位小数
     */
    calculateDiscountRateByPrice(oldPrice, nowPrice) {
      let discountRate = ''
      if (oldPrice && nowPrice && oldPrice > 0 && nowPrice > 0) {
        let result = (nowPrice / oldPrice) * 10
        discountRate = result.toFixed(1)
      }
      console.log('oldPrice = ' + oldPrice + ' nowPrice = ' + nowPrice + ' discountRate = ' + discountRate)
      return discountRate
    },
    // 根据地区编码获取级联完整层级code
    getDefaultValueByCode(areaCode) {
      if (!areaCode) return []
      let tempArray = new Array()
      for (let i = 0; i < 3; i++) {
        if (areaCode.length >= 2 * i) {
          tempArray.push(areaCode.substring(2 * i, 2 * i + 2))
        }
      }
      return tempArray
    },

    // 处理客户label树
    labelFilterHandle(groupLabelData) {
      return groupLabelData.map((item, index) => {
        return {
          ...item,
          Id: '10000' + item.Id,
          disabled: true,
          Children: item.Labels,
        }
      })
    },
    /**
     * 获取SelectCityAreas默认回显数据
     * @param {*} areaCode
     */
    getSelectCityAreasDefaultValueByCode(areaCode) {
      if (!areaCode) return []
      let tempArray = new Array()
      for (let i = 0; i < 3; i++) {
        if (areaCode.length >= 2 * i) {
          tempArray.push(areaCode.substring(0, 2 * i + 2))
        }
      }
      return tempArray
    },
    /**
     * 获取商品扩展属性显示内容
     * @param {*} item
     */
    getGoodsAttributeValueText(attribute) {
      let value = ''
      if (attribute) {
        switch (attribute.EnumAttributeFormat) {
          case 1: //文本
          case 2: //段落
          case 3: //数字
          case 4: //日期
          case 5: //图片
          case 6: //视频
          case 7: //文档
            value = attribute.AttributeValue
            break
          case 8: //单选
            if (attribute.CheckItemDto) {
              attribute.CheckItemDto.map((x) => {
                if (x.IsCheck) {
                  value = x.Name
                }
              })
            }
            break
          case 9: //复选
            if (attribute.CheckItemDto) {
              let str = ''
              attribute.CheckItemDto.map((x) => {
                if (x.IsCheck) {
                  str += x.Name + ','
                }
              })
              if (str) {
                value = str.substring(0, str.length - 1)
              }
            }
            break
        }
      }

      return value || '--'
    },
    /**
     * 检查按钮是否有权限
     * @param {按钮id} id
     * @returns
     */
    checkBtnPermissions(id) {
      return this.$store.getters.checkButtonPermissions(id)
    },
    /**
     * 检查table某列是否有权限
     * @param {列名关键字} key
     * @returns
     */
    checkColPermissions(key) {
      return this.$store.getters.checkColumnPermissions(key)
    },
    /**
     * 选择地址区域
     **/

    onAreasChange(val, node, model, key) {
      if (!val) {
        return
      }
      this.areaCodes = val
      if (model && key) {
        if (typeof node != 'undefined') {
          model[key + 'Code'] = node[node.length - 1].code
          model[key + 'Id'] = node[node.length - 1].id
          model[key + 'Name'] = node[node.length - 1].source.FullName
        } else {
          model[key + 'Code'] = null
          model[key + 'Id'] = null
          model[key + 'Name'] = ''
        }
        // if (form) {
        //   form.clearValidate([key + 'Code'])
        // }
      }
    },
    /**
     * 是否是中文汉字
     * @param {内容} value
     * @returns
     */
    IsItChinese(value) {
      // return /^[\u4e00-\u9fa5]+$/.test(value)
      return /^[\u4e00-\u9fa5()（）]+$/.test(value)
    },
    /**
     * 验证中文汉字
     * @param {*} label
     * @param {*} rule
     * @param {*} val
     * @param {*} callback
     */
    checkChinese(rule, val, callback) {
      if (rule.required) {
        if (val) {
          if (!this.IsItChinese(val)) {
            callback('只能输入中文')
          } else {
            callback()
          }
        } else {
          callback('请输入')
        }
      } else {
        if (val) {
          if (!this.IsItChinese(val)) {
            callback('只能输入中文')
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
    },
    /**
     * 验证手机号
     */
    checkPhone(rule, val, callback) {
      if (val) {
        if (!/^1[3456789]\d{9}$/.test(val)) {
          callback('手机号码有误！')
          return
        }
      } else if (rule.required) {
        callback('请输入')
        return
      }
      callback()
    },
    /***
     * 验证手机号或者座机
     */
    checkTelphone1(rule, val, callback) {
      const tel = /^0\d{2,3}-?\d{7,8}$/
      const phone = /^1[3456789]\d{9}$/
      if (val === 0 || val) {
        if (!phone.test(val) && !tel.test(val)) {
          callback('电话号码有误！')
          return
        }
      } else if (rule.required) {
        callback('请输入')
        return
      }
      callback()
    },
    /***
     * 验证手机号或者座机 数字加横杠
     */
    checkTelphone(rule, val, callback) {
      const tel = /^[0-9-]*$/
      if (val === 0 || val) {
        if (!tel.test(val)) {
          callback('电话号码有误！')
          return
        }
      } else if (rule.required) {
        callback('请输入')
        return
      }
      callback()
    },
    // 验证银行卡号
    checkBankNo(rule, val, callback) {
      // 1至30位的数字和-—验证 正则
      let bankNumber = /^[\d-—]{1,30}$/
      // let ruleStr = /^(\d{12}|\d{16}|\d{17}|\d{19})$/ 
      if (val === 0 || val) {
        if (!bankNumber.test(val)) {
          callback('银行卡号有误！')
          return
        } else if (val.length > 30) {
          callback('银行卡号不能超过30位！')
          return
        }
      } else if (rule.required) {
        callback('请输入！')
        return
      }
      callback()
    },
    //验证身份证号
    checkIdNo(rule, val, callback) {
      let _IDRe18 = /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
      let _IDre15 = /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/
      if (val) {
        if (!_IDRe18.test(val) && !_IDre15.test(val)) {
          callback('身份证号码有误！')
          return
        }
      }
      callback()
    },
    /**
     * min-max范围的大于0的整数
     * @param {*} label
     * @param {*} rule
     * @param {*} val
     * @param {*} callback
     */
    checkIntInMinToMax(rule, val, callback, min, max) {
      if (val) {
        if (isNaN(val)) {
          callback('只能输入数字')
          return
        } else if (!/(^[1-9]\d*$)/.test(val)) {
          callback('只能输入大于0的整数')
          return
        } else {
          if (min && max && (val < min || val > max)) {
            callback('只能输入' + min + '~' + max + '的数字')
            return
          }
        }
      } else {
        if (!rule.required) {
          callback()
          return
        } else {
          callback('请输入')
          return
        }
      }
      callback()
    },
    /**
     * 大于0的整数
     * @param {*} label
     * @param {*} rule
     * @param {*} val
     * @param {*} callback
     */
    checkIntNumber(rule, val, callback) {
      if (rule.required && !val) {
        callback('请输入')
        return
      }
      const intReg = /^\d*$/
      if ((val && !intReg.test(val)) || (val == 0 && val != '')) {
        callback('只能输入大于0的整数')
        return
      }
      callback()
    },
    /**
     * 只能输入数字（不含小数点）
     * @param {*} label
     * @param {*} rule
     * @param {*} val
     * @param {*} callback
     */
    checkIntNumber2(rule, val, callback) {
      const intReg = /^\d*$/
      if ((val || val == 0) && !intReg.test(val)) {
        callback('只能输入数字')
        return
      }
      if (!rule.required) {
        callback()
        return
      }
      if (rule.required && !val) {
        callback('请输入')
        return
      }
      callback()
    },
    /**
     * 通过录入转换方式交互
     * @param {*} label
     * @param {*} rule
     * @param {*} val
     * @param {*} callback
     */
    checkIntTime(rule, val, callback) {
      if (rule.required && !val) {
        callback('请输入')
        return
      }
      if (!rule.required && !val) {
        callback()
        return
      }
      if (
        this.IsItChinese(val) ||
        (val.length < 8 && val.indexOf('-') < 0) ||
        (val.length < 10 && val.indexOf('-') > -1)
      ) {
        callback('请按照正确格式输入(20230620)')
        return
      }
      if (val.indexOf('-') < 0 && val.length > 8) {
        callback('请按照正确格式输入(20230620)')
        return
      }
      if (val.length == 8 && val.indexOf('-') < 0) {
        let date = val.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3')
        let arr = date.split('-')
        if (arr.length == 3 && (arr[1] > 12 || arr[1] == '00')) {
          callback('请按照正确格式输入(20230620),月份（1-12）')
          return
        }
        if (arr.length == 3 && (arr[2] > 31 || arr[2] == '00')) {
          callback('请按照正确格式输入(20230620),天数（1-31）')
          return
        }
        callback()
        return
      }
      if (val.length >= 8 && val.indexOf('-') >= 0) {
        let arr = val.split('-')
        if (arr.length != 3) {
          callback('请按照正确格式输入(20230620)')
          return
        }
        if (arr.length == 3 && arr[0].length < 4) {
          callback('请按照正确格式输入(20230620)')
          return
        }
        if (arr.length == 3 && (arr[1] > 12 || arr[1] == '00')) {
          callback('请按照正确格式输入(20230620),月份（1-12）')
          return
        }
        if (arr.length == 3 && (arr[2] > 31 || arr[2] == '00')) {
          callback('请按照正确格式输入(20230620),天数（1-31）')
          return
        }
        // 验证日期是否合法
        if (!this.isValidDate(val)) {
          callback('输入的日期不合法(当年当月没有这天)')
          return
        }
        // console.log(val, this.isValidDate(val))
        callback()
        return
      }
      callback()
    },
    // 验证日期是否合法
    isValidDate(strInputDate) {
      if (strInputDate == "") return false;
      strInputDate = strInputDate.replace(/-/g, "/");
      var d = new Date(strInputDate);
      if (isNaN(d)) return false;
      var arr = strInputDate.split("/");
      return ((parseInt(arr[0], 10) == d.getFullYear()) && (parseInt(arr[1], 10) == (d.getMonth() + 1)) && (parseInt(arr[2], 10) == d.getDate()));
    },
    handleReturnTime(e, key) {
      let val = e.target.value
      if (val.length == 8 && val.indexOf('-') < 0) {
        let date = val.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3')
        this.$set(this['model'], key, date)
        // console.log(this['model'])
      }
    },
    /**
     * 获取显示日期数据
     * @param {*} val
     * @returns
     */
    getShowDate(val) {
      let date = val
      if (val.length == 8 && val.indexOf('-') < 0) {
        date = val.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3')
      }
      return date
    },
    /**
     * 设置页面标题title
     * @param {*标题内容} title
     * @param {*当前页面索引 一般不填} index
     * @param {* 当前页面路径 可不填} fullPath
     * @returns
     */
    setTitle(title, fullPath, index) {
      if (!title) {
        return
      }
      this.$root.$emit('setPageTitle', index || null, title, fullPath || this.$route.fullPath)
    },
    /**
     * 页面返回
     * @param {*是否关闭当前页面} close
     * type string 返回的路径不传的话就返回上一个页面 传的话就返回到该页面
     */
    goBack(close, type) {
      switch (close) {
        case true:
          let backPath = ''
          if (typeof (type) == 'string') {
            backPath = type
          }
          if (!backPath) {
            this.$root.$emit('removeTabs', this.$route.fullPath)
            this.$router.go(-1)
          } else {
            this.$root.$emit('removeTabs', this.$route.fullPath, 'back', backPath)
          }
          break;
        case false:
          this.$router.go(-1)
          break;
        default:
          this.$router.go(-1)
          break;
      }
    },
    /**
     * 获取金额值，默认保留两位小数
     * @param {* 保留几位小数} size
     */
    getAmountOfMoney(money, size) {
      if (!money) {
        return money
      }
      if (money instanceof Number) {
        return money.toFixed(size || 2)
      } else {
        return Number(money).toFixed(size || 2)
      }
    },
    /**
     * 时间change
     * @description: 时间回调
     */
    onTimeChange(dates, dateStrings, startKey, endKey, isModel) {
      const isArry = dates.length == 2
      const searchStartkey = endKey ? startKey : startKey + 'Begin'
      const searchEndkey = endKey ? endKey : startKey + 'End'
      if (isModel) {
        this.$set(this['model'], searchStartkey, isArry ? dateStrings[0] : null)
        this.$set(this['model'], searchEndkey, isArry ? dateStrings[1] : null)
      } else {
        this.queryParam[searchStartkey] = isArry ? dateStrings[0] : null
        this.queryParam[searchEndkey] = isArry ? dateStrings[1] : null
      }
    },
    getAuditIcon(status) {
      if (status == 3) {
        return 'close-circle' //失败红色图标
      } else if (status == 2) {
        return 'check-circle' //成功绿色图标
      } else {
        return 'clock-circle' //进行中绿色图标
      }
    },
    getAuditColor(status) {
      if (status == 3) {
        return 'red'
      } else {
        return '#52c41a'
      }
    },
    // 单个占用
    singleOccupyFun(url, params, httpHead, AuditId, callback) {
      if (!AuditId) {
        return
      }
      if (!params) {
        params = {}
      }
      let linkUrl = url + '?id=' + AuditId
      this.isSingleOccupy = false
      postAction(linkUrl, params, httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        } else {
          this.isSingleOccupy = true
          this.$message.success('占用成功')
          callback && callback()
        }
      })
    },
    // 删除单个占用
    delSingleOccupyFun(url, params, httpHead, AuditId) {
      if (!AuditId) {
        return
      }
      if (!params) {
        params = {}
      }
      // if (this.isSingleOccupy) {
      //   return
      // }
      let linkUrl = url + '?id=' + AuditId
      postAction(linkUrl, params, httpHead).then((res) => {
        if (res.IsSuccess && res.Data) {
          if (!res.IsSuccess) {
            this.$message.warning(res.Msg)
            return
          } else {
            this.$message.success('删除占用成功')
          }
        }
      })
    },
    // 列合并处理数据
    // data 数组数据
    // oldKey 父级要合并的key
    // key 此次要合并的key
    // rowSpanKey 列合并的rowSpan的key
    // lastKey 最深层次要合并的key
    // firstKey 最父级要合并的key
    // x<y,返回-1，就是不改变位置
    // x>y,返回1,就要交换位置
    // 返回0意思是顺序不变
    getListRowSpanData(data, oldKey, key, rowSpanKey, lastKey, firstKey) {
      if (!data) {
        data = []
      }
      // 先排序
      if (!oldKey) {
        data.sort((a, b) => a[key] - b[key])
      } else {
        data.sort(function (a, b) {
          if (a[oldKey] != b[oldKey]) {
            return a[oldKey] - b[oldKey];
          }
          if (a[key] < b[key]) {
            return -1;
          } else if (a[key] > b[key]) {
            return 1;
          } else {
            return 0;
          }
        });
      }
      // 找相同的项出现次数
      const arr = data
      let countMap = {};
      arr.forEach(obj => {
        countMap[obj[key]] = (countMap[obj[key]] || 0) + 1;
      });
      // 赋值rowSpan
      const uniqueNames = new Set();
      arr.forEach(obj => {
        if (!uniqueNames.has(obj[key])) {
          obj[rowSpanKey] = countMap[obj[key]];
          uniqueNames.add(obj[key]);
        } else {
          obj[rowSpanKey] = 0;
        }
      });
      // 最终数据处理firstKey是0的时候重新排序,不为0时不排序
      if (lastKey && key == lastKey) {
        let dataFirstRowSpan = null
        if (lastKey && key == lastKey) {
          firstKey = firstKey ? firstKey : 'rowSpan'
          dataFirstRowSpan = arr.length > 0 ? arr[0][firstKey] : null
          console.log('dataFirstRowSpan', arr, firstKey, lastKey, dataFirstRowSpan)
        }
        // 排序第一次
        if (dataFirstRowSpan == 0) {
          if (!oldKey) {
            arr.sort((a, b) => a[key] - b[key])
          } else {
            arr.sort(function (a, b) {
              if (a[oldKey] != b[oldKey]) {
                return a[oldKey] - b[oldKey];
              }
              if (a[key] < b[key]) {
                return -1;
              } else if (a[key] > b[key]) {
                return 1;
              } else {
                return 0;
              }
            });
          }
        }
        // 假如还是0再反向排一次
        dataFirstRowSpan = arr.length > 0 ? arr[0][firstKey] : null
        if (dataFirstRowSpan == 0) {
          if (!oldKey) {
            arr.sort((a, b) => a[key] - b[key])
          } else {
            arr.sort(function (a, b) {
              if (a[oldKey] != b[oldKey]) {
                return a[oldKey] - b[oldKey];
              }
              if (a[key] < b[key]) {
                return 1;
              } else if (a[key] > b[key]) {
                return -1;
              } else {
                return 0;
              }
            });
          }
        }
      }
      if (arr[0][firstKey] == 0) {
        let firstRowsKeyArray = arr.map(item => {
          return item[firstKey]
        })
        let sameFirstRowsKeyArray = Array.from(new Set(firstRowsKeyArray))
        // 若rowSpan首位重复连续都为0,且只有0和另外一个数字
        if (sameFirstRowsKeyArray.length == 2 && sameFirstRowsKeyArray.indexOf(0) >= 0) {
          firstRowsKeyArray.sort((a, b) => b - a)
          arr.map((item, index) => {
            item[firstKey] = firstRowsKeyArray[index]
          })
          console.log('首位rowSpan重复连续都为0,且只有0和另外一个数字', firstRowsKeyArray, arr)
        } else {
          // 若rowSpan首位重复连续都为0,有0和多个数字,则默认第一个为连续有几个0的项
          console.log('首位rowSpan重复连续都为0,有0和多个数字,则默认第一个为连续有几个0的项')
          let count = 0
          for (let i = 0; i < arr.length; i++) {
            if (arr[0][firstKey] == 0 && arr[i][firstKey] !== 0) {
              count = i
              break;
            }
          }
          arr[0][firstKey] = count
        }

      }
      // console.log('ed', countMap, arr);
      return arr;
    },
    // 获取字符串的长度
    getStrMaxLength(str) {
      // console.log(str)
      let bytesCount = 0;
      if (!str) return bytesCount
      for (var i = 0; i < str.length; i++) {
        var c = str.charAt(i);
        if (/^[\u0000-\u00ff]$/.test(c)) //匹配双字节
        {
          bytesCount += 1;
        }
        else {
          bytesCount += 2;
        }
      }
      // console.log(' 字节长度         ', bytesCount)
      return bytesCount
    },
    // 验证字符串的长度
    validStrMaxLength(rule, value, callback, maxLength, name) {
      // console.log(rule.required)
      if (rule.required && !value) {
        return callback(new Error(`请输入${name || ''}!`))
      }
      let bytesCount = this.getStrMaxLength(value, maxLength)
      if (maxLength && bytesCount > maxLength) {
        return callback(new Error(`${name || ''}不能超过${maxLength}字符!`))
      }
      callback()
      // console.log('字节长度   ', this.getStrMaxLength(value, maxLength))
    },
    // 处理表格合并， rowKey为需要合并数据的相同值标识，如果数据本身没有可以自己构造
    setTableMerge(resData, rowKey, noSort) {
      if (resData && resData.length > 0) {
        // 相同的值排序
        if (!noSort) {
          resData = resData.sort((a, b) => {
            return a[rowKey] > b[rowKey] ? 1 : -1
          })
        }

        let groupedArrObj = resData.reduce((acc, obj) => {
          let key = obj[rowKey]
          if (!acc[key]) {
            acc[key] = []
          }
          acc[key].push(obj)
          return acc
        }, {})
        let samePolicyIdArray = Object.entries(groupedArrObj) || []
        if (samePolicyIdArray.length > 0) {
          resData.map((item, index) => {
            let rt = samePolicyIdArray.find((r) => {
              return (item[rowKey] == r[0]) && r[1].length > 1
            })
            if (rt) {
              // 深拷贝 不然会出现无限循环sameList的bug
              // item.sameList = lodash.cloneDeep(rt[1])
              item.sameList = rt[1]
            }
          })
          resData.map((item, index) => {
            if (item.sameList && item.sameList.length > 0) {
              item.sameList.map((rItem, rIndex) => {
                if (rIndex == 0) {
                  rItem.rowSpan = item.sameList.length
                } else {
                  rItem.rowSpan = 0
                }
              })
            }
          })
          // 删除sameList
          resData.map((item, index) => {
            delete item.sameList;
          })
        }
        return resData
      } else {
        return []
      }
    },
  },
}