/**
 * 必须搭配封装的新组件SimpleTable才能使用
 */
import { deleteAction, getAction, downFile, postAction, putAction } from '@/api/manage'
export const SimpleMixin = {
  data() {
    return {
      queryParam: {},
      linkHttpHead: 'P33000',
      linkUrl: {
        list: '',
        isValidUrl: '',
      },
    }
  },
  created() { },
  methods: {
    // 是否有效
    onSetValid(record, checked, httpType, idKey, checkKey) {
      let requestAction = postAction
      if (httpType == 'GET') {
        requestAction = getAction
      } else if (httpType == 'PUT') {
        requestAction = putAction
      }
      let params = {}
      params[idKey || 'Id'] = record.Id
      params[checkKey || 'IsValid'] = record.IsValid
      requestAction(this.linkUrl.isValidUrl, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            record.IsValid = checked
            if (this.onSetValidLoadData) {
              const newQueryParam = Object.assign({}, this.queryParam, this.$refs.SimpleSearchArea.queryParam || {})
              this.$refs.table.loadDatas(null, newQueryParam)
            }
            this.$message.success('操作成功！')
          } else {
            record.IsValid = !checked
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          record.IsValid = !checked
          this.$message.error(err.Msg || err)
        })
        .finally(() => {
          record.loading = false
        })
    },
    modalFormOk() {
      // 新增/修改 成功时，重载列表
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    modalOk() {
      // 新增/修改 成功时，重载列表 有搜索的时候
      this.queryParam = this.$refs.SimpleSearchArea.queryParam || {}
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 导出
    handleExportXls(fileName, method, paramUrl = null, loading, httpHead, queryParam, fileType, callBack) {
      this.$refs.table.handleExportXls(fileName, method, paramUrl, loading, httpHead, queryParam, fileType, callBack)
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type, extendParams, searchType) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
      }
      this.$refs.table.loadDatas(1, queryParam, null, type, extendParams, searchType)
    },

    // 判断是否从消息进入页面
    checkedFromMsg(msgKey) {
      // 判断是消息推送进来的不
      let url = decodeURI(localStorage.getItem('url'))
      if (url && url.split('bustype=')[1] && url.split('bustype=')[1].split('&')[0] == msgKey) {
        if (url.split('&scene=')[1].split('&')[0] == 'info') {
          //  详情页面
          this.$refs.iOrdersDetailsModal.show(url.split('id=')[1])
        } else {
          // list 多条数据
          if (url.split('key=')[1]) {
            let obj = url.split('key=')[1].split('&id=')[0]
            if (obj.indexOf('%') != -1) {
              const searchRegExp = /%3A/g
              obj.replace(searchRegExp, ':')
              let newurlObj = obj.replace(searchRegExp, ':')
              this.queryParam = Object.assign(this.queryParam, JSON.parse(newurlObj))
            } else {
              this.queryParam = Object.assign(this.queryParam, JSON.parse(obj))
            }
          } else {
            this.queryParam = {}
          }
        }
      }

      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 获取列表数量
    getListAmount(e) {
      // 数据是对象的普通数量
      if (e.constructor === Object) {
        let keyArray = Object.keys(e)
        let obj = e || {}
        this.tab.statusList.forEach((item, index) => {
          keyArray.forEach((r, rindex) => {
            if (item.key == r) {
              item.count = obj[item.key]
            }
          })
        })
      } else if (e.constructor === Array) {
        // 数据是数组的普通数量
        let array = e || []
        let arrNum = 0
        if (!e || (e && e.length == 0)) {
          return
        }
        this.tab.statusList.forEach((item, index) => {
          item.count = 0
          array.forEach((rItem, rIndex) => {
            if (item.value == rItem[this.tab.statusKey]) {
              item.count = rItem.Count
              arrNum += rItem.Count
            }
          })
        })
        this.tab.statusList[0].count = arrNum
      }
    },
    // 选中表格事件
    selectedRows(selectedRowKeys, selectedRows) { },
    // 状态切换
    changeTab(value, statusKey) {
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      if (statusKey) {
        this.queryParam[statusKey] = value
      }
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 首页数据定位
    analysisLocation() {
      let type = this.$root.indexOrderKey
      if (type) {
        this.tab.status = type
        this.$refs.SimpleSearchArea.queryParam[this.tab.statusKey] = type
        this.queryParam[this.tab.statusKey] = type
        this.$refs.table.loadDatas(1, this.queryParam)
        this.$root.indexOrderKey = ''
      }
    },
    // 判断是否从消息进入页面
    checkedFromMsg(msgKey, msgFuc = {}) {
      // 判断是消息推送进来的不
      let url = decodeURI(localStorage.getItem('url'))
      let bustypeKey = url && url.split('bustype=')[1] && url.split('bustype=')[1].split('&')[0]
      // 若msgKey是数组
      if (msgKey instanceof Array) {
        if (msgKey.indexOf(bustypeKey) > -1) {
          this.fromMsgFuc(url, msgFuc)
        }
      } else if (bustypeKey == msgKey) {
        // 若msgKey是字符串
        this.fromMsgFuc(url, msgFuc)
      }
      this.$refs.table.loadDatas(1, this.queryParam)
      this.$nextTick(() => {
        this.$refs.SimpleSearchArea.setQueryParam(this.queryParam)
      })
    },
    fromMsgFuc(url, msgFuc) {
      localStorage.removeItem('url')
      if (url.split('&scene=')[1].split('&')[0] == 'info') {
        //  详情页面
        let id = this.getQueryString(url, 'id')
        this.$refs.detailsModal.show(id)
      } else {
        // list 多条数据
        let keyVal = this.getQueryString(url, 'key')
        if (keyVal) {
          if (keyVal.indexOf('%') != -1) {
            const searchRegExp = /%3A/g
            keyVal.replace(searchRegExp, ':')
            let newurlObj = keyVal.replace(searchRegExp, ':')
            this.queryParam = Object.assign(this.queryParam, JSON.parse(newurlObj))
          } else {
            this.queryParam = Object.assign(this.queryParam, JSON.parse(keyVal))
          }
        } else {
          this.queryParam = {}
        }
      }
    },
    // 获取网址的参数
    getQueryString(url, name) {
      var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
      var r = url.substr(1).match(reg)
      if (r != null) {
        return unescape(r[2])
      }
      return null
    },
    // 设置时间
    setData(startTimeKey, endTimeKey, dayNum) {
      var d = new Date()
      var endTime = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate()
      var startTime = this.getBeforeDate(dayNum)
      this.queryParam[startTimeKey] = startTime
      this.queryParam[endTimeKey] = endTime
      this.$refs.SimpleSearchArea.queryParam[startTimeKey] = startTime
      this.$refs.SimpleSearchArea.queryParam[endTimeKey] = endTime
      this.searchInput[3].rangeDate = [this.$moment(startTime), this.$moment(endTime)]
    },
    // 获取之前的时间
    getBeforeDate(n) {
      var n = n
      var d = new Date()
      var year = d.getFullYear()
      var mon = d.getMonth() + 1
      var day = d.getDate()
      if (day <= n) {
        if (mon > 1) {
          mon = mon - 1
        } else {
          year = year - 1
          mon = 12
        }
      }
      d.setDate(d.getDate() - n)
      year = d.getFullYear()
      mon = d.getMonth() + 1
      day = d.getDate()
      var s = year + '-' + (mon < 10 ? '0' + mon : mon) + '-' + (day < 10 ? '0' + day : day)
      return s
    },
    getAppId() {
      return window._CONFIG['ApplicationId'] == 2 ? window._CONFIG['ApplicationId'] : 1
    },
    // 删除占用
    delOccupyFun(url, params, httpHead) {
      postAction(url, params, httpHead).then((res) => {
        if (res.IsSuccess && res.Data) {
          this.$message.success('操作成功')
          this.$refs.table.loadDatas(1, this.queryParam)
        }
      })
    },
    /***
     * 详情点击事件
     */
    onDetailClick(detailPath, queryParams) {
      if (!detailPath) {
        this.$message.warning('详情路径参数必填')
      }
      if (this.checkIsKeepAlive(detailPath)) {
        this.clearEditPageCache(detailPath)
      }
      this.$router.push({
        path: detailPath,
        query: queryParams || {},
      })
      // this.cacheUtil.saveEditPageData(this.getPageName(detailPath), { clearCache: true })
    },
    goPage(path, queryParams) {
      if (!path) {
        this.$message.warning('路径参数必填')
        return
      }

      if (this.checkIsKeepAlive(path)) {
        this.clearEditPageCache(path)
      }

      this.$router.push({
        path: path,
        query: queryParams || {},
      })
    },
    /**
     * 页面返回
     * @param {*是否关闭当前页面} close
     * type string 返回的路径不传的话就返回上一个页面 传的话就返回到该页面
     */
    goBack(close, type) {
      switch (close) {
        case true:
          let backPath = ''
          if (typeof (type) == 'string') {
            backPath = type
          }
          if (!backPath) {
            this.$root.$emit('removeTabs', this.$route.fullPath)
            this.$router.go(-1)
          } else {
            this.$root.$emit('removeTabs', this.$route.fullPath, 'back', backPath)
          }
          break;
        case false:
          this.$router.go(-1)
          break;
        default:
          this.$router.go(-1)
          break;
      }
    },
    // 检测是否缓存页面
    checkIsKeepAlive(path) {
      let lastSignIndex = path.lastIndexOf('/') > -1 ? path.lastIndexOf('/') : null
      let pathName = path
      if (lastSignIndex != null) {
        pathName = path.substring(lastSignIndex + 1, path.length)
      } else {
        pathName = path
      }
      let index = this.$store.state.user.excludePageList.indexOf(pathName)
      if (index >= 0) {
        return false
      } else {
        return true
      }
    },
    clearEditPageCache(path) {
      let tableView = this.$refs.tableView || this.$refs.table
      if (tableView) {
        tableView.clearEditPageChaceData(this.getPageName(path))
      }
    },
    // 列合并处理数据
    // data 数组数据
    // oldKey 父级要合并的key
    // key 此次要合并的key
    // rowSpanKey 列合并的rowSpan的key
    // lastKey 最深层次要合并的key
    // firstKey 最父级要合并的key
    // x<y,返回-1，就是不改变位置
    // x>y,返回1,就要交换位置
    // 返回0意思是顺序不变
    getListRowSpanData(data, oldKey, key, rowSpanKey, lastKey, firstKey) {
      if (!data) {
        data = []
      }
      // 先排序
      if (!oldKey) {
        data.sort((a, b) => a[key] - b[key])
      } else {
        data.sort(function (a, b) {
          if (a[oldKey] != b[oldKey]) {
            return a[oldKey] - b[oldKey];
          }
          if (a[key] < b[key]) {
            return -1;
          } else if (a[key] > b[key]) {
            return 1;
          } else {
            return 0;
          }
        });
      }
      // 找相同的项出现次数
      const arr = data
      let countMap = {};
      arr.forEach(obj => {
        countMap[obj[key]] = (countMap[obj[key]] || 0) + 1;
      });
      // 赋值rowSpan
      const uniqueNames = new Set();
      arr.forEach(obj => {
        if (!uniqueNames.has(obj[key])) {
          obj[rowSpanKey] = countMap[obj[key]];
          uniqueNames.add(obj[key]);
        } else {
          obj[rowSpanKey] = 0;
        }
      });
      // 最终数据处理firstKey是0的时候重新排序,不为0时不排序
      if (lastKey && key == lastKey) {
        let dataFirstRowSpan = null
        if (lastKey && key == lastKey) {
          firstKey = firstKey ? firstKey : 'rowSpan'
          dataFirstRowSpan = arr.length > 0 ? arr[0][firstKey] : null
          console.log('dataFirstRowSpan', arr, firstKey, lastKey, dataFirstRowSpan)
        }
        // 排序第一次
        if (dataFirstRowSpan == 0) {
          if (!oldKey) {
            arr.sort((a, b) => a[key] - b[key])
          } else {
            arr.sort(function (a, b) {
              if (a[oldKey] != b[oldKey]) {
                return a[oldKey] - b[oldKey];
              }
              if (a[key] < b[key]) {
                return -1;
              } else if (a[key] > b[key]) {
                return 1;
              } else {
                return 0;
              }
            });
          }
        }
        // 假如还是0再反向排一次
        dataFirstRowSpan = arr.length > 0 ? arr[0][firstKey] : null
        if (dataFirstRowSpan == 0) {
          if (!oldKey) {
            arr.sort((a, b) => a[key] - b[key])
          } else {
            arr.sort(function (a, b) {
              if (a[oldKey] != b[oldKey]) {
                return a[oldKey] - b[oldKey];
              }
              if (a[key] < b[key]) {
                return 1;
              } else if (a[key] > b[key]) {
                return -1;
              } else {
                return 0;
              }
            });
          }
        }
      }
      if (arr[0][firstKey] == 0) {
        let firstRowsKeyArray = arr.map(item => {
          return item[firstKey]
        })
        let sameFirstRowsKeyArray = Array.from(new Set(firstRowsKeyArray))
        // 若rowSpan首位重复连续都为0,且只有0和另外一个数字
        if (sameFirstRowsKeyArray.length == 2 && sameFirstRowsKeyArray.indexOf(0) >= 0) {
          firstRowsKeyArray.sort((a, b) => b - a)
          arr.map((item, index) => {
            item[firstKey] = firstRowsKeyArray[index]
          })
          console.log('首位rowSpan重复连续都为0,且只有0和另外一个数字', firstRowsKeyArray, arr)
        } else {
          // 若rowSpan首位重复连续都为0,有0和多个数字,则默认第一个为连续有几个0的项
          console.log('首位rowSpan重复连续都为0,有0和多个数字,则默认第一个为连续有几个0的项')
          let count = 0
          for (let i = 0; i < arr.length; i++) {
            if (arr[0][firstKey] == 0 && arr[i][firstKey] !== 0) {
              count = i
              break;
            }
          }
          arr[0][firstKey] = count
        }

      }
      // console.log('ed', countMap, arr);
      return arr;
    },
    getPageName(path) {
      if (path.indexOf('/') > -1) {
        let list = path.split('/')
        let name = list && list.length > 0 ? list[list.length - 1] : null
        console.log(name)
        return name
      } else {
        return path
      }
    },
  },
}