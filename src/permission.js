import Vue from 'vue'
import router from './router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import notification from 'ant-design-vue/es/notification'
import { ACCESS_TOKEN, USER_NAME, USER_ID } from '@/store/mutation-types'
import { generateIndexRouter, getParams } from '@/utils/util'

NProgress.configure({
  showSpinner: false,
}) // NProgress Configuration

const whiteList = ['/user/login', '/user/register', '/user/register-result', '/whitePages/EmptyPage', '/enquiry/enquiry'] // no redirect whitelist

router.beforeEach((to, from, next) => {
  let enquiryUrl = Vue.API.EnquiryUrl || ''
  let isEnquiryUrl = window.location.href.indexOf(enquiryUrl) > -1 ? true : false
  // 如果是独立页面 直接重定向去这个页面
  if (isEnquiryUrl) {
    goWebPath('/enquiry/enquiry', to, next)
  } else {
    if (to.path === '/enquiry/enquiry') {
      next({
        path: '/404',
      })
    }
  }
  const menuIdStr = to && to.meta && to.meta.url ? to.meta.url : ''
  if (menuIdStr && menuIdStr.length > 5) {
    const beginStr = menuIdStr.substring(0, 6)
    if (beginStr == 'MENUID') {
      const menuId = menuIdStr.substring(6, menuIdStr.length)
      Vue.ls.set('MENU_ID', menuId)
      // console.log('beginStr = ' + beginStr + ' menuId = ' + menuId)
    }
  }

  NProgress.start() // start progress bar

  //解析url判断是否来自应用系统的用户  若是直接进入系统
  var appUrl = window.location.href
  if (appUrl.indexOf('appToken=') > 0 && appUrl.indexOf('appUserId=') > 0 && appUrl.indexOf('appUserName=') > 0) {
    Vue.ls.set(ACCESS_TOKEN, getParams().appToken)
    Vue.ls.set(USER_ID, getParams().appUserId)
    Vue.ls.set(USER_NAME, getParams().appUserName)
    // store.dispatch('LoginByToken').then(() => {
    //   if (to.path === '/user/login') {
    //     next()
    //     // NProgress.done()
    //   }
    // })
    if (to.path === '/user/login') {
      next()
    }
  }

  if (Vue.ls.get(ACCESS_TOKEN)) {
    /* has token */
    if (to.path === '/user/login') {
      next({
        path: '/Home',
      })
      NProgress.done()
    } else {
      if (store.state.user.menuList.length === 0) {
        store
          .dispatch('loadUserMenuData')
          .then((res) => {
            if (!res.success || !res.result) {
              notification.error({
                message: '系统提示',
                description: res.message,
              })
              setTimeout(() => {
                store.dispatch('Logout').then(() => {
                  location.reload()
                })
              }, 1000)
              return
            }
            const menuData = res.result.menu
            if (menuData === null || menuData === '' || menuData === undefined) {
              return
            }
            let constRoutes = []
            constRoutes = generateIndexRouter(menuData)
            // 添加主界面路由
            // store
            //   .dispatch('UpdateAppRouter', {
            //     constRoutes
            //   })
            //   .then(() => {
            // 根据roles权限生成可访问的路由表
            // 动态添加可访问路由表
            let roterList = constRoutes //store.getters.addRouters
            router.addRoutes(roterList)
            const redirect = decodeURIComponent(from.query.redirect || to.path)
            if (to.path === redirect) {
              // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
              next({
                ...to,
                replace: true,
              })
            } else {
              // 跳转到目的路由
              next({
                path: redirect,
              })
            }
            // })
          })
          .catch((e) => {
            console.log('请求用户信息失败，请重试！', e)
            if (e) {
              notification.error({
                message: '系统提示',
                description: e,
              })
              if (store.state.user.token) {
                setTimeout(() => {
                  store.dispatch('Logout').then(() => {
                    location.reload()
                  })
                }, 2000)
              }
            }
          })
      } else {
        next()
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      let lastTo = {
        path: '' + to.path,
        query: { ...to.query },
      }
      sessionStorage.setItem('LAST_PAGE_URL', JSON.stringify(lastTo))
      next({
        path: '/user/login',
      })
      NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
    }
  }
})

function goWebPath(path, to, next) {
  if (to.path == path) {
    next()
  } else {
    next({
      path: path,
    })
  }
}

router.afterEach(() => {
  NProgress.done() // finish progress bar
})

