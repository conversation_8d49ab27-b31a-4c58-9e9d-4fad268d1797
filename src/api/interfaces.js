/**
 * api接口配置
 */
let CXAdminUrl = '', //采销api地址前缀
  SRMAdminUrl = '', //SRM系统地址前缀
  DSAdminUrl = '', //电商api地址前缀
  TextReportUrl = '',//采销查看检验报告
  CodeLoginUrl = '',//获取图形验证码
  OSSUrl = '', //OSS地址前缀
  EnquiryUrl = '' //询价H5地址前缀

const env = process.env.VUE_APP_PREVIEW.trim()
console.log('当前环境变量 VUE_APP_PREVIEW:', process.env.VUE_APP_PREVIEW)
console.log('处理后的环境值 env:', env)
// const env = 'production'
switch (env) {
  case 'development':
  case 'test':
    CXAdminUrl = 'https://testscm.hysyzs.com'
    DSAdminUrl = 'http://testbaseapi.hysyzs.cn'
    SRMAdminUrl = 'https://testscm.hysyzs.com'
    TextReportUrl = 'https://test.haoyaoyouxuan.com'
    CodeLoginUrl = 'https://testscm.hysyzs.com'
    OSSUrl = 'https://testapifile.hysyyl.com'
    EnquiryUrl = 'https://testscm.hysyzs.com'
    break
  case 'production':
    CXAdminUrl = 'https://scm.haoyaoyouxuan.com'
    DSAdminUrl = 'https://admin.hysmall.com'
    SRMAdminUrl = 'https://srm.haoyaoyouxuan.com'
    TextReportUrl = 'https://sy.haoyaoyouxuan.com'
    CodeLoginUrl = 'https://scm.haoyaoyouxuan.com'
    OSSUrl = 'https://apifile.hysyyl.com'
    EnquiryUrl = 'https://scm.haoyaoyouxuan.com'
    break
  case 'stage':
    CXAdminUrl = 'http://stage.haoyaoyouxuan.com'
    DSAdminUrl = 'http://stage.haoyaoyouxuan.com'
    SRMAdminUrl = 'http://ymallstage.hysyyl.com'
    TextReportUrl = 'http://stage.haoyaoyouxuan.com'
    CodeLoginUrl = 'http://stage.haoyaoyouxuan.com'
    OSSUrl = 'http://apifile.hysyzs.cn'
    EnquiryUrl = 'http://stage.haoyaoyouxuan.com'
    break
}
const Version = '1.5.1.250612'
const commonApi = {
  Version: Version,
  P31100: `${CXAdminUrl}:46001/api/Base`, //电商-Base
  // P31121: `${DSAdminUrl}:8898/api/Message`, //消息
  P46053: `${SRMAdminUrl}:46053/api/PriceAnalyse`, //采销-三方比价
  SRM_SITE: `${SRMAdminUrl}:46050/#/dashboard/Analysis?inAccount=***********&inPassword=123456`, //SRM采购系统站点地址
  EnquiryUrl: 'https://testscm.hysyzs.com:46039/',//询价表的url

  P36100: `${CXAdminUrl}:46100`, //平台地址
  P36001: `${CXAdminUrl}:46001/api/Base`, //采销-基础
  P36002: `${CXAdminUrl}:46002/api/Customer`, //采销-客户
  P36003: `${CXAdminUrl}:46003/api/Supplier`, //采销-供应商
  P36004: `${CXAdminUrl}:46004/api/CA`, //采销-CA
  P36005: `${CXAdminUrl}:46005/api/Approval`, //采销-审核
  P36006: `${CXAdminUrl}:46006/api/Goods`, //采销-商品
  P36007: `${CXAdminUrl}:46007/api/Contract`, //采销-合约
  P36008: `${CXAdminUrl}:46008/api/Sales`, //采销-订单
  P36009: `${CXAdminUrl}:46009/api/Purchase`, //采销-采购
  P36010: `${CXAdminUrl}:46010/api/Team`, //采销-团队
  P36011: `${CXAdminUrl}:46011/api/Account`, //采销-账户
  P36012: `${CXAdminUrl}:46012/api/Financial`, //采销-认款
  P36015: `${CXAdminUrl}:46015/api/Reverse`, //采销-认款
  P46003: `${DSAdminUrl}:8898/api/Goods`, //采销-查看检验报告
  P46033: `${EnquiryUrl}:46033/api/Purchase`,//采销-询价表格
}

// 测试
const test = {
  LOGIN: `${DSAdminUrl}:8891`, //认证中心
  CODELOGIN: `${CodeLoginUrl}:46338`,//图形验证码
  // SMS: `${DSAdminUrl}:8891`, //短信中心
  SMS: `${DSAdminUrl}:8010`, //短信中心
  BURYINGPOINT: `${DSAdminUrl}:8891/`, // 埋点
  OSS: `${OSSUrl}:30881/api`,
  BASE: `${CXAdminUrl}:46001/api/Base`, //采销-基础
  WEB_URL: `${CXAdminUrl}:36300`, //平台地址

  ...commonApi,
}
const prod = {
  Version: Version,
  P36100: `${CXAdminUrl}:36100`, //平台地址
  LOGIN: `https://apipassport.hysyyl.com`,//图形验证码, //认证中心
  CODELOGIN: `https://apipassport.hysyyl.com`,//图形验证码
  SMS: 'https://apisms.hysyyl.com', //短信中心
  BURYINGPOINT: 'https://v2.haoyaoyouxuan.com:29151/', // 埋点
  OSS: `${OSSUrl}/api`, // Oss
  BASE: `${CXAdminUrl}:36001/api/Base`, //采销-基础

  P31100: `${CXAdminUrl}:36001/api/Base`, //电商-Base 实际使用：省市区
  P31102: `${DSAdminUrl}:21102/api/Customer`, // 电商 客户 实际使用：图片识别 禁售客户名单列表
  P31121: `${DSAdminUrl}:21121/api/Message`, //消息 实际使用：商品求购
  P46053: `${SRMAdminUrl}:25206/api/PriceAnalyse`, //采销-三方比价
  SRM_SITE: `${SRMAdminUrl}/#/dashboard/Analysis?inAccount=***********&inPassword=456789`, //SRM采购系统站点地址
  EnquiryUrl: 'http://inquiry.haoyaoyouxuan.com/',//询价表的url

  WEB_URL: `${CXAdminUrl}:36100`, //平台地址
  P36001: `${CXAdminUrl}:36001/api/Base`, //采销-基础
  P36002: `${CXAdminUrl}:36002/api/Customer`, //采销-客户
  P36003: `${CXAdminUrl}:36003/api/Supplier`, //采销-供应商
  P36004: `${CXAdminUrl}:36004/api/CA`, //采销-CA
  P36005: `${CXAdminUrl}:36005/api/Approval`, //采销-审核
  P36006: `${CXAdminUrl}:36006/api/Goods`, //采销-商品
  P36007: `${CXAdminUrl}:36007/api/Contract`, //采销-合约
  P36008: `${CXAdminUrl}:36008/api/Sales`, //采销-订单
  P36009: `${CXAdminUrl}:36009/api/Purchase`, //采销-采购
  P36010: `${CXAdminUrl}:36010/api/Team`, //采销-团队
  P36011: `${CXAdminUrl}:36011/api/Account`, //采销-账户
  P36012: `${CXAdminUrl}:36012/api/Financial`, //采销-认款
  P36015: `${CXAdminUrl}:36015/api/Reverse`, //采销-认款
  P46003: `${TextReportUrl}:46003/api/Sy`, //采销-查看检验报告
  P46033: `${EnquiryUrl}:36016/api/Purchase`,//采销-询价表格
}

// 预发布
const stage = {
  LOGIN: `${CXAdminUrl}:8890`, //认证中心
  CODELOGIN: `${CodeLoginUrl}:46338`,//图形验证码
  SMS: `${CXAdminUrl}:8890`, //短信中心
  BURYINGPOINT: `${CXAdminUrl}:8890/`, // 埋点
  OSS: `${OSSUrl}:8094/api`, // Oss
  BASE: `${CXAdminUrl}:46001/api/Base`, //采销-基础
  ...commonApi,
}
const api = Object.create(Object.prototype, {
  InterfaceHost: {
    get: function () {
      switch (env) {
        case 'development':
        case 'test':
          window._CONFIG['AliOssConfig'].BaseBucketName = 'hysyylosstest' //公用
          window._CONFIG['AliOssConfig'].AuthBucketName = 'hysyylosstest' //  客户
          window._CONFIG['AliOssConfig'].GoodsBucketName = 'hysyylosstest' // 商品

          // window._CONFIG['AliOssConfig'].CommonDir = 'base' //公用
          // window._CONFIG['AliOssConfig'].AuthDir = 'customer' //  客户
          // window._CONFIG['AliOssConfig'].GoodsDir = 'goods' // 商品
          return test
        case 'production':
          window._CONFIG['AliOssConfig'].BaseBucketName = 'hyszbbbase' //公用
          window._CONFIG['AliOssConfig'].AuthBucketName = 'hyszbbcustomers' //  客户
          window._CONFIG['AliOssConfig'].GoodsBucketName = 'hyszbbgoods' // 商品
          console.log('正式环境 - 设置桶名为:', window._CONFIG['AliOssConfig'].BaseBucketName)

          // window._CONFIG['AliOssConfig'].CommonDir = 'base' //公用
          // window._CONFIG['AliOssConfig'].AuthDir = 'customer' //  客户
          // window._CONFIG['AliOssConfig'].GoodsDir = 'goods' // 商品
          return prod
        case 'stage':
          window._CONFIG['AliOssConfig'].BaseBucketName = 'hysyylosstest' //公用
          window._CONFIG['AliOssConfig'].AuthBucketName = 'hysyylosstest' //  客户
          window._CONFIG['AliOssConfig'].GoodsBucketName = 'hysyylosstest' // 商品

          // window._CONFIG['AliOssConfig'].CommonDir = 'base' //公用
          // window._CONFIG['AliOssConfig'].AuthDir = 'customer' //  客户
          // window._CONFIG['AliOssConfig'].GoodsDir = 'goods' // 商品
          return stage
      }
    },
  },
})

const install = (Vue) => {
  Vue.prototype.$API = Vue.prototype.API = api.InterfaceHost
  Vue.$API = Vue.API = api.InterfaceHost
}
export default {
  install,
}
