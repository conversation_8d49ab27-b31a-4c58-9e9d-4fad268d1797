import { axios } from '@/utils/request'

import Vue from 'vue'

export function apiHead(type) {
  return (type && Vue.API[type]) || Vue.API.P36001
}

//post
export function postAction(url, parameter, type) {
  Vue.prototype.lastUpdate.time = new Date()
  return axios({
    url: apiHead(type) + url,
    method: 'post',
    data: parameter,
  })
}

//post method= {post | put}
export function httpAction(url, parameter, method, type) {
  Vue.prototype.lastUpdate.time = new Date()
  return axios({
    url: apiHead(type) + url,
    method: method,
    data: parameter,
  })
}

//put
export function putAction(url, parameter, type) {
  Vue.prototype.lastUpdate.time = new Date()
  return axios({
    url: apiHead(type) + url,
    method: 'put',
    data: parameter,
  })
}
//patch
export function patchAction(url, parameter, type) {
  Vue.prototype.lastUpdate.time = new Date()
  return axios({
    url: apiHead(type) + url,
    method: 'patch',
    data: parameter,
  })
}

//get
export function getAction(url, parameter, type) {
  Vue.prototype.lastUpdate.time = new Date()
  return axios({
    url: apiHead(type) + url,
    method: 'get',
    params: parameter,
  })
}

//deleteAction
export function deleteAction(url, parameter, type) {
  Vue.prototype.lastUpdate.time = new Date()
  return axios({
    url: apiHead(type) + url,
    method: 'delete',
    data: parameter,
  })
}

/**
 * 下载文件 用于excel导出
 * @param url
 * @param parameter
 * @returns {*}
 */
export function downFile(url, parameter, method = 'get', type) {
  Vue.prototype.lastUpdate.time = new Date()
  const para = {
    url: apiHead(type) + url,
    method: method,
    responseType: 'blob',
  }
  if (['post', 'POST', 'Post'].includes(method)) {
    para['data'] = parameter
  } else {
    para['params'] = parameter
  }
  return axios(para)
}
