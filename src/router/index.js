import Vue from 'vue'
import Router from 'vue-router'
import { constantRouterMap } from '@/config/router.config'

Vue.use(Router)

/**
 * 重写路由的push方法
 */
const routerPush = Router.prototype.push
Router.prototype.push = function push(location) {
  try {
    if (typeof location.meta == 'undefined') {
      Vue.prototype.$lastLocation = location
    }
    return routerPush.call(this, location).catch(error => error)
  } catch (ex) {}
}

export default new Router({
  // mode: 'history',
  base: process.env.BASE_URL,
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRouterMap
})
