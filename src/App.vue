<template>
  <a-config-provider :locale="locale">
    <div id="app" ref="app">
      <router-view />
    </div>
  </a-config-provider>
</template>
<script>
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
import enquireScreen from '@/utils/device'

import Vue from 'vue'
import { apiHead } from '@/api/manage'
import { mixinDevice } from '@/utils/mixin.js'
// import Track from '@/track.js'
import { ACCESS_TOKEN, USER_ID, USER_NAME } from '@/store/mutation-types'
import _ from 'lodash'

export default {
  data() {
    return {
      locale: zhCN,
      showHeight: document.documentElement.clientHeight,   //实时屏幕高度
    }
  },
  watch: {
    showHeight(val) {
      console.log(val)
    }
  },
  mixins: [mixinDevice],
  created() {
    // 根据屏幕高度计算表格高度
    this.$root.windowHeight = window.innerHeight
    this.$root.tableHeight = 400
    let num = 0
    if (window.innerHeight > 900) {
      num = 0.53
    } else {
      num = 0.46
    }
    this.$root.tableHeight = Number((window.innerHeight * num).toFixed(2))
    console.log(window.innerHeight + '高度', this.$root.tableHeight)

    let that = this
    enquireScreen((deviceType) => {
      // tablet
      if (deviceType === 0) {
        that.$store.commit('TOGGLE_DEVICE', 'mobile')
        that.$store.dispatch('setSidebar', false)
      }
      // mobile
      else if (deviceType === 1) {
        that.$store.commit('TOGGLE_DEVICE', 'mobile')
        that.$store.dispatch('setSidebar', false)
      } else {
        that.$store.commit('TOGGLE_DEVICE', 'desktop')
        that.$store.dispatch('setSidebar', true)
      }
    })
    
  },
  mounted() {

  },
}
</script>
<style>
html > body {
  overflow: auto;
}
#app {
  height: 100%;
  overflow: hidden;
}
/* 弹框高度 */
.ant-modal-body {
  max-height: 75vh;
  overflow-y: auto;
  /* 兼容IE */
  -ms-overflow-style: none;
  -ms-scroll-chaining: chained;
  -ms-content-zooming: zoom;
  -ms-scroll-rails: none;
  -ms-content-zoom-limit-min: 100%;
  -ms-content-zoom-limit-max: 500%;
  -ms-scroll-snap-type: proximity;
  -ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);
}
/* 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
.ant-modal-body::-webkit-scrollbar {
  width: 4px;
  height: 6px;
  background-color: #f5f5f5;
}
/* 定义滚动条轨道 */
.ant-modal-body::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}

/* 定义滑块 */
.ant-modal-body::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /* background-color: rgba(64, 169, 255, 0.5); */
  background-color: rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
}
.ant-card-extra {
  padding: 0 !important;
}

/* 滚动条样式 */
.scroll-style {
  /* 兼容IE */
  -ms-overflow-style: none;
  -ms-scroll-chaining: chained;
  -ms-content-zooming: zoom;
  -ms-scroll-rails: none;
  -ms-content-zoom-limit-min: 100%;
  -ms-content-zoom-limit-max: 500%;
  -ms-scroll-snap-type: proximity;
  -ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);
}
/* 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
.scroll-style::-webkit-scrollbar {
  width: 4px;
  height: 6px;
  background-color: #f5f5f5;
}
/* 定义滚动条轨道 */
.scroll-style::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}

/* 定义滑块 */
.scroll-style::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /* background-color: rgba(64, 169, 255, 0.5); */
  background-color: rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
}
/* 表格边框 */
.ant-table-middle {
  border: 1px solid #e8e8e8;
  border-bottom: none;
  border-radius: 4px;
}
/* 表格行变红色 */
.table-color-red-dust {
  color: #f5222d;
}
/* .ant-divider-horizontal.ant-divider-with-text-left::before,.ant-divider-horizontal.ant-divider-with-text-left::after{
  top: 100% !important;
  transform: translateY(-50%) !important;
} */
</style>
