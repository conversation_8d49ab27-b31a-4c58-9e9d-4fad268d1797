<template>
  <div style="margin: 10px;">
    <div style="overflow-y: auto;height:88vh;">
      <div style="margin:10px 0;">{{supplierName?(supplierName+'报价'):'--'}}</div>
      <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
      <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
      <!-- 列表 -->
      <TableNewView ref="tableView" :table="table" :columns="columns" :tabDataList="tabDataList" :dataList="dataSource" @operate="operate">

      </TableNewView>
    </div>
    <a-affix :offset-bottom="0" style="float: right; width: 100%; text-align: right">
      <a-card :bordered="false" style="border-top: 1px solid #e8e8e8;">
        <a-button type="primary" :loading="loading" @click="handleOk(1)">提交报价</a-button>
      </a-card>
    </a-affix>
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { PublicMixin } from '@/mixins/PublicMixin'//非必要 公用的 有需要时候用其他mixin也行
import { getAction, postAction, putAction } from '@/api/manage'
export default {
  title: '四川欣佳能达医药有限公司询价表',
  name: 'enquiry',
  mixins: [ListMixin, PublicMixin],
  components: {},
  data() {
    return {
      doctorRole: null,
      searchItems: [
        {
          name: '商品名称', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'GoodsKeyword', //搜索key 必填
          placeholder: '请输入商品名称/编号',
        },
        {
          name: '生产厂家', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'BrandManufacturerKeyword', //搜索key 必填
          placeholder: '请输入厂家名称',
        },
      ],
      queryParam: {

      },
      table: {
        scrollY: this.scrollY(),
        operateBtns: [

        ], //右上角按钮集合
        rowKey: 'Id',
      },
      tabDataList: [

      ],
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批准文号',
          dataIndex: 'ApprovalNumber',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '求购数量',
          dataIndex: 'PurchaseQuantity',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单价',
          dataIndex: 'num1',
          width: 100,
          ellipsis: true,
          // fixed: 'right',
          precision: 2,
          min: 0,
          max: 999999,
          scopedSlots: { customRender: 'inputNumber' },
          onBlur: this.onBlur,
        },
        {
          title: '供货数量',
          dataIndex: 'num2',
          width: 100,
          ellipsis: true,
          min: 0,
          max: 99999999,
          // fixed: 'right',
          scopedSlots: { customRender: 'inputCount' },
          onBlur: this.onBlur,
        },
        {
          title: '效期优于',
          dataIndex: 'num3',
          width: 100,
          ellipsis: true,
          // fixed: 'right',
          maxLength: 20,
          scopedSlots: { customRender: 'input' },
          onBlur: this.onBlur,
        },
      ],
      writeData: [],
      supplierName: '',
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      isSubmitted: null,//是否提交过
      httpHead: 'P46033',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/PurchaseQuotation/GetToQuotationDataPageListAsync',
        save: '/{v}/PurchaseQuotation/SubmitQuotationAsync',
      },
    }
  },
  computed: {},
  created() {
    const params = new URLSearchParams(window.location.search);
    const code = params.get('code'); // 返回 'value1'
    this.queryParam.LinkUrlCode = code || this.$route.query.code || ''

    // 设置标题
    this.setWebTitle()
    this.init()
  },
  methods: {
    setWebTitle() {
      document.getElementsByTagName('title')[0].innerText = '四川欣佳能达医药有限公司询价表'
    },
    scrollY() {
      let num = 0
      if (window.innerHeight > 900) {
        num = 0.65
      } else if (window.innerHeight > 750) {
        num = 0.58
      } else if (window.innerHeight > 650) {
        num = 0.45
      } else if (window.innerHeight > 600) {
        num = 0.4
      } else {
        num = 0.52
      }
      // console.log('window.innerHeight', window.innerHeight, num)
      return Number((window.innerHeight * num).toFixed(2))
    },
    init() {
      let writeData = JSON.parse(localStorage.getItem(this.queryParam.LinkUrlCode + 'writeData')) || [];
      if (writeData) {
        this.writeData = writeData || []
      }
      console.log('localWriteDataObj', this.writeData)
    },
    loadBefore() {

    },
    loadAfter(data) {
      if (!data) {
        return
      }
      this.isSubmitted = data.IsSubmitted
      this.supplierName = data.SupplierName
      if (this.isSubmitted == true) {
        this.dataSource = []
        this.ipagination.total = 0
        this.$message.warning('该条询价单已完成报价，如需调整请联系采购人员')
        return
      }
      this.dataSource = data.ToQuotationDetailList
      this.dataSource.map(item => {
        let haveWrite = this.writeData.find(r => {
          return r.Id == item.Id
        })
        if (haveWrite) {
          this.$set(item, 'num1', haveWrite.num1)
          this.$set(item, 'num2', haveWrite.num2)
          this.$set(item, 'num3', haveWrite.num3)
        }
      })
    },
    onBlur(key, record, index) {
      if (record.num1 == 0) {
        record.num1 = null
      }
      this.setWriteData(record)
    },
    setWriteData() {
      this.dataSource.map(item => {
        let haveKeyRowIndex = this.writeData.findIndex(r => {
          return r.Id == item.Id
        })
        if (haveKeyRowIndex > -1) {
          this.$set(this.writeData, haveKeyRowIndex, item)
        } else {
          if (item.num1 || item.num2 || item.num3) {
            this.writeData.push(item)
          }
        }
      })

      this.writeData = this.writeData.filter(item => {
        return item.num1 || item.num2 || item.num3
      })

      let jsonData = JSON.stringify(this.writeData)

      localStorage.setItem(this.queryParam.LinkUrlCode + 'writeData', jsonData);
      // console.log(this.writeData)
    },
    handleOk(type) {
      if (type == 1) {
        console.log('保存的数据', this.writeData)
        if (this.isSubmitted == true) {
          this.dataSource = []
          this.ipagination.total = 0
          this.$message.warning('该条询价单已完成报价，如需调整请联系采购人员')
          return
        }
        if (this.writeData.length == 0) {
          this.$message.warning('请填写报价信息')
          return
        }
        let that = this
        this.$confirm({
          title: '提示',
          content: '提交报价后将不能修改，是否继续提交？',
          okText: '继续',
          cancelText: '取消',
          onOk() {
            that.saveData()
          },
          onCancel() { },
        });
      }
    },
    saveData() {
      let QuotationInfoList = this.writeData.map(item => {
        return {
          Id: item.Id,
          QuotationPrice: item.num1,
          SupplyQuantity: item.num2,
          PreferredExpiryDate: item.num3,
        }
      })
      let formData = {
        LinkUrlCode: this.queryParam.LinkUrlCode || '',
        QuotationInfoList: QuotationInfoList || [],
      }
      this.loading = true
      postAction(this.url.save, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess == true) {
            this.$refs.searchView.queryParam = {}
            this.dataSource = []
            this.ipagination.total = 0
            this.isSubmitted = true
            this.$message.success('报价信息提交成功')
            localStorage.removeItem(this.queryParam.LinkUrlCode + 'writeData')
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 操作
    operate(record, key, index) {
      console.log(record, key, index)
    },
  },
}
</script>
<style lang="less" scoped>
/deep/.ant-card-body {
  padding: 16px 0;
}
// /deep/.ant-table-body {
//   background: unset;
// }
</style>
