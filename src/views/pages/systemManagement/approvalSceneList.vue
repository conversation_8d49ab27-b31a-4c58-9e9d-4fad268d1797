<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab" />
    <!-- 新增 -->
    <ApprovalSceneAddModal ref="ApprovalSceneAddModal" @ok="modalOk"></ApprovalSceneAddModal>
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "approvalSceneList",
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '对应单据', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增', type: 'primary', icon: 'plus', key: 'add', id: '2b60e79c-248b-43b2-93bd-23dad583c5b5', }
        ],
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
      columns: [
        {
          title: '审批场景',
          dataIndex: 'ScenesStr',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审批方式',
          dataIndex: 'ApprovalMethodStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应单据',
          dataIndex: 'CnName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: "right",
          actionBtn: [{ name: '编辑', icon: '', id: '34c22f0a-2215-425c-859b-e6e034f0121d' },],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {

      },
      isTableInitData: true,//是否自动加载
      linkHttpHead: 'P36005',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/ApprovalWorkFlow/GetApprovalFormList',
        sub: '/PurchaseOrder/SubmitAsync',
        del: '/PurchaseOrder/DeleteAsync',
        isValidUrl: '/YYDepartment/GetDepartmentList',//若有是否有效需要写这个url
      },
    }
  },
  created() {

  },
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        this.$refs.ApprovalSceneAddModal.add()
      } else if (type == '编辑') {
        this.$refs.ApprovalSceneAddModal.edit(record)
      }
    },

  }

}
</script>

<style>
</style>