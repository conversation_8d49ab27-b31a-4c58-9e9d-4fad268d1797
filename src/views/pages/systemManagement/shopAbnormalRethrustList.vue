<!--商品禁售异常重推-->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab" />
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "shopAbnormalRethrustList",
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '商品名称', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编码' },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [

        ],
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '禁售类型',
          dataIndex: 'SellsBannedTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否推送成功',
          dataIndex: 'RedoPushSuccess',
          width: 150,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (t) => {
            return t == true ? '是' : (t == false ? '否' : '--')
          },
        },
        {
          title: '重推时间',
          dataIndex: 'RedoPushTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '错误信息',
          dataIndex: 'ErrorMsg',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: "right",
          actionBtn: [{
            name: '重推',
            icon: '',
            specialShowFuc: (e) => {
              return e.RedoPushSuccess != true
            },
          },],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {

      },
      isTableInitData: true,//是否自动加载
      linkHttpHead: 'P36006',
      linkUrlType: 'POST',//请求方式
      linkUrl: {
        list: '/{v}/GoodsSaleManage/QueryGoodsSellsBannedSettingPushFailedRecordList',
        redoPushFailedRecord: '/{v}/GoodsSaleManage/RedoPushFailedRecord',
      },
    }
  },
  created() {

  },
  methods: {

    // 列表操作
    operate(record, type) {
      let that = this
      if (type == '重推') {
        this.$confirm({
          title: '是否进行重推?',
          content: '',
          onOk() {
            that.subRethrust(record)
          },
          onCancel() { },
        });
      }
    },
    subRethrust(record) {
      let url = this.linkUrl.redoPushFailedRecord + '?recordId=' + record.RecordId
      this.$refs.table.loading = true
      putAction(url, {}, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.error(res.Msg)
        } else {
          this.$message.success('操作成功')
        }
        this.$refs.table.loadDatas(1, this.queryParam)
      }).finally((res) => {
        this.$refs.table.loading = false
      })
    },

  }

}
</script>

<style>
</style>