<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab" />
    <!-- 新增 -->
    <GlobalParameterAddModal ref="GlobalParameterAddModal" @ok="modalOk"></GlobalParameterAddModal>
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "globalParameterList",
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '参数', type: 'input', value: '', key: 'Key', defaultVal: '', placeholder: '名称/编码' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增', type: 'primary', icon: 'plus', key: 'add', id: 'f1d27455-6b51-42c4-b129-677ebc262e18', }
        ],
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
      columns: [
        {
          title: '参数名称',
          dataIndex: 'Name',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '参数编码',
          dataIndex: 'Code',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '参数值',
          dataIndex: 'Value',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '参数备注',
          dataIndex: 'Remarks',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: "right",
          actionBtn: [{ name: '编辑', icon: '',id:'cc040c89-a1a0-46f4-8a38-87f5a9f65344' },],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {

      },
      isTableInitData: true,//是否自动加载
      linkHttpHead: 'P36001',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/Global/GetListGlobalParameter',
        sub: '/PurchaseOrder/SubmitAsync',
        del: '/PurchaseOrder/DeleteAsync',
        isValidUrl: '/YYDepartment/GetDepartmentList',//若有是否有效需要写这个url
      },
    }
  },
  created() {

  },
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        this.$refs.GlobalParameterAddModal.add()
      } else if (type == '编辑') {
        this.$refs.GlobalParameterAddModal.edit(record)
      }
    },

  }

}
</script>

<style>
</style>