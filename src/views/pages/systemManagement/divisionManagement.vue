<template>
  <a-row>
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tableInfo="tableInfo"
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="onSetValid"
      @operate="operate"
      @changeTab="changeTab"
    >
      <!-- 部门负责人 -->
      <span slot="PrincipalName" slot-scope="{ text, record }">
        <j-ellipsis :value="`${text || ''}/${record['PrincipalPhone'] || ''}`" :length="30" />
      </span>
    </SimpleTable>
    <!-- 新增部门 -->
    <DivisionManagementAddModal ref="DivisionManagementAddModal" @ok="modalOk"></DivisionManagementAddModal>
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'

export default {
  name: 'divisionManagement',
  components: { JEllipsis },
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '参数', type: 'input', value: '', key: 'PurchaseNo1', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        rowKey: 'Id',
        hintArray: [],
        sortArray: ['PrincipalName'],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      tableInfo: {
        scrollY: '550px',
        scrollX: null,
        actionIndex: null, //操作项的位置
        size: 'middle',
      },
      columns: [
        {
          title: '部门名称',
          dataIndex: 'Name',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '部门负责人',
          dataIndex: 'PrincipalName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'PrincipalName' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 220,
          fixed: 'right',
          actionBtn: [
            {
              name: '新增下级',
              icon: '',
              id: '76d10443-7ea3-4358-bfef-c6526ec179e6',
              specialShowFuc: (e) => {
                let record = e || {}
                if (record.Name !== '隔离中转部') {
                  return true
                } else {
                  return false
                }
              },
            },
            {
              name: '编辑',
              icon: '',
              id: '06e6a1d3-3295-4596-812f-b4bc79afa67c',
              specialShowFuc: (e) => {
                let record = e || {}
                if (record.ParentId && record.Name !== '隔离中转部') {
                  return true
                } else {
                  return false
                }
              },
            },
            {
              name: '删除',
              icon: '',
              id: '66687be1-a2b8-4d0a-8df9-a02e437b6e72',
              specialShowFuc: (e) => {
                let record = e || {}
                if (record.ParentId && record.Name !== '隔离中转部') {
                  return true
                } else {
                  return false
                }
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36001',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/UserPermission/Department/List',
        del: '/{v}/UserPermission/Department/Delete',
      },
    }
  },
  created() {},
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam, (data) => {
  //     // 获取表格数据
  //     if (data && data.length > 0) {
  //       // 默认展开第一个
  //       this.$nextTick(() => {
  //         this.$refs.table.expandedRowKeys = [data[0].Id]
  //         this.$refs.table.dataSource = this.setTableData(data)
  //       })
  //     }
  //   })
  // },
  activated() {
    this.$refs.table.loadDatas(1, this.queryParam, (data) => {
      // 获取表格数据
      if (data && data.length > 0) {
        // 默认展开第一个
        this.$nextTick(() => {
          this.$refs.table.expandedRowKeys = [data[0].Id]
          this.$refs.table.dataSource = this.setTableData(data)
        })
      }
    })
  },
  methods: {
    // 递归重组数据
    setTableData(data) {
      if (data == null || data.length <= 0) {
        return []
      }
      let tempArr = []
      try {
        data.forEach((item) => {
          var temp = item
          if (item.ChildrenDepartments && item.ChildrenDepartments.length > 0) {
            temp['children'] = this.setTableData(item.ChildrenDepartments)
          }
          tempArr.push(temp)
        })
      } catch (e) {
        console.log(e)
      }
      return tempArr
    },
    modalOk() {
      this.$refs.table.loadDatas(1, this.queryParam, (data) => {
        if (data && data.length > 0) {
          this.$refs.table.dataSource = this.setTableData(data)
        }
      })
    },
    // 确定删除
    subDelInfo(record) {
      let params = {}
      let url = this.linkUrl.del + '?id=' + record.Id
      getAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('删除成功')
            this.$refs.table.loadDatas(1, this.queryParam, (data) => {
              if (data && data.length > 0) {
                this.$refs.table.dataSource = this.setTableData(data)
              }
            })
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => {})
    },
    // 列表操作
    operate(record, type) {
      if (type == '新增下级') {
        this.$refs.DivisionManagementAddModal.add(record)
      } else if (type == '编辑') {
        this.$refs.DivisionManagementAddModal.edit(record)
      } else if (type == '删除') {
        let that = this
        this.$confirm({
          title: '确定删除该部门及其下级部门？',
          content: '',
          onOk() {
            that.subDelInfo(record)
          },
          onCancel() {},
        })
      }
    },
  },
}
</script>

<style></style>
