<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab" />
    <!-- 新增 -->
    <AbnormalDocumentAddModal ref="AbnormalDocumentAddModal" @ok="modalOk"></AbnormalDocumentAddModal>
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "abnormalDocumentList",
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '单据编号', type: 'input', value: '', key: 'Code', defaultVal: '', placeholder: '请输入' },
        {
          name: '单据类型',
          type: 'select',
          value: '',
          key: 'BusinessOrderType',
          defaultVal: [
            { title: '采购订单', value: 1 },
            { title: '采购发票', value: 15 },
            { title: '采购票折', value: 9 },
            { title: '采购调价', value: 6 },
            { title: '采购退货', value: 4 },
            { title: '商销调价', value: 23 },
            { title: '商销售后', value: 22 },
            { title: '销货对抵', value: 10 },
            { title: '打款', value: 60 },
          ],
          placeholder: '请选择',
        },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        { name: '创建时间', type: 'timeInput', value: '', key: ['CreateTimeBegin', 'CreateTimeEnd'], defaultVal: ['', ''], placeholder: ['开始日期', '结束日期'] },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增异常单据处理', type: 'primary', icon: 'plus', key: 'add', id: '0a20f10d-21d7-4fd6-965b-65fc32f88358', }
        ],
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
      columns: [
        {
          title: '单据编号',
          dataIndex: 'BusinessOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessOrderTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '处理结果',
          dataIndex: 'AbnormalBusinessStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '异常描述',
          dataIndex: 'ErrMsg',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      queryParam: {

      },
      isTableInitData: true,//是否自动加载
      linkHttpHead: 'P36015',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/AbnormalBusiness/GetList',
        sub: '/PurchaseOrder/SubmitAsync',
        del: '/PurchaseOrder/DeleteAsync',
        isValidUrl: '/YYDepartment/GetDepartmentList',//若有是否有效需要写这个url
      },
    }
  },
  created() {

  },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        this.$refs.AbnormalDocumentAddModal.add()
      }
    },

  }

}
</script>

<style>
</style>