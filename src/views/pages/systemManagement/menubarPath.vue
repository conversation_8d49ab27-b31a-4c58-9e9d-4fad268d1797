<template>
  <a-row>
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tableInfo="tableInfo"
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="onSetValid"
      @operate="operate"
      @changeTab="changeTab"
    />
    <!-- 新增下级 -->
    <MenubarPathAddModal ref="MenubarPathAddModal" @ok="modalOk"></MenubarPathAddModal>
    <!-- 设置序号 -->
    <MenubarPathSortModal ref="MenubarPathSortModal" @ok="modalOk"></MenubarPathSortModal>
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'

export default {
  name: 'menubarPath',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '参数', type: 'input', value: '', key: 'PurchaseNo1', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        rowKey: 'Id',
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      tableInfo: {
        scrollY: '550px',
        scrollX: null,
        actionIndex: null, //操作项的位置
        size: 'middle',
      },
      columns: [
        {
          title: '菜单名称',
          dataIndex: 'Name',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '菜单类型',
          dataIndex: 'TypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '菜单路径',
          dataIndex: 'Path',
          width: 300,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否校验数据权限',
          dataIndex: 'IsVerifyData',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'IsWhether' },
        },
        {
          title: 'Id',
          dataIndex: 'Id',
          width: 300,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '序号',
          dataIndex: 'Sort',
          width: 50,
          ellipsis: true,
          scopedSlots: { customRender: 'setSort' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 170,
          fixed: 'right',
          actionBtn: [
            {
              name: '新增下级',
              icon: '',
              id: '17a3497a-cfc6-4536-a159-a0f33b07688a',
              specialShowFuc: (e) => {
                let record = e || {}
                if (record.Name !== '首页') {
                  return true
                } else {
                  return false
                }
              },
            },
            // 一般条件隐藏显示,列表中字段Sort等于2的时候显示,或者等于数组为Sort多个条件[1,2]时候显示
            {
              name: '编辑',
              icon: '',
              id: '903aa660-426d-4a71-bb8b-af5883980a10',
              specialShowFuc: (e) => {
                let record = e || {}
                if (record.Name !== '首页') {
                  return true
                } else {
                  return true
                }
              },
            },
            {
              // 特殊条件隐藏显示,根据自己条件隐藏显示
              name: '删除',
              icon: '',
              id: '7c133acf-df24-45f1-a7df-82553422a12d',
              specialShowFuc: (e) => {
                let record = e || {}
                if (record.ParentId && record.Name !== '首页') {
                  return true
                } else {
                  return false
                }
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36001',
      linkUrlType: 'Get', //请求方式
      linkUrl: {
        list: '/{v}/UserPermission/Menu/List',
        del: '/{v}/UserPermission/Menu/Delete',
      },
    }
  },
  created() {},
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam, (data) => {
  //     // 获取表格数据
  //     if (data && data.length > 0 && this.$refs.table) {
  //       // 默认展开第一个
  //       this.$refs.table.expandedRowKeys = [data[0].Id]
  //       this.$refs.table.dataSource = data
  //     }
  //   })
  // },
  activated() {
    this.$refs.table.loadDatas(1, this.queryParam, (data) => {
      // 获取表格数据
      if (data && data.length > 0 && this.$refs.table) {
        // 默认展开第一个
        this.$refs.table.expandedRowKeys = [data[0].Id]
        this.$refs.table.dataSource = data
      }
    })
  },
  methods: {
    modalOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
      this.$store.dispatch('loadUserMenuData').then((res) => {
        this.$root.$emit('getMenusListData', 'get')
      })
    },
    // 确定删除
    subDelInfo(record) {
      let params = {}
      let url = this.linkUrl.del + '?id=' + record.Id
      postAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('删除成功')
            this.modalOk()
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => {})
    },
    // 列表操作
    operate(record, type) {
      if (type == '新增下级') {
        this.$refs.MenubarPathAddModal.add(record)
      } else if (type == '编辑') {
        this.$refs.MenubarPathAddModal.edit(record)
      } else if (type == 'setSort') {
        this.$refs.MenubarPathSortModal.show(record)
      } else if (type == '删除') {
        let that = this
        this.$confirm({
          title: '确定删除该菜单及其下级菜单，并同步移除相关角色对应权限？',
          content: '',
          onOk() {
            that.subDelInfo(record)
          },
          onCancel() {},
        })
      }
    },
  },
}
</script>

<style></style>
