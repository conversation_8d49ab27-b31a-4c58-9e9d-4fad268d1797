<!-- 管理授权品种 -->
<template>
  <a-modal :footer="null" :title="title" :width="1080" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        // padding: '10px 16px',
        background: '#fff',
      }">
      <!-- 搜索 -->
      <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
      <SimpleTable ref="table" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="selectTableData.columns" :linkHttpHead="httpHead" :linkUrlType="linkUrlType" :isTableInitData="isTableInitData" :linkUrl="{ list: url.list }" @operate="operate">
        <span slot="commonContent" slot-scope="{ text, record }" @click="onAbnormalClick(record)">
          <a v-if="record.ErrorShow">
            <a-icon type="question-circle" />
          </a>
          <j-ellipsis :value="text" :length="record.ErrorShow ? 8 : 15" :style="{ color: record.ErrorShow ? 'red' : '' }" />
        </span>
      </SimpleTable>
    </div>
    <!-- 表格选择数据弹窗 -->
    <TableSelectDataModal ref="TableSelectDataModal" :selectTableData="selectTableData" @chooseData="chooseData"></TableSelectDataModal>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'
import moment from 'moment'

export default {
  name: 'SupplierManageGoodsListModel',
  mixins: [ListMixin],
  components: {
    JEllipsis,
  },
  data() {
    return {
      title: '管理授权品种',
      visible: false,
      model: {},
      searchItems: [
        {
          name: '商品',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'GoodsKey',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '批量移除', type: '', icon: '', key: 'allDel', },
          { name: '新增', type: 'primary', icon: '', key: 'add', },
        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        rowSelection: {
          type: 'checkbox',
        },
        rowKey: 'ErpGoodsId',
      },
      confirmLoading: false,
      httpHead: 'P36001',
      isTableInitData: false,
      linkUrlType: 'GET',
      IsAdd: false,
      url: {
        list: '',
      },
      selectTableData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '商品', type: 'input', value: '', key: 'GoodsKey', defaultVal: '', placeholder: '名称/编号/拼音码' },
        ],
        title: '新增授权品种',
        name: '授权品种',
        recordKey: 'ErpGoodsId',
        httpHead: 'P36001',
        isInitData: false,
        url: {
          list: '/{v}/SupplierSystem/QueryGoodses',
        },
        columns: [
          {
            title: '商品名称',
            dataIndex: 'ErpGoodsName',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '规格',
            dataIndex: 'PackingSpecification',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '单位',
            dataIndex: 'PackageUnit',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '生产厂商',
            dataIndex: 'BrandManufacturer',
            width: 250,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '商品编号',
            dataIndex: 'ErpGoodsCode',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            actionBtn: [{ name: '移除', icon: '' }],
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
      queryParam: {
        SupplierId: '',
        UserId: '',
        GoodsKey: '',
        IsAdd: false,
      },
    }
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record) {
      this.url.list = '/{v}/SupplierSystem/QueryUserRelationSupplierGoodses'
      this.queryParam.SupplierId = record.SupplierId
      this.queryParam.UserId = record.UserId
      this.visible = true
      this.$nextTick(() => {
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    modalOk(data, updateIndex, type) {
      if (updateIndex == null) {
        this.dataSource.push(data)
      } else if (updateIndex !== null && type == 'sort') {
        let array = this.dataSource
        let index = updateIndex
        if (array[index + 1]) {
          array.splice(index + 1, 1, data, array[index + 1])
        } else {
          this.dataSource.push(data)
        }
      } else {
        this.dataSource.splice(updateIndex, 1, data)
      }
    },
    // 确定删除
    subDelInfo(record, index) {
      this.dataSource.splice(index, 1)
    },
    operate(record, type) {
      if (type == 'allDel') {
        if (this.$refs.table && (this.$refs.table.selectedRowKeys || []).length < 1) {
          this.$message.warning('请选择数据!')
          return
        }

        let that = this
        that.$confirm({
          title: '您确定批量移除商品？',
          content: '',
          onOk() {
            postAction(
              '/{v}/SupplierSystem/DeleteGoodses',
              {
                SupplierId: that.queryParam.SupplierId,
                UserId: that.queryParam.UserId,
                ErpGoodsIds: that.$refs.table.selectedRowKeys,
              },
              'P36001'
            )
              .then((res) => {
                if (res.Data) {
                  that.$message.success('移除成功')
                  that.dataSource = []
                  that.$refs.table.loadDatas(1, that.queryParam)
                } else {
                  that.$message.error(res.Msg)
                }
              })
              .catch((err) => {
                that.$message.error(err.Msg || err)
              })
              .finally(() => {
                that.confirmLoading = false
              })
          },
          onCancel() { },
        })
      } else if (type == 'add') {
        let dataSource = this.dataSource || []
        this.$refs.TableSelectDataModal.show(dataSource)
      } else if (type == '移除') {
        let that = this
        this.$confirm({
          title: '您确定移除当前商品？',
          content: '',
          onOk() {
            postAction(
              '/{v}/SupplierSystem/DeleteGoodses',
              {
                SupplierId: that.queryParam.SupplierId,
                UserId: that.queryParam.UserId,
                ErpGoodsIds: [record.ErpGoodsId],
              },
              'P36001'
            )
              .then((res) => {
                if (res.Data) {
                  that.dataSource = that.dataSource.filter((e) => e.ErpGoodsId != record.ErpGoodsId)
                  that.$message.success('移除成功')
                  that.$refs.table.loadDatas(1, that.queryParam)
                } else {
                  that.$message.error(res.Msg)
                }
              })
              .catch((err) => {
                that.$message.error(err.Msg || err)
              })
          },
          onCancel() { },
        })
      }
    },
    searchQuery(queryParam) {
      if (!this.$refs.table) {
        return
      }
      let queryParamObj = {
        SupplierId: this.queryParam.SupplierId,
        UserId: this.queryParam.UserId,
        ...queryParam,
      }
      this.$refs.table.loadDatas(1, queryParamObj)
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    // 选择的数据
    chooseData(data) {
      if (data && data.length > 0) {
        this.dataSource = this.dataSource.concat(data)
        postAction(
          '/{v}/SupplierSystem/AddGoodses',
          {
            SupplierId: this.queryParam.SupplierId,
            UserId: this.queryParam.UserId,
            ErpGoodsIds: this.dataSource.map((e) => e.ErpGoodsId),
          },
          'P36001'
        )
          .then((res) => {
            if (res.Data) {
              this.$message.success('添加成功')
              this.$refs.table.loadDatas(1, this.queryParam)
            } else {
              this.$message.error(res.Msg)
            }
          })
          .catch((err) => {
            this.$message.error(err.Msg || err)
          })
      }
    },
  },
}
</script>
<style scoped>
.title-box {
  border: 1px solid #e8e8e8;
  border-bottom: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 15px;
}
</style>
