<!-- 部门管理新增 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="数据范围：" prop="MenuDataPermission">
                <a-radio-group v-model="model.MenuDataPermission">
                  <a-radio :value="1">
                    全部
                  </a-radio>
                  <a-radio :value="2">
                    本人
                  </a-radio>
                  <a-radio :value="3">
                    本人及指定人员
                  </a-radio>
                  <a-radio :value="4">
                    本部门
                  </a-radio>
                  <a-radio :value="5">
                    本部门及下级部门
                  </a-radio>
                </a-radio-group>
              </a-form-model-item>
              <a-card title="已选指定人员" style="width: 100%" v-if="model.MenuDataPermission == 3">
                <div slot="extra">
                  <a-button style="margin-right:10px;" @click="clearTableData">清空</a-button>
                  <a-button type="primary" @click="chooseTableData">
                    选 择
                  </a-button>
                </div>
                <div>
                  <a-table :bordered="false" ref="table" rowKey="Id" size="small" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: '400px', x: '1000' }">
                    <span slot="component" slot-scope="text">
                      <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                      <j-ellipsis :value="text" v-else />
                    </span>
                    <span slot="action" slot-scope="text, record,index">
                      <a @click="delTableData(record,index)">
                        <a-icon type="del" />
                        移除
                      </a>

                    </span>
                  </a-table>
                </div>
              </a-card>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" v-if="type == 'batch'" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存到所有菜单</a-button>
      <a-button :disabled="confirmLoading" v-if="type != 'batch'" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>

    <!-- 表格选择数据弹窗 -->
    <TableSelectDataModal ref="TableSelectDataModal" :selectTableData="selectTableData" @chooseData="chooseData"></TableSelectDataModal>
  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from "@/api/manage";
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
const columns = [
  {
    title: '姓名',
    dataIndex: 'Name',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '手机号',
    dataIndex: 'Phone',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '人员类型',
    dataIndex: 'TypeListStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '所在部门',
    dataIndex: 'DepartmentName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 180,
    fixed: "right",
    scopedSlots: { customRender: 'action' }
  }

]
export default {
  name: "RoleManagementChooseModal",
  mixins: [ListMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "修改菜单数据权限",
      visible: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      type: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      columns: columns,
      roleId: '',
      confirmLoading: false,
      selectTableData: {
        searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '姓名', type: 'input', value: '', key: 'Name', defaultVal: '', placeholder: '请输入' },
          { name: '手机号', type: 'input', value: '', key: 'Phone', defaultVal: '', placeholder: '请输入' },
          {
            name: '所在部门', type: 'selectTreeViewLink', vModel: 'DepartmentId', defaultVal: '', disabled: false, placeholder: '请选择', dataKey: {
              value: 'Id',
              label: 'Name',
              selectableKey: 'Id',
              children: 'ChildrenDepartments'
            }, url: '/{v}/UserPermission/Department/List', httpHead: 'P36001'
          },
        ],
        title: '新增角色授权人员',
        name: '角色授权人员',
        recordKey: 'Id',
        httpHead: 'P36001',
        isInitData: false,
        url: {
          list: '/{v}/UserPermission/User/ListByAddRoleUser',
        },
        columns: [
          {
            title: '姓名',
            dataIndex: 'Name',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '手机号',
            dataIndex: 'Phone',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '人员类型',
            dataIndex: 'TypeListStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '所在部门',
            dataIndex: 'DepartmentName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: "right",
            scopedSlots: { customRender: 'action' },
          }
        ]
      },
      httpHead: 'P36001',
      url: {
        updateRoleMenu: "/{v}/UserPermission/Role/UpdateRoleMenu",
      },
    };
  },
  computed: {
    rules() {
      return {
        MenuDataPermission: [{ required: true, message: "请选择数据范围!" }],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record, roleId, type) {
      this.roleId = roleId
      this.type = type
      this.model = Object.assign({}, record);
      this.dataSource = record.Users || []
      this.visible = true;
    },
    // 确定
    handleOk() {
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          if (this.model.MenuDataPermission == 3 && this.dataSource.length == 0) {
            this.$message.warning('请先选择指定人员')
            return
          }
          let UserIds = []
          let Users = []
          if (this.dataSource && this.dataSource.length > 0) {
            this.dataSource.map((item) => {
              UserIds.push(item.Id)
              Users.push(item)
            })
          }
          if (this.model.MenuDataPermission == 1) {
            this.model.MenuDataPermissionStr = '全部'
          } else if (this.model.MenuDataPermission == 2) {
            this.model.MenuDataPermissionStr = '本人'
          } else if (this.model.MenuDataPermission == 3) {
            let textArray = Users.map((item) => {
              return item.Name
            })
            let text = textArray.join(',')
            this.model.MenuDataPermissionStr = '本人及指定人员：' + text
          } else if (this.model.MenuDataPermission == 4) {
            this.model.MenuDataPermissionStr = '本部门'
          } else if (this.model.MenuDataPermission == 5) {
            this.model.MenuDataPermissionStr = '本部门及下级部门'
          }

          this.model.UserIds = UserIds
          this.model.Users = Users
          this.$emit('ok', this.model, this.roleId, this.type)
          this.close()
        }
      });
    },
    clearTableData() {
      this.dataSource = []
    },
    subDelTableData(record, index) {
      this.dataSource.splice(index, 1)
    },
    delTableData(record, index) {
      let that = this
      this.$confirm({
        title: '确定移除当前人员吗？',
        content: '',
        onOk() {
          that.subDelTableData(record, index)
        },
        onCancel() { }
      })
    },
    chooseTableData() {
      let dataSource = this.dataSource || []
      let queryParam = {
        RoleId: this.roleId,
      }
      this.$refs.TableSelectDataModal.show(dataSource, queryParam)
    },
    // 选择的数据
    chooseData(data) {
      console.log('选择的数据', data)
      if (data && data.length > 0) {
        this.dataSource = this.dataSource.concat(data)
      }

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
