<!-- 人员管理新增 -->
<template>
  <a-modal :title="title" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="序号：(填写99为不展示)" prop="Sort">
                <a-input placeholder="选填" v-model="model.Sort" style="width: 100%" :maxLength="100" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="disableSubmit" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "MenubarPathSortModal",
  components: {},
  data() {
    return {
      title: "编辑序号",
      visible: false,
      disableSubmit: false,
      isEdit: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      ParentId: null,
      ParentName: null,
      confirmLoading: false,
      httpHead: 'P36001',
      url: {
        update: "/{v}/UserPermission/Menu/Update",
      },
    };
  },
  computed: {
    rules() {
      return {
        Sort: [{ required: true, message: "请填写序号!" }],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record) {
      this.ParentId = record.ParentId
      this.ParentName = record.Name
      this.model = Object.assign({}, record);
      if (this.model.IsVerifyData) {
        this.model.IsVerifyData = 1
      } else {
        this.model.IsVerifyData = 2
      }
      this.isEdit = true;
      this.visible = true;
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          //   let formData = Object.assign(this.model, values);
          let formData = {}
          let url = ''
          if (this.isEdit) {
            url = this.url.update
            formData = {
              Id: this.model.Id,
              ParentId: this.ParentId,
              Name: this.model.Name,
              Type: this.model.Type,
              Path: this.model.Path,
              Icon: this.model.Icon,
              IsVerifyData: this.model.IsVerifyData == 1 ? true : false,
              Sort: this.model.Sort ? this.model.Sort : 1,
            }
          } else {
            url = this.url.add
            formData = {
              ParentId: this.ParentId,
              Name: this.model.Name,
              Type: this.model.Type,
              Path: this.model.Path,
              Icon: this.model.Icon,
              IsVerifyData: this.model.IsVerifyData == 1 ? true : false,
              Sort: this.model.Sort ? this.model.Sort : 1,
            }
          }
          postAction(url, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
