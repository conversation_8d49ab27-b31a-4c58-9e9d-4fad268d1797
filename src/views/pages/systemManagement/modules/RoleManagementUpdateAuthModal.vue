<!-- 部门管理新增 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">

      <a-table :bordered="false" ref="table" rowKey="Id" size="middle" :columns="columns" :dataSource="dataSource" :expanded-row-keys.sync="expandedRowKeys" :pagination="ipagination" :loading="loading" :customRow="tableClick" expandRowByClick :row-selection="{ selectedRowKeys: selectedRowKeys, onSelectAll: onSelectAll, onSelect: onSelect,}" :scroll="{ y: '450px', x: false }">
        <span slot="menuDataPermissionName">数据范围(<a @click.stop="batchPermission()">点此批量修改</a>)</span>
        <!-- 数据范围 -->
        <span slot="menuDataPermission" slot-scope="text, record">
          <div style="display: flex;" v-if="record.Type == 2 && record.IsVerifyData">
            <div style="margin-right:10px;" v-if="record.MenuDataPermission">
              <j-ellipsis :value="'' + (text==0?text:(text?text:'--'))" :length="12" />
            </div>
            <div v-else style="margin-right:5px;">请选择，不填则默认本人</div>
            <div>
              <span v-if="record.MenuDataPermission">请选择</span>
              <a style="margin-left:5px;" @click.stop="editMenuDataPermission(record)">
                <a-icon type="edit" />
              </a>
            </div>

          </div>
        </span>
      </a-table>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
    <!-- 修改菜单数据权限 -->
    <RoleManagementChooseModal ref="RoleManagementChooseModal" @ok="modalOk"></RoleManagementChooseModal>
  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from "@/api/manage";
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
const columns = [
  {
    title: '菜单名称',
    dataIndex: 'Name',
    width: 300,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '菜单类型',
    dataIndex: 'TypeStr',
    width: 100,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    dataIndex: 'MenuDataPermissionStr',
    width: 300,
    ellipsis: true,
    slots: { title: 'menuDataPermissionName' },
    scopedSlots: { customRender: 'menuDataPermission' },
  },

]

export default {
  name: "RoleManagementUpdateAuthModal",
  mixins: [ListMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "修改角色权限",
      visible: false,
      isEdit: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      roleId: '',
      type: '',
      queryParam: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      selectMenuDataPermission: {},
      selectedRowKeys: [],//选中的数据
      expandedRowKeys: [],//展开的数据
      allRowKeys: [],
      isClick: false,
      expandRowByClick: false,
      columns: columns,
      confirmLoading: false,
      isInitData: false, //是否自动加载
      httpHead: 'P36001',
      url: {
        list: '/{v}/UserPermission/Role/RoleMenuList',
        updateRoleMenu: "/{v}/UserPermission/Role/UpdateRoleMenu",
      },
    };
  },
  filters: {
    MenuDataPermission(val) {
      if (val == 1) {
        return '全部'
      } else if (val == 2) {
        return '本人'
      } else if (val == 3) {
        return '本人及指定人员'
      } else if (val == 4) {
        return '本部门'
      } else if (val == 5) {
        return '本部门及下级部门'
      }
    }
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(roleId) {
      this.model = {}
      this.dataSource = []
      this.visible = true;
      this.roleId = roleId;
      this.type = ''
      this.queryParam.roleId = roleId;
      this.confirmLoading = false;
      this.selectedRowKeys = []
      // 重组选中的数据
      this.loadData(1, (data) => {
        this.allRowKeys = []
        let chooseTableData = []
        let allTableData = this.setSelectedRowKeys(data) || []
        allTableData.forEach((item) => {
          if (item.IsCheck) {
            chooseTableData.push(item.MenuId)
          }
        })
        this.selectedRowKeys = chooseTableData
        // 默认展开
        if (data.length > 0) {
          this.expandedRowKeys = [data[0].Id]
        } else {
          this.expandedRowKeys = []
        }
        this.dataSource = data
      })
    },
    editMenuDataPermission(record) {
      console.log('点击请选择', record, this.dataSource)
      this.type = ''
      this.$refs.RoleManagementChooseModal.show(record, this.roleId)
    },
    batchPermission() {
      console.log('点击批量修改')
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请先选中菜单名称再点击批量修改')
        return
      }
      this.type = 'batch'
      this.$refs.RoleManagementChooseModal.show({}, this.roleId, 'batch')
    },
    modalOk(model, roleId, type) {
      this.selectMenuDataPermission = model
      console.log('选择的数据范围', model, roleId, type)
      if (type == 'batch') {
        this.dataSource = this.batchTableData(this.dataSource)
        console.log(this.dataSource)
      } else {
        this.dataSource = this.setTableData(this.dataSource)
      }
    },
    // 递归重组批量修改筛选数据
    batchTableData(data) {
      if (data == null || data.length <= 0) {
        return []
      }
      let tempArr = []
      try {
        data.forEach(item => {
          var temp = item
          if (item.children && item.children.length > 0) {
            this.batchTableData(item.children)
          }
          if (item.IsCheck && item.Type == 2) {
            temp.MenuDataPermission = this.selectMenuDataPermission.MenuDataPermission
            temp.MenuDataPermissionStr = this.selectMenuDataPermission.MenuDataPermissionStr
            temp.Users = this.selectMenuDataPermission.Users
            temp.UserIds = this.selectMenuDataPermission.UserIds
          }
          tempArr.push(temp)
        })

      } catch (e) {
        console.log(e)
      }
      return tempArr
    },

    // 确定
    handleOk() {
      let chooseDataArray = this.getChooseData() || []
      let formData = {
        RoleId: this.roleId,
        Menus: chooseDataArray,
      }
      if (this.type == 'batch') {
        if (chooseDataArray.length == 0) {
          this.$message.warning("批量修改权限需要选择对应的菜单权限再操作");
          return
        }
      }
      this.confirmLoading = true
      this.loading = true
      postAction(this.url.updateRoleMenu, formData, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          this.$message.success("角色权限修改成功");
          this.$emit("ok");
          this.close();
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {
        this.loading = false
        this.confirmLoading = false
      });
    },
    // 递归重组表格数据
    setTableData(data) {
      if (data == null || data.length <= 0) {
        return []
      }
      let tempArr = []
      try {
        data.forEach(item => {
          var temp = item
          if (item.children && item.children.length > 0) {
            this.setTableData(item.children)
          }
          if (item.Id == this.selectMenuDataPermission.Id) {
            temp.MenuDataPermission = this.selectMenuDataPermission.MenuDataPermission
            temp.MenuDataPermissionStr = this.selectMenuDataPermission.MenuDataPermissionStr
            temp.Users = this.selectMenuDataPermission.Users
            temp.UserIds = this.selectMenuDataPermission.UserIds
            temp.IsVerifyData = this.selectMenuDataPermission.IsVerifyData
          }
          tempArr.push(temp)
        })

      } catch (e) {
        console.log(e)
      }
      return tempArr
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
    /*递归数状表格*/
    // 当用户手动勾选全选 Checkbox 时触发的事件
    onSelectAll(selected) {
      if (selected) {
        const tabData = this.dataSource;
        const arr = [];
        setVal(tabData, arr);
        this.selectedRowKeys = arr;
      } else {
        this.selectedRowKeys = [];
      }
      function setVal(list, arr) {
        list.forEach(v => {
          v.IsCheck = true
          arr.push(v.Id);
          if (v.children) {
            setVal(v.children, arr);
          }
        });
      }
    },
    setTableCheckData(data) {
      if (data == null || data.length <= 0) {
        return []
      }
      let tempArr = []
      try {
        data.forEach(item => {
          var temp = item
          if (item.children && item.children.length > 0) {
            this.setTableCheckData(item.children)
          }
          let checkObj = null
          checkObj = this.selectedRowKeys.find((r) => {
            return item.Id == r
          })
          if (checkObj) {
            temp.IsCheck = true
          } else {
            temp.IsCheck = false
          }
          tempArr.push(temp)
        })

      } catch (e) {
        console.log(e)
      }
      return tempArr
    },
    // 当用户手动勾选数据行的 Checkbox 时触发的事件
    onSelect(record, selected) {
      const set = new Set(this.selectedRowKeys);
      const tabData = this.dataSource;
      const key = record.Id;
      if (selected) {
        set.add(key);
        record.children && setChildCheck(record.children);
        setParentCheck(key);
      } else {
        set.delete(key);
        // record.children && setChildUncheck(record.children);
        // setParentUncheck(key);
      }

      this.selectedRowKeys = Array.from(set);
      // 手动切换的数据赋值
      this.dataSource = this.setTableCheckData(this.dataSource)
      // console.log('手动切换的数据', this.selectedRowKeys)
      // 设置父级选择
      function setParentCheck(Id) {
        let parent = getParent(Id);
        if (parent) {
          set.add(parent.Id);
          setParentCheck(parent.Id);
        }
      }
      // 设置父级取消，如果父级的子集有选择，则不取消
      function setParentUncheck(Id) {
        let childHasCheck = false,
          parent = getParent(Id);
        if (parent) {
          let childlist = parent.children;
          childlist.forEach(function (v) {
            if (set.has(v.Id)) {
              childHasCheck = true;
            }
          });
          if (!childHasCheck) {
            set.delete(parent.Id);
            setParentUncheck(parent.Id);
          }
        }
      }
      // 获取当前对象的父级
      function getParent(Id) {
        for (let i = 0; i < tabData.length; i++) {
          if (tabData[i].Id === Id) {
            return null;
          }
        }
        return _getParent(tabData);
        function _getParent(list) {
          let childlist,
            isExist = false;
          for (let i = 0; i < list.length; i++) {
            if ((childlist = list[i].children)) {
              childlist.forEach(function (v) {
                if (v.Id === Id) {
                  isExist = true;
                }
              });
              if (isExist) {
                return list[i];
              }
              if (_getParent(childlist)) {
                return _getParent(childlist);
              }
            }
          }
        }
      }
      // 设置child全选
      function setChildCheck(list) {
        list.forEach(function (v) {
          set.add(v.Id);
          v.children && setChildCheck(v.children);
        });
      }
      // 设置child取消
      function setChildUncheck(list) {
        list.forEach(function (v) {
          set.delete(v.Id);
          v.children && setChildUncheck(v.children);
        });
      }
    },

    /** 点击a-table中的行后，展开或关闭其子行 */
    tableClick(record, index) {
      //   console.log('selectedRowKeys', this.selectedRowKeys)
      return {
        style: {
          cursor: 'pointer',
        },
        on: {
          click: (event) => {
            this.expandRowByClick = !this.expandRowByClick;
          },
        }
      }
    },
    getChooseData() {
      this.allRowKeys = []
      let allArray = this.setSelectedRowKeys(this.dataSource) || []
      let selectedRowData = []
      allArray.forEach((item) => {
        this.selectedRowKeys.forEach((rItem) => {
          if (item.MenuId == rItem) {
            selectedRowData.push(item)
          }
        })
      })
      return selectedRowData
    },
    // 递归重组数据
    setSelectedRowKeys(data) {
      if (data == null || data.length <= 0) {
        return []
      }
      try {
        data.forEach(item => {
          var temp = {
            Name: item.Name,
            MenuId: item.Id,
            MenuCode: item.Code,
            MenuDataPermission: item.MenuDataPermission,
            UserIds: item.UserIds,
            IsCheck: item.IsCheck,
          }
          this.allRowKeys.push(temp)
          if (item.children && item.children.length > 0) {
            this.setSelectedRowKeys(item.children)
          }
        })

      } catch (e) {
        console.log(e)
      }
      return this.allRowKeys
    },

  },
};
</script>
