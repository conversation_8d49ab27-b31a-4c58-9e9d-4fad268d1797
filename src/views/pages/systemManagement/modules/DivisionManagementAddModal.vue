<!-- 部门管理新增 -->
<template>
  <a-modal
    :title="title"
    :width="600"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true">
    <div
      :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <p>上级部门：{{ ParentName || '无' }}</p>
              <a-form-model-item label="部门名称：" prop="Name">
                <a-input placeholder="请输入" v-model="model.Name" style="width: 100%" />
              </a-form-model-item>
              <a-form-model-item label="部门负责人：">
                <SingleInputSearchView
                  placeholder="请输入或选择"
                  width="100%"
                  :httpParams="{
                    pageIndex: 1,
                    pageSize: 200,
                    IsValid: true,
                  }"
                  keyWord="keyWord"
                  :httpHead="httpHead"
                  :Url="url.principaList"
                  :value="model.PrincipalId"
                  :name="model.PrincipalName"
                  :isAbsolute="true"
                  @clear="()=>{model.PrincipalId='';model.PrincipalName = ''}"
@change="changeEmployeeName" />
              </a-form-model-item>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "DivisionManagementAddModal",
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      title: "新增部门",
      visible: false,
      isEdit: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      ParentId: null,
      ParentName: null,
      confirmLoading: false,
      logisticOrderRemarksData: [],
      httpHead: 'P36001',
      url: {
        add: "/{v}/UserPermission/Department/Create",
        update: "/{v}/UserPermission/Department/Update",
        principaList: '/{v}/UserPermission/User/List',
      },
    };
  },
  computed: {
    rules() {
      return {
        Name: [{ required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 20, '部门名称') },],
        PrincipalId: [{ required: true, message: "请选择部门负责人!" }],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add(record) {
      this.title = '新增部门'
      this.ParentId = record.Id
      this.ParentName = record.Name
      this.model = {
        PrincipalId: null,
        PrincipalName: '',
      }
      this.isEdit = false;
      this.visible = true;
    },
    edit(record) {
      this.title = '编辑部门'
      this.ParentId = record.ParentId
      this.ParentName = record.ParentName
      this.model = Object.assign({}, record);
      this.isEdit = true;
      this.visible = true;
    },
    changeEmployeeName(val, txt, item) {
      this.model.PrincipalId = val;
      this.model.PrincipalName = txt;
    },
    changeSingleSelet(val, txt, item) {
      this.model.CallCompanyId = val
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          //   let formData = Object.assign(this.model, values);
          let formData = {}
          let url = ''
          if (this.isEdit) {
            url = this.url.update
            formData = {
              Id: this.model.Id,
              ParentId: this.ParentId,
              Name: this.model.Name,
              PrincipalId: this.model.PrincipalId || null,
            }
          } else {
            url = this.url.add
            formData = {
              ParentId: this.ParentId,
              Name: this.model.Name,
              PrincipalId: this.model.PrincipalId || null,
            }
          }
          postAction(url, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
      this.logisticOrderRemarksData = [];
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-modal-body {
  overflow-y: unset;
}
</style>
