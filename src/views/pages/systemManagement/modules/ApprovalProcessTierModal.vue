<!-- 新增审批层级 -->
<template>
  <a-modal
    :title="title"
    :width="600"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <div
      :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }"
    >
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <p v-if="preModel.ApprovalMethod == 1">
                层级排序：<span style="color: red">{{ pliesNum }}</span>
              </p>
              <a-form-model-item label="审批类型：" prop="ApprovalType">
                <a-radio-group v-model="model.ApprovalType" name="ApprovalType" @change="onTypeChange">
                  <a-radio :value="1"> 内部审批 </a-radio>
                  <a-radio :value="2"> 外部审批 </a-radio>
                </a-radio-group>
              </a-form-model-item>
              <a-form-model-item label="审批部门：" v-if="model.ApprovalType !== 1" prop="DepartmentId">
                <SelectTreeViewModel
                  style="width: 100%"
                  placeholder="请选择或搜索名称"
                  :Url="url.getDepartment"
                  v-model="model.DepartmentId"
                  :httpHead="httpHead"
                  :dataKey="{
                    value: 'Id',
                    label: 'Name',
                    children: 'ChildrenDepartments',
                  }"
                  @change="(val, selectItem) => changeTreeId(val, selectItem, 'DepartmentId', 'DepartmentName')"
                />
              </a-form-model-item>
              <a-form-model-item label="审批角色：" prop="RoleId">
                <SingleInputSearchView
                  placeholder="请输入或选择"
                  width="100%"
                  :httpParams="{
                    pageIndex: 1,
                    pageSize: 200,
                    IsValid: true,
                  }"
                  keyWord="keyWord"
                  :httpHead="httpHead"
                  :Url="url.roleList"
                  :value="model.RoleId"
                  :name="model.RoleName"
                  :isAbsolute="true"
                  @clear="
                    () => {
                      model.RoleId = ''
                      model.RoleName = ''
                    }
                  "
                  @change="changeRole"
                />
                <!-- <SingleChoiceSearchView style="width: 100%" placeholder="请选择" :httpHead="httpHead" :Url="url.roleList" v-model="model.RoleId" @change="changeRole" /> -->
              </a-form-model-item>
              <a-form-model-item label="前置条件：">
                <a-textarea placeholder="请输入" v-model="model.TriggerContent" :rows="4" />
              </a-form-model-item>
              <a-form-model-item label="前置条件描述：">
                <a-textarea placeholder="请输入" v-model="model.Description" :rows="4" />
              </a-form-model-item>
              <a-form-model-item label="erp配置：">
                <!-- <a-textarea placeholder="请输入" v-model="model.ErpConfig" :rows="4" :maxLength="300" /> -->
                <EnumSingleChoiceView
                  style="width: 100%"
                  placeholder="请选择"
                  dictCode="EnumApproavalErpLevel"
                  v-model="model.ErpLevel"
                  @change="(value, text) => ((model.ErpLevel = value), (model.ErpLevelStr = text))"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary"
        >保存</a-button
      >
      <a-button @click="handleCancel"> 取消 </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'ApprovalProcessTierModal',
  components: {},
  data() {
    return {
      title: '新增审批层级',
      visible: false,
      isEdit: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      model: { RoleId: '', RoleName: '' },
      preModel: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      type: '',
      pliesNum: null,
      ParentName: '',
      updateIndex: null,
      confirmLoading: false,
      httpHead: 'P36001',
      url: {
        add: '/{v}/ApprovalWorkFlow/CreateApprovalWorkFlow',
        getDepartment: '/{v}/UserPermission/Department/List', //部门
        roleList: '/{v}/UserPermission/Role/List',
      },
    }
  },
  computed: {
    rules() {
      return {
        ApprovalType: [{ required: true, message: '请选择审批类型!' }],
        DepartmentId: [{ required: true, message: '请选择审批部门!' }],
        RoleId: [{ required: true, message: '请选择审批角色!' }],
      }
    },
  },
  mounted() {},
  created() {},
  methods: {
    moment,
    add(preModel, index, type) {
      this.title = '新增审批' + (preModel.ApprovalMethod == 1 ? '层级' : '对象')
      this.model = {
        RoleId: '',
        RoleName: '',
        ApprovalType: 1,
        ApprovalTypeStr: '内部审批',
      }
      this.preModel = preModel
      this.type = type ? type : ''
      this.updateIndex = index
      this.pliesNum = index == 0 || index ? index + 2 : preModel.dataSourceLength + 1
      this.isEdit = false
      this.visible = true
    },
    edit(preModel, record, index) {
      this.title = '编辑审批' + (preModel.ApprovalMethod == 1 ? '层级' : '对象')
      this.updateIndex = index
      record.ErpLevel = record.ErpLevel != null ? record.ErpLevel.toString() : ''
      this.model = Object.assign({}, record)
      this.preModel = preModel
      this.isEdit = true
      this.visible = true
    },
    onTypeChange(e) {
      if (this.model.ApprovalType == 1) {
        this.model.ApprovalTypeStr = '内部审批'
      } else if (this.model.ApprovalType == 2) {
        this.model.ApprovalTypeStr = '外部审批'
      }
    },
    changeTreeId(val, selectItem, key, name) {
      this.model[key] = val
      this.model[name] = selectItem.label
    },
    changeRole(val, name) {
      // this.model.RoleName = name
      this.model.RoleId = val
      this.model.RoleName = name
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.$message.success('添加成功')
          if (this.model.ApprovalType == 1) {
            this.model.DepartmentId = null
            this.model.DepartmentName = null
          }
          that.$emit('ok', this.model, this.updateIndex, this.type)
          that.close()
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>
