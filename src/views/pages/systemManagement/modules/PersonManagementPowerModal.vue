<!-- 人员管理权限信息 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :footer="null" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div>权限范围：{{model.Permission || '--'}}</div>
    <!-- 列表 -->
    <SimpleTable ref="table" v-show="!IsAllPermission" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab :cardBordered="false" :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab" />

  </a-modal>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'

export default {
  name: "PersonManagementPowerModal",
  mixins: [SimpleMixin],
  data() {
    return {
      title: '查看人员权限',
      visible: false,
      confirmLoading: false,
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '参数', type: 'input', value: '', key: 'PurchaseNo1', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [

        ],
        rowKey: 'Id',
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
      columns: [
        {
          title: '菜单名称',
          dataIndex: 'Name',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '菜单类型',
          dataIndex: 'TypeStr',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '数据范围',
          dataIndex: 'MenuDataPermissionStr',
          width: 300,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      queryParam: {

      },
      model: {},
      IsAllPermission: null,
      isTableInitData: false,//是否自动加载
      linkHttpHead: 'P36001',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/UserPermission/User/MenuListByUserId',
      },
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    show(record) {
      this.visible = true
      this.model = record
      this.$nextTick(() => {
        this.queryParam.userId = record.Id
        this.$refs.table.loadDatas(1, this.queryParam, (data) => {
          // 获取表格数据
          if (data) {
            this.IsAllPermission = data.IsAllPermission
            // 默认展开第一个
            if (data.Menus && data.Menus.length > 0) {
              this.$refs.table.expandedRowKeys = [data.Menus[0].Id]
              this.$refs.table.dataSource = data.Menus || []
            }

          }
        })
      })
    },
    operate() {

    },
    handleOk() {
      this.visible = false
    },
    handleCancel() {
      this.visible = false
    },
  }

}
</script>

<style>
</style>