<!-- 新增审批流程 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-col :md="24" style="margin-bottom:10px;">
          <a-col :md="12">审批场景：{{model.ScenesStr || '--'}}</a-col>
          <a-col :md="12">审批方式：{{model.ApprovalMethodStr || '--'}}</a-col>
        </a-col>
        <a-form-model-item :label="model.ApprovalMethod == 1?'审批层级：':'审批对象：'" required prop="ApprovalWorkFlowNodes">
          <div class="title-box">
            <!-- 会签 2 或签 1 -->
            <span class="title-span" v-if="model.ApprovalMethod == 1">或签审批可配置多个层级，每个层级只有1个审批对象</span>
            <span class="title-span" v-if="model.ApprovalMethod == 2">会签审批只有1个层级，可配置多个审批对象</span>
            <a-button type="primary" @click="addTier">
              新增
            </a-button>
          </div>
          <a-table ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length>0?'400px':false, x: '800px' }">
            <!-- 序号 -->
            <span slot="Order" slot-scope="text, record, order">
              <span style="color:red;"> {{ getOrder(record, order) }}</span>
            </span>
            <!-- 字符串超长截取省略号显示-->
            <span slot="component" slot-scope="text">
              <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
              <j-ellipsis :value="text" v-else />
            </span>
            <!-- 操作 -->
            <span slot="action" slot-scope="text, record,index">
              <a v-if="model.ApprovalMethod == 1" @click="operate('sort',record,index)">插入下级</a>
              <a @click="operate('update',record,index)" style="margin:0 10px;">编辑</a>
              <a @click="operate('del',record,index)">删除</a>
            </span>
          </a-table>
        </a-form-model-item>
        <a-form-model-item label="备注：">
          <a-textarea placeholder="请输入" v-model="model.Description" :rows="4" :maxLength="200" />
        </a-form-model-item>

      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
    <!-- 新增审批层级 -->
    <ApprovalProcessTierModal ref="ApprovalProcessTierModal" @ok="modalOk"></ApprovalProcessTierModal>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
import moment from "moment";
const columns = [
  {
    title: '层级排序',
    dataIndex: 'Order',
    width: 80,
    ellipsis: true,
    scopedSlots: { customRender: 'Order' },
  },
  {
    title: '审批类型',
    dataIndex: 'ApprovalTypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '审批部门',
    dataIndex: 'DepartmentName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '审批角色',
    dataIndex: 'RoleName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '前置条件描述',
    dataIndex: 'Description',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: 'erp配置',
    dataIndex: 'ErpLevelStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 160,
    fixed: "right",
    scopedSlots: { customRender: 'action' }
  }
]
const columns2 = [
  {
    title: '审批类型',
    dataIndex: 'ApprovalTypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '审批部门',
    dataIndex: 'DepartmentName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '审批角色',
    dataIndex: 'RoleName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '前置条件描述',
    dataIndex: 'Description',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: 'erp配置',
    dataIndex: 'ErpLevelStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 160,
    fixed: "right",
    scopedSlots: { customRender: 'action' }
  }
]

export default {
  name: "ApprovalProcessAddModal",
  mixins: [ListMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "新增审批流程",
      visible: false,
      isEdit: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      columns: columns,
      confirmLoading: false,
      isInitData: false,
      httpHead: 'P36005',
      url: {
        list: '',
        add: '/{v}/ApprovalWorkFlow/CreateApprovalWorkFlow',
        update: '/{v}/ApprovalWorkFlow/EditApprovalWorkFlow',
        getInfo: '/{v}/ApprovalWorkFlow/GetApprovalWorkFlowInfo',
      },
    };
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record) {
      if (record.Id) {
        this.title = '编辑审批流程'
        this.isEdit = true;
        this.dataSource = []
        this.getInfo(record)
      } else {
        this.title = '新增审批流程'
        this.isEdit = false;
        this.model = record
        this.dataSource = []
        this.setData()
      }
      this.visible = true;
    },
    setData() {
      // 会签 2 或签 1
      if (this.model.ApprovalMethod == 2) {
        this.columns = columns2
      } else if (this.model.ApprovalMethod == 1) {
        // 或签
        this.columns = columns
      }
    },
    getInfo(record) {
      let formData = {
        id: record.Id,
      }
      this.confirmLoading = true;
      getAction(this.url.getInfo, formData, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          this.model = res.Data
          this.model.ScenesStr = record.ScenesStr
          this.model.ApprovalMethod = record.ApprovalMethod
          this.model.ApprovalMethodStr = record.ApprovalMethodStr
          if (res.Data.ApprovalWorkflowNodeDtos && res.Data.ApprovalWorkflowNodeDtos.length > 0) {
            this.dataSource = res.Data.ApprovalWorkflowNodeDtos
          }
          this.setData()
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {
        this.confirmLoading = false;
      });
    },
    addTier() {
      let dataSourceLength = this.dataSource.length
      this.model.dataSourceLength = dataSourceLength
      this.$refs.ApprovalProcessTierModal.add(this.model)
    },
    modalOk(data, updateIndex, type) {
      data.ErpLevelStr = data.ErpLevelStr ? data.ErpLevelStr : ''
      if (updateIndex == null) {
        this.dataSource.push(data)
      } else if (updateIndex !== null && type == 'sort') {
        let array = this.dataSource
        let index = updateIndex
        if (array[index + 1]) {
          array.splice(index + 1, 1, data, array[index + 1])
        } else {
          this.dataSource.push(data)
        }

      } else {
        this.dataSource.splice(updateIndex, 1, data)
      }
    },
    // 确定
    handleOk() {
      if (this.dataSource.length == 0) {
        this.$message.warning("请先选择审批层级!");
        return
      }
      let newArray = this.dataSource.map((item) => {
        return {
          ApprovalType: item.ApprovalType,
          TriggerContent: item.TriggerContent,
          Description: item.Description,
          DepartmentId: item.DepartmentId,
          DepartmentName: item.DepartmentName,
          RoleId: item.RoleId,
          RoleName: item.RoleName,
          ErpLevel: item.ErpLevel ? Number(item.ErpLevel) : null,
        }
      })
      this.confirmLoading = true;
      let formData = {
        Name: '',
        Description: this.model.Description ? this.model.Description : '',
        ApprovalFormId: this.model.ApprovalFormId,
        ApprovalWorkFlowNodes: newArray,
      }
      let url = ''
      if (this.isEdit) {
        url = this.url.update
        formData.Id = this.model.Id
      } else {
        url = this.url.add
      }
      postAction(url, formData, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          this.$message.success("操作成功");
          this.$emit("ok");
          this.close();
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {
        this.confirmLoading = false;
      });
    },
    // 确定删除
    subDelInfo(record, index) {
      this.dataSource.splice(index, 1)
    },
    operate(type, record, index) {
      if (type == 'sort') {
        this.$refs.ApprovalProcessTierModal.add(this.model, index, 'sort')
      } else if (type == 'update') {
        this.$refs.ApprovalProcessTierModal.edit(this.model, record, index)
      } else if (type == 'del') {
        let that = this
        this.$confirm({
          title: '您确定删除当前审批层级？',
          content: '',
          onOk() {
            that.subDelInfo(record, index)
          },
          onCancel() { }
        })
      }
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
<style scoped>
.title-box {
  border: 1px solid #e8e8e8;
  border-bottom: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 15px;
}
</style>
