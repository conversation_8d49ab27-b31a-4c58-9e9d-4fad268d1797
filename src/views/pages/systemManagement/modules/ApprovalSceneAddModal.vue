<!-- 审批流程场景新增 -->
<template>
  <a-modal :title="title" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="审批场景：" prop="Scenes">
                <EnumSingleChoiceView style="width: 100%" placeholder="请选择" dictCode="EnumApprovalScenes" v-model="model.Scenes" />
              </a-form-model-item>
              <a-form-model-item label="审批方式：" prop="ApprovalMethod">
                <a-select style="width: 100%" v-model="model.ApprovalMethod" placeholder="请选择">
                  <a-select-option value="">请选择</a-select-option>
                  <a-select-option :value="1">或签</a-select-option>
                  <a-select-option :value="2">会签</a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="对应单据：" prop="CnName">
                <a-input placeholder="请输入" v-model="model.CnName" style="width: 100%" :maxLength="50" />
              </a-form-model-item>
              <a-form-model-item label="数据库表名：" prop="EnName">
                <a-input placeholder="请输入" v-model="model.EnName" style="width: 100%" :maxLength="50" />
              </a-form-model-item>
              <a-form-model-item label="审批事件：" prop="SubscriptionCapEventName">
                <a-input placeholder="请输入" v-model="model.SubscriptionCapEventName" style="width: 100%" :maxLength="50" />
              </a-form-model-item>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "ApprovalSceneAddModal",
  components: {},
  data() {
    return {
      title: "新增审批流程场景",
      visible: false,
      isEdit: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      httpHead: 'P36005',
      url: {
        add: "/{v}/ApprovalWorkFlow/CreateApprovalForm",
        update: "/{v}/ApprovalWorkFlow/EditApprovalForm",
      },
    };
  },
  computed: {
    rules() {
      return {
        Scenes: [{ required: true, message: "请选择审批场景!" }],
        ApprovalMethod: [{ required: true, message: "请选择审批方式!" }],
        CnName: [{ required: true, message: "请输入对应单据!" }],
        EnName: [{ required: true, message: "请输入数据库表名!" }],
        SubscriptionCapEventName: [{ required: true, message: "请输入审批事件!" }],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add() {
      this.title = '新增审批流程场景'
      this.model = {}
      this.isEdit = false;
      this.visible = true;
    },
    edit(record) {
      this.title = '编辑审批流程场景'
      this.model = Object.assign({}, record);
      this.model.Scenes = this.model.Scenes ? this.model.Scenes.toString() : ''
      this.isEdit = true;
      this.visible = true;
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          let formData = {}
          let url = ''
          if (this.isEdit) {
            url = this.url.update
            formData = {
              Id: this.model.Id,
              CnName: this.model.CnName,
              EnName: this.model.EnName,
              Scenes: this.model.Scenes ? parseInt(this.model.Scenes) : null,
              ApprovalMethod: this.model.ApprovalMethod,
              SubscriptionCapEventName: this.model.SubscriptionCapEventName,
            }
          } else {
            url = this.url.add
            formData = {
              CnName: this.model.CnName,
              EnName: this.model.EnName,
              Scenes: this.model.Scenes ? parseInt(this.model.Scenes) : null,
              ApprovalMethod: this.model.ApprovalMethod,
              SubscriptionCapEventName: this.model.SubscriptionCapEventName,
            }
          }
          postAction(url, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
