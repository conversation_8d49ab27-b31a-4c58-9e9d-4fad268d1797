<!-- 新增供应商业务员 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        // border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <p style="color: rgba(0, 0, 0, 0.85); margin-bottom: 8px">供应商：{{ record.SupplierName || '--' }}</p>
              <a-form-model-item label="姓名：" prop="UserName">
                <a-input placeholder="请输入" v-model="model.UserName" style="width: 100%" />
              </a-form-model-item>
              <a-form-model-item label="手机号：" prop="UserPhone">
                <a-input placeholder="请输入" v-model="model.UserPhone" style="width: 100%" :maxLength="11" />
              </a-form-model-item>
              <div class="erp-goods">已授权品种：</div>
              <a-card title="已选商品" style="width: 100%">
                <div slot="extra">
                  <a-button style="margin-right: 10px" @click="clearTableData">清空</a-button>
                  <a-button type="primary" @click="chooseTableData"> 选 择 </a-button>
                </div>
                <div>
                  <a-table :bordered="false" ref="table" rowKey="ErpGoodsId" size="small" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length == 0 ? false : '400px', x: '1000' }">
                    <span slot="component" slot-scope="text">
                      <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                      <j-ellipsis :value="text" v-else />
                    </span>
                    <span slot="action" slot-scope="text, record, index">
                      <a @click="delTableData(record, index)">
                        <a-icon type="del" />
                        移除
                      </a>
                    </span>
                  </a-table>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="saveLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </a-row>

    <!-- 表格选择数据弹窗 -->
    <TableSelectDataModal ref="TableSelectDataModal" :selectTableData="selectTableData" @chooseData="chooseData"></TableSelectDataModal>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'SupplierManageSalesAddModel',
  mixins: [ListMixin, EditMixin],
  components: {
    JEllipsis,
  },
  data() {
    return {
      title: '新增供应商业务员',
      visible: false,
      model: {
        SupplierId: '',
        UserName: '',
        UserPhone: '',
        ErpGoodsIds: [],
      },
      record: {},
      type: '',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
        },
      ],
      roleId: '',
      confirmLoading: false,
      saveLoading: false,
      selectTableData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '商品', type: 'input', value: '', key: 'GoodsKey', defaultVal: '', placeholder: '名称/编号/拼音码' },
        ],
        title: '新增授权品种',
        name: '授权品种',
        recordKey: 'ErpGoodsId',
        httpHead: 'P36001',
        isInitData: false,
        url: {
          list: '/{v}/SupplierSystem/QueryGoodses',
        },
        columns: [
          {
            title: '商品名称',
            dataIndex: 'ErpGoodsName',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '规格',
            dataIndex: 'PackingSpecification',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '单位',
            dataIndex: 'PackageUnit',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '生产厂商',
            dataIndex: 'BrandManufacturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '商品编号',
            dataIndex: 'ErpGoodsCode',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
      httpHead: 'P36001',
    }
  },
  computed: {
    rules() {
      return {
        UserName: [{ required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 20, '姓名') },],
        UserPhone: [{ required: true, validator: this.checkTelphone }],
      }
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record) {
      this.model.SupplierId = record.SupplierId
      this.record = record
      this.visible = true
    },
    // 确定
    handleOk() {
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          if (this.dataSource && this.dataSource.length == 0) {
            this.$message.warning('请选择授权品种！')
            return
          }

          this.model.ErpGoodsIds = this.dataSource.map((e) => e.ErpGoodsId)
          this.saveLoading = true
          postAction('/{v}/SupplierSystem/CreateSupplierUser', this.model, 'P36001')
            .then((res) => {
              if (res.Data) {
                this.$message.success('操作成功')
                this.model = {
                  SupplierId: '',
                  UserName: '',
                  UserPhone: '',
                  ErpGoodsIds: [],
                }
                this.$emit('ok')
                this.dataSource = []
                this.close()
              } else {
                this.$message.error(res.Msg)
              }
            })
            .catch((err) => {
              this.$message.error(err.Msg || err)
            })
            .finally(() => {
              this.saveLoading = false
            })
        }
      })
    },
    clearTableData() {
      this.dataSource = []
    },
    subDelTableData(record, index) {
      this.dataSource.splice(index, 1)
    },
    delTableData(record, index) {
      let that = this
      this.$confirm({
        title: '确定移除当前品种吗？',
        content: '',
        onOk() {
          that.subDelTableData(record, index)
        },
        onCancel() { },
      })
    },
    chooseTableData() {
      let dataSource = this.dataSource || []
      let queryParam = {
        SupplierId: this.model.SupplierId,
        IsAdd: true,
      }
      this.$refs.TableSelectDataModal.show(dataSource, queryParam)
    },
    // 选择的数据
    chooseData(data) {
      if (data && data.length > 0) {
        this.dataSource = this.dataSource.concat(data)
      }
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
        ; (this.model = {
          SupplierId: '',
          UserName: '',
          UserPhone: '',
          ErpGoodsIds: [],
        }),
          (this.visible = false)
    },
  },
}
</script>
<style>
.erp-goods {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}
.erp-goods::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}
</style>
