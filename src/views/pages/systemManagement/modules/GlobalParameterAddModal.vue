<!-- 全局参数新增 -->
<template>
  <a-modal :title="title" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="参数名称：" prop="Name">
                <a-input placeholder="请输入" v-model="model.Name" style="width: 100%" :maxLength="50" />
              </a-form-model-item>
              <a-form-model-item label="参数编码：" prop="Code">
                <a-input placeholder="请输入" v-model="model.Code" style="width: 100%" :maxLength="50" />
              </a-form-model-item>
              <a-form-model-item label="参数备注：">
                <a-textarea placeholder="请输入" v-model="model.Remarks" :rows="4" :maxLength="200" />
              </a-form-model-item>
              <a-form-model-item label="参数值：" prop="Value">
                <a-textarea placeholder="请输入" v-model="model.Value" :rows="4" :maxLength="200" />
              </a-form-model-item>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "GlobalParameterAddModal",
  components: {},
  data() {
    return {
      title: "新增全局参数",
      visible: false,
      isEdit: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      httpHead: 'P36001',
      url: {
        add: "/{v}/Global/UpdateGlobalParameter",
      },
    };
  },
  computed: {
    rules() {
      return {
        Name: [{ required: true, message: "请输入参数名称!" }],
        Code: [{ required: true, message: "请输入参数编码!" }],
        Value: [{ required: true, message: "请输入系统值!" }],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add() {
      this.title = '新增全局参数'
      this.model = {}
      this.isEdit = false;
      this.visible = true;
    },
    edit(record) {
      this.title = '编辑全局参数'
      this.model = Object.assign({}, record);
      this.isEdit = true;
      this.visible = true;
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          let formData = {}
          let url = ''
          if (this.isEdit) {
            url = this.url.add
            formData = {
              Id: this.model.Id,
              Code: this.model.Code,
              Name: this.model.Name,
              Value: this.model.Value,
              Remarks: this.model.Remarks,
            }
          } else {
            url = this.url.add
            formData = {
              Code: this.model.Code,
              Name: this.model.Name,
              Value: this.model.Value,
              Remarks: this.model.Remarks,
            }
          }
          postAction(url, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
