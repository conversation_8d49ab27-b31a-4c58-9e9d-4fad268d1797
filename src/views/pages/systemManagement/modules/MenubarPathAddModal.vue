<!-- 人员管理新增 -->
<template>
  <a-modal :title="title" :width="800" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff'
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <p>上级菜单：{{ ParentName || '无' }}</p>
              <a-form-model-item label="菜单类型：" prop="Type">
                <a-radio-group v-model="model.Type" @change="onTypeChange">
                  <a-radio :value="1">
                    文件夹
                  </a-radio>
                  <a-radio :value="2">
                    页面
                  </a-radio>
                  <a-radio :value="3">
                    按钮
                  </a-radio>
                </a-radio-group>
              </a-form-model-item>
              <a-form-model-item label="菜单名称：" prop="Name">
                <a-input placeholder="请输入" v-model="model.Name" style="width: 100%" :maxLength="100" />
              </a-form-model-item>
              <a-form-model-item label="菜单路径：(没有路径填写/)" prop="Path">
                <a-input placeholder="请输入" v-model="model.Path" style="width: 100%" :maxLength="100" />
              </a-form-model-item>
              <a-form-model-item label="菜单图标：(图库地址：http://fontawesome.dashgame.com/，不展示图标填写none)" prop="Icon">
                <a-input placeholder="选填" v-model="model.Icon" style="width: 100%" :maxLength="100" />
              </a-form-model-item>
              <a-form-model-item label="序号：(填写99为不展示)">
                <a-input placeholder="选填" v-model="model.Sort" style="width: 100%" :maxLength="100" />
              </a-form-model-item>
              <a-form-model-item label="是否校验数据权限：" v-if="model.Type == 2" prop="IsVerifyData">
                <a-radio-group v-model="model.IsVerifyData">
                  <a-radio :value="1">
                    是
                  </a-radio>
                  <a-radio :value="2">
                    否
                  </a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'MenubarPathAddModal',
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      title: '新增菜单',
      visible: false,
      isEdit: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 }
      },
      ParentId: null,
      ParentName: null,
      confirmLoading: false,
      httpHead: 'P36001',
      url: {
        add: '/{v}/UserPermission/Menu/Create',
        update: '/{v}/UserPermission/Menu/Update'
      }
    }
  },
  computed: {
    rules() {
      return {
        Type: [{ required: true, message: '请选择菜单类型!' }],
        Name: [{ required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 20, '菜单名称') }],
        Path: [{ required: true, message: '请输入菜单路径!' }],
        Icon: [{ required: true, message: '请输入菜单图标!' }],
        IsVerifyData: [{ required: true, message: '请选择是否校验数据权限!' }]
      }
    }
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add(record) {
      this.title = '新增菜单'
      this.ParentId = record.Id
      this.ParentName = record.Name
      this.model = {
        Type: '',
        Name: '',
        Type: '',
        Path: '',
        Icon: '',
        IsVerifyData: null,
      }
      this.isEdit = false
      this.visible = true
    },
    edit(record) {
      this.title = '编辑菜单'
      this.ParentId = record.ParentId
      this.ParentName = record.Name
      this.model = Object.assign({}, record)
      if (this.model.IsVerifyData) {
        this.model.IsVerifyData = 1
      } else {
        this.model.IsVerifyData = 2
      }
      this.isEdit = true
      this.visible = true
    },
    onTypeChange(e) {
      switch (this.model.Type) {
        case 2:
          this.model.IsVerifyData = 1
          this.model.Path = ''
          this.model.Icon = ''
          break
        default:
          this.model.IsVerifyData = 2
          this.model.Path = '/'
          this.model.Icon = 'none'
          break
      }
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let formData = {}
          let url = ''
          if (this.isEdit) {
            url = this.url.update
            formData = {
              Id: this.model.Id,
              ParentId: this.ParentId,
              Name: this.model.Name,
              Type: this.model.Type,
              Path: this.model.Path,
              Icon: this.model.Icon,
              IsVerifyData: this.model.IsVerifyData == 1 ? true : false,
              Sort: this.model.Sort ? this.model.Sort : 1
            }
          } else {
            url = this.url.add
            formData = {
              ParentId: this.ParentId,
              Name: this.model.Name,
              Type: this.model.Type,
              Path: this.model.Path,
              Icon: this.model.Icon,
              IsVerifyData: this.model.IsVerifyData == 1 ? true : false,
              Sort: this.model.Sort ? this.model.Sort : 1
            }
          }
          postAction(url, formData, this.httpHead)
            .then(res => {
              if (res.IsSuccess) {
                that.$message.success('操作成功')
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    }
  }
}
</script>
