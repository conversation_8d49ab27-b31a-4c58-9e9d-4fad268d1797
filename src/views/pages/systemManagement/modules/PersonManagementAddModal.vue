<!-- 人员管理新增 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="姓名：" prop="Name">
                <a-input placeholder="请输入" v-model="model.Name" style="width: 100%" />
              </a-form-model-item>
              <a-form-model-item label="手机号：" :prop="isEdit?'':'Phone'">
                <a-input placeholder="请输入" v-model="model.Phone" :disabled="isEdit?true:false" style="width: 100%" :maxLength="11" />
              </a-form-model-item>
              <a-form-model-item label="人员类型：" prop="TypeList">
                <EnumMultipleChoiceView style="width: 100%" placeholder="请选择" dictCode="EnumUserType" :httpHead="'P36001'" v-model="model.TypeList" />
              </a-form-model-item>
              <a-form-model-item label="所在部门：" prop="DepartmentId">
                <SelectTreeViewModel style="width: 100%" placeholder="请选择" :Url="url.getDepartment" v-model="model.DepartmentId" :httpHead="httpHead" :dataKey="{
                    value: 'Id',
                    label: 'Name',
                    children: 'ChildrenDepartments',
                  }" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel"> 取消 </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'PersonManagementAddModal',
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      title: '新增人员',
      visible: false,
      isEdit: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      model: {
        DepartmentId: '',
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      httpHead: 'P36001',
      url: {
        add: '/{v}/UserPermission/User/Create',
        update: '/{v}/UserPermission/User/Update',
        getMarketAreaList: '/Customer/GetMarketAreaList', //市场区域
        getDepartment: '/{v}/UserPermission/Department/List', //部门
      },
    }
  },
  computed: {
    rules() {
      return {
        Name: [{ required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 20, '姓名') },],
        Phone: [{ required: true, validator: this.checkTelphone }],
        TypeList: [{ required: true, message: '请选择人员类型!' }],
        DepartmentId: [{ required: true, message: '请选择所在部门!' }],
      }
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add() {
      this.title = '新增人员'
      this.model = {}
      this.isEdit = false
      this.visible = true
    },
    edit(record) {
      this.title = '编辑人员'
      this.model = Object.assign({}, record)
      this.model.TypeList = record.TypeList
      this.isEdit = true
      this.visible = true
    },
    changeSingleSelet(val, txt, item) {
      this.model.CallCompanyId = val
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          if (!this.isEdit && (!/^1[23456789]\d{9}$/.test(this.model.Phone))) {
            this.$message.warning('手机号码格式有误，请重新输入')
            return
          }
          that.confirmLoading = true
          if (this.model.TypeList.length > 0) {
            this.model.TypeList = this.model.TypeList.map(item => {
              return Number(item)
            })
          }
          let formData = {}
          let url = ''
          if (this.isEdit) {
            url = this.url.update
            formData = {
              Id: this.model.Id,
              Name: this.model.Name,
              Phone: this.model.Phone,
              TypeList: this.model.TypeList || [],
              DepartmentId: this.model.DepartmentId,
            }
          } else {
            url = this.url.add
            formData = {
              Name: this.model.Name,
              Phone: this.model.Phone,
              TypeList: this.model.TypeList || [],
              DepartmentId: this.model.DepartmentId,
            }
          }
          postAction(url, formData, this.httpHead)
            .then((res) => {
              if (res.IsSuccess) {
                that.$message.success('操作成功')
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    changeTreeId(selectItem, key) {
      this.model[key] = selectItem.value
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>
