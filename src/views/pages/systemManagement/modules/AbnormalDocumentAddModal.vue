<!-- 新增异常单据处理 -->
<template>
  <a-modal :title="title" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="单据类型：" prop="BusinessOrderType">
                <a-select style="width: 100%" v-model="model.BusinessOrderType" @change="changeOrderType" placeholder="请选择">
                  <a-select-option value="">请选择</a-select-option>
                  <a-select-option :value="1">采购订单</a-select-option>
                  <a-select-option :value="15">采购发票</a-select-option>
                  <a-select-option :value="9">采购票折</a-select-option>
                  <a-select-option :value="6">采购调价</a-select-option>
                  <a-select-option :value="4">采购退货</a-select-option>
                  <a-select-option :value="23">商销调价</a-select-option>
                  <a-select-option :value="22">商销售后</a-select-option>
                  <a-select-option :value="10">销货对抵</a-select-option>
                  <a-select-option :value="60">打款</a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="单据编号：" prop="BusinessOrderId">
                <SingleInputSearchView placeholder="请选择" :key="inputKey" width="100%" :httpParams="{
                        PageIndex: 1,
                        PageSize: 100,
                        BusinessOrderType:model.BusinessOrderType,
                        IsValid: true,
                      }" keyWord="Code" httpHead="P36015" Url="/{v}/AbnormalBusiness/GetAbnormalBusinessOrder" :dataKey="{name:'BusinessOrderCode',value:'BusinessOrderId',nameList: []}" :value="model.BusinessOrderId" :name="model.BusinessOrderCode" :disabled="model.BusinessOrderType?false:true" :isAuto="isAuto" :isAbsolute="true" @clear="
                        () => {
                          model.BusinessOrderId = ''
                          model.BusinessOrderCode = ''
                        }
                      " @change="changeOrder" />
              </a-form-model-item>
              <a-form-model-item label="原因：">
                <a-textarea placeholder="请输入" v-model="model.Reason" :rows="4" :maxLength="200" />
              </a-form-model-item>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">
        取消
      </a-button>
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">提交</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "AbnormalDocumentAddModal",
  components: {},
  data() {
    return {
      title: "新增异常单据处理",
      visible: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      inputKey: 0,
      confirmLoading: false,
      isAuto: false,
      httpHead: 'P36015',
      url: {
        add: "/{v}/AbnormalBusiness/Submit",
      },
    };
  },
  computed: {
    rules() {
      return {
        BusinessOrderType: [{ required: true, message: "请选择单据类型!" }],
        BusinessOrderId: [{ required: true, message: "请选择单据编号!" }],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add() {
      this.title = '新增异常单据处理'
      this.model = {}
      this.inputKey = 0;
      this.isAuto = false;
      this.visible = true;
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          let formData = {}
          let url = ''
          url = this.url.add
          formData = {
            BusinessOrderType: this.model.BusinessOrderType,
            BusinessOrderId: this.model.BusinessOrderId,
            Reason: this.model.Reason,
          }
          postAction(url, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    changeOrderType() {
      if (!this.model.BusinessOrderType) {
        return
      }
      this.inputKey++
      this.isAuto = true
      this.$set(this.model, 'BusinessOrderId','')
      this.$set(this.model, 'BusinessOrderCode', '')
    },
    changeOrder(val, name, item) {
      this.model.BusinessOrderId = val
      this.model.BusinessOrderCode = name
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
