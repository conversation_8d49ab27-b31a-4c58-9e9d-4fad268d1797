<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab" />
    <!-- 新增 -->
    <ApprovalProcessAddModal ref="ApprovalProcessAddModal" @ok="modalOk"></ApprovalProcessAddModal>
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'supplierFlowPoolList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/编号/拼音码',
          type: SEnum.INPUT,
          key: 'SupplierKey'
        },
        {
          name: '业务员',
          placeholder: '姓名/手机号',
          type: SEnum.INPUT,
          key: 'UserKey'
        }
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          // { name: '新增', type: 'primary', icon: 'plus', key: 'add', id: '2b60e79c-248b-43b2-93bd-23dad583c5b5', }
        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        rowKey: 'SupplierId'
      },
      columns: [
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '业务员数量',
          dataIndex: 'UserCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          actionBtn: [{ name: '管理业务员', icon: '', id: '2c74e748-8c82-4964-89d3-59cac30d2c10' }],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {},
      isTableInitData: true, //是否自动加载
      linkHttpHead: 'P36001',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/SupplierSystem/QuerySupplierFlowDirections'
      }
    }
  },
  created() { },
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '管理业务员') {
        this.onDetailClick('supplierManageSalesList', { Id: record.SupplierId, name: record.SupplierName })
      }
    }
  }
}
</script>

<style></style>
