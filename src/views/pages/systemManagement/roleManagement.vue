<template>
  <a-row :gutter="24">
    <a-col :md="5">
      <a-card title="角色" :bodyStyle="{ padding: '0px' }" class="label-group-list">
        <a-button slot="extra" style="width: auto;height: auto;" type="link" icon="plus" @click="addModal"></a-button>
        <a-list item-layout="horizontal" :data-source="groupList">
          <a-list-item slot="renderItem" slot-scope="item, index" :class="{ 'item-divs': curIndex == index }" @click.stop="onItemClick(item, index)">
            <div class="item-div">{{item.Name}}</div>
            <div class="item-icon" v-if="item.Type !== 1">
              <a-icon type="edit" style="margin-right:10px;color:#1890ff;" @click="updateModal(item)" />
              <a-icon type="delete" style="color:#1890ff;" @click="delModal(item)" />
            </div>
          </a-list-item>
        </a-list>
      </a-card>
    </a-col>
    <a-col :span="19">
      <a-card>
        <div class="fun-box">
          <div>授权人员</div>
          <a @click="addAuthPerson">新增授权人员</a>
        </div>
        <a-table :bordered="false" ref="table" rowKey="Id" size="small" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: '400px', x: false }">
          <!-- 字符串超长截取省略号显示-->
          <span slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </span>
          <!-- 操作 -->
          <span slot="action" slot-scope="text, record">
            <a @click="removeRole(record)"> 移除 </a>
          </span>
        </a-table>
        <div class="line"></div>
        <div class="fun-box">
          <div>功能与数据权限</div>
          <a @click="updateAuth">修改权限</a>
        </div>
        <div class="power">权限范围：{{permissionName || '--'}}</div>
        <a-table :bordered="false" v-if="permissionName != '全部功能和数据'" ref="table" rowKey="Id" size="small" :columns="columns2" :dataSource="dataSource2" :expanded-row-keys.sync="expandedRowKeys" :pagination="ipagination2" :loading="loading2" @change="handleTableChange" :scroll="{ y: '400px', x: false }">
          <!-- 字符串超长截取省略号显示-->
          <span slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </span>
        </a-table>
      </a-card>
    </a-col>
    <!-- 新增角色 -->
    <RoleManagementAddModal ref="RoleManagementAddModal" @ok="modalOk"></RoleManagementAddModal>
    <!-- 表格选择数据弹窗 -->
    <TableSelectDataModal ref="TableSelectDataModal" :selectTableData="selectTableData" @chooseData="chooseData"></TableSelectDataModal>
    <!-- 修改权限 -->
    <RoleManagementUpdateAuthModal ref="RoleManagementUpdateAuthModal" @ok="modalOk"></RoleManagementUpdateAuthModal>
  </a-row>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
const columns1 = [
  {
    title: '姓名',
    dataIndex: 'Name',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '手机号',
    dataIndex: 'Phone',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '人员类型',
    dataIndex: 'TypeListStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '所在部门',
    dataIndex: 'DepartmentName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 100,
    // fixed: "right",
    scopedSlots: { customRender: 'action' }
  }

]
const columns2 = [
  {
    title: '菜单名称',
    dataIndex: 'Name',
    width: 300,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '菜单类型',
    dataIndex: 'TypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '数据范围',
    dataIndex: 'MenuDataPermissionStr',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },

]

export default {
  name: 'roleManagement',
  mixins: [ListMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      columns: columns1,
      columns2: columns2,
      dataSource: [],
      dataSource2: [],
      groupList: [],
      curIndex: 0,
      curGroup: null,
      loading2: false,
      queryParam: {},
      permissionName: '',
      ipagination2: {
        current: 1,
        pageSize: 15,
        pageSizeOptions: ['15', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      selectTableData: {
        searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '姓名', type: 'input', value: '', key: 'Name', defaultVal: '', placeholder: '请输入' },
          { name: '手机号', type: 'input', value: '', key: 'Phone', defaultVal: '', placeholder: '请输入' },
          {
            name: '所在部门', type: 'selectTreeViewLink', vModel: 'DepartmentId', defaultVal: '', disabled: false, placeholder: '请选择', dataKey: {
              value: 'Id',
              label: 'Name',
              selectableKey: 'Id',
              children: 'ChildrenDepartments'
            }, url: '/{v}/UserPermission/Department/List', httpHead: 'P36001'
          },
        ],
        title: '新增角色授权人员',
        name: '角色授权人员',
        recordKey: 'Id',
        httpHead: 'P36001',
        isInitData: false,
        url: {
          list: '/{v}/UserPermission/User/ListByAddRoleUser',
        },
        columns: [
          {
            title: '姓名',
            dataIndex: 'Name',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '手机号',
            dataIndex: 'Phone',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '人员类型',
            dataIndex: 'TypeListStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '所在部门',
            dataIndex: 'DepartmentName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: "right",
            scopedSlots: { customRender: 'action' },
          }
        ]
      },
      expandedRowKeys: [],//展开项
      isInitData: false, //是否自动加载
      httpHead: 'P36001',
      url: {
        list: '/{v}/UserPermission/Role/RoleUserList',
        list2: '/{v}/UserPermission/Role/RoleMenuList',
        roleList: '/{v}/UserPermission/Role/List',
        del: '/{v}/UserPermission/Role/Delete',
        addRoleUser: '/{v}/UserPermission/Role/AddRoleUser',
        removeRoleUser: '/{v}/UserPermission/Role/RemoveRoleUser',
        rolePermission: '/{v}/UserPermission/Role/RoleMenuPermissionRange',
      }
    }
  },
  // created() {
  //   this.getList()
  // },
  activated() {
    this.getList()
  },
  methods: {
    getList() {
      this.loadGroupsData()
    },
    // 获取标签组数据
    loadGroupsData() {
      this.loading = true
      getAction(this.url.roleList, {}, 'P36001')
        .then(res => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.groupList = res.Data
              this.curGroup = res.Data[this.curIndex]
              this.queryParam.RoleId = this.curGroup.Id
              this.loadData(1)
              this.getPowerList(1)
              this.getRolePermission()
            } else {
              this.groupList = []
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(res => {
          this.loading = false
        })
    },
    // 新增角色
    addModal() {
      this.$refs.RoleManagementAddModal.add()
    },
    // 编辑角色
    updateModal(item) {
      this.$refs.RoleManagementAddModal.edit(item)
    },
    // 新增授权人员
    addAuthPerson() {
      let dataSource = this.dataSource || []
      let queryParam = {
        RoleId: this.curGroup.Id,
      }
      this.$refs.TableSelectDataModal.show(dataSource, queryParam)
    },
    // 修改权限
    updateAuth() {
      if (this.curGroup.Type == 1) {
        this.$message.warning('管理员不能修改权限')
        return
      }
      this.$refs.RoleManagementUpdateAuthModal.show(this.curGroup.Id)
    },
    // 确定移除
    subRemoveRole(record) {
      let params = {
        RoleId: this.curGroup.Id,
        UserId: record.Id,
      }
      let url = this.url.removeRoleUser
      postAction(url, params, this.httpHead)
        .then(res => {
          if (res.Data) {
            this.$message.success('移除成功')
            this.getList()
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(err => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    // 移除人员
    removeRole(record) {
      let that = this
      this.$confirm({
        title: '确定要移除该角色对该人员的授权？',
        content: '',
        onOk() {
          that.subRemoveRole(record)
        },
        onCancel() { }
      })
    },
    // 选择的数据
    chooseData(data) {
      if (data && data.length > 0) {
        data = data.map((item) => {
          return item.Id
        })
      } else {
        this.$message.warning('请选择授权人员')
        return
      }
      let params = {
        RoleId: this.curGroup.Id,
        UserIds: data || [],
      }
      postAction(this.url.addRoleUser, params, this.httpHead)
        .then(res => {
          if (res.Data) {
            this.$message.success('操作成功')
            this.getList()
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(err => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    // 确认删除
    subDelInfo(item) {
      let params = {}
      let url = this.url.del + '?id=' + item.Id
      postAction(url, params, this.httpHead)
        .then(res => {
          if (res.Data) {
            this.$message.success('删除成功')
            this.getList()
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(err => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    // 删除角色
    delModal(item) {
      if (item.Type == 1) {
        this.$message.warning('管理员不能删除')
        return
      }
      let that = this
      this.$confirm({
        title: '确定删除该角色，并移除人员对该角色的授权？',
        content: '',
        onOk() {
          that.subDelInfo(item)
        },
        onCancel() { }
      })
    },
    onItemClick(item, index) {
      this.loading = true
      this.curIndex = index
      this.curGroup = item
      this.$nextTick(() => {
        this.getList()
      })
    },
    // 获取权限范围
    getRolePermission() {
      let params = {
        roleId: this.curGroup.Id,
      }
      getAction(this.url.rolePermission, params, this.httpHead).then(res => {
        if (res.IsSuccess) {
          this.permissionName = res.Data
        }
      })
    },
    // 获取权限列表
    getPowerList(arg) {
      if (arg === 1) {
        this.ipagination2.current = 1
      }

      this.loading2 = true
      var params = this.getQueryParams() //查询条件
      getAction(this.url.list2, params, this.httpHead).then(res => {
        if (res.IsSuccess) {
          let source = res.Data ? res.Data : []
          source = this.handleLoadedData(source)
          if (typeof source === Array) {
            source.forEach(item => {
              item.loading = false
            })
          }

          // 默认展开第一个
          if (source.length > 0) {
            this.expandedRowKeys = [source[0].Id]
          } else {
            this.expandedRowKeys = []
          }
          this.dataSource2 = this.menuDataFilter(source, false)
          this.ipagination2.total = res.Count ? res.Count : 0
        } else {
          this.$message.warning(res.Msg)
          this.dataSource2 = []
          this.ipagination2.total = 0
        }
        this.loading2 = false
      })
    },
    // 过滤没有选中的数据
    menuDataFilter(arr, value) {
      return arr.filter(item => {
        if (item.IsCheck === value) {
          return false
        }
        if (item.children && item.children.length > 0) {
          item.children = this.menuDataFilter(item.children, value)
        }
        return true
      })
    },
    modalOk() {
      this.getList()
    },
  }
}
</script>

<style lang="less" scoped>
.item-div {
  width: 100%;
  padding-left: 16px;
}
.item-icon {
  display: flex;
  align-items: center;
  padding-right: 16px;
}
.item-divs {
  background-color: #e6f7ff;
  color: #1890ff;
  border-right: 2px solid #1890ff;
}
/deep/.ant-card-head {
  padding: 0 16px;
}
/deep/.label-group-list {
  .ant-list-item:hover {
    cursor: pointer;
  }
  .ant-card-body {
    height: calc(100vh - 270px);
    overflow-y: auto;
  }
}
.line {
  width: 100%;
  height: 1px;
  margin: 10px 0;
  border: none;
  border-bottom: 1px dashed #cbc3c3;
}
.fun-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  /* margin: 0 10px 10px 10px; */
}
.fun-box div {
  color: #333333;
  font-weight: 600;
}
.power {
  margin-bottom: 10px;
}
</style>