<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :onSetValidLoadData="onSetValidLoadData" :isTableInitData="isTableInitData" @onSetValid="(item, chceked) => onSetValid(item, chceked, 'GET', 'id')" @operate="operate" @changeTab="changeTab">
      <!-- 字符串超长截取省略号显示-->
      <span slot="componentTime" slot-scope="{text, record}">
        <j-ellipsis :value="text && !record.IsValid ? '' + text : '--'" :length="100" />
      </span>
    </SimpleTable>
    <!-- 新增 -->
    <PersonManagementAddModal ref="PersonManagementAddModal" @ok="modalOk"></PersonManagementAddModal>
    <!-- 查看权限 -->
    <PersonManagementPowerModal ref="PersonManagementPowerModal"></PersonManagementPowerModal>
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'personManagementList',
  components: { JEllipsis },
  mixins: [SimpleMixin],
  data() {
    return {
      onSetValidLoadData: true,
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '姓名', type: 'input', value: '', key: 'Name', defaultVal: '', placeholder: '请输入' },
        { name: '手机号', type: 'input', value: '', key: 'Phone', defaultVal: '', placeholder: '请输入' },
        {
          name: '人员类型',
          type: 'enumMultipleChoiceView',
          vModel: 'Types',
          vModelStr: true,
          dictCode: 'EnumUserType',
          placeholder: '请选择',
          maxTagCount: 2,
        },
        {
          name: '所在部门',
          type: 'selectTreeViewLink',
          vModel: 'DepartmentId',
          defaultVal: '',
          disabled: false,
          placeholder: '请选择',
          dataKey: {
            value: 'Id',
            label: 'Name',
            selectableKey: 'Id',
            children: 'ChildrenDepartments',
          },
          url: '/{v}/UserPermission/Department/List',
          httpHead: 'P36001',
        },
        {
          name: '同步状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '授权角色',
          type: 'selectLink',
          vModel: 'RoleId',
          defaultVal: '',
          placeholder: '请选择',
          url: '/{v}/UserPermission/Role/List',
          httpHead: 'P36001',
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '导出', type: '', icon: 'download', key: '导出', id: '63f138dc-13da-418a-874d-622d417942f9' },
          { name: '新增', type: 'primary', icon: 'plus', key: 'add', id: 'c97ff403-3de0-4bd3-9d46-b13c2dc8c4e0' },
        ],
        hintArray: [],
        sortArray: ['componentTime'],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '姓名',
          dataIndex: 'Name',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '手机号',
          dataIndex: 'Phone',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '人员类型',
          dataIndex: 'TypeListStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'purchaseStatus' },
        },
        {
          title: '所在部门',
          dataIndex: 'DepartmentName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '授权角色',
          dataIndex: 'RoleNames',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '权限范围',
          dataIndex: 'Permission',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '禁用/启用',
          dataIndex: 'InquiryNo',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'IsValid' },
        },
        {
          title: '禁用时间',
          dataIndex: 'LastNotValidTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'componentTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          fixed: 'right',
          actionBtn: [
            { name: '查看权限', icon: '', id: 'b09e3265-bcf7-4ae7-9d33-5d47364c37ba' },
            { name: '重置密码', icon: '', id: '06894262-8c21-4ccc-badf-68a967f7297b' },
            { name: '编辑', icon: '', id: '4589db7c-c7d8-420c-84fe-e108d915bacb' },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: true, //是否自动加载
      linkHttpHead: 'P36001',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/UserPermission/User/List',
        sub: '/PurchaseOrder/SubmitAsync',
        del: '/PurchaseOrder/DeleteAsync',
        isValidUrl: '/{v}/UserPermission/User/SetIsValid', //若有是否有效需要写这个url
        resetPassword: '/{v}/UserPermission/User/ResetPassword',
        exportUrl: '/{v}/UserPermission/User/Export'
      },
    }
  },
  created() { },
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    resetPassword(record) {
      let params = {}
      let url = this.linkUrl.resetPassword + '?id=' + record.Id
      getAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('密码重置成功')
            this.$refs.table.loadDatas(1, this.queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        this.$refs.PersonManagementAddModal.add()
      } else if (type == '查看权限') {
        this.$refs.PersonManagementPowerModal.show(record)
      } else if (type == '重置密码') {
        let that = this
        this.$confirm({
          title: '您确定重置当前业务员账号密码为初始密码吗(手机号不变)？',
          content: '',
          onOk() {
            that.resetPassword(record)
          },
          onCancel() { },
        })
      } else if (type == '编辑') {
        this.$refs.PersonManagementAddModal.edit(record)
      } else if (type == '导出') {
        this.handleExportXls(
          '人员管理',
          'Get',
          this.linkUrl.exportUrl,
          null,
          this.linkHttpHead,
          this.$refs.SimpleSearchArea.queryParam
        )
      }
    },
  },
}
</script>

<style></style>
