<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="(item, chceked) => onSetValid({ Id: item.UserId }, chceked, 'GET', 'userId')"
      @operate="operate"
      @changeTab="changeTab"
    />
    <!-- 新增 -->
    <SupplierManageSalesAddModel ref="SupplierManageSalesAddModel" @ok="modalOk"></SupplierManageSalesAddModel>
    <!-- 管理授权品种 -->
    <SupplierManageGoodsListModel ref="SupplierManageGoodsListModel" @ok="modalOk"></SupplierManageGoodsListModel>
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'supplierManageSalesList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchItems: [
        {
          name: '业务员',
          placeholder: '姓名/手机号',
          type: SEnum.INPUT,
          key: 'UserKey',
        },
        {
          name: '商品',
          placeholder: '名称/编号/拼音码',
          type: SEnum.INPUT,
          key: 'GoodsKey',
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增', type: 'primary', icon: '', key: 'add', id: '6a1633bf-26a9-4eda-944f-4785524f4a6c' },
        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '姓名',
          dataIndex: 'UserName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '手机号',
          dataIndex: 'UserPhone',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '授权品种数',
          dataIndex: 'Count',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '禁用/启用',
          dataIndex: 'IsValid',
          width: 150,
          ellipsis: true,
          id: 'e9cf45bf-52b9-485a-93a9-64a8c8d9e938',
          scopedSlots: { customRender: 'IsActivity' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          actionBtn: [
            { name: '管理授权品种', icon: '', id: 'ff2e5f7e-85ad-4e63-92d1-c55092cf35a2' },
            { name: '重置密码', icon: '', id: '97196177-78b1-46de-8d0a-426f7d4af4ba' },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {
        SupplierId: '',
      },
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36001',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/SupplierSystem/QuerySupplierUsers',
        isValidUrl: '/{v}/SupplierSystem/SetUserIsValid', //若有是否有效需要写这个url
        resetPassword: '/{v}/SupplierSystem/ResetUserPassword',
      },
    }
  },
  created() {},
  mounted() {
    if (this.$route.query) {
      this.queryParam.SupplierId = this.$route.query.Id || ''
      this.$refs.table.loadDatas(1, this.queryParam)
    }
  },
  methods: {
    searchQuery() {
      if (this.$refs.SimpleSearchArea) {
        this.queryParam = this.$refs.SimpleSearchArea.queryParam
      }
      this.queryParam.SupplierId = this.$route.query.Id || ''
      this.$nextTick(() => {
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    resetPassword(record) {
      let params = {}
      let url = this.linkUrl.resetPassword + '?userId=' + record.UserId
      getAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('密码重置成功')
            this.$refs.table.loadDatas(1, this.queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => {})
    },
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        this.$refs.SupplierManageSalesAddModel.show({
          SupplierId: this.queryParam.SupplierId,
          SupplierName: this.$route.query.name || '',
        })
      } else if (type == '管理授权品种') {
        this.$refs.SupplierManageGoodsListModel.show(record)
      } else if (type == '重置密码') {
        let that = this
        this.$confirm({
          title: '您确定重置当前业务员账号密码为初始密码吗(手机号不变)？',
          content: '',
          onOk() {
            that.resetPassword(record)
          },
          onCancel() {},
        })
      }
    },
    modalOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
  },
}
</script>

<style></style>
