<!-- 商品列表 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :tableInfo="tableInfo"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="(item, chceked) => onSetValid(item, chceked, 'POST', 'GoodsSpuId')"
      @operate="operate"
      @changeTab="changeTab"
      @selectedRows="
              (selectedRowKeys, selectedRows) => handleSelectedRows(selectedRowKeys, selectedRows, 'table')
            "
    >
      <!-- <div slot="operateBtnAfter" style="float: right">
        <a-popover>
          <template slot="content">
            <p @click="operate('', 'exportProduct')">导出商品信息<br/>（列表中的商品）</p>
            <p @click="operate('', 'export')">导出审批表<br/>（勾选的商品）</p>
          </template>
          <YYLButton
            menuId="2f773f3b-e88a-4aef-9632-3eedd6836427"
            text="导出"
            type="primary"
            icon="plus"
          />
        </a-popover>
      </div> -->
    </SimpleTable>

    <WithdrawModal ref="WithdrawModal" @ok="handleOk" />

    <a-modal
      title="导出中"
      :visible="visible"
      @cancel="() => visible = false"
      :footer="null"
      strokeColor="#108ee9"
      :strokeWidth="10"
    >
      <div style="text-align: center">
        <a-progress type="circle" :percent="percent" />
        <p style="margin-top: 10px">正在导出，请勿关闭弹窗，否则将中止操作</p>
      </div>
    </a-modal>
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction, deleteAction } from '@/api/manage'

export default {
  description: '商品列表',
  name: 'commodityArchivesList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '商品',
          type: 'input',
          value: '',
          key: 'KeyWord',
          defaultVal: '',
          placeholder: '名称/编号/拼音码/SPZ码',
        },
        {
          name: '商品分类',
          type: 'selectLink',
          vModel: 'GoodsManageClassify2',
          dataKey: { name: 'Name', value: 'Id' },
          httpParams: { Name: '', Level: 2 },
          keyWord: 'Name',
          defaultVal: '',
          placeholder: '请选择',
          httpType: 'POST',
          url: '/{v}/GoodsManage/QueryGoodsManageClassifyList',
          httpHead: 'P36006',
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '首营状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuthBusinessStatus',
          dictCode: 'EnumAuthBusiness',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '同步状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        // {
        //   name: '生产厂家',
        //   type: 'input',
        //   value: '',
        //   key: 'BrandManufacturer',
        //   defaultVal: '',
        //   placeholder: '请输入生产厂家名称'
        // },
        {
          name: '生产厂家',
          type: 'selectLink',
          vModel: 'BrandManufacturerId',
          dataKey: { name: 'Name', value: 'Id' },
          httpParams: { KeyWord: '', IsValid: true },
          keyWord: 'KeyWord',
          defaultVal: '',
          placeholder: '请选择',
          httpType: 'GET',
          url: '/{v}/BrandManufacturer/QueryBrandManufacturerList',
          httpHead: 'P36006',
        },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        { name: '责任人', type: 'input', value: '', key: 'PurchaserName', defaultVal: '', placeholder: '请输入' },
        { name: '来货单位', type: 'input', value: '', key: 'SupplierName', defaultVal: '', placeholder: '请输入' },
        { name: '上市许可持有人', type: 'input', value: '', key: 'LicensedHolder', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增', type: 'primary', icon: 'plus', key: 'add', id: '7d826a3a-9679-447d-ae6a-a177a2939b19' },
          { name: '导出商品信息', type: 'primary', icon: 'plus', key: 'exportProduct', id: '2f773f3b-e88a-4aef-9632-3eedd6836427' },
          { name: '导出审批表', type: 'primary', icon: 'plus', key: 'export', id: '9b007fbd-c72c-4811-8ed0-060edbbe6ed4' }
        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        rowKey: 'Id',
        rowSelection: {
          type: 'checkbox',
        },
      },
      tableInfo: {
        scrollX: '2200px',
        actionIndex: null, //操作项的位置
        size: 'middle',
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: 'SPZ码',
          dataIndex: 'ERPGoodsId',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品分类',
          dataIndex: 'ManageClassifyStr2',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '来货单位',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '上市许可持有人',
          dataIndex: 'LicensedHolder',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '首营状态',
          dataIndex: 'AuthBusinessStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'PurchaserName',
          // width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否活动',
          dataIndex: 'IsValid',
          width: 100,
          ellipsis: true,
          fixed: 'right',
          id: '9839bee7-7875-4167-911d-49c79d2be5cd',
          scopedSlots: { customRender: 'IsActivity' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 210,
          fixed: 'right',
          actionBtn: [
            {
              name: '采购编辑',
              icon: '',
              id: '8773f477-ed3f-41c5-86e3-a5e64db958ae',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.AuditStatus != 1
              },
            },
            {
              name: '质管编辑',
              icon: '',
              id: '0219a0e4-1879-42cb-95fe-d238850eaf1e',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.AuthBusinessStatus != 1 && record.AuditStatus != 1
              },
            },
            {
              name: '删除',
              icon: '',
              id: '29161e65-7b61-4b21-92de-f081d43d2437',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.AuthBusinessStatus == 1 && (record.AuditStatus == 0 || record.AuditStatus == 3)
              },
            },
            {
              name: '详情',
              icon: '',
              id: '683d4e20-71f7-4aaa-9080-c5c42170e998',
              specialShowFuc: (e) => {
                let record = e || {}
                return true
              },
            },
            {
              name: '首营品种审批表',
              icon: '',
              id: 'e9f3f438-c57b-40be-946a-3bcf8b4cb420',
              specialShowFuc: (e) => {
                let record = e || {}
                // return record.AuthBusinessStatus === 2
                return [1, 2].includes(record.AuditStatus)
                // 1.4.1需求更改
                // 审核中 审核通过需要显示按钮
              },
            },
            {
              name: '撤回',
              icon: '',
              id: '42b3df25-e044-44e1-bb43-0c437b0566ac',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.AuditStatus == 1
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36006',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '/{v}/GoodsManage/QueryGoodsList',
        isValidUrl: '/{v}/GoodsManage/SetGoodsIsValid',
        del: '/{v}/GoodsManage/DeleteDraftGoodsSpu',
        ceateFile: '/{v}/GoodsManage/GetGoodsFirstOperatingVarietiesApprovalFile',
        exportProductUrl: '/{v}/GoodsManage/ExportGoodsInfo',
        exportUrl: '/{v}/GoodsManage/GetGoodsFirstOperatingVarietiesApprovalFileAll'
      },
      tableSelectedRowsKeys: [],
      tableSelectedRows: [],
      visible: false,
      percent: 0,
      maxCount: 20
    }
  },
  created() {},
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  activated() {
    let queryParam = this.$refs.SimpleSearchArea ? this.$refs.SimpleSearchArea.queryParam : null
    this.$refs.table.loadDatas(null, queryParam)
    this.ExportMaxCount()
  },
  watch: {
    visible: {
      handler(newVal, oldVal) {
        if(newVal) {
          this.PDFEXport()
        }
      },
      immediate: true,//第一次声明了就监听
    }
  },
  methods: {
    searchQuery(queryParam) {
      this.$refs.table.clearSelected()
      if (queryParam) {
        let params = queryParam ? JSON.parse(JSON.stringify(queryParam)) : {}    
        // params['AuditStatus'] = queryParam['AuditStatus'] > 0 ? Number(queryParam['AuditStatus']) : null
        // params['PushERPStatus'] = queryParam['PushERPStatus'] > 0 ? Number(queryParam['PushERPStatus']) : null
        // params['AuthBusinessStatus'] =
        //   queryParam['AuthBusinessStatus'] > 0 ? Number(queryParam['AuthBusinessStatus']) : null
        this.$refs.table.loadDatas(1, params)
      } else {
        this.$refs.table.loadDatas(1)
      }
    },
    // 确认删除
    subDel(item) {
      let params = {}
      let url = this.linkUrl.del + '?goodsSpuId=' + item.Id
      deleteAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('删除成功')
            let queryParam = this.$refs.SimpleSearchArea ? this.$refs.SimpleSearchArea.queryParam : null
            this.$refs.table.loadDatas(1, queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => {})
    },
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        this.onDetailClick('commodityArchivesAdd', { id: record.Id, opType: 1 })
      } else if (type == '采购编辑') {
        this.onDetailClick('commodityArchivesAdd', { id: record.Id, opType: 2 })
      } else if (type == '质管编辑') {
        this.onDetailClick('commodityArchivesDetail', { id: record.Id, acType: 3 })
      } else if (type == '详情') {
        this.onDetailClick('commodityArchivesDetail', { id: record.Id, acType: 4 })
      } else if (type == '首营品种审批表') {
        if (record.GoodsFirstOperatingVarietiesApprovalFile) {
          window.open(record.GoodsFirstOperatingVarietiesApprovalFile)
        } else {
          let that = this
          that.$refs.table.loading = true
          getAction(this.linkUrl.ceateFile, { goodsSpuId: record.Id }, this.linkHttpHead)
            .then((res) => {
              console.log(111, res)
              if (res.IsSuccess) {
                if (res.Data) {
                  window.open(res.Data)
                } else {
                  that.$message.warning('无数据')
                }
              } else {
                that.$message.error(res.Msg)
              }
            })
            .finally(() => {
              that.$refs.table.loading = false
            })
        }
      } else if (type == '删除') {
        let that = this
        this.$confirm({
          title: '您确定要删除该商品吗?',
          content: '',
          onOk() {
            that.subDel(record)
          },
          onCancel() {},
        })
      } else if(type === 'export') {
        if(!this.tableSelectedRowsKeys.length) {
          this.$message.warning('请先勾选商品！')
          return
        } else if (this.tableSelectedRowsKeys.length > this.maxCount) {
          this.$message.warning(`一次性最多只能导出${this.maxCount}条！`)
        } else {
          // 判断是否有审批表
          let isHasAuditForm = false
          if(this.tableSelectedRows.filter(item => item.AuditStatus === 1 || item.AuditStatus === 2).length > 0) {
            isHasAuditForm = true
          }
          if(!isHasAuditForm) {
            this.$message.warning('所选商品没有可下载的审批表！')
          } else {
            this.visible = true
          }
        }
      } else if(type === 'exportProduct') {
        this.handleExportXls(
          '商品明细',
          'Post',
          this.linkUrl.exportProductUrl,
          null,
          this.linkHttpHead,
          {
            ...this.$refs.SimpleSearchArea.queryParam,
            GoodsManageClassify2: this.$refs.SimpleSearchArea.queryParam.GoodsManageClassify2 || null,
            BrandManufacturerId: this.$refs.SimpleSearchArea.queryParam.BrandManufacturerId || null
          }
        )
      } else if (type === '撤回') {
        this.$refs.WithdrawModal.show(record)
      }
    },
    handleOk() {
      let queryParam = this.$refs.SimpleSearchArea ? this.$refs.SimpleSearchArea.queryParam : null
      this.$refs.table.loadDatas(null, queryParam)
    },
    handleSelectedRows(selectedRowKeys, selectedRows, key) {
      console.log('handleSelectedRows', selectedRowKeys, selectedRows, key)
      this.tableSelectedRowsKeys = selectedRowKeys
      this.tableSelectedRows = selectedRows
    },
    PDFEXport() {
      this.percent = 50
      postAction(this.linkUrl.exportUrl, {GoodsIds:this.tableSelectedRowsKeys}, this.linkHttpHead).then((res) => {
        if (res.IsSuccess) {
          this.$message.success('下载成功')
          this.percent = 100
          window.open(res.Data, '_blank')
          this.visible = false
        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
    ExportMaxCount(){
      getAction('/{v}/GoodsManage/MaxExportCount', {} ,this.linkHttpHead).then(res => {
        if (res.IsSuccess) {
          this.maxCount = res.Data
        }
      })
    }
  },
}
</script>

<style></style>
