<template>
  <a-modal
    :title="title"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <a-spin :spinning="confirmLoading">
      <div>
        <!-- 商销 -->
        <div v-if="type == 2">
          <SimpleSearchArea
            ref="SimpleSearchArea1"
            :searchInput="searchInput1"
            :queryCol="12"
            @searchQuery="searchQuery"
          />
          <SimpleTable
            ref="table1"
            :tab="tab"
            :isTableInitData="isTableInitData"
            :queryParam="queryParam1"
            :columns="columns1"
            linkHttpHead="P36002"
            linkUrlType="GET"
            :linkUrl="linkUrl"
            @selectedRows="
              (selectedRowKeys, selectedRows) => handleSelectedRows(selectedRowKeys, selectedRows, 'table1')
            "
          >
            <span slot="commonContent" slot-scope="{ record }">
              <j-ellipsis :value="`${record['RegAreaName'] || ''}${record['RegAddress'] || ''}`" :length="15" />
            </span>
          </SimpleTable>
        </div>
        <!-- 电商 -->
        <div v-if="type == 1">
          <SimpleSearchArea
            ref="SimpleSearchArea"
            :searchInput="searchInput2"
            :queryCol="12"
            @searchQuery="searchQuery"
          />
          <SimpleTable
            ref="table"
            :tab="tab"
            :isTableInitData="isTableInitData"
            :queryParam="queryParam"
            :columns="columns2"
            :linkHttpHead="linkHttpHead"
            :linkUrlType="linkUrlType"
            :linkUrl="linkUrl"
            @selectedRows="
              (selectedRowKeys, selectedRows) => handleSelectedRows(selectedRowKeys, selectedRows, 'table')
            "
          >
            <span slot="commonContent" slot-scope="{ record }">
              <j-ellipsis :value="`${record['AreaFullName'] || ''}${record['DetailAddress'] || ''}`" :length="15" />
            </span>
          </SimpleTable>
        </div>
      </div>
    </a-spin>
    <div slot="footer">
      <div>
        <a-button
          type="primary"
          class="mr5"
          v-if="checkBtnPermissions('6daec0c2-9f32-48a6-91cd-0d0434223bcf')"
          @click="handleOk"
          >保存</a-button
        >
        <a-button @click="handleCancel">取消</a-button>
      </div>
    </div>
  </a-modal>
</template>
<script>
// 药约约版本
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'SelectBannedCustomerListModalNew',
  components: { JEllipsis },
  mixins: [SimpleMixin, EditMixin],
  data() {
    return {
      type: null, //1 电商客户 2 商销客户
      title: '选择禁售客户名单',
      confirmLoading: false,
      visible: false,
      tab: {
        rowKey: 'Id',
        rowSelection: {
          type: 'checkbox',
          getCheckboxProps: this.getCheckboxProps,
        },
      },
      searchInput1: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '客户', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '请输入名称或编号' },
      ],
      searchInput2: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '客户', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '请输入名称或ID' },
      ],
      // 表头
      columns1: [
        {
          title: '客户名称',
          dataIndex: 'Name',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户编号',
          dataIndex: 'Code',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '注册地址',
          dataIndex: 'RegAreaName',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
      ],
      columns2: [
        {
          title: '客户名称',
          dataIndex: 'Name',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户ID',
          dataIndex: 'Id',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '注册地址',
          dataIndex: 'RegAreaName',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
      ],
      queryParam: {
        // FirstCampAuditStatus: 3,
      },
      queryParam1: {},
      tableSelectedRows: [],
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36006', //'Pyyy',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '',
        getCustomerListByLabels: '/{v}/GoodsSaleManage/QueryCustomerList', //电商客户
        getCustomerList: '/{v}/Customer/ListForOrder', // 商销客户
        selectedList: '/{v}/GoodsSaleManage/GetGoodsAlreadyLimitCustomerCodeList', // 已选客户列表
      },
      // 已选择的商品
      selectedList: [],
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(type, data = []) {
      // console.log(`已选客户：`,type, data);
      this.type = type
      this.title = type == 2 ? '选择禁售商销客户名单' : '选择禁售电商客户名单'
      this.tableSelectedRows = []
      this.selectedList = []
      this.visible = true
      this.queryParam = {}
      this.getSelectedList(data)
      this.getList(type)
    },
    // 获取以选择的所有数据
    async getSelectedList(data, callback) {
      if (!data.hasSelected) return
      let res = await getAction(
        this.linkUrl.selectedList,
        {
          ...data.queryParam,
        },
        data.http
      )

      if (res.IsSuccess) {
        this.selectedList = res.Data || []
      }
    },
    // 将已选择的商品禁用，不能勾选
    getCheckboxProps(row) {
      const id = this.type === 2 ? row.Code : row.Id
      return {
        props: {
          disabled: this.selectedList.includes(id),
          defaultChecked: this.selectedList.includes(id),
        },
      }
    },
    getList(type) {
      if (type == 2) {
        this.linkHttpHead = 'P36002'
        this.linkUrlType = 'GET'
        this.linkUrl.list = this.linkUrl.getCustomerList
        this.$nextTick(() => {
          if (this.$refs.table1) this.$refs.table1.loadDatas(1, this.queryParam1)
        })
      } else {
        this.linkHttpHead = 'P36006'
        this.linkUrlType = 'POST'
        this.linkUrl.list = this.linkUrl.getCustomerListByLabels
        this.$nextTick(() => {
          if (this.$refs.table) this.$refs.table.loadDatas(1, this.queryParam)
        })
      }
    },
    searchQuery() {
      if (this.type == 2) {
        this.queryParam1 = this.$refs.SimpleSearchArea1.queryParam
        this.$nextTick(() => {
          if (this.$refs.table1) this.$refs.table1.loadDatas(1, this.queryParam1)
        })
      } else {
        this.queryParam = this.$refs.SimpleSearchArea.queryParam
        this.$nextTick(() => {
          if (this.$refs.table) this.$refs.table.loadDatas(1, this.queryParam)
        })
      }
    },
    handleCancel() {
      this.visible = false
      this.linkUrl.list = ''
      this.tableSelectedRows = []
      this.queryParam = {}
      this.queryParam1 = {}
      if (this.$refs.SimpleSearchArea) this.$refs.SimpleSearchArea.queryParam = {}
      if (this.$refs.SimpleSearchArea1) this.$refs.SimpleSearchArea1.queryParam = {}
    },
    handleSelectedRows(selectedRowKeys, selectedRows, key) {
      this.tableSelectedRows = selectedRows
    },
    handleOk(record) {
      if (this.tableSelectedRows.length == 0) {
        this.$message.warning('请选择客户')
        return
      }
      this.$emit('ok', this.tableSelectedRows, this.type)
      this.handleCancel()
    },
  },
}
</script>
<style scoped></style>
