<!--
 * @Author: LP
 * @Description: 转存资料选择弹窗
 * @Date: 2024-01-10 10:51:46
-->

<template>
  <a-modal
    :title="title"
    :width="'80vw'"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    @ok="hanleOK"
    :footer="false"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <div>
      <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="onSearch" />

      <!-- 列表 -->
      <TableView
        ref="tableView"
        showCardBorder
        :tab="tab"
        :columns="columns"
        :dataList="dataSource"
        @operateClick="onOperateClick"
        @actionClick="onActionClick"
      >
        <template slot="image" slot-scope="{ text, record, index, column }">
          <a v-if="record.GoodsQualificationPictures.length" @click="onLookImageClick(record)">查看</a>
          <span v-else>--</span>
        </template>
      </TableView>
      <ImageListModal ref="imageListModal" title="批件图片" />
    </div>
  </a-modal>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { postAction } from '@/api/manage'
export default {
  name: 'ZhuanChunQualificationModel',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      title: '请选择转存资料',
      visible: false,
      confirmLoading: false,
      searchItems: [
        {
          name: '商品名称',
          type: SEnum.INPUT,
          defaultValue: '',
          key: 'ErpGoodsName',
          isMust: true,
        },
        {
          name: '厂家',
          type: SEnum.INPUT,
          defaultValue: '',
          key: 'BrandManufacturer',
          isMust: true,
        },
        {
          name: '规格',
          type: SEnum.INPUT,
          key: 'PackingSpecification',
        },

        {
          name: '包装单位',
          type: SEnum.INPUT,
          key: 'PackageUnit',
        },
        {
          name: '批准文号',
          type: SEnum.INPUT,
          key: 'ApprovalNumber',
        },
      ],
      tab: {
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        // rowKey: 'QualificationGroupId',
        customSlot: ['image'],
      },
      isInitData: false,
      httpHead: 'P36004',
      goodsInfo: null,
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '厂家',
          dataIndex: 'BrandManufacturer',
          width: 250,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '包装单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批准文号',
          dataIndex: 'ApprovalNumber',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '资料图片',
          dataIndex: 'GoodsQualificationPictures',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'image' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '选择',
            },
          ],
        },
      ],
      url: {
        list: '/{v}/Goods/GoodsQulificationFromCA',
        listType: 'POST',
        uploadFile: '/{v}/Goods/UpLoadFileToOss',
      },
    }
  },
  computed: {},
  created() {},

  methods: {
    onSearch(queryParam, extendParams, searchType) {
      if (!queryParam.ErpGoodsName) {
        this.$message.warning('请输入商品名称')
        return
      }
      if (!queryParam.BrandManufacturer) {
        this.$message.warning('请输入厂家')
        return
      }
      this.searchQuery(queryParam, extendParams, searchType)
    },
    loadBefore() {
      this.queryParam['SupplierName'] = this.goodsInfo ? this.goodsInfo.SupplierName : ''
    },
    onOperateClick(key) {},
    onActionClick(record, key, index) {
      if (key === '选择') {
        let that = this
        that.loading = true
        // 每种类型要分开获取
        const codeIdMap = new Map([
          [23, 'ZCPJ'], // 注册批件/补充批件/再注册批件
          [40, 'JKYPJYBGHTGD'], // 进口药品检验报告或通关单
          [144210, 'YSBHPZPJHXYBS'], // 野生保护品种批件或相应标识
          [26, 'ZLBZ'], // 质量标准
          [29, 'BZBQJSMSSW'], // 包装标签及说明书实物
          [43, 'QT'], // 其它
        ])
        
        // {
        //   ZCPJ: 23,   
        //   JKYPJYBGHTGD: 40, 
        //   YSBHPZPJHXYBS: 144210, 
        //   ZLBZ: 26, 
        //   BZBQJSMSSW: 29, 
        //   QT: 43, 
        // }

        if(!record.GoodsQualificationPictures.length) {
          that.loading = false
          that.$emit('zcOk', {})
          that.close()
        }

        const tempArr = record.GoodsQualificationPictures.map(item => {
          const  key = codeIdMap.get(item.TypeId)
          return {
            id: item.TypeId,
            code: key,
            urls: item.PictureUrls || []
          }
        })

        const promiseAll = tempArr.map(item => postAction(that.url.uploadFile, item.urls, that.httpHead))

        Promise.allSettled(promiseAll).then(res => {
          const successRes = res.map(r => (r.status === 'fulfilled' && r.value.IsSuccess ) ? r.value.Data : [])
          const tempObj = {}
          tempArr.forEach((i, iIndex) => {
            tempObj[i.code] = successRes[iIndex]
          })
          
          that.$message.success('选择成功！')
          that.$emit('zcOk', tempObj || {})
          that.close()
        })
        .finally(() => {
          that.loading = false
        })
        .catch((err) => {
          that.loading = false
        })





        // const urls = record.GoodsQualificationPictures.length ? record.GoodsQualificationPictures.map(item => item.PictureUrls).flat() : []
        // postAction(that.url.uploadFile, urls, that.httpHead)
        //   .then((res) => {
        //     if (res.IsSuccess) {
        //       that.$message.success('选择成功！')
        //       that.$emit('zcOk', res.Data || [])
        //       that.close()
        //     } else {
        //       that.$message.error(res.Msg)
        //     }
        //   })
        //   .finally(() => {
        //     that.loading = false
        //   })
        //   .catch((err) => {
        //     that.loading = false
        //   })
      }
    },

    show(goods) {
      this.goodsInfo = goods || null
      this.visible = true
      if (goods) {
        this.searchItems[0].defaultValue = goods.ErpGoodsName
        this.searchItems[1].defaultValue = goods.BrandManufacturer
      }
      this.$nextTick(() => {
        this.searchQuery()
      })
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
    hanleOK() {
      this.close()
    },
    /**
     * 查看图片
     * @param {*} record
     */
    onLookImageClick(record) {
      const urls = record.GoodsQualificationPictures.map(item => item.PictureUrls).flat()
      this.$refs.imageListModal.show(urls)
    },
  },
}
</script>
<style scoped>

</style>
