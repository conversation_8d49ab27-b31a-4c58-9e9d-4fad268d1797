<!-- 导入搜索置顶品种 modal-->
<template>
  <a-modal
    :title="modalTitle"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <div>
      <template>
        <div style="font-size: 16px; font-weight: 600; margin-bottom: 15px">
          上传文件
          <a :href="downloadUrl" :download="downloadFileName" style="margin-left: 8px">
            <a-button type="primary">下载导入模板</a-button>
          </a>
        </div>

        <div style="margin-bottom: 5px"><span style="color: red">*</span> 导入文档：</div>
        <a-upload-dragger
          name="file"
          :multiple="false"
          :headers="tokenHeader"
          :action="importExcelUrl"
          @change="onUploadChange"
        >
          <p class="ant-upload-drag-icon">
            <a-icon type="plus" />
          </p>
          <p class="ant-upload-hint">{{ tips }}</p>
        </a-upload-dragger>
      </template>
      <div style="font-size: 16px; font-weight: 600; margin: 30px 0 15px 0">
        导入预览 <span style="color: red; font-size: 14px; font-weight: 400">{{ previewTips }}</span>
      </div>
      <!-- 搜索 -->
      <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
      <SimpleTable
        ref="table"
        showTab
        cardBordered
        :tab="tab"
        :queryParam="queryParam"
        :columns="columns"
        :linkHttpHead="HttpHead"
        :linkUrlType="linkUrlType"
        :isTableInitData="isTableInitData"
        :isHandleTableChange="true"
        :linkUrl="{ list: url.list }"
        @operate="operate"
        @simpleTableLoadAfter="simpleTableLoadAfter"
        @changeTab="changeTab"
        @handleTableChange="onTableChange"
      >
        <span slot="commonContent" slot-scope="{ text, record }" @click="onAbnormalClick(record)">
          <a v-if="record[ErrorShowKey]">
            <a-icon type="question-circle" />
          </a>
          <j-ellipsis :value="text" :style="{ color: record[ErrorShowKey] ? 'red' : '' }" />
        </span>
      </SimpleTable>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="saveLoading" @click="handleCancel">取消</a-button>
      <a-button :loading="saveLoading" @click="handleOk" type="primary">保存</a-button>
    </a-row>

    <a-modal
      :visible="errVisibile"
      title="提示"
      :closable="false"
      @close="onKnowClick"
      :maskClosable="true"
      :destroyOnClose="true"
    >
      <div>
        <div style="margin-bottom: 10px">{{ ToastModel.Title }}</div>
        <div v-for="(item, index) in ToastModel.Content" :key="index" style="color: red">
          <span v-if="item">{{ item }};</span>
        </div>
      </div>
      <a-row :style="{ textAlign: 'right' }" slot="footer">
        <a-button @click="onKnowClick" type="primary">知道了</a-button>
      </a-row>
    </a-modal>
  </a-modal>
</template>

<script>
import { putAction, postAction, apiHead, deleteAction, getAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { SimpleMixin } from '@/mixins/SimpleMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')

export default {
  name: 'BatchImportModal',
  mixins: [EditMixin, SimpleMixin, ListMixin],
  components: { JEllipsis },
  props: {
    modalTitle: {
      type: String,
      default: '批量导入',
    },
    tips: {
      type: String,
      default: '点击或将文档拖拽到这里上传',
    },
    previewTips: {
      type: String,
      default: '(重新导入将覆盖本次已导入数据，不可恢复)',
    },
    searchInput: {
      type: Array,
      default: () => [],
    },
    defaultUrl: {
      type: String,
      default: '',
    },
    downloadFileName: {
      type: String,
      default: '导入文档',
    },
    importUrl: {
      // 导入数据接口
      type: String,
      default: '',
    },

    columnsD: {
      // 列
      type: Array,
      default: () => [],
    },
    // 异常信息字段名称
    ErrorShowKey: {
      type: String,
      default: 'ErrorShow',
    },
    HttpHead: {
      type: String,
      default: '',
    },
    linkUrlType: {
      type: String,
      default: '',
    },
    linkUrlDelType: {
      type: String,
      default: '',
    },
    // 删除 参数 key
    TempDataRemoveKey: {
      type: String,
      default: '',
    },
    linkUrlAddType: {
      type: String,
      default: '',
    },
    TempDataUrl: {
      // 查询数据接口
      type: String,
      default: '',
    },
    TempDataType: {
      // 查询数据接口请求方式
      type: String,
      default: 'GET',
    },
    TempDataRemoveUrl: {
      //移除数据接口
      type: String,
      default: '',
    },
    AddUrl: {
      // 保存数据接口
      type: String,
      default: '',
    },
    // 批量移除id
    batchDelId: {
      type: String,
      default: '',
    },
    // 导出id
    exportId: {
      type: String,
      default: '',
    },
    // 列表查询 异常 正常 key
    queryParamStatusKey: {
      type: String,
      default: 'Status',
    },
    // 有异常数据时 保存提示文字
    errorMessage: {
      type: String,
      default: '',
    },
    // 是否默认弹出时候不加载数据
    noLoadData: {
      type: Boolean,
      default: false,
    },
    // loadData的时候需要带过来的参数
    loadDataQuery: {
      type: Array,
      default: () => {
        return null
      },
    },
    // 保存的时候，需要传的参数
    saveDataQuery: {
      type: String,
      default: '',
    },
    // 保存的时候，上传返回的key，保存的时候需要传的值
    saveResDataKey: {
      type: String,
      default: '',
    },
    dataSourceListKey: {
      type: String,
      default: '',
    },
    // 清空临时表的接口
    clearUrl: {
      type: String,
      default: '',
    },
    // 清空临时表的接口方式
    clearUrlDataType: {
      type: String,
      default: '',
    },
    // 保存清空临时表的时候需要传的值
    saveClearKey: {
      type: String,
      default: '',
    },
    // 异常时，列表显示的异常字段，有就传，没有不传
    exceptionField: {
      type: String,
      default: '',
    },
    exportUrl: {
      // 异常数据导出接口
      type: String,
      default: '',
    },
    linkUrlExportType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      columns: [],
      saveLoading: false,
      // 是否上传文件
      isUploadFile: false,
      visible: false,
      errVisibile: false,
      ToastModel: {
        Title: '',
        Content: [],
      },
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '批量移除', type: '', icon: '', key: 'del', id: this.batchDelId }
        ],
        hintArray: [],
        status: 1,
        statusKey: '正常', //标签的切换关键字
        statusList: [
          {
            name: '正常',
            value: '1',
            count: '',
          },
          {
            name: '异常',
            value: '2',
            count: '',
          },
        ],
        rowKey: 'Id',
        rowSelection: {
          type: 'checkbox',
        },
      },
      confirmLoading: false,
      isTableInitData: false,
      importFileName: '', //导入文件名称
      queryParam: {},
      queryCol: 6,
      id: '',
      curTabValue: 1,
      url: {
        list: '',
        listType: this.TempDataType || 'GET',
        list1: this.TempDataUrl || '/GoodsStickyOnTop/QueryGoodsStickyOnTopTempData',
        // count: '/StoreCustomer/ImportCount',
        delete: this.TempDataRemoveUrl || '/GoodsStickyOnTop/RemoveStikyOnTopTmpData',
        add: this.AddUrl || '/GoodsStickyOnTop/CreateGoodsStickyOnTopImportRecord', //最终保存接口
        importUrl: this.importUrl || '/GoodsStickyOnTop/Upload', //导入接口
      },
    }
  },
  computed: {
    importExcelUrl: function () {
      return apiHead(this.HttpHead) + `${this.url.importUrl}`
    },
    downloadUrl() {
      return apiHead('P36100') + `/${this.defaultUrl}.xlsx`
    },
  },
  watch: {
    'tab.status': {
      handler(newValue, oldValue) {
        // console.log(newValue);
        // const cols = JSON.parse(JSON.stringify(this.columnsD))
        if (this.exceptionField && Number(newValue) === 1) {
          this.columns = this.columnsD.filter((item) => item.dataIndex !== this.exceptionField)
        } else {
          this.columns = [...this.columnsD]
        }
        if (Number(newValue) === 2) {
          this.tab.operateBtn = [
            { name: '批量移除', type: '', icon: '', key: 'del', id: this.batchDelId },
            { name: '导出', type: 'primary', icon: '', key: 'export', id: '' }
          ]
        } else {
          this.tab.operateBtn = [{ name: '批量移除', type: '', icon: '', key: 'del', id: this.batchDelId }]
        }
      },
      immediate: true,
    },
  },
  mounted() {},
  created() {},
  methods: {
    importExcelAfter(fileName) {
      this.importFileName = fileName
    },
    /**
     * @param id
     *  */
    show(id, customerSourceType) {
      this.id = id
      this.selectionRows = []
      this.selectedRowKeys = []
      this.url.list = this.url.list1
      this.isUploadFile = false
      this.tab.status = 1
      if (this.exceptionField) {
        this.columns = this.columnsD.filter((item) => item.dataIndex !== this.exceptionField)
      }

      this.tab.statusList = [
        {
          name: '正常',
          value: '1',
          count: '',
        },
        {
          name: '异常',
          value: '2',
          count: '',
        },
      ]

      this.visible = true
      if (this.saveDataQuery) {
        this.url.add = this.AddUrl
      }
      if (this.noLoadData) {
        return
      }
      this.$nextTick(() => {
        this.queryParam = this.$refs.SimpleSearchArea.queryParam
        this.queryParam['CustomerSourceType'] = customerSourceType ? customerSourceType : null
        // this.queryParam[this.queryParamStatusKey] =
        //   this.queryParamStatusKey != 'Status'
        //     ? this.$refs.table.tab.status == 1
        //       ? false
        //       : true
        //     : this.$refs.table.tab.status
        this.queryParam[this.queryParamStatusKey] = this.resetTapQueryField(
          this.$refs.table.tab.status,
          this.queryParamStatusKey
        )
        this.loadData(1, this.queryParam)
      })
    },
    searchQuery() {
      // noLoadData 为true 时，没有上传文件，不能点击
      if (this.noLoadData && !this.isUploadFile) {
        this.$message.warning('请上传文件!')
        return
      }

      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      // this.queryParam[this.queryParamStatusKey] =
      //   this.queryParamStatusKey != 'Status'
      //     ? this.$refs.table.tab.status == 1
      //       ? false
      //       : true
      //     : this.$refs.table.tab.status
      this.queryParam[this.queryParamStatusKey] = this.resetTapQueryField(
        this.$refs.table.tab.status,
        this.queryParamStatusKey
      )

      this.$nextTick(() => {
        this.loadData(1, this.queryParam)
      })
    },
    simpleTableLoadAfter(data) {
      // console.log(333, data);
      this.setTableResData(data)
    },
    setTableResData(data) {
      let that = this
      if (data) {
        let arr = []
        let that = this
        const statusArr = data.StatuCount || data.StatusCount || []
        if (statusArr.length) {
          statusArr.map((v) => {
            arr.push({
              name: v.StatusStr || v.QueryTypeStr,
              value: v.Status || v.QueryType,
              count: v.Count,
            })
          })
        }

        if (data.SuccessCount || data.SuccessCount == 0) {
          arr = [
            {
              name: '正常',
              value: 1,
              count: data.SuccessCount,
            },
            {
              name: '异常',
              value: 2,
              count: data.ErrorCount,
            },
          ]
        }

        that.$refs.table.tab.statusList = arr

        that.$refs.table.dataSource =
          data.ItemList || data.CashReceipList || (this.dataSourceListKey ? data[this.dataSourceListKey] : [])
      } else {
        that.$refs.table.dataSource = []
        that.$refs.table.ipagination.total = 0
      }
    },
    onTableChange() {
      this.ipagination = this.$refs.table.ipagination
      this.loadData()
    },
    changeTab(value) {
      this.curTabValue = value
      this.queryParam = {
        ...this.queryParam,
        ...this.$refs.SimpleSearchArea.queryParam,
      }
      this.queryParam[this.queryParamStatusKey] = this.resetTapQueryField(value, this.queryParamStatusKey)

      // noLoadData 为true 时，没有上传文件，不能点击
      if (this.noLoadData && !this.isUploadFile) {
        this.$message.warning('请上传文件!')
        return
      }

      this.$nextTick(() => {
        this.loadData(1, this.queryParam)
      })
    },
    // 兼容tab选项字段
    resetTapQueryField(val, field = null) {
      const fieldMap = {
        Status: val,
        QueryType: val || 1,
      }
      return fieldMap[field] ? fieldMap[field] : val == 1 ? false : true
    },
    loadData(arg, queryParam) {
      let that = this
      if (!that.$refs.table) {
        return
      }
      if (arg === 1 && that.$refs.table.ipagination) {
        that.$refs.table.ipagination.current = 1
      }
      if (that.$refs.table.loading || !that.$refs.table.linkUrl.list) {
        return
      }

      that.$refs.table.loading = true
      let params = JSON.parse(JSON.stringify(this.getQueryParams())) //查询条件
      params.Status = this.curTabValue || 1
      params.PageIndex = that.$refs.table.ipagination.current
      if (params['ReceiptType']) params['ReceiptType'] = Number(params['ReceiptType'])
      let obj
      if (this.url.listType == 'GET') {
        obj = getAction(that.$refs.table.linkUrl.list, params, this.HttpHead)
      } else {
        obj = postAction(that.$refs.table.linkUrl.list, params, this.HttpHead)
      }
      obj.then((res) => {
        if (res.IsSuccess) {
          this.setTableResData(res.Data)
          that.$refs.table.ipagination.total = res.Count
          that.$refs.table.loading = false
        } else {
          that.$refs.table.loading = false
          that.$message.warning(res.Msg)
        }
      })
    },

    // 列表操作
    operate(record, type) {
      if (type == 'del') {
        this.onMoreDeleteClick()
      } else if (type == '移除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认移除？',
          onOk() {
            that.onRemoveGoodsClick(record)
          },
          onCancel() {},
        })
      } else if (type == 'export') {
        if (this.$refs.table && this.$refs.table.dataSource.length < 1) {
          this.$message.warning('请先导入数据!')
          return
        }
        this.handleExportXls(
          `${this.modalTitle}异常数据`,
          this.linkUrlExportType,
          this.exportUrl,
          null,
          this.HttpHead,
          {}
        )
      }
    },

    // 确定
    handleOk() {
      let that = this
      if (that.$refs.table && that.$refs.table.tab.statusList[1].count > 0) {
        const text = this.errorMessage || '导入的文档存在异常，请处理后再保存！'
        that.$message.error(text)
      } else {
        if (that.$refs.table && that.$refs.table.tab.statusList[0].count > 0) {
          if (this.AddUrl) {
            that.saveData()
          } else {
            that.$emit('ok', that.$refs.table.dataSource)
            that.close()
          }
        } else {
          that.$message.warning('请选择导入的数据！')
        }
      }
    },
    saveData() {
      let params = {}
      let obj = {}
      if (this.linkUrlAddType && this.linkUrlAddType == 'POST') {
        obj = postAction(this.url.add, params, this.HttpHead)
      } else if (this.linkUrlAddType && this.linkUrlAddType == 'GET') {
        obj = getAction(this.url.add, params, this.HttpHead)
      } else {
        obj = putAction(this.url.add, params, this.HttpHead)
      }
      this.saveLoading = true
      obj
        .then((res) => {
          if (res.IsSuccess) {
            this.$message.success('操作成功')
            let data = []
            if (typeof res.Data == 'boolean') {
              data = this.$refs.table.dataSource
            } else {
              data = res.Data
            }
            this.$emit('ok', data)
            this.close()
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.saveLoading = false
        })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.importFileName = ''
      let id = undefined
      if (this.clearUrl) {
        if (this.saveClearKey) {
          id = this.queryParam[this.saveClearKey]
        }
        this.clearTemporaryTable(id)
      }
    },
    clearTemporaryTable(id) {
      let params = {
        [this.saveClearKey]: id,
      }
      let obj = {}
      if (this.clearUrlDataType && this.clearUrlDataType == 'POST') {
        obj = postAction(this.clearUrl, this.saveClearKey ? params : id, this.HttpHead)
      } else if (this.clearUrlDataType && this.clearUrlDataType == 'DELETE') {
        obj = deleteAction(this.clearUrl, this.saveClearKey ? params : id, this.HttpHead)
      } else if (this.clearUrlDataType && this.clearUrlDataType == 'PUT') {
        obj = putAction(this.clearUrl, this.saveClearKey ? params : id, this.HttpHead)
      }
      obj.then((res) => {
        if (res.IsSuccess) {
          // this.loadData()
        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
    onRemoveGoodsClick(record) {
      let ids = []
      ids.push(record.Id)
      this.deleteGoods(ids)
    },
    onMoreDeleteClick() {
      if (this.$refs.table && (this.$refs.table.selectedRowKeys || []).length < 1) {
        this.$message.warning('请选择数据!')
        return
      }
      this.deleteGoods(this.$refs.table.selectedRowKeys, true)
    },

    deleteGoods(ids, isBatch = false) {
      let params = {
        [this.TempDataRemoveKey]: ids,
      }
      let obj = {}
      if (this.linkUrlDelType && this.linkUrlDelType == 'POST') {
        obj = postAction(this.url.delete, this.TempDataRemoveKey ? params : ids, this.HttpHead)
      } else {
        obj = deleteAction(this.url.delete, this.TempDataRemoveKey ? params : ids, this.HttpHead)
      }
      obj.then((res) => {
        if (res.IsSuccess) {
          isBatch && (this.$refs.table.selectedRowKeys = [])
          if(this.$refs.table.dataSource.length === 1 && this.$refs.table.ipagination.current > 1) {
            this.$refs.table.ipagination.current--
          }
          this.loadData()
        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
    /**
     * 异常提示点击事件
     */
    onAbnormalClick(record) {
      if (!record[this.ErrorShowKey]) {
        return
      }
      this.ToastModel.Title = '存在以下异常，将导致保存失败：'
      this.ToastModel.Content = record[this.ErrorShowKey] ? record[this.ErrorShowKey].split(';') : []

      this.errVisibile = true
    },
    onKnowClick() {
      this.ToastModel = {
        Title: '',
        Content: [],
      }
      this.errVisibile = false
    },
    onUploadChange(info) {
      const status = info.file.status
      if (status === 'done') {
        if (info.file.response.IsSuccess) {
          this.isUploadFile = true
          this.tab.status = 1
          let resData = info.file.response.Data
          let queryParam = {}
          if (this.loadDataQuery && this.loadDataQuery.length > 0) {
            this.loadDataQuery.map((item) => {
              queryParam[item] = resData[item]
            })
          }
          if (this.saveDataQuery) {
            // 保存接口
            if (this.saveResDataKey) {
              // 保存接口
              this.url.add = this.url.add + `?${this.saveDataQuery}=${resData[this.saveResDataKey]}`
            } else {
              this.url.add = this.AddUrl + `?${this.saveDataQuery}=${resData}`
            }
          }
          this.queryParam = queryParam
          this.loadData()
        } else if (!info.file.response.IsSuccess && info.file.response.Msg) {
          this.$message.warning(info.file.response.Msg)
        }
      }
    },
    searchReset() {
      this.queryParam = {}
      this.loadData(1)
    },
    searchList() {
      this.loadData(1)
    },
  },
}
</script>
<style scoped>
.excel-div {
  position: relative;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background: #fafafa;
}
.icon-close {
  position: absolute;
  top: 10px;
  right: 10px;
}
.ant-result {
  padding: 5px 4px;
}
</style>
