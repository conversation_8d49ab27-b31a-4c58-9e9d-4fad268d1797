<!-- 选择税收编码 -->
<template>
  <a-modal
    title="选择税收编码"
    :width="1000"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    @ok="handleOk"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :footer="false"
  >
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl"  cardBordered :tab="tab" :tableInfo="tableInfo" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData"  @operate="operate"   />
  </a-modal>
</template>
<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
export default {
  name: 'SelectTaxClassificationCodeModal',
  props: {},
  components: {},
  mixins: [SimpleMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '关键字',
          type: 'input',
          value: '',
          key: 'KeyWord',
          defaultVal: '',
          placeholder: '税收编码/名称/简称',
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
        ],
        hintArray: [],
      },
      tableInfo: {
        scrollX: null,
        actionIndex: null, //操作项的位置
        size: 'middle',
      },
      columns: [
        {
          title: '税收编码',
          dataIndex: 'TaxCode',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '名称',
          dataIndex: 'TaxCategoryName',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '简称',
          dataIndex: 'TaxCategoryShortName',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '销售税率(%)',
          dataIndex: 'SellsTaxRate',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          fixed: 'right',
          actionBtn: [
            {
              name: '选择',
              icon: '',
              id: '',
              specialShowFuc: (e) => {
                return true
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36006',
      linkUrl: {
        list: '/{v}/GoodsManage/GetGoodsTaxInfoList',
      },
    }
  },
  mounted() {},
  created() {},
  methods: {
    show(data) {
      this.visible = true
      this.queryParam.SellsTaxRate = data;
      this.$nextTick(() => {
        this.searchQuery(this.queryParam)
      })
    },
    searchQuery(queryParam, type) {
      if (type == 'searchReset') {
        this.queryParam =  {
          SellsTaxRate: this.queryParam['SellsTaxRate']
        }
      } else {
        this.queryParam = Object.assign(queryParam, this.queryParam);
      }

      this.$refs.table.loadDatas(1,this.queryParam)
    },
    operate(record, type){
      if (type == '选择') {
        this.$emit('ok',record)
        this.close()
      }
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.queryParam = {}
    },
    handleOk(record) {
      this.$emit('ok', this.model)
      this.close()
    }
  }
}
</script>
