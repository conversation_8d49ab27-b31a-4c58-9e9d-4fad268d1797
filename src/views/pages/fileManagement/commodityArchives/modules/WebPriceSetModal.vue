<template>
  <a-modal
    title="新增挂网价"
    :width="600"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="ruleForm"
        :rules="rules"
        :model="model"
        layout="vertical"
      >
        <a-form-model-item
          label="挂网区域："
          prop="Code"
        >
          <SingleChoiceView
            style="width: 100%"
            placeholder="请选择"
            :httpParams="{name:'',level:1}"
            :dataKey="{ name: 'Name', value: 'Code', nameList: []}"
            httpHead="P31100"
            httpType="GET"
            keyWord="KeyWord"
            Url="/{v}/AdministrativeArea/AllList"
            v-model="model.Code"
            @change="(val, txt, item)=>{model.Name = txt}"
          />
        </a-form-model-item>
        <a-form-model-item
          label="挂网价："
          prop="Price"
        >
          <a-input-number
            v-model="model.Price"
            :precision="2"
            style="width:100%"
            :min="0"
            placeholder="请输入"
          />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <div slot="footer">
      <div>
        <a-button
          type="primary"
          class="mr5"
          v-if="checkBtnPermissions('7a717047-228f-43b4-9097-332a2e221472')"
          @click="handleOk"
        >保存</a-button>
        <a-button @click="handleCancel">取消</a-button>
      </div>
    </div>
  </a-modal>
</template>
<script>
import { EditMixin } from '@/mixins/EditMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'WebPriceSetModal',
  props: {},
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      model: {}
    }
  },
  computed: {
    rules() {
      return {
        Code: [{ required: true, message: '请选择!' }],
        Price: [
          {
            required: true,
            // message: '请输入!',
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请输入!'))
              } else if (value < 0.01) {
                callback(new Error('请输入!'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    }
  },
  mounted() {},
  created() {},
  methods: {
    show(areaList) {
      this.model = {
        Code: '',
        Name: '',
        Price: null
      }
      this.visible = true
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk(record) {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.$emit('ok', this.model)
          this.close()
        } else {
          return false
        }
      })
    }
  }
}
</script>
