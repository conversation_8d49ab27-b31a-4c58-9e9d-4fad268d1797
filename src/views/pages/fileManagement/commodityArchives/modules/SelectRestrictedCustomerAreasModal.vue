<template>
  <a-modal
    title="选择禁售客户区域"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <a-spin :spinning="confirmLoading">
      <a-tree
        v-model="chKeys"
        checkable
        :tree-data="treeData"
        :replaceFields="{
                children: 'Children',
                title: 'Name',
                key: 'Code'
              }"
        @check="onCheck"
        :checkStrictly="checkStrictly"
      />
    </a-spin>
    <div slot="footer">
      <div>
        <a-button
          type="primary"
          class="mr5"
          v-if="checkBtnPermissions('7a717047-228f-43b4-9097-332a2e221472')"
          @click="handleOk"
        >保存</a-button>
        <a-button @click="handleCancel">取消</a-button>
      </div>
    </div>
  </a-modal>
</template>
<script>
import { EditMixin } from '@/mixins/EditMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'SelectRestrictedCustomerAreasModal',
  props: {
    //父子节点选中状态   true不再关联   false 关联  默认不关联
    checkStrictly: {
      type: Boolean,
      default: true
    },
    //不要区 默认要
    noQu: {
      type: Boolean,
      default: false
    }
  },
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      treeData: [],
      checkedKeys: [],
      chKeys: [],
      selectedKeys: [],
      queryParam: {
        // Level: 1
      },
      // 表头
      url: {
        list: ''
      }
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(areaList) {
      // console.log(areaList)
      this.checkedKeys = []
      if ((areaList || []).length) {
        let Codes = []

        areaList.map(v => {
          Codes = Codes.concat(v['Codes'])
        })
        this.chKeys = Codes
      }

      this.visible = true
      this.url.list = '/{v}/AdministrativeArea/GetAllAdministrativeAreaList'
      this.loadAreaDataList()
    },
    // 加载区域数据
    loadAreaDataList(areaList) {
      this.loading = true

      let params = this.queryParam

      getAction(this.url.list, params, 'P31100').then(res => {
        if (res.IsSuccess) {
          if (res.Data && res.Data.length > 0) {
            // 如果传入了指定行政区划，则过滤掉其他数据
            if (areaList && areaList.length > 0) {
              this.treeData = this.getAreasByCode(areaList, res.Data)
            } else {
              this.treeData = res.Data
            }
          }
        } else {
          this.$message.warning(res.Msg)
        }
        this.loading = false
      })
    },
    // 将一个多层级数组转成一层数组
    flattenDeep(array, level) {
      let tempArr = []
      if (level === 0) {
        tempArr = [...array]
      }
      array.forEach(x => {
        if (x.Children && x.Children.length > 0) {
          tempArr = [...tempArr, ...x.Children, ...this.flattenDeep(x.Children)]
        }
      })
      return tempArr
    },
    /* flattenDeep(array) {
      return lodash.flatMapDeep(array, x => {
        if (x.Children && x.Children.length) {
          return [x, [...x.Children]]
        } else return x
      })
    }, */
    // 根据传入的行政区过滤数据
    getAreasByCode(areas, allArea) {
      let arr = []
      let flatList = this.flattenDeep(allArea, 0)
      areas.forEach(ele => {
        let findItem = flatList.find(it => it.Code === ele.AreaCode)
        arr.push({ ...findItem, checkable: true }) //之前这里是false
      })
      return arr
    },
    /**
     * e:{checked: bool, checkedNodes, node, event}
     */
    onCheck(checkedKeys, e) {
      let checkedNodes = e.checkedNodes
      this.checkedKeys = checkedNodes.map(it => {
        return {
          AreaName: it.data.props.Name,
          AreaCode: it.data.props.Code,
          Id: it.data.props.Id,
          FullName: it.data.props.FullName
        }
      })
      // console.log('check', checkedKeys)
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.treeData = []
      this.chKeys = []
      this.visible = false
    },
    handleOk(record) {
      //  console.log(this.chKeys)
      if ((this.checkedKeys || []).length == 0 && (this.chKeys || []).length == 0) {
        this.$message.warning('请选择数据!')
        return
      }
      let oldList = []
      if((this.chKeys || []).length){
        this.chKeys.map(v=>{
          oldList.push({
            AreaCode:v
          })
        })
      }
      let list = this.checkedKeys.length? this.checkedKeys:oldList
      this.$emit('ok', list)
      this.close()
    }
  }
}
</script>
<style scoped></style>
