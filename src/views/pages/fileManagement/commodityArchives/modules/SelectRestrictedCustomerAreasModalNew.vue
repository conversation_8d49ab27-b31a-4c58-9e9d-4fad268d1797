<template>
  <a-modal :title="modalTitle" :width="900" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :footer="isInfo?null:undefined" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="loading">
      <!-- div 占位，避免 loading 偏移 -->
      <div>
        <a-tree v-model="chKeys" v-if="!loading" checkable :tree-data="treeData" :expanded-keys="expandedKeys" :auto-expand-parent="autoExpandParent" :replaceFields="{
          children: 'Children',
          title: 'Name',
          key: 'Code',
        }" @check="onCheck" @expand="onExpand" :checkStrictly="checkStrictly" />
      </div>
    </a-spin>
    <div v-if="!isInfo" slot="footer">
      <div>
        <a-button type="primary" class="mr5" v-if="checkBtnPermissions('7a717047-228f-43b4-9097-332a2e221472')" @click="handleOk">保存</a-button>
        <a-button @click="handleCancel">取消</a-button>
      </div>
    </div>
  </a-modal>
</template>
<script>
// 药约约版本
import { EditMixin } from '@/mixins/EditMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'

export default {
  name: 'SelectRestrictedCustomerAreasModalNew',
  props: {
    modalTitle: {
      type: String,
      default: '选择区域',
    },
    //父子节点选中状态   true不再关联   false 关联  默认不关联
    checkStrictly: {
      type: Boolean,
      default: false
    },
    //不要区 默认要
    noQu: {
      type: Boolean,
      default: false,
    },
    // 是否加载所有数据
    loadAllList: {
      type: Boolean,
      default: false,
    },
    dataKey: {
      type: Object,
      default() {
        return { name: 'AreaName', value: 'AreaCode' }
      }
    },
    // 详情模式
    isInfo: {
      type: Boolean,
      default: false,
    },
  },
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      loading: false,
      confirmLoading: false,
      visible: false,
      treeData: [],
      allTreeData: [],
      checkedKeys: [],
      chKeys: [],
      selectedKeys: [],
      qgData: {
        Level: 0,
        Code: '0',
        Name: '全国',
        FullName: '全国',
        Id: 1000000000000000,
        Children: [],
      },
      queryParam: {
        goodsSpuId: '', // 商品id
        // Level: 1
      },
      // 表头
      url: {
        list: '',
      },
      expandedKeys: [],
      autoExpandParent: true,
    }
  },
  computed: {},
  mounted() { },
  created() { },
  methods: {
    show(areaList, payload) {
      console.log(` 禁售客户区域配置: `, areaList, payload)
      this.checkedKeys = areaList || []
      // 默认展开全国
      this.expandedKeys = ['0']
      if ((areaList || []).length) {
        let Codes = []

        areaList.map((v) => {
          Codes = Codes.concat(v['Codes'] ? v['Codes'] : v[this.dataKey.value])
        })
        // 反显
        this.chKeys = Codes
        // 计算展开的的节点
        this.calcExpand(this.chKeys)
      }

      this.visible = true
      if (this.loadAllList) {
        this.url.list = '/{v}/GoodsSaleManage/GetAllAdministrativeAreaList'
      } else {
        // 更换这个接口
        this.url.list = '/{v}/GoodsSaleManage/GetAllAdministrativeAreaListByGoodsSpuId'
        this.queryParam.goodsSpuId = payload ? payload.goodsSpuId : null
      }
      this.loadAreaDataList()
    },
    calcExpand(chKeys) {
      console.log('chKeys', chKeys)
      // 没有已选择
      if (!chKeys.length) return
      // 选择中国 默认展开全国
      if (chKeys.length === 1 && chKeys[0] === '0') {
        return
      }
      // 选择其他
      const tempArr = chKeys.map(item => {
        if (item.length === 4) {
          return ['0']
        } else if (item.length === 6) {
          return ['0', item.substr(0, 4)]
        }
        // else if (item.length === 8) {
        //   return ['0', item.substr(0, 4), item.substr(0, 6)]
        // }
      })
      this.expandedKeys = Array.from(new Set(tempArr.flat()))
    },
    // 加载区域数据
    loadAreaDataList(areaList) {
      this.loading = true

      let params = this.queryParam

      getAction(this.url.list, params, 'P36006').then((res) => {
        if (res.IsSuccess) {
          this.qgData.Children = res.Data || []
          this.treeData = [this.qgData]
          // console.log('this.treeData', this.treeData)
        } else {
          this.$message.warning(res.Msg)
        }
        this.loading = false
      })
    },
    handleCancel() {
      this.close()
    },

    onCheck(checkedKeys, info) {
      let checkedNodes = info.checkedNodes
      this.checkedKeys = checkedNodes.map(it => {
        return {
          AreaName: it.data.props.Name,
          AreaCode: it.data.props.Code,
          Id: it.data.props.Id,
          FullName: it.data.props.FullName,
        }
      })
    },
    onExpand(expandedKeys) {
      console.log('onExpand', expandedKeys);
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    close() {
      this.$emit('close')
      this.treeData = []
      this.chKeys = []
      this.visible = false
    },
    handleOk() {
      // console.log('checkedKeys', this.checkedKeys)
      // console.log('chKeys', this.chKeys)
      if ((this.checkedKeys || []).length == 0 && (this.chKeys || []).length == 0) {
        this.$message.warning('请选择数据!')
        return
      }
      let keys = []
      if ((this.chKeys || []).length) {
        this.chKeys.map((v) => {
          keys.push({
            AreaCode: v,
          })
        })
      }
      this.$emit('ok', keys)
      this.close()
    },
  },
}
</script>
<style scoped></style>
