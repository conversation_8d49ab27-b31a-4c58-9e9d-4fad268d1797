<template>
  <a-modal
    title="选择禁售客户名单"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <a-spin :spinning="confirmLoading">
      <a-tabs
        v-model="activeKey"
        @change="handleActiveKey"
      >
        <a-tab-pane
          key="1"
          tab="电商客户"
        >
          <!-- 搜索 -->
          <SimpleSearchArea
            ref="SimpleSearchArea"
            :searchInput="searchInput"
            @searchQuery="searchQuery"
          />
          <SimpleTable
            ref="table"
            :tab="tab"
            :isTableInitData="isTableInitData"
            :queryParam="queryParam"
            :columns="columns"
            :linkHttpHead="linkHttpHead"
            :linkUrlType="linkUrlType"
            :linkUrl="linkUrl"
            @selectedRows="(selectedRowKeys, selectedRows)=>handleSelectedRows(selectedRowKeys, selectedRows,'table')"
          >
            <span
              slot="commonContent"
              slot-scope="{text,record}"
            >
              <j-ellipsis
                :value="`${record['AreaFullName'] || ''}${record['DetailAddress'] || ''}`"
                :length="15"
              />
            </span>
          </SimpleTable>
        </a-tab-pane>
        <a-tab-pane
          key="2"
          tab="商销客户"
        >
          <!-- 搜索 -->
          <SimpleSearchArea
            ref="SimpleSearchArea1"
            :searchInput="searchInput1"
            @searchQuery="searchQuery"
          />
          <SimpleTable
            ref="table1"
            :tab="tab"
            :isTableInitData="isTableInitData"
            :queryParam="queryParam1"
            :columns="columns"
            linkHttpHead="P36002"
            linkUrlType="GET"
            :linkUrl="linkUrl"
            @selectedRows="(selectedRowKeys, selectedRows)=>handleSelectedRows(selectedRowKeys, selectedRows,'table1')"
          >
            <span
              slot="commonContent"
              slot-scope="{text,record}"
            >
              <j-ellipsis
                :value="`${record['RegAreaName'] || ''}${record['RegAddress'] || ''}`"
                :length="15"
              />
            </span>
          </SimpleTable>
        </a-tab-pane>

      </a-tabs>

    </a-spin>
    <div slot="footer">
      <div>
        <a-button
          type="primary"
          class="mr5"
          v-if="checkBtnPermissions('6daec0c2-9f32-48a6-91cd-0d0434223bcf')"
          @click="handleOk"
        >保存</a-button>
        <a-button @click="handleCancel">取消</a-button>
      </div>
    </div>
  </a-modal>
</template>
<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'SelectBannedCustomerListModal',
  components: { JEllipsis },
  mixins: [SimpleMixin, EditMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      activeKey: '1',
      tab: {
        rowKey: 'Id',
        rowSelection: {
          type: 'checkbox',
        },
      },
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '客户名称', type: 'input', value: '', key: 'Name', defaultVal: '', placeholder: '请输入' },
        { name: '客户编号', type: 'input', value: '', key: 'Code', defaultVal: '', placeholder: '请输入' },
        // { name: '生产厂家', type: 'input', value: '', key: 'CreateByName4', defaultVal: '', placeholder: '请输入' }
      ],
      searchInput1: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '客户', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号' },
      ],
      // 表头
      columns: [
        {
          title: '客户名称',
          dataIndex: 'Name',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户编号',
          dataIndex: 'Code',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '生产厂家',
        //   dataIndex: 'Creat1eTime',
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'component' }
        // },
        {
          title: '注册地址',
          dataIndex: 'RegAreaName',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
      ],
      queryParam: {
        // FirstCampAuditStatus: 3,
      },
      queryParam1: {
        AuthBusiness: 2,
        AuditStatus: 2,
      },
      tableSelectedRows: [],
      tableSelectedRows1: [],
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P31102',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '',
        getCustomerListByLabels: '/Customer/ListByLabels', //电商客户
        getCustomerList: '/{v}/Customer/List', // 商销客户
      },
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(data = []) {
      // console.log(data)
      this.tableSelectedRows = []
      this.tableSelectedRows1 = []
      this.visible = true
      this.queryParam = {
        // FirstCampAuditStatus: 3,
      }
      this.queryParam1 = {
        AuthBusiness: 2,
        AuditStatus: 2,
      }
      this.$nextTick(() => {
        // this.$refs.table.selectedRowKeys = data.map(v => v.Id)
        // this.$refs.table.selectedRows = data.map(v => {
        //   return {
        //     Id: v.Id,
        //     // Code: v.Code,
        //     Name: v.CustomerName,
        //     Code: v.CustomerCode,
        //     RegAddress: v.RegAddress
        //   }
        // })
        this.linkUrl.list = this.linkUrl.getCustomerListByLabels
        if (this.$refs.table) this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    handleActiveKey() {
      if (this.activeKey == 1) {
        this.linkUrl.list = this.linkUrl.getCustomerListByLabels
        this.$nextTick(() => {
          if (this.$refs.table) this.$refs.table.loadDatas(1, this.queryParam)
        })
      } else {
        this.linkUrl.list = this.linkUrl.getCustomerList
        this.$nextTick(() => {
          if (this.$refs.table1) this.$refs.table1.loadDatas(1, this.queryParam1)
        })
      }
    },
    searchQuery() {
      if (this.activeKey == 1) {
        this.queryParam = this.$refs.SimpleSearchArea.queryParam
        // this.queryParam.FirstCampAuditStatus = 3
        this.$nextTick(() => {
          if (this.$refs.table) this.$refs.table.loadDatas(1, this.queryParam)
        })
      } else {
        this.queryParam1 = this.$refs.SimpleSearchArea1.queryParam
        this.queryParam1.AuthBusiness = 2
        this.queryParam1.AuditStatus = 2
        this.$nextTick(() => {
          if (this.$refs.table1) this.$refs.table1.loadDatas(1, this.queryParam1)
        })
      }
    },
    handleCancel() {
      this.visible = false
      this.activeKey = '1'
      this.linkUrl.list = ''
      this.tableSelectedRows = []
      this.tableSelectedRows1 = []
      this.queryParam = {}
      this.queryParam1 = {}
      if (this.$refs.SimpleSearchArea) this.$refs.SimpleSearchArea.queryParam = {}
      if (this.$refs.SimpleSearchArea1) this.$refs.SimpleSearchArea1.queryParam = {}
    },
    handleSelectedRows(selectedRowKeys, selectedRows, key) {
      // console.log('selectedRowKeys   ', selectedRowKeys)
      // console.log('selectedRows   ', selectedRows)
      if (key == 'table') {
        selectedRows.map((j) => {
          if (this.tableSelectedRows.findIndex((item) => item.Id == j.Id) == -1) {
            this.tableSelectedRows.push(j)
          }
        })
      } else {
        selectedRows.map((j) => {
          if (this.tableSelectedRows1.findIndex((item) => item.Id == j.Id) == -1) {
            this.tableSelectedRows1.push(j)
          }
        })
      }
      // console.log('tableSelectedRows   ', this.tableSelectedRows)
      // console.log('tableSelectedRows1   ', this.tableSelectedRows1)
    },
    handleOk(record) {
      let tableSelectedRows = this.tableSelectedRows // (this.$refs.table && this.$refs.table.selectedRows) || []
      let table1SelectedRows = this.tableSelectedRows1 //(this.$refs.table1 && this.$refs.table1.selectedRows) || []
      // console.log(tableSelectedRows.length)
      // console.log('table1SelectedRows   ', table1SelectedRows.length)
      if (!tableSelectedRows.length && !table1SelectedRows.length) {
        this.$message.warning('请选择数据!')
        return
      }

      let selectedRows = [].concat(tableSelectedRows).concat(table1SelectedRows)
      // console.log(selectedRows)
      this.$emit('ok', selectedRows)
      this.handleCancel()
    },
  },
}
</script>
<style scoped></style>
