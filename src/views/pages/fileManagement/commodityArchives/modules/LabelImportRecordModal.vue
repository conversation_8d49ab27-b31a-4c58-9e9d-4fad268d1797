<template>
  <a-modal 
    title="导入记录" 
    :width="1000" 
    :visible="visible" 
    @cancel="handleCancel" 
    :footer="null"
    :maskClosable="false" 
    :destroyOnClose="true"
  >
    <div>
      <SimpleSearchView ref="searchView" :searchParamsList="searchItems" :queryCol="8" @search="searchQuery" />
      <!-- 列表 -->
      <TableNewView ref="tableView" :table="table" :columns="columns" :dataList="dataSource" @operate="operate"/>
    </div>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { PublicMixin } from '@/mixins/PublicMixin'//非必要 公用的 有需要时候用其他mixin也行

export default {
  name: 'LabelImportRecordModal',
  mixins: [
    ListMixin, 
    PublicMixin
  ],
  data() {
    return {
      visible: false,
      searchItems: [
        {
          name: '操作时间', //输入框名称 必填
          type: this.$SEnum.DATE, //类型 必填
          key: 'DateBegin', //搜索key 必填
          keyEnd: 'DateEnd'
        },  
        {
          name: '操作人', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'CreateByName', //搜索key 必填
          placeholder: '请输入',
        },  
      ],
      table: {
        operateBtns: [

        ], //右上角按钮集合
        rowKey: 'Id',
        customSlot: [],
      },
      columns: [
        {
          title: '操作时间',
          dataIndex: 'CreateTime',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '类型',
          dataIndex: 'ImportTypeStr',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作人',
          dataIndex: 'CreateByName',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          // fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '下载',
              isShow: (record) => {
                return true
              },
            },
          ],
        },
      ],
      queryParam: {
        ImportTypes: [1, 2],
        IsAchievementSuccess: true
      },
      httpHead: 'P36001',
      url: {
        listType: 'POST', //列表接口请求类型
        list: '/{v}/ImportRecord/GetImportRecordList',//列表数据接口
      },
    }
  },
  methods: {
    show() {
      this.visible = true
      this.loadData()
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
      // this.importFileName = ''
    },
    // 操作
    operate(record, key, index) {
      console.log(record, key, index)
      if (key == '下载') {
        if(!record.AchievementFileUrl) {
          this.$message.warning('下载地址不存在')
          return
        }
        const a = document.createElement('a')
        a.href = record.AchievementFileUrl
        a.download =``
        a.click()
      }
    },
  },
}
</script>

<style scoped>

</style>