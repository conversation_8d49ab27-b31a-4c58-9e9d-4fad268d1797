<template>
  <a-modal
    title="商品换绑"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :footer="false"
  >
    <a-spin :spinning="confirmLoading">
      <!-- 搜索 -->
      <SimpleSearchArea
        ref="SimpleSearchArea"
        :searchInput="searchInput"
        @searchQuery="searchQuery"
      />
      <SimpleTable
        ref="table"
        :tab="tab"
        :columns="columns"
        :queryParam="queryParam"
        :isTableInitData="isTableInitData"
        :linkHttpHead="linkHttpHead"
        :linkUrlType="linkUrlType"
        :linkUrl="linkUrl"
        @operate="operate"
      />
    </a-spin>
  </a-modal>
</template>
<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'SalesMaintenanceCommoditySwapModal',
  components: {},
  mixins: [SimpleMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      data: [],
      tab: {
        showIpagination: true
      },
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '商品名称', type: 'input', value: '', key: 'GoodsName', defaultVal: '', placeholder: '请输入' },
        // { name: '商品条码', type: 'input', value: '', key: 'PurchaseNo1', defaultVal: '', placeholder: '请输入' },
        { name: '生产厂家', type: 'input', value: '', key: 'Manufacturer', defaultVal: '', placeholder: '请输入' }
        // { name: '商品规格', type: 'input', value: '', key: 'CreateByName4', defaultVal: '', placeholder: '请输入' }
      ],
      // 表头
      columns: [
        {
          title: '商品名称',
          dataIndex: 'GoodsName',
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '规格',
          dataIndex: 'Specification',
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '生产厂家',
          dataIndex: 'Manufacturer',
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        // {
        //   title: '商品条码',
        //   dataIndex: 'CreateTim1e',
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'component' }
        // },
        // {
        //   title: '销售原价',
        //   dataIndex: 'CreateTi1me',
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'component' }
        // },

        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtn: [
            {
              name: '绑定',
              icon: '',
              id: '5fdce6b2-817a-407a-949d-6dd2333d865a',
              specialShowFuc: e => {
                let record = e || {}
                if (this.data.length < 1) {
                  return true
                } else if (
                  record.Id != (this.queryParam.PlatformType == '合纵' ? this.data[0].Id_HZ : this.data[0].Id_JC)
                ) {
                  return true
                } else {
                  return false
                }
              }
            },
            {
              name: '已绑定',
              icon: '',
              disabled: true,
              id: '5fdce6b2-817a-407a-949d-6dd2333d865a',
              specialShowFuc: e => {
                let record = e || {}
                if (this.data.length < 1) {
                  return false
                } else if (
                  record.Id == (this.queryParam.PlatformType == '合纵' ? this.data[0].Id_HZ : this.data[0].Id_JC)
                ) {
                  return true
                } else {
                  return false
                }
              }
            }
          ],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P46053',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '',
        create: '/GoodsContrast/Create'
      }
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(goods = {}, data = [], name) {
      // console.log('=================')
      // console.log(data)
      this.visible = true
      this.$set(this.queryParam, 'PlatformType', name)
      if (goods) {
        this.$set(this.queryParam, 'GoodsName', goods.AErpGoodsName)
        this.$set(this.queryParam, 'Manufacturer', goods.ABrandManufacturer)
      }

      this.data = data
      this.linkUrl['list'] = '/GoodsContrast/OtherGoodsByContrast'
      this.$nextTick(() => {
        this.$refs.SimpleSearchArea.queryParam = this.queryParam
        if (this.$refs.table) this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    handleCancel() {
      this.visible = false
      this.linkUrl['list'] = ''
      this.queryParam = {}
    },
    searchQuery(queryParam, type) {
      if (type == 'searchReset') {
        queryParam.PlatformType = this.queryParam.PlatformType
      }

      this.$refs.table.loadDatas(type == 'searchReset' ? 1 : null, queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '绑定') {
        let that = this
        this.handleBind(record)
      }
    },
    handleBind(record) {
      if (this.data.length < 1) {
        this.$message.warning('商品数据出错')
        return
      }
      let params = {
        PlatformType: this.queryParam.PlatformType,
        OtherGoodsId: record['GoodsId'],
        PlatGoodsId: this.data[0].Id,
        IsUseUnitRatio: false,
        MyUnitRatio: 1,
        OtherUnitRatio: 1
      }
      postAction(this.linkUrl.create, params, this.linkHttpHead)
        .then(res => {
          if (res.IsSuccess) {
            this.$message.success('操作成功！')
            this.$emit('ok')
            this.handleCancel()
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .catch(err => {
          this.$message.warning(err.Msg || err)
        })
        .finally(() => {})
    }
  }
}
</script>
<style scoped></style>
