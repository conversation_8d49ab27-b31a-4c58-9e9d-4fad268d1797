<!-- 商品销售管理 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商品销售管理',
  name: 'commoditySalesManagementList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '商品', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码/SPZ码' },
        {
          name: '商品类别',
          type: 'selectLink',
          vModel: 'ManageClassify1',
          dataKey: { name: 'Name', value: 'Id' },
          httpParams: { Name: '', Level: 1 },
          keyWord: 'Name',
          defaultVal: '',
          placeholder: '请选择',
          httpType: 'POST',
          url: '/{v}/GoodsManage/QueryGoodsManageClassifyList',
          httpHead: 'P36006',
        },

        { name: '采购人', type: 'input', value: '', key: 'GoodsPurchaser', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: 'SPZ码',
          dataIndex: 'ErpGoodsId',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品类别',
          dataIndex: 'ManageClassifyStr1',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购人',
          dataIndex: 'GoodsPurchaser',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 250,
          fixed: 'right',
          actionBtn: [
            { name: '销售维护', icon: '', id: '2fde43d2-f79a-421a-bbd6-abc5dfe49d34' },
            { name: '禁售维护', icon: '', id: 'dd76588f-e5d7-4437-9082-ca875bbaacc8' },
            // {
            //   name: '数据详情',
            //   icon: '',
            //   id: '800f913b-b444-437b-bb7e-782b69a0ac4d',
            // },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: true, //是否自动加载
      linkHttpHead: 'P36006',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/GoodsSaleManage/GetGoodsList',
        sub: '/PurchaseOrder/SubmitAsync',
        del: '/PurchaseOrder/DeleteAsync',
        isValidUrl: '/YYDepartment/GetDepartmentList', //若有是否有效需要写这个url
      },
    }
  },
  created() { },
  // mounted() {
  //   // let msgKey = 'purchaseDemandList'
  //   // this.checkedFromMsg(msgKey)
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    // 确定提交
    subPurchaseDemand(record) {
      let params = {}
      let url = this.linkUrl.sub + '?orderId=' + record.Id
      postAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('提交成功')
            this.$refs.table.loadDatas(1, this.queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    // 确定删除
    subDelPurchaseDemand(record) {
      let params = {}
      let url = this.linkUrl.del + '?orderId=' + record.Id
      postAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('删除成功')
            this.$refs.table.loadDatas(1, this.queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    // 列表操作
    operate(record, type) {
      if (type == '销售维护') {
        this.onDetailClick('salesMaintenance', { id: record.GoodsSpuId })
      } else if (type == '禁售维护') {
        this.onDetailClick('prohibitedMaintenanceNew', { id: record.GoodsSpuId })
      } else if (type == '数据详情') {
        this.$message.warning('数据详情')
      }
    },
  },
}
</script>

<style></style>
