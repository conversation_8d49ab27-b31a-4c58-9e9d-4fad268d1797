<!-- 商品档案新增 -->
<template>
  <div>
    <a-alert
      type="error"
      :message="`驳回原因：${RejectReason || ''}`"
      banner
      :showIcon="false"
      v-if="opType != 1 && RejectReason"
    />
    <a-card style="margin: 15px 0" :bordered="false">
      <a-spin :spinning="spinning">
        <a-steps :current="current" :class="!notPrimaryInfoBool ? 'w50-center' : 'w10-center'">
          <a-step title="基本信息" />
          <a-step title="首营信息" v-if="!notPrimaryInfoBool" />
        </a-steps>
        <a-divider />
        <!-- 基本信息 -->
        <BasicInfoTemplateForm
          ref="iBasicInfoTemplateForm"
          v-if="current === 0 && isBasicInfoTemplateForm"
          :opType="opType"
          :Info="model"
          @changeManageClassify="changeManageClassify"
          @changeManageClassify2="val=> ManageClassify2Code = val"
          @supplierChange="onSupplierChange"
        />
        <!-- 首营信息 -->
        <PrimaryInfoTemplateForm
          ref="iPrimaryInfoTemplateForm"
          v-if="current === 1 && model && id && !notPrimaryInfoBool"
          :opType="opType"
          :id="id"
          :fileNo="model['FileNo']"
          :goodsManageClassifyId="model['ManageClassify2']"
          :erpGoodsId="model['ErpGoodsId']"
          :ManageClassify2Code="ManageClassify2Code"
          :SupplierType="model['SupplierType']"
          :AppendInfo="AppendInfo"
          :goods="model"
          :hasList="qList"
          :remark="Remark"
          :isGoodsQualificationExist="isGoodsQualificationExist"
          :BusinessSerialNumber="BusinessSerialNumber"
          :FileId="FileId"
        />
      </a-spin>
    </a-card>
    <a-affix :offset-bottom="0" style="float: right; width: 100%; text-align: right">
      <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
        <a-button class="mr8" v-if="current > 0 && !notPrimaryInfoBool" @click="handleLast">上一步</a-button>
        <YYLButton
          menuId="41dab671-4510-4bf1-882f-11a12a955788"
          text="保存"
          type="primary"
          :loading="spinning"
          @click="handleSave"
          v-if="opType == 1 || notPrimaryInfoBool"
        />
        <YYLButton
          menuId="c1e46d9b-487d-4af2-b726-5997dc512367"
          text="保存并提交审核"
          type="primary"
          :loading="spinning"
          v-if="(current > 0 || (current == 0 && supplierType == 2 && (ManageClassify2Code !== 'ZYC_2' && ManageClassify2Code !== 'ZYYP_2') )) && !notPrimaryInfoBool"
          @click="handleSubmit"
        />
        <!--supplierType:null, 1批发企业 2生产企业 -->
        <YYLButton
          menuId="ece2b97f-5e62-44da-98f7-b0bc341cb677"
          text="下一步"
          type="primary"
          :loading="spinning"
          v-if="
            current == 0 &&
            !notPrimaryInfoBool &&
            (supplierType != 2 ||
              (ManageClassify2Code === 'ZYC_2' || ManageClassify2Code === 'ZYYP_2'))
          "
          @click="handleNext"
        />
        <a-button @click="goBack(true, true)">取消</a-button>
      </a-card>
    </a-affix>
  </div>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商品档案新增',
  name: 'commodityArchivesAdd',
  mixins: [EditMixin],
  data() {
    return {
      spinning: false,
      current: 0,
      // model: {
      //   ManageClassify2: '' //'31d8008f-ed37-4bab-9a4b-48dcea356950'
      // },
      model: null,
      BusinessSerialNumber: null,
      FileId: null,
      isBasicInfoTemplateForm: true,
      notPrimaryInfoBool: false, //不是主要商品，实际意思就是 是否是促销品
      supplierType: null, //1批发企业 2生产企业
      Remark: '',
      AppendInfo: '',
      RejectReason: '',
      qList: [], //资质list
      isGoodsQualificationExist: true,
      id: '',
      opType: 1, //1 新增  2 采购编辑 3质管编辑(此界面不使用） 4详情 此界面不使用）
      httpHead: 'P36006',
      url: {
        createGoodsSpu: '/{v}/GoodsManage/CreateGoodsSpu',
        editGoodsSpu: '/{v}/GoodsManage/EditGoodsSpu',
        detail: '/{v}/GoodsManage/GetGoodsBaseInfoForPurchasing',
        getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
        getGoodsAdditionalInformationDetail: '/{v}/GoodsManage/GetGoodsAdditionalInformationDetail',
        createApprovalUrl: '/{v}/ApprovalWorkFlowInstance/CreateApproval', // 创建审核实例
        submitAdd: '/{v}/GoodsManage/CreateGoodsSpuForSC',
        submitEdit: '/{v}/GoodsManage/EditGoodsSpuForSC',
      },
      ManageClassify2Code: 'YP_2', // 商品分类 BasicInfoTemplateForm组件默认药品
    }
  },
  created() {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.opType = this.$route.query.opType ? Number(this.$route.query.opType) : 1
      this.setTitle(this.opType == 1 ? '商品档案新增' : '商品采购编辑')
      if (this.opType == 2) {
        this.loadDetailInfo()
      } else {
      }
    }
  },
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { goodsSpuId: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            this.supplierType = that.model.SupplierType
            this.ManageClassify2Code = that.model.ManageClassifyCode2
            if (this.$refs.iBasicInfoTemplateForm) this.$refs.iBasicInfoTemplateForm.model = res.Data
            if (this.model.ManageClassifyCode1 != 'ZP') this.getGoodsAdditionalInformationDetail()
            this.changeManageClassify(this.model.ManageClassifyCode1)
            this.toggle('isBasicInfoTemplateForm')
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    setData(data) {
      if ((data || []).length < 1) {
        return null
      }
      data.map((group) => {
        if ((group.Qualifications || []).length) {
          group.Qualifications.forEach((item) => {
            item['QualificationValue'] = item.Urls
          })
        } else {
          // group['Qualifications'] = [
          //   {
          //     QualificationName: group['GroupName'],
          //     QualificationCode: group['GroupCode'],
          //     QualificationType: 1,
          //     QualificationTypeStr: '图片',
          //     IsMust: group['IsMust'],
          //     IsShow: true
          //   }
          // ]
        }
      })
      return data
    },
    getGoodsAdditionalInformationDetail() {
      let that = this
      that.loading = true
      getAction(that.url.getGoodsAdditionalInformationDetail, { goodsSpuId: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (((res.Data && res.Data['Items']) || []).length) {
              that.qList = this.setData(res.Data['Items'])
              // console.log('qList   ', that.qList)
            }
            this.isGoodsQualificationExist = res.Data.IsGoodsQualificationExist
            this.Remark = (res.Data && res.Data['Remark']) || ''
            let BusinessSerialNumber = (res.Data && res.Data['BusinessSerialNumber']) || null
            this.BusinessSerialNumber = BusinessSerialNumber
            this.FileId = (res.Data && res.Data['FileId']) || null
            this.getApprovalResults(BusinessSerialNumber)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // IsHasSuperior
    getApprovalResults(bussinessNo) {
      if (!bussinessNo) return
      getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.RejectReason = res.Data['RejectReason']
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {})
    },
    // 保存
    handleSave() {
      let isClickEdit = true
      if (this.current === 0) {
        let formData = this.$refs.iBasicInfoTemplateForm.onSubmitIsDraft(isClickEdit)
        this.model = formData
        if (formData) {
          if (this.opType == 1 && formData['ManageClassify1Code'] != 'ZP') formData['IsDraft'] = true
          this.stepSubmitPost(formData, null, (bool) => {
            if (bool) {
              this.$message.success('操作成功！')
              setTimeout(() => {
                this.goBack(true, true)
              }, 500)
              return
            }
          })
        }
      } else {
        if (!this.$refs.iPrimaryInfoTemplateForm) {
          return
        }
        if (this.opType == 1) {
          //新增
          this.$refs.iPrimaryInfoTemplateForm.addSaveData(false, (bool) => {
            if (bool) {
              setTimeout(() => {
                this.goBack(true, true)
              }, 500)
            }
          })
        } else {
          //编辑
          this.$refs.iPrimaryInfoTemplateForm.editSaveData(false, (bool) => {
            if (bool) {
              setTimeout(() => {
                this.goBack(true, true)
              }, 500)
            }
          })
        }
      }
    },
    // 上一步
    handleLast() {
      // console.log('handleLast', this.$refs.iBasicInfoTemplateForm)
      this.model = null
      this.current = 0
      if (this.id) this.loadDetailInfo()
    },
    // 下一步
    handleNext(callback) {
      // this.current = 1
      // return
      let formData = this.$refs.iBasicInfoTemplateForm.onSubmit()
      if (!formData) return
      this.model = formData
      this.AppendInfo = ''
      if (
        this.model.ManageClassify1Code == 'YLQX' &&
        this.model.BusinessScopeName &&
        this.model.BusinessScopeName.startsWith('I')
      ) {
        this.AppendInfo = '1'
      }
      if (
        (this.ManageClassify2Code == 'YP_2' || this.ManageClassify2Code == 'JKYP_2') &&
        this.model['SupplierType']
      ) {
        this.AppendInfo = '' + this.model['SupplierType']
      }
      if (this.opType == 1) formData['IsDraft'] = false
      if (callback) {
        callback(formData)
      } else {
        this.stepSubmitPost(formData, 'next', (bool) => {
          if (bool) this.current = 1
        })
      }
    },
    checkIsMedicalInsurancek(formData) {
      if (formData.IsMedicalInsurance && ['YP_2', 'JKYP_2'].includes(formData.ManageClassify2Code)) {
        return true
      } else if (formData.IsMedicalInsurance == false || formData.IsMedicalInsurance == 'false') {
        return false
      } else {
        return null
      }
    },
    checkMedicalInsurancekInfo(formData) {
      // 显示销售信息时处理数据
      if (formData.showSaleInfo) {
        let medicalInsurancekStatus = null
        formData.IsMedicalInsurance = this.checkIsMedicalInsurancek(formData)
        medicalInsurancekStatus = formData.IsMedicalInsurance
        formData.IsBaseDrug = medicalInsurancekStatus ? formData.IsBaseDrug : null
        formData.IsCentralizedPurchasing = medicalInsurancekStatus ? formData.IsCentralizedPurchasing : null
        formData.IsOverallPlanning = medicalInsurancekStatus ? formData.IsOverallPlanning : null
        formData.BaseDrugAreas = !medicalInsurancekStatus || !formData.BaseDrugAreas ? [] : formData.BaseDrugAreas
        formData.CentralizedPurchasingAreas =
          !medicalInsurancekStatus || !formData.CentralizedPurchasingAreas ? [] : formData.CentralizedPurchasingAreas
        formData.OverallPlanningAreas =
          !medicalInsurancekStatus || !formData.OverallPlanningAreas ? [] : formData.OverallPlanningAreas
        formData.MedicalInsuranceType = medicalInsurancekStatus ? formData.MedicalInsuranceType : null
        formData.HasWebPrice = !medicalInsurancekStatus || !formData.HasWebPrice ? false : formData.HasWebPrice
        formData.WebPrice = !medicalInsurancekStatus || !formData.WebPrice ? [] : formData.WebPrice
      }
      return formData
    },
    stepSubmitPost(formData, act, call) {
      // this.$refs.iBasicInfoTemplateForm.loading = true
      if (this.id) formData['Id'] = this.id
      // 医保信息无或者否的时候默认为空
      let medicalInsurancekInfo = this.checkMedicalInsurancekInfo(formData) || {}
      formData = {
        ...formData,
        ...medicalInsurancekInfo,
      }
      this.spinning = true
      postAction(!this.id ? this.url.createGoodsSpu : this.url.editGoodsSpu, formData, this.httpHead).then((res) => {
        // this.$refs.iBasicInfoTemplateForm.loading = false
        this.spinning = false
        if (!res.IsSuccess) {
          call && call(false)
          this.$message.error(res.Msg)
          return
        }
        this.id = this.id ? this.id : res.Data
        call && call(true)
        // // 下一步
        // call && call()
      })
    },
    // 保存并审核
    handleSubmit() {
      let that = this
      //生产企业 1批发企业 2生产企业
      if (this.supplierType != 2 || (this.ManageClassify2Code === 'ZYC_2' || this.ManageClassify2Code === 'ZYYP_2')) {
        //批发企业
        if (!this.$refs.iPrimaryInfoTemplateForm) {
          return
        }
        if (this.opType == 1) {
          //新增
          this.spinning = true
          this.$refs.iPrimaryInfoTemplateForm.addSaveData(true, (bool) => {
            this.spinning = false
            if (bool) {
              this.goBack(true, true)
            }
          })
        } else {
          //编辑
          this.spinning = true
          this.$refs.iPrimaryInfoTemplateForm.editSaveData(true, (bool) => {
            this.spinning = false
            if (bool) {
              this.goBack(true, true)
            }
          })
        }
      } else {
        this.handleNext((data) => {
          data['AppendInfo'] = this.AppendInfo
          console.log('NBA', data)
          that.submitAndAudit(data)
        })
      }
    },

    submitAndAudit(formData) {
      let that = this
      if (this.id) formData['Id'] = this.id
      // 医保信息无或者否的时候默认为空
      let medicalInsurancekInfo = this.checkMedicalInsurancekInfo(formData) || {}
      formData = {
        ...formData,
        ...medicalInsurancekInfo,
      }
      this.spinning = true
      postAction(!this.id ? this.url.submitAdd : this.url.submitEdit, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            //这里要
            that.createApproval(res.Data)
          } else {
            that.spinning = false
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {
          // that.spinning = false
        })
        .catch((err) => {
          that.spinning = false
        })
    },
    // 创建审核实例
    createApproval(bussinessNo, remark) {
      let that = this
      if (!bussinessNo) {
        this.$message.error('BussinessNo未获取到值')
        that.spinning = false
        return
      }
      postAction(
        that.url.createApprovalUrl,
        {
          BussinessNo: bussinessNo,
          Scenes: 3,
          OpratorId: that.getLoginUserId(),
          Remark: remark || '',
        },
        'P36005'
      )
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.goBack(true, true)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.spinning = false
        })
        .catch((err) => {
          that.spinning = false
        })
    },
    toggle(key) {
      this[key] = false
      this.$nextTick(() => {
        this[key] = true
      })
    },
    changeManageClassify(ManageClassify1Code) {
      this.notPrimaryInfoBool = ManageClassify1Code == 'ZP' ? true : false
    },
    /**
     * 来货单位change
     * type 企业类型 1批发企业 2生产企业
     */
    onSupplierChange(type) {
      this.supplierType = type
    },
  },
}
</script>
<style lang="less" scoped>
.w10-center {
  width: 10%;
  margin: 0 auto;
}
</style>
