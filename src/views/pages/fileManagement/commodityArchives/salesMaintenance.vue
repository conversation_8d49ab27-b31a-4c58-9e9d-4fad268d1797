<!-- 商品档案 销售维护 -->
<template>
  <div>
    <a-spin :spinning="spinning">
      <a-card :bordered="false">
        <a-descriptions title="商品信息">
          <a-descriptions-item label="商品名称">
            {{ model['ErpGoodsName'] || ' -- ' }}
          </a-descriptions-item>
          <a-descriptions-item label="商品编号">
            {{ model['ErpGoodsCode'] || ' -- ' }}
          </a-descriptions-item>
          <a-descriptions-item label="商品类别">
            {{ model['ManageClassifyStr1'] || ' -- ' }}
          </a-descriptions-item>
          <a-descriptions-item label="商品规格">
            {{ model['PackingSpecification'] || ' -- ' }}
          </a-descriptions-item>
          <a-descriptions-item label="生产厂家">
            {{ model['BrandManufacturer'] || ' -- ' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-divider />
      <a-card style="margin: 15px 0" class="form-model-style" :bordered="false">
        <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical">
          <!-- 禁售客户类型配置 -->
          <template>
            <a-row :gutter="gutter">
              <a-col :span="md">
                <a-form-model-item prop="OriginalPrice" label="销售原价：">
                  <a-input-number v-model="model.OriginalPrice" :precision="2" style="width: 100%" :min="0" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
              <a-col :span="md">
                <a-form-model-item prop="EnumGoodsSaleType" label="商城和商销控制：">
                  <EnumSingleChoiceView style="width: 100%" placeholder="请选择" dictCode="EnumGoodsSaleType" v-model="model.EnumGoodsSaleType" @change="(value, text) => (model.EnumGoodsSaleTypeStr = text)" />
                </a-form-model-item>
              </a-col>
              <a-col :span="md" v-if="model.EnumGoodsSaleType != 2">
                <a-form-model-item prop="LimitPrice" label="商销底价：">
                  <a-input-number v-model="model.LimitPrice" :precision="2" style="width: 100%" :min="0" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
              <a-col :span="md">
                <a-form-model-item prop="IsTicketPrice" label="是否有终端开票价：">
                  <a-select placeholder="请选择" v-model="model.IsTicketPrice">
                    <a-select-option :value="''"> 请选择 </a-select-option>
                    <a-select-option v-for="(r, rIndex) in [
                        { title: '是', value: 'true' },
                        { title: '否', value: 'false' },
                      ]" :key="rIndex" :value="r.value">
                      {{ r.title }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="md" v-if="model.IsTicketPrice == 'true'">
                <a-form-model-item prop="TicketPrice" label="终端开票价：">
                  <a-input-number v-model="model.TicketPrice" :precision="2" style="width: 100%" :min="0" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
              <a-col :span="md" v-if="model.HasSpecialGoodsLimitPrice">
                <a-form-model-item prop="SpecialGoodsLimitPrice" label="特殊商品底价：">
                  <a-input-number v-model="model.SpecialGoodsLimitPrice" :precision="2" style="width: 100%" :min="0" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
              <a-col :span="md">
                <a-form-model-item label="建议零售价：" prop="AdviseSalePrice">
                  <a-input-number v-model="model.AdviseSalePrice" :precision="2" style="width: 100%" :min="0" placeholder="请输入" />
                </a-form-model-item>
              </a-col>
              <a-col :span="md" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassifyCode2)">
                <a-form-model-item label="是否医保品种：" prop="IsMedicalInsurance">
                  <a-select placeholder="请选择" v-model="model.IsMedicalInsurance" @change="changeIsMedicalInsurance">
                    <a-select-option :value="''"> 请选择 </a-select-option>
                    <a-select-option v-for="(r, rIndex) in [
                      { title: '是', value: 'true' },
                      { title: '否', value: 'false' },
                    ]" :key="rIndex" :value="r.value">
                      {{ r.title }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <!-- 医保信息 -->
              <HealthInsuranceForm ref="HealthInsuranceForm" v-if="model.IsMedicalInsurance == 'true' && ['YP_2', 'JKYP_2'].includes(model.ManageClassifyCode2)" :md="md" :info="model" />

              <!-- <a-col :span="md">
                <a-form-model-item
                  label="是否暂不经营品种："
                  prop="NotAllowedPurchase"
                >
                  <a-select
                    placeholder="请选择"
                    v-model="model.NotAllowedPurchase"
                  >
                    <a-select-option :value="''"> 请选择 </a-select-option>
                    <a-select-option
                      v-for="(r, rIndex) in [
                      { title: '是', value: 'true' },
                      { title: '否', value: 'false' },
                    ]"
                      :key="rIndex"
                      :value="r.value"
                    >
                      {{ r.title }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col> -->
            </a-row>
            <a-row :gutter="gutter" v-if="model.IsMedicalInsurance == 'true'">
              <a-col :span="md">
                <a-form-model-item label="是否挂网：" prop="HasWebPrice">
                  <a-row :gutter="gutter">
                    <a-col :span="
                        checkBtnPermissions('4e7ad68f-4d0e-43e9-b98e-da94d0ea563e') && model.HasWebPrice == 'true'
                          ? 16
                          : 24
                      ">
                      <a-select placeholder="请选择" v-model="model.HasWebPrice" @change="handleHasWebPrice">
                        <a-select-option :value="''"> 请选择 </a-select-option>
                        <a-select-option v-for="(r, rIndex) in [
                            { title: '是', value: 'true' },
                            { title: '否', value: 'false' },
                          ]" :key="rIndex" :value="r.value">
                          {{ r.title }}
                        </a-select-option>
                      </a-select>
                    </a-col>
                    <a-col :span="8" style="text-align: right" v-if="checkBtnPermissions('4e7ad68f-4d0e-43e9-b98e-da94d0ea563e') && model.HasWebPrice == 'true'">
                      <YYLButton menuId="4e7ad68f-4d0e-43e9-b98e-da94d0ea563e" type="primary" text="新增挂网价" @click="$refs.iWebPriceSetModal.show()" />
                    </a-col>
                  </a-row>
                </a-form-model-item>
              </a-col>
              <a-col :span="24" v-if="model.HasWebPrice == 'true'">
                <SimpleTable ref="table" :tab="tab" :columns="columns" @operate="operate">
                  <span slot="commonContent" slot-scope="{ text }">
                    <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                  </span>
                </SimpleTable>
              </a-col>
            </a-row>
            <a-row :gutter="gutter" style="margin-top: 50px">
              <a-col :span="24">
                <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">竞品信息</div>
              </a-col>
              <a-col :span="24" style="margin-bottom: 50px">
                <a-table :columns="columns1" :data-source="data" bordered size="middle" :scroll="{ x: false, y: 240 }" :pagination="false">
                  <template slot="titleHZ" slot-scope="{}">
                    合纵
                    <a @click="
                        $refs.iSalesMaintenanceCommoditySwapModal.show(
                          { AErpGoodsName: model['ErpGoodsName'], ABrandManufacturer: model['BrandManufacturer'] },
                          data,
                          '合纵'
                        )
                      " v-if="checkBtnPermissions('2705bbbd-6072-4bc1-8466-0277596a0492')">
                      <span v-if="data && data.some((v) => v.Id_HZ)">（商品换绑）</span>
                      <span v-else>（商品绑定）</span>
                    </a>
                  </template>
                  <template slot="titleJC" slot-scope="{}">
                    聚创
                    <a v-if="checkBtnPermissions('2705bbbd-6072-4bc1-8466-0277596a0492')" @click="
                        $refs.iSalesMaintenanceCommoditySwapModal.show(
                          { AErpGoodsName: model['ErpGoodsName'], ABrandManufacturer: model['BrandManufacturer'] },
                          data,
                          '聚创'
                        )
                      ">
                      <span v-if="data && data.some((v) => v.Id_JC)">（商品换绑）</span>
                      <span v-else>（商品绑定）</span>
                    </a>
                  </template>
                </a-table>
              </a-col>
            </a-row>
          </template>
        </a-form-model>

      </a-card>
    </a-spin>
    <a-affix :offset-bottom="0" style="float: right; width: 100%; text-align: right">
      <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
        <a-button type="primary" class="mr5" @click="onSubmit">保存</a-button>
        <a-button @click="goBack(true,true)">取消</a-button>
      </a-card>
    </a-affix>
    <!-- 商品换绑 | 绑定 -->
    <SalesMaintenanceCommoditySwapModal ref="iSalesMaintenanceCommoditySwapModal" @ok="queryAllGoodsOriginalPriceContrastList" />
    <WebPriceSetModal ref="iWebPriceSetModal" @ok="handleWebPriceSetModalOk" />
  </div>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const columns1 = [
  {
    title: '云医疗',
    align: 'center',
    children: [
      {
        title: '销售原价(元)',
        dataIndex: 'OriginalPrice',
        key: 'OriginalPrice',
      },
    ],
  },
  {
    children: [
      {
        title: '销售原价(元)',
        dataIndex: 'OriginalPrice_HZ',
        key: 'OriginalPrice_HZ',
      },
      {
        title: '更新时间',
        dataIndex: 'UpdateTime_Price_HZ',
        key: 'UpdateTime_Price_HZ',
      },
    ],
    scopedSlots: { title: 'titleHZ' },
  },
  {
    children: [
      {
        title: '销售原价(元)',
        dataIndex: 'OriginalPrice_JC',
        key: 'OriginalPrice_JC',
      },
      {
        title: '更新时间',
        dataIndex: 'UpdateTime_Price_JC',
        key: 'UpdateTime_Price_JC',
      },
    ],
    scopedSlots: { title: 'titleJC' },
  },
]
export default {
  description: '销售维护',
  name: 'salesMaintenance',
  mixins: [EditMixin, SimpleMixin],
  components: {
    JEllipsis,
  },
  data() {
    return {
      md: 8,
      model: {},
      spinning: false,
      tab: { hideIpagination: true },
      tab1: { hideIpagination: true },
      columns: [
        {
          title: '挂网区域',
          dataIndex: 'Name',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '挂网价',
          dataIndex: 'Price',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },

        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtn: [{ name: '移除', icon: '', id: '7f68e394-46d5-4aa6-a396-82edd7620782' }],
          scopedSlots: { customRender: 'action' },
        },
      ],
      columns1,
      data: [
        // {
        //   key: 1,
        //   name: 'John Brown',
        //   age: 2,
        //   street: 'Lake Park',
        //   building: 'C',
        //   number: 2035,
        //   companyAddress: 'Lake Street 42',
        //   companyName: 'SoftLake Co',
        //   gender: 'M'
        // }
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36006',
      linkUrlType: 'GET',
      id: '',
      linkUrl: {
        list: '/{v}/GoodsSaleManage/GetGoodsDetailAndPrice',
        updateGoodsSettingWebPrice: '/{v}/GoodsSaleManage/UpdateGoodsSettingWebPrice',
        queryAllGoodsOriginalPriceContrastList: '/GoodsPriceAnalyseQuery/QueryAllGoodsOriginalPriceContrastList',
      },
    }
  },
  computed: {
    rules() {
      return {
        OriginalPrice: [{ required: true, message: '请输入!' }],
        EnumGoodsSaleType: [{ required: true, message: '请选择!' }],
        LimitPrice: [{ required: true, message: '请输入!' }],
        SpecialGoodsLimitPrice: [{ required: true, message: '请输入!' }],
        IsTicketPrice: [{ required: true, message: '请选择!' }],
        TicketPrice: [{ required: true, message: '请输入!' }],
        HasWebPrice: [{ required: true, message: '请选择!' }],
        IsMedicalInsurance: [{ required: true, message: '请选择!' }],
        // NotAllowedPurchase: [{ required: true, message: '请选择!' }],
        AdviseSalePrice: [
          {
            required: false,
            // message: '请输入!',
            validator: (rule, value, callback) => {
              if (!value && rule.required) {
                callback(new Error('请输入!'))
                return
              }
              if (rule.required && value < 0.01) {
                callback(new Error('请输入!'))
                return
              }
              // if (!rule.required && value == 0) {
              //   callback(new Error('请输入!'))
              //   return
              // }
              callback()
              return
            },
          },
        ],
      }
    },
  },
  created() { },
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.getGoodsDetail()
    }
  },
  methods: {
    getGoodsDetail() {
      if (!this.id) {
        return
      }
      this.spinning = true
      getAction(this.linkUrl.list, { goodsSpuId: this.id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }

        if (res.Data) {
          res.Data.EnumGoodsSaleType = '' + (res.Data.EnumGoodsSaleType || '')
          res.Data.IsTicketPrice = '' + res.Data.IsTicketPrice
          res.Data.HasWebPrice = '' + res.Data.HasWebPrice
          res.Data.WebPrice = res.Data.WebPrice || []
          res.Data.IsMedicalInsurance = res.Data.IsMedicalInsurance == null ? '' : String(res.Data.IsMedicalInsurance)
          this.$set(this, 'model', res.Data)
          this.$nextTick(() => {
            this.model.IsMedicalInsurance = this.model.IsMedicalInsurance == null ? '' : String(this.model.IsMedicalInsurance)
            // this.model.NotAllowedPurchase =
            //   this.model.NotAllowedPurchase == null ? '' : '' + this.model.NotAllowedPurchase
            if (res.Data['HasWebPrice'] == 'true' && this.$refs.table) {
              this.$refs.table.dataSource = res.Data['WebPrice']
            }
          })
          this.queryAllGoodsOriginalPriceContrastList()
        }
      })
    },
    queryAllGoodsOriginalPriceContrastList() {
      if (!this.model['ErpGoodsId']) {
        return
      }
      postAction(
        this.linkUrl.queryAllGoodsOriginalPriceContrastList,
        { ErpGoodsId: this.model['ErpGoodsId'], AllOriginalPriceContrastType: 99 },
        'P46053'
      ).then((res) => {
        this.spinning = false
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (res.Data) {
          this.data = res.Data
          // console.log(res.Data)
        }
      }).finally(() => {
        this.spinning = false
      })
    },
    // 是否挂网  切换
    handleHasWebPrice() {
      this.$nextTick(() => {
        if (this.model['HasWebPrice'] == 'true' && this.$refs.table) {
          this.$refs.table.dataSource = this.model['WebPrice']
        }
      })
    },
    changeIsMedicalInsurance() {
      this.$nextTick(() => {
        if (this.model['HasWebPrice'] == 'true' && this.$refs.table) {
          this.$refs.table.dataSource = this.model['WebPrice']
        }
      })
    },
    // 新增挂网价回调
    handleWebPriceSetModalOk(formData) {
      let index = (this.model['WebPrice'] || []).findIndex((v) => v['Code'] == formData['Code'])
      if (index < 0) {
        this.model['WebPrice'].push(formData)
      } else {
        this.$set(this.model['WebPrice'], index, formData)
      }
    },
    // 列表操作
    operate(record, type) {
      if (type == '移除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认移除该数据？',
          onOk() {
            let WebPrice = that.model['WebPrice'].filter((v) => v['Code'] != record['Code'])
            that.$set(that.model, 'WebPrice', WebPrice)
            that.$refs.table.dataSource = that.model['WebPrice']
          },
          onCancel() { },
        })
      }
    },
    operate1(record, type) {
      if (type == '删除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认删除本单？删除后不能恢复',
          onOk() {
            that.subDelPurchaseDemand(record)
          },
          onCancel() { },
        })
      }
    },
    reBool(key) {
      return key == '' ? null : key === 'false' ? false : true
    },
    onSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let formData = {
            GoodsSpuId: this.id,
            OriginalPrice: this.model['OriginalPrice'],
            EnumGoodsSaleType: Number(this.model['EnumGoodsSaleType']),
            EnumGoodsSaleTypeStr: this.model['EnumGoodsSaleTypeStr'],
            LimitPrice: this.model['LimitPrice'],
            IsTicketPrice: this.model['IsTicketPrice'] == 'true' ? true : false,
            TicketPrice: this.model['TicketPrice'],
            HasWebPrice: this.model['HasWebPrice'] == 'true' ? true : false,
            WebPrice: this.model['WebPrice'],
            SpecialGoodsLimitPrice: this.model['SpecialGoodsLimitPrice'],
            AdviseSalePrice: this.model['AdviseSalePrice'],
            IsMedicalInsurance: this.reBool(this.model['IsMedicalInsurance']),
            // NotAllowedPurchase: this.reBool(this.model['NotAllowedPurchase']),
          }
          // 合并医保的数据
          let yibaoData = {}
          if (this.model.IsMedicalInsurance == 'true' && this.$refs.HealthInsuranceForm) {
            yibaoData = this.$refs.HealthInsuranceForm.getData() || {}
            if (Object.keys(yibaoData).length === 0) {
              return false
            }
          }
          formData = Object.assign({}, formData, yibaoData)
          // 医保信息无或者否的时候默认为空
          if (!formData.IsMedicalInsurance) {
            formData.IsBaseDrug = null
            formData.IsCentralizedPurchasing = null
            formData.IsOverallPlanning = null
            formData.BaseDrugAreas = []
            formData.CentralizedPurchasingAreas = []
            formData.OverallPlanningAreas = []
            formData.MedicalInsuranceType = null
            formData.HasWebPrice = false
            formData.WebPrice = []
          }
          this.spinning = true
          putAction(this.linkUrl.updateGoodsSettingWebPrice, formData, this.linkHttpHead).then((res) => {
            this.spinning = false
            if (!res.IsSuccess) {
              this.$message.warning(res.Msg)
              return
            }
            this.$message.success('操作成功!')
            setTimeout(() => {
              this.goBack(true,true)
            }, 500)
          })
        } else {
          return false
        }
      })
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
    },
  },
}
</script>

<style lang="less" scoped>
.form-model-style {
  /deep/.ant-form-item-control {
    height: 34px;
  }
}
</style>
