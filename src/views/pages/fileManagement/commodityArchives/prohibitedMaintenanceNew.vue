
<!-- 商品档案 禁售维护 -->
<template>
  <div>
    <a-spin :spinning="spinning">
      <a-card :bordered="false">
        <a-descriptions title="商品信息">
          <a-descriptions-item label="商品名称">
            {{model['ErpGoodsName'] || ' -- '}}
          </a-descriptions-item>
          <a-descriptions-item label="商品编号">
            {{model['ErpGoodsCode'] || ' -- '}}
          </a-descriptions-item>
          <a-descriptions-item label="商品类别">
            {{model['ManageClassifyStr1'] || ' -- '}}
          </a-descriptions-item>
          <a-descriptions-item label="商品规格">
            {{model['PackingSpecification'] || ' -- '}}
          </a-descriptions-item>
          <a-descriptions-item label="生产厂家">
            {{model['BrandManufacturer'] || ' -- '}}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <a-card style="margin:15px 0" :bordered="false">
        <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical">
          <!-- 禁售客户类型配置 -->
          <template>
            <a-row :gutter="gutter">
              <a-col :span="24">
                <div style="font-size: 16px; font-weight: 600;margin:0 0 15px 0">
                  禁售客户类型配置
                </div>
              </a-col>
              <a-col :span="md">

                <a-form-model-item>
                  <a-row :gutter="10" align="middle" type="flex">
                    <a-col :span="8">
                      <MultipleChoiceView style="width: 100%;" placeholder="请选择" tagType="checkbox" v-model="model.CustomerType" @change="onMultipChange" :dataKey=" { name: 'ItemName', value: 'ItemValue' }" ref="iCustomerType" />
                    </a-col>
                    <a-col :span="14">
                      <YYLButton menuId="32fcfb3a-4e5b-41c9-829e-1df4dc56e070" text="保存客户类型配置" type="primary" @click="onSaveCustomerType" />
                    </a-col>
                  </a-row>

                </a-form-model-item>
                <a-divider dashed />
              </a-col>
              <a-col :span="24">
                <a-row :gutter="gutter">
                  <a-col :span="12">
                    <div style="font-size: 16px; font-weight: 600;margin:0 0 15px 0">
                      禁售客户区域配置
                    </div>
                  </a-col>
                  <a-col style="text-align:right" :span="12">
                    <YYLButton menuId="f38315e9-33e1-4a1b-87b1-7d8d23019ad2" text="批量导入" type="primary" @click="() => $refs.iBatchImportAreaModal.show(null)" />
                    <YYLButton menuId="7a717047-228f-43b4-9097-332a2e221472" text="新增" type="primary" @click="$refs.iSelectRestrictedCustomerAreasModal.show($refs.table.dataSource, {goodsSpuId: id})" />
                    <YYLButton menuId="d25ecec2-ca26-425f-9b07-fcb0a919c41f" text="批量移除" @click="goHandleBatchDel(1)" />
                  </a-col>
                </a-row>
              </a-col>
              <a-col :span="24">
                <!-- 列表 -->
                <SimpleTable ref="table" :tab="tab" :linkHttpHead="linkHttpHead" :linkUrl="{
                    list:linkUrl['getGooodsLimitAreaList']
                  }" :linkUrlType="linkUrlType" :queryParam="queryParam" :isTableInitData="isTableInitData" :columns="columns" @operate="operate">
                  <span slot="commonContent" slot-scope="{text}">
                    <j-ellipsis :value="text" :length="15" />
                  </span>
                </SimpleTable>
                <a-divider dashed />
              </a-col>
              <a-col :span="24">
                <a-row :gutter="gutter">
                  <a-col :span="12">
                    <div style="font-size: 16px; font-weight: 600;margin:0 0 15px 0">
                      禁售商销客户名单配置
                    </div>
                  </a-col>
                  <a-col style="text-align:right" :span="12">
                    <YYLButton menuId="dde2a750-2dda-40f8-8821-98fca02465d7" text="批量导入" type="primary" @click="bulkImport(2)" />
                    <YYLButton menuId="56ab5370-fe84-478b-a7a9-a8f4af5fbcff" text="新增" type="primary" @click="$refs.iSelectBannedCustomerListModal.show(2, {
                      http: linkYyyHttpHead,
                      hasSelected: !!$refs.table2.dataSource.length,
                      queryParam: queryParam1
                    })" />
                    <YYLButton menuId="f6298ebc-beb5-475f-8163-5a702a2d0fda" text="批量移除" @click="goHandleBatchDel(2,null,2)" />
                  </a-col>
                </a-row>
              </a-col>
              <a-col :span="24" style="margin-bottom:50px">
                <!-- 筛选 -->
                <SimpleSearchArea ref="SimpleSearchArea2" :searchInput="searchInput2" @searchQuery="searchQuery2" />
                <!-- 列表 -->
                <SimpleTable ref="table2" :tab="tab1" :linkHttpHead="linkYyyHttpHead" :linkUrl="{
                    list:linkUrl['getGooodsLimitCustomerList']
                  }" :linkUrlType="linkUrlType" :queryParam="queryParam" :isTableInitData="isTableInitData" :columns="columns1" @operate="operateTable2">
                  <span slot="commonContent" slot-scope="{text}">
                    <j-ellipsis :value="text" :length="15" />
                  </span>
                </SimpleTable>
              </a-col>

              <a-col :span="24">
                <a-row :gutter="gutter">
                  <a-col :span="12">
                    <div style="font-size: 16px; font-weight: 600;margin:0 0 15px 0">
                      禁售电商客户名单配置
                    </div>
                  </a-col>
                  <a-col style="text-align:right" :span="12">
                    <YYLButton menuId="dde2a750-2dda-40f8-8821-98fca02465d7" text="批量导入" type="primary" @click="bulkImport(1)" />
                    <YYLButton menuId="56ab5370-fe84-478b-a7a9-a8f4af5fbcff" text="新增" type="primary" @click="$refs.iSelectBannedCustomerListModal.show(1,{
                      http: linkYyyHttpHead,
                      hasSelected: !!$refs.table1.dataSource.length,
                      queryParam: queryParam2
                    })" />
                    <YYLButton menuId="f6298ebc-beb5-475f-8163-5a702a2d0fda" text="批量移除" @click="goHandleBatchDel(2,null,1)" />
                  </a-col>
                </a-row>
              </a-col>
              <a-col :span="24" style="margin-bottom:50px">
                <!-- 筛选 -->
                <SimpleSearchArea ref="SimpleSearchArea1" :searchInput="searchInput1" @searchQuery="searchQuery1" />
                <!-- 列表 -->
                <SimpleTable ref="table1" :tab="tab1" :linkHttpHead="linkYyyHttpHead" :linkUrl="{
                    list:linkUrl['getGooodsLimitCustomerList']
                  }" :linkUrlType="linkUrlType" :queryParam="queryParam" :isTableInitData="isTableInitData" :columns="columns2" @operate="operateTable1">
                  <span slot="commonContent" slot-scope="{text}">
                    <j-ellipsis :value="text" :length="15" />
                  </span>
                </SimpleTable>
              </a-col>

            </a-row>
          </template>
        </a-form-model>

      </a-card>
      <a-affix :offset-bottom="0" style="float: right;width: 100%;text-align: right;">
        <a-card :bordered="false" style="border-top:1px solid #e8e8e8">
          <a-button @click="goBack(true,true)">返回</a-button>
        </a-card>

      </a-affix>
    </a-spin>
    <!-- 选择禁售客户区域 -->
    <SelectRestrictedCustomerAreasModalNew ref="iSelectRestrictedCustomerAreasModal" @ok="handleSelectRestrictedCustomerAreasModalOk" />
    <!-- 选择禁售客户名单 -->
    <SelectBannedCustomerListModalNew ref="iSelectBannedCustomerListModal" @ok="handleSelectBannedCustomerListModalOk" />
    <!-- 批量导入 禁售客户区域 -->
    <BatchImportModal ref="iBatchImportAreaModal" @ok="handleBatchImportModalOk('table')" :searchInput="searchInputArea" modalTitle="客户区域批量导入" :columnsD="columnsArea" HttpHead="P36006" linkUrlType="GET" :importUrl="`/v1/GoodsSaleManage/ImportGoodsLimitAreas?goodsSpuId=${id}`" :TempDataUrl="`/{v}/GoodsSaleManage/GetGoodsLimitAreaImportTempList?goodsSpuId=${id}`" TempDataRemoveUrl="/{v}/GoodsSaleManage/DelGoodsLimitAreasTempData" :AddUrl="`/{v}/GoodsSaleManage/SaveGoodsLimitAreasTempData?goodsSpuId=${id}`" batchDelId='f38315e9-33e1-4a1b-87b1-7d8d23019ad2' defaultUrl="商品禁售客户区域导入模板" downloadFileName="商品禁售客户区域导入文档" v-if="id" />
    <!-- 批量导入 商销客户 -->
    <BatchImportModal ref="iBatchImportModal2" @ok="handleBatchImportModalOk('table2')" :searchInput="searchInput1Customer" modalTitle="商销客户批量导入" :columnsD="columns1Customer" HttpHead="P36006" linkUrlType="GET" :importUrl="`/v1/GoodsSaleManage/ImportGoodsLimitCustomer?goodsSpuId=${id}`" :TempDataUrl="`/{v}/GoodsSaleManage/GetGoodsLimitCustomerImportTempList?goodsSpuId=${id}`" TempDataRemoveUrl="/{v}/GoodsSaleManage/DelGoodsLimitCustomerTempData" :AddUrl="`/{v}/GoodsSaleManage/SaveGoodsLimitCustomerTempData?goodsSpuId=${id}`" batchDelId='dde2a750-2dda-40f8-8821-98fca02465d7' defaultUrl="商品禁售商销客户导入模板" downloadFileName="商品禁售商销客户导入模板" v-if="id" />
    <!-- 批量导入 电商客户 -->
    <BatchImportModal ref="iBatchImportModal1" @ok="handleBatchImportModalOk('table1')" :searchInput="searchInput2Customer" modalTitle="电商客户批量导入" :columnsD="columns2Customer" HttpHead="P36006" linkUrlType="GET" :importUrl="`/v1/GoodsSaleManage/ImportGoodsLimitCustomer_YYY?goodsSpuId=${id}`" :TempDataUrl="`/{v}/GoodsSaleManage/GetGoodsLimitCustomerImportTempList?goodsSpuId=${id}`" TempDataRemoveUrl="/{v}/GoodsSaleManage/DelGoodsLimitCustomerTempData" :AddUrl="`/{v}/GoodsSaleManage/SaveGoodsLimitCustomerTempData_YYY?goodsSpuId=${id}`" batchDelId='dde2a750-2dda-40f8-8821-98fca02465d7' defaultUrl="商品禁售电商客户导入模板" downloadFileName="商品禁售电商客户导入模板" v-if="id" />
  </div>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction, deleteAction } from '@/api/manage'
export default {
  description: '禁售维护',
  name: 'prohibitedMaintenanceNew',
  mixins: [EditMixin, SimpleMixin],
  components: {
    JEllipsis,
  },
  data() {
    return {
      md: 24,
      model: {},
      spinning: false,
      tab: {
        rowKey: 'Id',
        rowSelection: {
          type: 'checkbox',
        },
        hideIpagination: true,
      },
      tab1: {
        rowKey: 'Id',
        rowSelection: {
          type: 'checkbox',
        },
      },
      columnsBase: [
        {
          title: '省',
          dataIndex: 'Province',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '市',
          dataIndex: 'City',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '区/县',
          dataIndex: 'County',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns1Base: [
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '客户编号',
          dataIndex: 'CustomerCode',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns2Base: [
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '客户ID',
          dataIndex: 'CustomerCode',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      searchInputArea: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '省', type: 'input', value: '', key: 'Province', defaultVal: '', placeholder: '请输入' },
        { name: '市', type: 'input', value: '', key: 'City', defaultVal: '', placeholder: '请输入' },
        { name: '区/县', type: 'input', value: '', key: 'County', defaultVal: '', placeholder: '请输入' },
      ],
      searchInput1Customer: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '客户', type: 'input', value: '', key: 'CustomerCode', defaultVal: '', placeholder: '请输入名称或编号' },
      ],
      searchInput2Customer: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '客户', type: 'input', value: '', key: 'CustomerCode', defaultVal: '', placeholder: '请输入名称或ID' },
      ],
      queryParam: {

      },
      queryParam1: {

      },
      queryParam2: {},

      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36006',//'Pyyy'
      linkYyyHttpHead: 'P36006',//'Pyyy',
      linkUrlType: 'GET',
      id: '',
      linkUrl: {
        list: '/{v}/GoodsSaleManage/GetGoodsDetailAndLimitCustomerType',
        updateGoodsLimitCustomerType: '/{v}/GoodsSaleManage/UpdateGoodsLimitCustomerTypeV2',
        getGooodsLimitAreaList: '/{v}/GoodsSaleManage/GetGooodsLimitAreaList',
        addGoodsLimitAreaData: '/{v}/GoodsSaleManage/AddGoodsLimitAreaDataV2',
        delGoodsLimitAreaData: '/{v}/GoodsSaleManage/RemoveGoodsLimitAreaDataV2',
        getGooodsLimitCustomerList: '/{v}/GoodsSaleManage/GetGooodsLimitCustomerList',
        addGoodsLimitCustomerData: '/{v}/GoodsSaleManage/AddGoodsLimitCustomerDataV2',
        delGoodsLimitCustomerData: '/{v}/GoodsSaleManage/DelGoodsLimitCustomerDataV2',
      },
      // 商销客户表格筛选
      searchInput2: [
        {
          name: '客户',
          type: 'input',
          value: '',
          key: 'KeyWord',
          defaultVal: '',
          placeholder: '客户名称/编号',
        },
      ],
      // 电商客户表格筛选
      searchInput1: [
        {
          name: '客户',
          type: 'input',
          value: '',
          key: 'KeyWord',
          defaultVal: '',
          placeholder: '客户名称/ID',
        },
      ]
    }
  },
  computed: {
    rules() {
      return {
        name: [{ required: true, message: '请选择!' }],
        AttributeValue: [
          {
            required: this.model.EnumAttributeFormat != 8 && this.model.EnumAttributeFormat != 9 ? false : true,
            validator: (rule, value, callback) => {
              callback()
            },
          },
        ],
      }
    },
    columns() {
      return this.columnsBase.concat([
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtn: [{ name: '移除', icon: '', id: '8199b006-92f3-4371-af1a-90573d635a33' }],
          scopedSlots: { customRender: 'action' },
        },
      ])
    },
    columnsArea() {
      return this.columnsBase.concat([
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtn: [{ name: '移除', icon: '', id: 'f38315e9-33e1-4a1b-87b1-7d8d23019ad2' }],
          scopedSlots: { customRender: 'action' },
        },
      ])
    },
    columns1() {
      return this.columns1Base.concat([
        {
          title: '禁售时间',
          dataIndex: 'LimitTime',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtn: [{ name: '移除', icon: '', id: '4864e40d-e983-4b53-817c-af37426dafc5' }],
          scopedSlots: { customRender: 'action' },
        },
      ])
    },
    columns2() {
      return this.columns2Base.concat([
        {
          title: '禁售时间',
          dataIndex: 'LimitTime',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtn: [{ name: '移除', icon: '', id: '4864e40d-e983-4b53-817c-af37426dafc5' }],
          scopedSlots: { customRender: 'action' },
        },
      ])
    },
    columns1Customer() {
      return this.columns1Base.concat([
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtn: [{ name: '移除', icon: '', id: 'dde2a750-2dda-40f8-8821-98fca02465d7' }],
          scopedSlots: { customRender: 'action' },
        },
      ])
    },
    columns2Customer() {
      return this.columns2Base.concat([
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtn: [{ name: '移除', icon: '', id: 'dde2a750-2dda-40f8-8821-98fca02465d7' }],
          scopedSlots: { customRender: 'action' },
        },
      ])
    },
  },
  created() { },
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.getGoodsDetail()
      this.queryParam['GoodsSpuId'] = this.id
      this.$refs.table.loadDatas(1, this.queryParam)
      let queryParam1 = JSON.parse(JSON.stringify(this.queryParam))
      queryParam1['CustomerSourceType'] = 2
      this.queryParam1 = queryParam1
      this.$refs.table2.loadDatas(1, queryParam1)
      let queryParam2 = JSON.parse(JSON.stringify(this.queryParam))
      queryParam2['CustomerSourceType'] = 1
      this.queryParam2 = queryParam2
      this.$refs.table1.loadDatas(1, queryParam2)
    }
  },
  methods: {
    searchQuery2(queryParam) {
      const params = {
        ...queryParam,
        ...this.queryParam1
      }
      this.$refs.table2.loadDatas(1, params)
    },
    searchQuery1(queryParam) {
      const params = {
        ...queryParam,
        ...this.queryParam2
      }
      this.$refs.table1.loadDatas(1, params)
    },
    getGoodsDetail() {
      if (!this.id) {
        return
      }
      this.spinning = true
      getAction(this.linkUrl.list, { goodsSpuId: this.id }, this.linkHttpHead).then((res) => {
        this.spinning = false
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (res.Data) {
          this.$set(this, 'model', res.Data)
          if ((res.Data.CustomerType || []).length) {
            res.Data.CustomerType.map((v) => {
              v['ItemName'] = v['EnumCustomerTypeStr']
              v['ItemValue'] = v['EnumCustomerType']
            })
            this.$refs.iCustomerType.dataList = res.Data.CustomerType || []
            let CustomerTypes = res.Data.CustomerType.filter((v) => v['IsChecked'])
            // 只回显number
            this.$set(
              this.model,
              'CustomerType',
              (CustomerTypes || []).map((c) => c.EnumCustomerType)
            )
            // 回显number和string
            this.$set(
              this.model,
              'CustomerTypeArray',
              (CustomerTypes || []).map((c) => {
                return {
                  CustomerType: c.EnumCustomerType,
                  CustomerTypeStr: c.EnumCustomerTypeStr,
                }
              })
            )
          }
        }
      })
    },
    // 批量导入
    bulkImport(type) {
      if (type == 2) {
        this.$refs.iBatchImportModal2.show(null, type)
      } else if (type == 1) {
        this.$refs.iBatchImportModal1.show(null, type)
      }

    },
    onMultipChange(e, itemArray) {
      if (itemArray && itemArray.length > 0) {
        itemArray = itemArray.map((item) => {
          return {
            CustomerType: item.EnumCustomerType,
            CustomerTypeStr: item.EnumCustomerTypeStr,
          }
        })
      } else {
        itemArray = []
      }
      this.$set(this.model, 'CustomerTypeArray', itemArray)
    },
    // 保存客户类型配置
    onSaveCustomerType() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let formData = {
            GoodsSpuId: this.id,
            CustomerTypeInt: this.model['CustomerTypeArray'] || []
          }
          this.spinning = true
          putAction(this.linkUrl.updateGoodsLimitCustomerType, formData, this.linkYyyHttpHead).then((res) => {
            this.spinning = false
            if (!res.IsSuccess) {
              this.$message.warning(res.Msg)
              return
            }
            this.$message.success('操作成功!')
          })
        } else {
          return false
        }
      })
    },
    // 列表操作
    operate(record, type) {
      if (type == '移除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认移除?',
          onOk() {
            that.handleBatchDel(1, record)
          },
          onCancel() { },
        })
      }
    },
    operateTable2(record, type) {
      if (type == '移除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认移除?',
          onOk() {
            that.handleBatchDel(2, record, 2)
          },
          onCancel() { },
        })
      }
    },
    operateTable1(record, type) {
      if (type == '移除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认移除?',
          onOk() {
            that.handleBatchDel(2, record, 1)
          },
          onCancel() { },
        })
      }
    },
    // 批量删除
    goHandleBatchDel(type, obj = null, CustomerSourceType) {
      if (!obj && (this.$refs[type == 1 ? 'table' : (CustomerSourceType == 2 ? 'table2' : 'table1')].selectedRows || []).length < 1) {
        this.$message.warning('请选择数据!')
        return
      }
      let that = this
      this.$confirm({
        title: '提示',
        content: '确认批量移除?',
        onOk() {
          that.handleBatchDel(type, null, CustomerSourceType)
        },
        onCancel() { },
      })
    },
    onSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          alert('submit!')
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
    },
    // 选择禁售客户区域
    handleSelectRestrictedCustomerAreasModalOk(list) {
      postAction(
        this.linkUrl['addGoodsLimitAreaData'],
        {
          GoodsSpuId: this.id,
          Codes: list.map((v) => v['AreaCode']),
        },
        this.linkYyyHttpHead
      ).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    // 选择禁售客户名单
    handleSelectBannedCustomerListModalOk(list, type) {
      let array = []
      if (list.length) {
        list.map((v) => {
          array.push({
            Id: type == 2 ? v.Code : v.Id.toString(),
            Name: v.Name,
            Address: type == 2 ? v.RegAddress : v.Address,
            CustomerType: v.CustomerType || 0,
          })
        })
      }
      postAction(
        this.linkUrl['addGoodsLimitCustomerData'],
        {
          GoodsSpuId: this.id,
          CustomerSourceType: type == 2 ? 2 : 1,
          CustomerList: array,
        },
        this.linkYyyHttpHead
      ).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        let queryParam = JSON.parse(JSON.stringify(this.queryParam))
        queryParam['CustomerSourceType'] = type
        if (type == 2) {
          this.$refs.table2.loadDatas(1, queryParam)
        } else {
          this.$refs.table1.loadDatas(1, queryParam)
        }
      })
    },
    // 全部移除
    // type 1 : 禁售客户区域配置  | 2 : 禁售客户名单配置
    handleBatchDel(type, obj = null, CustomerSourceType) {
      if (!obj && (this.$refs[type == 1 ? 'table' : (CustomerSourceType == 2 ? 'table2' : 'table1')].selectedRows || []).length < 1) {
        this.$message.warning('请选择数据!')
        return
      }
      let params = { GoodsSpuId: this.id }
      let Codes = []
      if (obj) {
        if (type == 1) {
          params['Codes'] = obj.Codes
        } else {
          params['CustomerList'] = [{
            Id: obj.Code,
            Name: obj.CustomerName,
            Address: obj.RegAddress,
            CustomerType: obj.CustomerType || 0,
          }]
          params['CustomerSourceType'] = CustomerSourceType
        }
      } else {
        if (type == 1) {
          this.$refs[type == 1 ? 'table' : (CustomerSourceType == 2 ? 'table2' : 'table1')].selectedRows.map((v) => {
            Codes = type == 1 ? Codes.concat(v['Codes']) : Codes.concat(v['Code'])
          })
          params['Codes'] = Codes
        } else {
          this.$refs[type == 1 ? 'table' : (CustomerSourceType == 2 ? 'table2' : 'table1')].selectedRows.map((v) => {
            Codes.push({
              Id: v.Code,
              Name: v.CustomerName,
              Address: v.RegAddress,
              CustomerType: v.CustomerType || 0,
            })
          })
          params['CustomerList'] = Codes
          params['CustomerSourceType'] = CustomerSourceType
        }

      }
      let url = type == 1 ? this.linkUrl.delGoodsLimitAreaData : this.linkUrl.delGoodsLimitCustomerData
      deleteAction(url, params, this.linkYyyHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        this.queryParam['CustomerSourceType'] = CustomerSourceType
        this.$refs[type == 1 ? 'table' : (CustomerSourceType == 2 ? 'table2' : 'table1')].loadDatas(1, this.queryParam)
        this.$refs[type == 1 ? 'table' : (CustomerSourceType == 2 ? 'table2' : 'table1')].clearSelected()
      })
    },
    // 导入保存
    handleBatchImportModalOk(key) {
      let CustomerSourceType = null
      if (key == 'table2') {
        CustomerSourceType = 2
        this.queryParam['CustomerSourceType'] = CustomerSourceType
      } else if (key == 'table1') {
        CustomerSourceType = 1
        this.queryParam['CustomerSourceType'] = CustomerSourceType
      }
      this.$refs[key].loadDatas(null, this.queryParam)
    },
  },
}
</script>

<style>
</style>