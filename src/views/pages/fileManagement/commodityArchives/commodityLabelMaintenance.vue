<!-- 标签维护-->
<template>
  <a-spin :spinning="loading">
    <a-card style="margin-bottom:10px">
      <a-descriptions title="商品信息">
        <a-descriptions-item label="商品名称">{{ info.ErpGoodsName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="商品编号">{{ info.ErpGoodsCode || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="商品类别">{{ info.ManageClassifyStr1 || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="商品规格">{{ info.PackingSpecification || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="生产厂家">{{ info.BrandManufacturer || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
    </a-card>
    <a-card :bordered="false" style="min-height:50vh">
      <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical">
        <a-row :gutter="gutter">
          <a-col :span="md">

            <a-form-model-item label="采购管理模式：" prop="ProcurementManagementModelCode">
              <SingleChoiceView style="width: 100%;" placeholder="请选择" :httpParams="{
                  groupPY: 'cgglms'
                }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'ItemPym' }" v-model="model.ProcurementManagementModelCode" :Url="url.getListDictItem" @change="(val, txt, item) => (model.ProcurementManagementModelName = txt)" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item label="季节性品种：" prop="IsSeasonal">
              <a-select placeholder="请选择" v-model="model.IsSeasonal">
                <a-select-option :value="''"> 请选择 </a-select-option>
                <a-select-option v-for="(r, rIndex) in [{title:'是',value:'true'},{title:'否',value:'false'}]" :key="rIndex" :value="r.value">
                  {{ r.title }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item label="是否暂不经营品种：" prop="NotAllowedPurchase">
              <a-select placeholder="请选择" v-model="model.NotAllowedPurchase">
                <a-select-option :value="''"> 请选择 </a-select-option>
                <a-select-option v-for="(r, rIndex) in [
                  { title: '是', value: 'true' },
                  { title: '否', value: 'false' },
                ]" :key="rIndex" :value="r.value">
                  {{ r.title }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="gutter">
          <a-col :span="md">
            <a-form-model-item label="是否有药品追溯码：" prop="IsHasGoodsTraceCode">
              <a-select placeholder="请选择" v-model="model.IsHasGoodsTraceCode">
                <a-select-option :value="''"> 请选择 </a-select-option>
                <a-select-option v-for="(r, rIndex) in [
                  { title: '是', value: 'true' },
                  { title: '否', value: 'false' },
                ]" :key="rIndex" :value="r.value">
                  {{ r.title }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-card>
    <a-card :bordered="false">
      <a-affix :offset-bottom="0" style="float: right;width: 100%;text-align: right;">
        <a-card :bordered="false" style="border-top:1px solid #e8e8e8">
          <!-- <a-button
            type="primary"
            class="mr8"
            v-if="checkBtnPermissions('372d7b04-5d59-4e71-adb8-6daf8afd693a')"
            @click="onSavePagaDataClick"
          >保存</a-button> -->
          <YYLButton menuId="372d7b04-5d59-4e71-adb8-6daf8afd693a" text="保存" type="primary" @click="onSavePagaDataClick" />
          <a-button @click="goBack(true,true)">取消</a-button>
        </a-card>

      </a-affix>
    </a-card>
  </a-spin>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'commodityLabelMaintenance',
  mixins: [EditMixin],
  data() {
    return {
      info: {},
      model: {
        ProcurementManagementModelCode: '',
        ProcurementManagementModelName: '',
        IsSeasonal: '',
        NotAllowedPurchase: ''
      },
      md: 8,
      loading: false,
      id: '',
      httpHead: 'P36006',
      url: {
        detail: '/{v}/GoodsManage/GetGoodsBaseInfo',
        getListDictItem: '/{v}/Global/GetListDictItem',
        editGoodsLabel: '/{v}/GoodsManage/EditGoodsLabel'
      }
    }
  },
  computed: {
    rules() {
      return {
        ProcurementManagementModelCode: [{ required: true, message: '请选择!' }],
        IsSeasonal: [{ required: true, message: '请选择!' }],
        NotAllowedPurchase: [{ required: true, message: '请选择!' }],
        IsHasGoodsTraceCode: [{ required: true, message: '请选择!' }]
      }
    }
  },
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.loadDetailInfo()
    }
  },
  created() { },
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { goodsSpuId: that.id }, that.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            that.info = res.Data || {}
            that.model = {
              ProcurementManagementModelCode: that.info['ProcurementManagementModelCode'] || '',
              ProcurementManagementModelName: that.info['ProcurementManagementModelName'] || '',
              IsSeasonal: that.info['IsSeasonal'] == null ? '' : '' + that.info['IsSeasonal'],
              NotAllowedPurchase: that.info['NotAllowedPurchase'] == null ? '' : '' + that.info['NotAllowedPurchase'],
              IsHasGoodsTraceCode: that.info['IsHasGoodsTraceCode'] == null ? '' : '' + that.info['IsHasGoodsTraceCode'],
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    setData(data) {
      if ((data || []).length < 1) {
        return null
      }
      data.map(group => {
        if ((group.Qualifications || []).length) {
          group.Qualifications.forEach(item => {
            item['QualificationValue'] = item.Url
          })
        } else {
          group['Qualifications'] = [
            {
              QualificationName: group['GroupName'],
              QualificationCode: group['GroupCode'],
              QualificationType: 1,
              QualificationTypeStr: '图片',
              IsMust: group['IsMust'],
              IsShow: true
            }
          ]
        }
      })
      return data
    },

    onSavePagaDataClick() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          let formData = Object.assign({}, this.model)
          formData['GoodsSpuId'] = this.id
          formData['IsSeasonal'] = formData['IsSeasonal'] == '' ? null : formData['IsSeasonal'] == 'true' ? true : false
          formData['NotAllowedPurchase'] = formData['NotAllowedPurchase'] == '' ? null : formData['NotAllowedPurchase'] == 'true' ? true : false
          formData['IsHasGoodsTraceCode'] = formData['IsHasGoodsTraceCode'] == '' ? null : formData['IsHasGoodsTraceCode'] == 'true' ? true : false
          if (!formData) {
            return
          }
          this.loading = true
          postAction(this.url.editGoodsLabel, formData, this.httpHead).then(res => {
            this.loading = false
            if (!res.IsSuccess) {
              this.$message.error(res.Msg)
              return
            }
            this.$message.success('操作成功！')
            setTimeout(() => {
              this.goBack(true, true)
            }, 500)
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
