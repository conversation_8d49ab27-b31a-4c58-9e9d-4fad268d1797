<!-- 商品标签管理 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="onSetValid"
      @operate="operate"
      @changeTab="changeTab"
    />
    <!-- 导入标签 -->
    <!-- 取消 exportId="d53928e0-479d-4d9e-addb-9ee598b722b1" -->
    <BatchImportModal
      ref="iBatchImportLabelModal"
      @ok="handleBatchImportModalOk"
      :searchInput="searchInputLabel"
      queryParamStatusKey="QueryType"
      modalTitle="商品标签导入"
      :columnsD="columnsLabel"
      HttpHead="P36006"
      linkUrlType="GET"
      linkUrlAddType="POST"
      linkUrlExportType="POST"
      :importUrl="`/v1/GoodsLabelManage/ImportGoodsLableDataTemp`"
      :TempDataUrl="`/{v}/GoodsLabelManage/GetGoodsLabelImportTempList`"
      TempDataRemoveUrl="/{v}/GoodsLabelManage/DeleteGoodsLableDataTemp"
      :AddUrl="`/{v}/GoodsLabelManage/ImportLableDataConfirm`"
      batchDelId="bbf67026-1587-43c2-a9b0-aafe753035f4"
      defaultUrl="商品标签导入模板"
      downloadFileName="商品标签导入文档"
      exceptionField="ErrorInfo"
      saveDataQuery="id"
      noLoadData
      exportUrl="/{v}/GoodsLabelManage/ExportGoodsLabelException"
    />
    <!-- 品种转移 -->
    <!-- 取消 exportId="65aed08a-57dc-4fd3-b560-a3708e5e4c19" -->
    <BatchImportModal
      ref="iBatchImportTransferModal"
      @ok="handleBatchImportModalOk"
      :searchInput="searchInputTransfer"
      queryParamStatusKey="QueryType"
      modalTitle="品种转移"
      :columnsD="columnsTransfer"
      HttpHead="P36006"
      linkUrlType="GET"
      linkUrlAddType="POST"
      linkUrlExportType="POST"
      :importUrl="`/v1/GoodsLabelManage/ImportGoodsPurchaserDataTemp`"
      :TempDataUrl="`/{v}/GoodsLabelManage/GetGoodsPurchaserImportTempList`"
      TempDataRemoveUrl="/{v}/GoodsLabelManage/DeleteGoodsPurchaserDataTemp"
      :AddUrl="`/{v}/GoodsLabelManage/ImportPurchaserDataConfirm`"
      batchDelId="bbf67026-1587-43c2-a9b0-aafe753035f4"
      defaultUrl="商品批量品种转移模板"
      downloadFileName="商品批量品种转移导入文档"
      saveDataQuery="id"
      exceptionField="ErrorInfo"
      noLoadData
      exportUrl="/{v}/GoodsLabelManage/ExportGoodsPurchaserException"
    />
    
    <!-- 导入记录 -->
    <LabelImportRecordModal ref="LabelImportRecordModal" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商品标签管理',
  name: 'commodityLabelManageList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '商品',
          type: 'input',
          value: '',
          key: 'KeyWord',
          defaultVal: '',
          placeholder: '名称/编号/拼音码/SPZ码',
        },
        {
          name: '商品类别',
          type: 'selectLink',
          vModel: 'GoodsManageClassify1',
          dataKey: { name: 'Name', value: 'Id' },
          httpParams: { Name: '', Level: 1 },
          keyWord: 'Name',
          defaultVal: '',
          placeholder: '请选择',
          httpType: 'POST',
          url: '/{v}/GoodsManage/QueryGoodsManageClassifyList',
          httpHead: 'P36006',
        },
        { name: '采购人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '采购管理模式',
          type: 'selectLink',
          vModel: 'ProcurementManagementModelCode',
          dataKey: { name: 'ItemValue', value: 'ItemPym' },
          httpParams: { groupPY: 'cgglms' },
          keyWord: 'Name',
          defaultVal: '',
          placeholder: '请选择',
          httpType: 'GET',
          url: '/{v}/Global/GetListDictItem',
          httpHead: 'P36001',
        },
        {
          name: '季节性品种',
          type: 'select',
          value: '',
          key: 'IsSeasonal',
          defaultVal: [
            { title: '是', value: 'true' },
            { title: '否', value: 'false' },
          ],
          placeholder: '请选择',
        },
        {
          name: '是否暂不经营',
          type: 'select',
          value: '',
          key: 'NotAllowedPurchase',
          defaultVal: [
            { title: '是', value: 'true' },
            { title: '否', value: 'false' },
          ],
          placeholder: '请选择',
        },
        {
          name: '是否有药品追溯码',
          type: 'select',
          value: '',
          key: 'IsHasGoodsTraceCode',
          defaultVal: [
            { title: '是', value: 'true' },
            { title: '否', value: 'false' },
          ],
          placeholder: '请选择',
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          {
            name: '品种批量转移',
            type: 'primary',
            icon: '',
            key: 'transfer',
            id: '21989d15-120d-4e52-92f6-15199c8e9928',
          },
          { name: '导入标签', type: 'primary', icon: '', key: 'label', id: 'c72190f7-7318-4cc7-a655-43d78e28177e' },
          { name: '导入记录', type: 'primary', icon: '', key: 'record', id: '3802ed8d-2d99-4d57-afd2-d0df6b4a244b' },
        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: 'SPZ码',
          dataIndex: 'ERPGoodsId',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品类别',
          dataIndex: 'ManageClassifyStr1',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'purchaseStatus' },
        },
        {
          title: '商品规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购人',
          dataIndex: 'PurchaserName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购管理模式',
          dataIndex: 'ProcurementManagementModelName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否暂不经营',
          dataIndex: 'NotAllowedPurchase',
          width: 150,
          ellipsis: true,
          customRender: (v, r, i) => {
            return v == null ? ' -- ' : v ? '是' : '否'
          },
        },
        {
          title: '季节性品种',
          dataIndex: 'IsSeasonal',
          width: 150,
          ellipsis: true,
          customRender: (v, r, i) => {
            return v == null ? ' -- ' : v ? '是' : '否'
          },
        },
        {
          title: '是否有药品追溯码',
          dataIndex: 'IsHasGoodsTraceCode',
          width: 150,
          ellipsis: true,
          customRender: (v, r, i) => {
            return v == null ? ' -- ' : v ? '是' : '否'
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          fixed: 'right',
          actionBtn: [{ name: '标签维护', icon: '', id: '79f91686-d87e-4e4a-a36a-efdd14fbd556' }],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {
        AuthBusinessStatus: 2,
      },
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36006',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '/{v}/GoodsManage/QueryGoodsAuditPasssList',
      },
      searchInputLabel: [
        { name: '商品编码', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '请输入' },
      ],
      columnsLabel: [
        {
          title: '商品编码',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否暂不经营',
          dataIndex: 'NotAllowedPurchaseStr',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否季节性品种',
          dataIndex: 'IsSeasonalStr',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购管理模式',
          dataIndex: 'ProcurementManagementModelName',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否有药品追溯码',
          dataIndex: 'IsHasGoodsTraceCode',
          ellipsis: true,
          customRender: (v, r, i) => {
            return v == null ? ' -- ' : v ? '是' : '否'
          },
        },
        {
          title: '异常原因',
          dataIndex: 'ErrorInfo',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtn: [{ name: '移除', icon: '', id: 'bbf67026-1587-43c2-a9b0-aafe753035f4' }],
          scopedSlots: { customRender: 'action' },
        },
      ],
      searchInputTransfer: [
        { name: '商品编码', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '请输入' },
        { name: '采购人', type: 'input', value: '', key: 'Purchaser', defaultVal: '', placeholder: '请输入' },
      ],
      columnsTransfer: [
        {
          title: '商品编码',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购人',
          dataIndex: 'PurchaserName',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '异常原因',
          dataIndex: 'ErrorInfo',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtn: [{ name: '移除', icon: '', id: 'bbf67026-1587-43c2-a9b0-aafe753035f4' }],
          scopedSlots: { customRender: 'action' },
        },
      ],
    }
  },
  created() {},
  // mounted() {
  //   this.queryParam['AuthBusinessStatus'] = 2
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  activated() {
    this.queryParam['AuthBusinessStatus'] = 2
    // this.$refs.table.loadDatas(null, this.queryParam)
    this.searchQuery(this.$refs.SimpleSearchArea.queryParam, null, undefined)
  },
  methods: {
    handleBatchImportModalOk() {
      this.searchQuery(this.$refs.SimpleSearchArea.queryParam, null, undefined)
    },
    searchQuery(queryParam, type, page) {
      queryParam.AuthBusinessStatus = 2
      let params = JSON.parse(JSON.stringify(queryParam))
      if (queryParam.IsSeasonal) {
        params.IsSeasonal = queryParam.IsSeasonal == 'true' ? true : false
      }
      if (queryParam.NotAllowedPurchase) {
        params.NotAllowedPurchase = queryParam.NotAllowedPurchase == 'true' ? true : false
      }
      if (queryParam.IsHasGoodsTraceCode) {
        params.IsHasGoodsTraceCode = queryParam.IsHasGoodsTraceCode == 'true' ? true : false
      }
      if (page === undefined) {
        page = null
      } else {
        page = 1
      }
      this.$refs.table.loadDatas(page, params)
    },
    // 列表操作
    operate(record, type) {
      if (type === 'transfer') {
        // console.log(`品种批量转移`);
        let that = this
        this.$confirm({
          title: '品种批量转移只是转移品种，不会将品种对应的单据、账目同时转移，是否要继续？',
          okText: '继续',
          onOk() {
            that.$refs.iBatchImportTransferModal.show(null)
          },
          onCancel() {},
        })
      } else if (type === 'label') {
        console.log(`导入标签`)
        this.$refs.iBatchImportLabelModal.show(null)
      } else if (type === 'record') {
        console.log(`导入记录`)
        this.$refs.LabelImportRecordModal.show()
      } else if (type == '标签维护') {
        this.onDetailClick('commodityLabelMaintenance', { id: record.Id })
      }
    },
  },
}
</script>

<style></style>
