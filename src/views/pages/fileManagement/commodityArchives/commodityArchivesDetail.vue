<!-- 商品详情-->
<template>
  <div>
    <a-card :bordered="false">
      <a-tabs v-model="activeTabKey">
        <a-tab-pane :key="1" tab="基础信息">
          <BasicInfoTemplateForm
            ref="iBasicInfoTemplateForm"
            :opType="acType"
            :Info="model"
            v-if="activeTabKey == 1 && model"
          />
        </a-tab-pane>
        <a-tab-pane :key="2" tab="首营信息" v-if="!notPrimaryInfoBool">
          <PrimaryInfoTemplateForm
            ref="iPrimaryInfoTemplateForm"
            v-if="activeTabKey === 2 && model"
            :opType="acType"
            :id="id"
            :fileNo="model['FileNo']"
            :erpGoodsId="model['ErpGoodsId']"
            :goodsManageClassifyId="model['ManageClassify2']"
            :hasList="qList"
            :remark="model['Remark']"
            :BusinessSerialNumber="BusinessSerialNumber"
            :AppendInfo="AppendInfo"
          />
        </a-tab-pane>
        <a-tab-pane :key="3" tab="审核信息" v-if="!notPrimaryInfoBool">
          <SupAuditInformation
            ref="supAuditInformation"
            :bussinessNo="BusinessSerialNumber"
            v-if="activeTabKey === 3 && id"
          />
        </a-tab-pane>
      </a-tabs>
    </a-card>
    <a-affix :offset-bottom="0" style="float: right; width: 100%; text-align: right">
      <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
        <!-- <a-button
          type="primary"
          class="mr8"
          v-if="checkBtnPermissions('fa1f0c69-6f65-4772-b283-b54c989d42ff') && activeTabKey != 3 && isEdit"
          @click="onSavePagaDataClick"
        >保存当前页</a-button> -->
        <YYLButton
          menuId="fa1f0c69-6f65-4772-b283-b54c989d42ff"
          text="保存当前页"
          type="primary"
          v-if="activeTabKey != 3 && isEdit"
          @click="onSavePagaDataClick"
        />
        <a-button @click="goBack(true,true)">{{ isEdit && activeTabKey != 3 ? '取消' : '返回' }}</a-button>
      </a-card>
    </a-affix>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'commodityArchivesDetail',
  mixins: [EditMixin],
  data() {
    return {
      activeTabKey: 1,
      model: null,
      qList: [], //资质list
      loading: false,
      acType: 4, //1 新增（此界面不使用）  2 采购编辑（此界面不使用） 3质管编辑 4详情  (详情界面只会出现 3 和 4)
      id: '',
      BusinessSerialNumber: null,
      notPrimaryInfoBool: false,
      AppendInfo: '',
      httpHead: 'P36006',
      url: {
        detail: '',
        detail2: '/{v}/GoodsManage/GetGoodsBaseInfoForQualityManagement',
        detail4: '/{v}/GoodsManage/GetGoodsBaseInfoForDetail',
        getGoodsAdditionalInformationDetail: '/{v}/GoodsManage/GetGoodsAdditionalInformationDetail',
        editGoodsSpu: '/{v}/GoodsManage/EditGoodsSpu',
        editGoodsSpu_ForQualityManagement: '/{v}/GoodsManage/EditGoodsSpu_ForQualityManagement',
      },
    }
  },
  computed: {
    isEdit() {
      return this.acType === 3
    },
  },
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.acType = this.$route.query.acType ? Number(this.$route.query.acType) : 4
      // let auditStatus = this.$route.query.auditStatus ? Number(this.$route.query.auditStatus) : 0
      this.url.detail = this.url.detail2 //this.acType == 3 ? this.url.detail2 : auditStatus != 1 ? this.url.detail2 : this.url.detail4
      this.setTitle(this.acType == 3 ? '商品质管编辑' : '商品档案详情')
      this.loadDetailInfo()
    }
  },
  created() {},
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { goodsSpuId: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            this.AppendInfo = ''
            if (
              this.model.ManageClassifyCode1 == 'YLQX' &&
              this.model.BusinessScopeName &&
              this.model.BusinessScopeName.startsWith('I')
            ) {
              this.AppendInfo = '1'
            }
            if (
              (this.model.ManageClassifyCode2 == 'YP_2' || this.model.ManageClassifyCode2 == 'JKYP_2') &&
              this.model['SupplierType']
            ) {
              this.AppendInfo = '' + this.model['SupplierType']
            }
            // console.log(this.AppendInfo)
            this.changeManageClassify(this.model.ManageClassifyCode1)
            if (this.model.ManageClassifyCode1 != 'ZP') that.getGoodsAdditionalInformationDetail()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    setData(data) {
      if ((data || []).length < 1) {
        return null
      }
      data.map((group) => {
        if ((group.Qualifications || []).length) {
          group.Qualifications.forEach((item) => {
            item['QualificationValue'] = item.Urls
          })
        } else {
          // group['Qualifications'] = [
          //   {
          //     QualificationName: group['GroupName'],
          //     QualificationCode: group['GroupCode'],
          //     QualificationType: 1,
          //     QualificationTypeStr: '图片',
          //     IsMust: group['IsMust'],
          //     IsShow: true
          //   }
          // ]
        }
      })
      return data
    },
    getGoodsAdditionalInformationDetail() {
      let that = this
      that.loading = true
      getAction(that.url.getGoodsAdditionalInformationDetail, { goodsSpuId: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            // console.log('Data       ', res.Data)

            if (((res.Data && res.Data['Items']) || []).length) {
              that.qList = this.setData(res.Data['Items'])
            }
            this.model['Remark'] = (res.Data && res.Data['Remark']) || ''
            this.BusinessSerialNumber = (res.Data && res.Data['BusinessSerialNumber']) || null
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    onSavePagaDataClick() {
      if (this.activeTabKey == 1) {
        let formData = this.$refs.iBasicInfoTemplateForm.onSubmit()
        if (!formData) {
          return
        }
        this.$refs.iBasicInfoTemplateForm.loading = true
        postAction(
          this.acType == 3 ? this.url.editGoodsSpu_ForQualityManagement : this.url.editGoodsSpu,
          formData,
          this.httpHead
        ).then((res) => {
          this.$refs.iBasicInfoTemplateForm.loading = false
          if (!res.IsSuccess) {
            this.$message.error(res.Msg)
            return
          }
          this.$message.success('操作成功！')
          setTimeout(() => {
            // this.$router.go(-1)
            this.loadDetailInfo()
          }, 500)
        })
      } else {
        if (!this.$refs.iPrimaryInfoTemplateForm) {
          return
        }
        this.$refs.iPrimaryInfoTemplateForm.editSaveData(false, (bool) => {
          // this.spinning = false
          if (bool) {
            setTimeout(() => {
              // this.getGoodsAdditionalInformationDetail()
              this.goBack(true,true)
            }, 500)
          }
        })
      }
    },
    changeManageClassify(ManageClassify1Code) {
      this.notPrimaryInfoBool = ManageClassify1Code == 'ZP' ? true : false
    },
  },
}
</script>

<style scoped>

</style>
