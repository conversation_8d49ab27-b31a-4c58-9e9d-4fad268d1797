<template>
  <a-spin :spinning="loading">
    <div class="rflex vcenter mb15" v-if="[3, 4].includes(opType) && !isColorCertification">
      <span>档案编号：</span>
      <SingleChoiceSearchView
        style="width: 200px"
        placeholder="请选择或生成编号"
        httpHead="P36001"
        :httpParams="{ IdPrefixType: 206 }"
        :keyWord="'KeyWord'"
        Url="/{v}/Global/GetCodeRecords"
        :dataKey="{ name: 'Code', value: 'Code' }"
        v-model="FileNo"
        :name="FileNo"
        :disabled="opType == 4"
        @change="onFileNoChange"
      />
      <a-button
        v-if="!FileNo && opType == 3"
        type="primary"
        class="ml15"
        @click="onShengChengBianHaoClick"
        :loading="fileNoLoading"
        >生成编号</a-button
      >
    </div>
    <FirstCampInformation
      ref="firstCampInformation"
      :allList="dataList"
      addGroupBtnId="85bbdf0a-8548-458c-8314-9421e25aac10"
      :isEdit="opType == 4 ? false : true"
      :remark="remark"
      :showRemark="!isColorCertification"
      :showZlxs="showZlxs"
      :zlxsVal="FileId ? 2 : 1"
      :zcLoading="zcLoading"
      @getZCData="getZCData"
    />
    <ZhuanChunQualificationModel ref="zhuanChunQualificationModel" @zcOk="onZhuanChunCallback" />
  </a-spin>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'

import { getAction, postAction } from '@/api/manage'
export default {
  name: 'PrimaryInfoTemplateForm',
  props: {
    /**
     * 商品信息
     */
    goods: {
      type: Object,
      default: () => {
        return {
          ErpGoodsName: '', //商品名称
          SupplierName: '', //供应商名称
          BrandManufacturer: '', //厂家
        }
      },
    },
    /**
     * 1 新增  2 采购编辑 3质管编辑 4详情
     */
    opType: {
      type: Number,
      default: 1,
      required: true,
      validator: (s) => [1, 2, 3, 4].includes(s),
    },
    /**
     * erpGoodsId：
     */
    erpGoodsId: {
      type: String,
      default: '',
    },
    /**
     * 商品分类：
     */
    goodsManageClassifyId: {
      type: String,
      default: '',
    },
    /**
     * 商品分类：
     */
    ManageClassify2Code: {
      type: String,
      default: '',
    },
    /**
     * 来货单位
     */
    SupplierType: {
      type: Number,
      default: null,
    },
    /**
     * 商品id
     */
    id: {
      type: String,
      default: '',
    },
    /**
     * 已填资质项
     */
    hasList: {
      type: Array,
      default: () => {
        return []
      },
    },
    // 备注
    remark: {
      type: String,
      default: '',
    },
    // 是否是七彩
    isColorCertification: {
      type: Boolean,
      default: false,
    },
    // 是否是七彩
    isGoodsQualificationExist: {
      type: Boolean,
      default: true,
    },
    //
    BusinessSerialNumber: {
      type: String,
      default: '',
    },
    //
    FileId: {
      type: String,
      default: '',
    },
    //
    GoodsAdditionalInformationId: {
      type: String,
      default: '',
    },
    // 医疗器械 一类医疗器械就传1
    // 药品 进口药品  ，批发企业填1，生产企业填2
    //，其他不填
    AppendInfo: {
      type: String,
      default: '',
    },
    /**
     * 档案编号
     */
    fileNo: {
      type: String,
      default: () => {
        return ''
      },
    },
  },
  data() {
    return {
      loading: false,
      dataList: [],
      httpHead: 'P36006',
      zcLoading: false,
      fileNoLoading: false,
      FileNo: '', //编码
      url: {
        dataUrl: '/{v}/GoodsManage/QueryGoodsManageClassifyAdditionalInfoGroupSettings',
        add: '/{v}/GoodsManage/AddGoodsAdditionalInformation',
        edit: '',
        edit2: '/{v}/GoodsManage/SaveGoodsAdditionalInformationForPurchasing',
        edit3: '/{v}/GoodsManage/SaveGoodsAdditionalInformationForQualityManagement',
        edit4: '/{v}/Goods/EditGoodsCAAdditionalInformation', // 七彩认证编辑资质
        createApproval: '/{v}/ApprovalWorkFlowInstance/CreateApproval', // 创建审核实例
        getGoodsQulificationFromCA: '/{v}/Goods/GoodsQulificationFromCA2', // 从七彩获取商品资质
        createAndSaveNo: '/{v}/GoodsManage/BuildGoodsFileNo',
        saveFileNo: '/{v}/GoodsManage/SetGoodsFileNo',
      },
    }
  },

  computed: {
    showZlxs() {
      return (
        ['YP_2', 'JKYP_2'].includes(this.ManageClassify2Code) &&
        this.SupplierType == 1 &&
        !this.isColorCertification &&
        [1, 2].includes(this.opType)
      )
    },
  },
  mounted() {
    //是否显示备注 true edit = edit4
    //是否显示备注 false this.opType == 3 eidt= edit3 this.opType == 2 edit = eidt2  非2或3 edit = add
    this.url.edit = this.isColorCertification
      ? this.url.edit4
      : this.opType == 3
      ? this.url.edit3
      : this.opType == 2
      ? this.url.edit2
      : this.url.add
    this.FileNo = this.fileNo
    this.loadFirstCampData()
  },
  created() {},
  methods: {
    loadFirstCampData() {
      this.loading = true
      postAction(
        this.url.dataUrl,
        { GoodsManageClassifyId: this.goodsManageClassifyId, AppendInfo: this.AppendInfo },
        this.httpHead
      )
        .then((res) => {
          if (res.IsSuccess) {
            if ((res.Data || []).length) {
              res.Data.map((v, index) => {
                v.MaxCount = 1
                v['Qualifications'] = [
                  {
                    QualificationName: v['GroupName'],
                    QualificationCode: v['GroupCode'],
                    QualificationType: 1,
                    QualificationTypeStr: '图片',
                    IsMust: true,
                    IsShow: true,
                  },
                ]
                // console.log('this.hasList                        ', this.hasList)
                if ((this.hasList || []).length) {
                  let index = this.hasList.findIndex((j) => j['GroupCode'] == v['GroupCode'])
                  if (index > -1) {
                    if (!v.IsMust) {
                      v.IsAdd = true
                    }
                    v['NeedToCAContrast'] = this.hasList[index].NeedToCAContrast
                    v['Qualifications'] = (this.hasList[index].Qualifications || []).length
                      ? this.hasList[index].Qualifications
                      : [
                          {
                            QualificationName: v['GroupName'],
                            QualificationCode: v['GroupCode'],
                            QualificationType: 1,
                            QualificationTypeStr: '图片',
                            IsMust: true,
                            IsShow: true,
                          },
                        ]
                  }
                }
              })
            }
            //这里不知道为啥有详情 没图片数据就不展示的逻辑
            // if (this.opType == 4 && (res.Data || []).length) {
            //   this.dataList = (res.Data || []).filter(
            //     (v) => (v['Qualifications'][0].QualificationValue || []).length > 0
            //   )
            // } else {
            this.dataList = res.Data || []
            // }

            // console.log(this.opType )
            console.log('hasList         ', this.hasList)
            console.log('dataList         ', this.dataList)
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    async setData(data) {
      // if (data && data.zlxs == 2) {
      //   // 七彩转存
      //   let dataObj = await this.getGoodsQulificationFromCA(data)
      //   return dataObj
      // }
      // console.log(this.FileId)
      if (!data || (data.list || []).length < 1) {
        return null
      }
      data.list.map((group) => {
        if (!(data && data.zlxs == 2)) {
          group['NeedToCAContrast'] = true
        } else {
          group['NeedToCAContrast'] = group['NeedToCAContrast']
        }
        if (group.Qualifications) {
          group.Qualifications.forEach((item) => {
            item['Urls'] = item.QualificationValue
          })
        }
      })
      return { list: data.list, remark: data.remark, zlxs: data.zlxs, fileId: data.fileId || this.FileId }
    },
    async getGoodsQulificationFromCA(data = null) {
      let res = await postAction(
        this.url.getGoodsQulificationFromCA,
        {
          // goodsSpuId: this.id,
          ErpGoodsId: this.erpGoodsId, //'SPZ00000001', // this.erpGoodsId,
          AppendInfo: this.AppendInfo,
          GoodsManageClassify2: this.goodsManageClassifyId,
        },
        'P36004'
      )
      if (!res.IsSuccess) {
        this.$message.warning(res.Msg)
        return null
      }
      if (!data) {
        // console.log(res.Data)
        return { list: res.Data.Items || [], remark: res.Data.remark, zlxs: 2, fileId: res.Data && res.Data['FileId'] }
      }
      // 注释下面部分
      // 有填写的资质信息
      if (data && (data.list || []).length) {
        data.list.map((group) => {
          group['NeedToCAContrast'] = true
          if (group.Qualifications) {
            group.Qualifications.forEach((item) => {
              item['Urls'] = item.QualificationValue
            })
          }
        })
      }
      let noMustArr = data && (data.list || []).length ? data.list.filter((v) => !v.IsMust) : []
      let dataArr = []
      let noArr = []
      // 七彩数据
      if (res.Data && (res.Data.Items || []).length) {
        res.Data.Items.map((group) => {
          group['NeedToCAContrast'] = false
          group['Qualifications'].map((j) => {
            j['QualificationName'] = group['GroupName']
            j['QualificationCode'] = group['GroupCode']
          })
          if ((noMustArr || []).length) {
            let index = noMustArr.findIndex((v) => v['GroupCode'] == group['GroupCode'])
            if (index >= 0) {
              group['Qualifications'][0].QualificationValue = []
                .concat(group['Qualifications'][0].Urls)
                .concat(noMustArr[index]['Qualifications'][0].QualificationValue)
            } else {
              noArr = noMustArr.filter((v) => v['GroupCode'] != group['GroupCode'])
            }
          }
        })

        dataArr = [].concat(noArr || []).concat(res.Data.Items || [])
        //     console.log(dataArr)
        // return
      } else {
        dataArr = [].concat(noMustArr || [])
      }

      return { list: dataArr, remark: data.remark, zlxs: data.zlxs, fileId: res.Data && res.Data['FileId'] }
    },
    /**
     * 点击获取转存资料事件
     */
    async getZCData() {
      this.$refs.zhuanChunQualificationModel.show(this.goods)
      return
      this.zcLoading = true
      let dataObj = await this.getGoodsQulificationFromCA()
      if (dataObj) {
        if ((this.dataList || []).length) {
          this.dataList.map((v, index) => {
            if ((dataObj.list || []).length) {
              let index = dataObj.list.findIndex((j) => j['GroupCode'] == v['GroupCode'])
              if (index > -1) {
                if (!v.IsMust) {
                  v.IsAdd = true
                }
                let QualificationsArr = []
                if ((dataObj.list[index].Qualifications || []).length) {
                  dataObj.list[index].Qualifications.map((j) => {
                    QualificationsArr.push({
                      QualificationName: v['GroupName'],
                      QualificationCode: v['GroupCode'],
                      QualificationType: j['QualificationType'],
                      QualificationTypeStr: j['QualificationTypeStr'],
                      IsMust: j['IsMust'],
                      IsShow: j['IsShow'],
                      QualificationValue: j.Urls,
                    })
                  })
                } else {
                  QualificationsArr = [
                    {
                      QualificationName: v['GroupName'],
                      QualificationCode: v['GroupCode'],
                      QualificationType: 1,
                      QualificationTypeStr: '图片',
                      IsMust: true,
                      IsShow: true,
                    },
                  ]
                }

                v['Qualifications'] = QualificationsArr
                v['fileId'] = dataObj.fileId
                v['NeedToCAContrast'] = false
              }
            }
          })
        }
      }

      this.zcLoading = false
    },
    /**
     * 获取转存资料-回调
     * @param {Object 返回的阿里云地址集合} list
     */
    onZhuanChunCallback(listObj) {
      console.log(`获取转存资料: `, listObj)
      const keyList = Object.keys(listObj)
      if (keyList.length > 0) {
        let change = false
        console.log(`已有的类型: `, this.dataList)
        const temtArr = ['YSBHPZPJHXYBS', 'JKYPJYBGHTGD', 'QT']
        this.dataList.forEach((group) => {
          group.Qualifications.forEach((item) => {
            if (item.QualificationType == 1) {
              change = true
              if (item.QualificationValue && item.QualificationValue.length > 0) {
                if (listObj[group.GroupCode]) {
                  item.QualificationValue = item.QualificationValue.concat(listObj[group.GroupCode])
                }
              } else {
                item.QualificationValue = listObj[group.GroupCode]
              }
            }
          })
          if (temtArr.includes(group.GroupCode) && listObj[group.GroupCode] && listObj[group.GroupCode].length) {
            group.IsAdd = true
          }
        })
        if (change) {
          let oldList = JSON.parse(JSON.stringify(this.dataList))
          this.dataList = []
          this.dataList = oldList
        }
      }
    },
    /***
     * 新增保存数据
     */
    async addSaveData(subAudit, callback) {
      if (!this.$refs.firstCampInformation) {
        callback && callback(false)
        return
      }
      let data = await this.setData(this.$refs.firstCampInformation.getFirstCampData())
      if (data) {
        let that = this
        let subModel = {
          GoodsSpuId: this.id || null,
          Remark: data.remark,
          AuditStatus: !subAudit ? 0 : 1,
          Groups: data.list || [],
        }
        if (data.zlxs == 2 && (data['fileId'] || ((data.list || []).length && data.list[0].fileId))) {
          subModel['FileId'] = data['fileId'] || data.list[0].fileId
        } else {
          delete subModel['FileId']
        }
        that.loading = true
        postAction(that.url.add, subModel, that.httpHead)
          .then((res) => {
            if (res.IsSuccess) {
              // console.log('subAudit          ', subAudit)
              if (subAudit) {
                that.createApproval(
                  {
                    Remark: data.remark,
                    BussinessNo: res.Data,
                  },
                  (bool) => {
                    if (bool) {
                      callback && callback(true)
                    } else {
                      callback && callback(false)
                    }
                  }
                )
              } else {
                that.$message.success('操作成功！')
                callback && callback(true)
              }
            } else {
              that.$message.error(res.Msg)
              callback && callback(false)
            }
          })
          .finally(() => {
            that.loading = false
          })
      } else {
        callback && callback(false)
      }
    },
    async editSaveData(subAudit, callback) {
      // console.log('subAudit         ', subAudit)
      // console.log(
      //   'hasList         ',
      //   this.hasList.every(v => v['Qualifications'].length < 1)
      // )
      // return
      // 没有资质信息 走新增资质接口
      // if (this.hasList.every((v) => v['Qualifications'].length < 1) && !this.isColorCertification) {
      if (this.isGoodsQualificationExist === false && !this.isColorCertification) {
        this.addSaveData(subAudit, callback)
        return
      }
      if (!this.$refs.firstCampInformation) {
        callback && callback(false)
        return
      }
      let data = await this.setData(this.$refs.firstCampInformation.getFirstCampData())
      if (data) {
        let that = this
        let subModel = {
          GoodsSpuId: this.id || null,
          Remark: data.remark,
          AuditStatus: !subAudit ? 0 : 1,
          Groups: data.list || [],
        }

        if (data.zlxs == 2 && (data['fileId'] || ((data.list || []).length && data.list[0].fileId))) {
          subModel['FileId'] = data['fileId'] || data.list[0].fileId
        } else {
          delete subModel['FileId']
        }
        // return
        if (this.isColorCertification) delete subModel['Remark']
        if (this.FileNo && this.FileNo != subModel.FileNo) {
          subModel['FileNo'] = this.FileNo
        }
        that.loading = true
        if (this.isColorCertification) subModel.GoodsAdditionalInformationId = this.GoodsAdditionalInformationId
        postAction(that.url.edit, subModel, this.isColorCertification ? 'P36004' : that.httpHead)
          .then((res) => {
            if (res.IsSuccess) {
              // this.BusinessSerialNumber = res.Data
              if (subAudit && !this.isColorCertification) {
                that.createApproval(
                  {
                    Remark: data.remark,
                    BussinessNo: res.Data, //this.BusinessSerialNumber
                  },
                  (bool) => {
                    if (bool) {
                      callback && callback(true)
                    } else {
                      callback && callback(false)
                    }
                  }
                )
              } else {
                that.$message.success('操作成功！')
                callback && callback(true)
              }
            } else {
              that.$message.error(res.Msg)
              callback && callback(false)
            }
          })
          .finally(() => {
            that.loading = false
          })
      } else {
        callback && callback(false)
      }
    },
    // 创建审核实例
    createApproval(data, callback) {
      if (!data['BussinessNo']) {
        this.$message.error('BussinessNo未获取到值')
        return
      }
      postAction(
        this.url.createApproval,
        {
          BussinessNo: data['BussinessNo'],
          Scenes: 3,
          OpratorId: Vue.ls.get(USER_ID),
          Remark: data['Remark'],
        },
        'P36005'
      ).then((res) => {
        if (!res.IsSuccess) {
          this.$message.error(res.Msg)
          callback && callback(false)
          return
        }
        this.$message.success('操作成功！')
        callback && callback(true)
      })
    },
    /**
     * 生成编号点击事件
     */
    onShengChengBianHaoClick() {
      let that = this
      that.fileNoLoading = true
      this.$confirm({
        title: '生成编号',
        content: '档案编号生成后不可修改，是否确认生成？',
        // showCancel: false,
        onOk: () => {
          that.createAndSaveNo()
        },
        onCancel: () => {
          that.fileNoLoading = false
        },
      })
    },
    /**
     * 生成编号接口调用
     */
    createAndSaveNo() {
      let that = this
      getAction(that.url.createAndSaveNo, { goodsSpuId: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.FileNo = res.Data || ''
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.fileNoLoading = false
        })
    },
    /**
     * 档案编号切换事件（要调用接口保存数据）
     * @param {*} val
     * @param {*} txt
     * @param {*} item
     */
    onFileNoChange(val, txt, item) {
      let that = this
      // if (!that.FileNo) {
      //   return
      // }
      that.loading = true
      postAction(that.url.saveFileNo, { GoodsSpuId: that.id, FileNo: that.FileNo }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success(that.FileNo ? '档案编号保存成功！' : '档案编号删除成功！')
          } else {
            that.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          that.loading = false
        })
        .finally(() => {
          that.loading = false
        })
    },
  },
}
</script>

<style></style>
