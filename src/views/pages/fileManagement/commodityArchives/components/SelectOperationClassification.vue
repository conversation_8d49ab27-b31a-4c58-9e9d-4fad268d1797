<!--
 * @Description: wenjingGao 运营分类
 * @Version: 1.0
 * @Author: wenjingGao
 * @Date: 2025-05-16 13:57:22
 * @LastEditors: wenjingGao
 * @LastEditTime: 2025-06-05 11:00:58
-->
<template>
  <a-cascader
    style="width: 100%"
    allowClear
    :value="defaultValue"
    :options="dataList"
    :disabled="disabled"
    placeholder="请选择"
    :loadData="loadData"
    change-on-select
    @change="handleInput"
  />
</template>

<script>
import { getAction } from '@/api/manage'

export default {
  name: 'SelectOperationClassification',
  props: {
    disabled: Boolean,
    httpHead: {
      type: String,
      default: 'P36006'
    },
    level: {
      type: Number,
      default: 5
    },
    value: {
      type: [String, Array]
    }, //选中值
    defaultIds: [Array, String], //选中值，如：['一级分类', '二级分类', '三级分类', '四级分类', '五级分类']
  },
  data() {
    return {
      defaultValue: [], //['一级分类', '二级分类', '三级分类', '四级分类', '五级分类'],
      selectedName: [], //选中的运营分类的名称
      dataList: [],
      url: '/{v}/OperationalClassify/GetOperationalClassifys'
    }
  },
  model: {
    prop: 'defaultIds',
    event: 'change'
  },
  watch: {
    value(val, oldval) {
      if (!(val && val.length)) {
        this.defaultValue = []
        this.selectedName = []
      }
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initData(parentId = '') {
      let params = { parentId: parentId }
      getAction(this.url, params, this.httpHead).then(res => {
        if (res.IsSuccess) {
          this.dataList = this.changeData(res.Data)
          if (typeof this.defaultIds === 'string') {
            this.$set(this, 'defaultValue', this.getDefaultValueById(this.defaultIds))
          } else {
            this.$set(this, 'defaultValue', this.defaultIds || [])
          }
          if (this.defaultValue.length) {
            var selectedOptions = this.dataList.filter(q => q.value == this.defaultValue[0])
            this.selectedName[0] = selectedOptions[0].label
            this.loadData(selectedOptions)
          }
        }
      })
    },
    loadData(selectedOptions) {
      let that = this
      console.log('loadData', selectedOptions)
      if(this.defaultValue.length !== this.level) {
        that.setDefaultValue(selectedOptions)
      } else {
        this.selectedName[selectedOptions[0].level - 1] = selectedOptions[0].label
      }
      const targetOption = selectedOptions[selectedOptions.length - 1]
      if (!targetOption || targetOption.level >= that.level) {
        return
      }
      targetOption.loading = true
      let params = { parentId: targetOption.value }
      getAction(that.url, params, that.httpHead).then(res => {
        if (res.IsSuccess) {
          targetOption.loading = false
          targetOption.children = that.changeData(res.Data)
          that.dataList = [...that.dataList]
          that.$forceUpdate()
          //return;
          if (that.defaultValue.length && targetOption.children.length > 0) {
            var selectedOptions = targetOption.children.filter(function(q) {
              if(q.level == 1) {
                return q.value == that.defaultValue[0]
              } else {
                return q.value == that.defaultValue[q.level-1]
              }
            })
            // console.log('selectedOptions', selectedOptions)
            that.loadData(selectedOptions)
          }
        }
      })
    },
    changeData(data) {
      let dataList = []
      if(data && data.length) {
        data.forEach(q=> {
          dataList.push({
            level: q.Level,
            parentId: q.ParentId,
            value: q.Id,
            label: q.Name,
            isLeaf: q.Level >= this.level ? true : false,
          })
        })
      }
      return dataList
    },
    getDefaultValueById(ids) {
      if (!ids) return []
      let tempArray = ids.split("/")
      return tempArray
    },
    setDefaultValue(selectedOptions) {
      console.log('setDefaultValue', selectedOptions)
      if (typeof selectedOptions != 'undefined' && typeof selectedOptions[0] != 'undefined') {
        if (selectedOptions.length === 1) {
          this.selectedName = []
        } 
        // this.defaultValue.push(selectedOptions[selectedOptions.length -1].value)
        // this.selectedName.push(selectedOptions[selectedOptions.length -1].label)
        this.defaultValue[selectedOptions.length -1] = selectedOptions[selectedOptions.length -1].value
        this.selectedName[selectedOptions.length -1] = selectedOptions[selectedOptions.length -1].label
        this.$forceUpdate()
      }
    },
    handleInput(val, node) {

      this.defaultValue = val
      if (val && val.length) {
        // this.defaultValue = val
        if(val.length === 5) {
          // this.selectedName.push(node[4].label)
          this.selectedName[4] = node[4].label
        }
      } else {
        this.selectedName = []
      }
      this.$emit('change', val, this.selectedName)
    }
  }
}
</script>