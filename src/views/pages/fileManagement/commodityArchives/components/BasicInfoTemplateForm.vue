<!-- 商品档案新增 基本信息 -->
<template>
  <div :class="isAudit ? 'form-model-style form-model-style-scroll scroll-style' : 'form-model-style'">
    <a-spin :spinning="loading">
      <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical" v-if="[1, 2, 3].includes(opType)">
        <!-- 商品信息 -->
        <template>
          <a-row :gutter="gutter">
            <a-col :span="24">
              <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">
                {{ isAudit ? '基础信息' : '商品信息' }}
              </div>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="商品类别：" prop="ManageClassify1" :class="returnRed('ManageClassify1') ? 'red' : ''">
                <SingleChoiceSearchView style="width: 100%" placeholder="请选择" :httpParams="{ Name: '', Level: 1 }" httpHead="P36006" httpType="POST" keyWord="Name" Url="/{v}/GoodsManage/QueryGoodsManageClassifyList" v-model="model.ManageClassify1" :name="model.ManageClassifyStr1" @change="handleManageClassify1" :disabled="isAudit" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="商品分类：" prop="ManageClassify2" :class="returnRed('ManageClassify2') ? 'red' : ''">
                <SingleChoiceSearchView style="width: 100%" placeholder="请选择" :httpParams="{ Name: '', Level: 2, ParentId: model.ManageClassify1 }" httpHead="P36006" httpType="POST" keyWord="Name" :Url="'/{v}/GoodsManage/QueryGoodsManageClassifyList'" v-model="model.ManageClassify2" :name="model.ManageClassifyStr2" @change="
                    (val, txt, item) => {
                      model.ManageClassify2Code = item['Code']
                      iDosageFormCode = false
                      iStorageConditionCode = false
                      $nextTick(() => {
                        iDosageFormCode = true
                        iStorageConditionCode = true
                      })
                      $refs.ruleForm.clearValidate('Producer')
                      $emit('changeManageClassify2', model.ManageClassify2Code || '')
                    }
                  " v-if="isShowManageClassify2" :disabled="isAudit" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="商品名称：" prop="ErpGoodsName" :class="returnRed('ErpGoodsName') ? 'red' : ''">
                <!-- :maxLength="100" -->
                <a-input placeholder="请输入" v-model="model.ErpGoodsName" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="
                !['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code) &&
                !['YLQX', 'ZP'].includes(model.ManageClassify1Code)
              ">
              <a-form-model-item label="商品名：" prop="AliasName" :class="returnRed('AliasName') ? 'red' : ''">
                <a-input placeholder="请输入" v-model="model.AliasName" :maxLength="80" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="生产厂家：" prop="BrandManufacturer" :class="returnRed('BrandManufacturer') ? 'red' : ''">
                <a-input placeholder="请输入" v-model="model.BrandManufacturer" @change="
                    () => {
                      if (!model.Id) model.Producer = model.BrandManufacturer
                    }
                  " />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="iDosageFormCode">
              <a-form-model-item label="产地：" prop="Producer" :class="returnRed('Producer') ? 'red' : ''" :rules="{
                  required: ['ZYYP_2', 'ZYC_2'].includes(this.model.ManageClassify2Code),
                  validator: (rule, value, callback) => {
                    validStrMaxLength(rule, value, callback, 200)
                  },
                }">
                <a-input placeholder="请输入" v-model="model.Producer" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassify2Code) || ['YLQX'].includes(model.ManageClassify1Code)">
              <a-form-model-item label="上市许可持有人：" prop="LicensedHolder" :class="returnRed('LicensedHolder') ? 'red' : ''">
                <!--  :rules="{
                  required: ['YP_2', 'JKYP_2'].includes(this.model.ManageClassify2Code),
                  validator: (rule, value, callback) => {
                    validStrMaxLength(rule, value, callback, 200)
                  },
                }" -->
                <a-input placeholder="请输入" v-model="model.LicensedHolder" :maxLength="100" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="gutter">
            <a-col :span="md">
              <a-form-model-item label="商品规格：" prop="PackingSpecification" :class="returnRed('PackingSpecification') ? 'red' : ''">
                <a-input placeholder="请输入" v-model="model.PackingSpecification" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassify2Code)">
              <a-form-model-item label="商品条码：" prop="BarCode" :class="returnRed('BarCode') ? 'red' : ''">
                <a-input placeholder="请输入" v-model="model.BarCode" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="包装单位：" prop="PackageUnit" :class="returnRed('PackageUnit') ? 'red' : ''">
                <SingleChoiceView style="width: 100%" placeholder="请选择" :httpParams="{
                    groupPY: 'dw',
                  }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'ItemValue' }" v-model="model.PackageUnit" :Url="url.getListDictItem" @change="(val, txt, item) => (model.PackageUnit = txt)" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="
                !['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code) &&
                !['RYBH', 'ZP'].includes(model.ManageClassify1Code)
              ">
              <a-form-model-item label="批准文号：" prop="ApprovalNumber" :class="returnRed('ApprovalNumber') ? 'red' : ''">
                <a-input placeholder="请输入" v-model="model.ApprovalNumber" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="!['ZP'].includes(model.ManageClassify1Code)">
              <a-form-model-item label="批件有效期：" prop="ValidityPeriodOfApprovalDocument" :class="returnRed('ValidityPeriodOfApprovalDocument') ? 'red' : ''">
                <a-input placeholder="请输入(格式例：20230620)" v-model="model.ValidityPeriodOfApprovalDocument" :maxLength="10" @change="(e) => handleReturnTime(e, 'ValidityPeriodOfApprovalDocument')" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="!['ZP'].includes(model.ManageClassify1Code)">
              <a-form-model-item label="剂型：" prop="DosageFormName" :class="returnRed('DosageFormName') ? 'red' : ''">
                <SingleChoiceView style="width: 100%" placeholder="请选择" :httpParams="{
                    groupPY: 'jx',
                  }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'ItemPym' }" v-model="model.DosageFormCode" :Url="url.getListDictItem" @change="(val, txt, item) => (model.DosageFormName = txt)" v-if="iDosageFormCode" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="!['ZP'].includes(model.ManageClassify1Code)">
              <a-form-model-item label="有效期：" prop="TermOfValidity" :class="returnRed('TermOfValidity') ? 'red' : ''">
                <a-input placeholder="请输入" v-model="model.TermOfValidity" :maxLength="10" suffix="月" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="!['ZP'].includes(model.ManageClassify1Code)">
              <a-form-model-item label="存储条件：" prop="StorageConditionName" :class="returnRed('StorageConditionName') ? 'red' : ''">
                <SingleChoiceView style="width: 100%" placeholder="请选择" :httpParams="{
                    groupPY: 'cunchtj',
                  }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'ItemPym' }" v-model="model.StorageConditionCode" :Url="url.getListDictItem" @change="(val, txt, item) => (model.StorageConditionName = txt)" v-if="iStorageConditionCode" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassify2Code)">
              <a-form-model-item label="中包装：" prop="MiddlePackage" :class="returnRed('MiddlePackage') ? 'red' : ''">
                <a-input placeholder="请输入" v-model="model.MiddlePackage" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="gutter">
            <a-col :span="md" v-if="
                !['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code) &&
                !['XDYP', 'RYBH', 'ZP'].includes(model.ManageClassify1Code)
              ">
              <a-form-model-item label="生产企业许可证证号：" prop="ProductionEnterpriseLicenseNumber" :class="returnRed('ProductionEnterpriseLicenseNumber') ? 'red' : ''">
                <a-input placeholder="请输入" v-model="model.ProductionEnterpriseLicenseNumber" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="
                !['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code) &&
                !['XDYP', 'RYBH', 'ZP'].includes(model.ManageClassify1Code)
              ">
              <a-form-model-item label="生产企业许可证有效期：" prop="ProductionEnterpriseLicenseTermOfValidity" :class="returnRed('ProductionEnterpriseLicenseTermOfValidity') ? 'red' : ''">
                <a-input placeholder="请输入(格式例：20230620)" v-model="model.ProductionEnterpriseLicenseTermOfValidity" :maxLength="10" @change="(e) => handleReturnTime(e, 'ProductionEnterpriseLicenseTermOfValidity')" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="采购税率：" prop="PurchaseTaxRate" :class="returnRed('PurchaseTaxRate') ? 'red' : ''">
                <SingleChoiceView style="width: 100%" placeholder="请选择" :httpParams="{
                    groupPY: 'cgshlv',
                  }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'ItemPym' }" v-model="model.PurchaseTaxRate" :Url="url.getListDictItem" @change="
                    (val, txt, item) => {
                      if (!model.Id) model.SellsTaxRate = model.PurchaseTaxRate
                      $refs.ruleForm.clearValidate('SellsTaxRate')
                    }
                  " v-if="IsPurchaseTaxRate" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="销售税率：" prop="SellsTaxRate" :class="returnRed('SellsTaxRate') ? 'red' : ''">
                <SingleChoiceView style="width: 100%" placeholder="请选择" :httpParams="{
                    groupPY: 'xsshlv',
                  }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'ItemPym' }" v-model="model.SellsTaxRate" :Url="url.getListDictItem" v-if="IsSellsTaxRate" @change="handleSellsTaxRateChange" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['ZP'].includes(model.ManageClassify1Code)">
              <a-form-model-item label="存储库区：" prop="StorageArea" :class="returnRed('StorageArea') ? 'red' : ''">
                <a-input placeholder="请输入" v-model="model.StorageArea" :maxLength="10" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="件装量：" prop="PackageCount" :class="returnRed('PackageCount') ? 'red' : ''">
                <a-input placeholder="请输入" v-model="model.PackageCount" :maxLength="10" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['YP'].includes(model.ManageClassify1Code)">
              <a-form-model-item label="质量标准：" prop="QualityStandardCode" :class="returnRed('QualityStandardCode') ? 'red' : ''">
                <SingleChoiceView style="width: 100%" placeholder="请选择" :httpParams="{
                    groupPY: 'zhilbz',
                  }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'ItemPym' }" v-model="model.QualityStandardCode" :Url="url.getListDictItem" @change="(val, txt, item) => (model.QualityStandardName = txt)" v-if="IsQualityStandardCode" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['YP'].includes(model.ManageClassify1Code) && [3, 4].includes(opType)">
              <a-form-model-item label="是否重点养护：" prop="IsCriticalMaintenance" :class="returnRed('IsCriticalMaintenance') ? 'red' : ''">
                <a-select placeholder="请选择" v-model="model.IsCriticalMaintenance">
                  <a-select-option :value="''"> 请选择 </a-select-option>
                  <a-select-option v-for="(r, rIndex) in [
                      { title: '是', value: 'true' },
                      { title: '否', value: 'false' },
                    ]" :key="rIndex" :value="r.value">
                    {{ r.title }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="
                ['YP'].includes(model.ManageClassify1Code) &&
                [3, 4].includes(opType) &&
                model.IsCriticalMaintenance == 'true'
              ">
              <a-form-model-item label="重点养护原因：" prop="CriticalMaintenanceReason" :class="returnRed('CriticalMaintenanceReason') ? 'red' : ''">
                <a-input placeholder="请输入" v-model="model.CriticalMaintenanceReason" />
              </a-form-model-item>
            </a-col>
            <!-- isAudit?24:md -->
            <a-col :span="md" v-if="[3, 4].includes(opType) || ['ZP'].includes(model.ManageClassify1Code)">
              <a-form-model-item label="税收分类编码：" prop="TaxClassificationCode" :class="returnRed('TaxClassificationCode') ? 'red' : ''">
                <!-- <SingleChoiceView
                  style="width: 100%;"
                  placeholder="请选择"
                  :httpParams="{
                    groupPY: 'shuisflbm'
                  }"
                  httpHead="P36001"
                  :dataKey="{ name: 'ItemValue', value: 'ItemValue',nameList:['ItemPym']}"
                  v-model="model.TaxClassificationCode"
                  :Url="url.getListDictItem"
                  @change="(val, txt, item) => (model.TaxClassificationName = txt)"
                /> -->
                <!-- <SingleChoiceSearchAndInputView style="width: 100%" placeholder="请选择" :httpParams="{
                    Key: '',
                    groupPY: 'shuisflbm',
                  }" httpHead="P36001" keyWord="Key" :dataKey="{ name: 'ItemValue', value: 'ItemPym', nameList: ['ItemPym'] }" :Url="url.getListDictItem" v-model="model.TaxClassificationCode" @change="handelTaxClassificationCode" ref="iTaxClassificationCode" IsInput /> -->
                <a-input ref="iTaxClassificationCode" v-model="model.TaxClassificationNameStr" placeholder="请选择" :readOnly="true">
                  <a-button slot="suffix" type="primary" size="small" @click="handelTaxClassificationCode">
                    选择
                  </a-button>
                </a-input>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="gutter">
            <a-col :span="md" v-if="!['ZP'].includes(model.ManageClassify1Code)">
              <a-form-model-item label="经营范围控制类别：" prop="BusinessScopeName" :class="returnRed('BusinessScopeName') ? 'red' : ''">
                <!-- <SingleChoiceView
                  style="width: 100%"
                  placeholder="请选择"
                  :httpParams="
                    ['YLQX'].includes(model.ManageClassify1Code)
                      ? {}
                      : {
                          groupPY: 'shangplx',
                        }
                  "
                  :httpHead="['YLQX'].includes(model.ManageClassify1Code) ? 'P36006' : 'P36001'"
                  :dataKey="{ name: 'ItemValue', value: 'ItemPym' }"
                  v-model="model.BusinessScopeCode"
                  :Url="
                    ['YLQX'].includes(model.ManageClassify1Code) ? url.getYLQXClassManageSocpeList : url.getListDictItem
                  "
                  @change="(val, txt, item) => (model.BusinessScopeName = txt)"
                  v-if="IsBusinessScopeName"
                /> -->
                <SingleInputSearchView placeholder="搜索经营范围控制类别" width="100%" :httpParams="
                    ['YLQX'].includes(model.ManageClassify1Code)
                      ? {}
                      : {
                          groupPY: 'shangplx',
                        }
                  " keyWord="keyWord" :dataKey="{ name: 'ItemValue', value: 'ItemPym' }" :httpHead="['YLQX'].includes(model.ManageClassify1Code) ? 'P36006' : 'P36001'" :Url="['YLQX'].includes(model.ManageClassify1Code) ? url.getYLQXClassManageSocpeList : url.getBusinessScopeCategories" :value="model.BusinessScopeCode" :name="model.BusinessScopeName" :isAbsolute="true" @clear="()=>{model.BusinessScopeName = '';model.BusinessScopeCode = ''}" @change="(val, txt, item) => {model.BusinessScopeName = txt;model.BusinessScopeCode = val}" :isLocalSearch="true" v-if="IsBusinessScopeName" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassify2Code)">
              <a-form-model-item label="用药分类：" prop="ClassificationOfMedicationName" :class="returnRed('ClassificationOfMedicationName') ? 'red' : ''">
                <SingleChoiceView style="width: 100%" placeholder="请选择" :httpParams="{
                    groupPY: 'yongyfl',
                  }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'ItemPym' }" v-model="model.ClassificationOfMedicationCode" :Url="url.getListDictItem" @change="(val, txt, item) => (model.ClassificationOfMedicationName = txt)" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassify2Code)">
              <a-form-model-item label="处方分类：" prop="CategorizationOfPrescriptionName" :class="returnRed('CategorizationOfPrescriptionName') ? 'red' : ''">
                <SingleChoiceView style="width: 100%" placeholder="请选择" :httpParams="{
                    groupPY: 'chuffl',
                  }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'ItemPym' }" v-model="model.CategorizationOfPrescriptionCode" :Url="url.getListDictItem" @change="(val, txt, item) => (model.CategorizationOfPrescriptionName = txt)" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="!['ZP'].includes(model.ManageClassify1Code)">
              <a-form-model-item label="来货单位：" prop="Supplier" :class="returnRed('Supplier') ? 'red' : ''">
                <!--  AuthBusiness: 2, -->
                <SingleChoiceSearchView style="width: 100%" placeholder="请选择" :httpParams="{
                    input: '',
                    PageIndex: 1,
                    PageSize: 100,
                  }" httpHead="P36003" keyWord="input" :Url="'/{v}/Supplier/GetListSupplierSimple'" v-model="model.Supplier" :name="model.SupplierName" @change="handleSupplier" :disabled="isAudit" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassify2Code)">
              <a-form-model-item label="是否监管码品种：" prop="IsRegulatoryBarcodeVariety" :class="returnRed('IsRegulatoryBarcodeVariety') ? 'red' : ''">
                <a-select placeholder="请选择" v-model="model.IsRegulatoryBarcodeVariety">
                  <a-select-option :value="''"> 请选择 </a-select-option>
                  <a-select-option v-for="(r, rIndex) in [
                      { title: '是', value: 'true' },
                      { title: '否', value: 'false' },
                    ]" :key="rIndex" :value="r.value">
                    {{ r.title }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['YP_2', 'ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code) && [3, 4].includes(opType)">
              <a-form-model-item label="特殊标识鹿标或蓝标：" prop="IsSpecialSymbol" :class="returnRed('IsSpecialSymbol') ? 'red' : ''">
                <a-select placeholder="请选择" v-model="model.IsSpecialSymbol">
                  <a-select-option :value="''"> 请选择 </a-select-option>
                  <a-select-option v-for="(r, rIndex) in [
                      { title: '是', value: 'true' },
                      { title: '否', value: 'false' },
                    ]" :key="rIndex" :value="r.value">
                    {{ r.title }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="采购人：" prop="PurchaserId" :class="returnRed('PurchaserId') ? 'red' : ''">
                <SingleChoiceSearchView style="width: 100%" placeholder="请选择" :httpParams="{
                    // IsValid: true,
                    PageIndex: 1,
                    PageSize: 1000,
                  }" httpHead="P36001" keyWord="Name" :Url="'/{v}/UserPermission/User/List'" v-model="model.PurchaserId" :name="model.PurchaserName" @change="
                    (val, txt, item) => {
                      model.PurchaserName = txt
                    }
                  " />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="医保代码：" prop="MedicalInsuranceCode" :class="returnRed('MedicalInsuranceCode') ? 'red' : ''">
                <a-input v-model="model.MedicalInsuranceCode" style="width: 100%" placeholder="请输入" :maxLength="50"/>
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="是否药品有追溯码：" prop="IsHasGoodsTraceCode" :class="returnRed('IsHasGoodsTraceCode') ? 'red' : ''">
                <a-select placeholder="请选择" v-model="model.IsHasGoodsTraceCode">
                  <a-select-option :value="''"> 请选择 </a-select-option>
                  <a-select-option v-for="(r, rIndex) in [
                      { title: '是', value: 'true' },
                      { title: '否', value: 'false' },
                    ]" :key="rIndex" :value="r.value">
                    {{ r.title }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="是否强制扫追溯码：" prop="IsForceScanTraceCode" :class="returnRed('IsForceScanTraceCode') ? 'red' : ''">
                <a-select placeholder="请选择" v-model="model.IsForceScanTraceCode">
                  <a-select-option :value="''"> 请选择 </a-select-option>
                  <a-select-option v-for="(r, rIndex) in [
                      { title: '是', value: 'true' },
                      { title: '否', value: 'false' },
                    ]" :key="rIndex" :value="r.value">
                    {{ r.title }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="isAudit">
              <a-form-model-item label="资料缺项：" prop="Remark" :class="returnRed('Remark') ? 'red' : ''">
                <a-input v-model="model.Remark" style="width: 100%" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code)">
              <a-form-model-item label="运营分类：" prop="OperationalClassifyFullIds">
                <!-- <SelectAreaView style="width: 100%" ref="selectCityAreas" :selectCode="true" :defaultCodes="areaCodes"  /> -->
                  <SelectOperationClassification style="width: 100%" :defaultIds="model.OperationalClassifyFullIds"  @change="(val, node) => onOperationalClassifyChange(val, node)" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code)">
              <a-form-model-item label="换算系数：" prop="ConversionFactor" :class="returnRed('ConversionFactor') ? 'red' : ''">
                <a-input v-model="model.ConversionFactor" style="width: 100%" placeholder="请输入" :maxLength="50"/>
              </a-form-model-item>
            </a-col>
          </a-row>
        </template>
        <!-- 销售信息 -->
        <!-- opType 1 新增 2 采购编辑 3质管编辑 4详情 -->
        <!-- (model.AuthBusinessStatus == 1 && model.AuditStatus == 0) -->
        <template v-if="showSaleInfo">
          <a-row :gutter="gutter">
            <a-col :span="24">
              <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">销售信息</div>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="采购进价：" prop="PurchaseCostPrice">
                <a-input-number v-model="model.PurchaseCostPrice" :precision="2" style="width: 100%" :min="0" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="建议零售价：" prop="AdviseSalePrice">
                <a-input-number v-model="model.AdviseSalePrice" :precision="2" style="width: 100%" :min="0" placeholder="请输入" />
              </a-form-model-item>
            </a-col>
            <a-col :span="md" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassify2Code)">
              <a-form-model-item label="是否医保品种：" prop="IsMedicalInsurance">
                <a-select placeholder="请选择" v-model="model.IsMedicalInsurance">
                  <a-select-option :value="''"> 请选择 </a-select-option>
                  <a-select-option v-for="(r, rIndex) in [
                      { title: '是', value: 'true' },
                      { title: '否', value: 'false' },
                    ]" :key="rIndex" :value="r.value">
                    {{ r.title }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <!-- 医保信息 -->
            <HealthInsuranceForm ref="HealthInsuranceForm" v-if="model.IsMedicalInsurance == 'true' && ['YP_2', 'JKYP_2'].includes(model.ManageClassify2Code)" :md="md" :info="model" />
          </a-row>

          <a-row :gutter="gutter" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassify2Code) && !isAudit && model.IsMedicalInsurance == 'true'">
            <a-col :span="md">
              <a-form-model-item label="是否挂网：" prop="HasWebPrice">
                <a-row :gutter="gutter">
                  <a-col :span="checkBtnPermissions('4e3fbebb-9cf7-48dd-9ed6-aff992aeb19f') && model.HasWebPrice ? 16 : 24">
                    <a-select placeholder="请选择" v-model="model.HasWebPrice">
                      <a-select-option :value="''"> 请选择 </a-select-option>
                      <a-select-option v-for="(r, rIndex) in [
                          { title: '是', value: 'true' },
                          { title: '否', value: 'false' },
                        ]" :key="rIndex" :value="r.value">
                        {{ r.title }}
                      </a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="8" style="text-align: right" v-if="checkBtnPermissions('4e3fbebb-9cf7-48dd-9ed6-aff992aeb19f') && model.HasWebPrice == 'true'">
                    <YYLButton menuId="4e3fbebb-9cf7-48dd-9ed6-aff992aeb19f" text="新增挂网价" type="primary" @click="$refs.iWebPriceSetModal.show()" />
                  </a-col>
                </a-row>
              </a-form-model-item>
            </a-col>
            <a-col :span="24" v-if="model.HasWebPrice == 'true' && ['YP_2', 'JKYP_2'].includes(model.ManageClassify2Code)">
              <!-- 列表 -->
              <SimpleTable ref="table" :tab="tab" :columns="columns" @operate="operate">
                <span slot="commonContent" slot-scope="{ text }">
                  <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                </span>
              </SimpleTable>
            </a-col>
          </a-row>
        </template>
        <!-- 登记信息 -->
        <template v-if="[3].includes(opType) && !isAudit">
          <a-row :gutter="gutter">
            <a-col :span="24">
              <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">登记信息</div>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="创建人：" prop="CreateByName">
                <a-input v-model="model.CreateByName" style="width: 100%" placeholder="请输入" disabled />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item label="创建时间：" prop="CreateTime">
                <a-input v-model="model.CreateTime" style="width: 100%" placeholder="请输入" disabled />
              </a-form-model-item>
            </a-col>
          </a-row>
        </template>
      </a-form-model>
      <!-- 详情展示部分 -->
      <a-card :bordered="false" v-else>
        <a-descriptions title="商品信息" :column="inputMd ? 2 : 3">
          <a-descriptions-item label="商品类别">{{ model.ManageClassifyStr1 || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="商品分类">{{ model.ManageClassifyStr2 || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="商品名称">{{ model.ErpGoodsName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="商品名" v-if="
              !['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code) &&
              !['YLQX', 'ZP'].includes(model.ManageClassify1Code)
            ">{{ model.AliasName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="生产厂家">{{ model.BrandManufacturer || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="产地">{{ model.Producer || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="上市许可持有人" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassifyCode2) || ['YLQX'].includes(model.ManageClassify1Code)">{{
            model.LicensedHolder || ' -- '
          }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions title="" :column="inputMd ? 2 : 3">
          <a-descriptions-item label="商品规格">{{ model.PackingSpecification || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="商品条码">{{ model.BarCode || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="包装单位">{{ model.PackageUnit || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="批准文号" v-if="
              !['ZYC_2', 'ZYYP_2'].includes(model.ManageClassifyCode2) &&
              !['RYBH', 'ZP'].includes(model.ManageClassifyCode1)
            ">{{ model.ApprovalNumber || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="批件有效期" v-if="!['ZP'].includes(model.ManageClassify1Code)">{{
            model.ValidityPeriodOfApprovalDocument || ' -- '
          }}</a-descriptions-item>
          <a-descriptions-item label="剂型" v-if="!['ZP'].includes(model.ManageClassify1Code)">{{
            model.DosageFormName || ' -- '
          }}</a-descriptions-item>
          <a-descriptions-item label="有效期" v-if="!['ZP'].includes(model.ManageClassify1Code)">{{ model.TermOfValidity || ' -- ' }}月</a-descriptions-item>
          <a-descriptions-item label="存储条件" v-if="!['ZP'].includes(model.ManageClassify1Code)">{{
            model.StorageConditionName || ' -- '
          }}</a-descriptions-item>
          <a-descriptions-item label="中包装" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassifyCode2)">{{
            model.MiddlePackage || ' -- '
          }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions title="" :column="inputMd ? 2 : 3">
          <a-descriptions-item label="生产企业许可证证号" v-if="
              !['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code) &&
              !['XDYP', 'RYBH', 'ZP'].includes(model.ManageClassify1Code)
            ">{{ model.ProductionEnterpriseLicenseNumber || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="生产企业许可证有效期" v-if="
              !['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code) &&
              !['XDYP', 'RYBH', 'ZP'].includes(model.ManageClassify1Code)
            ">{{ model.ProductionEnterpriseLicenseTermOfValidity || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="采购税率">{{ model.PurchaseTaxRate || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="销售税率">{{ model.SellsTaxRate || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="存储库区" v-if="['ZP'].includes(model.ManageClassify1Code)">{{
            model.StorageArea || ' -- '
          }}</a-descriptions-item>
          <a-descriptions-item label="件装量">{{ model.PackageCount || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="质量标准" v-if="['YP'].includes(model.ManageClassifyCode1)">{{
            model.QualityStandardName || ' -- '
          }}</a-descriptions-item>
          <a-descriptions-item label="是否重点养护" v-if="['YP'].includes(model.ManageClassifyCode1) && [3, 4].includes(opType)">{{
              model.IsCriticalMaintenance ? (reBool(model['IsCriticalMaintenance']) ? '是' : '否') : ' -- '
            }}</a-descriptions-item>
          <a-descriptions-item label="重点养护原因" v-if="
              ['YP'].includes(model.ManageClassifyCode1) &&
              [3, 4].includes(opType) &&
              model.IsCriticalMaintenance == 'true'
            ">{{ model.CriticalMaintenanceReason || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="税收分类编码" v-if="[3, 4].includes(opType)">{{
            model.TaxClassificationName || ' -- ' 
          }} / {{model.TaxClassificationCode || ' -- '}}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions title="" :column="inputMd ? 2 : 3">
          <a-descriptions-item label="经营范围控制类别" v-if="!['ZP'].includes(model.ManageClassify1Code)">{{
            model.BusinessScopeName || ' -- '
          }}</a-descriptions-item>
          <a-descriptions-item label="用药分类" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassifyCode2)">{{
            model.ClassificationOfMedicationName || ' -- '
          }}</a-descriptions-item>
          <a-descriptions-item label="处方分类" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassifyCode2)">{{
            model.CategorizationOfPrescriptionName || ' -- '
          }}</a-descriptions-item>
          <a-descriptions-item label="来货单位">{{ model.SupplierName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="是否监管码品种" v-if="['YP_2', 'JKYP_2'].includes(model.ManageClassifyCode2)">{{
            model.IsRegulatoryBarcodeVariety ? (reBool(model['IsRegulatoryBarcodeVariety']) ? '是' : '否') : ' -- '
          }}</a-descriptions-item>
          <a-descriptions-item label="特殊标识鹿标或蓝标" v-if="['YP_2', 'ZYC_2', 'ZYYP_2'].includes(model.ManageClassifyCode2) && [3, 4].includes(opType)">{{
              model.IsSpecialSymbol ? (reBool(model['IsSpecialSymbol']) ? '是' : '否') : ' -- '
            }}</a-descriptions-item>
          <a-descriptions-item label="采购人">{{ model.PurchaserName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="医保代码">{{ model.MedicalInsuranceCode || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="是否药品有追溯码">{{ model.IsHasGoodsTraceCode ? (reBool(model['IsHasGoodsTraceCode']) ? '是' : '否') : ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="是否强制扫追溯码">{{ model.IsForceScanTraceCode ? (reBool(model['IsForceScanTraceCode']) ? '是' : '否') : ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="备注">{{ model.Remark || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="运营分类" v-if="['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code)">{{ model.OperationalClassifyFullNames || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="换算系数" v-if="['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code)">{{ model.ConversionFactor || ' -- ' }}</a-descriptions-item>
        </a-descriptions>
        <a-descriptions title="登记信息" :column="inputMd ? 2 : 3">
          <a-descriptions-item label="创建人">{{ model.CreateByName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ model.CreateTime || ' -- ' }}</a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 审核信息 -->
      <template v-if="isAudit">
        <a-row :gutter="gutter">
          <a-col :span="24">
            <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">审核信息</div>
          </a-col>
          <a-col :span="24">
            <SupAuditInformation ref="supAuditInformation" :bussinessNo="BusinessSerialNumber" v-if="BusinessSerialNumber" />
          </a-col>
        </a-row>
      </template>
    </a-spin>
    <WebPriceSetModal ref="iWebPriceSetModal" @ok="handleWebPriceSetModalOk" />
    <SelectTaxClassificationCodeModal ref="iSelectTaxClassificationCode" @ok="handleSelectTaxClassificationCodeOk" />
  </div>
</template>

<script>
import Vue from 'vue'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { USER_ID, USER_INFO } from '@/store/mutation-types'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
import moment from 'moment'
export default {
  description: '商品档案新增 基本信息',
  name: 'BasicInfoTemplateForm',
  mixins: [EditMixin, SimpleMixin],
  components: {
    JEllipsis,
  },
  props: {
    /**
     * 1 新增 2 采购编辑 3质管编辑 4详情
     */
    opType: {
      type: Number,
      default: 1,
      required: true,
      validator: (s) => [1, 2, 3, 4].includes(s),
    },
    Info: {
      type: [Object, Boolean],
      default: () => null,
    },
    inputMd: {
      type: Number,
      default: null,
    },
    isAudit: {
      type: Boolean,
      default: false,
    },
    BusinessSerialNumber: {
      type: String,
      default: null,
    },
    Remark: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      isShowManageClassify2: true,
      iDosageFormCode: true,
      iStorageConditionCode: true,
      IsBusinessScopeName: true,
      IsQualityStandardCode: true,
      IsPurchaseTaxRate: true,
      IsSellsTaxRate: true,
      md: this.inputMd || 8,
      isClickEdit: null,
      redFileds: [],
      areaCodes: [], //运营分类
      model: {
        ManageClassify1: 'ec35be1c-d61f-4c8a-8ee4-98a53bb322f9',
        ManageClassify1Code: 'YP',
        ManageClassify2Code: 'YP_2',
        ManageClassify2: '61dc1b8e-b521-4eef-8b18-ca40a4cda072',
        ErpGoodsName: '',
        AliasName: '',
        BrandManufacturer: '',
        Producer: '',
        LicensedHolder: '',
        PackingSpecification: '',
        BarCode: '',
        PackageUnit: '',
        ApprovalNumber: '',
        ValidityPeriodOfApprovalDocument: null,
        TermOfValidity: null,
        DosageFormCode: '',
        DosageFormName: '',
        StorageConditionCode: '',
        StorageConditionName: '',
        MiddlePackage: '',
        ProductionEnterpriseLicenseNumber: '',
        ProductionEnterpriseLicenseTermOfValidity: null,
        PurchaseTaxRate: '',
        SellsTaxRate: '',
        PackageCount: '',
        StorageArea: '',
        QualityStandardCode: '',
        QualityStandardName: '',
        BusinessScopeCode: '',
        BusinessScopeName: '',
        TaxClassificationCode: '',
        TaxClassificationName: '',
        TaxClassificationNameStr: '',
        ClassificationOfMedicationCode: '',
        ClassificationOfMedicationName: '',
        CategorizationOfPrescriptionCode: '',
        CategorizationOfPrescriptionName: '',
        Supplier: '',
        SupplierName: '',
        SupplierType: null,
        IsCriticalMaintenance: '',
        CriticalMaintenanceReason: '',
        IsRegulatoryBarcodeVariety: '',
        IsSpecialSymbol: 'false',
        PurchaserId: Vue.ls.get(USER_ID),
        PurchaserName: Vue.ls.get(USER_INFO)['Name'],
        IsMedicalInsurance: '',
        PurchaseCostPrice: null,
        AdviseSalePrice: null,
        HasWebPrice: '',
        WebPrice: [],
        Remark: '',
        MedicalInsuranceCode: '', // 医保代码
        IsHasGoodsTraceCode: '', // 是否药品有追溯码
        IsForceScanTraceCode: '', // 是否强制扫追溯码
        OperationalClassifyFullIds: '', //运营分类id合集
        OperationalClassifyFullNames: '', //运营分类名称合集
        ConversionFactor: '', // 换算系数
      },
      tab: { hideIpagination: true },
      columns: [
        {
          title: '挂网区域',
          dataIndex: 'Name',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '挂网价',
          dataIndex: 'Price',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtn: [{ name: '移除', icon: '', id: '4e3fbebb-9cf7-48dd-9ed6-aff992aeb19f' }],
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        manageClassify2Url: '/{v}/GoodsManage/QueryGoodsManageClassifyList',
        getListDictItem: '/{v}/Global/GetListDictItem',
        // 搜索经营范围控制类别专用接口
        getBusinessScopeCategories: '/{v}/Global/GetBusinessScopeCategories',
        getYLQXClassManageSocpeList: '/{v}/GoodsManage/GetYLQXClassManageSocpeList',
        getBaseInfoDifferences: '/{v}/GoodsManage/GetBaseInfoDifferences',
      },
    }
  },
  watch: {
    'model.HasWebPrice'(to) {
      if (to == 'true' || to == true) {
        this.$nextTick(() => {
          if (this.$refs.table) this.$refs.table.dataSource = this.model['WebPrice']
        })
      }
    },
  },
  computed: {
    // 是否显示销售信息
    showSaleInfo() {
      let status = (![3, 4].includes(this.opType) && !this.isAudit && !['ZP'].includes(this.model.ManageClassify1Code) && (this.model.AuthBusinessStatus == 1 || this.model.AuthBusinessStatus == undefined) && (this.model.AuditStatus == 0 || this.model.AuditStatus == undefined))
      return status
    },
    rules() {
      return {
        ManageClassify1: [{ required: true, message: '请选择!' }],
        ManageClassify2: [{ required: true, message: '请选择!' }],
        ErpGoodsName: [
          {
            required: true,
            validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200),
          },
        ],
        BrandManufacturer: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200) },
        ],
        // Producer: [
        //   {
        //     required: ['ZYYP_2', 'ZYC_2'].includes(this.model.ManageClassify2Code),
        //     validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200),
        //   },
        // ],
        //因为只有药品和进口药品才显示次字段 所以直接是必填
        LicensedHolder: [
          {
            required: true,
            validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200),
          },
        ],
        PackingSpecification: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200) },
        ],
        PackageUnit: [{ required: true, message: '请选择!' }],
        ApprovalNumber: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200) },
        ],
        ProductionEnterpriseLicenseNumber: [
          { required: false, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 40) },
        ],
        CriticalMaintenanceReason: [
          { required: false, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200) },
        ],
        Remark: [
          { required: false, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 100) },
        ],
        TermOfValidity: [{ required: true, validator: this.checkIntNumber }],
        StorageConditionName: [{ required: true, message: '请选择!' }],
        MiddlePackage: [
          {
            required: true,
            validator: (rule, value, callback) =>
              this.checkIntNumber(rule, value, (str) => {
                if (str != undefined) {
                  callback(str)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 14)
              }),
          },
        ],
        PurchaseTaxRate: [{ required: true, message: '请选择!' }],
        SellsTaxRate: [{ required: true, message: '请选择!' }],
        PackageCount: [
          { required: ['ZP'].includes(this.model.ManageClassify1Code) ? false : true, validator: this.checkIntNumber },
        ],
        StorageArea: [{ required: true, message: '请输入!' }],
        QualityStandardCode: [{ required: true, message: '请选择!' }],
        Supplier: [{ required: true, message: '请选择!' }],
        PurchaserId: [{ required: true, message: '请选择!' }],
        IsMedicalInsurance: [{ required: true, message: '请选择!' }],
        BusinessScopeName: [
          {
            required: this.model['ManageClassify1Code'] == 'YLQX' ? true : false,
            validator: (rule, value, callback) => {
              if (this.model['ManageClassify1Code'] == 'YLQX' && !value) {
                callback(new Error('请选择!'))
              } else {
                callback()
              }
            },
          },
        ],
        HasWebPrice: [
          {
            required: true,
            message: '请选择!',
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请选择!'))
              } else {
                callback()
              }
            },
          },
        ],
        BarCode: [
          {
            required: false,
            validator: (rule, value, callback) =>
              this.checkIntNumber2(rule, value, (str) => {
                if (str != undefined) {
                  callback(str)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 14)
              }),
          },
        ],
        ValidityPeriodOfApprovalDocument: [{ required: false, validator: this.checkIntTime }],
        ProductionEnterpriseLicenseTermOfValidity: [{ required: false, validator: this.checkIntTime }],
        PurchaseCostPrice: [
          {
            required: this.model['SupplierType'] == 2 ? true : false,
            // message: '请输入!',
            validator: (rule, value, callback) => {
              if (!value && rule.required) {
                callback(new Error('请输入!'))
                return
              }
              if (rule.required && value < 0.01) {
                callback(new Error('请输入!'))
                return
              }
              if (!rule.required && value == 0) {
                callback(new Error('请输入!'))
                return
              }
              callback()
              return
            },
          },
        ],
        AdviseSalePrice: [
          {
            required: false,
            // message: '请输入!',
            validator: (rule, value, callback) => {
              if (!value && rule.required) {
                callback(new Error('请输入!'))
                return
              }
              if (rule.required && value < 0.01) {
                callback(new Error('请输入!'))
                return
              }
              if (!rule.required && value == 0) {
                callback(new Error('请输入!'))
                return
              }
              callback()
              return
            },
          },
        ],
        TaxClassificationCode: [
          {
            required: this.opType == 3 || this.isAudit ? true : false,
            validator: (rule, value, callback) => {
              if (!this.model.TaxClassificationCode && rule.required) {
                callback(new Error('请选择!'))
                return
              }
              callback()
              return
              // let newSoftName = this.$refs.iTaxClassificationCode && this.$refs.iTaxClassificationCode['newSoftName']
              // if (!value && !newSoftName && rule.required) {
              //   callback(new Error('请输入或选择!'))
              //   return
              // }
              // callback()
              // return
            },
          },
        ],
        IsSpecialSymbol: [{ required: true, message: '请选择!' }],
        MedicalInsuranceCode: [{ required: true, message: '请输入!' }],
        IsHasGoodsTraceCode: [{ required: true, message: '请选择!' }],
        OperationalClassifyFullIds: [{ required: false, validator: (rule, value, callback) => {
              if (value && value.split("/").length < 5) {
                callback(new Error('运营分类需选到第5级，请重新选择!'))
                return
              }
              callback()
              return
            },}]
      }
    },
  },
  created() { },
  mounted() {
    if (this.Info) {
      this.model = Object.assign({ HasWebPrice: '', Remark: this.Remark }, this.Info)
      this.model.ManageClassify1Code = this.Info['ManageClassifyCode1']
      this.model.ManageClassify2Code = this.Info['ManageClassifyCode2']
      this.model.SupplierType = this.Info['SupplierType']
      this.model.Supplier = this.model.Supplier || ''
      this.model.PurchaserId = this.model.PurchaserId || ''
      this.model.IsCriticalMaintenance =
        this.model.IsCriticalMaintenance == null ? '' : '' + this.model.IsCriticalMaintenance
      this.model.IsMedicalInsurance = this.model.IsMedicalInsurance == null ? '' : '' + this.model.IsMedicalInsurance
      this.model.TermOfValidity = this.Info.TermOfValidity == null ? '' : '' + this.Info.TermOfValidity
      this.model.PurchaseTaxRate = this.Info.PurchaseTaxRate == null ? '' : '' + this.Info.PurchaseTaxRate
      this.model.SellsTaxRate = this.Info.SellsTaxRate == null ? '' : '' + this.Info.SellsTaxRate
      // this.model.HasWebPrice = this.model.HasWebPrice == null ? '' : '' + this.model.HasWebPrice
      this.model.IsRegulatoryBarcodeVariety =
        this.model.IsRegulatoryBarcodeVariety == null ? '' : '' + this.model.IsRegulatoryBarcodeVariety
      this.model.IsSpecialSymbol = this.model.IsSpecialSymbol == null ? '' : '' + this.model.IsSpecialSymbol
      this.model.CategorizationOfPrescriptionCode = this.model.CategorizationOfPrescriptionCode || ''
      this.model.TaxClassificationCode = this.model.TaxClassificationCode || ''
      this.model.TaxClassificationNameStr = (this.model.TaxClassificationName || this.model.TaxClassificationCode) ? (this.model.TaxClassificationName || '') + '/' + (this.model.TaxClassificationCode || '') : ''
      this.model.IsHasGoodsTraceCode = this.model.IsHasGoodsTraceCode == null ? '' : '' + this.model.IsHasGoodsTraceCode
      this.model.IsForceScanTraceCode = this.model.IsForceScanTraceCode == null ? '' : '' + this.model.IsForceScanTraceCode
      if (this.model.ValidityPeriodOfApprovalDocument) {
        this.model.ValidityPeriodOfApprovalDocument = moment(this.model.ValidityPeriodOfApprovalDocument).format(
          'YYYY-MM-DD'
        )
      }
      if (this.model.ProductionEnterpriseLicenseTermOfValidity) {
        this.model.ProductionEnterpriseLicenseTermOfValidity = moment(
          this.model.ProductionEnterpriseLicenseTermOfValidity
        ).format('YYYY-MM-DD')
      }

      // console.log(this.model.TaxClassificationCode)
      // console.log(this.$refs.iTaxClassificationCode.value)
      // console.log(this.$refs.iTaxClassificationCode.newSoftName)
      // if (this.model.TaxClassificationCode == this.model.TaxClassificationName) {
      //   if (this.$refs.iTaxClassificationCode) {
      //     this.$refs.iTaxClassificationCode.newSoftName = this.model.TaxClassificationName
      //   }
      // } else {
      //   if (this.$refs.iTaxClassificationCode) {
      //     this.$refs.iTaxClassificationCode.newSoftName = this.model.TaxClassificationCode
      //   }
      // }
      // this.model.HasWebPrice = ''
      if ((this.model['WebPrice'] || []).length) {
        this.model.HasWebPrice = 'true'
      } else {
        this.model.HasWebPrice = 'false'
      }
      // 需要标红的字段
      if (this.isAudit) this.getBaseInfoDifferences()

      // 商品分类
      this.isShowManageClassify2 = false
      this.iStorageConditionCode = false
      this.IsQualityStandardCode = false
      this.IsSellsTaxRate = false
      this.IsPurchaseTaxRate = false
      this.$nextTick(() => {
        this.isShowManageClassify2 = true
        this.iStorageConditionCode = true
        this.IsQualityStandardCode = true
        this.IsSellsTaxRate = true
        this.IsPurchaseTaxRate = true
      })
    }
  },
  methods: {
    moment,
    handleManageClassify1(val, txt, item) {
      this.model.ManageClassify1Code = item['Code']
      this.model.ManageClassify2 = null
      this.model.ManageClassify2Code = ''
      this.model.LicensedHolder = null
      this.isShowManageClassify2 = false
      this.IsBusinessScopeName = false
      this.$nextTick(() => {
        this.IsBusinessScopeName = true
        this.isShowManageClassify2 = true
        if (this.model.ManageClassify1Code == 'HZP') {
          this.model.ManageClassify2Code = 'HZP_2'
          this.model.ManageClassify2 = '752e4cd3-8d35-4746-9a85-89c2c8de449f'
        }
        if (this.model.ManageClassify1Code == 'YP') {
          this.model.ManageClassify2Code = 'YP_2'
          this.model.ManageClassify2 = '61dc1b8e-b521-4eef-8b18-ca40a4cda072'
        }
        // if (this.model.ManageClassify1Code == 'YLQX') {
        this.model.BusinessScopeCode = ''
        this.model.BusinessScopeName = ''
        // }
      })
      this.$refs.ruleForm.clearValidate('BusinessScopeName')
      this.$emit('changeManageClassify', this.model.ManageClassify1Code || '')
    },
    // 列表操作
    operate(record, type) {
      if (type == '移除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认移除该数据？',
          onOk() {
            let WebPrice = that.model['WebPrice'].filter((v) => v['Code'] != record['Code'])
            that.$set(that.model, 'WebPrice', WebPrice)
            that.$refs.table.dataSource = that.model['WebPrice']
          },
          onCancel() { },
        })
      }
    },
    // 税收分类编码
    handelTaxClassificationCode(val, txt, item) {
      // console.log('税收分类编码',this.model.SellsTaxRate)
      if (!this.model.SellsTaxRate) {
        this.$message.warning('请先选择销售税率！')
        return
      }
      this.$refs.iSelectTaxClassificationCode.show(this.model.SellsTaxRate)
      // // console.log(val, txt, item)
      // this.model.TaxClassificationCode = val
      // this.model.TaxClassificationName = item ? item['ItemValue'] : ''
    },
    handleSelectTaxClassificationCodeOk(data) {
      this.$set(this.model, 'TaxClassificationCode', data['TaxCode'] || '')
      this.$set(this.model, 'TaxClassificationName', data['TaxCategoryName'] || '')
      this.$set(this.model, 'TaxClassificationNameStr', (data['TaxCategoryName'] || '') + '/' + (data['TaxCode'] || ''))
      // console.log('税收分类编码===========',this.model)
      this.$refs.ruleForm.clearValidate('TaxClassificationCode')
      this.$forceUpdate()

    },
    // 新增挂网价回调
    handleWebPriceSetModalOk(formData) {
      //  Name: '',
      //     Code: '',
      //     Price: null,
      //     Sort: null
      let index = this.model['WebPrice'].findIndex((v) => v['Code'] == formData['Code'])
      if (index < 0) {
        this.model['WebPrice'].push(formData)
      } else {
        this.$set(this.model['WebPrice'], index, formData)
      }
    },
    // 保存为草稿
    //     按钮判断：
    // 1、至少填写了商品名称、生产厂家、规格、商品类别、商品分类
    // 2、商品名称、生产厂家、规格合起来看和商品中其他商品不能重复。
    onSubmitIsDraft(isClickEdit) {
      // console.log(this.opType)
      this.isClickEdit = isClickEdit
      this.$refs.ruleForm.clearValidate()
      let arr = []

      this.$refs.ruleForm.validateField(
        ['ErpGoodsName', 'BrandManufacturer', 'PackingSpecification', 'ManageClassify1', 'ManageClassify2'],
        (valid) => {
          arr.push(valid ? false : true)
        }
      )

      if (this.model.ManageClassify1Code == 'ZP') {
        this.$refs.ruleForm.validate((valid) => {
          arr.push(valid)
        })
      }
      if (arr.some((v) => !v)) {
        return null
      }
      return this.setFormData()
    },
    onSubmit() {
      this.$refs.ruleForm.clearValidate()
      // console.log(this.opType)
      let returnValid = false
      if (this.model['HasWebPrice'] == 'true' && (this.model['WebPrice'] || []).length < 1) {
        this.$message.warning('请添加挂网价!')
        return returnValid
      }
      this.$refs.ruleForm.validate((valid) => {
        returnValid = valid
      })

      if (!returnValid) {
        if (this.model.IsMedicalInsurance == 'true' && this.$refs.HealthInsuranceForm && !this.isClickEdit) {
          this.$refs.HealthInsuranceForm.getData()
        }
        return returnValid
      }
      return this.setFormData()
    },
    setFormData() {
      // return
      let formData = JSON.parse(JSON.stringify(this.model))
      // if (
      //   this.$refs.iTaxClassificationCode &&
      //   this.$refs.iTaxClassificationCode['newSoftName'] != this.$refs.iTaxClassificationCode['value']
      // ) {
      //   formData['TaxClassificationCode'] = this.$refs.iTaxClassificationCode['newSoftName']
      //   formData['TaxClassificationName'] = this.$refs.iTaxClassificationCode['newSoftName']
      // }
      // console.log(formData)
      // return
      formData['HasWebPrice'] = this.reBool(formData['HasWebPrice'])
      formData['IsMedicalInsurance'] = this.reBool(formData['IsMedicalInsurance'])
      formData['TermOfValidity'] = formData['TermOfValidity'] == '' ? null : Number(formData['TermOfValidity'])
      formData['IsRegulatoryBarcodeVariety'] = this.reBool(formData['IsRegulatoryBarcodeVariety'])
      formData['IsSpecialSymbol'] = this.reBool(formData['IsSpecialSymbol'])
      formData['IsCriticalMaintenance'] = this.reBool(formData['IsCriticalMaintenance'])
      formData['Supplier'] = formData['Supplier'] == '' ? null : formData['Supplier']
      formData['ValidityPeriodOfApprovalDocument'] = formData['ValidityPeriodOfApprovalDocument'] || null
      formData['ProductionEnterpriseLicenseTermOfValidity'] =
        formData['ProductionEnterpriseLicenseTermOfValidity'] || null
      formData['PackageCount'] = '' + formData['PackageCount']
      if (formData['HasWebPrice']) {
        formData['WebPrice'] = formData['WebPrice']
      } else {
        formData['WebPrice'] = []
      }
      // 合并医保的数据
      let yibaoData = {}
      if (this.model.IsMedicalInsurance == 'true' && this.$refs.HealthInsuranceForm && !this.isClickEdit) {
        yibaoData = this.$refs.HealthInsuranceForm.getData() || {}
        if (Object.keys(yibaoData).length === 0) {
          return false
        }
      } else {
        // 保存医保信息草稿
        if (this.$refs.HealthInsuranceForm) {
          let isNotCheck = this.isClickEdit ? true : false
          yibaoData = this.$refs.HealthInsuranceForm.getData(isNotCheck) || {}
        }
      }
      formData['IsHasGoodsTraceCode'] = this.reBool(formData['IsHasGoodsTraceCode'])
      formData['IsForceScanTraceCode'] = this.reBool(formData['IsForceScanTraceCode'])
      formData.showSaleInfo = this.showSaleInfo
      formData = Object.assign({}, formData, yibaoData)
      return formData
    },
    reBool(key) {
      return key == '' ? null : key === 'false' ? false : true
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
    },
    // 来货单位
    handleSupplier(val, txt, item) {
      this.model.SupplierName = txt
      this.model.SupplierType = val ? item['Type'] : null
      this.$refs.ruleForm.clearValidate('PurchaseCostPrice')
      //来货单位的企业类型 1批发企业 2生产企业
      this.$emit('supplierChange', this.model.SupplierType)
    },
    // 需要标红的字段
    getBaseInfoDifferences() {
      if (!this.model['Id'] && !this.isAudit) {
        return
      }
      getAction(this.url.getBaseInfoDifferences, { goodsSpuId: this.model['Id'] }, 'P36006').then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.redFileds = res.Data || []
      })
    },
    returnRed(key) {
      if ((this.redFileds || []).length < 1) {
        return false
      }
      return this.redFileds.findIndex((v) => v == key) > -1 ? true : false
    },
    // 销售税率： 改变 清空税收分类编码：
    handleSellsTaxRateChange(val, txt, item) {
      // console.log('销售税率SellsTaxRate   ',this.model.SellsTaxRate)
      this.$set(this.model, 'TaxClassificationCode', '')
      this.$set(this.model, 'TaxClassificationName', '')
      this.$set(this.model, 'TaxClassificationNameStr', '')
      // console.log('税收分类编码===========',this.model)
      this.$refs.ruleForm.validateField('TaxClassificationCode')
      this.$forceUpdate()

    },
    /**
     * 选择运营分类
     **/

    onOperationalClassifyChange(val, node) {
      console.log('onOperationalClassifyChange', val, node)
      if (!val) {
        return
      }
      if(val.length) {
        this.$set(this.model, 'OperationalClassifyFullIds', val.join('/'))
        this.$set(this.model, 'OperationalClassifyFullNames', node.join('/'))
      } else {
        this.$set(this.model, 'OperationalClassifyFullIds', '')
        this.$set(this.model, 'OperationalClassifyFullNames', '')
      }
      console.log('onOperationalClassifyChange ~~', this.model.OperationalClassifyFullNames)
    },
  },
}
</script>

<style lang="less" scoped>
.form-model-style {
  /deep/.ant-form-item-control {
    height: 34px;
  }
}
.form-model-style-scroll {
  height: 54vh;
  overflow-y: auto;
  overflow-x: hidden;
}
.red {
  /deep/.ant-form-item-label > label {
    color: red;
  }
}
</style>
