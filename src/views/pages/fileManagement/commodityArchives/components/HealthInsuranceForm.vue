<template>
  <a-form-model ref="yibaoForm" :rules="rules" :model="model">
    <a-col style="padding:0;">
      <a-col :span="md" v-if="this.isShowItem">
        <a-form-model-item label="医保类型：" prop="MedicalInsuranceType">
          <EnumSingleChoiceView style="width: 100%" placeholder="请选择" dictCode="EnumMedicalInsurance" v-model="model.MedicalInsuranceType" />
        </a-form-model-item>
      </a-col>
      <a-col :span="md" v-if="this.isShowItem">
        <a-form-model-item label="是否基药：" prop="IsBaseDrug">
          <a-select placeholder="请选择" v-model="model.IsBaseDrug" @change="changeIsBaseDrug">
            <a-select-option :value="''"> 请选择 </a-select-option>
            <a-select-option v-for="(r, rIndex) in [
                      { title: '是', value: 'true' },
                      { title: '否', value: 'false' },
                    ]" :key="rIndex" :value="r.value">
              {{ r.title }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :span="md" v-if="this.isShowItem && model.IsBaseDrug == 'true'">
        <a-form-model-item label="基药区域：" prop="jyKeys">
          <div style="display: flex;align-items: center;justify-content: space-between;">
            <MultipleChoiceView style="flex: 1;margin-right:8px;" placeholder="请选择区域" :open="false" :maxTagCount="maxTagCount" :localDataList="model['jyDataList']" v-model="model['jyKeys']" :dataKey=" { name: 'Name', value: 'Code' }" ref="iCustomerType" @change="(value, contents)=>changeArea(value, contents,'jyDataList')" />
            <a style="white-space: nowrap;" @click="chooseArea('jyDataList')">选择区域</a>
          </div>
        </a-form-model-item>
      </a-col>
      <a-col :span="md" v-if="this.isShowItem">
        <a-form-model-item label="是否集采：" prop="IsCentralizedPurchasing">
          <a-select placeholder="请选择" v-model="model.IsCentralizedPurchasing">
            <a-select-option :value="''"> 请选择 </a-select-option>
            <a-select-option v-for="(r, rIndex) in [
                      { title: '是', value: 'true' },
                      { title: '否', value: 'false' },
                    ]" :key="rIndex" :value="r.value">
              {{ r.title }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :span="md" v-if="this.isShowItem && model.IsCentralizedPurchasing == 'true'">
        <a-form-model-item label="集采区域：" prop="jcKeys">
          <div style="display: flex;align-items: center;justify-content: space-between;">
            <MultipleChoiceView style="flex: 1;margin-right:8px;" placeholder="请选择区域" :open="false" :maxTagCount="maxTagCount" :localDataList="model['jcDataList']" v-model="model['jcKeys']" :dataKey=" { name: 'Name', value: 'Code' }" ref="iCustomerType" @change="(value, contents)=>changeArea(value, contents,'jcDataList')" />
            <a style="white-space: nowrap;" @click="chooseArea('jcDataList')">选择区域</a>
          </div>
        </a-form-model-item>
      </a-col>
      <a-col :span="md" v-if="this.isShowItem">
        <a-form-model-item label="是否统筹：" prop="IsOverallPlanning">
          <a-select placeholder="请选择" v-model="model.IsOverallPlanning">
            <a-select-option :value="''"> 请选择 </a-select-option>
            <a-select-option v-for="(r, rIndex) in [
                      { title: '是', value: 'true' },
                      { title: '否', value: 'false' },
                    ]" :key="rIndex" :value="r.value">
              {{ r.title }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col :span="md" v-if="this.isShowItem && model.IsOverallPlanning == 'true'">
        <a-form-model-item label="统筹区域：" prop="tcKeys">
          <div style="display: flex;align-items: center;justify-content: space-between;">
            <MultipleChoiceView style="flex: 1;margin-right:8px;" placeholder="请选择区域" :open="false" :maxTagCount="maxTagCount" :localDataList="model['tcDataList']" v-model="model['tcKeys']" :dataKey=" { name: 'Name', value: 'Code' }" ref="iCustomerType" @change="(value, contents)=>changeArea(value, contents,'tcDataList')" />
            <!-- <MultipleChoiceView style="flex: 1;margin-right:8px;" placeholder="请选择区域" Url="/{v}/Global/GetListDictItem" :maxTagCount="5" httpHead="P36001" :httpParams="{ pageindex: 1, pagesize: 1000,groupPY:'yongyfl' }" v-model="model.areaType3" :dataKey=" { name: 'ItemValue', value: 'ItemPym' }" ref="iCustomerType" /> -->
            <a style="white-space: nowrap;" @click="chooseArea('tcDataList')">选择区域</a>
          </div>
        </a-form-model-item>
      </a-col>

      <!-- 选择区域 -->
      <SelectAreaTree ref="SelectAreaTree" filterCountry="onlyProvince" :level="1" :title="areaTitle" @ok="selectAreaOk" />
    </a-col>
  </a-form-model>
</template>

<script>
import { getAction } from '@/api/manage'
export default {
  name: 'HealthInsuranceForm',
  title: '医保form框',
  data() {
    return {
      areaTitle: '选择区域',
      maxTagCount: 4,
      model: {
        jyDataList: [],
        jcDataList: [],
        tcDataList: [],
        jyKeys: [],
        jcKeys: [],
        tcKeys: [],
      },
      modelType: '',
      dataSource: [],
      url: {
        listLevelArea: '/{v}/AdministrativeArea/AllList',
      }
    }
  },
  props: {
    md: {
      type: [Number, String],
      default: null,
    },
    info: {
      type: Object,
      default: () => {
        return {

        }
      }
    },
  },
  watch: {
    'model.IsBaseDrug'(to) {
      if (!to || to == 'false') {
        this.model['jyDataList'] = []
        this.model['jyKeys'] = []
      }
    },
    'model.IsCentralizedPurchasing'(to) {
      if (!to || to == 'false') {
        this.model['jcDataList'] = []
        this.model['jcKeys'] = []
      }
    },
    'model.IsOverallPlanning'(to) {
      if (!to || to == 'false') {
        this.model['tcDataList'] = []
        this.model['tcKeys'] = []
      }
    },

  },
  created() {
    this.getInfo()
    this.getAllTreeData()
    this.updateScreenWidth(); // 初始获取屏幕宽度
    window.addEventListener('resize', this.updateScreenWidth); // 监听屏幕大小改变
  },
  computed: {
    rules() {
      return {
        MedicalInsuranceType: [{ required: true, message: '请选择!' }],
        IsBaseDrug: [{ required: true, message: '请选择!' }],
        jyKeys: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.jyKeys || this.model.jyKeys.length == 0) {
                callback(new Error('请选择区域!'))
              } else {
                callback()
              }
            }
          }
        ],
        IsCentralizedPurchasing: [{ required: true, message: '请选择!' }],
        jcKeys: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.jcKeys || this.model.jcKeys.length == 0) {
                callback(new Error('请选择区域!'))
              } else {
                callback()
              }
            }
          }
        ],
        IsOverallPlanning: [{ required: true, message: '请选择!' }],
        tcKeys: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.tcKeys || this.model.tcKeys.length == 0) {
                callback(new Error('请选择区域!'))
              } else {
                callback()
              }
            }
          }
        ],

      }
    },
    isShowItem() {
      return this.info.IsMedicalInsurance == 'true' && ['YP_2', 'JKYP_2'].includes(this.info.ManageClassify2Code ? this.info.ManageClassify2Code : this.info.ManageClassifyCode2)
    },
  },
  methods: {
    getInfo() {
      let jyDataList = []
      let jyKeys = []
      let jcDataList = []
      let jcKeys = []
      let tcDataList = []
      let tcKeys = []
      // 基药区域
      if (this.info.BaseDrugAreas && this.info.BaseDrugAreas.length > 0) {
        this.info.BaseDrugAreas.map(item => {
          jyKeys.push(item.AreaCode)
          jyDataList.push({ Code: item.AreaCode, Name: item.AreaName })
        })
      }
      // 集采区域
      if (this.info.CentralizedPurchasingAreas && this.info.CentralizedPurchasingAreas.length > 0) {
        this.info.CentralizedPurchasingAreas.map(item => {
          jcKeys.push(item.AreaCode)
          jcDataList.push({ Code: item.AreaCode, Name: item.AreaName })
        })
      }
      // 统筹区域
      if (this.info.OverallPlanningAreas && this.info.OverallPlanningAreas.length > 0) {
        this.info.OverallPlanningAreas.map(item => {
          tcKeys.push(item.AreaCode)
          tcDataList.push({ Code: item.AreaCode, Name: item.AreaName })
        })
      }

      this.model = {
        MedicalInsuranceType: (this.info.MedicalInsuranceType || this.info.MedicalInsuranceType == 0) ? String(this.info.MedicalInsuranceType) : undefined,
        jyDataList: jyDataList || [],
        jcDataList: jcDataList || [],
        tcDataList: tcDataList || [],
        jyKeys: jyKeys || [],
        jcKeys: jcKeys || [],
        tcKeys: tcKeys || [],
        IsBaseDrug: (!this.info.IsBaseDrug && typeof (this.info.IsBaseDrug) != 'boolean') ? undefined : String(this.info.IsBaseDrug),
        IsCentralizedPurchasing: (!this.info.IsCentralizedPurchasing && typeof (this.info.IsCentralizedPurchasing) != 'boolean') ? undefined : String(this.info.IsCentralizedPurchasing),
        IsOverallPlanning: (!this.info.IsOverallPlanning && typeof (this.info.IsOverallPlanning) != 'boolean') ? undefined : String(this.info.IsOverallPlanning),
      }
    },
    // 获取所有的数据，先从session里面取，没取到再调接口
    getAllTreeData() {
      const sessionData = sessionStorage.getItem('AREA_TREE_DATA')
      if (sessionData) {
        this.dataSource = JSON.parse(sessionData) || []
        return
      }
      let params = {
        level: 1,
      }
      this.loading = true
      getAction(this.url.listLevelArea, params, 'P31100')
        .then(res => {
          if (res.IsSuccess) {
            this.dataSource = res.Data || []
            sessionStorage.setItem('AREA_TREE_DATA', JSON.stringify(res.Data))
          } else {
            this.$message.warning(res.Msg)
            this.dataSource = []
          }
        })
        .finally(e => {
          this.loading = false
        })
    },
    changeIsBaseDrug(e) {
      if (this.model.IsBaseDrug) {
        let codeArray = this.dataSource.map(item => {
          return item.Code
        })
        this.model['jyKeys'] = codeArray
        this.model['jyDataList'] = this.dataSource
      } else {
        this.model['jyKeys'] = []
        this.model['jyDataList'] = []
      }
    },
    chooseArea(type) {
      let defaultSelected = this.model[type] || []
      this.modelType = type
      this.$refs.SelectAreaTree.show(defaultSelected)
    },
    changeArea(value, contents, type) {
      this.setAreaData(contents, type)
    },
    // 动态的获取区域最大显示数
    updateScreenWidth() {
      let screenWidth = window.innerWidth;
      if (screenWidth <= 890) {
        this.maxTagCount = 1
      } else if (screenWidth <= 910) {
        this.maxTagCount = 2
      } else if (screenWidth <= 1256) {
        this.maxTagCount = 3
      } else {
        this.maxTagCount = 4
      }
    },
    selectAreaOk(data) {
      this.setAreaData(data, this.modelType)
    },
    setAreaData(data, type) {
      let codeArray = data.map(item => {
        return item.Code
      })
      let keys = null
      this.model[type] = data || []
      switch (type) {
        case 'jyDataList':
          keys = 'jyKeys'
          this.model[keys] = codeArray
          break;
        case 'jcDataList':
          keys = 'jcKeys'
          this.model[keys] = codeArray
          break;
        case 'tcDataList':
          keys = 'tcKeys'
          this.model[keys] = codeArray
          break;
      }
      let arr = []
      this.$refs.yibaoForm.validateField([keys], (valid) => {
        arr.push(valid ? false : true)
      })

      // 有false的情况下返回null
      if (arr.some((v) => !v)) {
        return null
      }
    },
    reBool(key) {
      return (key == '' || key == undefined) ? null : (key === 'false' ? false : true)
    },
    getData(isNotCheck) {
      this.$refs.yibaoForm.clearValidate()
      let status = null
      if (isNotCheck !== true) {
        this.$refs.yibaoForm.validate((data, values) => {
          if (data) {
            status = true
          }
        });
      }
      if (status || isNotCheck) {
        let BaseDrugAreas = []
        let CentralizedPurchasingAreas = []
        let OverallPlanningAreas = []
        if (this.model.jyDataList.length > 0) {
          BaseDrugAreas = this.model.jyDataList.map(item => {
            return {
              AreaCode: item.Code,
              AreaName: item.Name,
              ParentCode: "00",
            }
          })
        }
        if (this.model.jcDataList.length > 0) {
          CentralizedPurchasingAreas = this.model.jcDataList.map(item => {
            return {
              AreaCode: item.Code,
              AreaName: item.Name,
              ParentCode: "00",
            }
          })
        }
        if (this.model.tcDataList.length > 0) {
          OverallPlanningAreas = this.model.tcDataList.map(item => {
            return {
              AreaCode: item.Code,
              AreaName: item.Name,
              ParentCode: "00",
            }
          })
        }
        let params = {
          ...this.model,
          BaseDrugAreas: BaseDrugAreas,//基药区域
          CentralizedPurchasingAreas: CentralizedPurchasingAreas,//集采区域
          OverallPlanningAreas: OverallPlanningAreas,//统筹区域
        }
        params.MedicalInsuranceType = (params.MedicalInsuranceType || params.MedicalInsuranceType == 0) ? Number(params.MedicalInsuranceType) : null
        params.IsBaseDrug = this.reBool(params.IsBaseDrug)
        params.IsCentralizedPurchasing = this.reBool(params.IsCentralizedPurchasing)
        params.IsOverallPlanning = this.reBool(params.IsOverallPlanning)
        console.log('form', params)
        // 没有则清空数据
        if (!this.isShowItem) {
          params = {
            ...this.model,
            MedicalInsuranceType: null,
            IsBaseDrug: null,
            IsCentralizedPurchasing: null,
            IsOverallPlanning: null,
            BaseDrugAreas: [],//基药区域
            CentralizedPurchasingAreas: [],//集采区域
            OverallPlanningAreas: [],//统筹区域
          }
        }
        delete params.jcDataList
        delete params.jcKeys
        delete params.jyDataList
        delete params.jyKeys
        delete params.tcDataList
        delete params.tcKeys
        return params
      } else {
        return null
      }
    },

  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateScreenWidth); // 移除监听器
  },

}
</script>

<style>
</style>