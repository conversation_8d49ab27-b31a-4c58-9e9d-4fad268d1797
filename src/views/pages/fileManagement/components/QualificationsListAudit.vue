<!-- 资质列表 -->
<template>
  <!-- class="a-tag-box" -->
  <div>
    <a-row :gutter="[16, 6]" v-if="groupList && groupList.length > 0">
      <a-col :span="4">
        <!-- 资质列表 class="a-tag-box-type"-->
        <div>
          <div style="font-size: 18px; font-weight: bold; padding-bottom: 10px">证照列表</div>
          <div style="height: 58vh; overflow-y: auto" class="scroll-style">
            <div v-for="(group, gIndex) in groupList" :key="group.Title" @click="onGroupClick(group, gIndex)" class="group" :class="{ groups: groupIndex == gIndex }">
              <span :class="{ warning: group.IsChanged && isEdit }" style="margin-bottom: 16px">
                {{ group.GroupName + '(' + getGroupImageSize(group) + ')' }}
              </span>
            </div>
          </div>
        </div>
      </a-col>
      <a-col :span="20" v-if="groupList[groupIndex]">
        <a-form-model ref="auditForm" :model="model" :layout="isEdit ? 'vertical' : 'horizontal'">
          <template v-for="(groupItem, gItemIndex) in groupList[groupIndex].Qualifications || []">
            <a-col :span="24" v-if="groupItem.QualificationType == 1" :key="gItemIndex">
              <div class="preview-box">
                <div class="container">
                  <!-- PDF展示 -->
                  <div class="pdf-box" v-if="
                      groupItem.Files.length > 0 &&
                      groupItem.Files[imgIndex].FileUrl &&
                      groupItem.Files[imgIndex].FileUrl.indexOf('.pdf' || '.PDF') > -1
                    ">
                    <pdf width="100%" height="100px" ref="pdf" :rotate="pageRotate" :src="getPdfUrl(groupItem.Files[imgIndex].FileUrl)" v-for="(i, index) in numPages" :page="i" :key="index"></pdf>
                    <div class="pdf-operator">
                      <div class="pdf-operator-ctn">
                        <div @click="operator('nsz')">
                          <a-icon style="font-size: 30px" type="redo" />
                          <span>顺时针</span>
                        </div>
                        <div @click="operator('ssz')">
                          <a-icon style="font-size: 30px" type="undo" />
                          <span>逆时针</span>
                        </div>
                        <div @click="operator('yl', groupItem.Files[imgIndex].FileUrl)">
                          <a-icon style="font-size: 30px" type="fullscreen" />
                          <span>预览</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 图片展示 -->
                  <div class="container-box" v-else>
                    <div class="option-btn" v-if="showUploadAndDelete">
                      <a-dropdown placement="bottomLeft">
                        <a-button type="primary">上传图片</a-button>
                        <a-menu slot="overlay">
                          <a-menu-item>
                            <upload-button text="本地上传" @uploadOK="onAddPictureClick" />
                          </a-menu-item>
                          <a-menu-item>
                            <a-button type="primary" @click="onUploadClick(groupIndex, groupItem, gItemIndex)">拍照上传</a-button>
                          </a-menu-item>
                        </a-menu>
                      </a-dropdown>
                      <a-button class="ml8" type="danger" @click="onDeletePictureClick(gItemIndex)" v-if="groupItem.Files.length > 0">删除</a-button>
                    </div>
                    <a-carousel @afterChange="previewChange" dotPosition="bottom" :autoplay="false" height="58vh" style="background: rgba(0, 0, 0, 0.4)">
                      <viewer :images="groupItem.Files" v-viewer="{
                          inline: true,
                          fullscreen: false,
                          navbar: false,
                          title: false,
                          button: false,
                          view: onViewed,
                        }">
                        <div style="height: 58vh; margin: auto; padding: 20px">
                          <img :src="
                              groupItem.Files.length > 0 && imgIndex < groupItem.Files.length
                                ? groupItem.Files[imgIndex].FileUrl
                                : require('@/assets/image/wutu.png')
                            " style="height: 100%; margin: auto" class="img-style" id="image" />
                        </div>
                      </viewer>
                    </a-carousel>
                  </div>
                  <!-- 图片分页 -->
                  <div class="container-page" v-if="groupItem.Files.length > 0">
                    <span v-for="(file, index) in groupItem.Files" :key="index" :class="{
                        'page-active': groupItem.Files[imgIndex].FileUrl === file.FileUrl,
                      }"></span>
                  </div>
                  <!-- 上一页 -->
                  <div class="page-prev" @click="onChangeImageClick('prev', groupItem.Files)" v-if="groupIndex > 0 || (groupIndex === 0 && imgIndex > 0)">
                    <a-icon type="left" />
                  </div>
                  <!-- 下一页 -->
                  <div class="page-next" @click="onChangeImageClick('next', groupItem.Files)" v-if="
                      groupIndex < groupList.length - 1 ||
                      (groupIndex === groupList.length - 1 && imgIndex < groupItem.Files.length - 1)
                    ">
                    <a-icon type="right" />
                  </div>
                </div>
              </div>
            </a-col>

            <!-- 文本 -->
            <a-col :span="12" v-if="groupItem.QualificationType == 4" :key="gItemIndex">
              <a-form-model-item v-if="isEdit" :class="{ red: groupItem.IsChanged && isEdit }" :label="groupItem.QualificationName ? groupItem.QualificationName + '：' : ''" :key="groupIndex + groupItem.QualificationCode + gItemIndex" :prop="groupIndex + groupItem.QualificationCode + gItemIndex" :rules="{
                  required: groupItem.IsMust,
                  validator: (rule, value, callback) => {
                    if (!groupList[groupIndex].Qualifications[gItemIndex].QualificationValue) {
                      callback('请输入!')
                    } else {
                      if (
                        groupList[groupIndex].Qualifications[gItemIndex].QualificationCode.indexOf('IdCardNo') != -1
                      ) {
                        let val = groupList[groupIndex].Qualifications[gItemIndex].QualificationValue
                        checkIdNo(rule, val, callback)
                        // if (!IDRe18.test(val) && !IDre15.test(val)) {
                        //   callback(groupItem.QualificationName + '有误！')
                        // } else {
                        //   callback()
                        // }
                      } else if (
                        groupList[groupIndex].Qualifications[gItemIndex].QualificationName.indexOf('姓名') > -1 ||
                        groupList[groupIndex].Qualifications[gItemIndex].QualificationName.indexOf('名称') > -1
                      ) {
                        validStrMaxLength(
                          rule,
                          groupList[groupIndex].Qualifications[gItemIndex].QualificationValue,
                          callback,
                          50
                        )
                      } else if (
                        groupList[groupIndex].Qualifications[gItemIndex].QualificationName.indexOf('证号') > -1
                      ) {
                        validStrMaxLength(
                          rule,
                          groupList[groupIndex].Qualifications[gItemIndex].QualificationValue,
                          callback,
                          100
                        )
                      } else {
                        callback()
                      }
                    }
                  },
                  // trigger: 'blur'
                }">
                <a-input placeholder="请输入" v-model="groupItem.QualificationValue" :maxLength="100" @blur="onInputInputBlur(groupItem, groupIndex + groupItem.QualificationCode + gItemIndex)" @focus="onInputInputFocus(groupItem)" />
                <span slot="help" class="cred" v-if="tipInfo && groupItem.QualificationCode == 'BusinessLincense-Number'">{{ tipInfo.text }}，<a @click="onTipBtnClick">{{ tipInfo.btn }}</a></span>
              </a-form-model-item>
              <span v-else>
                <span>{{ groupItem.QualificationName ? groupItem.QualificationName + '：' : '' }}</span>
                <span>{{ groupItem.QualificationValue }}</span>
              </span>
            </a-col>
            <!-- 日期 -->
            <a-col :span="12" v-if="groupItem.QualificationType == 3" :key="gItemIndex">
              <a-form-model-item v-if="isEdit" :class="{ red: groupItem.IsChanged && isEdit }" :label="groupItem.QualificationName ? groupItem.QualificationName + '：' : ''" :key="groupIndex + groupItem.QualificationCode + gItemIndex" :prop="groupIndex + groupItem.QualificationCode + gItemIndex" :rules="{
                  required: groupItem.IsMust,
                  validator: (rule, value, callback) => {
                    if (groupList[groupIndex].GroupName.indexOf('委托书') > -1) {
                      checkIntTime(rule, groupList[groupIndex].Qualifications[gItemIndex].QualificationValue, (msg) => {
                        checkStartAndEndTime(groupIndex, gItemIndex, msg, callback)
                      })
                    } else {
                      checkIntTime(rule, groupList[groupIndex].Qualifications[gItemIndex].QualificationValue, callback)
                    }
                  },
                  // trigger: 'blur'
                }">
                <a-input placeholder="请输入" v-model="groupItem.QualificationValue" :maxLength="10" @change="
                    (e) => {
                      groupList[groupIndex].Qualifications[gItemIndex].QualificationValue = getShowDate(e.target.value)
                    }
                  ">
                  <template #suffix v-if="groupItem.QualificationCode.indexOf('-BeginDate') == -1">
                    <a-checkbox v-model="groupItem.Checked" @change="(e) => onValidDateChange(e, groupIndex, gItemIndex)" class="qualification-check">长期</a-checkbox>
                  </template>
                </a-input>
              </a-form-model-item>
              <span v-else>
                <span>{{ groupItem.QualificationName ? groupItem.QualificationName + '：' : '' }}</span>
                <span>{{ groupItem.QualificationValue }}</span>
              </span>
            </a-col>
            <!-- 类型 -->
            <a-col :span="12" v-if="groupItem.QualificationType == 5 && !isEdit" :key="gItemIndex">
              <span>
                <span>{{ groupItem.QualificationName ? groupItem.QualificationName + '：' : '' }}</span>
                <span>{{ groupItem.QualificationValue }}</span>
              </span>
            </a-col>
          </template>
        </a-form-model>
      </a-col>
    </a-row>
    <a-empty v-else style="margin-top: 150px">
      <span slot="description"> 无证照数据 </span>
    </a-empty>
    <!-- 摄像头弹窗 -->
    <PhotographModal ref="PhotographModal" @getPhotoUrl="getPhotoUrl" />
  </div>
</template>

<script>
// import pdf from 'vue-pdf'
import pdf from 'vue-pdf-signature'
import CMapReaderFactory from 'vue-pdf-signature/src/CMapReaderFactory.js'
import moment from 'moment'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
import { getAction } from '@/api/manage'

export default {
  name: 'QualificationsListAudit',
  mixins: [EditMixin, ListMixin],
  props: {
    // 监听父组件传递的row对象
    groupList: {
      type: Array,
      default: () => {
        return []
      },
    },
    // 是否可编辑
    isEdit: {
      type: Boolean,
      default: false,
    },
    /**
     * 来源类型 1供应商  2客户 3商品
     */
    fromType: {
      type: Number,
      default: 1,
    },
    /**
     * 业务id
     * 供应商就传供应商id
     * 客户就传客户id
     */
    businessId: {
      type: String,
      default: '',
    },
    /**
     * 显示新增和删除图片按钮
     */
    showUploadAndDelete: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      groupIndex: 0, //当前tab的index
      imgIndex: 0, //当前tab下File的当前显示index
      model: {},
      tipInfo: null,
      longTimeDate: '2099-12-31',
      pageRotate: 0,
      numPages: 1,
      pdfUrl: '',
      groupListData: this.groupList,
    }
  },
  components: {
    pdf,
    CMapReaderFactory,
  },
  watch: {
    groupList(val) {
      this.groupListData = val
      this.fristPdf()
    },
  },
  computed: {},
  created() {
    this.initImages()
  },
  methods: {
    moment,
    fristPdf() {
      // 首张是pdf获取页码
      if (
        this.groupListData[0] &&
        this.groupListData[0].Qualifications &&
        this.groupListData[0].Qualifications[0].Files &&
        this.groupListData[0].Qualifications[0].Files.length > 0
      ) {
        if (this.groupListData[0].Qualifications[0].Files[0].FileUrl.indexOf('.pdf' || '.PDF') > -1) {
          let url = this.groupListData[0].Qualifications[0].Files[0].FileUrl || ''
          this.getNumPages(url)
        }
      }
    },
    operator(type, url) {
      if (type == 'nsz') {
        if (this.pageRotate == 360) {
          this.pageRotate = 0
        }
        this.pageRotate = this.pageRotate + 90
      } else if (type == 'ssz') {
        if (this.pageRotate == 0) {
          this.pageRotate = 360
        }
        this.pageRotate = this.pageRotate - 90
      } else if (type == 'yl') {
        window.open(url)
      }
    },
    getPdfUrl(pdfUrl) {
      if (pdfUrl && pdfUrl.indexOf('.pdf' || '.PDF') >= 0) {
        return pdf.createLoadingTask({ url: pdfUrl, CMapReaderFactory })
      } else {
        return pdfUrl
      }
    },
    // 获取pdf的页码
    getNumPages(pdfUrl) {
      if (!pdfUrl || pdfUrl.indexOf('.pdf' || '.PDF') == -1) {
        return
      }
      let loadingTask = pdf.createLoadingTask(pdfUrl)
      loadingTask.promise
        .then((pdf) => {
          this.numPages = pdf.numPages
          console.log('页码是', pdf.numPages)
        })
        .catch((err) => {
          console.error('pdf 加载失败', err)
        })
    },
    initImages() {
      this.groupIndex = 0
      this.imgIndex = 0
      setTimeout(() => {
        if (document.querySelector('.img-style')) {
          document.querySelector('.img-style').style.display = 'none'
        }
      }, 500)
    },
    onViewed() {
      // document.querySelector('.img-style').style.display = 'none'
      this.$nextTick(() => {
        if (document.getElementById('image')) {
          document.getElementById('image').style.display = 'block'
        }
      })
    },
    /**
     * 左边tab项点击事件
     */
    onGroupClick(group, tIndex) {
      this.groupIndex = tIndex
      this.imgIndex = 0
      this.$forceUpdate()
    },
    /*
     * 大图预览
     */
    previewChange() {
      // 获取遮罩层dom
      let domImageMask = document.querySelector('.a-image-viewer__mask')
      if (!domImageMask) {
        return
      }
      document.querySelector('.a-image-viewer__close').click()
    },

    /**
     * 更具组下标获取当前文件集合
     * @param {*} gIndex
     */
    getFilesByGroupIndex(gIndex) {
      let files = []
      let item = this.groupList[gIndex].Qualifications.find((x) => {
        return x.QualificationType == 1
      })
      if (item) {
        files = item.Files
      }
      return files
    },
    /*
     * 图片分页器
     * type = prev 上一页
     * type = next 下一页
     */
    onChangeImageClick(type, files) {
      if (!this.groupList || this.groupList.length === 0) {
        return
      }

      this.previewChange()
      let curGroupFiles = this.getFilesByGroupIndex(this.groupIndex)
      //只有一个tab
      if (this.groupList.length === 1) {
        if (type === 'prev') {
          if (this.imgIndex > 0) {
            this.imgIndex = this.imgIndex - 1
          }
        } else if (type === 'next') {
          if (this.imgIndex < curGroupFiles.length - 1) {
            this.imgIndex = this.imgIndex + 1
          }
        }
      } else {
        //多个tab
        if (type === 'prev') {
          if (this.imgIndex > 0) {
            this.imgIndex = this.imgIndex - 1
          } else {
            if (this.groupIndex > 0) {
              this.groupIndex = this.groupIndex - 1
              let files = this.getFilesByGroupIndex(this.groupIndex)
              this.imgIndex = (files && files, length > 0) ? files.length - 1 : 0
            }
          }
        } else if (type === 'next') {
          if (this.imgIndex < curGroupFiles.length - 1) {
            this.imgIndex = this.imgIndex + 1
          } else {
            if (this.groupIndex < this.groupList.length - 1) {
              this.groupIndex = this.groupIndex + 1
              this.imgIndex = 0
            }
          }
        }
      }
      // this.$forceUpdate()
      console.log('groupIndex = ' + this.groupIndex + ' / imgIndex = ' + this.imgIndex)
      if (files.length > 0) {
        this.getNumPages(files[this.imgIndex].FileUrl)
      }
    },
    /**
     * 长期change
     */
    onValidDateChange(e, gIndex, gItemIndex) {
      let checked = e.target.checked
      this.groupList[gIndex].Qualifications[gItemIndex].Checked = checked
      if (checked) {
        this.groupList[gIndex].Qualifications[gItemIndex].QualificationValue = this.longTimeDate //moment(this.longTimeDate)
      } else {
        this.groupList[gIndex].Qualifications[gItemIndex].QualificationValue = null
      }
      console.log('QualificationValue = ' + this.groupList[gIndex].Qualifications[gItemIndex].QualificationValue)
    },

    /***
     * 检查数据-是否填写完整
     */
    checkData() {
      let bool = false
      this.$refs.auditForm.validate((valid) => {
        if (valid) {
          bool = true
        } else {
          bool = false
        }
      })
      if (!bool) {
        console.log('当前表单验证未通过')
        return false
      }
      let errList = []
      this.groupList.forEach((group) => {
        if (group.Qualifications) {
          group.Qualifications.forEach((item) => { 
            if (item.IsMust && item.QualificationType != 1 && !item.QualificationValue) {
              errList.push(item.QualificationName)
            }
            if(item.IsMust && item.QualificationType == 1 && !item.Files.length) {
              errList.push(item.QualificationName)
            }
          })
        }
      })
      if (errList.length > 0) {
        this.$message.error(String(errList) + '未填写！')
      }

      // if (this.tipInfo) {
      //   this.$message.error(this.tipInfo.text)
      //   return false
      // }
      return errList.length == 0
    },
    /**
     * 获取数据 checkData返回true再调用此方法
     * return 一个数组  就是项的集合（Qualifications）
     */
    getData() {
      let oldGroupList = JSON.parse(JSON.stringify(this.groupList))
      oldGroupList.forEach((group) => {
        if (group.ImgItemSize > 1) {
          let newQList = []
          if (group.OldQualifications) {
            group.OldQualifications.forEach((item) => {
              if (item.QualificationType == 1) {
                newQList.push(item)
              }
            })
          }
          if (group.Qualifications) {
            group.Qualifications.forEach((item) => {
              if (item.QualificationType != 1) {
                newQList.push(item)
              }
            })
          }
          group.Qualifications = newQList
        }
      })
      return oldGroupList
    },
    getGroupImageSize(group) {
      let size = 0
      if (group.Qualifications) {
        let list = group.Qualifications.filter((g) => g.QualificationType == 1)
        if (list && list.length > 0) {
          list.forEach((x) => {
            size += x.Files ? x.Files.length : 0
          })
        }
      }
      return size
    },
    /**
     * 营业执照输入框获取焦点事件
     */
    onInputInputFocus(item) {
      if (!item || item.QualificationCode != 'BusinessLincense-Number') {
        return
      }
      this.tipInfo = null
    },
    /**
     * 供应商名称输入框失去焦点事件
     */
    onInputInputBlur(item, prop) {
      if (!item || item.QualificationCode != 'BusinessLincense-Number') {
        return
      }
      let that = this
      this.$refs.auditForm.validateField([prop], (errMsg) => {
        if (!errMsg) {
          this.checkZZNumber(item.QualificationValue)
        } else {
          this.tipInfo = null
        }
      })
    },
    checkZZNumber(val) {
      let existsUrl = ''
      let existsHeephead = ''
      switch (this.fromType) {
        case 2:
          existsHeephead = 'P36002'
          existsUrl = '/{v}/Customer/SameIdByBusinessNumber'
          break
        default:
          existsHeephead = 'P36003'
          existsUrl = '/{v}/Supplier/GetSameIdByBusinessNumber'
          break
      }
      let that = this
      getAction(existsUrl, { id: this.businessId, businessNumber: val }, existsHeephead).then((res) => {
        if (res.IsSuccess) {
          if (res.Data) {
            that.tipInfo = {
              type: 1,
              text: '该营业执照证号已存在，无法新增',
              btn: '去查看',
              id: res.Data,
            }
          }
        }
      })
    },
    onTipBtnClick() {
      if (!this.tipInfo) {
        return
      }
      switch (this.tipInfo.type) {
        case 1: //去查看
          if (this.fromType == 1) {
            this.$router.push({
              path: '/pages/fileManagement/supplierProfile/SupplierProfileDetail',
              query: { id: this.tipInfo.id, opType: 4 },
            })
          } else if (this.fromType == 2) {
            this.$router.push({
              path: '/pages/fileManagement/commercialCustomerFile/commercialCustomerFileDetail',
              query: { id: this.tipInfo.id, acType: 4 },
            })
          }

          break
      }
    },
    /**
     * 委托书有开始时间结束时间的验证输入
     * @param {*} gIndex
     * @param {*} gItemIndex
     * @param {*} msg
     * @param {*} callback
     */
    checkStartAndEndTime(gIndex, gItemIndex, msg, callback) {
      // console.log('1111111111',gIndex, gItemIndex, msg, callback)
      if (!msg) {
        let curItem = this.groupList[gIndex].Qualifications[gItemIndex]
        let sygItem = null
        let beginValue = null
        let endValue = null
        // console.log('curItem = ', curItem)
        let type = -1
        //如果当前是开始时间输入框,则判断不能大于结束时间
        if (curItem.QualificationCode.indexOf('-BeginDate') > -1) {
          if (gItemIndex + 1 < this.groupList[gIndex].Qualifications.length) {
            sygItem = this.groupList[gIndex].Qualifications.find((x) => x.QualificationCode.indexOf('-Date') > -1)
          }
          beginValue = curItem.QualificationValue //.replaceAll('-', '')
          endValue = sygItem ? sygItem.QualificationValue || '' : ''
          type = 1
        } else if (curItem.QualificationCode.indexOf('-Date') > -1) {
          //当前是结束时间输入框，不能小于开始时间
          sygItem = this.groupList[gIndex].Qualifications.find((x) => x.QualificationCode.indexOf('-BeginDate') > -1)
          beginValue = sygItem ? sygItem.QualificationValue || '' : ''
          endValue = curItem.QualificationValue
          type = 2
        }
        // console.log(
        //   '33333333333 beginValue = ' + beginValue + ' endValue = ' + endValue,
        //   moment(beginValue).isBefore(endValue)
        // )
        let index = -1
        if (sygItem) {
          if (this.list && this.list.length > 0) {
            index = this.list[gIndex].Qualifications.findIndex((x) => x.QualificationCode == sygItem.QualificationCode)
          }
        }
        if (beginValue && endValue && !moment(beginValue).isBefore(endValue)) {
          if (type == 1) {
            callback('开始时间不能大于结束时间')
            this.$refs.auditForm.clearValidate(gIndex + sygItem.QualificationCode + index)
          } else if (type == 2) {
            callback('结束时间不能小于开始时间')
            this.$refs.auditForm.clearValidate(gIndex + sygItem.QualificationCode + index)
          }
          return
        } else {
          // console.log('type = ' + type)
          if (type == 1) {
            // console.log('NBA')
            if (sygItem) {
              this.$refs.auditForm.clearValidate(gIndex + sygItem.QualificationCode + index)
            }
          } else if (type == 2) {
            // console.log('CBA')
            if (sygItem) {
              this.$refs.auditForm.clearValidate(gIndex + sygItem.QualificationCode + index)
            }
          }

          callback()
        }
      } else {
        callback(msg)
      }
    },
    /**
     * 添加图片
     * @param {* 组对象} group
     * @param {* 组索引} gIndex
     */
    onAddPictureClick(url) {
      if (!url) {
        return
      }
      if (this.groupList[this.groupIndex].Qualifications) {
        this.groupList[this.groupIndex].Qualifications.forEach((item) => {
          if (item.QualificationType == 1 && item.Files) {
            item.Files.push({
              FileType: 1,
              FileUrl: url,
            })
            this.imgIndex = item.Files.length - 1
          }
        })
      }
    },
    /**
     * 移除图片
     */
    onDeletePictureClick() {
      let that = this
      this.$confirm({
        title: '删除提示',
        content: '确定要删除当前展示的图片吗？',
        onOk() {
          that.deletePic()
        },
        onCancel() { },
      })
    },
    deletePic() {
      if (this.imgIndex < 0) {
        return
      }
      if (this.groupList[this.groupIndex].Qualifications) {
        for (let i = 0; i < this.groupList[this.groupIndex].Qualifications.length; i++) {
          if (
            this.groupList[this.groupIndex].Qualifications[i].QualificationType == 1 &&
            this.groupList[this.groupIndex].Qualifications[i].Files
          ) {
            this.groupList[this.groupIndex].Qualifications[i].Files.splice(this.imgIndex, 1)
            this.onChangeImageClick('prev', this.groupList[this.groupIndex].Qualifications[i].Files)
            break
          }
        }
      }
      // this.groupList[this.groupIndex].Qualifications[0].Files.splice(this.imgIndex, 1)
      // this.$forceUpdate()
    },
    // 拍照上传
    onUploadClick(gIndex, gItem, gItemIndex) {
      // let count = gItem.Count || 6
      // if (gItem.QualificationValue && gItem.QualificationValue.length >= count) {
      //   this.$message.warning('最多上传' + count + '张图片')
      //   return
      // }
      this.$refs.PhotographModal.show(gIndex, gItem, gItemIndex)
    },
    // 拍照上传的url
    getPhotoUrl(url, gIndex, gItem, gItemIndex) {
      console.log(url, gIndex, gItem, gItemIndex)
      if (!gItem.QualificationValue) {
        this.$set(gItem, 'QualificationValue', [])
      }
      gItem.Files.push({
        FileType: 1,
        FileUrl: url
      })
      this.$refs.auditForm.clearValidate(
        gIndex + this.groupList[gIndex].Qualifications[gItemIndex].QualificationCode + gItemIndex
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.red {
  ::v-deep .ant-form-item-label > label {
    color: red;
  }
}
.warning {
  color: red !important;
}
.group {
  background-color: white;
  padding: 15px 5px;
  border-radius: 4px;
}
.groups {
  background-color: rgba(242, 242, 242, 1);
}
.container{
  position: relative;
}
.pdf-box {
  width: 100%;
  height: 60vh;
  overflow-y: auto;
}
.preview-box:hover .pdf-operator-ctn {
  display: flex;
}
.pdf-operator {
  width: inherit;
  position: absolute;
  bottom: 0%;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pdf-operator-ctn {
  width: 20%;
  background: #ddd;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-right: 6%;
  border-radius: 10px;
  display: none;
}
.pdf-operator-ctn div {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.container {
  height: 60vh;
  position: relative;
  // overflow: hidden;
  .page-prev {
    position: absolute;
    top: 28vh;
    left: 5px;
    z-index: 999;
    border-radius: 100%;
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 30px;
    box-shadow: 0 0 10px #ccc;
    background: #ffffff;
    cursor: pointer;
  }
  .page-next {
    position: absolute;
    top: 28vh;
    right: 5px;
    z-index: 999;
    border-radius: 100%;
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 30px;
    box-shadow: 0 0 10px #ccc;
    background: #ffffff;
    cursor: pointer;
  }
  .container-box {
    position: relative;
    height: 58vh;
  }
  .container-page {
    text-align: center;
    span {
      display: inline-block;
      background: #dcdfe6;
      color: #dcdfe6;
      margin: 0 6px;
      height: 4px;
      width: 25px;
    }
    .page-active {
      background: #409eff;
      color: #409eff;
    }
  }
  .option-btn {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 99;
  }
}
</style>
