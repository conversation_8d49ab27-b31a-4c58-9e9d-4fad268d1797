<!--
 * @Author: LP
 * @Description: 资质项view组件
 * @Date: 2023/06/15
-->
<template>
  <a-form-model ref="groupForm" :model="model" layout="vertical">
    <a-row :gutter="gutter">
      <template v-for="(group, gIndex) in list">
        <a-col
          :key="gIndex"
          :span="
            (
              group.Qualifications.filter((x) => {
                return x.QualificationType && x.QualificationType != 1 && x.IsShow
              }) || []
            ).length >= 1
              ? 24
              : 12
          "
          :class="{
            height12:
              (
                group.Qualifications.filter((x) => {
                  return x.QualificationType && x.QualificationType != 1 && x.IsShow
                }) || []
              ).length == 0,
          }"
        >
          <!-- 选项 + '-类型'-->
          <a-col span="24" v-if="group.GroupType == 5">
            <a-form-model-item
              :label="group.GroupName || ''"
              :key="'group' + gIndex"
              :prop="'group' + gIndex"
              :rules="{
                required: group.IsMust,
                validator: (rule, value, callback) => {
                  if (list[gIndex].IsMust) {
                    if (!list[gIndex].RadioVal) {
                      callback('请选择')
                    } else {
                      callback()
                    }
                  } else {
                    callback()
                  }
                },
              }"
            >
              <a-radio-group
                v-model="group.RadioVal"
                :disabled="!edit"
                @change="
                  (e) => {
                    $emit('changeRadio', group.GroupCode)
                  }
                "
              >
                <a-radio v-for="(radio, rIndex) in group.RadioList" :value="radio.Value" :key="rIndex">{{
                  radio.Name
                }}</a-radio>
                <a-button v-if="group.IsAdd && edit" type="danger" @click="onDeleteGroupClick(gIndex)">删除</a-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
          <template v-if="group.Qualifications && group.Qualifications.length > 0">
            <!-- .filter(x => {
                return group.RadioVal && x.FilterList ? x.FilterList.includes(group.RadioVal) : true
              }) -->
            <template v-for="(groupItem, gItemIndex) in group.Qualifications">
              <template v-if="groupItem.IsShow">
                <!-- 图片 -->
                <template v-if="groupItem.QualificationType == 1">
                  <!-- img.FilterList.includes(group.RadioVal) && img.QualificationType == 1 -->
                  <a-col
                    :key="gItemIndex"
                    :span="
                      group.GroupType == 5
                        ? group.Qualifications.filter((img) => {
                            return img.IsShow && img.QualificationType == 1
                          }).length > 2
                          ? 8
                          : 12
                        : 24
                    "
                  >
                    <a-form-model-item
                      :key="gIndex + groupItem.QualificationCode + gItemIndex"
                      :prop="gIndex + groupItem.QualificationCode + gItemIndex"
                      :rules="{
                        required: groupItem.IsMust,
                        validator: (rule, value, callback) => {
                          if (
                            list[gIndex].Qualifications[gItemIndex].IsMust &&
                            (!list[gIndex].Qualifications[gItemIndex].QualificationValue ||
                              list[gIndex].Qualifications[gItemIndex].QualificationValue.length == 0)
                          ) {
                            callback('请上传图片!')
                          } else {
                            callback()
                          }
                        },
                        // trigger: 'blur'
                      }"
                    >
                      <template slot="label">
                        <span>{{ groupItem.QualificationName ? groupItem.QualificationName + '：' : '' }}</span>
                        <YYLButton
                          type="primary"
                          menuId="a5a1bce5-7a56-4d58-bf0d-a949d74e439a"
                          style="margin-right: 8px"
                          text="拍照上传"
                          @click="onUploadClick(gIndex, groupItem, gItemIndex)"
                        />
                        <a-button
                          v-if="
                            group.IsAdd &&
                            edit &&
                            (gItemIndex == 0 ||
                              (groupItem.QualificationName && groupItem.QualificationName.indexOf('图片') >= 0))
                          "
                          type="danger"
                          @click="onDeleteGroupClick(gIndex)"
                          >删除</a-button
                        >
                      </template>
                      <MultiUpload
                        :ref="'multiUpload' + gIndex + groupItem.QualificationCode + gItemIndex"
                        :preModalWidth="750"
                        :preImgHeight="550"
                        :key="'multiUpload' + gIndex + groupItem.QualificationCode + gItemIndex"
                        :max="groupItem.Count || 6"
                        :images="groupItem.QualificationValue || []"
                        :bName="BName"
                        :dir="Dir"
                        :add="edit"
                        :fileType="4"
                        :readOnly="!edit"
                        @change="
                          (images) => {
                            onMultiUploadImageChange(images, gIndex, gItemIndex)
                          }
                        "
                      />
                    </a-form-model-item>
                  </a-col>
                </template>
                <!-- 文本 -->
                <a-col :span="8" v-else-if="groupItem.QualificationType == 4" :key="gItemIndex">
                  <a-form-model-item
                    :label="groupItem.QualificationName ? groupItem.QualificationName + '：' : ''"
                    :key="gIndex + groupItem.QualificationCode + gItemIndex"
                    :prop="gIndex + groupItem.QualificationCode + gItemIndex"
                    :rules="{
                      required: groupItem.IsMust,
                      validator: (rule, value, callback) => {
                        checkTextInputValue(rule, gIndex, gItemIndex, callback)
                      },
                      // trigger: 'blur'
                    }"
                  >
                    <a-input
                      :key="gIndex + groupItem.QualificationCode + gItemIndex"
                      placeholder="请输入"
                      v-model="groupItem.QualificationValue"
                      :maxLength="100"
                      :disabled="!edit"
                      @change="$forceUpdate()"
                      @blur="onInputInputBlur(groupItem, gIndex + groupItem.QualificationCode + gItemIndex)"
                      @focus="onInputInputFocus(groupItem)"
                    />
                    <span
                      slot="help"
                      class="cred"
                      v-if="tipInfo && groupItem.QualificationCode == 'BusinessLincense-Number'"
                      >{{ tipInfo.text }}，<a @click="onTipBtnClick">{{ tipInfo.btn }}</a></span
                    >
                  </a-form-model-item>
                </a-col>
                <!-- 日期 -->
                <a-col :span="8" v-else-if="groupItem.QualificationType == 3" :key="gItemIndex">
                  <a-form-model-item
                    :label="groupItem.QualificationName ? groupItem.QualificationName + '：' : ''"
                    :key="gIndex + groupItem.QualificationCode + gItemIndex"
                    :prop="gIndex + groupItem.QualificationCode + gItemIndex"
                    :rules="{
                      required: groupItem.IsMust,
                      validator: (rule, value, callback) => {
                        if (group.GroupName.indexOf('委托书') > -1) {
                          checkIntTime(rule, list[gIndex].Qualifications[gItemIndex].QualificationValue, (msg) => {
                            checkStartAndEndTime(gIndex, gItemIndex, msg, callback)
                          })
                        } else {
                          checkIntTime(rule, list[gIndex].Qualifications[gItemIndex].QualificationValue, callback)
                        }
                      },
                      // trigger: 'blur'
                    }"
                  >
                    <a-input
                      :key="gIndex + groupItem.QualificationCode + gItemIndex"
                      placeholder="请输入"
                      v-model="groupItem.QualificationValue"
                      :maxLength="10"
                      :disabled="!edit"
                      @change="(e) => onDateInputChange(e, gIndex, gItemIndex)"
                    >
                      <template #suffix v-if="groupItem.QualificationCode.indexOf('-BeginDate') == -1">
                        <a-checkbox
                          :key="gIndex + groupItem.QualificationCode + gItemIndex"
                          v-model="groupItem.Checked"
                          @change="(e) => onValidDateChange(e, gIndex, gItemIndex, groupItem)"
                          class="qualification-check"
                          v-if="edit"
                          >长期</a-checkbox
                        >
                      </template>
                    </a-input>
                  </a-form-model-item>
                </a-col>
                <!-- <a-col :span="8" v-else> 未处理类型{{ groupItem.QualificationType }} </a-col> -->
              </template>
            </template>
          </template>
        </a-col>
      </template>
    </a-row>
    <!-- 摄像头弹窗 -->
    <PhotographModal ref="PhotographModal" @getPhotoUrl="getPhotoUrl" />
  </a-form-model>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')

export default {
  name: 'QualificationsList',
  mixins: [ListMixin, EditMixin],
  components: {
    JEllipsis,
  },
  props: {
    /**
     * 商品
     */
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    /**
     * edit 是否能编辑资质
     */
    edit: {
      type: Boolean,
      default: true,
    },
    /**
     * 来源类型 1 供应商  2 客户
     */
    fromType: {
      type: Number,
      default: 1,
    },
    /**
     * 业务id
     * 供应商就传供应商id
     * 客户就传客户id
     */
    businessId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      description: '资质管理',
      longTimeDate: '2099-12-31',
      model: {},
      BName: window._CONFIG.AliOssConfig['BaseBucketName'],
      Dir: window._CONFIG.AliOssConfig['CommonDir'],
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      confirmLoading: false,
      IDRe18: /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      IDre15: /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/,
      tipInfo: null, //供应商名称查询结果提醒// type  = 1 存在 去查看  2 已建立客户档案 可导入
    }
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    moment,

    /**
     * 日期输入框 chagne事件
     * @param {*} e
     * @param {*} gIndex
     * @param {*} gItemIndex
     */
    onDateInputChange(e, gIndex, gItemIndex) {
      let date = this.getShowDate(e.target.value)
      this.list[gIndex].Qualifications[gItemIndex].QualificationValue = date
      this.list[gIndex].Qualifications[gItemIndex].Checked = date == this.longTimeDate
      this.$forceUpdate()
    },
    /**
     * @description:长期checked
     */
    onValidDateChange(e, gIndex, gItemIndex, groupItem) {
      let checked = e.target.checked
      this.list[gIndex].Qualifications[gItemIndex].Checked = checked
      if (checked) {
        this.list[gIndex].Qualifications[gItemIndex].QualificationValue = this.longTimeDate
      } else {
        this.list[gIndex].Qualifications[gItemIndex].QualificationValue = ''
      }
      this.$refs.groupForm.clearValidate(gIndex + groupItem.QualificationCode + gItemIndex)
      this.$forceUpdate()
    },
    /**
     * 多图上传组件change时间
     */
    onMultiUploadImageChange(images, gIndex, gItemIndex) {
      this.list[gIndex].Qualifications[gItemIndex].QualificationValue = images
      this.$refs.groupForm.clearValidate(
        gIndex + this.list[gIndex].Qualifications[gItemIndex].QualificationCode + gItemIndex
      )
    },
    // 拍照上传
    onUploadClick(gIndex, gItem, gItemIndex) {
      let count = gItem.Count || 6
      if (gItem.QualificationValue && gItem.QualificationValue.length >= count) {
        this.$message.warning('最多上传' + count + '张图片')
        return
      }
      this.$refs.PhotographModal.show(gIndex, gItem, gItemIndex)
    },
    // 拍照上传的url
    getPhotoUrl(url, gIndex, gItem, gItemIndex) {
      // console.log(url, gIndex, gItem, gItemIndex)
      if (!gItem.QualificationValue) {
        this.$set(gItem, 'QualificationValue', [])
      }
      gItem.QualificationValue.push(url)
      this.$refs.groupForm.clearValidate(
        gIndex + this.list[gIndex].Qualifications[gItemIndex].QualificationCode + gItemIndex
      )
    },
    /**
     * 删除group
     */
    onDeleteGroupClick(gIndex) {
      this.list[gIndex].IsAdd = false
      //刷新界面
      this.$emit('refresh')
    },
    /**
     * 验证表单输入的方法
     */
    checkFrom() {
      let bool = false
      this.$refs.groupForm.validate((valid) => {
        if (valid) {
          bool = true
        } else {
          bool = false
        }
      })
      // console.log('checkForm', bool)
      // if (bool) {
      //   let typeNum = 1 //this.fromType == 1 ? 1 : 1
      //   console.log('tipInfo typeNum = ' + typeNum, this.tipInfo)
      //   if (this.tipInfo && this.tipInfo.type != typeNum) {
      //     this.$message.error(this.tipInfo.text)
      //     return false
      //   }
      // }
      return bool
    },
    /**
     * 营业执照输入框获取焦点事件
     */
    onInputInputFocus(item) {
      if (!item || item.QualificationCode != 'BusinessLincense-Number') {
        return
      }
      this.tipInfo = null
    },
    /**
     * 供应商名称输入框失去焦点事件
     */
    onInputInputBlur(item, prop) {
      if (!item || item.QualificationCode != 'BusinessLincense-Number') {
        return
      }
      let that = this
      this.$refs.groupForm.validateField([prop], (errMsg) => {
        if (!errMsg) {
          this.checkZZNumber(item.QualificationValue)
        } else {
          this.tipInfo = null
        }
      })
    },
    checkZZNumber(val) {
      let existsUrl = ''
      let existsHeephead = ''
      switch (this.fromType) {
        case 2:
          existsHeephead = 'P36002'
          existsUrl = '/{v}/Customer/SameIdByBusinessNumber'
          break
        default:
          existsHeephead = 'P36003'
          existsUrl = '/{v}/Supplier/GetSameIdByBusinessNumber'
          break
      }
      let that = this
      getAction(existsUrl, { id: this.businessId, businessNumber: val }, existsHeephead).then((res) => {
        if (res.IsSuccess) {
          if (res.Data) {
            that.tipInfo = {
              type: 1,
              text: '该营业执照证号已存在', //，无法新增',
              btn: '去查看',
              id: res.Data,
            }
          } else {
            //只有供应商-才有导入功能
            if (that.fromType == 1) {
              getAction('/{v}/Customer/SameIdByBusinessNumber', { businessNumber: val, id: '' }, 'P36002').then(
                (result) => {
                  if (result.IsSuccess && result.Data) {
                    that.tipInfo = {
                      type: 2,
                      text: '该营业执照已建立客户档案，可直接导入信息',
                      btn: '导入',
                      id: result.Data,
                    }
                  }
                }
              )
            }
          }
        }
      })
    },
    onTipBtnClick() {
      if (!this.tipInfo) {
        return
      }
      switch (this.tipInfo.type) {
        case 1: //去查看
          if (this.fromType == 1) {
            this.$router.push({
              path: 'SupplierProfileDetail',
              query: { id: this.tipInfo.id, opType: 4 },
            })
          } else if (this.fromType == 2) {
            this.$router.push({
              path: '/pages/fileManagement/commercialCustomerFile/commercialCustomerFileDetail',
              query: { id: this.tipInfo.id, acType: 4 },
            })
          }

          break
        case 2:
          this.$emit('import', this.tipInfo.id)
          let that = this
          setTimeout(() => {
            that.tipInfo = null
          }, 200)
          break
      }
    },
    checkTextInputValue(rule, gIndex, gItemIndex, callback) {
      if (rule.required) {
        if (!this.list[gIndex].Qualifications[gItemIndex].QualificationValue) {
          callback('请输入!')
        } else {
          if (this.list[gIndex].Qualifications[gItemIndex].QualificationCode.indexOf('IdCardNo') != -1) {
            let val = this.list[gIndex].Qualifications[gItemIndex].QualificationValue
            if (!this.IDRe18.test(val) && !this.IDre15.test(val)) {
              callback(this.list[gIndex].Qualifications[gItemIndex].QualificationName + '有误！')
            } else {
              callback()
            }
          } else if (
            this.list[gIndex].Qualifications[gItemIndex].QualificationName.indexOf('姓名') > -1 ||
            this.list[gIndex].Qualifications[gItemIndex].QualificationName.indexOf('名称') > -1
          ) {
            this.validStrMaxLength(rule, this.list[gIndex].Qualifications[gItemIndex].QualificationValue, callback, 50)
          } else if (this.list[gIndex].Qualifications[gItemIndex].QualificationName.indexOf('证号') > -1) {
            this.validStrMaxLength(rule, this.list[gIndex].Qualifications[gItemIndex].QualificationValue, callback, 100)
          } else {
            callback()
          }
        }
      } else {
        callback()
      }
    },
    /**
     * 委托书有开始时间结束时间的验证输入
     * @param {*} gIndex
     * @param {*} gItemIndex
     * @param {*} msg
     * @param {*} callback
     */
    checkStartAndEndTime(gIndex, gItemIndex, msg, callback) {
      // console.log(
      //   '1111111111 GroupName = ' + this.list[gIndex].GroupName,
      //   this.list[gIndex].GroupName.indexOf('委托书') > -1
      // )
      if (!msg) {
        let curItem = this.list[gIndex].Qualifications[gItemIndex]
        let sygItem = null
        let beginValue = null
        let endValue = null
        // console.log('curItem = ', curItem)
        let type = -1
        //如果当前是开始时间输入框,则判断不能大于结束时间
        if (curItem.QualificationCode.indexOf('-BeginDate') > -1) {
          if (gItemIndex + 1 < this.list[gIndex].Qualifications.length) {
            sygItem = this.list[gIndex].Qualifications.find((x) => x.QualificationCode.indexOf('-Date') > -1)
          }
          beginValue = curItem.QualificationValue //.replaceAll('-', '')
          endValue = sygItem ? sygItem.QualificationValue || '' : ''
          type = 1
        } else if (curItem.QualificationCode.indexOf('-Date') > -1) {
          //当前是结束时间输入框，不能小于开始时间
          sygItem = this.list[gIndex].Qualifications.find((x) => x.QualificationCode.indexOf('-BeginDate') > -1)
          beginValue = sygItem ? sygItem.QualificationValue || '' : ''
          endValue = curItem.QualificationValue
          type = 2
        }
        // console.log(
        //   '33333333333 beginValue = ' + beginValue + ' endValue = ' + endValue,
        //   moment(beginValue).isBefore(endValue)
        // )
        let index = -1
        if (sygItem) {
          index = this.list[gIndex].Qualifications.findIndex((x) => x.QualificationCode == sygItem.QualificationCode)
        }
        if (beginValue && endValue && !moment(beginValue).isBefore(endValue)) {
          if (type == 1) {
            callback('开始时间不能大于结束时间')
            this.$refs.groupForm.clearValidate(gIndex + sygItem.QualificationCode + index)
          } else if (type == 2) {
            callback('结束时间不能小于开始时间')
            this.$refs.groupForm.clearValidate(gIndex + sygItem.QualificationCode + index)
          }
          return
        } else {
          console.log('type = ' + type)
          if (type == 1) {
            // console.log('NBA')
            if (sygItem) {
              this.$refs.groupForm.clearValidate(gIndex + sygItem.QualificationCode + index)
            }
          } else if (type == 2) {
            // console.log('CBA')
            if (sygItem) {
              this.$refs.groupForm.clearValidate(gIndex + sygItem.QualificationCode + index)
            }
          }

          callback()
        }
      } else {
        callback(msg)
      }
    },
  },
  watch: {},
}
</script>
<style lang="less" scoped>
/deep/.qualification-check {
  margin-left: 16px;
  span:last-child {
    padding-right: 0;
  }
}
/deep/.qualification-date {
  width: calc(100% - 72px);
}
.add-div {
  border: 1px dashed blue;
}
.height12 {
  min-height: 215px;
}
/**
* 解决必填项未填 输入提示 会把后面的输入框抵走的问题
**/
.ant-col-8 {
  height: 100px !important;
}
</style>

<!-- /**
     * 时间change
     */
    onEndTimeChange(dates, dateStrings, gIndex, gItemIndex) {
      if (dates) {
        // 去掉开始日期的选择，选择结束日期后默认把开始日期置为前一天
        // let aDayAgo = moment(new Date(dates).getTime() - 1 * 24 * 60 * 60 * 1000)
        // this.list[gIndex].Qualifications[gItemIndex].BeginValidityDate = aDayAgo
        this.list[gIndex].Qualifications[gItemIndex].QualificationValue = moment(dates)
      } else {
        this.list[gIndex].Qualifications[gItemIndex].BeginValidityDate = null
        // this.list[gIndex].Qualifications[gItemIndex].QualificationValue = null
      }

      if (
        this.list[gIndex].Qualifications[gItemIndex].QualificationValue &&
        moment(this.list[gIndex].Qualifications[gItemIndex].QualificationValue).format(this.dateFormat) ==
          this.longTimeDate
      ) {
        this.list[gIndex].Qualifications[gItemIndex].Checked = true
      } else {
        this.list[gIndex].Qualifications[gItemIndex].Checked = false
      }
    }, -->
