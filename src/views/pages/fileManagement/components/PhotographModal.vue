<template>
  <a-modal :width="1280" v-model="visible" @cancel="handleCancel">
    <div slot="title">
      <span>{{title}}</span>
    </div>
    <div v-if="isFirst">
      <p>第一次使用操作说明</p>
      <p>1、在浏览器的地址栏中输入： <strong>{{linkUrl}}</strong><a style="margin-left: 5px;" @click="copy(1)">复制</a></p>
      <p>2、将该 <strong>Disabled</strong> 切换成 <strong>enable</strong> 状态，若是中文切换为 <strong>已启用</strong></p>
      <p>3、在灰色框内输入： <strong>{{webUrl}}</strong><a style="margin-left: 5px;" @click="copy(2)">复制</a></p>
      <p>4、点击右下角的<strong>Relaunch</strong>按钮，若是中文为<strong>重启</strong>按钮，再次打开浏览器后方可进行拍照上传</p>
      <img style="width:100%;height:450px;" src="@/assets/image/photo_description.png" alt="">
    </div>
    <div v-if="!isFirst" class="publish" style="width:100%;">
      <video :style="{width:'100%',height:'auto'}" ref="video"></video>
      <canvas style="display:none;" id="canvasCamera"></canvas>
      <!-- <button @click="OpenCamera">打开摄像头</button>
      <button @click="CloseCamera">关闭摄像头</button>
      <button @click="photograph">拍照</button> -->
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <span style="position: relative;right: 10px;top: 1px;">
        当前分辨率(分辨率越高上传速度会越慢)：
        <a-select style="width: 120px" v-model="chooseCanvasPx" @change="changeCanvasPx">
          <a-select-option v-for="(item,index) in canvasPxArray" :key="index" :value="item">
            {{item}}
          </a-select-option>
        </a-select>
      </span>
      <a-button :disabled="confirmLoading" 
      :style="{ marginRight: '8px' }" type="primary" @click="photograph">拍照</a-button>
      <a-button @click="handleCancel"> 取消 </a-button>
    </a-row>
  </a-modal>
</template>

<script>
// 高拍仪拍照
import { ListMixin } from '@/mixins/ListMixin.js'
export default {
  name: 'PhotographModal',
  mixins: [ListMixin],
  data() {
    return {
      title: '拍照上传',
      confirmLoading: false,
      visible: false,
      mediaStreamTrack: {},
      video_stream: '', // 视频stream
      canvas: null,
      context: null,
      gIndex: null,
      isFirst: false,//是否是第一次使用拍照
      gItem: {},
      canvasPxArray: [
        '6000x4500', '4608x3456', '2592x1944', '1280x720',
      ],
      chooseCanvasPx: '2592x1944',
      videoWidth: { min: 1280, ideal: 4608, max: 6000 },
      videoheight: { min: 720, ideal: 3456, max: 4500 },
      canvasWidth: 2592,
      canvasHeight: 1944,
      gItemIndex: null,
      env: process.env.VUE_APP_PREVIEW.trim(),
      linkUrl: 'chrome://flags/#unsafely-treat-insecure-origin-as-secure',
      webUrl: 'https://scm.haoyaoyouxuan.com:36100',
      isPhotographing: false, // 添加拍照中的状态标识
    }
  },
  created() {

  },
  methods: {
    show(gIndex, gItem, gItemIndex) {
      this.gIndex = gIndex
      this.gItem = gItem
      this.gItemIndex = gItemIndex
      this.visible = true
      this.confirmLoading = false  // 重置按钮状态
      this.isPhotographing = false // 重置拍照状态
      this.getToast()
      this.$nextTick(() => {
        this.OpenCamera()
      })
    },
    close() {
      if (!this.isFirst) {
        this.CloseCamera()
      }
      this.isPhotographing = false // 重置拍照状态
      this.visible = false
    },
    getToast() {
      switch (this.env) {
        case 'development':
        case 'test':
          this.webUrl = 'https://testscm.hysyzs.com:46050'
          break
        case 'production':
          this.webUrl = 'https://scm.haoyaoyouxuan.com:36100'
          break
        case 'stage':
          this.webUrl = 'https://scm.haoyaoyouxuan.com:36100'
          break
        default:
          this.webUrl = 'https://scm.haoyaoyouxuan.com:36100'
      }
    },
    // 复制
    copy(type) {
      var input = document.createElement('input') // 创建input标签
      input.value = type == 1 ? this.linkUrl : this.webUrl; // 将input的值设置为需要复制的内容
      document.body.appendChild(input) // 添加input标签
      input.select()  // 选中input标签
      document.execCommand('copy') // 执行复制
      this.$message.success('复制成功，去粘贴') // 成功提示信息
      document.body.removeChild(input) // 移除input标签
    },
    changeCanvasPx() {
      this.setCanvasPx()
    },
    setCanvasPx() {
      let width = this.chooseCanvasPx.indexOf('x') > -1 ? this.chooseCanvasPx.split('x')[0] : 2592
      let height = this.chooseCanvasPx.indexOf('x') > -1 ? this.chooseCanvasPx.split('x')[1] : 1944
      this.canvasWidth = width
      this.canvasHeight = height
    },
    // 调用打开摄像头功能
    getCamera() {
      // 旧版本浏览器可能根本不支持mediaDevices，我们首先设置一个空对象
      if (navigator.mediaDevices === undefined || navigator.isFirst == true) {
        // this.$message.warning('您的浏览器不支持拍照功能，请设置浏览器参数')
        navigator.mediaDevices = {};
        navigator.isFirst = true;
        this.isFirst = true
        return
      } else {
        this.isFirst = false
      }
      // 获取 canvas 画布
      this.canvas = document.getElementById('canvasCamera');
      if (!this.canvas) {
        this.isFirst = true
        return
      }
      this.context = this.canvas.getContext('2d');
      // 正常支持版本
      navigator.mediaDevices
        .getUserMedia({
          // video: true,
          video: {
            // width: 1280, height: 720,//笔记本常用分辨率
            width: this.videoWidth,
            height: this.videoheight,
          }
        })
        .then((stream) => {
          // 摄像头开启成功
          this.mediaStreamTrack = typeof stream.stop === 'function' ? stream : stream.getTracks()[0];
          this.video_stream = stream;
          this.$refs.video.srcObject = stream;
          // 获取视频轨道
          const videoTrack = stream.getVideoTracks()[0];
          // 获取摄像头的设置
          const settings = videoTrack.getSettings();
          // 若设备是笔记本常用分辨率
          if (settings && settings.width == this.videoWidth.min && settings.height == this.videoheight.min) {
            console.log('设备是笔记本常用分辨率')
            this.chooseCanvasPx = '1280x720'
            this.setCanvasPx()
          }
          // console.log('视频设置信息', settings)
          this.$refs.video.play();
        })
        .catch(err => {
          console.log(err);
          this.$message.error('摄像头开启失败,请检查摄像头分辨率是否满足拍摄要求' + err)
          // if(err){
          //   this.getCamera('again')
          // }
        });
    },
    // 拍照 绘制图片
    photograph() {
      console.log('拍照');
      if (this.isFirst) {
        this.$message.warning('请先按照操作说明设置浏览器参数后再操作')
        return
      }
      
      // 添加防抖判断
      if (this.isPhotographing) {
        this.$message.warning('正在处理中，请稍候...')
        return
      }
      
      this.isPhotographing = true // 设置拍照中状态
      this.confirmLoading = true  // 禁用拍照按钮
      
      const canvas = document.getElementById('canvasCamera');
      canvas.width = this.canvasWidth
      canvas.height = this.canvasHeight
      
      try {
        // 点击canvas画图
        this.context.drawImage(
          this.$refs.video,
          0,
          0,
          canvas.width, canvas.height,
        );
        
        // 获取图片base64链接
        let bName = window._CONFIG.AliOssConfig['BaseBucketName']
        let dir = window._CONFIG.AliOssConfig['CommonDir']
        let that = this
        
        //转成文件流上传到oss
        canvas.toBlob(function (blob) {
          //上传blob到oss
          that.uploadBlob(
            blob,
            realurl => {
              that.$emit('getPhotoUrl', realurl, that.gIndex, that.gItem, that.gItemIndex);
              that.isPhotographing = false // 重置拍照状态
              that.confirmLoading = false  // 重置按钮状态
              that.close()
            },
            bName,
            dir
          ).catch(error => {
            console.error('上传失败:', error);
            that.$message.error('上传失败，请重试');
            that.isPhotographing = false; // 确保在失败时也重置状态
            that.confirmLoading = false;  // 重置按钮状态
          })
        })
      } catch (error) {
        console.error('拍照过程出错:', error);
        this.$message.error('拍照失败，请重试');
        this.isPhotographing = false; // 确保在失败时也重置状态
        this.confirmLoading = false;  // 重置按钮状态
      }
    },
    // 打开摄像头
    OpenCamera() {
      console.log('打开摄像头');
      this.getCamera();
    },
    // 关闭摄像头
    CloseCamera() {
      console.log('关闭摄像头');
      if (this.$refs.video) {
        this.$refs.video.srcObject && this.$refs.video.srcObject.getTracks()[0].stop();
      }
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>

<style lang="less" scoped>
/deep/.ant-modal-body {
  padding: 10px;
}
</style>