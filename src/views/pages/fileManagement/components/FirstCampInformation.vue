<!--
 * @Author: LP
 * @Description: 首营信息操作（新增、编辑）组件 （供应商、商销客户、商品）
 * @Date: 2023/06/12
-->
<template>
  <a-spin :spinning="confirmLoading">
    <!-- 编辑状态 -->
    <a-row :gutter="gutter" v-if="isEdit">
      <a-col :span="24" class="mb20">
        <span style="font-size: 16px; font-weight: 600">首营信息（必填证照）</span>
      </a-col>
      <a-col :span="24" v-if="showZlxs">
        <a-form-model ref="zlxsForm" :model="model" :label-col="labelCol" :wrapper-col="wrapperCol" class="zlxsForm">
          <a-form-model-item label="资料形式" :required="true">
            <a-radio-group
              name="radioGroup"
              v-model="model.zlxs"
              @change="
                (e) => {
                  $emit('zlxsChange', e)
                }
              "
            >
              <a-radio :value="1"> 正常上传 </a-radio>
              <a-radio :value="2"> 七彩转存 </a-radio>
            </a-radio-group>
            <a-button type="primary" :loading="zcLoading" v-if="model.zlxs == 2" @click="$emit('getZCData')"
              >点击获取转存资料</a-button
            >
          </a-form-model-item>
        </a-form-model>
      </a-col>
      <!-- v-if="model.zlxs == 1" -->
      <a-col :span="24">
        <QualificationsList
          ref="qualificationsListMust"
          key="qualificationsListMust"
          :edit="isEdit"
          :list="
            allList.filter((x) => {
              return x.IsMust && !x.IsAdd
            })
          "
          :businessId="businessId"
          :fromType="fromType"
          @import="
            (id) => {
              $emit('import', id)
            }
          "
          @refresh="refreshMustList"
          @changeRadio="(gCode) => changeRadioVal(gCode, true)"
          v-if="refreshMust"
        />
      </a-col>
      <a-col :span="24" class="mt10 mb20">
        <span style="font-size: 16px; font-weight: 600">首营证照（选填证照）</span>
        <a-button type="primary" @click="onAddGroupClick" v-if="checkBtnPermissions(addGroupBtnId) && isEdit"
          >新增证照</a-button
        >
      </a-col>
      <a-col :span="24">
        <QualificationsList
          ref="qualificationsList"
          key="qualificationsList"
          :edit="isEdit"
          :list="
            allList.filter((x) => {
              return x.IsAdd && !x.IsMust
            })
          "
          @refresh="refreshNotMustList"
          @changeRadio="(gCode) => changeRadioVal(gCode, true)"
          v-if="refresh"
        />
      </a-col>
      <a-col :span="12" v-if="showRemark">
        <a-form-model ref="syform" :model="model" layout="vertical">
          <a-form-model-item
            label="资料缺项"
            :rules="{
              required: false,
              validator: (rule, value, callback) => {
                if (value) {
                  this.validStrMaxLength(rule, value, callback, 100)
                }
              },
            }"
          >
            <a-input
              placeholder="请输入"
              v-model="model.Remark"
              style="width: 100%"
              :maxLength="100"
              :disabled="!isEdit"
            />
          </a-form-model-item>
        </a-form-model>
      </a-col>
    </a-row>
    <!-- 详情状态 -->
    <a-row :gutter="gutter" v-else>
      <template v-for="(bool, j) in [1, 2]">
        <a-col :span="24" style="margin-bottom: 20px; margin-top: 10px" :key="'colleft' + j">
          <span style="font-size: 16px; font-weight: 600">{{
            j == 0 ? '首营信息（必填证照）' : '首营证照（选填证照）'
          }}</span>
        </a-col>
        <a-col
          :span="12"
          v-for="(group, gIndex) in allList.filter((x) => {
            return j == 0 ? x.IsMust == true && !x.IsAdd : x.IsAdd == true && !x.IsMust
          })"
          :key="'colright' + j + '' + gIndex"
        >
          <a-card :title="group.GroupName">
            <a-row :gutter="gutter" class="card-item">
              <template v-for="(item, index) in group.Qualifications">
                <a-col :span="24" v-if="item.QualificationType == 5" :key="index">
                  <span>类型: {{ item.QualificationValue }}</span>
                </a-col>

                <template v-else-if="item.QualificationType == 1">
                  <a-col
                    :key="index"
                    v-if="item.QualificationValue && item.QualificationValue.length > 0"
                    :span="
                      group.Qualifications.filter(
                        (imgItem) =>
                          imgItem.QualificationType == 1 &&
                          imgItem.QualificationValue &&
                          imgItem.QualificationValue.length > 0
                      ).length >= 2
                        ? 8
                        : 24
                    "
                  >
                    <span>
                      {{ item.QualificationName + '：' }}
                    </span>
                    <MultiUpload
                      :ref="'multiUpload' + gIndex + '' + index"
                      :key="'multiUpload' + gIndex + '' + index"
                      :images="item.QualificationValue || []"
                      :add="false"
                      :readOnly="true"
                    />
                  </a-col>
                  <a-col v-else :key="index" :span="24">
                    <span>{{ item.QualificationName + '：无' }}</span>
                  </a-col>
                </template>
                <template v-else>
                  <a-col :span="12" v-if="item.QualificationValue" :key="index">
                    <span>{{ item.QualificationName + '：' }}{{ item.QualificationValue }}</span>
                  </a-col>
                </template>
              </template>
            </a-row>
          </a-card>
        </a-col>
      </template>
      <a-col :span="24" style="margin-top: 20px" v-if="showRemark"> 资料缺项：{{ remark || '--' }} </a-col>
    </a-row>
    <!-- 新增证照 弹窗 -->
    <ChooseLicenseModal ref="chooseLicenseModal" :allList="allList" @ok="onChooseCallback" />
  </a-spin>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'FirstCampInformation',
  mixins: [EditMixin],
  components: {},
  props: {
    /**
     * 可否编辑
     */
    isEdit: {
      type: Boolean,
      default: true,
    },
    /**
     * 所有证照数据（包含必填非必填）
     */
    allList: {
      type: Array,
      default: () => {
        return []
      },
    },
    /**
     * 新增证照按钮id
     */
    addGroupBtnId: {
      type: String,
      required: true,
      default: () => {
        return ''
      },
    },
    /**
     * 是否显示备注
     */
    showRemark: {
      type: Boolean,
      default: true,
    },
    /**
     * 备注
     */
    remark: {
      type: String,
      default: '',
    },
    /**
     * 是否显示 资料形势 默认不显示 只有商品资质需要用到这个参数
     */
    showZlxs: {
      type: Boolean,
      default: false,
    },
    /**
     * 资料形势的  选择值 只要商品资质需要使用
     */
    zlxsVal: {
      type: Number,
      default: 0,
    },
    /**
     * 来源类型 1 供应商  2 客户
     */
    fromType: {
      type: Number,
      default: 1,
    },
    /**
     * 业务id
     * 供应商就传供应商id
     * 客户就传客户id
     */
    businessId: {
      type: String,
      default: '',
    },
    zcLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      confirmLoading: false,
      refresh: true,
      refreshMust: true,
      model: {
        zlxs: this.zlxsVal || 1, //资料形势 1 正常上传 2 七彩上传
        Remark: '',
      },
      labelCol: { md: 2 },
      wrapperCol: { md: 22 },
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.model.Remark = this.remark
  },
  methods: {
    /**
     * 新增证照点击事件
     */
    onAddGroupClick() {
      this.$refs.chooseLicenseModal.show()
    },
    /**
     * 选择证照回调函数
     */
    onChooseCallback() {
      this.refreshNotMustList()
    },
    /**
     * 刷新非必填数据组
     */
    refreshNotMustList() {
      this.refresh = false
      this.$nextTick(() => {
        this.refresh = true
      })
    },
    /**
     * 刷新必填数据组
     */
    refreshMustList() {
      this.refreshMust = false
      this.$nextTick(() => {
        this.refreshMust = true
      })
    },
    changeRadioVal(gCode, IsMust) {
      this.clearGroupQualificationValue(gCode, IsMust)
    },
    /**
     * 清空某个组的项的值
     * @param {*} gIndex
     */
    clearGroupQualificationValue(gCode, IsMust) {
      if (gCode) {
        let gIndex = this.allList.findIndex((group) => group.GroupCode == gCode)
        if (gIndex > -1) {
          this.allList[gIndex].Qualifications.forEach((item) => {
            if (item.QualificationType != 5) {
              item.QualificationValue = ''
              if (item.FilterList) {
                item.IsShow = item.FilterList.includes(this.allList[gIndex].RadioVal)
              }
            }
          })
        }
        if (IsMust) {
          this.refreshMustList()
        } else {
          this.refreshNotMustList()
        }
      }
    },
    /**
     * 获取首营界面数据（构建界面的原始数据）
     * return {remark:'';list:[]}
     */
    getFirstCampData() {
      let data = null
      // let mustOk = this.showZlxs && this.model.zlxs == 2 ? true : this.$refs.qualificationsListMust.checkFrom()
      let mustOk = this.$refs.qualificationsListMust.checkFrom()
      let noMustOK = this.$refs.qualificationsList.checkFrom()
      if (mustOk && noMustOK) {
        // data = { list: JSON.parse(JSON.stringify(this.allList)), remark: this.model.Remark, zlxs: this.model.zlxs }
        let newList = this.allList.filter((item) => {
          return item.IsMust || item.IsAdd
        })
        data = { list: JSON.parse(JSON.stringify(newList)), remark: this.model.Remark, zlxs: this.model.zlxs }
      }
      return data
    },
    /**
     * 获取首营界面数据（提交结构的数据）
     * return {remark:'';list:[]}
     * @param {是否校验数据} checkData true 校验数据 false 不校验数据
     */
    getFirstCampSubData(checkData) {
      let data = null
      // let mustOk = this.showZlxs && this.model.zlxs == 2 ? true : this.$refs.qualificationsListMust.checkFrom()
      let mustOk = checkData == false ? true : this.$refs.qualificationsListMust.checkFrom()
      let noMustOK = checkData == false ? true : this.$refs.qualificationsList.checkFrom()
      console.log('checkData = ' + checkData + ' mustOK = ' + mustOk + ' noMustOk = ' + noMustOK)
      if (mustOk && noMustOK) {
        let subList = []
        this.allList.forEach((group) => {
          let subGroup = JSON.parse(JSON.stringify(group))
          if (group.IsMust || group.IsAdd) {
            subList.push(subGroup)
          }
        })

        subList.forEach((group) => {
          if (group.Qualifications) {
            let subItems = []
            group.Qualifications.forEach((item) => {
              if (group.GroupType == 5 && item.QualificationType == 5) {
                item.QualificationValue = group.RadioVal
              }
              if (item.QualificationValue) {
                let subItem = {
                  ...item,
                }
                //图片
                if (item.QualificationType == 1 && item.QualificationValue instanceof Array) {
                  subItem['Files'] = []
                  subItem.QualificationValue = ''
                  item.QualificationValue.forEach((img) => {
                    let imgFile = {
                      FileUrl: img,
                      FileType: 1,
                    }
                    subItem['Files'].push(imgFile)
                  })
                }
                subItems.push(subItem)
              } else {
                subItems.push(item)
              }
            })
            group.Qualifications = subItems
          }
        })

        data = { list: JSON.parse(JSON.stringify(subList)), remark: this.model.Remark, zlxs: this.model.zlxs }
      }
      return data
    },
  },
  watch: {},
}
</script>
<style lang="less" scoped>

.mb20 {
  margin-bottom: 20px;
}
.zlxsForm {
  /deep/.ant-col.ant-col-md-2.ant-form-item-label {
    text-align: left;
  }
}
.card-item {
  height: 250px;
  overflow-y: auto;
}
</style>
