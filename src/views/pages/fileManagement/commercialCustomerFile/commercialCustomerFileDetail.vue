<!--
 * @Author: 姚惠英
 * @Description: 客户详情
 * @Date: 2023/06/12
-->
<template>
  <a-card>
    <a-tabs v-model="activeTabKey">
      <a-tab-pane :key="1" tab="基础信息">
        <CusBasicInfoTemplateForm ref="CusBasicInfoTemplateForm" isShowInfo :info="info" v-if="info" :opType="acType" />
      </a-tab-pane>
      <a-tab-pane :key="2" tab="首营信息">
        <CusFirstCampInformation
          ref="iCusFirstCampInformation"
          :opType="acType"
          :info="info"
          :remark="remark"
          :hasList="qList"
        />
      </a-tab-pane>
      <a-tab-pane :key="3" tab="经营范围">
        <SupBusinessScope
          ref="cusBusinessScope"
          :isEdit="isEdit"
          v-if="activeTabKey == 3 && info"
          :model="info"
          :isCustomer="true"
          scopeKey="ManageClassifyTypeDto"
        />
      </a-tab-pane>
      <a-tab-pane :key="4" tab="开票信息">
        <CusBasicInfoTemplateForm
          ref="CusBasicInfoTemplateForm"
          isShowInvoice
          :info="info"
          v-if="info"
          :opType="acType"
        />
      </a-tab-pane>
      <a-tab-pane :key="5" tab="审核信息" v-if="info && info.AuditId">
        <CusAuditInformation ref="CusAuditInformation" :AuditId="info.AuditId" />
      </a-tab-pane>
    </a-tabs>
    <template v-if="!info && activeTabKey != 5">
      <a-empty />
    </template>
    <a-affix :offset-bottom="10" style="float: right; width: 100%; text-align: right">
      <YYLButton
        type="primary"
        v-if="isEdit && activeTabKey != 5"
        class="mr8"
        @click="savePage()"
        :loading="loading"
        menuId="28861639-b1bd-4ec3-b653-8cdfa191ce92"
        text="保存当前页面"
      ></YYLButton>
      <a-button @click="$router.go(-1)">返回</a-button>
    </a-affix>
  </a-card>
</template>
<script>
import { getAction, putAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'commercialCustomerFileDetail',
  mixins: [EditMixin],
  data() {
    return {
      activeTabKey: 1,
      model: {},
      remark: '',
      qList: [], //资质list
      id: '',
      info: undefined,
      loading: false,
      acType: 4, //1 新增（此界面不使用）  2 采购编辑（此界面不使用） 3质管编辑 4详情  (详情界面只会出现 3 和 4)
    }
  },
  computed: {
    isEdit() {
      return this.acType === 3
    },
  },
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      if (this.id) {
        this.getDetail()
      }

      this.acType = this.$route.query.acType ? Number(this.$route.query.acType) : 4

      this.setTitle('商销客户档案' + (this.acType == 3 ? '编辑' : '详情'))
    }
  },
  watch: {
    // activeTabKey(val) {
    //   if (val != 1 && val != 5 && this.model && this.acType == 3 && !this.model.IsFormalCustomer) {
    //     this.$message.warning('请先完善基础信息')
    //     this.activeTabKey = 1
    //   }
    // }
  },
  created() {},
  methods: {
    getDetail() {
      getAction(
        '/v1/Customer/DetailByQuality',
        {
          id: this.id,
        },
        'P36002'
      ).then((res) => {
        if (res.IsSuccess) {
          if (res.Data) {
            this.info = res.Data
            this.info.Id = this.id

            if (res.Data.CustomerQualificationVersion && res.Data.CustomerQualificationVersion.QualificationGroups) {
              this.qList = res.Data.CustomerQualificationVersion.QualificationGroups
            }

            this.remark = res.Data.CustomerQualificationVersion
              ? res.Data.CustomerQualificationVersion.Remarks || ''
              : ''
          }
        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
    savePage() {
      this.loading = true
      if (this.activeTabKey == 3) {
        this.$refs.cusBusinessScope.editSaveData(() => {
          this.getDetail()
          this.loading = false
        })
      } else if (this.activeTabKey == 2) {
        this.$refs.iCusFirstCampInformation.onSubmitUpdate(() => {
          this.getDetail()
          this.loading = false
        })
      } else {
        this.$refs.CusBasicInfoTemplateForm.onSubmit(false, this.acType, this.activeTabKey, () => {
          this.getDetail()
          this.loading = false
        })
      }
    },
  },
}
</script>

<style scoped>

</style>
