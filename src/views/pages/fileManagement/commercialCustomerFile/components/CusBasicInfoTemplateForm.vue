<!-- 商品档案新增 基本信息 -->
<template>
  <div class="form-model-style">
    <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical" v-if="[1, 2, 3].includes(opType)">
      <!-- 商品信息 -->
      <template v-if="isShowInfo">
        <a-row :gutter="gutter">
          <a-col :span="24">
            <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">企业信息</div>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="Name" label="客户名称：" prop="Name">
              <a-input
                placeholder="请输入"
                v-model="model.Name"
                :maxLength="80"
                @blur="handleConfirmBlur"
                @focus="handleConfirmFocus"
              />
              <span slot="help" class="cred" v-if="tipInfo"
                >{{ tipInfo.text }}，<a @click="goDetail()">{{ tipInfo.btn }}</a></span
              >
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="Type" label="企业类别：" prop="Type">
              <SingleChoiceView
                placeholder="请选择"
                :httpParams="{
                  groupPY: 'kehulb',
                }"
                :httpHead="'P36001'"
                :dataKey="{ name: 'ItemValue', value: 'Id' }"
                :Url="'/v1/Global/GetListDictItemForCustomer'"
                v-model="model.Type"
                @change="typeChange"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="SecondType" label="企业二级分类：" prop="SecondType">
              <SingleChoiceView
                style="width: 100%"
                placeholder="请选择"
                :httpParams="{
                  groupPY: 'kehufl',
                }"
                httpHead="P36001"
                :dataKey="{ name: 'ItemValue', value: 'Id' }"
                v-model="model.SecondType"
                :Url="'/v1/Global/GetListDictItemForCustomer'"
                disabled
                @dataLoaded="secondTypeLoad"
                @change="(val, txt, item) => (model.SecondTypeStr = txt)"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="LegalPerson" label="法人代表：" prop="LegalPerson">
              <a-input placeholder="请输入" v-model="model.LegalPerson" :maxLength="30" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="EnterpriseCharge" label="企业负责人：" prop="EnterpriseCharge">
              <a-input placeholder="请输入" v-model="model.EnterpriseCharge" :maxLength="20" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="QualityCharge" label="质量负责人：" prop="QualityCharge">
              <a-input placeholder="请输入" v-model="model.QualityCharge" :maxLength="20" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="RegAddress" label="注册地址：" prop="RegAddress">
              <a-input placeholder="请输入" v-model="model.RegAddress" :maxLength="100" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="ShippingAddress" label="收货地址：" prop="ShippingAddress">
              <a-input placeholder="请输入" v-model="model.ShippingAddress" :maxLength="240" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md" v-if="isShowArea">
            <a-form-model-item label="区域分类（省市区）" prop="RegAreaCode">
              <SelectAreaView
                style="width: 100%"
                ref="selectCityAreas"
                :selectCode="true"
                :defaultCodes="areaValue"
                @change="(val, node) => onAreasChange(val, node, model, 'RegArea')"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="LinkName" label="联系人：" prop="LinkName">
              <a-input placeholder="请输入" v-model="model.LinkName" :maxLength="20" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="LinkPhone" label="联系电话：" prop="LinkPhone">
              <a-input placeholder="请输入" v-model="model.LinkPhone" :maxLength="12" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </template>
      <!-- 开票信息 -->
      <template v-if="isShowInvoice">
        <a-row :gutter="gutter">
          <a-col :span="24">
            <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">开票信息</div>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="Title" label="纳税人姓名：" prop="Title">
              <a-input placeholder="请输入" v-model="model.Title" :maxLength="200" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="TaxNumber" label="纳税人识别号：" prop="TaxNumber">
              <a-input placeholder="请输入" v-model="model.TaxNumber" :maxLength="30" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="BankAccount" label="开户人：" prop="BankAccount">
              <a-input placeholder="请输入" v-model="model.BankAccount" :maxLength="80" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="BankName" label="开户银行：" prop="BankName">
              <a-input placeholder="请输入" v-model="model.BankName" :maxLength="60" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="BankCardNumber" label="银行账号：" prop="BankCardNumber">
              <a-input placeholder="请输入" v-model="model.BankCardNumber" :maxLength="50" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="RegPhone" label="注册电话：" prop="RegPhone">
              <a-input placeholder="请输入" v-model="model.RegPhone" :maxLength="12" />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="Email" label="电子邮箱：" prop="Email">
              <a-input placeholder="请输入" v-model="model.Email" :maxLength="40" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </template>
      <template v-if="model && model.CreateByName && model.CreateTime && isShowInfo">
        <a-row :gutter="gutter">
          <a-col :span="24">
            <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">登记信息</div>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="Title" label="创建人：">
              <a-input placeholder="请输入" v-model="model.CreateByName" disabled />
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item ref="TaxNumber" label="创建时间：">
              <a-input placeholder="请输入" v-model="model.CreateTime" disabled />
            </a-form-model-item>
          </a-col>
        </a-row>
      </template>
    </a-form-model>
    <!-- 详情展示部分 -->
    <a-card :bordered="false" v-else-if="opType === 4">
      <a-descriptions title="企业信息" v-if="isShowInfo">
        <a-descriptions-item label="供应商名称">{{ model.Name || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="企业类别">{{ model.TypeStr || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="企业二级分类">{{ model.SecondTypeStr || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="法人代表">{{ model.LegalPerson || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="企业负责人">{{ model.EnterpriseCharge || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="质量负责人">{{ model.QualityCharge || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="注册地址">{{ model.RegAddress || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="收货地址">{{ model.ShippingAddress || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="区域分类">{{ model.RegAreaName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="联系人">{{ model.LinkName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label=" 联系电话">{{ model.LinkPhone || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
      <a-descriptions title="开票信息" v-if="isShowInvoice && model.CustomerInvoice">
        <a-descriptions-item label="纳税人名称">{{ model.CustomerInvoice.Title || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="纳税人识别号">{{ model.CustomerInvoice.TaxNumber || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="开户人">{{ model.CustomerInvoice.BankAccount || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="银行账号">{{ model.CustomerInvoice.BankName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="开户银行">{{ model.CustomerInvoice.BankCardNumber || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="注册电话">{{ model.CustomerInvoice.RegPhone || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="电子邮箱">{{ model.CustomerInvoice.Email || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
      <a-descriptions title="登记信息" v-if="isShowInfo">
        <a-descriptions-item label="创建人">{{ model.CreateByName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ model.CreateTime || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
    </a-card>
  </div>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商品客户档案新增 基本信息',
  name: 'CusBasicInfoTemplateForm',
  mixins: [SimpleMixin, EditMixin],
  props: {
    /**
     * 1 新增  2 采购编辑 3质管编辑 4详情
     */
    opType: {
      type: Number,
      default: 1,
      required: true,
      validator: (s) => [1, 2, 3, 4].includes(s),
    },
    isShowInfo: {
      type: Boolean,
      default: false,
    },
    isShowInvoice: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      md: 8,
      model: {
        Name: '',
        Type: '',
        TypeStr: '',
        SecondType: '',
        SecondTypeStr: '',
        LegalPerson: '',
        EnterpriseCharge: '',
        QualityCharge: '',
        RegAddress: '',
        ShippingAddress: '',
        RegAreaCode: '',
        RegAreaId: null,
        RegAreaName: '',
        LinkName: '',
        LinkPhone: '',
        Title: '',
        TaxNumber: '',
        BankAccount: '',
        BankName: '',
        BankCardNumber: '',
        RegPhone: '',
        Email: '',
        SupplierId: undefined,
        SupplierName: undefined,
        SupplierCode: undefined,
      },
      Id: '', //已存在的客户ID
      areaValue: '',
      loading: false,
      tipInfo: null, //客户名称查询结果提醒// type  = 1 存在 去查看  2 已建立客户档案 可导入
      supplierInfo: undefined,
      secondTypeList: [],
      isShowArea: true,
      oldName: '',
    }
  },
  computed: {
    rules() {
      return {
        Name: [
          {
            required: true,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 80)
              })
            },
          },
        ],
        Type: [{ required: true, message: '请选择!' }],
        LegalPerson: [
          {
            required: true,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 30)
              })
            },
          },
        ],
        RegAddress: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 240) },
        ],
        ShippingAddress: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 240) },
        ],
        RegAreaCode: [{ required: true, message: '请选择!' }],
        LinkName: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 20) },
        ],
        LinkPhone: [{ required: true, validator: this.checkTelphone }],
        Title: [
          {
            required: true,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 200)
              })
            },
          },
        ],
        TaxNumber: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 30) },
        ],
        BankAccount: [
          {
            required: true,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 80)
              })
            },
          },
        ],
        BankName: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 60) },
        ],
        BankCardNumber: [{ required: true, validator: this.checkBankNo }],
        RegPhone: [{ required: true, validator: this.checkTelphone }],
        Email: [
          {
            required: false,
            validator: (rule, value, callback) => {
              const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
              if (this.model.Email && emailRegex.test(this.model.Email) == false) {
                callback(new Error('请输入正确的邮箱!'))
              } else {
                callback()
              }
            },
          },
        ],
        EnterpriseCharge: [
          {
            required: false,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 20)
              })
            },
          },
        ],
        QualityCharge: [
          {
            required: false,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 20)
              })
            },
          },
        ],
      }
    },
  },
  created() {},
  mounted() {
    if (this.info && this.info.Name) {
      this.model = this.info
      this.areaValue = this.getDefaultValueByCode(this.model.RegAreaCode)
      if (this.info.CustomerInvoice) {
        this.model.Title = this.info.CustomerInvoice.Title
        this.model.TaxNumber = this.info.CustomerInvoice.TaxNumber
        this.model.BankAccount = this.info.CustomerInvoice.BankAccount
        this.model.BankName = this.info.CustomerInvoice.BankName
        this.model.BankCardNumber = this.info.CustomerInvoice.BankCardNumber
        this.model.RegPhone = this.info.CustomerInvoice.RegPhone
        this.model.Email = this.info.CustomerInvoice.Email
      }
      this.oldName = this.model.Name

      this.model = JSON.parse(JSON.stringify(this.model))
    }
  },
  methods: {
    handleConfirmBlur() {
      if (this.oldName && this.oldName == this.model.Name) {
        return false
      }
      this.$refs.ruleForm.validateField(['Name'], (errMsg) => {
        if (!errMsg) {
          this.checkCusInfoByName()
        } else {
          this.tipInfo = null
        }
      })
    },
    /**
     * 更具供应商名称查询是否已存在和是否建立了客户档案
     */
    checkCusInfoByName() {
      getAction('/{v}/Customer/GetSameIdByName', { name: this.model.Name }, 'P36002').then((res) => {
        if (res.IsSuccess) {
          if (res.Data) {
            this.tipInfo = {
              type: 1,
              text: '该客户已存在，无法新增',
              btn: '去查看',
              oldId: res.Data,
              supInfo: undefined,
            }
          } else {
            getAction('/{v}/Supplier/GetSameIdByName', { name: this.model.Name, id: res.Data }, 'P36003').then(
              (result) => {
                if (result.IsSuccess && result.Data) {
                  this.tipInfo = {
                    type: 2,
                    text: '该客户已建立供应商档案，可直接导入信息',
                    btn: '导入',
                    oldId: result.Data,
                    supInfo: undefined,
                  }
                } else {
                  this.model.Title = this.model.Name
                }
              }
            )
          }
        }
      })
    },
    typeChange(val, txt, item) {
      this.model.TypeStr = txt
      if (txt == '批发企业') {
        if (this.secondTypeList && this.secondTypeList.length) {
          this.secondTypeList.forEach((e) => {
            if (e.ItemName == '商业客户') {
              this.model.SecondTypeStr = e.ItemName
              this.model.SecondType = e.ItemValue
            }
          })
        }
      } else if (txt == '零售连锁总部') {
        if (this.secondTypeList && this.secondTypeList.length) {
          this.secondTypeList.forEach((e) => {
            if (e.ItemName == '连锁公司') {
              this.model.SecondTypeStr = e.ItemName
              this.model.SecondType = e.ItemValue
            }
          })
        }
      }
    },
    secondTypeLoad(list) {
      this.secondTypeList = list || []
    },
    handleConfirmFocus() {
      this.tipInfo = null
    },
    goDetail() {
      if (this.tipInfo) {
        if (this.tipInfo.type == 1) {
          this.onDetailClick('/pages/fileManagement/commercialCustomerFile/commercialCustomerFileDetail', {
            id: this.tipInfo.oldId,
            acType: 4,
          })
        } else {
          if (this.loading) {
            return false
          }
          this.loading = true
          this.isShowArea = false
          getAction('/{v}/Supplier/GetSupplier', { id: this.tipInfo.oldId }, 'P36003')
            .then((result) => {
              if (result.IsSuccess && result.Data) {
                this.tipInfo.supInfo = result.Data
                this.$set(this, 'model', result.Data)
                this.model.Type = ''
                this.model.TypeStr = ''
                this.areaValue = this.getDefaultValueByCode(result.Data.RegAreaCode)
                this.$set(this.model, 'ShippingAddress', result.Data.StoreAddress)
                this.$set(this.model, 'Title', result.Data.Title)
                this.$set(this.model, 'TaxNumber', result.Data.TaxNumber)
                this.$set(this.model, 'Email', result.Data.Email)
                if (result.Data.SupplierInvoices && result.Data.SupplierInvoices.length) {
                  this.$set(this.model, 'BankAccount', result.Data.SupplierInvoices[0].BankAccount)
                  this.$set(this.model, 'BankName', result.Data.SupplierInvoices[0].BankName)
                  this.$set(this.model, 'BankCardNumber', result.Data.SupplierInvoices[0].BankCardNumber)
                  this.$set(this.model, 'RegPhone', result.Data.SupplierInvoices[0].RegPhone)
                }

                this.model.SupplierId = result.Data.SupplierRecordId
                this.model.SupplierName = result.Data.Name
                this.model.SupplierCode = result.Data.Code
                this.supplierInfo = result.Data
              } else {
                this.$message.warning(result.Msg)
              }
            })
            .finally(() => {
              this.loading = false
              this.isShowArea = true
            })
        }
      }
    },
    /*
     * IsDraft是否存为草稿
     * acType 1 新增  2 采购编辑 3质管编辑 4详情
     * activeTabKey 编辑或详情 1基础信息 2首营信息 3许可/经营范围 4开票信息 5 审核信息
     */
    onSubmit(IsDraft, acType, activeTabKey, callback) {
      if (this.tipInfo && this.tipInfo.type == 1) {
        callback && callback('isValidateFail')
        return false
      }
      if (IsDraft) {
        let list = ['Name', 'Type']
        let num = 0
        this.$refs.ruleForm.validateField(list, (errMsg) => {
          if (errMsg) {
            num++
          }
        })

        if (num == 0) {
          this.onSubmitPush(IsDraft, acType, activeTabKey, callback)
        } else {
          callback && callback('isValidateFail')
        }
      } else {
        this.$refs.ruleForm.validate((errMsg) => {
          if (errMsg) {
            this.onSubmitPush(IsDraft, acType, activeTabKey, callback)
          } else {
            callback && callback('isValidateFail')
          }
        })
      }
    },
    onSubmitPush(IsDraft, acType, activeTabKey, callback) {
      this.model.IsDraft = IsDraft
      this.model.Invoice = {
        Title: this.model.Title,
        TaxNumber: this.model.TaxNumber,
        BankAccount: this.model.BankAccount,
        BankName: this.model.BankName,
        BankCardNumber: this.model.BankCardNumber,
        RegPhone: this.model.RegPhone,
        Email: this.model.Email,
      }

      let url = '/v1/Customer/Create'

      if (acType == 2) {
        url = '/v1/Customer/UpdateByPurchase'
      } else if (acType == 3) {
        if (activeTabKey && activeTabKey == 4) {
          url = '/v1/Customer/UpdateCustomerInvoice'
          this.model.CustomerRecordId = this.model.Id
        } else {
          this.model.Invoice = null
          url = '/v1/Customer/UpdateByQuality'
        }
      }

      postAction(url, this.model, 'P36002').then((res) => {
        if (res.IsSuccess) {
          if (this.model.Id) {
            this.$message.success('编辑成功!')
          } else {
            this.$message.success('添加成功!')
          }

          //导入供应商
          if (this.supplierInfo) {
            this.$emit('uploadSup', this.supplierInfo)
          }
          if (acType == 3) {
            callback && callback()
          } else {
            callback && callback(res.Data)
          }
        } else {
          callback && callback('fail')
          this.$message.warning(res.Msg)
        }
      })
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
    },
  },
}
</script>

<style lang="less" scoped>

.errTip {
  color: #f5222d;
  font-size: 14px;
}
.go-page {
  padding-left: 6px;
  color: #1890ff;
}

/deep/.ant-form-item-control {
  height: 34px;
}
</style>
