<!--
 * @Author: 姚惠英
 * @Description: 首营信息
 * @Date: 2023/06/12
-->
<template>
  <a-spin :spinning="loading">
    <div class="rflex vcenter mb15" v-if="[3, 4].includes(opType)">
      <span>档案编号：</span>
      <a-input placeholder="" v-model="FileNo" style="width: 200px" :maxLength="30" :readOnly="true" />
      <a-button
        v-if="!FileNo && opType == 3"
        type="primary"
        class="ml15"
        @click="onShengChengBianHaoClick"
        :loading="fileNoLoading"
        >生成编号</a-button
      >
    </div>
    <FirstCampInformation
      :isEdit="[1, 2, 3].includes(opType)"
      ref="iFirstCampInformation"
      :remark="remark"
      :allList="dataList"
      :fromType="2"
      :businessId="addId ? addId : info ? info.Id || '' : ''"
      addGroupBtnId="2433a9f9-4584-4b4c-8c5a-5127b8e6aba1"
    />
  </a-spin>
</template>

<script>
import Vue from 'vue'
import { USER_ID, USER_INFO } from '@/store/mutation-types'
import moment from 'moment'
import { getAction, putAction, postAction } from '@/api/manage'
import { PublicMixin } from '@/mixins/PublicMixin'
export default {
  name: 'CusFirstCampInformation',
  mixins: [PublicMixin],
  props: {
    info: {
      type: Object,
      default: undefined,
    },
    addId: {
      type: String,
      default: '',
    },
    /**
     * 已填资质项
     */
    hasList: {
      type: Array,
      default: () => {
        return []
      },
    },
    /**
     * 1 新增  2 采购编辑 3质管编辑 4详情
     */
    opType: {
      type: Number,
      default: 1,
      required: true,
      validator: (s) => [1, 2, 3, 4].includes(s),
    },
    /**
     * 备注
     */
    remark: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      model: {
        CustomerRecordId: '',
        IsSubmitAudit: false,
        Remarks: '',
        Qualifications: [],
      },
      FileNo: '', //档案编号
      fileNoLoading: false,
      dataList: [],
      httpHead: 'P36001',
      url: {
        dataUrl: '/{v}/Qualification/QualificationGroupsByDataType',
        fileNoUrl: '/{v}/Customer/SaveCustomerFileNo',
      },
    }
  },

  computed: {},
  mounted() {
    this.loadFirstCampData()
    if (this.addId) {
      this.model.CustomerRecordId = this.addId
      return false
    }

    if (this.info && this.info.Id) {
      this.FileNo = this.info.FileNo || ''
      this.model.CustomerRecordId = this.info.Id
    }
  },
  created() {},
  methods: {
    moment,
    loadFirstCampData() {
      if (this.opType == 4) {
        this.parsingHasList(this.hasList, (list) => {
          this.dataList = list
        })
        return
      }
      let that = this
      this.loading = true
      getAction(this.url.dataUrl, { dataType: 2 }, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.parsingDataToBasicListData(
              (list) => {
                that.dataList = list
              },
              res.Data || [],
              this.hasList,
              [1, 2, 3].includes(this.opType),
              [1, 2].includes(this.opType)
            )
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    onSubmit(IsDraft, callback) {
      let data = this.$refs.iFirstCampInformation.getFirstCampSubData()
      if (!data) {
        callback && callback()
        return false
      }

      this.model.IsSubmitAudit = IsDraft
      this.model.Remarks = data.remark
      this.model.QualificationGroups = data.list
      postAction('/v1/Customer/CreateQualificationByPurchase', this.model, 'P36002')
        .then((res) => {
          if (res.IsSuccess) {
            this.$message.success('保存成功!')
            if (IsDraft && res.Data) {
              postAction(
                '/v1/ApprovalWorkFlowInstance/CreateApproval',
                {
                  MainBussinessNo: this.addId,
                  BussinessNo: res.Data,
                  Scenes: 2,
                  OpratorId: Vue.ls.get(USER_ID),
                },
                'P36005'
              ).then((res) => {
                if (res.IsSuccess) {
                  this.$router.go(-1)
                } else {
                  this.$message.warning(res.Msg)
                }
              })
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          callback && callback()
        })
    },
    onSubmitUpdate(callback) {
      let data = this.$refs.iFirstCampInformation.getFirstCampSubData()
      if (!data) {
        callback && callback()
        return false
      }
      this.model.Remarks = data.remark
      this.model.QualificationGroups = data.list
      postAction('/v1/Customer/CreateQualificationByQuality', this.model, 'P36002')
        .then((res) => {
          if (res.IsSuccess) {
            this.$message.success('操作成功!')
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          callback && callback()
        })
    },
    onSubmitColor(IsSubmit, callback) {
      let data = this.$refs.iFirstCampInformation.getFirstCampSubData()
      if (!data) {
        callback && callback()
        return false
      }
      this.model.Id = this.info.Id
      this.model.IsSubmit = IsSubmit || false
      this.model.Remarks = data.remark
      this.model.QualificationGroups = data.list

      postAction('/v1/Customer/UpdateCustomerQualification', this.model, 'P36004')
        .then((res) => {
          if (res.IsSuccess) {
            this.$message.success('操作成功!')
            if (IsSubmit) {
              this.$router.go(-1)
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          callback && callback()
        })
    },
    /**
     * 生成编号点击事件
     */
    onShengChengBianHaoClick() {
      let that = this
      that.fileNoLoading = true
      this.$confirm({
        title: '生成编号',
        content: '档案编号生成后不可再次生成，是否确认生成？',
        // showCancel: false,
        onOk: () => {
          that.createAndSaveNo()
        },
        onCancel: () => {
          that.fileNoLoading = false
        },
      })
    },
    /**
     * 生成档案编号
     */
    createAndSaveNo() {
      let that = this
      getAction(that.url.fileNoUrl, { customerId: this.info.Id }, 'P36002')
        .then((res) => {
          if (res.IsSuccess) {
            that.FileNo = res.Data || ''
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.fileNoLoading = false
        })
    },
  },
}
</script>

<style></style>
