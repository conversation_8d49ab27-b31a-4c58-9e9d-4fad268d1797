<!--
 * @Author: LP
 * @Description: 审核信息
 * @Date: 2023/06/12
-->
<template>
  <a-spin :spinning="loading">
    <a-card :bordered="false">
      <a-timeline v-if="auditList && auditList.length > 0">
        <a-timeline-item color="blue" v-for="(group, index) in auditList" :key="index">
          <p v-for="(item, i) in group.LabelInfo || []" :key="i">{{ item.Label }}{{ item.Value }}</p>
        </a-timeline-item>
      </a-timeline>
      <template v-else>
        <a-empty />
      </template>
    </a-card>
  </a-spin>
</template>

<script>
import { getAction } from '@/api/manage'
export default {
  name: 'CusAuditInformation',
  components: {},
  props: {
    /**
     * 审核id
     */
    AuditId: {
      type: String,
      default: () => {
        return ''
      },
    },
    /**
     * 审核数据
     */
    approval: {
      type: Object,
      default: () => {
        return {}
      },
    },
    // 是否默认调用接口
    isDefault: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      loading: false,
      auditList: null,
      httpHead: 'P36005',
      url: {
        list: '/v1/ApprovalWorkFlowInstance/GetApprovalResults',
      },
    }
  },
  computed: {
    rules() {
      return {}
    },
  },
  mounted() {
    if (this.isDefault && this.AuditId) {
      this.loadAuditList()
    }
  },
  created() {},
  methods: {
    loadAuditList() {
      if (this.approval && this.approval.ApprovalWorkFlowInstanceResults) {
        this.auditList = this.approval.ApprovalWorkFlowInstanceResults
      } else {
        this.loading = true
        getAction(this.url.list, { bussinessNo: this.AuditId }, this.httpHead)
          .then((res) => {
            if (res.IsSuccess) {
              this.auditList = res.Data.ApprovalWorkFlowInstanceResults || []
            } else {
              this.$message.error(res.Msg)
            }
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    setAuditList(data) {
      if (data && data.ApprovalWorkFlowInstanceResults) {
        this.auditList = data.ApprovalWorkFlowInstanceResults
      }
    },
  },
}
</script>

<style scoped>

</style>
