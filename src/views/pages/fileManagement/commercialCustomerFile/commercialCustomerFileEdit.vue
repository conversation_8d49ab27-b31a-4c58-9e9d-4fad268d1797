<!--
 * @Author: 姚惠英
 * @Description: 客户信息新增、编辑
 * @Date: 2023/06/11
-->
<template>
  <div>
    <a-alert
      v-if="info && info.AuditOpinion && info.AuditStatus && info.AuditStatus == 3"
      type="error"
      :message="'驳回原因：' + info.AuditOpinion"
      banner
      :showIcon="false"
    />
    <a-card style="margin: 15px 0">
      <a-spin :spinning="spinning">
        <a-steps :current="curStep" class="w50-center">
          <a-step title="基本信息" />
          <a-step title="首营信息" />
        </a-steps>
        <a-divider />
        <!-- 基本信息keepalive动态缓存组件 -->
        <keep-alive :include="cachedComponents">
          <CusBasicInfoTemplateForm
            ref="iCusBasicInfoTemplateForm"
            isShowInfo
            isShowInvoice
            :info="info"
            v-if="(info || acType == 1) && curStep === 0"
            :opType="acType"
            @uploadSup="uploadSup"
          />
        </keep-alive>
        <!-- v-else-if="curStep === 1" -->
        <CusFirstCampInformation
          ref="iCusFirstCampInformation"
          :hasList="qList"
          :opType="acType"
          v-if="curStep === 1"
          :info="info"
          :addId="id"
          :remark="remark"
        />
      </a-spin>
      <a-affix :offset-bottom="0" style="float: right; width: 100%; text-align: right">
        <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
          <a-button class="mr5" @click="prevStep()" v-if="curStep === 1">上一步</a-button>
          <YYLButton
            menuId="86ebc7ff-6223-4f68-a5fa-573fd0a0fdd9"
            type="primary"
            class="mr5"
            text="保存"
            v-if="curStep === 0 && acType == 1"
            @click="save(true, 1)"
            :loading="submitLoading"
          ></YYLButton>
          <YYLButton
            menuId="86ebc7ff-6223-4f68-a5fa-573fd0a0fdd9"
            type="primary"
            class="mr5"
            text="保存"
            v-if="curStep === 1 && acType == 1"
            :loading="submitLoading"
            @click="save(false, 2)"
          ></YYLButton>
          <YYLButton
            menuId="b7f2b65c-39e5-458a-883c-98e495a2a182"
            type="primary"
            class="mr5"
            v-if="curStep === 0"
            @click="save(false, 3)"
            text="下一步"
            :loading="submitLoading"
          ></YYLButton>
          <YYLButton
            menuId="8fdce8ef-996f-438a-8a5e-f40accf145ca"
            type="primary"
            class="mr5"
            v-if="curStep === 1"
            @click="save(true, 4)"
            text="保存并提交审核"
            :loading="submitLoading"
          ></YYLButton>
          <a-button @click="$router.go(-1)">取消</a-button>
        </a-card>
      </a-affix>
    </a-card>
  </div>
</template>

<script>
import { getAction, putAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  description: '商销客户档案新增',
  name: 'commercialCustomerFileEdit',
  mixins: [EditMixin],
  data() {
    return {
      spinning: false,
      curStep: 0,
      acType: 1, //1 新增（此界面不使用）  2 采购编辑（此界面不使用） 3质管编辑 4详情  (详情界面只会出现 3 和 4)
      id: '',
      info: undefined,
      qList: [], //资质list
      remark: '',
      submitLoading: false,

      currentComponent: 'CusBasicInfoTemplateForm',
      cacheActive: false,
    }
  },
  created() {},
  mounted() {
    this.initData()
    this.cacheActive = true
  },
  computed: {
    isEdit() {
      return this.acType === 3
    },
    cachedComponents() {
      return this.cacheActive ? ['CusBasicInfoTemplateForm'] : []
    },
  },
  methods: {
    initData() {
      if (this.$route.query) {
        this.id = this.$route.query.id || ''
        if (this.id) {
          this.getDetail()
        }

        this.acType = this.$route.query.acType ? Number(this.$route.query.acType) : 1
        if (this.acType == 1) {
          this.setTitle('商销客户档案新增')
        } else if (this.acType == 2) {
          this.setTitle('商销客户档案编辑')
        }
        this.info = undefined
      }
    },
    getDetail() {
      this.spinning = true
      getAction(
        '/v1/Customer/DetailByPurchase',
        {
          id: this.id,
        },
        'P36002'
      )
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.info = res.Data
              this.info.Id = this.id
              if (res.Data.CustomerQualificationVersion) {
                if (
                  res.Data.CustomerQualificationVersion.QualificationGroups &&
                  res.Data.CustomerQualificationVersion.QualificationGroups.length
                ) {
                  this.qList = res.Data.CustomerQualificationVersion.QualificationGroups
                }
                this.remark = res.Data.CustomerQualificationVersion.Remarks
              }
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.spinning = false
        })
    },
    save(IsDraft, type) {
      if (this.curStep === 0) {
        this.submitLoading = true
        this.$refs.iCusBasicInfoTemplateForm.onSubmit(IsDraft, this.acType, undefined, (data) => {
          this.submitLoading = false
          if (data == 'isValidateFail' || data == 'fail') {
            return false
          }

          this.id = data
          if (!IsDraft) {
            this.curStep = 1
          } else {
            this.$router.go(-1)
          }
        })
      } else {
        this.$refs.iCusFirstCampInformation.onSubmit(IsDraft, (res) => {
          this.submitLoading = false
        })
      }
    },
    uploadSup(e) {
      if (e.SupplierQualificationVersion && e.SupplierQualificationVersion.QualificationGroups) {
        this.qList = e.SupplierQualificationVersion.QualificationGroups
      }

      this.remark = e.SupplierQualificationVersion ? e.SupplierQualificationVersion.Remarks || '' : ''
    },
    prevStep() {
      // this.cacheActive = true
      this.curStep = 0
    },
  },
}
</script>

<style lang="less" scoped>

</style>
