<!--
 * @Author: 姚惠英
 * @Description: 商销客户列表
 * @Date: 2023/06/11
-->
<template>
  <div>
    <!-- 搜索 -->
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
      @onSetValid="(item, chceked) => handleSetValid(item, chceked, 'POST', 'Id')"
    />
  </div>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  description: '商销客户',
  name: 'commercialCustomerFileList',
  mixins: [ListMixin],
  data() {
    return {
      searchItems: [
        {
          name: '客户',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'KeyWord',
        },
        {
          name: '企业类别',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/v1/Global/GetListDictItemForCustomer',
          key: 'Type',
          searchKey: 'brandName',
          params: { groupPY: 'kehulb' },
          dataKey: { name: 'ItemValue', value: 'Id' },
        },
        {
          name: '是否活动',
          type: SEnum.SELECT,
          key: 'IsValid',
          items: [
            { label: '是', value: true },
            { label: '否', value: false },
          ],
          placeholder: '请选择',
        },
        {
          name: '审核状态',
          type: SEnum.ENUM,
          key: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
        },
        {
          name: '首营状态',
          type: SEnum.ENUM,
          key: 'AuthBusiness',
          dictCode: 'EnumAuthBusiness',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
        {
          name: '同步状态',
          type: SEnum.ENUM,
          key: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtns: [
          { name: '新增', type: 'primary', icon: 'plus', key: 'add', id: 'cd3848c8-c011-4619-92ba-34e7220d3ccc' },
        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '客户名称',
          dataIndex: 'Name',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户编号',
          dataIndex: 'Code',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '注册地址',
          dataIndex: 'RegAddress',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '联系电话',
          dataIndex: 'LinkPhone',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '企业类别',
          dataIndex: 'TypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '首营状态',
          dataIndex: 'AuthBusinessStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否活动',
          dataIndex: 'IsValid',
          width: 100,
          ellipsis: true,
          align: 'center',
          fixed: 'right',
          menuId: '9f5750e6-0f0e-4194-bef6-b853f2c66c52',
          scopedSlots: { customRender: 'isValid' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          fixed: 'right',
          actionBtns: [
            {
              name: '采购编辑',
              icon: '',
              id: 'b8085c2a-7b79-48e1-bdeb-f0a28c740088',
              isShow: (record) => {
                return record.AuditStatus != 1
              },
            },
            {
              name: '质管编辑',
              icon: '',
              id: '32b93ad7-79ec-42bb-a6f5-07335e1f33e9',
              isShow: (record) => {
                return record.AuditStatus != 1 && record.IsFormalCustomer
              },
            },
            {
              name: '详情',
              icon: '',
              id: 'a14b7103-c044-4027-a3d7-f6261a99e154',
              isShow: (record) => {
                return true //record.AuditStatus == 1
              },
            },
            {
              name: '删除',
              id: '0f9c054e-2668-45ec-9dcc-e197704f0534',
              isShow: (record) => {
                //当审核状态为未提交或者审核失败、首营状态为未通过、同步状态为未同步显示删除功能
                return [0, 3].includes(record.AuditStatus) && record.AuthBusiness === 1 && record.PushERPStatus === 1
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      // isTableInitData: true, //是否自动加载
      httpHead: 'P36002',
      // linkUrlType: 'GET', //请求方式
      url: {
        list: '/v1/Customer/List',
        setValidUrl: '/v1/Customer/UpdateCustomerIsValid', //若有是否有效需要写这个url
        delete: '/{v}/Customer/Delete',
      },
      tempUrl: {
        delete: '/v1/Customer/Delete',
      }
    }
  },
  created() {},
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    onOperateClick(key) {
      if (key == 'add') {
        this.onDetailClick('/pages/fileManagement/commercialCustomerFile/commercialCustomerFileEdit', {
          acType: 1,
        })
      }
    },
    // 列表操作
    onActionClick(record, type) {
      if (type == '详情') {
        this.onDetailClick('/pages/fileManagement/commercialCustomerFile/commercialCustomerFileDetail', {
          id: record.Id,
          acType: 4,
        })
      } else if (type == '采购编辑') {
        this.onDetailClick('/pages/fileManagement/commercialCustomerFile/commercialCustomerFileEdit', {
          id: record.Id,
          acType: 2,
        })
      } else if (type == '质管编辑') {
        this.onDetailClick('/pages/fileManagement/commercialCustomerFile/commercialCustomerFileDetail', {
          id: record.Id,
          acType: 3,
        })
      } else if (type === '删除') {
        this.url.delete=`${this.tempUrl.delete}?id=${record.Id}`
        this.handleDeleteClick()
      }
    },
  },
}
</script>

<style></style>
