<!--
 * @Author: LP
 * @Description: 供应商审核列表
 * @Date: 2023/06/20
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
    />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction } from '@/api/manage'
export default {
  name: 'SupplierAuditList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '请输入名称或者编号',
          type: SEnum.INPUT,
          key: 'KeyWord',
        },
        // {
        //   name: '企业类别',
        //   type: SEnum.RADIO,
        //   httpHead: 'P36001',
        //   url: '/{v}/Global/GetListDictItem',
        //   key: 'Kind',
        //   searchKey: 'brandName',
        //   params: { groupPY: 'kehulb' },
        //   dataKey: { name: 'ItemValue', value: 'ItemPym' }
        // },
        {
          name: '企业类别',
          type: SEnum.ENUM,
          key: 'Type',
          dictCode: 'EnumSupplierType',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '审核人',
          type: SEnum.INPUT,
          key: 'AuditorName',
        },
        // ,
        // {
        //   name: '创建时间',
        //   type: SEnum.DATE,
        //   rangeDate: this.rangeDate,
        //   key: 'CreateTime'
        // }
      ],
      tabDataList: [
        {
          name: '待审核',
          value: 'true',
          count: '',
          key: 'NeedAuditCount',
        },
        {
          name: '审核记录',
          value: 'false',
          count: '',
          key: 'AuditedCount',
        },
      ],
      tab: {
        tabTitles: ['供应商审核列表'],
        curStatus: 'true',
        tabStatusKey: 'IsNeedAudit', //标签的切换关键字
        operateBtns: [
          { name: '释放我的占用', type: '', icon: '', key: 'release', id: '094bcf24-2471-436a-a7f5-21e6a76a3e60' },
        ],
        bordered: false, //是否显示表格的边框
        rowKey: '',
      },
      httpHead: 'P36003',
      url: {
        list: '/{v}/SupplierAudit/List',
        tabCount: '/{v}/SupplierAudit/Count',
        deleteAllAuditOccupy: '/{v}/SupplierAudit/DeleteAllAuditOccupyAsync', //删除登录用户占用
      },
    }
  },
  computed: {
    columns() {
      const auditUser =
        this.tab.curStatus == 'true'
          ? [
              {
                title: '审核人',
                dataIndex: 'NowAuditByName',
                scopedSlots: { customRender: 'component' },
                ellipsis: true,
              },
            ]
          : []
      const auditStatus =
        this.tab.curStatus == 'false'
          ? [
              {
                title: '审核状态',
                dataIndex: 'AuditStatusStr',
                scopedSlots: { customRender: 'component' },
                ellipsis: true,
              },
            ]
          : []
      const detailAction =
        this.tab.curStatus == 'true' ? [] : [{ name: '详情', id: '649a259f-9304-440a-96ab-38cfa2dfd30b' }]
      const auditAction =
        this.tab.curStatus == 'true'
          ? [
              {
                name: '审核',
                id: '655bacc1-1eee-466f-a09c-c1594571e5d2',
                isShow: (record) => {
                  return !(record.NowAuditBy && record.NowAuditBy != this.getLoginUserId())
                },
              },
            ]
          : []
      const actionList = [
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [...detailAction, ...auditAction],
        },
      ]
      return [
        {
          title: '供应商名称',
          dataIndex: 'Name',
          scopedSlots: { customRender: 'component' },
          // fixed: 'left',
          ellipsis: true,
        },
        {
          title: '供应商编号',
          dataIndex: 'Code',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '企业类别',
          dataIndex: 'TypeStr',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },

        {
          title: '创建人',
          dataIndex: 'CreateByName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '提交时间',
          dataIndex: 'CreateTime',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        ...auditUser,
        ...auditStatus,
        ...actionList,
      ]
    },
  },
  created() {},
  methods: {
    onOperateClick(key) {
      if (key == 'release') {
        this.deleteAllOccupy()
      }
    },
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('SupplierAuditAndInfoPage', { id: record.Id, isAudit: 2 })
      } else if (key === '审核') {
        this.onDetailClick('SupplierAuditAndInfoPage', { id: record.Id, isAudit: 1 })
      }
    },
    /**
     * 删除占用
     */
    deleteAllOccupy() {
      getAction(this.url.deleteAllAuditOccupy, {}, this.httpHead).then((res) => {
        if (res.IsSuccess && res.Data) {
          this.$message.success('操作成功')
          this.loadData(1)
        }
      })
    },
  },
}
</script>
<style scoped>

</style>
