<!--
 * @Author: LP
 * @Description: 供应商新增和采购编辑页面
 * @Date: 2023/06/12
-->
<template>
  <div>
    <a-alert
      v-if="approvalInfo && approvalInfo.RejectReason"
      type="error"
      :message="'驳回原因：' + (approvalInfo.RejectReason || '--')"
      banner
      :showIcon="false"
      style="margin-bottom: 5px"
    />
    <a-card v-if="model">
      <template #title>
        <a-steps :current="curStep" class="w50-center">
          <a-step title="基本信息" />
          <a-step title="首营信息" />
        </a-steps>
      </template>
      <SupBasicInformation
        ref="supBasicInformation"
        v-if="curStep === 0"
        :opType="opType"
        :model="model"
        @import="importCustomerData"
      />

      <SupFirstCampInformation
        ref="supFirstCampInformation"
        v-else-if="curStep === 1"
        :opType="opType"
        :supId="supId"
        :remark="remark"
        :supType="model.Type"
        :hasList="qList"
        :fileNo="model.FileNo"
      />

      <a-affix :offset-bottom="10" class="affix">
        <!-- <a-divider /> -->
        <a-button class="mr8" v-if="curStep > 0" @click="onBackClick">上一步</a-button>

        <YYLButton
          v-if="opType == 1"
          menuId="15024005-abc2-4605-9ee5-6aa1b0974166"
          text="保存"
          type="primary"
          :loading="confirmLoading"
          @click="onSaveClick"
        />
        <template v-if="curStep > 0">
          <YYLButton
            menuId="31973a1e-e0f5-4ae9-ae50-cbdd376690ff"
            text="保存并提交审核"
            type="primary"
            :loading="confirmLoading"
            @click="onSaveAndAuditClick"
          />
        </template>
        <template v-if="curStep === 0">
          <YYLButton
            menuId="2c5996ae-ef2a-4d0a-8e82-4572265c1904"
            text="下一步"
            type="primary"
            :loading="confirmLoading"
            @click="onNextStepClick"
          />
        </template>
        <a-button @click="goBack(true,true)">取消</a-button>
      </a-affix>
    </a-card>
    <template v-else>
      <div class="empty-loading" v-if="loading">
        <a-spin size="large" />
      </div>
      <a-empty v-else />
    </template>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import YYLButton from '@/components/yyl/business/YYLButton.vue'
export default {
  components: { YYLButton },
  name: 'SupplierProfileEdit',
  mixins: [EditMixin],
  data() {
    return {
      curStep: 0,
      model: null,
      qList: [], //资质list
      loading: false,
      confirmLoading: false,
      supId: '', //供应商id
      remark: '',
      opType: 1, //1 新增 2 采购编辑 3质管编辑 4详情   11 七彩认证详情 12七彩认证编辑  [1,2,3,4,11]此界面不用
      httpHead: 'P36003',
      bankList: [
        {
          BankAccount: '',
          BankName: '',
          BankCardNumber: '',
        },
        {
          BankAccount: '',
          BankName: '',
          BankCardNumber: '',
        },
      ],
      approvalInfo: null,
      url: {
        detail: '',
        detail2: '/{v}/Supplier/GetSupplierRecord',
        detail3: '/{v}/Supplier/GetSupplier',
        import: '/{v}/Customer/DetailByPurchase',
        approvalResult: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults', //审批详情
      },
    }
  },
  computed: {},
  mounted() {
    this.initData()
  },
  created() {},
  methods: {
    initData() {
      if (this.$route.query) {
        this.supId = this.$route.query.id || ''
        this.opType = this.$route.query.opType ? Number(this.$route.query.opType) : 1
        this.url.detail = this.opType == 3 ? this.url.detail3 : this.url.detail2
        if (this.opType == 2) {
          this.loadDetailInfo()
        } else {
          this.model = {
            RegAreaCode: '',
            SupplierInvoices: [].concat(this.bankList),
          }
        }
      }
      this.setTitle('供应商' + (this.opType == 1 ? '新增' : '编辑'))
    },
    /**
     * 获取供应商详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true

      getAction(that.url.detail, { id: that.supId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data.SupplierQualificationVersion && res.Data.SupplierQualificationVersion.QualificationGroups) {
              that.qList = res.Data.SupplierQualificationVersion.QualificationGroups
            }

            that.model = res.Data || {}
            that.supId = that.model.Id
            that.model.Type = String(res.Data.Type)
            that.remark = res.Data.SupplierQualificationVersion
              ? res.Data.SupplierQualificationVersion.Remarks || ''
              : ''

            if (!that.model.SupplierInvoices || that.model.SupplierInvoices.length == 0) {
              that.model.SupplierInvoices = [].concat(that.bankList)
            } else if (that.model.SupplierInvoices.length == 1) {
              that.model.SupplierInvoices.push(that.bankList[0])
            }

            that.loadApprovalResult()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },

    /**
     * 上一步
     */
    onBackClick() {
      this.curStep = 0
    },
    /**
     * curSetp==0才会显示  下一步，需要验证表填必填是否填完整
     */
    onNextStepClick() {
      if (!this.$refs.supBasicInformation) {
        return
      }
      this.$refs.supBasicInformation.checkFrom((data) => {
        if (!data) {
          return
        }
        let subData = {
          IsNext: true,
          ...data,
        }
        this.confirmLoading = true
        if (this.supId) {
          if (!subData.Id) {
            subData['Id'] = this.supId
          }
          //编辑
          this.$refs.supBasicInformation.editSaveData(subData, (id) => {
            this.confirmLoading = false
            if (id) {
              this.supId = id
              this.curStep = 1
            }
          })
        } else {
          //新增
          this.$refs.supBasicInformation.addSaveData(subData, (id) => {
            if (id) {
              this.supId = id
              this.curStep = 1
            }
            this.confirmLoading = false
          })
        }
      })
    },
    /***
     * 保存按钮，第一页只需要验证 名称和类型完整即可提交
     */
    onSaveClick(isNext) {
      if (this.curStep == 0) {
        //第一步
        if (!this.$refs.supBasicInformation) {
          return
        }
        this.$refs.supBasicInformation.checkNameAndType((data) => {
          if (data) {
            data['IsNext'] = false
            if (!this.supId) {
              //新增
              this.confirmLoading = true
              this.$refs.supBasicInformation.addSaveData(data, (id) => {
                this.confirmLoading = false
                if (id) {
                  this.cancel()
                }
              })
            } else {
              if (!data.Id) {
                data['Id'] = this.supId
              }
              //编辑
              this.confirmLoading = true
              this.$refs.supBasicInformation.editSaveData(data, (id) => {
                this.confirmLoading = false
                if (id) {
                  this.cancel()
                }
              })
            }
          }
        })
      } else {
        //第二步
        if (!this.$refs.supFirstCampInformation) {
          return
        }
        if (!this.supId) {
          //新增
          this.confirmLoading = true
          this.$refs.supFirstCampInformation.addSaveData(false, (bool) => {
            this.confirmLoading = false
            if (bool) {
              this.cancel()
            }
          })
        } else {
          //编辑
          this.confirmLoading = true
          this.$refs.supFirstCampInformation.editSaveData(false, (bool) => {
            this.confirmLoading = false
            if (bool) {
              this.cancel()
            }
          })
        }
      }
    },
    onSaveAndAuditClick() {
      if (!this.$refs.supFirstCampInformation) {
        return
      }
      if (this.opType == 1) {
        //新增
        this.confirmLoading = true
        this.$refs.supFirstCampInformation.addSaveData(true, (bool) => {
          this.confirmLoading = false
          if (bool) {
            this.cancel()
          }
        })
      } else {
        //编辑
        this.confirmLoading = true
        this.$refs.supFirstCampInformation.editSaveData(true, (bool) => {
          this.confirmLoading = false
          if (bool) {
            this.cancel()
          }
        })
      }
    },
    cancel() {
      this.goBack(true,true)
    },
    /**
     * 导入供应商需要的所有基本和首营信息，从客户导入过来
     * @param {*} id 客户id
     */
    importCustomerData(id) {
      let that = this
      that.model = {}
      that.loading = true
      getAction(that.url.import, { id: id }, 'P36002')
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              that.changeCustomerDataToSup(res.Data)
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 转换客户数据到供应商
     * @param {*} cDate
     */
    changeCustomerDataToSup(cDate) {
      let basic = {
        Name: cDate.Name,
        Code: cDate.Code,
        // Kind: cDate.Kind,
        // KindStr: cDate.KindStr,
        LegalPerson: cDate.LegalPerson,
        EnterpriseCharge: cDate.EnterpriseCharge,
        QualityCharge: cDate.QualityCharge,
        RegAreaId: cDate.RegAreaId,
        RegAreaCode: cDate.RegAreaCode,
        RegAreaName: cDate.RegAreaName,
        RegAddress: cDate.RegAddress,
        StoreAddress: cDate.ShippingAddress,
        LinkName: cDate.LinkName,
        LinkPhone: cDate.LinkPhone,
      }
      let invoice = {}
      if (cDate.CustomerInvoice) {
        invoice = {
          Title: cDate.CustomerInvoice.Title,
          TaxNumber: cDate.CustomerInvoice.TaxNumber,
          SupplierInvoices: [
            {
              BankAccount: cDate.CustomerInvoice.BankAccount,
              BankName: cDate.CustomerInvoice.BankName,
              BankCardNumber: cDate.CustomerInvoice.BankCardNumber,
            },
            {
              BankAccount: '',
              BankName: '',
              BankCardNumber: '',
            },
          ],
        }
      }
      let supplierQualificationVersion = {
        QualificationGroups: [],
      }
      if (cDate.CustomerQualificationVersion) {
        this.remark = cDate.CustomerQualificationVersion.Remarks
        if (cDate.CustomerQualificationVersion.QualificationGroups) {
          this.qList = cDate.CustomerQualificationVersion.QualificationGroups
          supplierQualificationVersion.QualificationGroups = cDate.CustomerQualificationVersion.QualificationGroups
        }
      }

      this.model = {
        ...basic,
        ...invoice,
        SupplierQualificationVersion: supplierQualificationVersion,
      }
      setTimeout(() => {
        this.$refs.supBasicInformation && this.$refs.supBasicInformation.setAreaCodes()
      }, 200)

      console.log('import qList', this.qList)
    },
    /**
     * 获取审批记录详情
     */
    loadApprovalResult() {
      if (!this.model || !this.model.AuditId || !this.model.AuditStatus || this.model.AuditStatus < 1) {
        return
      }
      let that = this
      getAction(that.url.approvalResult, { bussinessNo: this.model.AuditId }, 'P36005').then((res) => {
        if (res.IsSuccess) {
          that.approvalInfo = res.Data
        } else {
          that.$message.error(res.Msg)
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>

/deep/.ant-alert-message {
  color: red;
}
</style>
