<!--
 * @Author: LP
 * @Description: 供应商七彩认证列表
 * @Date: 2023/06/19
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
    />
    <ViewSignatureInfoModal
      ref="viewSignatureInfoModal"
      :allList="fileList"
      nameKey="QualificationGroupName"
      urlKey="CACertFileUrl"
    />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction } from '@/api/manage'
export default {
  name: 'SupSevenColorCertificationList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'KeyWord'
        },
        // {
        //   name: '企业类别',
        //   type: SEnum.RADIO,
        //   httpHead: 'P36001',
        //   url: '/{v}/Global/GetListDictItem',
        //   key: 'Kind',
        //   searchKey: 'brandName',
        //   params: { groupPY: 'kehulb' },
        //   dataKey: { name: 'ItemValue', value: 'ItemPym' }
        // },
        {
          name: '企业类别',
          type: SEnum.ENUM,
          key: 'SupplierType',
          dictCode: 'EnumSupplierType'
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName'
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime'
        }
      ],
      tabDataList: [
        {
          name: '认证失败',
          value: '4',
          count: '',
          key: 'Count4'
        },
        {
          name: '认证中',
          value: '2',
          count: '',
          key: 'Count2'
        },
        {
          name: '认证通过',
          value: '3',
          count: '',
          key: 'Count3'
        },
        {
          name: '全部',
          value: '',
          count: '',
          key: 'Count0'
        }
      ],
      tab: {
        tabTitles: ['供应商七彩认证列表'],
        curStatus: '',
        tabStatusKey: 'CaAuditStatus', //标签的切换关键字
        operateBtns: [],
        bordered: false, //是否显示表格的边框
        rowKey: ''
      },

      httpHead: 'P36004',
      fileList: [],
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'Name',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true
        },
        {
          title: '供应商编号',
          dataIndex: 'Code',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '企业类别',
          dataIndex: 'TypeStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '注册地址',
          dataIndex: 'RegAddress',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '联系电话',
          dataIndex: 'LinkPhone',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '认证状态',
          dataIndex: 'CaAuditStatusStr',
          width: 150,
          // customRender: (t, r) => {
          //   const color = t ? 'green' : 'red'
          //   return <a-tag color={color}>{t ? '是' : '否'}</a-tag>
          // },
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '提交时间',
          dataIndex: 'CreateTime',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: '4374e61b-21ea-4e33-b38a-7557ea2e04d4',
              isShow: record => {
                return record.CaAuditStatus == 3 || record.CaAuditStatus == 2
              }
            },
            {
              name: '编辑',
              id: '7f167aaa-88cf-4860-89df-7b973ff4dadb',
              isShow: record => {
                return record.CaAuditStatus == 4 || record.CaAuditStatus == 1
              }
            },
            {
              name: '查看签章资料',
              id: 'cecebeee-44bc-46ba-b27c-6248a3940fab',
              isShow: record => {
                return record.CaAuditStatus == 3
              }
            }
          ]
        }
      ],
      url: {
        list: '/{v}/Supplier/List',
        tabCount: '/{v}/Supplier/Count',
        getFile: '/{v}/Supplier/ListCACertFile'
      }
    }
  },
  computed: {},
  created() {},
  methods: {
    onOperateClick(key) {},
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('SupSevenColorCertificationDetail', { id: record.Id })
      } else if (key === '编辑') {
        this.onDetailClick('SupSevenColorCertificationEdit', { id: record.Id, opType: 12 })
      } else if (key === '查看签章资料') {
        this.getFiles(record.Id)
      }
    },
    getFiles(id) {
      let that = this
      that.loading = true
      getAction(that.url.getFile, { id: id, certDateType: 1 }, this.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            if (res.Data && res.Data.length) {
              that.fileList = res.Data
              that.$refs.viewSignatureInfoModal.show()
            } else {
              that.$message.warning('暂无签章资料')
            }
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .catch(e => {
          that.loading = false
        })
        .finally(() => {
          that.loading = false
        })
    }
  }
}
</script>
<style scoped>

</style>
