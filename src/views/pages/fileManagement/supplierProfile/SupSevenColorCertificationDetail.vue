<!--
 * @Author: LP
 * @Description: 供应商七彩认证详情界面
 * @Date: 2023/06/12
-->
<template>
  <a-card>
    <a-tabs v-model="activeTabKey">
      <a-tab-pane :key="1" tab="基础信息">
        <SupBasicInformation
          ref="supBasicInformation"
          :opType="opType"
          :model="model"
          v-if="activeTabKey == 1 && model"
        />
      </a-tab-pane>
      <a-tab-pane :key="2" tab="首营信息">
        <SupFirstCampInformation
          ref="supFirstCampInformation"
          :supId="model.Id"
          :opType="opType"
          :supType="model.Type"
          :hasList="qList"
          v-if="activeTabKey == 2 && model"
        />
      </a-tab-pane>
      <a-tab-pane :key="6" tab="审核信息">
        <SupAuditInformation
          v-if="activeTabKey == 6 && model"
          :bussinessNo="model.CaAuditId"
          :aList="model.auditList || []"
        />
      </a-tab-pane>
    </a-tabs>
    <template v-if="!model && activeTabKey != 6">
      <a-empty />
    </template>
    <a-affix :offset-bottom="10" class="flr">
      <a-button @click="goBack(true,true)">返回</a-button>
    </a-affix>
  </a-card>
</template>

<script>
import { getAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'SupSevenColorCertificationDetail',
  mixins: [EditMixin],
  data() {
    return {
      activeTabKey: 1,
      confirmLoading: false,
      model: null,
      qList: [], //资质list
      loading: false,
      opType: 11, //1 新增 2 采购编辑 3质管编辑 4详情   11 七彩认证详情 12七彩认证编辑  [1,2,3,4,12]此界面不用
      supId: '', //供应商id
      httpHead: 'P36004',
      url: {
        detail: '/{v}/Supplier/Detail',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.supId = this.$route.query.id || ''
      this.loadDetailInfo()
    }
  },
  created() {},
  methods: {
    /**
     * 获取供应商详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.supId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data.SupplierCaContrastQualificationVersion) {
              if (res.Data.SupplierCaContrastQualificationVersion.QualificationGroups) {
                that.qList = res.Data.SupplierCaContrastQualificationVersion.QualificationGroups
              }
            }

            that.model = res.Data || {}
            that.model.Type = String(res.Data.Type)
            that.model.auditList = []
            if (that.model.Records) {
              that.model.Records.forEach((x) => {
                let group = {
                  LabelInfo: [
                    {
                      Label: '',
                      Value: x.CaAuditTime,
                    },
                  ],
                }
                if (x.CaAuditStatusStr) {
                  group.LabelInfo.push({
                    Label: '审核状态：',
                    Value: x.CaAuditStatusStr,
                  })
                }
                if (x.CaAuditOpinion) {
                  group.LabelInfo.push({
                    Label: '备注：',
                    Value: x.CaAuditOpinion,
                  })
                }
                that.model.auditList.push(group)
              })
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
