<!--
 * @Author: LP
 * @Description: 供应商详情
 * @Date: 2023/06/12
-->
<template>
  <a-card>
    <a-tabs v-model="activeTabKey">
      <a-tab-pane :key="1" tab="基础信息">
        <SupBasicInformation
          ref="supBasicInformation"
          :opType="opType"
          :model="model"
          v-if="activeTabKey == 1 && model"
        />
      </a-tab-pane>
      <a-tab-pane :key="2" tab="首营信息">
        <SupFirstCampInformation
          ref="supFirstCampInformation"
          :supId="model.Id"
          :remark="remark"
          :opType="opType"
          :hasList="qList"
          :supType="model.Type"
          :fileNo="model.FileNo"
          v-if="activeTabKey == 2 && model"
        />
      </a-tab-pane>
      <a-tab-pane :key="3" tab="许可/经营范围">
        <SupBusinessScope
          ref="supBusinessScope"
          :isEdit="isEdit"
          v-if="activeTabKey == 3 && model"
          :model="oldModel"
          scopeKey="ManageClassifyTypeDto"
        />
      </a-tab-pane>
      <a-tab-pane :key="4" tab="开票信息">
        <SupInvoicingInformation
          ref="supInvoicingInformation"
          :isEdit="isEdit"
          :model="model"
          v-if="activeTabKey == 4 && model"
        />
      </a-tab-pane>
      <a-tab-pane :key="5" tab="委托范围">
        <SupEntrustScope ref="supEntrustScope" :isEdit="isEdit" v-if="activeTabKey == 5 && model && !loading" :model="model" />
      </a-tab-pane>
      <a-tab-pane :key="6" tab="审核信息">
        <SupAuditInformation v-if="activeTabKey == 6" :bussinessNo="model.AuditId" />
      </a-tab-pane>
    </a-tabs>
    <template v-if="!model && activeTabKey < 5">
      <div class="empty-loading" v-if="loading">
        <a-spin size="large" />
      </div>
      <a-empty v-else />
    </template>
    <a-affix :offset-bottom="10" style="float: right; width: 100%; text-align: right">
      <template v-if="isEdit && activeTabKey != 6">
        <YYLButton
          menuId="6d783166-a7e6-4a1f-a39e-dcbdbb9fa716"
          text="保存当前页面"
          type="primary"
          :loading="confirmLoading"
          @click="onSavePagaDataClick"
        />
        <!-- <a-button
          type="primary"
          class="mr8"
          v-if="checkBtnPermissions('6d783166-a7e6-4a1f-a39e-dcbdbb9fa716')"
          @click="onSavePagaDataClick"
          :loading="confirmLoading"
          >保存当前页面</a-button
        > -->
      </template>
      <a-button @click="goBack(true,true)">{{ isEdit ? '取消' : '返回' }}</a-button>
    </a-affix>
  </a-card>
</template>

<script>
import { getAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'SupplierProfileDetail',
  mixins: [EditMixin],
  data() {
    return {
      activeTabKey: 1,
      confirmLoading: false,
      model: null,
      oldModel: null, //原始数据
      qList: [], //资质list
      loading: false,
      opType: 4, //1 新增（此界面不使用）  2 采购编辑（此界面不使用） 3质管编辑 4详情  (详情界面只会出现 3 和 4)
      supId: '', //供应商id
      remark: '',
      httpHead: 'P36003',
      bankList: [
        {
          BankAccount: '',
          BankName: '',
          BankCardNumber: '',
        },
        {
          BankAccount: '',
          BankName: '',
          BankCardNumber: '',
        },
      ],
      url: {
        detail: '',
        detail2: '/{v}/Supplier/GetSupplierRecord',
        detail3: '/{v}/Supplier/GetSupplier',
      },
    }
  },
  computed: {
    isEdit() {
      return this.opType === 3
    },
  },
  mounted() {
    this.initData()
  },
  created() {},
  methods: {
    initData() {
      if (this.$route.query) {
        this.supId = this.$route.query.id || ''
        this.opType = this.$route.query.opType ? Number(this.$route.query.opType) : 4
        this.url.detail = this.opType == 3 ? this.url.detail3 : this.url.detail2
        this.loadDetailInfo()

        this.setTitle('供应商' + (this.opType == 3 ? '编辑' : '详情'))
      }
    },
    /**
     * 获取供应商详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.supId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data.SupplierQualificationVersion && res.Data.SupplierQualificationVersion.QualificationGroups) {
              that.qList = res.Data.SupplierQualificationVersion.QualificationGroups
            }

            that.model = res.Data || {}
            that.oldModel = JSON.parse(JSON.stringify(that.model))
            that.model.Type = String(res.Data.Type)
            that.remark = res.Data.SupplierQualificationVersion
              ? res.Data.SupplierQualificationVersion.Remarks || ''
              : ''

            if (that.isEdit) {
              if (!that.model.SupplierInvoices || that.model.SupplierInvoices.length == 0) {
                that.model.SupplierInvoices = [].concat(that.bankList)
              } else if (that.model.SupplierInvoices.length == 1) {
                that.model.SupplierInvoices.push(that.bankList[0])
              }
            }
            console.log('detail', that.model)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    onSavePagaDataClick() {
      switch (this.activeTabKey) {
        case 1:
          this.$refs.supBasicInformation.checkFrom((data) => {
            if (data) {
              //编辑
              this.confirmLoading = true
              this.$refs.supBasicInformation.editSaveData(data, this.saveSuccess)
            }
          })
          break
        case 2:
          this.confirmLoading = true
          this.$refs.supFirstCampInformation.editSaveData(false, this.saveSuccess, this.model.SupplierRecordId)
          break
        case 3:
          this.confirmLoading = true
          this.$refs.supBusinessScope.editSaveData(this.saveSuccess)
          break
        case 4:
          this.confirmLoading = true
          this.$refs.supInvoicingInformation.editSaveData(this.saveSuccess)
          break
        case 5:
          this.confirmLoading = true
          this.$refs.supEntrustScope.editSaveData(this.saveSuccess)
          break
        default:
          break
      }
    },
    saveSuccess(bool) {
      this.confirmLoading = false
      if (bool) {
        this.loadDetailInfo()
      }
    },
    onTabsChange(key) {
      if (this.isEdit && key != 1 && !this.model.IsFormal) {
        this.activeTabKey = 1
        let that = this
        this.$confirm({
          title: '提示',
          content: '请先保存基础信息！',
          showCancel: false,
          // onOk: () => {
          //   that.activeTabKey = 1
          // }
        })
      }
    },
  },
}
</script>

<style scoped>

</style>
