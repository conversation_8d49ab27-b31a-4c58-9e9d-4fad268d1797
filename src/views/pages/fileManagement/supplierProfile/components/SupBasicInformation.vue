<!--
 * @Author: LP
 * @Description: 基础信息
 * @Date: 2023/06/12
-->
<template>
  <a-spin :spinning="loading">
    <!-- 编辑 -->
    <a-form-model ref="formBasic" :rules="rules" :model="model" v-if="[1, 2, 3, 12].includes(opType)">
      <!-- 企业信息 -->
      <a-row :gutter="gutter">
        <a-col :span="24">
          <span style="font-size: 16px; font-weight: 600">企业信息</span>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="供应商名称" prop="Name">
            <a-input
              placeholder="请输入"
              v-model="model.Name"
              style="width: 100%"
              :maxLength="80"
              @blur="onInputNameBlur"
              @focus="onInputNameFocus"
            />
            <span slot="help" class="cred" v-if="tipInfo"
              >{{ tipInfo.text }}，<a @click="onTipBtnClick">{{ tipInfo.btn }}</a></span
            >
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="企业类别" prop="Type">
            <EnumSingleChoiceView
              style="width: 100%"
              placeholder="请选择"
              dictCode="EnumSupplierType"
              :disabled="opType == 12"
              v-model="model.Type"
              @change="(val, txt, item) => (model.TypeStr = txt)"
            />
            <!-- <SingleChoiceView
              style="width: 100%;"
              placeholder="请选择"
              :httpParams="{
                groupPY: 'kehulb'
              }"
              httpHead="P36001"
              :dataKey="{ name: 'ItemValue', value: 'Id' }"
              v-model="model.Type"
              :Url="'/{v}/Global/GetListDictItem'"
              @change="(val, txt, item) => (model.TypeStr = txt)"
            /> -->
          </a-form-model-item>
        </a-col>
        <template v-if="opType != 12">
          <a-col :span="span">
            <a-form-model-item label="法人代表" prop="LegalPerson">
              <a-input placeholder="请输入" v-model="model.LegalPerson" style="width: 100%" :maxLength="30" />
            </a-form-model-item>
          </a-col>
          <a-col :span="span">
            <a-form-model-item label="企业负责人" prop="EnterpriseCharge">
              <a-input placeholder="请输入" v-model="model.EnterpriseCharge" style="width: 100%" :maxLength="20" />
            </a-form-model-item>
          </a-col>
          <a-col :span="span">
            <a-form-model-item label="质量负责人" prop="QualityCharge">
              <a-input placeholder="请输入" v-model="model.QualityCharge" style="width: 100%" :maxLength="20" />
            </a-form-model-item>
          </a-col>
          <a-col :span="span">
            <a-form-model-item label="企业类型" prop="Kind">
              <SingleChoiceView
                style="width: 100%"
                placeholder="请选择"
                :httpParams="{
                  groupPY: 'qylx',
                }"
                httpHead="P36001"
                :dataKey="{ name: 'ItemValue', value: 'ItemValue' }"
                v-model="model.Kind"
                :Url="'/{v}/Global/GetListDictItem'"
                @change="(val, txt, item) => (model.KindStr = txt)"
              />
              <!-- <EnumSingleChoiceView
              style="width: 100%;"
              placeholder="请选择"
              dictCode="EnumStatisticsType"
              v-model="model.Kind"
              @change="(val, txt, item) => (model.KindStr = txt)"
            /> -->
            </a-form-model-item>
          </a-col>
        </template>
        <a-col :span="span">
          <a-form-model-item label="注册地址" prop="RegAddress">
            <a-input placeholder="请输入" v-model="model.RegAddress" style="width: 100%" :maxLength="240" />
          </a-form-model-item>
        </a-col>
        <a-col :span="span" v-if="opType != 12">
          <a-form-model-item label="仓库地址" prop="StoreAddress">
            <a-input placeholder="请输入" v-model="model.StoreAddress" style="width: 100%" :maxLength="240" />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="区域分类（省市区）" prop="RegAreaCode">
            <SelectAreaView
              v-if="areaCodeRefrsh"
              style="width: 100%"
              ref="selectCityAreas"
              :selectCode="true"
              :defaultCodes="areaCodes"
              @change="(val, node) => onAreasChange(val, node, model, 'RegArea')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="联系人" prop="LinkName">
            <a-input placeholder="请输入" v-model="model.LinkName" style="width: 100%" :maxLength="20" />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="联系电话" prop="LinkPhone">
            <a-input placeholder="请输入" v-model="model.LinkPhone" style="width: 100%" :maxLength="12" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- 开票信息 -->
      <a-row :gutter="gutter" v-if="opType != 3 && opType != 12">
        <a-col :span="24" class="mt10">
          <span style="font-size: 16px; font-weight: 600">开票信息</span>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="纳税人名称" prop="Title">
            <a-input
              placeholder="请输入"
              v-model="model.Title"
              style="width: 100%"
              :maxLength="200"
              @change="$forceUpdate()"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="纳税人识别号" prop="TaxNumber">
            <a-input
              placeholder="请输入"
              v-model="model.TaxNumber"
              style="width: 100%"
              :maxLength="30"
              @change="$forceUpdate()"
            />
          </a-form-model-item>
        </a-col>
        <template v-if="(model.SupplierInvoices || []).length > 0">
          <a-col :span="span">
            <a-form-model-item label="开户人" prop="BankAccount">
              <a-input
                placeholder="请输入"
                v-model="model.SupplierInvoices[0].BankAccount"
                style="width: 100%"
                :maxLength="80"
              />
            </a-form-model-item>
          </a-col>
          <template v-for="(bank, index) in model.SupplierInvoices">
            <a-col :span="span" :key="'BankName' + index">
              <a-form-model-item :label="'开户银行' + (index + 1)" :prop="'BankName' + (index + 1)">
                <a-input placeholder="请输入" v-model="bank.BankName" style="width: 100%" :maxLength="60" />
              </a-form-model-item>
            </a-col>
            <a-col :span="span" :key="'BankCardNumber' + index">
              <a-form-model-item :label="'银行账号' + (index + 1)" :prop="'BankCardNumber' + (index + 1)">
                <a-input placeholder="请输入" v-model="bank.BankCardNumber" style="width: 100%" :maxLength="50" />
              </a-form-model-item>
            </a-col>
          </template>
        </template>
      </a-row>
      <!-- 登记信息 -->
      <a-row :gutter="gutter" v-if="opType == 3">
        <a-col :span="24" class="mt10">
          <span style="font-size: 16px; font-weight: 600">登记信息</span>
        </a-col>
        <a-col :span="24">
          <a-descriptions>
            <a-descriptions-item label="创建人">{{ model.CreateByName || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ model.CreateTime || ' -- ' }}</a-descriptions-item>
          </a-descriptions>
        </a-col>
      </a-row>
    </a-form-model>
    <!-- 详情展示部分 -->
    <a-card :bordered="false" v-else-if="opType == 4 || opType == 11">
      <a-descriptions title="企业信息">
        <a-descriptions-item label="供应商名称">{{ model.Name || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="企业类别">{{ model.TypeStr || ' -- ' }}</a-descriptions-item>
        <template v-if="opType != 11">
          <a-descriptions-item label="法人代表">{{ model.LegalPerson || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="企业负责人">{{ model.EnterpriseCharge || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="质量负责人">{{ model.QualityCharge || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="企业类型">{{ model.KindStr || ' -- ' }}</a-descriptions-item>
        </template>
        <a-descriptions-item label=" 注册地址">{{ model.RegAddress || ' -- ' }}</a-descriptions-item>
        <template v-if="opType != 11">
          <a-descriptions-item label="仓库地址">{{ model.StoreAddress || ' -- ' }}</a-descriptions-item>
        </template>

        <a-descriptions-item label="区域分类">{{ model.RegAreaName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="联系人">{{ model.LinkName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label=" 联系电话">{{ model.LinkPhone || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
      <template v-if="opType == 4">
        <a-descriptions title="登记信息">
          <a-descriptions-item label="创建人">{{ model.CreateByName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ model.CreateTime || ' -- ' }}</a-descriptions-item>
        </a-descriptions>
      </template>
    </a-card>
  </a-spin>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'SupBasicInformation',
  mixins: [EditMixin],
  components: {},
  props: {
    /**
     * 操作类型//1 新增 2 采购编辑 3质管编辑 4详情   11 七彩认证详情 12七彩认证编辑
     */
    opType: {
      type: Number,
      default: 1,
      required: true,
      validator: (s) => [1, 2, 3, 4, 11, 12].includes(s),
    },
    /**
     * 除新增外都需要传这个参数
     */
    model: {
      type: Object,
      default: () => {
        return {
          RegAreaCode: '',
          SupplierInvoices: [],
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      span: 8,
      httpHead: 'P36003',
      areaCodeRefrsh: true,
      tipInfo: null, //供应商名称查询结果提醒// type  = 1 存在 去查看  2 已建立客户档案 可导入
      oldName: '',
      url: {
        add: '/{v}/Supplier/CreateSupplierRecord',
        edit: '',
        edit1: '/{v}/Supplier/UpdateSupplierRecordByPurchase',
        edit2: '/{v}/Supplier/UpdateSupplierRecordByPurchase',
        edit3: '/{v}/Supplier/UpdateSupplier',
        edit12: '',
      },
    }
  },
  computed: {
    rules() {
      return {
        Name: [
          {
            required: true,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 80)
              })
            },
          },
        ],
        Kind: [{ required: false, message: '请选择!' }],
        LegalPerson: [
          {
            required: true,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 30)
              })
            },
          },
        ],
        EnterpriseCharge: [
          {
            required: false,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 20)
              })
            },
          },
        ],
        QualityCharge: [
          {
            required: false,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 20)
              })
            },
          },
        ],
        Type: [{ required: true, message: '请选择!' }],
        RegAddress: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 240) },
        ],
        StoreAddress: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 240) },
        ],
        RegAreaCode: [{ required: true, message: '请选择!' }],
        LinkName: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 20) },
        ],
        LinkPhone: [{ required: true, validator: this.checkTelphone }],
        Title: [
          {
            required: true,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 200)
              })
            },
          },
        ],
        TaxNumber: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 30) },
        ],
        BankAccount: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                this.model.SupplierInvoices &&
                this.model.SupplierInvoices.length > 0 &&
                !this.model.SupplierInvoices[0].BankAccount
              ) {
                callback(new Error('请输入!'))
              } else {
                this.checkChinese(rule, this.model.SupplierInvoices[0].BankAccount, (res) => {
                  if (res != undefined) {
                    callback(res)
                    return
                  }
                  this.validStrMaxLength(rule, this.model.SupplierInvoices[0].BankAccount, callback, 80)
                })
              }
            },
          },
        ],
        BankName1: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                this.model.SupplierInvoices &&
                this.model.SupplierInvoices.length > 0 &&
                !this.model.SupplierInvoices[0].BankName
              ) {
                callback(new Error('请输入!'))
              } else {
                this.validStrMaxLength(rule, this.model.SupplierInvoices[0].BankName, callback, 60)
              }
            },
          },
        ],
        BankCardNumber1: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                this.model.SupplierInvoices &&
                this.model.SupplierInvoices.length > 0 &&
                !this.model.SupplierInvoices[0].BankCardNumber
              ) {
                callback(new Error('请输入!'))
              } else {
                this.checkBankNo(rule, this.model.SupplierInvoices[0].BankCardNumber, callback)
                // this.validStrMaxLength(rule, this.model.SupplierInvoices[0].BankAccount, callback, 60)
              }
            },
          },
        ],
        BankName2: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (
                this.model.SupplierInvoices &&
                this.model.SupplierInvoices.length > 1 &&
                this.model.SupplierInvoices[1].BankName
              ) {
                this.validStrMaxLength(rule, this.model.SupplierInvoices[1].BankName, callback, 60)
              } else {
                callback()
              }
            },
          },
        ],
        BankCardNumber2: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (
                this.model.SupplierInvoices &&
                this.model.SupplierInvoices.length > 1 &&
                this.model.SupplierInvoices[1].BankCardNumber
              ) {
                this.checkBankNo(rule, this.model.SupplierInvoices[1].BankCardNumber, callback)
                // this.validStrMaxLength(rule, this.model.SupplierInvoices[0].BankAccount, callback, 60)
              } else {
                callback()
              }
            },
          },
        ],

        CreateByName: [{ required: true, message: '请输入!' }],
        CreateTime: [{ required: true, message: '请输入!' }],
      }
    },
  },
  mounted() {
    this.url.edit = this.url['edit' + this.opType]
    if (this.opType > 10) {
      this.httpHead = 'P36004'
    }
    this.oldName = ''
    if (this.model) {
      this.areaCodes = this.getDefaultValueByCode(this.model.RegAreaCode)
      this.oldName = '' + this.model.Name
    }
  },
  created() {},

  methods: {
    setAreaCodes() {
      if (this.model.RegAreaCode) {
        this.areaCodes = this.getDefaultValueByCode(this.model.RegAreaCode)

        this.areaCodeRefrsh = false
        this.$nextTick(() => {
          this.areaCodeRefrsh = true
        })
      }
    },
    /**
     * 验证供应商名称和企业类别
     */
    checkNameAndType(callback) {
      let list = ['Name', 'Type']
      let num = 0
      this.$refs.formBasic.validateField(list, (errMsg) => {
        num++
        if (num == list.length) {
          if (!errMsg) {
            let oldModel = JSON.parse(JSON.stringify(this.model))
            callback && callback(oldModel)
          } else {
            callback && callback()
          }
        }
      })
    },
    /**
     * 验证整个表单
     * @param {回调 返回data非空  则验证通过 执行下一步操作 反之 表单验证未通过} callback = funcation(data)
     */
    checkFrom(callback) {
      this.$refs.formBasic.validate((err, values) => {
        if (err) {
          let oldModel = JSON.parse(JSON.stringify(this.model))
          let data = Object.assign(oldModel, values)
          callback && callback(data)
        } else {
          callback && callback()
        }
      })
    },
    /***
     * 新增保存数据
     */
    addSaveData(data, callback) {
      if (!data) {
        this.$message.error('参数错误！')
        callback && callback()
        return
      }
      let that = this
      let subModel = { ...data }
      subModel.Type = Number(data.Type)
      subModel.SupplierInvoice = {
        Title: data.Title,
        TaxNumber: data.TaxNumber,
        SupplierInvoiceRecords: [],
      }
      subModel.SupplierInvoice.SupplierInvoiceRecords = data.SupplierInvoices.filter((x) => {
        return x.BankName && x.BankCardNumber
      })
      subModel.SupplierInvoice.SupplierInvoiceRecords.forEach((x) => {
        if (x.BankName && x.BankCardNumber && !x.BankAccount) {
          x.BankAccount = subModel.SupplierInvoice.SupplierInvoiceRecords[0].BankAccount
        }
      })
      that.loading = true
      postAction(that.url.add, subModel, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            callback && callback(res.Data)
          } else {
            that.$message.error(res.Msg)
            callback && callback()
          }
        })
        .catch((e) => {
          that.loading = false
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 修改保存数据
     * @param {*} data
     * @param {*} callback
     */
    editSaveData(data, callback) {
      if (!data) {
        this.$message.error('参数为空！')
        callback && callback()
        return
      }
      let that = this
      console.log('data', data)
      let subModel = { ...data, Id: data.IsFormal ? data.SupplierRecordId : data.Id }
      subModel.Type = Number(data.Type)
      if (that.opType < 3) {
        if (data.SupplierInvoices && data.SupplierInvoices.length > 0) {
          subModel.SupplierInvoice = {
            Title: data.Title,
            TaxNumber: data.TaxNumber,
            SupplierInvoiceRecords: [],
          }
          subModel.SupplierInvoice.SupplierInvoiceRecords = data.SupplierInvoices.filter((x) => {
            return x.BankName && x.BankCardNumber
          })
          subModel.SupplierInvoice.SupplierInvoiceRecords.forEach((x) => {
            if (x.BankName && x.BankCardNumber && !x.BankAccount) {
              x.BankAccount = subModel.SupplierInvoice.SupplierInvoiceRecords[0].BankAccount
            }
          })
        }
      }

      that.loading = true
      postAction(that.url.edit, subModel, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            callback && callback(res.Data)
          } else {
            that.$message.error(res.Msg)
            callback && callback()
          }
        })
        .catch((e) => {
          callback && callback()
          that.loading = false
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 供应商名称输入框获取焦点事件
     */
    onInputNameFocus() {
      this.tipInfo = null
    },
    /**
     * 供应商名称输入框失去焦点事件
     */
    onInputNameBlur() {
      let that = this
      this.$refs.formBasic.validateField(['Name'], (errMsg) => {
        if (!errMsg) {
          // if (!that.model.Title) {
          that.model.Title = that.model.Name
          // }
          if ([2, 3, 12].includes(that.opType)) {
            if (that.oldName && that.model.Name == that.oldName) {
            } else {
              that.checkSupInfoByName()
            }
          } else {
            that.checkSupInfoByName()
          }
          that.$forceUpdate()
        } else {
          that.tipInfo = null
        }
      })
    },
    /**
     * 更具供应商名称查询是否已存在和是否建立了客户档案
     */
    checkSupInfoByName() {
      let that = this
      getAction('/{v}/Supplier/GetSameIdByName', { name: this.model.Name }, 'P36003').then((res) => {
        if (res.IsSuccess) {
          if (res.Data) {
            that.tipInfo = {
              type: 1,
              text: '该供应商已存在，无法新增',
              btn: '去查看',
              id: res.Data,
            }
          } else {
            //新增和采购编辑 -才有导入功能
            if ([1, 2].includes(that.opType)) {
              getAction('/{v}/Customer/GetSameIdByName', { name: that.model.Name, id: '' }, 'P36002').then((result) => {
                if (result.IsSuccess && result.Data) {
                  that.tipInfo = {
                    type: 2,
                    text: '该供应商已建立客户档案，可直接导入信息',
                    btn: '导入',
                    id: result.Data,
                  }
                }
              })
            }
          }
        }
      })
    },
    onTipBtnClick() {
      if (!this.tipInfo) {
        return
      }
      switch (this.tipInfo.type) {
        case 1: //去查看
          this.$router.push({
            path: 'SupplierProfileDetail',
            query: { id: this.tipInfo.id, opType: 4 },
          })
          break
        case 2:
          this.$emit('import', this.tipInfo.id)
          setTimeout(() => {
            this.tipInfo = null
          }, 200)
          break
      }
    },
  },
}
</script>

<style lang="less" scoped>

/deep/.ant-form-item-control {
  height: 34px;
}
</style>
