<!--
 * @Author: LP
 * @Description: 许可/经营范围
 * @Date: 2023/06/13
-->
<template>
  <a-spin :spinning="loading">
    <template v-if="isEdit">
      <!-- 编辑 -->
      <template v-if="!isAudit">
        <a-card
          v-for="(bigGroup, bigIndex) in dataList"
          :key="bigIndex"
          :title="bigGroup.ManageClassifyTypeStr"
          class="mb15 mt15"
        >
          <div slot="extra">
            <a-button type="link" :icon="bigGroup.isOpen ? 'up' : 'down'" @click="onBigGroupOpenClick(bigIndex)">{{
              bigGroup.isOpen ? '收 起' : '展 开'
            }}</a-button>
          </div>
          <template v-if="bigGroup.isOpen">
            <a-card
              v-for="(group, index) in bigGroup.ManageClassifies.filter((x) => {
                return x.ManageClassifies && x.ManageClassifies.length > 0
              })"
              :key="index"
              :title="group.GroupName"
              class="mb15 mt15"
            >
              <div slot="extra">
                <a-checkbox
                  :ref="'group' + index"
                  :checked="group.checkAll"
                  @change="(e) => onCheckAllChange(e, group, index, bigIndex)"
                  v-if="isEdit"
                >
                  全选
                </a-checkbox>
              </div>
              <a-checkbox-group
                v-model="group.checkedList"
                @change="(e) => onCheckBoxGroupChange(e, index, bigIndex)"
                style="width: 100%"
              >
                <a-row>
                  <a-col :span="span" v-for="(item, j) in group.ManageClassifies" :key="j">
                    <a-checkbox :value="item">
                      {{ item }}
                    </a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </a-card>
          </template>
        </a-card>
      </template>
      <!-- 审核 -->
      <template v-else>
        <a-tabs class="pane-box" v-model="curBigTabKey" @change="curtabKey = 0">
          <a-tab-pane v-for="(bigGroup, bigIndex) in dataList" :key="bigIndex" :tab="bigGroup.ManageClassifyTypeStr">
            <a-tabs class="pane-box" v-model="curtabKey">
              <a-tab-pane
                v-for="(group, index) in bigGroup.ManageClassifies.filter((x) => {
                  return x.ManageClassifies && x.ManageClassifies.length > 0
                })"
                :key="index"
                :tab="group.GroupName"
              >
                <a-card>
                  <div slot="title" style="padding-right: 20px">
                    <a-input placeholder="请输入" v-model="group.filterKey" />
                  </div>
                  <a-checkbox
                    slot="extra"
                    :ref="'group' + index"
                    :checked="group.checkAll"
                    @change="(e) => onCheckAllChange(e, group, index, bigIndex)"
                    v-if="isEdit"
                  >
                    全选
                  </a-checkbox>
                  <a-checkbox-group
                    v-model="group.checkedList"
                    @change="(e) => onCheckBoxGroupChange(e, index, bigIndex)"
                    style="width: 100%"
                  >
                    <a-row>
                      <!-- <a-col :span="span" v-for="(item,i) in group.ManageClassifies.filter(x => search(x, group))" :key="i">
                    <a-checkbox :value="item" :key='i'>
                      {{ item }}
                    </a-checkbox>
                  </a-col> -->
                      <template v-for="(item, i) in group.ManageClassifies">
                        <a-col :span="span" v-show="search(item, group)" :key="i">
                          <a-checkbox :value="item" :key="i">
                            {{ item }}
                          </a-checkbox>
                        </a-col>
                      </template>
                    </a-row>
                  </a-checkbox-group>
                </a-card>
              </a-tab-pane>
            </a-tabs>
          </a-tab-pane>
        </a-tabs>
      </template>
    </template>
    <!-- 详情 -->
    <template v-else>
      <template v-if="model && model[scopeKey] && model[scopeKey].length > 0">
        <!-- <div style="font-size: 16px; margin-bottom: 10px">
          {{ model.Type == 1 ? '经营范围' : '许可范围' }}
        </div> -->
        <a-card
          v-for="(bigGroup, bigIndex) in formatModel"
          :key="bigIndex"
          :title="bigGroup.ManageClassifyTypeStr"
          class="mb15 mt15"
        >
          <a-card
            v-for="(group, gIndex) in bigGroup.ManageClassifies"
            :key="gIndex"
            :title="group.GroupName"
            style="margin-bottom: 12px"
          >
            <a-row>
              <a-col :span="span" v-for="(item, index) in group.ManageClassifies || []" :key="index">
                {{ item }}
              </a-col>
            </a-row>
          </a-card>
        </a-card>
      </template>
      <a-empty v-else />
    </template>
  </a-spin>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { PublicMixin } from '@/mixins/PublicMixin'
export default {
  name: 'SupBusinessScope',
  mixins: [PublicMixin],
  components: {},
  props: {
    /**
     * 是否是审核
     */
    isAudit: {
      type: Boolean,
      default: false,
    },
    /**
     * 可否编辑
     */
    isEdit: {
      type: Boolean,
      default: false,
    },
    /**
     * 数据
     */
    model: {
      type: Object,
      default: () => {
        return {}
      },
    },
    span: {
      type: Number,
      default: 8,
    },
    /**
     * 是否是客户
     */
    isCustomer: {
      type: Boolean,
      default: false,
    },
    /**
     * 经营返回接口返回model中的属性 关键字
     */
    scopeKey: {
      type: String,
      default: 'Classifies',
    },
  },
  data() {
    return {
      loading: false,
      dataList: [],
      httpHead: 'P36003',
      curtabKey: 0,
      curBigTabKey: 0,
      url: {
        list: '/{v}/Global/GetManageClassifies',
        edit: '/{v}/Supplier/UpdateSupplierClassify',
        editCustomer: '/v1/Customer/UpdateCustomerClassify',
      },
    }
  },
  watch: {},
  created() {},
  computed: {
    formatModel() {
      const list = JSON.parse(JSON.stringify(this.model[this.scopeKey]))
      console.log(123, list);
      list.forEach((bigGroup) => {
        if(bigGroup.ManageClassifyType === 1) {
          bigGroup.ManageClassifies.forEach(group => {
            if (group.GroupName === 'II类器械') {
              group.ManageClassifies = this.getNewList(group.ManageClassifies)
            }
            if (group.GroupName === 'III类器械') {
              group.ManageClassifies = this.getNewList(group.ManageClassifies)
            }
          })
        }
      })

      return list
    }
  },
  mounted() {
    if (this.isEdit) {
      this.getList()
    }
  },
  methods: {
    /**
     * @description: 获取所有经营范围
     * @param {*}
     * @return {*}
     */
    getList() {
      let that = this
      this.loading = true
      getAction(
        this.url.list,
        {
          manageClassifyType: this.isCustomer ? 1 : null,
        },
        'P36001'
      )
        .then((res) => {
          if (res.IsSuccess && res.Data) {
            res.Data.forEach((bigGroup) => {
              bigGroup.isOpen = true
              bigGroup.ManageClassifies.forEach((group) => {
                if ((group.ManageClassifies || []).length) {
                  group.checkAll = false
                  group.checkedList = []
                  group.filterKey = ''
                  //循环内容
                  // if (that.isCustomer) {
                  //   group.ManageClassifies.forEach((child) => {
                  //     //具体这个选项是否在已选择数据中
                  //     if (this.model[this.scopeKey]) {
                  //       let index = this.model[this.scopeKey].findIndex((x) => {
                  //         return x == child
                  //       })
                  //       if (index > -1) {
                  //         group.checkedList.push(child)
                  //       }
                  //     }
                  //   })
                  // } else {
                  //通过当前group去找model的数据中是否有当前组的数据
                  let sGroup = null
                  ;(that.model[that.scopeKey] || []).forEach((y) => {
                    if (y.ManageClassifyType === bigGroup.ManageClassifyType) {
                      sGroup = (y.ManageClassifies || []).find((z) => z.GroupName === group.GroupName)
                    }
                  })
                  if (sGroup && sGroup.ManageClassifies && sGroup.ManageClassifies.length > 0) {
                    // let typeData = sGroup.ClassifyNames.filter(t=>t.ManageClassifyType==that.model.Type)
                    //有、则循环赋值
                    group.ManageClassifies.forEach((child) => {
                      //具体这个选项是否在已选择数据中
                      let index = sGroup.ManageClassifies.findIndex((x) => {
                        return x === child
                      })
                      if (index > -1) {
                        group.checkedList.push(child)
                      }
                    })
                  }
                  // }
                }
                if (that.isAudit === false) {
                  if (group.GroupName === 'II类器械') {
                    group.ManageClassifies = this.getNewList(group.ManageClassifies)
                  }

                  if (group.GroupName === 'III类器械') {
                    group.ManageClassifies = this.getNewList(group.ManageClassifies)
                  }
                }
              })
            })
            this.dataList = res.Data || []

            console.log('dataList', this.dataList)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    onCheckBoxGroupChange(selectedList, index, bigIndex) {
      this.dataList[bigIndex].ManageClassifies[index].checkedList = selectedList
      this.$emit('check', this.dataList)
    },
    /**
     * @description: 全选
     * @param {*} e
     * @param {*} item
     * @return {*}
     */
    onCheckAllChange(e, group, index, bigIndex) {
      let checked = e.target.checked
      this.dataList[bigIndex].ManageClassifies[index].checkAll = e.target.checked
      if (checked) {
        this.dataList[bigIndex].ManageClassifies[index].checkedList = [].concat(group.ManageClassifies)
      } else {
        this.dataList[bigIndex].ManageClassifies[index].checkedList = []
      }

      this.$emit('check', this.dataList)
    },
    search(x, group) {
      if (group.filterKey) {
        let filterList = group.filterKey.split('')
        let xList = x.split('')
        let bool = false
        for (var i = 0; i < filterList.length; i++) {
          bool = xList.includes(filterList[i])
          if (bool) {
            break
          }
        }
        return bool
      } else {
        return true
      }
    },
    /**
     * 编辑数据
     * @param {*} callback
     */
    editSaveData(callback) {
      let that = this
      let subModel =
        this.isCustomer === true
          ? {
              CustomerRecordId: this.model.Id,
              CustomerClassifys: [],
            }
          : {
              SupplierRecordId: this.model.SupplierRecordId,
              SupplierClassifys: [],
              // ManageClassifyType: this.model.Type * 1,
            }
      let cList = []
      that.dataList.forEach((bigGroup) => {
        let gItem = {
          ManageClassifyType: bigGroup.ManageClassifyType,
          Classifies: [],
        }
        bigGroup.ManageClassifies.forEach((group) => {
          if (group.checkedList && group.checkedList.length > 0) {
            group.checkedList.forEach((x) => {
              gItem.Classifies.push({
                GroupName: group.GroupName,
                ClassifyName: x,
              })
            })
          }
        })
        if (gItem.Classifies.length > 0) {
          cList.push(gItem)
        }
      })
      if (this.isCustomer === true) {
        subModel.CustomerClassifys = cList
      } else {
        subModel.SupplierClassifys = cList
      }
      console.log('SupBusinessScope editSaveData', subModel)

      that.loading = true
      postAction(
        that.isCustomer === true ? that.url.editCustomer : that.url.edit,
        subModel,
        that.isCustomer === true ? 'P36002' : that.httpHead
      )
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            callback && callback(true)
          } else {
            that.$message.error(res.Msg)
            callback && callback(false)
          }
        })
        .catch((e) => {
          callback && callback(false)
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 获取选择的数据
     */
    getData() {
      let checkedList = []
      this.dataList.forEach((bigGroup) => {
        let gItem = {
          ManageClassifyType: bigGroup.ManageClassifyType,
          Classifies: [],
        }
        bigGroup.ManageClassifies.forEach((group) => {
          if (group.checkedList && group.checkedList.length > 0) {
            group.checkedList.forEach((x) => {
              gItem.Classifies.push({
                GroupName: group.GroupName,
                ClassifyName: x,
              })
            })
          }
        })
        if (gItem.Classifies.length > 0) {
          checkedList.push(gItem)
        }
      })

      return checkedList
    },
    onBigGroupOpenClick(index) {
      this.dataList[index].isOpen = !this.dataList[index].isOpen
    },
  },
}
</script>
<style lang="less" scoped>

/deep/.ant-checkbox-group-item {
  display: inline-block;
  margin-right: 20px;
  margin-bottom: 20px;
}
.mb15 {
  margin-bottom: 15px;
}
.mt15 {
  margin-top: 15px;
}
/deep/.pane-box .ant-tabs-nav .ant-tabs-tab {
  margin: 0 15px 0 0;
}
</style>
