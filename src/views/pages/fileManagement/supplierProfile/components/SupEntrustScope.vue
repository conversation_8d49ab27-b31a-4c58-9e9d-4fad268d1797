<!--
 * @Author: LP
 * @Description: 委托范围
 * @Date: 2023/06/13
-->
<template>
  <a-spin :spinning="loading" v-if="model">
    <a-card class="mb20" v-for="(item, index) in model.SupplierDelegateScopes" :key="index">
      <div>
        <span class="wlabel">委托人姓名：</span><span class="mr30">{{ item.DelegateUserName || '--' }}</span>
        <span class="wlabel">委托人身份证号：</span><span class="mr30">{{ item.DelegateUserIDCard || '--' }}</span>
        <span class="wlabel">委托人有效期：</span
        >{{
          (item.DelegateDateTimeBegin ? formatToYYYYMMDD(item.DelegateDateTimeBegin) + '至' : '') +
          (formatToYYYYMMDD(item.DelegateDateTime) || '--')
        }}
      </div>
      <template>
        <div class="radio-view">
          <enum-single-choice-view
            style="width: 100%"
            placeholder="请选择"
            tagType="radio"
            :disabled="!isEdit"
            dictCode="EnumDelegateScopeType"
            v-model="item.Type"
            @change="(val, txt, obj) => (item.TypeStr = txt)"
            v-if="isEdit"
          />
          <span v-else>{{ item.TypeStr ? item.TypeStr : item.Type == 1 ? '大委托' : '小委托' }}</span>
        </div>
        <div v-if="item.Type == 2">
          <div style="text-align: right; margin-bottom: 10px; width: 100%" v-if="isEdit">
            <YYLButton
              menuId="86e57b32-c941-4dd9-910d-420dbac00750"
              text="新增"
              type="primary"
              @click="onAddClick(index)"
            />
          </div>

          <a-table
            :bordered="true"
            :ref="'table' + index"
            rowKey="Id"
            size="middle"
            :columns="columns"
            :dataSource="item.SupplierDelegateGoodses || []"
            :pagination="ipagination"
            :loading="loading"
            @change="handleTableChange"
            :scroll="{ y: 300, x: '100%' }"
          >
            <!-- 字符串超长截取省略号显示-->
            <span slot="component" slot-scope="text">
              <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
              <j-ellipsis :value="text" v-else />
            </span>
            <span slot="action" slot-scope="text, record">
              <a @click="onDeleteClick(record, index)">移除</a>
            </span>
          </a-table>
        </div>
      </template>
    </a-card>
    <!-- <template v-else>
      <a-empty />
    </template> -->
    <SupSelectGoodsModal ref="supSelectGoodsModal" @ok="addGoodsLit" />
  </a-spin>
</template>

<script>
import { postAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'SupEntrustScope',
  mixins: [ListMixin, EditMixin],
  components: { JEllipsis },
  props: {
    /**
     * 可否编辑
     */
    isEdit: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否审核
     */
    isAudit: {
      type: Boolean,
      default: false,
    },
    /**
     * 数据
     */
    model: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      loading: false,

      httpHead: 'P36003',
      url: {
        edit: '/{v}/Supplier/UpdateSupplierDelegateScope',
      },
    }
  },
  computed: {
    rules() {
      return {}
    },
    columns() {
      const actions = this.isEdit
        ? [
            {
              title: '操作',
              dataIndex: 'action',
              align: 'center',
              width: 100,
              fixed: 'right',
              actionBtn: [{ name: '移除', icon: '' }],
              scopedSlots: { customRender: 'action' },
            },
          ]
        : []
      return [
        {
          title: '商品名称',
          dataIndex: 'Name',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'Code',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'Specification',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'Manufacturer',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        ...actions,
      ]
    },
  },
  mounted() {
    this.initData()
  },
  created() {},
  methods: {
    // moment,
    initData() {
      if (this.model.SupplierDelegateScopes && this.model.SupplierDelegateScopes.length > 0) {
        this.model.SupplierDelegateScopes.forEach((item, index, arr) => {
          //如果没选择啥类型
          if (!item.Type) {
            //审核界面 要默认大委托
            if (this.isAudit) {
              item.Type = '1'
              item.TypeStr = '大委托'
            }
          } else if (item.Type == 2) {
            //小委托的话 要把商品数据展示出来
            if (this.$refs['table' + index]) {
              this.$refs['table' + index].dataSource = item.SupplierDelegateGoodses || []
            }
          }
          item.Type = String(item.Type)
        })
      } else {
        if (this.isEdit) {
          this.model.SupplierDelegateScopes = [
            {
              DelegateUserName: '',
              DelegateUserIDCard: '',
              DelegateDateTime: '',
              Type: 0,
              SupplierDelegateGoodses: [],
            },
          ]
        }
      }
      console.log('委托list', this.model.SupplierDelegateScopes)
    },
    onAddClick(index) {
      this.$refs.supSelectGoodsModal.show(this.model.Type == 2 ? this.model.Name : '', index)
    },
    onDeleteClick(record, index) {
      this.removeGoods(record.Id, index)
    },
    /**
     * 移除商品
     * @param {*} id
     */
    removeGoods(id, tabIndex) {
      if (tabIndex > -1) {
        let index = this.model.SupplierDelegateScopes[tabIndex].SupplierDelegateGoodses.findIndex((x) => {
          return x.Id == id
        })
        if (index > -1) {
          this.model.SupplierDelegateScopes[tabIndex].SupplierDelegateGoodses.splice(index, 1)
        }
      }
    },
    /**
     * 添加商品
     * @param {*} sList
     */
    addGoodsLit(sList, index) {
      let newList = []
      sList.forEach((x) => {
        let item = this.model.SupplierDelegateScopes[index].SupplierDelegateGoodses.find((y) => {
          return y.Code == x.ErpGoodsCode
        })
        if (!item) {
          newList.push({
            Id: x.Id,
            Name: x.ErpGoodsName,
            Code: x.ErpGoodsCode,
            ERPGoodsId: x.ERPGoodsId,
            Specification: x.PackingSpecification,
            Manufacturer: x.BrandManufacturer,
          })
        }
      })
      if (newList.length > 0 && index > -1) {
        this.model.SupplierDelegateScopes[index].SupplierDelegateGoodses =
          this.model.SupplierDelegateScopes[index].SupplierDelegateGoodses.concat(newList)
      }
    },
    /**
     * 编辑数据
     * @param {*} callback
     */
    editSaveData(callback) {
      let that = this
      let list = JSON.parse(JSON.stringify(this.model.SupplierDelegateScopes))
      list.forEach((item, index, arr) => {
        item.Type = Number(item.Type)
        if (item.Type == 1) {
          item.SupplierDelegateGoodses = []
        }
      })
      let subModel = {
        SupplierDelegateScopes: list,
        SupplierRecordId: this.model.SupplierRecordId,
      }
      that.loading = true
      postAction(that.url.edit, subModel, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            callback && callback(true)
          } else {
            that.$message.error(res.Msg)
            callback && callback(false)
          }
        })
        .catch((e) => {
          callback && callback(false)
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 获取数据
     */
    getData() {
      let list = JSON.parse(JSON.stringify(this.model.SupplierDelegateScopes))
      list.forEach((item, index, arr) => {
        item.Type = Number(item.Type)
        if (item.Type == 1) {
          item.SupplierDelegateGoodses = []
        }
      })
      return list
    },
  },
}
</script>

<style scoped>

.mr30 {
  margin-right: 30px;
}
.mb20 {
  margin-bottom: 20px;
}
.radio-view {
  margin-top: 20px;
  margin-bottom: 15px;
}
.wlabel {
  color: rgba(0, 0, 0, 0.55);
  font-size: 14px;
}
</style>
