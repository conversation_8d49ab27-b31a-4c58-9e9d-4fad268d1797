<!--
 * @Author: LP
 * @Description: 审核右边区域-供应商信息
 * @Date: 2023/06/20
-->
<template>
  <a-tabs v-model="activeTabKey">
    <a-tab-pane :key="1" tab="基础信息" class="card">
      <SupAuditBasicInformation
        ref="supAuditBasicInformation"
        :opType="1"
        :model="model"
        :isEdit="isEdit"
        v-if="model"
      />
      <template v-if="activeTabKey == 1">
        <div style="margin-top: 15px; margin-bottom: 10px; font-weight: 600">审核信息</div>
        <SupAuditInformation :aList="auditList" :bussinessNo="model.AuditId" />
      </template>
    </a-tab-pane>
    <a-tab-pane :key="3" tab="许可/经营范围" class="card">
      <SupBusinessScope
        ref="supBusinessScope"
        :isEdit="isEdit"
        v-if="model"
        :span="24"
        :isAudit="true"
        :model="model"
        scopeKey="ManageClassifyTypeDto"
      />
    </a-tab-pane>
    <a-tab-pane :key="4" tab="开票信息" class="card">
      <SupAuditBasicInformation
        ref="supAuditKaiPiaoInformation"
        :isEdit="isEdit"
        :model="model"
        :opType="4"
        v-if="model"
      />
    </a-tab-pane>
    <a-tab-pane :key="5" tab="委托范围" class="card">
      <SupEntrustScope ref="supEntrustScope" :isEdit="isEdit" :isAudit="true" v-if="model" :model="model" />
    </a-tab-pane>
    <!-- <a-tab-pane :key="6" tab="审核信息">
      <SupAuditInformation v-if="activeTabKey == 6" :bussinessNo="model.Id" :aList="auditList" />
    </a-tab-pane> -->
  </a-tabs>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'SupAuditInfoComponents',
  mixins: [EditMixin],
  props: {
    /**
     * 数据对象
     */
    model: {
      type: Object,
      required: true,
      default: () => {
        return {}
      },
    },
    /**
     * 是否可编辑
     **/
    isEdit: {
      type: Boolean,
      required: true,
      default: () => {
        return false
      },
    },
    /**
     * 审核记录信息
     */
    auditList: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      activeTabKey: 1,
      confirmLoading: false,
      qList: [], //资质list
      loading: false,
      supId: '', //供应商id
      remark: '',
      httpHead: 'P36003',
      url: {
        detail: '',
      },
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    /**
     * 获取数据
     * @param {*} callback 返回布尔值 true 成功 false 失败
     */
    checkData(callback) {
      if (this.$refs.supAuditBasicInformation) {
        this.$refs.supAuditBasicInformation.checkFrom((basicInfo) => {
          if (basicInfo) {
            this.checkTab2(callback)
          } else {
            this.$message.error('基础信息-未填写完整！')
            this.activeTabKey = 1
            callback && callback(false)
          }
        })
      } else {
        callback && callback(false)
      }
    },
    checkTab2(callback) {
      let time = 0
      if (this.activeTabKey != 3) {
        this.activeTabKey = 3
        time = 200
      }
      setTimeout(() => {
        let classList = this.$refs.supBusinessScope ? this.$refs.supBusinessScope.getData() : []
        if (classList.length == 0) {
          this.$message.error('许可/经营范围未勾选！')
          callback && callback(false)
        } else {
          this.checkTab4(callback)
        }
      }, time)
    },
    checkTab4(callback) {
      let time = 0
      if (this.activeTabKey != 4) {
        this.activeTabKey = 4
        time = 200
      }
      setTimeout(() => {
        if (this.$refs.supAuditKaiPiaoInformation) {
          this.$refs.supAuditKaiPiaoInformation.checkFrom((kpInfo) => {
            if (kpInfo) {
              callback && callback(true)
            } else {
              this.$message.error('开票信息-未填写完整！')
              callback && callback(false)
            }
          })
        } else {
          callback && callback(false)
        }
      }, time)
    },
    getData() {
      // let data = JSON.parse(JSON.stringify(this.model))
      let jyfwList = this.$refs.supBusinessScope ? this.$refs.supBusinessScope.getData() : this.model.Classifies
      let data = {
        Id: this.model.Id,
        Name: this.model.Name,
        Code: this.model.Code,
        Kind: this.model.Kind,
        KindStr: this.model.KindStr,
        LegalPerson: this.model.LegalPerson,
        EnterpriseCharge: this.model.EnterpriseCharge,
        QualityCharge: this.model.QualityCharge,
        RegAreaId: this.model.RegAreaId,
        RegAreaCode: this.model.RegAreaCode,
        RegAreaName: this.model.RegAreaName,
        RegAddress: this.model.RegAddress,
        StoreAddress: this.model.StoreAddress,
        LinkName: this.model.LinkName,
        LinkPhone: this.model.LinkPhone,
        Title: this.model.Title,
        TaxNumber: this.model.TaxNumber,
        Remarks: this.model.Remarks,
        QualificationVersion: this.model.QualificationVersion,
        SupplierInvoiceRecords: this.model.SupplierInvoiceRecords, //开票信息
        SupplierDelegateScopes: this.$refs.supEntrustScope
          ? this.$refs.supEntrustScope.getData()
          : this.model.SupplierDelegateScopes, //委托范围
        Classifies: this.getClassifiesList(jyfwList), //jyfwList.map((x) => x.ClassifyName), //经营范围
        ClassifyDetails: jyfwList, //经营范围
      }
      return data
    },
    getClassifiesList(list) {
      let data = []
      if (list && list.length > 0) {
        list.forEach((item) => {
          if (item.Classifies && item.Classifies.length > 0) {
            item.Classifies.forEach((x) => {
              data.push(x.ClassifyName)
            })
          }
        })
      }
      return data
    },
  },
}
</script>

<style scoped>

.card {
  height: 60vh;
  overflow: auto;
}
</style>
