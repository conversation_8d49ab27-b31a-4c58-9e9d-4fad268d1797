<!--
 * @Author: LP
 * @Description: 审核-基础信息和开票信息
 * @Date: 2023/06/12
-->
<template>
  <a-spin :spinning="loading">
    <!-- 编辑 -->
    <a-form-model ref="formBasic" :rules="rules" :model="model" v-if="isEdit && model">
      <!-- 企业信息 -->
      <a-row :gutter="10" v-if="opType == 1">
        <a-col :span="24">
          <span style="font-size: 16px; font-weight: 600">企业信息</span>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="供应商名称" prop="Name" :class="{ red: model.NameChanged }">
            <a-input
              placeholder="请输入"
              v-model="model.Name"
              style="width: 100%"
              :maxLength="80"
              @blur="onInputNameBlur"
              @focus="onInputNameFocus"
            />
            <!-- ，<a @click="onTipBtnClick">{{ tipInfo.btn }}</a> -->
            <span slot="help" class="cred" v-if="tipInfo">{{ tipInfo.text }}</span>
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="企业类别" prop="Type" :class="{ red: model.TypeChanged }">
            <EnumSingleChoiceView
              style="width: 100%"
              placeholder="请选择"
              dictCode="EnumSupplierType"
              v-model="model.Type"
              :disabled="true"
              @change="(val, txt, item) => (model.TypeStr = txt)"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="法人代表" prop="LegalPerson" :class="{ red: model.LegalPersonChanged }">
            <a-input placeholder="请输入" v-model="model.LegalPerson" style="width: 100%" :maxLength="30" />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="企业负责人" prop="EnterpriseCharge" :class="{ red: model.EnterpriseChargeChanged }">
            <a-input placeholder="请输入" v-model="model.EnterpriseCharge" style="width: 100%" :maxLength="20" />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="质量负责人" prop="QualityCharge" :class="{ red: model.QualityChargeChanged }">
            <a-input placeholder="请输入" v-model="model.QualityCharge" style="width: 100%" :maxLength="20" />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="企业类型" prop="Kind" :class="{ red: model.KindChanged }">
            <SingleChoiceView
              style="width: 100%"
              placeholder="请选择"
              :httpParams="{
                groupPY: 'qylx',
              }"
              httpHead="P36001"
              :dataKey="{ name: 'ItemValue', value: 'ItemValue' }"
              v-model="model.Kind"
              :Url="'/{v}/Global/GetListDictItem'"
              @change="(val, txt, item) => (model.KindStr = txt)"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="注册地址" prop="RegAddress" :class="{ red: model.RegAddressChanged }">
            <a-input placeholder="请输入" v-model="model.RegAddress" style="width: 100%" :maxLength="240" />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="仓库地址" prop="StoreAddress" :class="{ red: model.ShippingAddressChanged }">
            <a-input placeholder="请输入" v-model="model.StoreAddress" style="width: 100%" :maxLength="240" />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="区域分类（省市区）" prop="RegAreaCode" :class="{ red: model.RegAreaIdChanged }">
            <SelectAreaView
              style="width: 100%"
              ref="selectCityAreas"
              :selectCode="true"
              :defaultCodes="areaCodes"
              @change="(val, node) => onAreasChange(val, node, model, 'RegArea')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="联系人" prop="LinkName" :class="{ red: model.LinkNameChanged }">
            <a-input placeholder="请输入" v-model="model.LinkName" style="width: 100%" :maxLength="20" />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="联系电话" prop="LinkPhone" :class="{ red: model.LinkPhoneChanged }">
            <a-input placeholder="请输入" v-model="model.LinkPhone" style="width: 100%" :maxLength="11" />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="备注" prop="Remarks">
            <a-input
              placeholder="请输入"
              v-model="model.Remarks"
              style="width: 100%"
              @change="$forceUpdate()"
              :maxLength="100"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- 开票信息 -->
      <a-row :gutter="10" v-else-if="opType == 4">
        <a-col :span="24" class="mt10">
          <span style="font-size: 16px; font-weight: 600">开票信息</span>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="纳税人名称" prop="Title" :class="{ red: model.TitleChanged }">
            <a-input
              placeholder="请输入"
              v-model="model.Title"
              style="width: 100%"
              :maxLength="80"
              @change="$forceUpdate()"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="纳税人识别号" prop="TaxNumber" :class="{ red: model.TaxNumberChanged }">
            <a-input
              placeholder="请输入"
              v-model="model.TaxNumber"
              style="width: 100%"
              :maxLength="30"
              @change="$forceUpdate()"
            />
          </a-form-model-item>
        </a-col>
        <template v-if="(model.SupplierInvoiceRecords || []).length > 0">
          <a-col :span="span">
            <a-form-model-item label="开户人" prop="BankAccount" :class="{ red: model.BankAccountChanged }">
              <a-input
                placeholder="请输入"
                v-model="model.SupplierInvoiceRecords[0].BankAccount"
                style="width: 100%"
                :maxLength="80"
              />
            </a-form-model-item>
          </a-col>
          <template v-for="(bank, index) in model.SupplierInvoiceRecords">
            <a-col :span="span" :key="'BankName' + index">
              <a-form-model-item
                :label="'开户银行' + (index + 1)"
                :prop="'BankName' + (index + 1)"
                :class="{ red: bank.BankNameChanged }"
              >
                <a-input placeholder="请输入" v-model="bank.BankName" style="width: 100%" :maxLength="60" />
              </a-form-model-item>
            </a-col>
            <a-col :span="span" :key="'BankCardNumber' + index">
              <a-form-model-item
                :label="'银行账号' + (index + 1)"
                :prop="'BankCardNumber' + (index + 1)"
                :class="{ red: bank.BankCardNumberChanged }"
              >
                <a-input placeholder="请输入" v-model="bank.BankCardNumber" style="width: 100%" :maxLength="50" />
              </a-form-model-item>
            </a-col>
          </template>
        </template>
      </a-row>
    </a-form-model>
    <!-- 详情展示部分 -->
    <a-card :bordered="false" v-else>
      <a-descriptions title="企业信息" :column="1" v-if="opType == 1">
        <a-descriptions-item label="供应商名称">{{ model.Name || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="企业类别">{{ model.TypeStr || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="法人代表">{{ model.LegalPerson || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="企业负责人">{{ model.EnterpriseCharge || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="质量负责人">{{ model.QualityCharge || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="企业类型">{{ model.KindStr || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label=" 注册地址">{{ model.RegAddress || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="仓库地址">{{ model.StoreAddress || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="区域分类">{{ model.RegAreaName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="联系人">{{ model.LinkName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="联系电话">{{ model.LinkPhone || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="备注">{{
          model.QualificationVersion ? model.QualificationVersion.Remarks || ' -- ' : '--'
        }}</a-descriptions-item>
      </a-descriptions>
      <a-descriptions title="开票信息" :column="1" v-else-if="opType == 4">
        <a-descriptions-item label="纳税人名称">{{ model.Title || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="纳税人识别号">{{ model.TaxNumber || ' -- ' }}</a-descriptions-item>
        <template v-if="model.SupplierInvoiceRecords && (model.SupplierInvoiceRecords || []).length > 0">
          <a-descriptions-item label="开户人">{{
            model.SupplierInvoiceRecords[0].BankAccount || ' -- '
          }}</a-descriptions-item>
          <template v-for="(bank, index) in model.SupplierInvoiceRecords">
            <a-descriptions-item :label="'开户银行' + (index + 1)" :key="'BankName' + index">{{
              bank.BankName || ' -- '
            }}</a-descriptions-item>
            <a-descriptions-item :label="'银行账号' + (index + 1)" :key="'BankCardNumber' + index">{{
              bank.BankCardNumber || ' -- '
            }}</a-descriptions-item>
          </template>
        </template>
      </a-descriptions>
    </a-card>
  </a-spin>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'SupAuditBasicInformation',
  mixins: [EditMixin],
  components: {},
  props: {
    /**
     * 类型 1基础信息 4开票信息
     */
    opType: {
      type: Number,
      default: 1,
      required: true,
      validator: (s) => [1, 4].includes(s),
    },
    /**
     * 是否能编辑
     */
    isEdit: {
      type: Boolean,
      required: true,
      default: () => {
        return false
      },
    },
    /**
     * 数据model
     */
    model: {
      type: Object,
      required: true,
      default: () => {
        return {
          Remarks: '',
          RegAreaCode: '',
          SupplierInvoiceRecords: [],
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      span: 24,
      httpHead: 'P36003',
      tipInfo: null, //供应商名称查询结果提醒// type  = 1 存在 去查看  2 已建立客户档案 可导入
      url: {},
    }
  },
  computed: {
    rules() {
      return {
        Name: [{ required: true, validator:  (rule, value, callback) => {
          if (value) {
            if (!/^[\u4e00-\u9fa5()（）-]+$/.test(value)) {
              callback('只能输入中文')
            } else {
              callback()
            }
          } else {
            callback('请输入')
          }
        }}],
        Remarks: [{ required: false, message: '请选择!' }],
        Kind: [{ required: true, message: '请选择!' }],
        LegalPerson: [{ required: true, validator: this.checkChinese }],
        EnterpriseCharge: [
          {
            required: false,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 20)
              })
            },
          },
        ],
        QualityCharge: [
          {
            required: false,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 20)
              })
            },
          },
        ],
        Type: [{ required: true, message: '请选择!' }],
        RegAddress: [{ required: true, message: '请输入!' }],
        StoreAddress: [{ required: true, message: '请输入!' }],
        RegAreaCode: [{ required: true, message: '请选择!' }],
        LinkName: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 20) },
        ],
        LinkPhone: [{ required: true, validator: this.checkTelphone }],
        Title: [{ required: true, validator: (rule, value, callback) => {
          if (value) {
            if (!/^[\u4e00-\u9fa5()（）-]+$/.test(value)) {
              callback('只能输入中文')
            } else {
              callback()
            }
          } else {
            callback('请输入')
          }
        } }],
        TaxNumber: [{ required: true, message: '请输入!' }],
        BankAccount: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                this.model.SupplierInvoiceRecords &&
                this.model.SupplierInvoiceRecords.length > 0 &&
                !this.model.SupplierInvoiceRecords[0].BankAccount
              ) {
                callback(new Error('请输入!'))
              } else {
                // this.checkChinese(rule, this.model.SupplierInvoiceRecords[0].BankAccount, callback)
                if (this.model.SupplierInvoiceRecords[0].BankAccount) {
                  if (!/^[\u4e00-\u9fa5()（）-]+$/.test(this.model.SupplierInvoiceRecords[0].BankAccount)) {
                    callback('只能输入中文')
                  } else {
                    callback()
                  }
                } else {
                  callback('请输入')
                }
              }
            },
          },
        ],
        BankName1: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                this.model.SupplierInvoiceRecords &&
                this.model.SupplierInvoiceRecords.length > 0 &&
                !this.model.SupplierInvoiceRecords[0].BankName
              ) {
                callback(new Error('请输入!'))
              } else {
                callback()
              }
            },
          },
        ],
        BankCardNumber1: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                this.model.SupplierInvoiceRecords &&
                this.model.SupplierInvoiceRecords.length > 0 &&
                !this.model.SupplierInvoiceRecords[0].BankCardNumber
              ) {
                callback(new Error('请输入!'))
              } else {
                this.checkBankNo(rule, this.model.SupplierInvoiceRecords[0].BankCardNumber, callback)
              }
            },
          },
        ],
      }
    },
  },
  mounted() {
    if (this.model) {
      this.model.Type = String(this.model.Type)
      this.areaCodes = this.getDefaultValueByCode(this.model.RegAreaCode)
    }
  },
  created() {},
  methods: {
    /**
     * 验证整个表单
     * @param {回调 返回data非空  则验证通过 执行下一步操作 反之 表单验证未通过} callback = funcation(data)
     */
    checkFrom(callback) {
      this.$refs.formBasic.validate((err, values) => {
        if (err) {
          let oldModel = JSON.parse(JSON.stringify(this.model))
          let data = Object.assign(oldModel, values)
          callback && callback(data)
        } else {
          callback && callback()
        }
      })
    },
    /**
     * 供应商名称输入框获取焦点事件
     */
    onInputNameFocus() {
      this.tipInfo = null
    },
    /**
     * 供应商名称输入框失去焦点事件
     */
    onInputNameBlur() {
      this.$refs.formBasic.validateField(['Name'], (errMsg) => {
        if (!errMsg) {
          this.checkSupInfoByName()
        } else {
          this.tipInfo = null
        }
      })
    },
    /**
     * 更具供应商名称查询是否已存在和是否建立了客户档案
     */
    checkSupInfoByName() {
      let that = this
      getAction('/{v}/Supplier/GetSameIdByName', { name: this.model.Name }, 'P36003').then((res) => {
        if (res.IsSuccess) {
          if (res.Data) {
            that.tipInfo = {
              type: 1,
              text: '该供应商已存在',
              btn: '',
              id: '',
            }
          }
          //  else {
          //   getAction('/{v}/Customer/GetSameIdByName', { name: that.model.Name, id: res.Data }, 'P36002').then(
          //     result => {
          //       if (result.IsSuccess && result.Data) {
          //         that.tipInfo = {
          //           type: 2,
          //           text: '该供应商已建立客户档案，可直接导入信息',
          //           btn: '导入',
          //           id: result.Data
          //         }
          //       }
          //     }
          //   )
          // }
        }
      })
    },
    onTipBtnClick() {
      if (!this.tipInfo) {
        return
      }
      switch (this.tipInfo.type) {
        case 1: //去查看
          this.$router.push({
            path: 'SupplierProfileDetail',
            query: { id: this.tipInfo.id, opType: 4 },
          })
          break
        case 2:
          this.$message.success('导入数据')
          break
      }
    },
    checkFrom(callback) {
      this.$refs.formBasic.validate((err, values) => {
        if (err) {
          let oldModel = JSON.parse(JSON.stringify(this.model))
          let data = Object.assign(oldModel, values)
          callback && callback(data)
        } else {
          callback && callback()
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>

/deep/.ant-form-item-control {
  height: 34px;
}
.red {
  /deep/.ant-form-item-label > label {
    color: red;
  }
}
</style>
