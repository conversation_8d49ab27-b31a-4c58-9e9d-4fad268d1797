<!--
 * @Author: LP
 * @Description: 开票信息
 * @Date: 2023/06/12
-->
<template>
  <a-spin :spinning="loading">
    <a-form-model v-if="isEdit" ref="formInvic" :rules="rules" :model="model">
      <a-row :gutter="gutter">
        <a-col :span="24" class="mt10">
          <span style="font-size: 16px; font-weight: 600">开票信息</span>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="纳税人名称" prop="Title">
            <a-input placeholder="请输入" v-model="model.Title" style="width: 100%" :maxLength="80" />
          </a-form-model-item>
        </a-col>
        <a-col :span="span">
          <a-form-model-item label="纳税人识别号" prop="TaxNumber">
            <a-input placeholder="请输入" v-model="model.TaxNumber" style="width: 100%" :maxLength="30" />
          </a-form-model-item>
        </a-col>
        <template v-if="(model.SupplierInvoices || []).length > 0">
          <a-col :span="span">
            <a-form-model-item label="开户人" prop="BankAccount">
              <a-input
                placeholder="请输入"
                v-model="model.SupplierInvoices[0].BankAccount"
                style="width: 100%"
                :maxLength="80"
              />
            </a-form-model-item>
          </a-col>
          <template v-for="(bank, index) in model.SupplierInvoices">
            <a-col :span="span" :key="'BankName' + index">
              <a-form-model-item :label="'开户银行' + (index + 1)" :prop="'BankName' + (index + 1)">
                <a-input placeholder="请输入" v-model="bank.BankName" style="width: 100%" :maxLength="60" />
              </a-form-model-item>
            </a-col>
            <a-col :span="span" :key="'BankCardNumber' + index">
              <a-form-model-item :label="'银行账号' + (index + 1)" :prop="'BankCardNumber' + (index + 1)">
                <a-input placeholder="请输入" v-model="bank.BankCardNumber" style="width: 100%" :maxLength="50" />
              </a-form-model-item>
            </a-col>
          </template>
        </template>
      </a-row>
    </a-form-model>
    <!-- :column="3" -->
    <a-card :bordered="false" v-else>
      <a-descriptions title="开票信息">
        <a-descriptions-item label="纳税人名称">{{ model.Title || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="纳税人识别号">{{ model.TaxNumber || ' -- ' }}</a-descriptions-item>
        <template v-if="model.SupplierInvoices && (model.SupplierInvoices || []).length > 0">
          <a-descriptions-item label="开户人">{{
            model.SupplierInvoices[0].BankAccount || ' -- '
          }}</a-descriptions-item>
          <template v-for="(bank, index) in model.SupplierInvoices">
            <a-descriptions-item :key="'BankName' + index" :label="'开户银行' + (index + 1)">{{
              bank.BankName || ' -- '
            }}</a-descriptions-item>
            <a-descriptions-item :key="'BankCardNumber' + index" :label="'银行账号' + (index + 1)">{{
              bank.BankCardNumber || ' -- '
            }}</a-descriptions-item>
          </template>
        </template>
      </a-descriptions>
    </a-card>
  </a-spin>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { postAction } from '@/api/manage'
export default {
  name: 'SupInvoicingInformation',
  mixins: [EditMixin],
  components: {},
  props: {
    /**
     * 可否编辑
     */
    isEdit: {
      type: Boolean,
      default: false,
    },
    /**
     * 数据
     */
    model: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      loading: false,
      span: 8,
      httpHead: 'P36003',
      url: {
        edit: '/{v}/Supplier/UpdateSupplierInvoice',
      },
    }
  },
  computed: {
    rules() {
      return {
        Title: [{ required: true, message: '请输入!' }],
        TaxNumber: [{ required: true, message: '请输入!' }],
        BankAccount: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                this.model.SupplierInvoices &&
                this.model.SupplierInvoices.length > 0 &&
                !this.model.SupplierInvoices[0].BankAccount
              ) {
                callback(new Error('请输入!'))
              } else {
                this.checkChinese(rule, this.model.SupplierInvoices[0].BankAccount, callback)
              }
            },
          },
        ],
        BankName1: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                this.model.SupplierInvoices &&
                this.model.SupplierInvoices.length > 0 &&
                !this.model.SupplierInvoices[0].BankName
              ) {
                callback(new Error('请输入!'))
              } else {
                callback()
              }
            },
          },
        ],
        BankCardNumber1: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                this.model.SupplierInvoices &&
                this.model.SupplierInvoices.length > 0 &&
                !this.model.SupplierInvoices[0].BankCardNumber
              ) {
                callback(new Error('请输入!'))
              } else {
                this.checkBankNo(rule, this.model.SupplierInvoices[0].BankCardNumber, callback)
              }
            },
          },
        ],
      }
    },
  },
  mounted() {},
  created() {},
  methods: {
    /**
     * 修改-保存数据
     */
    editSaveData(callback) {
      let that = this
      this.$refs.formInvic.validate((err, values) => {
        if (err) {
          let oldModel = JSON.parse(JSON.stringify(this.model))
          let data = Object.assign(oldModel, values)
          let subModel = {
            SupplierRecordId: data.SupplierRecordId,
            Title: data.Title,
            TaxNumber: data.TaxNumber,
            SupplierInvoiceRecords: data.SupplierInvoices,
          }
          subModel.SupplierInvoiceRecords.forEach((x) => {
            if (x.BankName && x.BankCardNumber && !x.BankAccount) {
              x.BankAccount = subModel.SupplierInvoiceRecords[0].BankAccount
            }
          })
          that.loading = true
          postAction(that.url.edit, subModel, that.httpHead)
            .then((res) => {
              if (res.IsSuccess) {
                that.$message.success('操作成功！')
                callback && callback(true)
              } else {
                that.$message.error(res.Msg)
                callback && callback(false)
              }
            })
            .catch((e) => {
              callback && callback(false)
            })
            .finally(() => {
              that.loading = false
            })
        } else {
          callback && callback(false)
        }
      })
    },
  },
}
</script>
<style lang="less" scoped>

/deep/.ant-form-item-control {
  height: 34px;
}
</style>
