<!--
 * @Author: LP
 * @Description: 供应商审核组件
 * @Date: 2023/06/20
-->
<template>
  <div>
    <div>
      <a-row :gutter="[16, 6]">
        <a-col :span="5"></a-col>
        <a-col :span="14" style="text-align: center">
          <a-icon type="left-circle" @click="handleSwitch(1, model.LastId)" v-if="model.LastId && isEdit" />
          <span style="font-weight: bold; font-size: 18px; padding: 0px 20px">{{ modelName || '--' }}</span>
          <a-icon type="right-circle" @click="handleSwitch(2, model.NextId)" v-if="model.NextId && isEdit"
        /></a-col>
        <a-col :span="5" style="text-align: right">
          <a-button @click="handleGoBack" style="margin-left: 16px">返回</a-button>
        </a-col>
      </a-row>
      <a-divider style="margin: 5px 0 10px 0" />
      <a-row :gutter="[16, 6]">
        <a-col :span="16">
          <QualificationsListAudit
            ref="qualificationsListAudit"
            :groupList="groupList"
            :businessId="model.Id"
            :isEdit="isEdit && model.AuditStatus == 1"
            :showUploadAndDelete="isEdit && model.AuditStatus == 1"
          />
        </a-col>
        <a-col :span="8">
          <SupAuditInfoComponents
            ref="supAuditInfoComponents"
            :model="model"
            v-if="model"
            :isEdit="isEdit && model.AuditStatus == 1"
            :auditList="approval ? approval.ApprovalWorkFlowInstanceResults : []"
          />
        </a-col>
      </a-row>
      <a-affix :offset-bottom="10" class="affix" v-if="isEdit && showBtn && model && model.AuditStatus == 1">
        <YYLButton
          menuId="a342d17b-d515-484b-bf71-76edf6d39001"
          text="通过审核"
          type="primary"
          :loading="confirnLoading"
          @click="onPassClick"
        />
        <YYLButton
          menuId="76bf8726-56ac-472a-8aa3-c9c8ef006c9c"
          text="驳回审核"
          type="danger"
          :loading="confirnLoading"
          @click="onRejectClick"
        />
      </a-affix>
    </div>
    <!-- 审核弹框 -->
    <AuditRemarkModal ref="auditRemarkModal" @ok="saveAuditData" />
  </div>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { PublicMixin } from '@/mixins/PublicMixin'
import moment from 'moment'
import { postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'SupAuditOrInfoComponents',
  components: {
    JEllipsis,
  },
  mixins: [EditMixin, PublicMixin],
  props: {
    /**
     * 数据对象
     */
    model: {
      type: Object,
      default: () => {
        return {}
      },
    },
    /**
     * 审批记录-数据对象
     */
    approval: {
      type: Object,
      default: () => {
        return {}
      },
    },
    /**
     * 是否可编辑
     **/
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      confirnLoading: false,
      boxLoading: false,
      groupList: [],
      httpHead: 'P36003',
      showBtn: true,
      modelName: '',
      url: {
        audit: '/{v}/SupplierAudit/Audit',
      },
    }
  },
  watch: {
    approval(val) {
      this.checkLoginUserIsHasBtnPerssion()
    },
  },
  computed: {},
  created() {},
  mounted() {
    this.modelName = this.model.Name
    this.getGroupList()
  },
  methods: {
    moment,
    getGroupList() {
      if (this.model && this.model.QualificationVersion && this.model.QualificationVersion.QualificationGroups) {
        this.parsingGroupList(this.model.QualificationVersion.QualificationGroups, (list) => {
          this.groupList = list
        })
      }
    },

    /*
     * 返回列表
     */
    handleGoBack() {
      this.$emit('goBack', true)
    },

    handleSwitch(type, id) {
      this.$emit('onSwitch', type, id)
    },
    checkLoginUserIsHasBtnPerssion() {
      let bool = false
      let userId = this.getLoginUserId()
      let list = this.approval ? this.approval.ApprovalUserIds : []
      if (userId && list && list.length > 0) {
        let item = list.find((v) => v == userId)
        if (item) {
          bool = true
        }
      }
      this.showBtn = bool
      console.log('showBtn = ' + this.showBtn + ' loginUser = ' + userId)
    },
    /**
     * 通过
     */
    onPassClick() {
      if (!this.$refs.qualificationsListAudit || !this.$refs.supAuditInfoComponents) {
        this.$message.error('组件发生错误')
        return
      }
      if (this.$refs.qualificationsListAudit.checkData()) {
        //资料数据重新替换

        this.$refs.supAuditInfoComponents.checkData((bool) => {
          if (bool) {
            this.showAuditModel(1)
          }
        })
      }
    },
    /**
     * 保存数据
     * @param {*} params
     */
    savePageData(params, formData) {
      let that = this
      that.confirnLoading = true
      postAction(that.url.audit, params, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (that.$refs.auditRemarkModal) {
              if (that.$refs.auditRemarkModal) {
                that.$refs.auditRemarkModal.postAuditInstance(formData, (bool) => {
                  if (bool) {
                    that.onAuditModalOk()
                  }
                })
              }
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.confirnLoading = false
        })
    },
    /**
     * 驳回
     */
    onRejectClick() {
      this.showAuditModel(2)
    },

    /**
     * 显示审核弹窗
     * @param {*} type 1通过  2驳回
     */
    showAuditModel(type) {
      let that = this
      if (this.$refs.auditRemarkModal) {
        let param = {
          ApprovalWorkFlowInstanceId: that.approval ? that.approval.ApprovalWorkFlowInstanceId : '',
          IsHasSuperior: that.approval ? that.approval.IsHasSuperior : false,
        }
        console.log('param', param)
        this.$refs.auditRemarkModal.show(type, param)
      }
    },
    saveAuditData(formData) {
      if (formData) {
        let groupList = this.$refs.qualificationsListAudit.getData()
        let infoData = this.$refs.supAuditInfoComponents ? this.$refs.supAuditInfoComponents.getData() : null
        if (infoData) {
          infoData.QualificationVersion.Remarks = infoData.Remarks
          infoData.QualificationVersion.QualificationGroups = groupList
          infoData.AuditStatusStr = '审核通过'
          infoData.AuditStatus = 2

          // console.log('saveData = ', infoData)

          this.savePageData(infoData, formData)
        }
      } else {
        this.onAuditModalOk()
      }
    },
    /**
     * 审核弹窗回调
     */
    onAuditModalOk() {
      if (this.model.NextId) {
        this.$emit('onSwitch', 2, this.model.NextId)
      } else {
        if (this.model.LastId) {
          this.$emit('onSwitch', 1, this.model.LastId)
        } else {
          this.handleGoBack()
        }
      }
    },
  },
}
</script>
<style scoped>

</style>
