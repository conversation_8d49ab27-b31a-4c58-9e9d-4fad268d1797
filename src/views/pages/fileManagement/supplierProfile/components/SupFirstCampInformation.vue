<!--
 * @Author: LP
 * @Description: 首营信息
 * @Date: 2023/06/12
-->
<template>
  <a-spin :spinning="loading">
    <div class="rflex vcenter mb15" v-if="[3, 4].includes(opType)">
      <span>档案编号：</span>
      <a-input placeholder="" v-model="FileNo" style="width: 200px" :maxLength="30" :readOnly="true" />
      <a-button
        v-if="!FileNo && opType == 3"
        type="primary"
        class="ml15"
        @click="onShengChengBianHaoClick"
        :loading="fileNoLoading"
        >生成编号</a-button
      >
    </div>
    <FirstCampInformation
      ref="firstCampInformation"
      :isEdit="[1, 2, 3, 12].includes(opType)"
      :allList="dataList"
      :showRemark="opType != 12 && opType != 11"
      :remark="remark"
      :businessId="supId"
      @import="importData"
      addGroupBtnId="0ff1dfd3-fc1f-46cd-948c-7fc47dff3004"
    />
  </a-spin>
</template>

<script>
import moment from 'moment'
import { EditMixin } from '@/mixins/EditMixin'
import { PublicMixin } from '@/mixins/PublicMixin'
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'SupFirstCampInformation',
  mixins: [EditMixin, PublicMixin],
  props: {
    /**
     * //1 新增 2 采购编辑 3质管编辑 4详情   11 七彩认证详情 12七彩认证编辑
     */
    opType: {
      type: Number,
      default: 1,
      required: true,
      validator: (s) => [1, 2, 3, 4, 11, 12].includes(s),
    },
    /**
     * 供应商id
     */
    supId: {
      type: String,
      default: '',
    },
    /**
     * 备注
     */
    remark: {
      type: String,
      default: '',
    },
    /**
     * 已填资质项
     */
    hasList: {
      type: Array,
      default: () => {
        return []
      },
    },
    /**
     * 企业类型
     */
    supType: {
      type: String,
      default: () => {
        return 1
      },
    },
    /**
     * 档案编号
     */
    fileNo: {
      type: String,
      default: () => {
        return ''
      },
    },
  },
  data() {
    return {
      loading: false,
      model: null,
      dataList: [],
      httpHead: 'P36003',
      FileNo: '', //档案编号
      fileNoLoading: false,
      url: {
        dataUrl: '/{v}/Qualification/QualificationGroupsByDataType',
        add: '/{v}/Supplier/CreateSupplierQualificationRecord',
        edit: '',
        edit1: '/{v}/Supplier/CreateSupplierQualificationRecord',
        edit2: '/{v}/Supplier/CreateSupplierQualificationRecord',
        edit3: '/{v}/Supplier/UpdateSupplierQualificationByQuality',
        createAudit: '/{v}/ApprovalWorkFlowInstance/CreateApproval',
        createAndSaveNo: '/{v}/Supplier/SaveSupplierFileNo', //生成并保存档案编号
      },
    }
  },

  computed: {},
  mounted() {
    this.url.edit = this.url['edit' + this.opType]
    console.log('fileNo = ' + this.fileNo)
    this.FileNo = this.fileNo
    this.loadFirstCampData()
  },
  created() {},
  methods: {
    moment,
    /**
     * 获取基础数据
     */
    loadFirstCampData() {
      if (this.opType == 4) {
        this.parsingHasList(this.hasList, (list) => {
          this.dataList = list
        })
      }
      this.loading = true
      getAction(this.url.dataUrl, { dataType: this.supType == 2 ? 0 : 1 }, 'P36001')
        .then((res) => {
          if (res.IsSuccess) {
            this.parsingDataToBasicListData(
              (list) => {
                this.dataList = list
              },
              res.Data || [],
              this.hasList,
              [1, 2, 3, 12].includes(this.opType),
              [1, 2].includes(this.opType)
            )
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    /***
     * 新增保存数据
     * @param {是否提交审核} subAudit
     */
    addSaveData(subAudit, callback) {
      if (!this.$refs.firstCampInformation) {
        callback && callback(false)
        return
      }
      let data = this.$refs.firstCampInformation.getFirstCampSubData(subAudit)
      if (data) {
        let that = this
        let subModel = {
          SupplierRecordId: this.supId,
          Remarks: data.remark,
          IsSubmitAudit: subAudit,
          QualificationGroups: data.list || [],
        }

        that.loading = true
        postAction(that.url.add, subModel, that.httpHead)
          .then((res) => {
            if (res.IsSuccess) {
              if (subAudit) {
                this.createApproval(
                  {
                    BussinessNo: res.Data,
                    Scenes: 1,
                    OpratorId: that.getLoginUserId(),
                    Remark: data.remark,
                    MainBussinessNo: that.supId,
                  },
                  callback
                )
              } else {
                that.$message.success('操作成功！')
                callback && callback(true)
              }
            } else {
              that.$message.error(res.Msg)
              callback && callback(false)
            }
          })
          .catch((e) => {
            that.loading = false
          })
          .finally(() => {
            that.loading = false
          })
      } else {
        callback && callback(false)
      }
    },
    /**
     * 编辑数据
     * @param {*} subAudit  是否提交审核
     * @param {*} callback  回调
     * @param {*} auditId   提交审核需要用的一个id
     */
    editSaveData(subAudit, callback, supRecoordId) {
      if (!this.$refs.firstCampInformation || !this.url.edit) {
        callback && callback(false)
        return
      }
      let checkData = subAudit
      if (subAudit == false && this.opType == 3) {
        checkData = true
      }
      let data = this.$refs.firstCampInformation.getFirstCampSubData(checkData)
      if (data) {
        let that = this
        let subModel = {
          SupplierRecordId: this.opType == 3 ? supRecoordId : this.supId,
          Remarks: data.remark,
          IsSubmitAudit: subAudit,
          QualificationGroups: data.list || [],
        }

        that.loading = true
        postAction(that.url.edit, subModel, that.httpHead)
          .then((res) => {
            if (res.IsSuccess) {
              if (subAudit) {
                that.createApproval(
                  {
                    BussinessNo: res.Data,
                    Scenes: 1,
                    OpratorId: that.getLoginUserId(),
                    Remark: data.remark,
                    MainBussinessNo: that.supId,
                  },
                  callback
                )
              } else {
                that.$message.success('操作成功！')
                callback && callback(true)
              }
            } else {
              that.$message.error(res.Msg)
              callback && callback(false)
            }
          })
          .catch((e) => {
            that.loading = false
            callback && callback(false)
          })
          .finally(() => {
            that.loading = false
          })
      } else {
        callback && callback(false)
      }
    },
    /**
     * 创建审核实例
     * @param {*} callback
     */
    createApproval(params, callback) {
      let that = this
      that.loading = true
      postAction(that.url.createAudit, params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            callback && callback(true)
          } else {
            that.$message.error(res.Msg)
            callback && callback(false)
          }
        })
        .catch((e) => {
          that.loading = false
        })
        .finally(() => {
          that.loading = false
        })
    },

    /**
     * 回去保存数据
     * @param {*} fileKey
     */
    getSaveData() {
      let data = null
      if (this.$refs.firstCampInformation) {
        data = this.$refs.firstCampInformation.getFirstCampSubData()
      }
      return data
    },
    /**
     * 导入数据
     * @param {*} id
     */
    importData(id) {
      let that = this
      that.loading = true
      getAction('/{v}/Customer/DetailByPurchase', { id: id }, 'P36002')
        .then((res) => {
          if (res.IsSuccess) {
            if (
              res.Data &&
              res.Data.CustomerQualificationVersion &&
              res.Data.CustomerQualificationVersion.QualificationGroups
            ) {
              let customerQList = res.Data.CustomerQualificationVersion.QualificationGroups
              that.parsingDataToBasicListData(
                (list) => {
                  this.dataList = list
                },
                [].concat(this.dataList),
                customerQList,
                [1, 2, 3, 12].includes(this.opType),
                [1, 2].includes(this.opType)
              )
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 生成编号点击事件
     */
    onShengChengBianHaoClick() {
      let that = this
      that.fileNoLoading = true
      this.$confirm({
        title: '生成编号',
        content: '档案编号生成后不可再次生成，是否确认生成？',
        // showCancel: false,
        onOk: () => {
          that.createAndSaveNo()
        },
        onCancel: () => {
          that.fileNoLoading = false
        },
      })
    },
    /**
     * 生成编号接口调用
     */
    createAndSaveNo() {
      let that = this
      getAction(that.url.createAndSaveNo, { supplierId: that.supId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.FileNo = res.Data || ''
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.fileNoLoading = false
        })
    },
  },
}
</script>

<style></style>
