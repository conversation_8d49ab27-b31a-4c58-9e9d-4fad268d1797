<!--
 * @Author: LP
 * @Description: 许可/经营范围--（已废弃不再使用-之前是客户审核部分在使用）
 * @Date: 2023/06/13
-->
<template>
  <a-spin :spinning="loading">
    <div style="font-size: 16px; margin-bottom: 10px">
      {{ model.Type == 1 ? '经营范围' : '许可范围' }}
    </div>
    <template v-if="isEdit">
      <template v-if="!isAudit">
        <a-card
          v-for="(group, index) in dataList.filter((x) => {
            return x.ManageClassifies && x.ManageClassifies.length > 0
          })"
          :key="index"
          :title="group.GroupName"
          class="mb15 mt15"
        >
          <div slot="extra">
            <a-checkbox
              :ref="'group' + index"
              :checked="group.checkAll"
              @change="(e) => onCheckAllChange(e, group, index)"
              v-if="isEdit"
            >
              全选
            </a-checkbox>
          </div>
          <a-checkbox-group
            v-model="group.checkedList"
            @change="(e) => onCheckBoxGroupChange(e, index)"
            style="width: 100%"
          >
            <a-row>
              <a-col :span="span" v-for="(item, j) in group.ManageClassifies" :key="j">
                <a-checkbox :value="item">
                  {{ item }}
                </a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </a-card>
      </template>
      <template v-else>
        <a-tabs class="pane-box" v-model="curtabKey">
          <a-tab-pane
            v-for="(group, index) in dataList.filter((x) => {
              return x.ManageClassifies && x.ManageClassifies.length > 0
            })"
            :key="index"
            :tab="group.GroupName"
          >
            <a-card>
              <div slot="title" style="padding-right: 20px">
                <a-input placeholder="请输入" v-model="group.filterKey" />
              </div>
              <a-checkbox
                slot="extra"
                :ref="'group' + index"
                :checked="group.checkAll"
                @change="(e) => onCheckAllChange(e, group, index)"
                v-if="isEdit"
              >
                全选
              </a-checkbox>
              <a-checkbox-group
                v-model="group.checkedList"
                @change="(e) => onCheckBoxGroupChange(e, index)"
                style="width: 100%"
              >
                <a-row>
                  <!-- <a-col :span="span" v-for="(item,i) in group.ManageClassifies.filter(x => search(x, group))" :key="i">
                    <a-checkbox :value="item" :key='i'>
                      {{ item }}
                    </a-checkbox>
                  </a-col> -->
                  <template v-for="(item, i) in group.ManageClassifies">
                    <a-col :span="span" v-show="search(item, group)" :key="i">
                      <a-checkbox :value="item" :key="i">
                        {{ item }}
                      </a-checkbox>
                    </a-col>
                  </template>
                </a-row>
              </a-checkbox-group>
            </a-card>
          </a-tab-pane>
        </a-tabs>
      </template>
    </template>
    <template v-else>
      <!-- 供应商 -->
      <a-card v-if="model && model[scopeKey] && model[scopeKey].length > 0">
        <a-row>
          <a-col :span="span" v-for="(item, j) in model[scopeKey]" :key="j">
            {{ item }}
          </a-col>
        </a-row>
      </a-card>
      <template v-else><a-empty /></template>
    </template>
  </a-spin>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'SupBusinessScopeOld',
  mixins: [],
  components: {},
  props: {
    /**
     * 是否是审核
     */
    isAudit: {
      type: Boolean,
      default: false,
    },
    /**
     * 可否编辑
     */
    isEdit: {
      type: Boolean,
      default: false,
    },
    /**
     * 数据
     */
    model: {
      type: Object,
      default: () => {
        return {}
      },
    },
    span: {
      type: Number,
      default: 8,
    },
    /**
     * 是否是客户
     */
    isCustomer: {
      type: Boolean,
      default: false,
    },
    /**
     * 经营返回接口返回model中的属性 关键字
     */
    scopeKey: {
      type: String,
      default: 'Classifies',
    },
  },
  data() {
    return {
      loading: false,
      dataList: [],
      httpHead: 'P36003',
      curtabKey: 0,
      url: {
        list: '/{v}/Global/GetManageClassifies',
        edit: '/{v}/Supplier/UpdateSupplierClassify',
      },
    }
  },
  watch: {},
  created() {},
  mounted() {
    if (this.isEdit) {
      this.getList()
    }
  },
  methods: {
    /**
     * @description: 获取所有经营范围
     * @param {*}
     * @return {*}
     */
    getList() {
      this.loading = true
      getAction(
        this.url.list,
        {
          manageClassifyType: this.isCustomer ? 1 : this.model.Type,
        },
        'P36001'
      )
        .then((res) => {
          if (res.IsSuccess && res.Data) {
            res.Data.forEach((group) => {
              if ((group.ManageClassifies || []).length) {
                group.checkAll = false
                group.checkedList = []
                group.filterKey = ''
                //循环内容
                group.ManageClassifies.forEach((child) => {
                  //具体这个选项是否在已选择数据中
                  if (this.model[this.scopeKey]) {
                    let index = this.model[this.scopeKey].findIndex((x) => {
                      return x == child
                    })
                    if (index > -1) {
                      group.checkedList.push(child)
                    }
                  }
                })
              }
            })
            this.dataList = res.Data || []
            console.log('dataList', this.dataList)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    onCheckBoxGroupChange(selectedList, index) {
      this.dataList[index].checkedList = selectedList
      this.$emit('check', this.dataList)
    },
    /**
     * @description: 全选
     * @param {*} e
     * @param {*} item
     * @return {*}
     */
    onCheckAllChange(e, group, index) {
      let checked = e.target.checked
      this.dataList[index].checkAll = e.target.checked
      if (checked) {
        this.dataList[index].checkedList = [].concat(group.ManageClassifies)
      } else {
        this.dataList[index].checkedList = []
      }

      this.$emit('check', this.dataList)
    },
    search(x, group) {
      if (group.filterKey) {
        let filterList = group.filterKey.split('')
        let xList = x.split('')
        let bool = false
        for (var i = 0; i < filterList.length; i++) {
          bool = xList.includes(filterList[i])
          if (bool) {
            break
          }
        }
        return bool
      } else {
        return true
      }
    },
    /**
     * 编辑数据
     * @param {*} callback
     */
    editSaveData(callback) {
      let that = this
      let subModel = {
        SupplierRecordId: this.model.SupplierRecordId,
        Classifies: [],
        ManageClassifyType: this.isCustomer ? 1 : this.model.Type,
      }
      that.dataList.forEach((group) => {
        if (group.checkedList && group.checkedList.length > 0) {
          subModel.Classifies = subModel.Classifies.concat(group.checkedList)
        }
      })

      that.loading = true
      postAction(that.url.edit, subModel, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            callback && callback(true)
          } else {
            that.$message.error(res.Msg)
            callback && callback(false)
          }
        })
        .catch((e) => {
          callback && callback(false)
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 获取选择的数据
     */
    getData() {
      let checkedList = []
      this.dataList.forEach((group) => {
        if (group.checkedList && group.checkedList.length > 0) {
          checkedList = checkedList.concat(group.checkedList)
        }
      })
      return checkedList
    },
  },
}
</script>
<style lang="less" scoped>

/deep/.ant-checkbox-group-item {
  display: inline-block;
  margin-right: 20px;
  margin-bottom: 20px;
}
.mb15 {
  margin-bottom: 15px;
}
.mt15 {
  margin-top: 15px;
}
/deep/.pane-box .ant-tabs-nav .ant-tabs-tab {
  margin: 0 15px 0 0;
}
</style>
