<!--
 * @Author: LP
 * @Description: 审核信息（流程历史）
 * @Date: 2023/06/12
-->
<template>
  <a-spin :spinning="loading">
    <a-timeline v-if="auditList && auditList.length > 0">
      <a-timeline-item color="blue" v-for="(group, index) in auditList" :key="index">
        <p v-for="(item, i) in group.LabelInfo || []" :key="i">{{ item.Label }}{{ item.Value }}</p>
      </a-timeline-item>
    </a-timeline>
    <template v-else>
      <a-empty />
    </template>
  </a-spin>
</template>

<script>
import { getAction } from '@/api/manage'
export default {
  name: 'SupAuditInformation',
  components: {},
  props: {
    /**
     * 接口参数
     */
    bussinessNo: {
      type: String,
      default: () => {
        return ''
      },
    },
    /**
     * 审核记录数据
     */
    aList: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      loading: false,
      auditList: null,
      httpHead: 'P36005',
      url: {
        list: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
      },
    }
  },
  computed: {
    rules() {
      return {}
    },
  },
  mounted() {
    this.loadAuditList()
  },
  created() {},
  methods: {
    loadAuditList() {
      let that = this
      if (that.aList && that.aList.length > 0) {
        this.auditList = [].concat(this.aList)
      } else {
        if (!this.bussinessNo) {
          return
        }
        that.loading = true
        getAction(that.url.list, { bussinessNo: this.bussinessNo }, this.httpHead)
          .then((res) => {
            if (res.IsSuccess) {
              that.auditList = res.Data.ApprovalWorkFlowInstanceResults || []
              this.$emit('loadAuditResData', res.Data)
            } else {
              that.$message.error(res.Msg)
            }
          })
          .finally(() => {
            that.loading = false
          })
      }
    },
  },
}
</script>

<style scoped>

</style>
