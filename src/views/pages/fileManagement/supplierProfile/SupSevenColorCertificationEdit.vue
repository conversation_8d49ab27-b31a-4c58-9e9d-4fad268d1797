<!--
 * @Author: LP
 * @Description: 供应商新七彩认证编辑界面
 * @Date: 2023/06/19
-->
<template>
  <div>
    <a-alert
      v-if="model && model.CaAuditOpinion"
      type="error"
      :message="'驳回原因：' + (model.CaAuditOpinion || '--')"
      banner
      :showIcon="false"
      style="margin-bottom: 5px"
    />
    <a-card>
      <template #title>
        <a-steps :current="curStep" class="w50-center">
          <a-step :title="'基本信息'" />
          <a-step title="首营信息" />
        </a-steps>
      </template>
      <SupBasicInformation ref="supBasicInformation" v-if="curStep === 0 && model" :opType="opType" :model="model" />

      <SupFirstCampInformation
        ref="supFirstCampInformation"
        v-else-if="curStep === 1 && model"
        :opType="opType"
        :supId="supId"
        :supType="model.Type"
        :hasList="qList"
      />

      <a-affix :offset-bottom="10" class="affix">
        <!-- <a-divider /> -->
        <a-button class="mr8" v-if="curStep > 0" @click="onBackClick">上一步</a-button>

        <template v-if="curStep === 0">
          <YYLButton
            menuId="e49e65e2-8623-4f3d-bc80-25a35b7422c2"
            text="下一步"
            type="primary"
            :loading="confirmLoading"
            @click="onNextStepClick"
          />
        </template>
        <template v-if="curStep > 0">
          <YYLButton
            menuId="912ec23a-59b9-48d1-b726-396877c15a79"
            text="保存并提交认证"
            type="primary"
            :loading="confirmLoading"
            @click="onSaveAndAuditClick"
          />
        </template>

        <a-button @click="goBack(true,true)">取消</a-button>
      </a-affix>
    </a-card>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'SupSevenColorCertificationEdit',
  mixins: [EditMixin],
  data() {
    return {
      curStep: 0,
      model: null,
      qList: [], //资质list
      loading: false,
      confirmLoading: false,
      supId: '', //供应商id
      opType: 12, //1 新增 2 采购编辑 3质管编辑 4详情   11 七彩认证详情 12七彩认证编辑  [1,2,3,4,11]此界面不用
      httpHead: 'P36004',
      url: {
        detail: '/{v}/Supplier/Detail',
        edit1: '/{v}/Supplier/UpdateSupplierInfo',
        edit2: '/{v}/Supplier/UpdateSupplierQualification',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.supId = this.$route.query.id || ''

      this.loadDetailInfo()
    }
  },
  created() {},
  methods: {
    /**
     * 获取供应商详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.supId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data.SupplierCaContrastQualificationVersion) {
              if (res.Data.SupplierCaContrastQualificationVersion.QualificationGroups) {
                that.qList = res.Data.SupplierCaContrastQualificationVersion.QualificationGroups
              }
            }

            that.model = res.Data || {}
            that.model.Type = String(res.Data.Type)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },

    /**
     * 上一步
     */
    onBackClick() {
      this.curStep = 0
    },
    /**
     * 下一步，需要验证表填必填是否填完整
     */
    onNextStepClick() {
      if (!this.$refs.supBasicInformation) {
        return
      }
      this.$refs.supBasicInformation.checkFrom((data) => {
        if (data) {
          let that = this
          this.confirmLoading = true
          let param = {
            Id: that.model.Id,
            Name: that.model.Name,
            RegAreaId: that.model.RegAreaId,
            RegAreaCode: that.model.RegAreaCode,
            RegAreaName: that.model.RegAreaName,
            RegAddress: that.model.RegAddress,
            LinkName: that.model.LinkName,
            LinkPhone: that.model.LinkPhone,
          }
          postAction(that.url.edit1, param, that.httpHead)
            .then((res) => {
              if (res.IsSuccess) {
                that.curStep = 1
              } else {
                that.$message.error(res.Msg)
              }
            })
            .catch((e) => {
              that.confirmLoading = false
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },

    onSaveAndAuditClick() {
      let data = this.$refs.supFirstCampInformation.getSaveData()
      if (data) {
        let that = this
        this.confirmLoading = true
        let param = {
          Id: that.model.Id,
          Remarks: that.model.SupplierCaContrastQualificationVersion
            ? that.model.SupplierCaContrastQualificationVersion.Remarks || ''
            : '',
          QualificationGroups: data.list,
          IsSubmit: true,
        }
        postAction(that.url.edit2, param, that.httpHead)
          .then((res) => {
            if (res.IsSuccess) {
              that.$message.success('操作成功！')
              that.cancel()
            } else {
              that.$message.error(res.Msg)
            }
          })
          .catch((e) => {
            that.confirmLoading = false
          })
          .finally(() => {
            that.confirmLoading = false
          })
      }
    },

    cancel() {
      this.goBack(true,true)
    },
  },
}
</script>

<style lang="less" scoped>

/deep/.ant-alert-message {
  color: red;
}
</style>
