<!--
 * @Author: LP
 * @Description: 供应商审核和审核详情界面
 * @Date: 2023/06/20
-->
<template>
  <!-- :bodyStyle="{ padding: '10px 16px 5px 16px' }" -->
  <a-card :bodyStyle="{ padding: '10px 10px 0 10px' }">
    <a-spin :spinning="loading" style="width: 100%">
      <template v-if="!model">
        <a-empty v-if="!loading" />
      </template>
      <template v-else>
        <SupAuditOrInfoComponents
          v-if="model"
          ref="supAuditOrInfoComponents"
          :model="model"
          :approval="approvalModel"
          :isEdit="isAudit"
          :showUploadAndDelete="isAudit"
          @onSwitch="handleSwitch"
          @goBack="handleGoBack"
        />
      </template>
    </a-spin>
  </a-card>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { getAction } from '@/api/manage'
export default {
  name: 'SupplierAuditAndInfoPage',
  mixins: [EditMixin],
  components: {},
  data() {
    return {
      description: '供应商审核',
      supId: '',
      model: null,
      loading: false,
      isAudit: false, //是否是审核 false则表示是详情
      httpHead: 'P36003',
      approvalModel: null,
      url: {
        detail: '/{v}/SupplierAudit/Detail',
        approval: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
      },
    }
  },
  mounted() {
    if (this.$route.query) {
      this.supId = this.$route.query.id
      if (this.$route.query.isAudit) {
        this.isAudit = Number(this.$route.query.isAudit) == 1
      }
      this.setTitle('供应商审核' + (this.isAudit ? '' : '详情'))
      console.log('isAudit', this.isAudit)
      this.getDetailInfo()
    }
  },
  computed: {},
  created() {},
  methods: {
    initAuditData() {
      let that = this
      if (that.isAudit) {
        //如果已被占用 提醒 并关闭界面
        if (that.model.NowAuditBy && that.model.NowAuditBy != that.getLoginUserId()) {
          that.$message.warning(
            '当前供应商已被审核人' + (that.model.NowAuditByName ? '[' + that.model.NowAuditByName + ']' : '') + '占用'
          )
          that.handleGoBack()
        } else {
          this.getApprovalResults()
        }
      }
    },
    /**
     * @description: 获取详情
     * @param {*}
     * @return {*}
     */
    getDetailInfo() {
      if (!this.supId) {
        return
      }
      if (this.model) {
        this.model = null
      }
      let that = this
      this.loading = true
      getAction(this.url.detail, { id: this.supId, isOccupy: this.isAudit }, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            if (that.model.QualificationVersion) {
              this.model.Remarks = this.model.QualificationVersion.Remarks
            }
            that.initAuditData()
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    /**
     * 获取审批记录
     */
    getApprovalResults() {
      if (!this.model) {
        return
      }
      this.loading = true
      getAction(this.url.approval, { bussinessNo: this.model.AuditId }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            this.approvalModel = res.Data || {}
            console.log('appr', this.approvalModel)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },

    handleGoBack(del) {
      if (del) {
        this.deleteAuditOccupy()
      }
      this.goBack(true,true)
    },
    /*
     * 数据切换
     * type = 1 上一条，type = 2 下一条
     */
    handleSwitch(type, id) {
      this.deleteAuditOccupy()
      if (id) {
        this.supId = id
        // 获取详情
        this.getDetailInfo()
      } else {
        this.handleGoBack()
      }
    },
    /**
     * 删除占用
     */
    deleteAuditOccupy() {
      getAction('/{v}/SupplierAudit/DeleteAuditOccupy', { id: this.model.Id }, this.httpHead).then((res) => {
        if (res.IsSuccess && res.Data) {
          console.log('释放单个占用成功')
        } else {
          console.log('释放单个占用失败' + res.Msg)
        }
      })
    },
  },
}
</script>
<style></style>
