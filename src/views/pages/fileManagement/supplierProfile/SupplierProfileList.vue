<!--
 * @Author: LP
 * @Description: 供应商列表
 * @Date: 2023/06/12
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
      @onSetValid="(item, chceked) => handleSetValid(item, chceked, 'GET', 'recordId')"
      @onSelectChange="onSelectChange"
    />

    <a-modal
      title="导出中"
      :visible="visible"
      @cancel="() => visible = false"
      :footer="null"
      strokeColor="#108ee9"
      :strokeWidth="10"
    >
      <div style="text-align: center">
        <a-progress type="circle" :percent="percent" />
        <p style="margin-top: 10px">正在导出，请勿关闭弹窗，否则将中止操作</p>
      </div>
    </a-modal>
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'SupplierProfileList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'Key',
        },
        // {
        //   name: '企业类别',
        //   type: SEnum.RADIO,
        //   httpHead: 'P36001',
        //   url: '/{v}/Global/GetListDictItem',
        //   key: 'Kind',
        //   searchKey: 'brandName',
        //   params: { groupPY: 'kehulb' },
        //   dataKey: { name: 'ItemValue', value: 'ItemPym' }
        // },
        {
          name: '企业类别',
          type: SEnum.ENUM,
          key: 'Type',
          dictCode: 'EnumSupplierType',
        },
        {
          name: '审核状态',
          type: SEnum.ENUM,
          key: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
        },
        {
          name: '首营状态',
          type: SEnum.ENUM,
          key: 'AuthBusiness',
          dictCode: 'EnumAuthBusiness',
        },
        {
          name: '同步状态',
          type: SEnum.ENUM,
          key: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        curStatus: '',
        tabStatusKey: '', //标签的切换关键字
        tabTitles: ['供应商列表'],
        operateBtns: [
          { name: '新增', type: 'primary', icon: 'plus', key: 'add', id: 'bca2c7e6-c3dd-4b9b-b965-d19fc3e6c630' },
          { name: '导出审批表', type: 'primary', icon: 'plus', key: 'export', id: '8d6ca273-f1c0-45bb-916e-552ed3de22d0' },
        ],
        selectType: 'checkbox',
        rowKey: 'Id'
      },
      url: {
        list: '/{v}/Supplier/GetSearchListSupplierRecord',
        setValidUrl: '/{v}/Supplier/SetIsActivity',
        delete: '/{v}/Supplier/DeleteSupplierRecord',
        ceateFile: '/{v}/Supplier/GetSupplierFirstOperatingEnterpriseApprovalFile',
        exportUrl: '/{v}/Supplier/GetSupplierFirstOperatingEnterpriseApprovalFileAll'
      },
      httpHead: 'P36003',
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'Name',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '供应商编号',
          dataIndex: 'Code',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '企业类别',
          dataIndex: 'TypeStr',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '注册地址',
          dataIndex: 'RegAddress',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '联系电话',
          dataIndex: 'LinkPhone',
          width: 120,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          // customRender: (t, r) => {
          //   const color = t ? 'green' : 'red'
          //   return <a-tag color={color}>{t ? '是' : '否'}</a-tag>
          // },
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '首营状态',
          dataIndex: 'AuthBusinessStr',
          width: 100,
          // customRender: (t, r) => {
          //   const color = t ? 'green' : 'red'
          //   return <a-tag color={color}>{t ? '是' : '否'}</a-tag>
          // },
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '是否活动',
          dataIndex: 'IsValid',
          width: 100,
          align: 'center',
          fixed: 'right',
          menuId: '5a7386fa-ad1e-45a1-994d-2e37aa16c197',
          scopedSlots: { customRender: 'isValid' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: 'b2e3391d-d83e-4329-95b6-4e4680f73ce7',
              isShow: (record) => {
                return true //record.AuditStatus == 1
              },
            },
            {
              name: '采购编辑',
              id: '5b08dc28-83de-4800-b5e2-3ecf50ae937a',
              isShow: (record) => {
                return record.AuditStatus != 1
              },
            },
            {
              name: '质管编辑',
              id: '1d36a538-9459-42f5-8e1f-ae1bf7b455b2',
              isShow: (record) => {
                return record.IsFormal && record.AuditStatus != 1
              },
            },
            {
              name: '删除',
              id: '70dd50df-1f64-41d6-8b8d-dc536bc64287',
              isShow: (record) => {
                //当审核状态为未提交或者审核失败、首营状态为未通过、同步状态为未同步显示删除功能
                return [0, 3].includes(record.AuditStatus) && record.AuthBusiness === 1 && record.PushERPStatus === 1
              },
            },
            {
              name: '首营企业审批表',
              id: 'a6cc25ad-8bc4-49c3-964c-4579cc30a853',
              isShow: (record) => {
                // return record.AuthBusiness === 2
                // 1.4.1需求更改
                // 审核中 审核通过需要显示按钮
                return [1,2].includes(record.AuditStatus)
              },
            },
          ],
        },
      ],
      visible: false,
      percent: 0,
      maxCount: 20
    }
  },
  computed: {},
  created() {},
  activated() {
    this.ExportMaxCount()
  },
  watch: {
    visible: {
      handler(newVal, oldVal) {
        if(newVal) {
          this.PDFEXport()
        }
      },
      immediate: true,//第一次声明了就监听
    }
  },
  methods: {
    onOperateClick(key) {
      if (key === 'add') {
        this.onDetailClick('SupplierProfileEdit', { id: '', opType: 1 })
      } else if (key === 'export') {
        if(!this.selectedRowKeys.length) {
          this.$message.warning('请先勾选供应商！')
          return
        } else if (this.selectedRowKeys.length > this.maxCount) {
          this.$message.warning(`一次性最多只能导出${this.maxCount}条！`)
        } else { 
          console.log('选中数据', this.selectionRows)
          // 判断是否有审批表
          let isHasAuditForm = false
          if(this.selectionRows.filter(item => item.AuditStatus === 1 || item.AuditStatus === 2).length > 0) {
            isHasAuditForm = true
          }
          if(!isHasAuditForm) {
            this.$message.warning('所选供应商没有可下载的审批表！')
          } else {
            this.visible = true
          }
        }
      }
    },
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('SupplierProfileDetail', { id: record.Id, opType: 4 })
      } else if (key === '采购编辑') {
        this.onDetailClick('SupplierProfileEdit', { id: record.Id, opType: 2 })
      } else if (key === '质管编辑') {
        this.onDetailClick('SupplierProfileDetail', { id: record.Id, opType: 3 })
      } else if (key === '删除') {
        this.handleDeleteClick('', 'POST', { SupplierRecordId: record.Id })
      } else if (key === '首营企业审批表') {
        if (record.FirstOperatingEnterpriseApprovalFile) {
          window.open(record.FirstOperatingEnterpriseApprovalFile)
        } else {
          let that = this
          that.loading = true
          getAction(this.url.ceateFile, { supplierRecordId: record.Id }, this.httpHead)
            .then((res) => {
              if (res.IsSuccess) {
                if (res.Data) {
                  window.open(res.Data)
                } else {
                  that.$message.warning('无数据')
                }
              } else {
                that.$message.error(res.msg)
              }
            })
            .finally(() => {
              that.loading = false
            })
        }
      }
    },
    ExportMaxCount(){
      getAction('/{v}/Supplier/MaxExportCount', {} ,this.httpHead).then(res => {
        if (res.IsSuccess) {
          this.maxCount = res.Data
        }
      })
    },
    PDFEXport() {
      this.percent = 50
      postAction(this.url.exportUrl, {SupplierIds:this.selectedRowKeys}, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          this.$message.success('下载成功')
          this.percent = 100
          window.open(res.Data, '_blank')
          this.visible = false
        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
  },
}
</script>
<style scoped lang="less">

/* 设置table行的高度防止错位 */
/deep/.ant-table-tbody tr {
  height: 61px;
}
</style>
