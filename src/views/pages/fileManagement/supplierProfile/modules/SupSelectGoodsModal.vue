<!--选择医贸商品-->
<template>
  <a-modal :title="title" :width="1100" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :destroyOnClose="true">
    <div>
      <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }" style="margin-bottom: 16px;">
        <div class="table-page-search-wrapper commodity-list">
          <a-form layout="inline">
            <a-row :gutter="10">
              <a-col :md="queryCol">
                <a-form-item label="商品" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="名称/拼音/编号" v-model="queryParam.KeyWord" />
                </a-form-item>
              </a-col>

              <a-col :md="queryCol">
                <a-form-item label="生产厂商" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入" v-model="queryParam.BrandManufacturer" :disabled="supName ? false : false" @change='changeBrandManufacturer' />
                </a-form-item>
              </a-col>

              <a-col :md="queryCol * 2">
                <span style="float: right; overflow: hidden; margin-bottom: 16px;" class="table-page-search-submitButtons">
                  <a-button @click="searchReset" icon="reload" style="margin-right: 8px;">重置</a-button>
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <!-- 数据列表 -->
      <a-table v-if="visible" :bordered="true" ref="table" rowKey="Id" size="middle" :columns="(columns || []).length ? columns : BaseColumns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" :rowSelection="{
          selectedRowKeys: selectedRowKeys,
          onChange: (sRowKeys, sRows) => {
            onSelectChange(sRowKeys, sRows, 'Id')
          },
          type: selectType
        }" @change="handleTableChange" :scroll="{ y: 300, x: '100%' }">
        <!-- 字符串超长截取省略号显示-->
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </span>
      </a-table>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel">取消</a-button>
      <a-button :disabled="confirmLoading" @click="handleOk" type="primary">确定</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'SupSelectGoodsModal',
  mixins: [ListMixin],
  components: { JEllipsis },
  props: {
    selectType: {
      type: String,
      default: 'checkbox'
    }
  },
  data() {
    return {
      title: '选择商品',
      visible: false,
      queryCol: 6,
      model: {},
      confirmLoading: false,
      httpHead: '',
      dataSource: [],
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 200,
          ellipsis: true
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        }
      ],
      httpHead: 'P36006',
      supName: '',
      index: -1,
      // 表头
      url: {
        listType: 'POST',
        list: ''
      }
    }
  },
  computed: {},
  mounted() { },
  created() { },
  methods: {
    show(name, index) {
      this.index = index
      this.supName = name
      this.url.list = '/{v}/GoodsManage/QueryGoodsAuditPasssList'
      this.loadData(1)
      this.visible = true
    },
    loadBefore() {
      if (this.supName) {
        this.$set(this.queryParam, 'BrandManufacturer', this.supName)
      }
      this.queryParam.AuthBusinessStatus = 2
      this.queryParam.IsValid = true
    },
    changeBrandManufacturer(e) {
      this.$set(this.queryParam, 'BrandManufacturer', e.target.value)
      this.supName = e.target.value
    },
    // 确定
    handleOk() {
      if (!(this.selectionRows || []).length) {
        this.$message.warning(`请选择数据！`)
        return
      }
      this.$emit('ok', this.selectType === 'radio' ? this.selectionRows[0] : this.selectionRows, this.index)
      this.handleCancel()
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.queryParam = {}
      this.onClearSelected()
      this.$emit('close')
      this.visible = false
      this.url.list = ''
    },
    searchReset() {
      this.queryParam = {}
      this.supName = ''
      this.onClearSelected()
      this.loadData(1)
    }
  }
}
</script>
<style lang="less">

</style>
