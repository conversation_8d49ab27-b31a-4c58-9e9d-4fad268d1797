<!--
 * @Author: LP
 * @Description: 选择证照 modal
 * @Date: 2023/06/14
-->
<template>
  <a-modal
    :title="title"
    :width="600"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    :destroyOnClose="true"
    :footer="null"
  >
    <SimpleTable ref="table" showTab :tab="tab" :columns="columns" @operate="onOperateClick" />
  </a-modal>
</template>

<script>
export default {
  name: 'ChooseLicenseModal',
  mixins: [],
  components: {},
  props: {
    allList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      title: '选择证照',
      visible: false,
      tab: {
        bordered: false, //是否显示表格的边框
        statusList: [],
        showIpagination: false
      },
      columns: [
        {
          title: '证照名称',
          dataIndex: 'GroupName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtn: [{ name: '选择' }]
        }
      ]
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show() {
      this.visible = true
      setTimeout(() => {
        this.setDataSource()
      }, 200)
    },
    setDataSource() {
      this.$refs.table.dataSource = this.allList.filter(x => {
        return !x.IsMust && !x.IsAdd && x.MaxCount >= 0
      })
    },
    onOperateClick(record, type) {
      if (type == '选择') {
        //选择的goup添加到非必填中
        let item = this.allList.find(x => {
          return x.GroupCode == record.GroupCode
        })

        //如果是可多次添加的
        if (record.MaxCount == 0) {
          //无线次数
          let newRecord = JSON.parse(JSON.stringify(record))
          delete newRecord.MaxCount
          newRecord.IsAdd = true
          this.allList.push(newRecord)
        } else {
          item.IsAdd = true
        }
        // let key = ''
        // if (record.GroupCode.startsWith('WTS')) {
        //   key = 'WTS'
        // } else if (record.GroupCode.startsWith('SpecialEntrustLetter')) {
        //   key = 'SpecialEntrustLetter'
        // }

        // if (key) {
        //   //看下 相同code
        //   let size = (this.allList.filter(g => g.GroupCode.startsWith(key)) || []).length
        //   let newRecord = JSON.parse(JSON.stringify(record))
        //   newRecord.GroupCode = key + (size + 1)
        //   newRecord.Qualifications.forEach(x => {
        //     x.QualificationCode = key + (size + 1)
        //   })
        //   newRecord.IsAdd = false
        //   this.allList.push(newRecord)
        // }
        //刷新界面
        this.setDataSource()
        this.$emit('ok')
        this.$message.success('新增成功')
        this.close()
      }
    },
    // 确定
    handleOk() {
      this.close()
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>

</style>
