<!-- 提交审核 -->
<template>
  <drag-modal :title="title" width="900px" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff'
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :gutter="gutter">
            <a-col :md="24" v-if="model.Type == 2">
              <a-form-model-item label="" prop="RejectType">
                <a-radio-group name="RejectType" v-model="model.RejectType">
                  <a-radio :value="2">
                    驳回到发起人
                  </a-radio>
                  <a-radio :value="1" v-if="model.IsHasSuperior">
                    驳回到上一级
                  </a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>
            <a-col :md="24">
              <a-form-model-item :label="model.Type == 1 ? (passFormTitle || '审核意见：') : (noPassFormTitle || '驳回原因：')" prop="Remark">
                <a-textarea placeholder="请输入" v-model="model.Remark" :auto-size="{ minRows: 3, maxRows: 5 }" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" :disabled="disableSubmit" :loading="confirmLoading" @click="handleOk" type="primary">提交</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
  </drag-modal>
</template>

<script>
import moment from 'moment'
import { getAction, putAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import DragModal from '@/components/DragModal'
export default {
  name: 'AuditRemarkModal',
  mixins: [EditMixin],
  components: { DragModal },
  props: {
    passTitle: {
      type: String,
      default: '',
    },
    noPassTitle: {
      type: String,
      default: '',
    },
    passFormTitle: {
      type: String,
      default: '',
    },
    noPassFormTitle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      title: '审核',
      visible: false,
      disableSubmit: false,
      model: {
        RejectType: 2,
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      url: {
        auditInstance: '/{v}/ApprovalWorkFlowInstance/AuditInstance',
      },
    }
  },
  computed: {
    rules() {
      return {
        Remark: [
          {
            required: this.model.Type == 1 ? false : true,
            validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 500),
          },
        ],
      }
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(type, data, remark) {
      console.log(data)
      if (type == 1) {
        this.title = this.passTitle ? this.passTitle : '通过审核'
      } else {
        this.title = this.noPassTitle ? this.noPassTitle : '驳回审核'
      }
      this.model = { Remark: remark ? remark : '', RejectType: 2, Type: type, ...data }
      this.visible = true
    },

    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          // that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          if (!formData['ApprovalWorkFlowInstanceId']) {
            this.$message.warning('请等待详情数据获取完毕在审核数据！')
            return
          }
          if (formData['Type'] == 1) this.$emit('ok', formData)
          // 先调业务逻辑上的审核接口  再调用 毛思越的审核接口
          // 不通过直接调用毛思越接口
          if (formData['Type'] == 2) this.postAuditInstance(formData)
          // that.close()
        }
      })
    },
    /**
     * @description: 审核实例
     * @param {*} Data['Type']  1 通过，2不通过
     * @return {*}
     */

    postAuditInstance(Data, call) {
      if (!Data['ApprovalWorkFlowInstanceId']) {
        this.$message.warning('审核流程出错！')
        return
      }
      let params = {
        AuditStatus: Data['Type'] == 1 ? 2 : Data['RejectType'] == 1 ? 1 : 3,
        // AuditStatusStr: 'string',
        // AuditTime: '2023-06-21T05:41:06.240Z',
        AuditOpinion: Data['Remark'],
        ApprovalWorkFlowInstanceId: Data['ApprovalWorkFlowInstanceId'],
      }
      if (Data['Type'] == 2) params.RejectType = Data['RejectType']
      this.confirmLoading = true
      postAction(this.url.auditInstance, params, 'P36005').then((res) => {
        this.confirmLoading = false
        if (!res.IsSuccess) {
          this.$message.error(res.Msg)
          call && call(false, res.ErrorCode)
          return
        }
        call && call(true, res.ErrorCode)
        if (res.ErrorCode === 6067) this.$message.error('操作失败！' + res.Msg); else this.$message.success('操作成功！')
        if (Data['Type'] == 2) this.$emit('ok')
        this.handleCancel()
      })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>
