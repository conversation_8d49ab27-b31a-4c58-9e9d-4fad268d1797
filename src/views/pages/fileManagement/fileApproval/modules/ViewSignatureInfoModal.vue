<!-- 查看签章资料-->
<template>
  <a-modal
    :title="title"
    :width="600"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    :destroyOnClose="true"
    :footer="null"
  >
    <SimpleTable ref="table" :tab="tab" :tableInfo="{ scrollX: 450 }" :columns="columns" @operate="onOperateClick" />
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'ViewSignatureInfoModal',
  mixins: [ListMixin],
  components: {},
  props: {
    allList: {
      type: Array,
      default: () => {
        return []
      },
    },
    nameKey: {
      type: String,
      default: 'QualificationName',
    },

    urlKey: {
      type: String,
      default: 'CertFileUrl',
    },
  },
  data() {
    return {
      title: '查看签章资料',
      visible: false,
      pageName: null,
      tab: {
        bordered: false, //是否显示表格的边框
        statusList: [],
        hideIpagination: true,
        scrollX: 450,
      },
      columns: [
        {
          title: '证照名称',
          dataIndex: this.nameKey,
          scopedSlots: { customRender: 'component' },
          width: 200,
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtn: [{ name: '查看' }],
        },
      ],
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(name) {
      this.visible = true
      this.pageName = name
      setTimeout(() => {
        this.setDataSource()
      }, 200)
    },
    setDataSource() {
      this.$refs.table.dataSource = this.allList
    },
    onOperateClick(record, type) {
      if (type == '查看') {
        // console.log(this.pageName)
        // console.log(record['Qualifications'].Urls)
        if (this.pageName == 'goods') {
          // 商品
          if ((record['Qualifications'] || []).length && (record['Qualifications'][0].FileIds || []).length) {
            record['Qualifications'][0].FileIds.map((v) => {
              this.getMoreFileTempUrls(record['Qualifications'][0].FileIds, (data) => {
                // console.log(data)
                if ((data || []).length) {
                  let openUrl = data[0].TempUrl || ''
                  if (openUrl) {
                    window.open(openUrl)
                  }
                }
              })
            })
          }
        } else {
          window.open(record[this.urlKey])
        }
      }
    },
    // 确定
    handleOk() {
      this.close()
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
  },
}
</script>
<style lang="less" scoped>

</style>
