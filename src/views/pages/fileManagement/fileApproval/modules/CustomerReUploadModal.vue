<!--
 重新上传图片
-->
<template>
  <a-modal
    title="上传图片"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <div>
      <template v-if="imageList">
        <div v-for="key in Object.keys(imageList)" class="upload-box" :key="key">
          <upload
            :bg="imageList[key]"
            @change="url => onUrlChange(url, key)"
            @clear="url => onClear(key)"
            :bName="BName"
            :showPicBtn="true"
            :dir="Dir"
            :isFormModel="false"
            :isFormModelDate="false"
            :isFormModelAddress="false"
          ></upload>
        </div>
      </template>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'CustomerReUploadModal',
  props: {},
  data() {
    return {
      visible: false,
      confirmLoading: false,
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
      imageList: null,
      fromType: ''
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(record, fromType) {
      this.visible = true
      this.fromType = fromType || ''
      this.imageList = JSON.parse(JSON.stringify(record))
    },
    /**
     * 资质上传change
     */
    onUrlChange(url, name) {
      if (typeof url == 'object') {
        this.imageList[name] = url.FileId
      } else {
        this.imageList[name] = url
      }
    },
    onClear(name) {
      this.imageList[name] = ''
    },
    // 确定
    handleOk() {
      // 存在空的图片
      const isSomeItemEmpty = Object.keys(this.imageList).some(item => !this.imageList[item])
      // 没有图片
      const isEveryEmpty = Object.keys(this.imageList).every(item => !this.imageList[item])
      /* if (isSomeItemEmpty && this.fromType !== 'realName') {
        this.$message.warning('请完整上传资质图片！')
        return
      } else if (isEveryEmpty && this.fromType === 'realName') {
        this.$message.warning('请至少上传一张图片！')
        return
      } */
      if (isEveryEmpty) {
        this.$message.warning('请至少上传一张图片！')
        return
      }
      // if(this.fromType === 'realName'){
      const imageKey = Object.keys(this.imageList).find(it => it.indexOf('2') == -1)
      // 图片1为空，图片2不为空，置换
        if(!this.imageList[imageKey] && this.imageList[imageKey + '2']){
          this.imageList[imageKey] = this.imageList[imageKey + '2']
          this.imageList[imageKey + '2'] = ''
        }
      // }
      this.$emit('ok', this.imageList)
      this.close()
    },

    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.confirmLoading = false
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
.upload-box {
  display: inline-block;
  margin-right: 30px;
  vertical-align: middle;
}
</style>
