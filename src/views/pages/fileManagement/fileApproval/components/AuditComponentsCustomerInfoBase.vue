<!-- 商品档案新增 基本信息 -->
<template>
  <div class="form-model-style">
    <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical" v-if="acType == 1">
      <!-- 商品信息 -->
      <a-form-model-item ref="Name" label="客户名称：" prop="Name" :class="{ 'label-c': model.NameChanged }">
        <a-input
          placeholder="请输入"
          v-model="model.Name"
          :maxLength="80"
          @blur="handleConfirmBlur"
          @focus="handleConfirmFocus"
        />
        <div class="errTip" v-if="isShow">该客户已存在</div>
      </a-form-model-item>
      <a-form-model-item ref="Type" label="企业类别：" prop="Type" :class="{ 'label-c': model.TypeChanged }">
        <SingleChoiceView
          placeholder="请选择"
          :httpParams="{
            groupPY: 'kehulb',
          }"
          :httpHead="'P36001'"
          :dataKey="{ name: 'ItemValue', value: 'Id' }"
          :Url="'/v1/Global/GetListDictItemForCustomer'"
          disabled
          v-model="model.Type"
          @change="(val, txt, item) => (model.TypeStr = txt)"
        />
      </a-form-model-item>
      <a-form-model-item
        ref="SecondType"
        label="企业二级分类："
        prop="SecondType"
        :class="{ 'label-c': model.SecondTypeChanged }"
      >
        <SingleChoiceView
          style="width: 100%"
          placeholder="请选择"
          :httpParams="{
            groupPY: 'kehufl',
          }"
          httpHead="P36001"
          :dataKey="{ name: 'ItemValue', value: 'Id' }"
          v-model="model.SecondType"
          :Url="'/v1/Global/GetListDictItemForCustomer'"
          disabled
          @change="(val, txt, item) => (model.SecondTypeStr = txt)"
        />
      </a-form-model-item>
      <a-form-model-item
        ref="LegalPerson"
        label="法人代表："
        prop="LegalPerson"
        :class="{ 'label-c': model.LegalPersonChanged }"
      >
        <a-input placeholder="请输入" v-model="model.LegalPerson" :maxLength="30" />
      </a-form-model-item>
      <a-form-model-item
        ref="EnterpriseCharge"
        label="企业负责人："
        :class="{ 'label-c': model.EnterpriseChargeChanged }"
      >
        <a-input placeholder="请输入" v-model="model.EnterpriseCharge" :maxLength="20" />
      </a-form-model-item>
      <a-form-model-item ref="QualityCharge" label="质量负责人：" :class="{ 'label-c': model.QualityChargeChanged }">
        <a-input placeholder="请输入" v-model="model.QualityCharge" :maxLength="20" />
      </a-form-model-item>
      <a-form-model-item
        ref="RegAddress"
        label="注册地址："
        prop="RegAddress"
        :class="{ 'label-c': model.RegAddressChanged }"
      >
        <a-input placeholder="请输入" v-model="model.RegAddress" :maxLength="255" />
      </a-form-model-item>
      <a-form-model-item
        ref="ShippingAddress"
        label="收货地址："
        prop="ShippingAddress"
        :class="{ 'label-c': model.ShippingAddressChanged }"
      >
        <a-input placeholder="请输入" v-model="model.ShippingAddress" :maxLength="255" />
      </a-form-model-item>
      <a-form-model-item label="区域分类（省市区）" prop="RegAreaCode" :class="{ 'label-c': model.RegAreaIdChanged }">
        <SelectAreaView
          style="width: 100%"
          ref="selectCityAreas"
          :defaultCodes="areaValue"
          @change="(val, node) => onAreasChange(val, node, model, 'RegArea', $refs.form)"
        />
      </a-form-model-item>
      <a-form-model-item ref="LinkName" label="联系人：" prop="LinkName" :class="{ 'label-c': model.LinkNameChanged }">
        <a-input placeholder="请输入" v-model="model.LinkName" :maxLength="20" />
      </a-form-model-item>
      <a-form-model-item
        ref="LinkPhone"
        label="联系电话："
        prop="LinkPhone"
        :class="{ 'label-c': model.LinkPhoneChanged }"
      >
        <a-input placeholder="请输入" v-model="model.LinkPhone" :maxLength="50" />
      </a-form-model-item>
      <a-form-model-item ref="Remark" label="备注：">
        <a-input placeholder="请输入" v-model="model.Remark" :maxLength="50" />
      </a-form-model-item>
    </a-form-model>
    <!-- 详情展示部分 -->
    <a-card :bordered="false" v-else-if="acType === 2">
      <a-descriptions title="企业信息" :column="1">
        <a-descriptions-item label="供应商名称">{{ model.Name || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="企业类别">{{ model.TypeStr || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="企业二级分类">{{ model.SecondTypeStr || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="法人代表">{{ model.LegalPerson || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="企业负责人">{{ model.EnterpriseCharge || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="质量负责人">{{ model.QualityCharge || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="注册地址">{{ model.RegAddress || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="收货地址">{{ model.ShippingAddress || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="区域分类">{{ model.RegAreaName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="联系人">{{ model.LinkName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label=" 联系电话">{{ model.LinkPhone || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
    </a-card>
  </div>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商品客户审核 基本信息',
  name: 'AuditComponentsCustomerInfoBase',
  mixins: [EditMixin],
  props: {
    model: {
      type: Object,
      default: undefined,
    },
    acType: {
      type: Number,
      default: 1,
      required: true,
      validator: (s) => [1, 2].includes(s),
    },
  },
  data() {
    return {
      md: 8,
      isShow: false,
      Id: '', //已存在的客户ID
      areaValue: '',
      oldName: '',
    }
  },
  computed: {
    rules() {
      return {
        Name: [{ required: true, validator:  (rule, value, callback) => {
          if (value) {
            if (!/^[\u4e00-\u9fa5()（）-]+$/.test(value)) {
              callback('只能输入中文')
            } else {
              callback()
            }
          } else {
            callback('请输入')
          }
        }}],
        Type: [{ required: true, message: '请选择!' }],
        SecondType: [{ required: true, message: '请选择!' }],
        LegalPerson: [{ required: true, validator: this.checkChinese }],
        RegAddress: [{ required: true, message: '请输入!' }],
        ShippingAddress: [{ required: true, message: '请输入!' }],
        RegAreaCode: [{ required: true, message: '请选择!' }],
        LinkName: [
          {
            required: true,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 20)
              })
            },
          },
        ],
        LinkPhone: [{ required: true, validator: this.checkTelphone }],
      }
    },
  },
  created() {},
  mounted() {
    if (this.model && this.model.Name) {
      this.areaValue = this.getSelectCityAreasDefaultValueByCode(this.model.RegAreaCode)
      if (this.model.QualificationVersion) {
        this.model.Remark = this.model.QualificationVersion.Remarks
      }

      this.oldName = this.model.Name
    }
  },
  methods: {
    checkFrom(callback) {
      if (this.isShow) {
        callback &&
          callback({
            Err: '该客户已存在',
          })
        return false
      }
      this.$refs.ruleForm.validate((err, values) => {
        if (err) {
          let oldModel = JSON.parse(JSON.stringify(this.model))
          let data = Object.assign(oldModel, values)
          callback && callback(data)
        } else {
          callback && callback()
        }
      })
    },
    handleConfirmBlur(e) {
      const value = e.target.value
      if (this.oldName == e.target.value) {
        return false
      }
      if (value) {
        if (!/^[\u4e00-\u9fa5]+$/.test(value)) {
          return
        }

        getAction(
          '/v1/Customer/GetSameIdByName',
          {
            name: value,
          },
          'P36002'
        ).then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.isShow = true
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
      }
    },
    handleConfirmFocus() {
      this.isShow = false
    },
  },
}
</script>

<style lang="less" scoped>

.errTip {
  color: #f5222d;
  font-size: 14px;
}
.go-page {
  padding-left: 6px;
  color: #1890ff;
}

/deep/.ant-form-item-control {
  height: 34px;
}

/deep/.label-c label {
  color: #f5222d;
}
</style>
