<!--
 * @Author: 姚惠英
 * @Description: 客户审核右边区域-客户信息
 * @Date: 2023/06/20
-->
<template>
  <a-tabs v-model="activeTabKey">
    <a-tab-pane :key="1" class="card">
      <span slot="tab" :style="{ color: 'rgba(0, 0, 0, 0.65)' }">基础信息</span>
      <a-descriptions title="基本信息" :column="1"></a-descriptions>
      <AuditComponentsCustomerInfoBase
        :acType="isEdit ? 1 : 2"
        :model="model"
        isShowInfo
        ref="auditComponentsCustomerInfoBase"
        v-show="activeTabKey == 1 && model"
      />
      <div v-if="model && model.AuditId">
        <a-descriptions title="审核信息" :column="1"></a-descriptions>
        <CusAuditInformation
          ref="CusAuditInformation"
          :approval="approval"
          v-if="approval && approval.ApprovalWorkFlowInstanceResults"
        />
      </div>
    </a-tab-pane>
    <a-tab-pane :key="2" tab="经营范围" class="card">
      <SupBusinessScope
        ref="cusBusinessScope"
        :isEdit="isEdit"
        v-if="model"
        :span="24"
        :isAudit="true"
        :model="model"
        :isCustomer="true"
        scopeKey="ManageClassifyTypeDto"
      />
    </a-tab-pane>
    <a-tab-pane :key="3" class="card">
      <span slot="tab" :style="{ color: model.InvoiceChanged ? 'red' : 'rgba(0, 0, 0, 0.65)' }">开票信息</span>
      <AuditComponentsCustomerInfo
        :acType="isEdit ? 1 : 2"
        :model="model"
        isShowInvoice
        ref="auditComponentsCustomerInfo"
        v-show="activeTabKey == 3 && model"
      />
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import { getAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'CusAuditInfoComponents',
  mixins: [EditMixin],
  props: {
    /**
     * 数据对象
     */
    model: {
      type: Object,
      required: true,
      default: () => {
        return {}
      },
    },
    /**
     * 审核数据
     */
    approval: {
      type: Object,
      default: () => {
        return {}
      },
    },
    /**
     * 是否可编辑
     **/
    isEdit: {
      type: Boolean,
      required: true,
      default: () => {
        return false
      },
    },
  },
  data() {
    return {
      activeTabKey: 1,
      confirmLoading: false,
      qList: [], //资质list
      loading: false,
      cusId: '', //客户id
      remark: '',
      httpHead: 'P36002',
      url: {
        detail: '',
      },
      classifies: [],
      classifyDetails: [],
    }
  },
  computed: {},
  mounted() {
    // if (this.model && this.model.Classifies) {
    //   this.classifies = this.model.Classifies
    // }
  },
  created() {},
  methods: {
    /**
     * 获取数据
     * @param {*} callback 返回布尔值 true 成功 false 失败
     */
    checkData(callback) {
      if (this.$refs.auditComponentsCustomerInfoBase) {
        this.$refs.auditComponentsCustomerInfoBase.checkFrom((basicInfo) => {
          if (basicInfo) {
            if (basicInfo.Err) {
              this.$message.error(basicInfo.Err)
              callback && callback(false)
              if (this.activeTabKey != 1) {
                this.activeTabKey = 1
              }
              return false
            }
            if (this.activeTabKey != 3) {
              this.activeTabKey = 3
            }
            setTimeout(() => {
              if (this.$refs.auditComponentsCustomerInfo) {
                this.$refs.auditComponentsCustomerInfo.checkFrom((kpInfo) => {
                  if (kpInfo) {
                    if (this.activeTabKey != 2) {
                      this.activeTabKey = 2
                    }
                    setTimeout(() => {
                      this.getClassifies()
                      if (this.classifies && this.classifies.length) {
                        callback && callback(true)
                      } else {
                        this.$message.error('请勾选经营范围！')
                        callback && callback(false)
                      }
                    }, 500)
                  } else {
                    this.$message.error('开票信息-未填写完整！')
                    callback && callback(false)
                  }
                })
              } else {
                callback && callback(false)
              }
            }, 200)
          } else {
            this.$message.error('基础信息-未填写完整！')
            this.activeTabKey = 1
            callback && callback(false)
          }
        })
      } else {
        if (this.$refs.supBusinessScope) {
          this.getClassifies()
          if (this.classifies && this.classifies.length) {
            if (this.activeTabKey != 1) {
              this.activeTabKey = 1
            }
            setTimeout(() => {
              this.$refs.auditComponentsCustomerInfoBase.checkFrom((basicInfo) => {
                if (basicInfo) {
                  if (basicInfo.Err) {
                    this.$message.error(basicInfo.Err)
                    callback && callback(false)
                    if (this.activeTabKey != 1) {
                      this.activeTabKey = 1
                    }
                    return false
                  }
                  if (this.activeTabKey != 3) {
                    this.activeTabKey = 3
                  }
                  setTimeout(() => {
                    if (this.$refs.auditComponentsCustomerInfo) {
                      this.$refs.auditComponentsCustomerInfo.checkFrom((kpInfo) => {
                        if (kpInfo) {
                          callback && callback(true)
                        } else {
                          this.$message.error('开票信息-未填写完整！')
                          callback && callback(false)
                        }
                      })
                    } else {
                      callback && callback(false)
                    }
                  }, 200)
                } else {
                  this.$message.error('基础信息-未填写完整！')
                  this.activeTabKey = 1
                  callback && callback(false)
                }
              })
            }, 200)
          } else {
            if (this.activeTabKey != 2) {
              this.activeTabKey = 2
            }
            this.$message.error('请勾选经营范围！')
            callback && callback(false)
          }
        } else {
          if (this.$refs.auditComponentsCustomerInfo) {
            this.$refs.auditComponentsCustomerInfo.checkFrom((kpInfo) => {
              if (kpInfo) {
                if (this.activeTabKey != 1) {
                  this.activeTabKey = 1
                }
                setTimeout(() => {
                  if (this.$refs.auditComponentsCustomerInfoBase) {
                    this.$refs.auditComponentsCustomerInfoBase.checkFrom((basicInfo) => {
                      if (basicInfo) {
                        if (basicInfo.Err) {
                          this.$message.error(basicInfo.Err)
                          callback && callback(false)
                          if (this.activeTabKey != 1) {
                            this.activeTabKey = 1
                          }
                          return false
                        }
                        this.getClassifies()
                        if (this.classifies && this.classifies.length) {
                          callback && callback(true)
                        } else {
                          if (this.activeTabKey != 2) {
                            this.activeTabKey = 2
                          }
                          this.$message.error('请勾选经营范围！')
                          callback && callback(false)
                        }
                      } else {
                        this.$message.error('基础信息-未填写完整！')
                        callback && callback(false)
                      }
                    })
                  } else {
                    callback && callback(false)
                  }
                }, 200)
              } else {
                this.$message.error('开票信息-未填写完整！')
                callback && callback(false)
              }
            })
          }
        }
      }
    },
    getData() {
      if (this.model.QualificationVersion) {
        this.model.QualificationVersion.Remarks = this.model.Remark || ''
      }
      let data = {
        Id: this.model.Id,
        Name: this.model.Name,
        AuditId: this.model.AuditId,
        Code: this.model.Code,
        Type: this.model.Type,
        TypeStr: this.model.TypeStr,
        SecondType: this.model.SecondType,
        SecondTypeStr: this.model.SecondTypeStr,
        LegalPerson: this.model.LegalPerson,
        EnterpriseCharge: this.model.EnterpriseCharge,
        QualityCharge: this.model.QualityCharge,
        RegAreaId: this.model.RegAreaId,
        RegAreaCode: this.model.RegAreaCode,
        RegAreaName: this.model.RegAreaName,
        RegAddress: this.model.RegAddress,
        ShippingAddress: this.model.ShippingAddress,
        LinkName: this.model.LinkName,
        LinkPhone: this.model.LinkPhone,
        Title: this.model.Title,
        TaxNumber: this.model.TaxNumber,
        BankAccount: this.model.BankAccount,
        BankName: this.model.BankName,
        BankCardNumber: this.model.BankCardNumber,
        RegPhone: this.model.RegPhone,
        Classifies: this.classifies,
        ClassifyDetails: this.classifyDetails,
        QualificationVersion: this.model.QualificationVersion,
        Email: this.model.Email,
      }
      return data
    },
    /**
     * 获取经营范围
     */
    getClassifies() {
      this.classifies = []
      if (this.$refs.cusBusinessScope) {
        this.classifyDetails = this.$refs.cusBusinessScope.getData()
        if (this.classifyDetails && this.classifyDetails.length > 0) {
          this.classifyDetails.forEach((item) => {
            if (item.Classifies && item.Classifies.length > 0) {
              item.Classifies.forEach((x) => {
                this.classifies.push(x.ClassifyName)
              })
            }
          })
        }
      }
      console.log('客户 classifies', this.classifies)
    },
    /**
     * 废弃
     * @param {*} arr
     */
    getScope(arr) {
      this.classifies = []
      if (arr && arr.length) {
        arr.forEach((e) => {
          if (e.checkedList && e.checkedList.length) {
            e.checkedList.forEach((e1) => {
              this.classifies.push(e1)
            })
          }
        })
      }
    },
  },
}
</script>

<style scoped>

.card {
  height: 60vh;
  overflow: auto;
}
</style>
