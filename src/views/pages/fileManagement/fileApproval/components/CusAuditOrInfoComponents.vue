<!--
 * @Author: 姚惠英
 * @Description: 客户审核组件
 * @Date: 2023/06/20
-->
<template>
  <a-spin :spinning="boxLoading">
    <div>
      <a-row :gutter="[16, 6]">
        <a-col :span="5"></a-col>
        <a-col :span="14" style="text-align: center">
          <a-icon type="left-circle" @click="handleSwitch(1, model.LastId)" v-if="model.LastId && isEdit" />
          <span style="font-weight: bold; font-size: 18px; padding: 0px 20px">{{ modelName || '--' }}</span>
          <a-icon type="right-circle" @click="handleSwitch(2, model.NextId)" v-if="model.NextId && isEdit"
        /></a-col>
        <a-col :span="5" style="text-align: right">
          <a-button @click="handleGoBack" style="margin-left: 16px">返回</a-button>
        </a-col>
      </a-row>
      <a-divider style="margin: 5px 0 10px 0" />
      <a-row :gutter="[16, 6]">
        <a-col :span="16">
          <QualificationsListAudit
            ref="qualificationsListAudit"
            :groupList="groupList"
            :isEdit="isEdit"
            :businessId="model.Id"
            :fromType="2"
            :showUploadAndDelete="isEdit"
          />
        </a-col>
        <a-col :span="8">
          <CusAuditInfoComponents
            ref="cusAuditInfoComponents"
            :approval="approval"
            :model="model"
            v-if="model"
            :isEdit="isEdit"
          />
        </a-col>
      </a-row>
    </div>
    <a-card v-if="showBtn" :bordered="false">
        <a-affix :offset-bottom="0" style="float: right; width: 100%; text-align: right" v-if="(model.NowAuditBy && userId && model.NowAuditBy == userId) || !model.NowAuditBy">
          <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
            <YYLButton
              :loading="confirnLoading"
              type="primary"
              menuId="db9b0104-f5ea-4267-a469-bcd355cb81fa"
              @click="onPassClick"
              text="通过审核"
              v-if="showBtn"
            ></YYLButton>
            <YYLButton
              type="danger"
              menuId="e8e6f4ff-3245-41df-bfce-5650d2fdab77"
              @click="onRejectClick"
              text="驳回审核"
              v-if="showBtn"
            ></YYLButton>
          </a-card>
        </a-affix>
      </a-card>
    <!-- 审核弹框 -->
    <AuditRemarkModal ref="auditRemarkModal" @ok="saveAuditData" />
  </a-spin>
</template>

<script>
// 引入api请求配置
import moment from 'moment'
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import { EditMixin } from '@/mixins/EditMixin'
import { PublicMixin } from '@/mixins/PublicMixin'
import { getAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'CusAuditOrInfoComponents',
  mixins: [EditMixin, PublicMixin],
  components: {
    JEllipsis,
  },
  props: {
    /**
     * 数据对象
     */
    model: {
      type: Object,
      default: () => {
        return {}
      },
    },
    /**
     * 是否可编辑
     **/
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      boxLoading: false,
      groupList: [],
      httpHead: 'P36002',
      url: {
        audit: '/v1/CustomerAudit/Audit',
        approval: '/v1/ApprovalWorkFlowInstance/GetApprovalResults',
      },
      showBtn: false,
      modelName: '',
      approval: undefined,
      confirnLoading: false,
      userId: Vue.ls.get(USER_ID),
    }
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {
    this.modelName = this.model.Name
    this.getGroupList()
    this.getApprovalResults()
  },
  methods: {
    moment,
    /**
     * 获取审批记录
     */
    getApprovalResults() {
      if (this.model && this.model.AuditId) {
        getAction(this.url.approval, { bussinessNo: this.model.AuditId }, 'P36005').then((res) => {
          if (res.IsSuccess) {
            this.approval = res.Data || {}
            this.checkLoginUserIsHasBtnPerssion()
          } else {
            this.$message.error(res.Msg)
          }
        })
      }
    },
    getGroupList() {
      if (this.model && this.model.QualificationVersion && this.model.QualificationVersion.QualificationGroups) {
        this.parsingGroupList(this.model.QualificationVersion.QualificationGroups, (list) => {
          this.groupList = list
        })
      }
    },
    checkLoginUserIsHasBtnPerssion() {
      let bool = false
      let userId = Vue.ls.get(USER_ID)
      let list = this.approval ? this.approval.ApprovalUserIds : []
      if (userId && list && list.length > 0) {
        bool = list.some((v) => v == userId)
      }
      this.showBtn = bool
    },

    /*
     * 返回列表
     */
    handleGoBack() {
      getAction('/v1/CustomerAudit/DeleteAuditOccupy', { id: this.model.Id }, 'P36002')
      this.$router.go(-1)
    },

    handleSwitch(type, id) {
      this.$emit('onSwitch', type, id)
    },
    /**
     * 通过
     */
    onPassClick() {
      if (!this.$refs.qualificationsListAudit || !this.$refs.cusAuditInfoComponents) {
        this.$message.error('组件发生错误')
        return
      }

      // if (this.$refs.qualificationsListAudit.checkData()) {
      //   //资料数据重新替换
      //   let qualifications = this.$refs.qualificationsListAudit.getData()
      //   this.$refs.cusAuditInfoComponents.checkData(bool => {
      //     if (bool) {
      //       let infoData = this.$refs.cusAuditInfoComponents.getData()
      //       infoData.QualificationVersion.QualificationGroups = qualifications
      //       infoData.AuditStatusStr = '审核通过'
      //       infoData.AuditStatus = 2
      //       this.savePageData(infoData)
      //     }
      //   })
      // }

      if (this.$refs.qualificationsListAudit.checkData()) {
        this.$refs.cusAuditInfoComponents.checkData((bool) => {
          if (bool) {
            this.showAuditModel(1)
          }
        })
      }
    },
    /**
     * 保存数据
     * @param {*} params
     */
    savePageData(params, formData) {
      let that = this
      that.confirnLoading = true
      params.Invoice = {
        Title: params.Title,
        TaxNumber: params.TaxNumber,
        BankAccount: params.BankAccount,
        BankName: params.BankName,
        BankCardNumber: params.BankCardNumber,
        RegPhone: params.RegPhone,
        Email: params.Email,
      }

      postAction(that.url.audit, params, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (that.$refs.auditRemarkModal) {
              if (that.$refs.auditRemarkModal) {
                that.$refs.auditRemarkModal.postAuditInstance(formData, (bool) => {
                  if (bool) {
                    that.onAuditModalOk()
                  }
                })
              }
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.confirnLoading = false
        })
    },
    /**
     * 驳回
     */
    onRejectClick() {
      this.showAuditModel(2)
    },

    /**
     * 显示审核弹窗
     * @param {*} type 1通过  2驳回
     */
    showAuditModel(type) {
      if (this.$refs.auditRemarkModal) {
        this.$refs.auditRemarkModal.show(type, {
          ApprovalWorkFlowInstanceId: this.approval ? this.approval.ApprovalWorkFlowInstanceId : '',
          IsHasSuperior: this.approval ? this.approval.IsHasSuperior : false,
        })
      }
    },
    saveAuditData(formData) {
      if (formData) {
        if (this.$refs.qualificationsListAudit.checkData()) {
          //资料数据重新替换
          let qualifications = this.$refs.qualificationsListAudit.getData()
          this.$refs.cusAuditInfoComponents.checkData((bool) => {
            if (bool) {
              let infoData = this.$refs.cusAuditInfoComponents.getData()
              infoData.QualificationVersion.QualificationGroups = qualifications
              infoData.AuditStatusStr = '审核通过'
              infoData.AuditStatus = 2
              this.savePageData(infoData, formData)
            }
          })
        }
      } else {
        this.onAuditModalOk()
      }
    },
    /**
     * 审核弹窗回调
     */
    onAuditModalOk() {
      if (this.model.NextId) {
        this.$emit('onSwitch', 2, this.model.NextId)
      } else {
        if (this.model.LastId) {
          this.$emit('onSwitch', 1, this.model.NextId)
        } else {
          getAction('/v1/CustomerAudit/DeleteAuditOccupy', { id: this.model.Id }, 'P36002')
          this.handleGoBack()
        }
      }
    },
  },
}
</script>
<style scoped>

</style>
