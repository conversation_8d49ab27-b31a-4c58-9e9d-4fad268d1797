<!-- 商品档案新增 基本信息 -->
<template>
  <div class="form-model-style">
    <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical" v-if="acType == 1">
      <!-- 开票信息 -->
      <template>
        <a-form-model-item ref="Title" label="纳税人姓名：" prop="Title" :class="{ 'label-c': model.TitleChanged }">
          <a-input placeholder="请输入" v-model="model.Title" :maxLength="80" />
        </a-form-model-item>
        <a-form-model-item ref="TaxNumber" label="纳税人识别号：" prop="TaxNumber" :class="{ 'label-c': model.TaxNumberChanged }">
          <a-input placeholder="请输入" v-model="model.TaxNumber" :maxLength="30" />
        </a-form-model-item>
        <a-form-model-item ref="BankAccount" label="开户人：" prop="BankAccount" :class="{ 'label-c': model.BankAccountChanged }">
          <a-input placeholder="请输入" v-model="model.BankAccount" :maxLength="80" />
        </a-form-model-item>
        <a-form-model-item ref="BankName" label="开户银行：" prop="BankName" :class="{ 'label-c': model.BankNameChanged }">
          <a-input placeholder="请输入" v-model="model.BankName" :maxLength="60" />
        </a-form-model-item>
        <a-form-model-item ref="BankCardNumber" label="银行账号：" prop="BankCardNumber" :class="{ 'label-c': model.BankCardNumberChanged }">
          <a-input placeholder="请输入" v-model="model.BankCardNumber" :maxLength="50" />
        </a-form-model-item>
        <a-form-model-item ref="RegPhone" label="注册电话：" prop="RegPhone" :class="{ 'label-c': model.RegPhoneChanged }">
          <a-input placeholder="请输入" v-model="model.RegPhone" :maxLength="15" />
        </a-form-model-item>
        <a-form-model-item ref="Email" label="电子邮箱：" prop="Email">
          <a-input placeholder="请输入" v-model="model.Email" :maxLength="15" />
        </a-form-model-item>
      </template>
    </a-form-model>
    <!-- 详情展示部分 -->
    <a-card :bordered="false" v-else-if="acType === 2">
      <a-descriptions title="开票信息" v-if="model.Invoice" :column="1">
        <a-descriptions-item label="纳税人名称">{{ model.Invoice.Title || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="纳税人识别号">{{ model.Invoice.TaxNumber || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="开户人">{{ model.Invoice.BankAccount || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="银行账号">{{ model.Invoice.BankName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="开户银行">{{ model.Invoice.BankCardNumber || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="注册电话">{{ model.Invoice.RegPhone || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="电子邮箱">{{ model.Invoice.Email || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
    </a-card>
  </div>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商品客户审核 发票信息',
  name: 'AuditComponentsCustomerInfo',
  mixins: [EditMixin],
  props: {
    model: {
      type: Object,
      default: undefined,
    },
    acType: {
      type: Number,
      default: 1,
      required: true,
      validator: (s) => [1, 2].includes(s),
    },
  },
  data() {
    return {
      md: 8,
      Id: '', //已存在的客户ID
    }
  },
  computed: {
    rules() {
      return {
        Title: [{ required: true, validator: (rule, value, callback) => {
          if (value) {
            if (!/^[\u4e00-\u9fa5()（）-]+$/.test(value)) {
              callback('只能输入中文')
            } else {
              callback()
            }
          } else {
            callback('请输入')
          }
        } }],
        TaxNumber: [{ required: true, message: '请输入!' }],
        BankAccount: [{ required: true, validator: (rule, value, callback) => {
          if (value) {
            if (!/^[\u4e00-\u9fa5()（）-]+$/.test(value)) {
              callback('只能输入中文')
            } else {
              callback()
            }
          } else {
            callback('请输入')
          }
        } }],
        BankName: [{ required: true, message: '请输入!' }],
        BankCardNumber: [{ required: true, validator: this.checkBankNo }],
        RegPhone: [{ required: true, validator: this.checkTelphone }],
        Email: [{
          required: false, validator: (rule, value, callback) => {
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (this.model.Email && (emailRegex.test(this.model.Email) == false)) {
              callback(new Error('请输入正确的邮箱!'))
            } else {
              callback()
            }
          },
        }],
      }
    },
  },
  created() { },
  mounted() { },
  methods: {
    checkFrom(callback) {
      this.$refs.ruleForm.validate((err, values) => {
        if (err) {
          let oldModel = JSON.parse(JSON.stringify(this.model))
          let data = Object.assign(oldModel, values)
          callback && callback(data)
        } else {
          callback && callback()
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>

.errTip {
  color: #f5222d;
  font-size: 14px;
}
.go-page {
  padding-left: 6px;
  color: #1890ff;
}

/deep/.ant-form-item-control {
  height: 34px;
}

/deep/.label-c label {
  color: #f5222d;
}
</style>
