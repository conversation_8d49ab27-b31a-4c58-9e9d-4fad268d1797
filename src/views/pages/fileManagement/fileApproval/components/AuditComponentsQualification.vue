<template>
  <!--
		* 图片资质组件（包含证件号，有效期）
		* 包含查看/编辑
	-->
  <div>
    <a-spin class="page" :spinning="loading">
      <div class="a-tag-box">
        <a-row :gutter="20">
          <a-col :span="3">
            <!-- 资质列表 -->
            <div class="a-tag-box-type">
              <div @click="handleTagClick(item)" v-for="item in tabsList" :key="item.name">
                <span
                  :class="{
                    active: item.name === tabsActiveName && !item.hasWarning,
                    warning: item.hasWarning,
                    warningActive: item.name === tabsActiveName && item.hasWarning
                  }"
                  style="margin-bottom: 16px"
                  v-if="item.sum"
                >
                  {{ item.name + '(' + item.sum + ')' }}
                </span>
              </div>
            </div>
          </a-col>
          <a-col :span="21">
            <div class="preview-box">
              <div class="container">
                <a-alert
                  message="提示"
                  class="error-image-tip"
                  v-if="containerImg && containerImg.url.startsWith('blob:')"
                  description="异常图片，请重新上传！"
                  type="error"
                  show-icon
                />
                <a-alert
                  message="提示"
                  type="warning"
                  show-icon
                  v-if="containerImg && isPdf(containerImg.url)"
                  class="error-image-tip pdf"
                >
                  <span
                    slot="description"
                  >客户上传格式为PDF文件（<a :href="containerImg.url" target="_blank">点此查看并下载</a>）<template
                    v-if="acType && acType == 1"
                  >，<br />请截图后重新上传！</template
                  >
                  </span>
                </a-alert>
                <div class="re-upload" v-if="imageBtns.length > 0 && acType && acType == 1">
                  <a-button
                    v-if="imageBtns.includes('reload')"
                    :disabled="!containerImg"
                    type="danger"
                    @click="imageHandle('reload')"
                  >重新上传</a-button
                  >
                  <a-button
                    v-if="imageBtns.includes('check')"
                    :disabled="!containerImg || isPdf(containerImg.url) || containerImg.url.startsWith('blob:')"
                    type="primary"
                    @click="imageHandle('check')"
                  >图片校准</a-button
                  >
                  <a-button
                    v-if="imageBtns.includes('identify')"
                    :disabled="!containerImg || isPdf(containerImg.url) || containerImg.url.startsWith('blob:')"
                    type="primary"
                    @click="imageHandle('identify')"
                  >图片识别</a-button
                  >
                </div>
                <!-- 图片展示 -->
                <div class="container-box">
                  <a-carousel
                    @afterChange="previewChange"
                    dotPosition="bottom"
                    :autoplay="false"
                    height="58vh"
                    style="background: rgba(0, 0, 0, 0.4)"
                  >
                    <viewer
                      :images="viewImgList"
                      v-viewer="{
                        inline: false,
                        fullscreen: false,
                        navbar: false,
                        title: false,
                        button: false
                      }"
                    >
                      <div style="height: 58vh; margin: auto; padding: 20px">
                        <img
                          :src="
                            containerImg === null
                              ? ''
                              : containerImg.url.startsWith('blob:') || isPdf(containerImg.url)
                                ? tempImage
                                : containerImg.url
                          "
                          alt="图片异常，请重新上传"
                          style="height: 100%; margin: auto; display: none"
                        />
                      </div>
                    </viewer>
                  </a-carousel>
                </div>
                <!-- 图片分页 -->
                <div class="container-page" v-if="viewImgList.length > 0">
                  <span
                    v-for="(src, index) in viewImgList"
                    :key="index"
                    :class="{ 'page-active': containerActive === src.id }"
                  ></span>
                </div>
                <!-- 上一页 -->
                <div class="page-prev" @click="page('prev')">
                  <a-icon type="left" />
                </div>
                <!-- 下一页 -->
                <div class="page-next" @click="page('next')">
                  <a-icon type="right" />
                </div>
              </div>
            </div>
            <!-- 证件号，有效期表单 -->
            <a-form-model :rules="rules" ref="formData" :model="formData">
              <a-row :gutter="20">
                <a-col :md="24">
                  <a-form-model-item
                    style="margin-bottom: 0"
                    v-show="tabsActiveName === '法人'"
                    label="姓名："
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    prop="CertificateName"
                  >
                    <a-input
                      v-if="acType && acType == 1"
                      style="width: 42%"
                      v-model.trim="formData.CertificateName"
                      maxlength="40"
                      placeholder="请输入姓名"
                    >
                    </a-input>
                    <template v-else>
                      {{ formData.CertificateName }}
                    </template>
                    <a-tooltip v-if="OCRData && OCRData.Name" placement="topLeft" title="点击识别结果填入表单">
                      <a style="margin-left:20px" @click="setOCRData('Name')">识别结果：{{ OCRData.Name }}</a>
                    </a-tooltip>
                  </a-form-model-item>
                </a-col>
                <a-col :md="12">
                  <a-form-model-item
                    style="margin-bottom: 0"
                    prop="CertificateNo"
                    label="证件号："
                    :labelCol="labelCol1"
                    :wrapperCol="wrapperCol1"
                  >
                    <a-input
                      v-if="acType && acType == 1"
                      style="width: 100%"
                      v-model.trim="formData.CertificateNo"
                      maxlength="40"
                      placeholder="请输入证件号"
                    >
                    </a-input>
                    <template v-else>
                      {{ formData.CertificateNo }}
                    </template>
                    <a-tooltip v-if="OCRData && OCRData.Number" placement="topLeft" title="点击识别结果填入表单">
                      <a @click="setOCRData('Number')">识别结果：{{ OCRData.Number }}</a>
                    </a-tooltip>
                  </a-form-model-item>
                </a-col>
                <a-col :md="12">
                  <a-form-model-item
                    style="margin-bottom: 0"
                    prop="EndValidDate"
                    label="有效期至："
                    :labelCol="labelCol1"
                    :wrapperCol="wrapperCol1"
                  >
                    <a-date-picker
                      v-if="acType && acType == 1"
                      @change="(dates, dateStrings) => onEndTimeChange(dates, dateStrings)"
                      v-model="formData.EndValidDate"
                      style="width: 65%"
                      placeholder="结束日期"
                    >
                    </a-date-picker>
                    <template v-else>
                      {{
                        moment(formData.EndValidDate).isValid()
                          ? moment(formData.EndValidDate).format(dateFormat)
                          : '--'
                      }}
                    </template>
                    <a-checkbox
                      v-if="acType && acType == 1"
                      v-model="formData.LongTimeCheck"
                      @change="e => onValidDateChange(e)"
                      style="margin-left: 16px"
                    >
                      长期
                    </a-checkbox>
                    <template v-else>
                      {{ formData.LongTimeCheck ? '（长期）' : '' }}
                    </template>
                    <template v-if="OCRData && OCRData.Date">
                      <br />
                      <a-tooltip placement="topLeft" title="点击识别结果填入表单">
                        <a @click="setOCRData('Date')">识别结果：{{ moment(OCRData.Date).format(dateFormat) }}</a>
                      </a-tooltip>
                    </template>
                  </a-form-model-item>
                </a-col>
                <!-- <a-col :md="24" v-if="isEdit && containerImg && isPdf(containerImg.url)">
                  <p class="pdf-tip" @click="viewPdf">客户上传格式为PDF文件（<a :href="containerImg.url" target="_blank">点此查看</a>），请截图后重新上传</p>
                </a-col> -->
              </a-row>
            </a-form-model>
          </a-col>
        </a-row>
      </div>
    </a-spin>
    <!-- 重新上传 -->
    <CustomerReUploadModal ref="CustomerReUploadModalRef" @ok="reUploadBack" />
    <!-- 图片校验 -->
    <PictureCalibrationDialog
      ref="pictureCalibrationDialog"
      @ok="onPictureCalibrationCallback"
      :bName="bName"
      :dir="dir"
    />
  </div>
</template>

<script>
import moment from 'moment'
// 引入api请求配置
import { getAction, postAction } from '@/api/manage'
import tempImage from '@/assets/tempimg.svg'
export default {
  name: 'AuditComponentsQualification',
  props: {
    // 监听父组件传递的row对象
    rowData: {
      type: Object,
      default: () => {}
    },
    // 是否可编辑
    imageBtns: {
      type: Array,
      default() {
        return []
      }
    },
    // 存储桶
    bName: {
      type: String,
      default: window._CONFIG.AliOssConfig['BaseBucketName']
    },
    // 文件夹
    dir: {
      type: String,
      default: window._CONFIG.AliOssConfig['CommonDir']
    },
    acType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      // 左侧标题选中值
      tabsActiveName: '',
      tabsActiveCode: '',
      tempImage: tempImage,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 3 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 21 }
      },
      labelCol1: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol1: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      longTimeStr: '2099-12-31',
      loading: false,
      nextShow: true,
      // 表单数据结构
      formData: {
        // 证件号
        CertificateNo: '',
        // 有效期
        CertificateTime: '',
        // 姓名
        CertificateName: '',
        // 结束日期
        EndValidDate: null,
        // 长期
        LongTimeCheck: false
      },
      // 节流计时器对象
      timer: null,
      // 临时渲染数据，单组
      viewImgList: [],
      // 左侧标题渲染集合
      tabsList: [],
      CertificateNameNew: '',
      // gsp是否缺失标记
      hasGSP: null,
      // 图片展示器展示的图片对象
      containerImg: null,
      // 图片切换比对选中参数
      containerActive: '',
      OCRData: null, //图片识别数据
      httpHead: 'P31102',
      url: {
        ImageOCR: '/ImageOCR/ImageOCR'
      }
    }
  },
  watch: {
    // 节流操作
    // 监听证件号变化
    'formData.CertificateNo': {
      handler(value) {
        if (this.timer) {
          clearTimeout(this.timer)
        }
        // 证件号输入0.5s后执行修改数据操作
        this.timer = setTimeout(() => {
          // 更新节点（证件号）
          this.againCertificateNoData(value, 'CertificateNo')
        }, 500)
      },
      deep: true
    },
    'formData.CertificateName': {
      handler(value) {
        if (this.timer) {
          clearTimeout(this.timer)
        }
        this.timer = setTimeout(() => {
          this.againCertificateNoData(value, 'CertificateName')
          this.CertificateNameNew = this.formData.CertificateName
        }, 500)
      },
      deep: true
    },
    'formData.EndValidDate': {
      handler(value) {
        if (this.timer) {
          clearTimeout(this.timer)
        }
        this.timer = setTimeout(() => {
          this.againCertificateNoData(value, 'EndValidDate')
        }, 500)
      },
      deep: true
    },
    containerImg() {
      this.OCRData = null
    },
    /*
     * 组件表单校验,反回状态到父组件
     */
    validateForm() {
      if (this.formData.CertificateName == '' && this.CertificateNameNew != '') {
        this.formData.CertificateName = this.CertificateNameNew
      }
      let flag = null
      this.$refs['formData'].validate(valid => {
        if (valid) {
          flag = true
          const hasEmptyItem = this.rowData.Qualifications.some(item => {
            return item.QualificationCode.indexOf('Image') == -1 && !item.QualificationValue
          })
          if (hasEmptyItem) {
            flag = false
          }
        } else {
          flag = false
        }
      })
      return flag
    }
  },
  computed: {
    rules() {
      return {
        CertificateNo: [
          {
            required: this.acType && this.acType == 1 ? true : false,
            message: '请输入'
          }
        ],
        CertificateName: [
          {
            required:
              (this.tabsList || []).findIndex(v => v.name == '法人' && v.sum) >= 0 && this.acType && this.acType == 1
                ? true
                : false,
            message: '请输入'
          }
        ],
        EndValidDate: [
          {
            required: this.acType && this.acType == 1 ? true : false,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!this.formData.EndValidDate) {
                callback(new Error('请选择'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    }
  },
  created() {
    this.GetCustomerZzAudit()
  },
  methods: {
    moment,
    /**
     * @description: 选择有效期
     * @param {*} dates
     * @param {*} dateStrings
     * @return {*}
     */
    onEndTimeChange(dates, dateStrings) {
      this.$refs.formData.clearValidate(['EndValidDate'])
      if (dates) {
        this.formData.EndValidDate = moment(dates)
      } else {
        this.formData.EndValidDate = null
      }
      if (dateStrings == this.longTimeStr) {
        this.formData.LongTimeCheck = true
      } else {
        this.formData.LongTimeCheck = false
      }
      // 如果是药品、并且没有有效期
      if (this.tabsActiveName == '药品经营') {
        let ListByYP = this.rowData.Qualifications.filter(v => v.QualificationGroupName == '药品经营') || []
        if (ListByYP.findIndex(item => item.QualificationCode == 'DrugLicenseDate') == -1 && ListByYP.length > 0) {
          this.rowData.Qualifications.push({
            // VersionNumber: ListByYP[0].VersionNumber,
            QualificationGroupName: '药品经营',
            QualificationCode: 'DrugLicenseDate',
            QualificationName: '药品经营许可证-效期',
            QualificationValue: moment(new Date(this.longTimeStr).getTime() - 1 * 24 * 60 * 60 * 1000) + '~' + dates,
            BeginValidityDate: moment(new Date(dates).getTime() - 1 * 24 * 60 * 60 * 1000),
            EndValidityDate: dates,
            AuditStatus: ListByYP[0].AuditStatus,
            AuditTime: ListByYP[0].AuditTime,
            AuditReason: ListByYP[0].AuditReason
          })
        }
      }
    },
    /**
     * @description:长期
     * @param {*}
     * @return {*}
     */
    onValidDateChange(e) {
      this.$refs.formData.clearValidate(['EndValidDate'])
      let checked = e.target.checked
      if (checked) {
        this.formData.EndValidDate = moment(this.longTimeStr)
        // 判断下药品
        // 如果是药品、并且没有有效期
        if (this.tabsActiveName == '药品经营') {
          let ListByYP = this.rowData.Qualifications.filter(v => v.QualificationGroupName == '药品经营') || []
          if (ListByYP.findIndex(item => item.QualificationCode == 'DrugLicenseDate') == -1 && ListByYP.length > 0) {
            this.rowData.Qualifications.push({
              // VersionNumber: ListByYP[0].VersionNumber,
              QualificationGroupName: '药品经营',
              QualificationCode: 'DrugLicenseDate',
              QualificationName: '药品经营许可证-效期',
              QualificationValue:
                moment(new Date(this.longTimeStr).getTime() - 1 * 24 * 60 * 60 * 1000) + '~' + this.longTimeStr,
              BeginValidityDate: moment(new Date(this.longTimeStr).getTime() - 1 * 24 * 60 * 60 * 1000),
              EndValidityDate: this.longTimeStr,
              AuditStatus: ListByYP[0].AuditStatus,
              AuditTime: ListByYP[0].AuditTime,
              AuditReason: ListByYP[0].AuditReason
            })
          }
        }
      } else {
        this.formData.EndValidDate = null
      }
    },
    /*
     * 初始化获取数据
     */
    GetCustomerZzAudit(type) {
      // 补充空数据
      // this.replenishEmptyRecord()
      // 设置左侧tabs
      let imgTab = this.rowData.Qualifications.filter(e => e.Files && e.Files.length)
      if (imgTab && imgTab.length) {
        this.tabsList = imgTab.map(e => {
          return {
            name: e.QualificationGroupName,
            sum: e.Files.length,
            code: e.QualificationCode
          }
        })

        console.log(this.tabsList)

        this.tabsActiveName = this.tabsList[0].name
        this.tabsActiveCode = this.tabsList[0].code

        this.viewImgList = imgTab[0].Files.map((e, idx) => {
          return {
            url: e.FileUrl,
            id: imgTab[0].QualificationGroupCode + idx,
            code: imgTab[0].QualificationGroupCode,
            srcList: imgTab[0].Files.map(e1 => e1.FileUrl)
          }
        })

        this.containerActive = this.viewImgList[0].id
        this.containerImg = this.viewImgList[0]
      }

      this.$nextTick(() => {
        setTimeout(() => {
          this.page('next')
        }, 300)
      })
    },
    // 是否是PDF文件
    isPdf(url) {
      return url.split('?')[0].length > 0 && url.split('?')[0].endsWith('.pdf')
    },
    // // 过滤异常图片、PDF
    filterBlobImage(url) {
      if (url.startsWith('blob:') || this.isPdf(url)) {
        return ''
      }
      return url
    },
    // 图片操作
    imageHandle(type) {
      if (type === 'reload') {
        // 重传
        // 元数据分组code
        const CertificateGroupCode =
          this.containerImg.code.indexOf('IdCard') > -1 ? 'IdCard' : this.containerImg.code.split('Image')[0]
        let imageObj = {}
        if (CertificateGroupCode === 'IdCard') {
          const frontImage = this.rowData.Qualifications.find(it => it.QualificationCode === 'IdCardFrontImage')
          const backImage = this.rowData.Qualifications.find(it => it.QualificationCode === 'IdCardBackImage')
          imageObj = {
            IdCardFrontImage: frontImage && this.filterBlobImage(frontImage.QualificationValue),
            IdCardBackImage: backImage && this.filterBlobImage(backImage.QualificationValue)
          }
        } else {
          const images = this.rowData.Qualifications.filter(
            it => it.QualificationCode.indexOf(CertificateGroupCode + 'Image') > -1
          )
          images.forEach(it => {
            imageObj[it.QualificationCode] = this.filterBlobImage(it.QualificationValue)
          })
        }
        this.$refs.CustomerReUploadModalRef.show(imageObj)
      } else if (type === 'check') {
        // 图片校验
        this.$refs.pictureCalibrationDialog && this.$refs.pictureCalibrationDialog.show(this.containerImg.url)
      } else if (type === 'identify') {
        // 视图识别
        this.imageOCRBack()
      }
    },
    // 图片识别
    imageOCRBack() {
      this.loading = true
      const params = { qualificationCode: this.containerImg.code || '', imageUrl: this.containerImg.url }
      getAction(this.url.ImageOCR, params, this.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            // 是否有识别结果
            const hasResult = Object.keys(res.Data).some(key => {
              return !!res.Data[key]
            })
            if (hasResult) {
              if (this.containerImg.code !== 'IdCardFrontImage' && res.Data.Name) {
                this.$emit('ImageOCROk', res.Data)
              }
              this.OCRData = res.Data
              this.$message.success('识别成功，点击字段识别结果可填入表单。')
            } else {
              this.$message.warning('未识别到有效信息！')
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(e => {
          this.loading = false
        })
    },
    // 图片识别修改元数据
    setOCRData(key) {
      // 元数据分组code
      const CertificateGroupCode =
        this.containerImg.code.indexOf('IdCard') > -1 ? 'IdCard' : this.containerImg.code.split('Image')[0]
      const setFn = name => {
        const dataIndex = this.rowData.Qualifications.findIndex(
          it => it.QualificationCode === CertificateGroupCode + name
        )
        if (dataIndex > -1) {
          this.rowData.Qualifications[dataIndex].QualificationValue = this.OCRData[name]
          this.OCRData[name] = null
        }
      }
      // 名称
      if (this.containerImg.code === 'IdCardFrontImage' && key === 'Name') {
        this.$set(this.formData, 'CertificateName', this.OCRData.Name || this.formData.CertificateName)
        setFn('Name')
      }
      // 证件号
      if (key === 'Number') {
        this.$set(this.formData, 'CertificateNo', this.OCRData.Number || this.formData.CertificateNo)
        setFn('Number')
      }
      // 有效期
      if (key === 'Date') {
        const dataIndex = this.rowData.Qualifications.findIndex(
          it => it.QualificationCode === CertificateGroupCode + 'Date'
        )
        if (dataIndex > -1) {
          const dateVal = `${moment(this.rowData.Qualifications[dataIndex].BeginValidityDate).format(
            this.dateFormat
          )} ~ ${moment(this.OCRData.Date).format(this.dateFormat)}`
          this.rowData.Qualifications[dataIndex].QualificationValue = dateVal
          this.rowData.Qualifications[dataIndex].EndValidityDate = moment(this.OCRData.Date).format(this.dateTimeFormat)

          const dateObj =
            (this.OCRData.Date && moment(this.OCRData.Date)) ||
            (this.formData.EndValidDate && moment(this.formData.EndValidDate)) ||
            null
          this.$set(this.formData, 'EndValidDate', dateObj)
          // 长期
          if (dateObj && moment(dateObj).format(this.dateFormat) === this.longTimeStr) {
            this.formData.LongTimeCheck = true
          } else if (dateObj) {
            this.formData.LongTimeCheck = false
          }
          this.OCRData.Date = null
        }
      }
    },

    // 重新上传图片回调
    reUploadBack(imgObj) {
      const imageLength = Object.keys(imgObj).filter(key => imgObj[key]).length
      // 修改左侧的数量
      const itemIndex = this.tabsList.findIndex(it => it.name === this.tabsActiveName)
      this.tabsList[itemIndex].sum = imageLength
      Object.keys(imgObj).forEach(key => {
        const keyIndex = this.viewImgList.findIndex(item => item.code === key)
        const qualificationIndex = this.rowData.Qualifications.findIndex(it => it.QualificationCode === key)
        if (keyIndex > -1) {
          this.viewImgList[keyIndex].url = imgObj[key]
          this.viewImgList[keyIndex].srcList = [imgObj[key]]
          if (this.containerImg.code === key) {
            this.containerImg = this.viewImgList[keyIndex]
          }
        }
        if (qualificationIndex > -1) {
          this.rowData.Qualifications[qualificationIndex].QualificationValue = imgObj[key]
        }
      })
    },
    // 校验图片回调
    onPictureCalibrationCallback(url) {
      const containerIndex = this.viewImgList.indexOf(this.containerImg)
      this.viewImgList[containerIndex].url = url
      this.viewImgList[containerIndex].srcList = [url]
      this.containerImg.url = url
      // 修改元数据
      const dataIndex = this.rowData.Qualifications.findIndex(it => it.QualificationCode === this.containerImg.code)
      if (dataIndex > -1) {
        this.rowData.Qualifications[dataIndex].QualificationValue = url
      }
    },
    /*
     * 标题点击切换监听
     */
    handleTagClick(e) {
      let vProps =
        this.tabsActiveName == '身份证' || '委托人'
          ? ['CertificateName', 'CertificateNo', 'EndValidDate']
          : ['CertificateNo', , 'EndValidDate']

      // if(this.acType == 1){
      //   let errArr = []
      //   if (this.acType==1) {
      //     this.$refs.formData.validateField(vProps, err => {
      //       if (err) errArr.push(err)
      //     })
      //   }

      //   if (this.acType==1 && errArr.length) {
      //     return
      //   }
      // }

      this.tabsActiveName = e.name
      let curList = this.rowData.Qualifications.filter(v => v.QualificationGroupName == e.name) || []
      let CertificateNo = '',
        CertificateTime = '',
        CertificateName = '',
        CertificateUrl = [],
        CertificateCode = [],
        QualificationValue = ''

      if (curList && curList.length) {
        if (curList.filter(v => v.Files && v.Files.length).length) {
          CertificateUrl = curList.filter(v => v.Files && v.Files.length)[0].Files
        }
        //1图片 2文件 3日期 4文本
        if (curList.filter(v => v.QualificationType == 3).length) {
          CertificateTime = curList.filter(v => v.QualificationType == 3)[0].QualificationValue
        }
        if (curList.filter(v => v.QualificationType == 4).length) {
          if (
            curList.filter(e => e.QualificationGroupName == '身份证' || e.QualificationGroupName == '委托人').length
          ) {
            CertificateNo = curList.filter(
              e => e.QualificationGroupName == '身份证' || e.QualificationGroupName == '委托人'
            )[0].QualificationValue
            // CertificateName = curList.filter(e=>e.QualificationGroupName=='身份证'||e.QualificationGroupName=='委托人')[0].QualificationValue
          } else {
            CertificateNo = curList.filter(v => v.QualificationType == 4)[0].QualificationValue
          }
          QualificationValue = curList.filter(v => v.QualificationType == 4)[0].QualificationValue
        }

        this.formData.CertificateNo = CertificateNo
        this.formData.CertificateName = QualificationValue
        if (this.CertificateNameNew == '' && this.formData.CertificateName != '') {
          this.CertificateNameNew = this.formData.CertificateName
        }
        this.formData.CertificateTime = moment(CertificateTime).format(this.dateFormat)

        if (this.formData.CertificateTime) {
          this.formData.EndValidDate =
            this.formData.CertificateTime && moment(this.formData.CertificateTime).isValid()
              ? moment(this.formData.CertificateTime)
              : null
          if (
            this.formData.CertificateTime &&
            moment(this.formData.CertificateTime).format(this.dateFormat) == this.longTimeStr
          ) {
            this.formData.LongTimeCheck = true
          } else {
            this.formData.LongTimeCheck = false
          }
        } else {
          this.formData.BeginValidDate = moment(new Date())
          this.formData.EndValidDate = null
        }
      }

      // 清空本地图片缓存
      this.viewImgList = []

      if (CertificateUrl.length) {
        // 遍历图片集合
        CertificateUrl.forEach((itemImg, index) => {
          this.viewImgList.push({
            url: itemImg.FileUrl, //第一张默认展示图层
            id: index, //key
            code: CertificateCode[index],
            srcList: CertificateUrl.filter(e => e.FileUrl) //图片集合
          })
        })

        // 每次切换设置图片分页指示器默认等于第一张图
        this.containerActive = this.viewImgList[0].id
        this.containerImg = this.viewImgList[0]
      } else {
        this.viewImgList.push({
          url: '', //第一张默认展示图层
          id: '', //key
          code: [''],
          srcList: [''] //图片集合
        })
        // 每次切换设置图片分页指示器默认等于第一张图
        this.containerActive = ''
        this.containerImg = null
      }
    },
    /*
     * 大图预览
     */
    previewChange() {
      // 获取遮罩层dom
      let domImageMask = document.querySelector('.a-image-viewer__mask')
      if (!domImageMask) {
        return
      }
      document.querySelector('.a-image-viewer__close').click()
    },
    /*
     * 组件表单校验,反回状态到父组件
     */
    validateForm() {
      if (this.formData.CertificateName == '' && this.CertificateNameNew != '') {
        this.formData.CertificateName = this.CertificateNameNew
      }
      let flag = null
      this.$refs['formData'].validate(valid => {
        if (valid) {
          flag = true
          const hasEmptyItem = this.rowData.Qualifications.some(item => {
            return item.QualificationCode.indexOf('Image') == -1 && !item.QualificationValue
          })
          if (hasEmptyItem) {
            flag = false
          }
        } else {
          flag = false
        }
      })
      return flag
    },
    // 查看PDF
    viewPdf() {
      //
    },
    /*
     * 数据重组方法（证件号）
     * value 证件号input变化最新的值
     */
    againCertificateNoData(value, Name) {
      // 获取当前缓存对象
      let updateArr = this.rowData.Qualifications

      // 根据当前选中的模块，去堆栈中匹配对应缓存
      updateArr.forEach(item => {
        if (item.QualificationGroupName == this.tabsActiveName && item.QualificationCode == this.tabsActiveCode) {
          if (Name == 'EndValidDate') {
            item.EndValidityDate = value ? moment(value).format(this.dateFormat) : ''
            item.QualificationValue = item.EndValidityDate
          } else {
            item.QualificationValue = value
          }

          console.log(item)
        }
      })

      // console.log(updateArr)
      this.rowData.Qualifications = updateArr
      this.rowData.IsUpdateCustomer = true
    },
    /*
     * input 获得焦点，选中所有值
     */
    onFocusAgainTimeData(e) {
      e.srcElement.select()
    },
    /*
     * 图片分页器
     * type = prev 上一页
     * type = next 下一页
     */
    page(type) {
      // 判断当前图片集合长度
      if (this.viewImgList.length !== 0) {
        // 基础校验，防止数据格式不正确的脏数据存在
        if (this.containerImg !== '' && this.containerImg !== null) {
          // 关闭图片预览
          this.previewChange()
          // 根据当前图片对象，获取其在整个集合中的索引位置
          let containerIndex = this.viewImgList.indexOf(this.containerImg)
          // 判断操作类型
          if (type === 'prev') {
            // 第一张图
            if (containerIndex === 0) {
              // 数据集合匹配下一组图片
              this.listMatching(type)
            } else {
              this.containerImg = this.viewImgList[containerIndex - 1]
              this.containerActive = this.viewImgList[containerIndex - 1].id
            }
          }
          if (type === 'next') {
            // 最后一张图
            if (containerIndex + 1 === this.viewImgList.length) {
              // 数据集合匹配下一组图片
              this.listMatching(type)
            } else {
              this.containerImg = this.viewImgList[containerIndex + 1]
              this.containerActive = this.viewImgList[containerIndex + 1].id
            }
          }
        }
      }
    },
    /**
     * @description:模拟  一个倒序查找的方法 :findIndex
     * @param {*} array
     * @param {*} cb
     * @return {*}
     */

    findLastIndex(array, cb) {
      if (!Array.isArray(array)) {
        return -1
      }
      if (array.length === 0) {
        return -1
      }
      for (var i = array.length - 1; i >= 0; i--) {
        const item = array[i]
        if (cb.call(null, item, i, array)) {
          return i
        }
      }
      return -1
    },
    findeItem(currentIndex) {
      let j = currentIndex == 0 ? len - 1 : currentIndex
      if (this.tabsList[j - 1].sum == 0) {
        return this.findeItem(j - 1)
      } else {
        return j
      }
    },

    /*
     * 图片分页器，匹配外层图片对象
     * type = prev 上一页
     * type = next 下一页
     */
    listMatching(type) {
      let that = this
      let curIndex = that.tabsList.findIndex(t => t.name == that.tabsActiveName)
      if (type == 'next') {
        // 下一页
        if (curIndex == that.tabsList.length - 1) {
          let fIndex = that.getNextIndex(-1)
          that.handleTagClick({ name: that.tabsList[fIndex].name })
        } else {
          let newIndex = that.getNextIndex(curIndex)
          that.handleTagClick({ name: that.tabsList[newIndex].name })
        }
      } else {
        let curLastIndex = that.tabsList.findIndex(t => t.name == that.tabsActiveName)
        if (curLastIndex != 0) {
          let index = that.findeItem(curLastIndex)
          that.handleTagClick({ name: that.tabsList[index - 1].name })
        } else {
          let index = that.findLastIndex(that.tabsList, t => t.sum)
          that.handleTagClick({ name: that.tabsList[index].name })
        }
      }
    },

    getNextIndex(curIndex) {
      let newIndex = curIndex
      for (const i in this.tabsList) {
        if (i > curIndex && this.tabsList[i].sum > 0) {
          newIndex = i
          break
        }
      }
      return newIndex
    }
  }
}
</script>

<style lang="scss" scoped>
.a-tag-box-type {
  max-height: calc(100vh - 350px);
  overflow: auto;
}
.re-upload {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 99;
  .ant-btn + .ant-btn {
    margin-left: 10px;
  }
}
.error-image-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -60px 0 0 -130px;
  width: 260px;
  z-index: 99;
  &.pdf {
    width: 400px;
    margin-left: -200px;
  }
}
.pdf-tip {
  text-align: right;
  color: red !important;
}
::v-deep .a-tag-header {
  .label-font {
    position: relative;
    padding-left: 10px;
    font-weight: bold;
  }
  .label-font:after {
    position: absolute;
    width: 5px;
    height: 100%;
    background-color: #409eff;
    left: 0;
    top: 0;
    content: '';
  }
}
::v-deep .a-tag-box {
  margin: 0 8px 8px 0;
  .a-tag-box-type {
    span {
      display: block;
      text-align: center;
      padding: 10px 5px;
      border: 1px solid #ecf5ff;
      background-color: #ecf5ff;
      border-radius: 4px;
      margin-bottom: 10px;
      color: #409eff;
      cursor: pointer;
    }
    span:last-child {
      margin-bottom: 0;
    }
    .active {
      background-color: #409eff !important;
      color: #ffffff !important;
      font-weight: bold;
    }
    .warning {
      color: red !important;
      background-color: #fff0f0 !important;
      border: 1px solid #fff0f0 !important;
    }
    .warningActive {
      color: #ffffff !important;
      background-color: #f56c6c !important;
      border: 1px solid #f56c6c !important;
    }
  }
  .preview-box {
    margin: 0 0 10px 0;
  }
  .a-carousel__item h3 {
    color: #475669;
    font-size: 14px;
    opacity: 0.75;
    line-height: 150px;
    margin: 0;
  }
  .flex-box {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .text-center {
    text-align: center;
  }
}
.a-icon-circle-close {
  color: #000000 !important;
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 16px;
}
.container {
  height: 60vh;
  position: relative;
  overflow: hidden;
  .page-prev {
    position: absolute;
    top: 28vh;
    left: 5px;
    z-index: 999;
    border-radius: 100%;
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 30px;
    box-shadow: 0 0 10px #ccc;
    background: #ffffff;
    cursor: pointer;
  }
  .page-next {
    position: absolute;
    top: 28vh;
    right: 5px;
    z-index: 999;
    border-radius: 100%;
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 30px;
    box-shadow: 0 0 10px #ccc;
    background: #ffffff;
    cursor: pointer;
  }
  .container-box {
    height: 58vh;
  }
  .container-page {
    text-align: center;
    span {
      display: inline-block;
      background: #dcdfe6;
      color: #dcdfe6;
      margin: 0 6px;
      height: 4px;
      width: 25px;
    }
    .page-active {
      background: #409eff;
      color: #409eff;
    }
  }
}
</style>
