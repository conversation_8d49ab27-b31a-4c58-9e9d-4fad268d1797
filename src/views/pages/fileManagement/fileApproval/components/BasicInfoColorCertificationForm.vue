
<!-- 商品七彩认证 基本信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-form-model
        ref="ruleForm"
        :rules="rules"
        :model="model"
        layout="vertical"
        v-if="[2].includes(opType)"
      >
        <!-- 商品信息 -->
        <template>
          <a-row :gutter="gutter">
            <a-col :span="24">
              <div style="font-size: 16px; font-weight: 600;margin:0 0 15px 0">
                商品信息
              </div>
            </a-col>
            <a-col :span="md">
              <a-form-model-item
                label="商品名称："
                prop="ErpGoodsName"
              >
                <!-- 
                  :maxLength="100"

               -->
                <a-input
                  placeholder="请输入"
                  v-model="model.ErpGoodsName"
                />
              </a-form-model-item>
            </a-col>
            <a-col
              :span="md"
              v-if="!['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code) && !['YLQX'].includes(model.ManageClassify1Code)"
            >
              <a-form-model-item
                label="商品名："
                prop="AliasName"
              >
                <a-input
                  placeholder="请输入"
                  v-model="model.AliasName"
                  :maxLength="80"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item
                label="商品规格："
                prop="PackingSpecification"
              >
                <a-input
                  placeholder="请输入"
                  v-model="model.PackingSpecification"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item
                label="生产厂家："
                prop="BrandManufacturer"
              >
                <a-input
                  placeholder="请输入"
                  v-model="model.BrandManufacturer"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="md">
              <a-form-model-item
                label="产地："
                prop="Producer"
              >
                <a-input
                  placeholder="请输入"
                  v-model="model.Producer"
                />
              </a-form-model-item>
            </a-col>

            <a-col :span="md">
              <a-form-model-item
                label="包装单位："
                prop="PackageUnit"
              >
                <SingleChoiceView
                  style="width: 100%;"
                  placeholder="请选择"
                  :httpParams="{
                    groupPY: 'dw'
                  }"
                  httpHead="P36001"
                  :dataKey="{ name: 'ItemValue', value: 'ItemValue' }"
                  v-model="model.PackageUnit"
                  :Url="url.getListDictItem"
                  @change="(val, txt, item) => (model.PackageUnit = txt)"
                />
              </a-form-model-item>
            </a-col>
            <a-col
              :span="md"
              v-if="!['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code) &&  !['RYBH'].includes(model.ManageClassify1Code)"
            >
              <a-form-model-item
                label="批准文号："
                prop="ApprovalNumber"
              >
                <a-input
                  placeholder="请输入"
                  v-model="model.ApprovalNumber"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </template>
      </a-form-model>
      <!-- 详情展示部分 -->
      <a-card
        :bordered="false"
        v-else
      >
        <a-descriptions title="商品信息">
          <a-descriptions-item label="商品名称">{{ model.ErpGoodsName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item
            label="商品名"
            v-if="!['ZYC_2', 'ZYYP_2'].includes(model.ManageClassify2Code) && !['YLQX'].includes(model.ManageClassify1Code)"
          >{{ model.AliasName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="商品规格">{{ model.PackingSpecification || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="生产厂家">{{ model.BrandManufacturer || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="产地">{{ model.Producer || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="包装单位">{{ model.PackageUnit || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item
            label="批准文号"
            v-if="!['ZYC_2', 'ZYYP_2'].includes(model.ManageClassifyCode2) &&  !['RYBH'].includes(model.ManageClassifyCode1)"
          >{{ model.ApprovalNumber || ' -- ' }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
    </a-spin>

  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ID, USER_INFO } from '@/store/mutation-types'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商品七彩认证 基本信息',
  name: 'BasicInfoColorCertificationForm',
  mixins: [EditMixin, SimpleMixin],
  props: {
    /**
     * 2 编辑   4详情
     */
    opType: {
      type: Number,
      default: 2,
      required: true,
      validator: (s) => [2, 4].includes(s),
    },
    Info: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      loading: false,
      md: 8,
      model: {
        ErpGoodsName: '',
        AliasName: '',
        BrandManufacturer: '',
        Producer: '',
        PackingSpecification: '',
        PackageUnit: '',
        ApprovalNumber: '',
      },

      url: {
        getListDictItem: '/{v}/Global/GetListDictItem',
      },
    }
  },
  computed: {
    rules() {
      return {
        ErpGoodsName: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200) },
        ],
        BrandManufacturer: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200) },
        ],
        Producer: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200) },
        ],
        PackingSpecification: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200) },
        ],
        PackageUnit: [{ required: true, message: '请选择!' }],
        ApprovalNumber: [
          { required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200) },
        ],
      }
    },
  },
  created() {},
  mounted() {
    if (this.Info) {
      this.model = this.Info
      this.model.ManageClassify1Code = this.Info['ManageClassifyCode1']
      this.model.ManageClassify2Code = this.Info['ManageClassifyCode2']
      // this.model.MedicalInsuranceType = '' + (this.model.MedicalInsuranceType || '')
      this.model.PurchaseTaxRate = '' + (this.model.PurchaseTaxRate || '')
      this.model.SellsTaxRate = '' + (this.model.SellsTaxRate || '')
      this.model.PackageCount = '' + (this.model.PackageCount || '')
    }
  },
  methods: {
    onSubmit() {
      // console.log(this.opType)
      let returnValid = false
      this.$refs.ruleForm.validate((valid) => {
        returnValid = valid
      })

      if (!returnValid) {
        return returnValid
      }
      return this.setFormData()
    },
    setFormData() {
      let formData = JSON.parse(JSON.stringify(this.model))
      return formData
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
    },
  },
}
</script>

<style lang="less" scoped>
.form-model-style {
  /deep/.ant-form-item-control {
    height: 34px;
  }
}
</style>