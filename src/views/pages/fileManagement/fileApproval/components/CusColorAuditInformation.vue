<!--
 * @Author: LP
 * @Description: 审核信息
 * @Date: 2023/06/12
-->
<template>
    <a-card :bordered="false">
      <a-timeline v-if="info && info.Records && info.Records.length">
        <a-timeline-item color="blue" v-for="(record,index) in info.Records" :key="index">
          <p>{{ record.CaAuditTime }}</p>
          <p>{{ record.CaAuditStatusStr }}</p>
          <p v-if="record.CaAuditOpinion">{{ record.CaAuditOpinion }}</p>
        </a-timeline-item>
      </a-timeline>
      <template v-else>
        <a-empty />
      </template>
    </a-card>
</template>

<script>
export default {
  name: 'CusColorAuditInformation',
  components: {},
  props: {
     info: {
      type: String,
      default: () => {
        return ''
      }
    },
  },
  data() {
    return {}
  },
  mounted() {
  },
  created() {},
  methods: {}
}
</script>

<style scoped>

</style>
