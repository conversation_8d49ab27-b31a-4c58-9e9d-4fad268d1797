<!-- 客户七彩认证 基本信息 -->
<template>
  <div class="form-model-style">
    <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical" v-if="[1, 2, 3].includes(opType)">
      <!-- 商品信息 -->
      <a-row :gutter="gutter">
        <a-col :span="24">
          <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">企业信息</div>
        </a-col>
        <a-col :span="md">
          <a-form-model-item ref="Name" label="企业名称：" prop="Name">
            <a-input placeholder="请输入" v-model="model.Name" :maxLength="80" />
            <span slot="help" class="cred" v-if="tipInfo"
              >{{ tipInfo.text }}，<a @click="goDetail()">{{ tipInfo.btn }}</a></span
            >
          </a-form-model-item>
        </a-col>
        <a-col :span="md">
          <a-form-model-item ref="Type" label="企业类别：" prop="Type">
            <SingleChoiceView
              placeholder="请选择"
              :httpParams="{
                groupPY: 'kehulb',
              }"
              disabled
              :httpHead="'P36001'"
              :dataKey="{ name: 'ItemValue', value: 'Id' }"
              :Url="'/v1/Global/GetListDictItemForCustomer'"
              v-model="model.Type"
              @change="(val, txt, item) => (model.TypeStr = txt)"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="md">
          <a-form-model-item ref="RegAddress" label="注册地址：" prop="RegAddress">
            <a-input placeholder="请输入" v-model="model.RegAddress" :maxLength="255" />
          </a-form-model-item>
        </a-col>
        <a-col :span="md">
          <a-form-model-item label="区域分类（省市区）" prop="RegAreaCode">
            <SelectAreaView
              style="width: 100%"
              ref="selectCityAreas"
              :selectCode="true"
              :defaultCodes="areaValue"
              @change="(val, node) => onAreasChange(val, node, model, 'RegArea')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="md">
          <a-form-model-item ref="LinkName" label="联系人：" prop="LinkName">
            <a-input placeholder="请输入" v-model="model.LinkName" :maxLength="20" />
          </a-form-model-item>
        </a-col>
        <a-col :span="md">
          <a-form-model-item ref="LinkPhone" label="联系电话：" prop="LinkPhone">
            <a-input placeholder="请输入" v-model="model.LinkPhone" :maxLength="11" />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <!-- 详情展示部分 -->
    <a-card :bordered="false" v-else-if="opType === 4">
      <a-descriptions title="企业信息">
        <a-descriptions-item label="客户名称">{{ model.Name || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="企业类别">{{ model.TypeStr || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="注册地址">{{ model.RegAddress || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="区域分类">{{ model.RegAreaName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="联系人">{{ model.LinkName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label=" 联系电话">{{ model.LinkPhone || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
    </a-card>
  </div>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商品客户七彩认证 基本信息',
  name: 'CusColorCertificationInfo',
  mixins: [SimpleMixin, EditMixin],
  props: {
    /**
     * 1 新增  2 采购编辑 3质管编辑 4详情
     */
    opType: {
      type: Number,
      default: 1,
      required: true,
      validator: (s) => [1, 2, 3, 4].includes(s),
    },
    info: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      md: 8,
      model: {
        Name: '',
        Type: '',
        TypeStr: '',
        RegAddress: '',
        RegArea: '',
        AreaId: '',
        AreaName: '',
        LinkName: '',
        LinkPhone: '',
      },
      Id: '', //已存在的客户ID
      areaValue: '',
      loading: false,
      tipInfo: null, //客户名称查询结果提醒// type  = 1 存在 去查看  2 已建立客户档案 可导入
      supplierInfo: undefined,
    }
  },
  computed: {
    rules() {
      return {
        Name: [{ required: true, validator: this.checkChinese }],
        Type: [{ required: true, message: '请选择!' }],
        RegAddress: [{ required: true, message: '请输入!' }],
        RegAreaCode: [{ required: true, message: '请选择!' }],
        LinkName: [
          {
            required: true,
            validator: (rule, value, callback) => {
              this.checkChinese(rule, value, (res) => {
                if (res != undefined) {
                  callback(res)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 20)
              })
            },
          },
        ],
        LinkPhone: [{ required: true, validator: this.checkTelphone }],
      }
    },
  },
  created() {},
  mounted() {
    if (this.info && this.info.Name) {
      this.model = this.info
      this.areaValue = this.getDefaultValueByCode(this.model.RegAreaCode)

      this.model = JSON.parse(JSON.stringify(this.model))
    }
  },
  methods: {
    /*
     * IsDraft是否存为草稿
     * acType 1 新增  2 采购编辑 3质管编辑 4详情
     */
    onSubmit(callback) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          postAction('/v1/Customer/UpdateCustomerInfo', this.model, 'P36004')
            .then((res) => {
              if (res.IsSuccess) {
                this.$message.success('操作成功!')
              } else {
                this.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              callback && callback()
            })
        } else {
          callback && callback()
          return false
        }
      })
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
    },
  },
}
</script>

<style lang="less" scoped>

.errTip {
  color: #f5222d;
  font-size: 14px;
}
.go-page {
  padding-left: 6px;
  color: #1890ff;
}

/deep/.ant-form-item-control {
  height: 34px;
}
</style>
