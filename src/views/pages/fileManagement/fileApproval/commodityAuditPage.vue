<!-- 商品审核详情-->
<template>
  <a-spin :spinning="loading">
    <!--  -->
    <div v-if="model">
      <a-alert
        type="error"
        :message="`驳回原因：${RejectReason || ' -- '}`"
        banner
        :showIcon="false"
        v-if="RejectReason"
      />
      <a-alert
        type="error"
        :message="`撤回原因：${WithdrawReason || ' -- '}`"
        banner
        :showIcon="false"
        v-if="WithdrawReason"
      />
      <a-card :bordered="false" :bodyStyle="{ padding: '10px 10px 0 10px' }">
        <a-row :gutter="10">
          <a-col :span="5"></a-col>
          <a-col :span="14" style="text-align: center">
            <a-icon
              type="left-circle"
              style="font-size: 18px"
              @click="handleSwitch(1, model.UnAuditLast)"
              v-if="model.UnAuditLast && isEdit"
            />
            <span style="font-weight: bold; font-size: 18px; padding: 0px 20px">{{ model.ErpGoodsName || '--' }}</span>
            <a-icon
              type="right-circle"
              style="font-size: 18px"
              @click="handleSwitch(2, model.UnAuditNext)"
              v-if="model.UnAuditNext && isEdit"
            />
          </a-col>
          <a-col :span="5" style="text-align: right">
            <a-button @click="handleGoBack" style="margin-left: 16px">返回</a-button>
          </a-col>
        </a-row>
        <a-divider style="margin: 5px 0 10px 0" />
        <a-row :gutter="10">
          <a-col :span="14">
            <QualificationsListAudit
              ref="qualificationsListAudit"
              :groupList="qList"
              :isEdit="isEdit"
              :fromType="3"
              :showUploadAndDelete="isEdit"
            />
          </a-col>
          <a-col :span="10">
            <a-tabs v-model="activeTabKey">
              <a-tab-pane :key="1" tab="基础信息" class="card">
                <BasicInfoTemplateForm
                  ref="iBasicInfoTemplateForm"
                  :opType="acType ? 3 : 4"
                  :Info="model"
                  v-if="model && activeTabKey == 1 && IsBasicInfoTemplateForm"
                  :inputMd="12"
                  isAudit
                  :BusinessSerialNumber="BusinessSerialNumber"
                  :Remark="model['Remark']"
                />
              </a-tab-pane>
            </a-tabs>
          </a-col>
        </a-row>
      </a-card>

      <a-card v-if="IsShowBtn" :bordered="false">
        <a-affix :offset-bottom="0" style="float: right; width: 100%; text-align: right">
          <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
            <YYLButton
              menuId="eccd6d5c-529c-478b-b4d8-ffeae960b800"
              type="primary"
              text="通过审核"
              @click="handlePass"
              v-if="IsShowBtn"
            />
            <YYLButton
              menuId="534fec22-b1ae-460d-82ac-5c169ea5e984"
              type="danger"
              text="驳回审核"
              @click="handleNoPass"
              v-if="IsShowBtn"
            />
            <span v-if="!IsShowBtn">无审核权限</span>
          </a-card>
        </a-affix>
      </a-card>
    </div>
    <a-empty v-else />
    <!-- 审核弹框 -->
    <AuditRemarkModal ref="iAuditRemarkModal" @ok="handleAuditModalOk" />
  </a-spin>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'

const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, postAction, putAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'commodityAuditPage',
  mixins: [EditMixin],
  components: { JEllipsis },
  data() {
    return {
      activeTabKey: 1,
      model: null,
      IsBasicInfoTemplateForm: true,
      qList: [], //资质list
      rowData: {},
      loading: false,
      IsHasSuperior: false,
      IsShowBtn: false,
      ApprovalWorkFlowInstanceId: null,
      RejectReason: null,
      WithdrawReason: null,
      BusinessSerialNumber: null,
      acType: 1, //1 审核  0 详情
      httpHead: 'P36006',
      url: {
        detail: '',
        detail3: '/{v}/GoodsManage/GetGoodsSpuAuditInfo',
        getGoodsAdditionalInformationDetail: '/{v}/GoodsManage/GetGoodsAdditionalInformationDetail',
        editGoodsSpu: '/{v}/GoodsManage/Audit',
        getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
        auditOccupy: '/{v}/GoodsManage/AuditOccupy',
        releaseAuditOccupy: '/{v}/GoodsManage/ReleaseAuditOccupy',
      },
    }
  },
  computed: {
    isEdit() {
      return this.acType === 1
    },
  },
  mounted() {
    if (this.$route.query) {
      this.detailId = this.$route.query.id || ''
      this.BusinessSerialNumber = this.$route.query.BusinessSerialNumber || ''
      this.acType = this.$route.query.audit ? this.$route.query.audit * 1 : 0
      this.url.detail = this.url.detail3
      this.setTitle(this.acType == 1 ? '商品审核' : '商品审核详情')
      this.loadDetailInfo()
      if (this.detailId && this.acType == 1) {
        this.auditOccupy()
      }
    }
  },
  created() {},
  methods: {
    /**
     * 占用
     */
    auditOccupy() {
      let that = this
      putAction(that.url.auditOccupy + '?goodsSpuId=' + this.detailId, { goodsSpuId: that.detailId }, that.httpHead)
        .then((res) => {
          if (!res.IsSuccess) {
            // that.$message.error(res.Msg)
            // return
          }
        })
        .finally(() => {})
    },
    //  删除占用缓存
    releaseAuditOccupy() {
      putAction(
        this.url.releaseAuditOccupy + '?goodsSpuId=' + this.detailId,
        { goodsSpuId: this.detailId },
        this.httpHead
      ).then((res) => {
        if (!res.IsSuccess) {
          // this.$message.warning(res.Msg);
          // return;
        }
      })
    },
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      if (this.model) {
        this.model = null
      }
      that.loading = true
      that.IsBasicInfoTemplateForm = false
      getAction(
        that.url.detail,
        { goodsSpuId: that.detailId, businessSerialNumber: this.BusinessSerialNumber },
        that.httpHead
      )
        .then((res) => {
          if (res.IsSuccess) {
            // that.$set(that, 'model', res.Data || {})
            that.model = res.Data || {}
            this.WithdrawReason = res.Data['ReCallReason']
            console.log('GOODS', that.model)
            // 被占用无法操作
            if (that.acType == 1 && that.model['AuditOccupy'] && that.model['AuditOccupy'] != Vue.ls.get(USER_ID)) {
              that.$message.error(`已被${that.model['AuditOccupyName']}占用，不可操作！`)
              this.handleGoBack()

              return
            }

            that.getGoodsAdditionalInformationDetail()
          } else {
            that.$message.error(res.Msg)
            that.loading = false
          }
        })
        .finally(() => {
          // that.loading = false
        })
    },
    // 获取资质详情
    getGoodsAdditionalInformationDetail() {
      let that = this
      // that.loading = true
      getAction(
        that.url.getGoodsAdditionalInformationDetail,
        { goodsSpuId: that.detailId, businessSerialNumber: this.BusinessSerialNumber },
        that.httpHead
      )
        .then((res) => {
          if (res.IsSuccess) {
            if (((res.Data && res.Data['Items']) || []).length) {
              // console.log('Data   ', res.Data)
              that.qList = this.setData(res.Data['Items'])
              // console.log('qList   ', that.qList)

              // console.log(that.qList)
            }
            this.model['Remark'] = (res.Data && res.Data['Remark']) || ''
            this.BusinessSerialNumber = (res.Data && res.Data['BusinessSerialNumber']) || null
            this.getApprovalResults(this.BusinessSerialNumber)
            that.IsBasicInfoTemplateForm = true
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    setData(data) {
      if ((data || []).length < 1) {
        return null
      }
      // "Qualifications": [
      //   {
      //     "SupplierQualificationVersionId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      //     "QualificationCode": "string",
      //     "QualificationName": "string",
      //     "QualificationGroupCode": "string",
      //     "QualificationGroupName": "string",
      //     "QualificationValue": "string",
      //     "QualificationType": 1,
      //     "Sort": 0,
      //     "SupplierQualificationFiles": [
      //       {
      //         "FileType": 1,
      //         "FileUrl": "string"
      //       }
      //     ]
      //   }
      // ]
      data.map((group) => {
        // console.log(group)
        group.QualificationGroupCode = group['GroupCode']
        group.QualificationGroupName = group['GroupName']
        if ((group.Qualifications || []).length) {
          group.Qualifications.forEach((item) => {
            // console.log(item)
            let Urls = []
            if ((item.Urls || []).length) {
              item.Urls.map((v) => {
                Urls.push({
                  FileType: 1,
                  FileUrl: v,
                })
              })
            }
            item['Files'] = Urls
          })
        } else {
          // group['Qualifications'] = [
          //   {
          //     QualificationName: group['GroupName'],
          //     QualificationCode: group['GroupCode'],
          //     QualificationType: 1,
          //     QualificationTypeStr: '图片',
          //     IsMust: group['IsMust'],
          //     IsShow: true
          //   }
          // ]
        }
      })
      return data
    },
    /*
     * 数据切换
     * type = 1 上一条，type = 2 下一条
     */
    handleSwitch(type, id, hasViewType) {
      if (this.detailId) this.releaseAuditOccupy() //删除占用缓存

      this.IsBasicInfoTemplateForm = false
      if (id) {
        this.detailId = id
        this.auditOccupy()
        // 获取详情
        this.BusinessSerialNumber = null
        this.loadDetailInfo(hasViewType)
      } else {
        this.handleGoBack()
      }
    },
    /*
     * 返回列表
     */
    handleGoBack() {
      this.goBack(true,true)
    },
    /**
     * @description: 通过审核
     * @param {*}
     * @return {*}
     */
    handlePass() {
      if (!this.$refs.qualificationsListAudit) {
        this.$message.error('组件发生错误')
        return
      }
      if (this.$refs.qualificationsListAudit.checkData()) {
        let formData = this.$refs.iBasicInfoTemplateForm.onSubmit()
        if (!formData) {
          return
        }
        this.$refs.iAuditRemarkModal.show(1, {
          ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
        })
      }
    },
    /**
     * @description: 驳回审核
     * @param {*}
     * @return {*}
     */
    handleNoPass() {
      let formData = this.$refs.iBasicInfoTemplateForm.onSubmitIsDraft()
      if (!formData) {
        return
      }
      this.$refs.iAuditRemarkModal.show(2, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
        IsHasSuperior: this.IsHasSuperior,
      })
    },
    /**
     * 审核弹框回调
     * @param {*} AuditModalOkformData
     */
    handleAuditModalOk(AuditModalOkformData) {
      if (AuditModalOkformData) {
        this.submit(1, AuditModalOkformData, (bool) => {
          if (bool) {
            let id = this.model.UnAuditNext || this.model.UnAuditLast || null
            this.handleSwitch(null, id, null)
          }
        })
      } else {
        let id = this.model.UnAuditNext || this.model.UnAuditLast || null
        this.handleSwitch(null, id, null)
      }
    },
    // IsHasSuperior
    getApprovalResults(bussinessNo) {
      if (!bussinessNo) return
      getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.IsHasSuperior = res.Data['IsHasSuperior'] //是否有上级 true 有上级 false 没有上级
              this.ApprovalWorkFlowInstanceId = res.Data['ApprovalWorkFlowInstanceId']
              this.RejectReason = res.Data['RejectReason']
              let userId = Vue.ls.get(USER_ID)
              if ((res.Data['ApprovalUserIds'] || []).length && this.acType == 1) {
                this.IsShowBtn = res.Data['ApprovalUserIds'].some((v) => v == userId)
              }
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {})
    },
    /*
     * 提交
     * Type 1 通过，2不通过
     */
    submit(Type, AuditModalOkformData, call) {
      let formData = this.$refs.iBasicInfoTemplateForm.setFormData()
      if (!formData) {
        return
      }
      let groupList = this.$refs.qualificationsListAudit.getData()
      groupList.forEach((group) => {
        group.Qualifications.forEach((item) => {
          if (item.QualificationType == 1) {
            item.Urls = item.Files.map((v) => v.FileUrl)
          }
        })
      })
      formData['GoodsAdditionalInformation'] = {
        Groups: groupList,
      }
      console.log('商品审核-formData', formData)
      formData.AuditStatus = 2
      this.loading = true
      postAction(this.url.editGoodsSpu, formData, this.httpHead).then((res) => {
        this.loading = false

        if (!res.IsSuccess) {
          this.$message.error(res.Msg)
          call && call(false)
          return
        }
        // 调用貌似月审核接口

        this.$refs.iAuditRemarkModal.postAuditInstance(AuditModalOkformData, (bool) => {
          call && call(bool)
        })
      })
    },
  },
  // 离开路由之前执行的函数 删除占用缓存
  beforeRouteLeave(to, from, next) {
    if (this.detailId && this.acType == 1) this.releaseAuditOccupy() //  删除占用缓存
    next()
    return
  },
}
</script>

<style scoped>

</style>
