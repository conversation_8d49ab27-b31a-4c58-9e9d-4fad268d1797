<!-- 商品七彩认证 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="onSetValid"
      @operate="operate"
      @changeTab="changeTab"
      @getListAmount="getListAmount"
    />
    <!-- 查看签章资料 弹窗 -->
    <ViewSignatureInfoModal ref="iViewSignatureInfoModal" :allList="allList" nameKey="GroupName" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商品七彩认证',
  name: 'commodityColorCertificationList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '商品', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },
        {
          name: '商品分类',
          type: 'selectLink',
          vModel: 'GoodsManageClassify2',
          dataKey: { name: 'Name', value: 'Id' },
          httpParams: { Name: '', Level: 2 },
          keyWord: 'Name',
          defaultVal: '',
          placeholder: '请选择',
          httpType: 'POST',
          url: '/{v}/GoodsManage/QueryGoodsManageClassifyList',
          httpHead: 'P36006',
        },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '提交时间',
          type: 'timeInput',
          value: '',
          key: ['SubmitTimeBegin', 'SubmitTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: 0,
        statusKey: '全部', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品分类',
          dataIndex: 'ManageClassifyStr2',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认证状态',
          dataIndex: 'CaAuditStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '提交时间',
          dataIndex: 'SubmitTime',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 250,
          fixed: 'right',
          actionBtn: [
            {
              name: '编辑',
              icon: '',
              id: '6bd78e2e-3069-41ef-904d-6a1e4887391e',
              // 认证失败 显示
              specialShowFuc: (e) => {
                let record = e || {}
                if (record.CaAuditStatus == 4) {
                  return true
                } else {
                  return false
                }
              },
            },
            {
              name: '详情',
              icon: '',
              id: 'c9d4151f-0146-4cc8-b180-fddd460b6ed5',
              // 认证失败 不显示
              specialShowFuc: (e) => {
                let record = e || {}
                if (record.CaAuditStatus != 4) {
                  return true
                } else {
                  return false
                }
              },
            },
            {
              name: '查看签章资料',
              icon: '',
              id: 'b7d99540-0e2a-4f72-906e-4802c7b39acd',
              // 认证通过 显示
              specialShowFuc: (e) => {
                let record = e || {}
                if (record.CaAuditStatus == 3) {
                  return true
                } else {
                  return false
                }
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: true, //是否自动加载
      linkHttpHead: 'P36004',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '/{v}/Goods/QueryGoodsCAList',
        listAmountType: 'POST',
        listAmount: '/{v}/Goods/GetGoodsCACount',
        sub: '/PurchaseOrder/SubmitAsync',
        del: '/PurchaseOrder/DeleteAsync',
        isValidUrl: '/YYDepartment/GetDepartmentList', //若有是否有效需要写这个url
        getGoodsCaAdditionalInformationSignDetailByContrastId:
          '/{v}/Goods/GetGoodsCaAdditionalInformationSignDetailByContrastId',
      },
      allList: [],
    }
  },
  created() {},
  // mounted() {
  //   // this.queryParam['CaAuditStatus'] = 1
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    getListAmount(data) {
      // console.log(data)
      if ((data || []).length) {
        this.$refs.table.tab.statusList = []
        data.map((v) => {
          this.$refs.table.tab.statusList.push({
            name: v['Name'],
            value: v['EnumQueryFilterType'],
            count: v['Count'],
          })
        })
      }
    },
    changeTab(value) {
      this.queryParam = this.$refs.SimpleSearchArea.queryParam

      if (value) this.queryParam.CaAuditStatus = value
      else delete this.queryParam.CaAuditStatus

      this.$nextTick(() => {
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    searchQuery(queryParam, type) {
      if (type == 'searchReset') {
        this.$refs.table.tab.status = 0
      }
      if (!queryParam['GoodsManageClassify2']) delete queryParam['GoodsManageClassify2']
      if (this.$refs.table.tab.status) queryParam['CaAuditStatus'] = this.$refs.table.tab.status
      else delete queryParam['CaAuditStatus']

      this.$refs.table.loadDatas(1, queryParam)
    },

    // 列表操作
    operate(record, type) {
      if (type == '编辑') {
        this.onDetailClick('commodityColorCertificationEdit', {
          id: record.Id,
          opType: 2,
          contrastId: record.ContrastId,
        })
      } else if (type == '详情') {
        this.onDetailClick('commodityColorCertificationDetail', {
          id: record.Id,
          acType: 4,
          contrastId: record.ContrastId,
        })
      } else if (type == '查看签章资料') {
        this.getGoodsCaAdditionalInformationSignDetailByContrastId(record.ContrastId)
      }
    },
    getGoodsCaAdditionalInformationSignDetailByContrastId(ContrastId) {
      getAction(
        this.linkUrl.getGoodsCaAdditionalInformationSignDetailByContrastId,
        { contrastId: ContrastId },
        this.linkHttpHead
      ).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.allList = res.Data['Items'] || []
        this.$refs.iViewSignatureInfoModal.show('goods')
      })
    },
  },
}
</script>

<style></style>
