
<!-- 商品七彩认证编辑 -->
<template>
  <div>
    <a-alert
      type="error"
      :message="`驳回原因：${model['CaAuditOpinion'] || ''}`"
      banner
      :showIcon="false"
      v-if="model && model['CaAuditOpinion']"
    />
    <a-card style="margin:15px 0">
      <a-spin :spinning="spinning">
        <a-steps
          :current="current"
          class="w50-center"
        >
          <a-step title="基本信息" />
          <a-step title="首营信息" />
        </a-steps>
        <a-divider />
        <!-- 基本信息 -->
        <BasicInfoColorCertificationForm
          ref="iBasicInfoColorCertificationForm"
          v-if="current === 0 && ((opType > 1 && model ))"
          :opType="opType"
          :Info="model"
        />
        <!-- 首营信息 -->
        <PrimaryInfoTemplateForm
          ref="iPrimaryInfoTemplateForm"
          v-if="current === 1 && model && id"
          :opType="opType"
          :id="id"
          :erpGoodsId="model['ErpGoodsId']"
          :goodsManageClassifyId="model['ManageClassify2']"
          :hasList="qList"
          :remark="model['Remark']"
          isColorCertification
          :BusinessSerialNumber="BusinessSerialNumber"
          :GoodsAdditionalInformationId="GoodsAdditionalInformationId"
          :AppendInfo="AppendInfo"
        />

        <a-affix
          :offset-bottom="0"
          style="float: right;width: 100%;text-align: right;"
        >
          <a-card
            :bordered="false"
            style="border-top:1px solid #e8e8e8"
          >
            <a-button
              class="mr8"
              v-if="current > 0"
              @click="current = 0"
            >上一步</a-button>

            <!-- <a-button
                type="primary"
                class="mr8"
                v-if="current > 0 && checkBtnPermissions('560c086f-5bb5-44eb-82f0-1878baf5456d')"
                @click="handleSubmit"
              >保存并提交审核</a-button> -->
            <YYLButton
              menuId="560c086f-5bb5-44eb-82f0-1878baf5456d"
              text="保存并提交审核"
              type="primary"
              v-if="current > 0"
              @click="handleSubmit"
            />
            <!-- <a-button
              type="primary"
              class="mr5"
              v-if="current == 0 && checkBtnPermissions('8a23e239-df1a-410a-ae76-70d2e7bae927')"
              @click="handleNext"
            >下一步</a-button> -->
            <YYLButton
              menuId="8a23e239-df1a-410a-ae76-70d2e7bae927"
              text="下一步"
              type="primary"
              v-if="current == 0"
              @click="handleNext"
            />
            <a-button @click="$router.go(-1)">取消</a-button>
          </a-card>

        </a-affix>
      </a-spin>
    </a-card>

  </div>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商品七彩认证编辑',
  name: 'commodityColorCertificationEdit',
  mixins: [EditMixin],
  data() {
    return {
      spinning: false,
      current: 0,
      // model: {
      //   ManageClassify2: '' //'31d8008f-ed37-4bab-9a4b-48dcea356950'
      // },
      model: null,
      BusinessSerialNumber: null,
      GoodsAdditionalInformationId: null,
      AppendInfo: '',
      qList: [], //资质list
      id: '',
      contrastId: '',
      opType: 2, //2编辑
      httpHead: 'P36004',
      url: {
        editGoodsSpu: '/{v}/Goods/EditGoodsSpu',
        detail: '/{v}/Goods/GetGoodsBaseInfoByContrastId',
        // getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
        getGoodsAdditionalInformationDetail: '/{v}/Goods/GetGoodsCaAdditionalInformationDetailByContrastId'
      }
    }
  },
  created() {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.contrastId = this.$route.query.contrastId || ''
      this.opType = this.$route.query.opType ? Number(this.$route.query.opType) : 2
      if (this.opType == 2) {
        this.loadDetailInfo()
      }
    }
  },
  methods: {
    // IsHasSuperior
    // getApprovalResults(bussinessNo) {
    //   if (!bussinessNo) return
    //   getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
    //     .then(res => {
    //       if (res.IsSuccess) {
    //         if (res.Data) {
    //           this.RejectReason = res.Data['RejectReason']
    //         }
    //       } else {
    //         this.$message.error(res.Msg)
    //       }
    //     })
    //     .finally(() => {})
    // },
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.spinning = true
      getAction(that.url.detail, { contrastId: that.contrastId }, that.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            this.AppendInfo = ''
            // that.model.PurchaseTaxRate = that.model.PurchaseTaxRate
            // that.model.SellsTaxRate = that.model.SellsTaxRate
            // that.model.Supplier = that.model.Supplier
            // that.model.PurchaserId = that.model.PurchaserId
            // that.id = that.model.Id
            if (
              this.model.ManageClassifyCode1 == 'YLQX' &&
              this.model.BusinessScopeName &&
              this.model.BusinessScopeName.startsWith('I')
            ) {
              this.AppendInfo = '1'
            }
            if (
              (this.model.ManageClassifyCode2 == 'YP_2' || this.model.ManageClassifyCode2 == 'JKYP_2') &&
              this.model['SupplierType']
            ) {
              this.AppendInfo = '' + this.model['SupplierType']
            }
            if (this.$refs.iBasicInfoColorCertificationForm)
              this.$refs.iBasicInfoColorCertificationForm.model = res.Data
            this.getGoodsAdditionalInformationDetail()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.spinning = false
        })
    },
    setData(data) {
      if ((data || []).length < 1) {
        return null
      }
      data.map(group => {
        if ((group.Qualifications || []).length) {
          group.Qualifications.forEach(item => {
            item['QualificationValue'] = item.Urls
          })
        } else {
          group['Qualifications'] = [
            {
              QualificationName: group['GroupName'],
              QualificationCode: group['GroupCode'],
              QualificationType: 1,
              QualificationTypeStr: '图片',
              IsMust: group['IsMust'],
              IsShow: true
            }
          ]
        }
      })
      return data
    },
    getGoodsAdditionalInformationDetail() {
      let that = this
      that.spinning = true
      getAction(that.url.getGoodsAdditionalInformationDetail, { contrastId: that.contrastId }, that.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            if (((res.Data && res.Data['Items']) || []).length) {
              that.qList = this.setData(res.Data['Items'])
              // console.log('qList   ', that.qList)
            }
            this.model['Remark'] = (res.Data && res.Data['Remark']) || ''
            let BusinessSerialNumber = (res.Data && res.Data['BusinessSerialNumber']) || null
            this.GoodsAdditionalInformationId = (res.Data && res.Data['GoodsAdditionalInformationId']) || null
            this.BusinessSerialNumber = BusinessSerialNumber
            // this.getApprovalResults(BusinessSerialNumber)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.spinning = false
        })
    },

    // 下一步
    handleNext() {
      // this.current = 1
      // return
      let formData = this.$refs.iBasicInfoColorCertificationForm.onSubmit()
      if (!formData) return
      this.model = formData

      this.stepSubmitPost(formData, 'next', () => {
        // console.log(this.id)
        this.current = 1
      })
    },
    stepSubmitPost(formData, act, call) {
      // this.$refs.iBasicInfoColorCertificationForm.spinning = true
      formData['ContrastId'] = this.contrastId
      this.spinning = true
      postAction(this.url.editGoodsSpu, formData, this.httpHead).then(res => {
        // this.$refs.iBasicInfoColorCertificationForm.spinning = false
        this.spinning = false
        if (!res.IsSuccess) {
          this.$message.error(res.Msg)
          return
        }
        this.id = this.id ? this.id : res.Data
        if (act != 'next') {
          this.$message.success('操作成功！')
          setTimeout(() => {
            this.$router.go(-1)
          }, 500)
          return
        }
        // 下一步
        call && call()
      })
    },
    // 保存并审核
    handleSubmit() {
      if (!this.$refs.iPrimaryInfoTemplateForm) {
        return
      }

      //编辑
      // this.spinning = true
      this.$refs.iPrimaryInfoTemplateForm.editSaveData(true, bool => {
        // this.spinning = false
        if (bool) {
          this.$router.go(-1)
        }
      })
    }
  }
}
</script>

