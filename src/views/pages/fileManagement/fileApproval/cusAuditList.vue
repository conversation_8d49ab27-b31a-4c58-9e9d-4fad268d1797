
<!-- 商销客户审核 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="httpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="tab.status=='true' ? columns1: columns"
      :isTableInitData="isTableInitData"
      @onSetValid="onSetValid"
      @operate="operate"
      @changeTab="changeTab"
      @getListAmount="getListAmount"
    />

  </a-row>
</template>

<script>
import Vue from 'vue'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
import { USER_ID } from '@/store/mutation-types'
export default {
  description: '商销客户审核',
  name: 'CusAuditList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchItems: [
        {
          name: '客户',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'KeyWord'
        },
        {
          name: '企业类别',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/v1/Global/GetListDictItemForCustomer',
          key: 'Type',
          searchKey: 'brandName',
          params: { groupPY: 'kehulb' },
          dataKey: { name: 'ItemValue', value: 'Id' }
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName'
        },
        {
          name: '审核人',
          type: SEnum.INPUT,
          key: 'NowAuditByName'
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime'
        }
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: 'true',
        statusKey: 'IsNeedAudit', //标签的切换关键字
        statusList: [
          {
            name: '待审核',
            value:'true',
            count: '',
            key:'NeedAuditCount'
          },
          {
            name: '审核记录',
            value:'false',
            count: '',
            key:'AuditedCount'
          },
        ],
        operateBtn: [
          { name: '释放我的占用', type: '', icon: '', key: 'release', id: '0976adc4-d648-474b-b3fe-9ae1b87654eb' }
        ]
      },
      columns1: [
        {
          title: '客户名称',
          dataIndex: 'Name',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '客户编号',
          dataIndex: 'Code',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '企业类别',
          dataIndex: 'TypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '提交时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '审核人',
          dataIndex: 'NowAuditByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          actionBtn: [
            { name: '审核', icon: '',id:'464b07bb-1a1b-482e-96ae-5a41eb4dbbf9',specialShowFuc: record => {
              return (record.NowAuditBy&&Vue.ls.get(USER_ID)&&record.NowAuditBy==Vue.ls.get(USER_ID))||!record.NowAuditBy
            }},
          ],
          scopedSlots: { customRender: 'action' }
        }
      ],
      columns: [
        {
          title: '客户名称',
          dataIndex: 'Name',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '客户编号',
          dataIndex: 'Code',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '企业类别',
          dataIndex: 'TypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '提交时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          actionBtn: [
            { name: '详情', icon: '', id:'5c3a76cf-fa78-4b37-b942-16c59436ce4b'}
          ],
          scopedSlots: { customRender: 'action' }
        }
      ],
      isTableInitData: true, //是否自动加载
      httpHead: 'P36002',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/v1/CustomerAudit/List',
        listAmount:'/v1/CustomerAudit/Count',
      },
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '审核') {
        this.onDetailClick('CusAuditQualificationDetail', { id: record.Id, isAudit: 1 })
      }else if(type == '详情'){
        this.onDetailClick('CusAuditQualificationDetail', { id: record.Id })
      }else if(type == 'release'){
        this.deleteAllOccupy()
      }
    },
    /**
     * 删除占用
     */
     deleteAllOccupy() {
      getAction('/v1/CustomerAudit/DeleteAllAuditOccupy', {}, this.httpHead).then(res=>{
        if(res.IsSuccess&&res.Data){
          this.$message.success("操作成功");
          this.$refs.table.loadDatas(1, this.queryParam)
        }
      })
    },
    searchQuery(queryParam, type) {
      if(!this.$refs.table){
        return
      }
      if (type == 'searchReset') {
        this.$refs.table.tab.status = 'true'
      }
      queryParam['IsNeedAudit'] = this.$refs.table.tab.status
      this.$refs.table.loadDatas(null, queryParam)
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-table-tbody tr {
  height: 46px;
}
</style>