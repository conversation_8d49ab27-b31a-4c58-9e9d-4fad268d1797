
<!-- 商销客户七彩认证编辑 -->
<template>
  <div>
    <a-alert v-if="info&&info.CaAuditOpinion" type="error" :message="'驳回原因：'+info.CaAuditOpinion" banner :showIcon="false" />
    <a-card style="margin:15px 0">
      <a-spin :spinning="spinning">
        <a-steps :current="curStep" class="w50-center">
          <a-step title="基本信息" />
          <a-step title="首营信息" />
        </a-steps>
        <a-divider />
        <!-- 基本信息 -->
        <CusColorCertificationInfo ref="iCusColorCertificationInfo" :info="info" v-if="(info||acType==1)&&curStep === 0" :opType="acType" />
        <CusFirstCampInformation ref="iCusFirstCampInformation" :hasList="qList" :opType="acType" v-else-if="curStep === 1" :info="info" :remark="remark" />
      </a-spin>
      <a-affix :offset-bottom="0" style="float: right;width: 100%;text-align: right;">
        <a-card :bordered="false" style="border-top:1px solid #e8e8e8">
          <a-button class="mr5" @click="prevStep()" v-if="curStep===1">上一步</a-button>
          <YYLButton menuId="70897319-faa6-442d-be7f-42e3ee35374d" type="primary" class="mr5" v-if="curStep===0" @click="save()" text="下一步" :loading="loading"></YYLButton>
          <YYLButton menuId="001209c1-1240-489a-bc40-51c87fdc8a7b" type="primary" class="mr5" v-if="curStep===1" @click="save(false)" text="保存" :loading="loading"></YYLButton>
          <YYLButton menuId="4d44c874-9622-49b1-bfa7-e383ae15965b" type="primary" class="mr5" v-if="curStep===1" @click="save(true)" text="保存并提交审核" :loading="loading"></YYLButton>
          <a-button @click="$router.go(-1)">取消</a-button>
        </a-card>
      </a-affix>
    </a-card>
  </div>
</template>

<script>
import { getAction, putAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  description: '商销客户七彩认证编辑',
  name: 'cusColorCertificationEdit',
  mixins: [EditMixin],
  data() {
    return {
      spinning: false,
      curStep: 0,
      acType: 1,//1 新增（此界面不使用）  2 采购编辑（此界面不使用） 3质管编辑 4详情  (详情界面只会出现 3 和 4)
      id: '',
      info: undefined,
      qList: [], //资质list
      remark: '',
      loading: false,
    }
  },
  created() { },
  mounted() {
    this.initData()
  },
  computed: {
    isEdit() {
      return this.acType === 3
    }
  },
  methods: {
    initData() {
      if (this.$route.query) {
        this.id = this.$route.query.id || '';
        if (this.id) {
          this.getDetail();
        }

        this.acType = this.$route.query.acType ? Number(this.$route.query.acType) : 1
      }
    },
    getDetail() {
      this.spinning = true;
      getAction('/v1/Customer/Detail', {
        id: this.id
      }, 'P36004').then(res => {
        if (res.IsSuccess) {
          if (res.Data) {
            this.info = res.Data;
            this.info.Id = this.id;
            if (res.Data.QualificationVersion) {
              if (res.Data.QualificationVersion.QualificationGroups && res.Data.QualificationVersion.QualificationGroups.length) {
                this.qList = res.Data.QualificationVersion.QualificationGroups;
              }
              this.remark = res.Data.QualificationVersion.Remarks
            }

          }
        } else {
          this.$message.warning(res.Msg)
        }
      }).finally(() => {
        this.spinning = false;
      })
    },
    save(IsSubmit) {
      if (this.curStep === 0) {
        this.loading = true;
        this.$refs.iCusColorCertificationInfo.onSubmit(() => {
          this.curStep = 1;
          this.loading = false;
        });
      } else {
        this.loading = true
        this.$refs.iCusFirstCampInformation.onSubmitColor(IsSubmit, () => {
          this.loading = false;
        });
      }
    },
    prevStep() {
      this.curStep = 0;
    }
  }
}
</script>

<style lang="less" scoped>

</style>