<!--
 * @Author: LP
 * @Description: 客户审核和审核详情界面
 * @Date: 2023/06/20
-->
<template>
  <!-- :bodyStyle="{ padding: '10px 16px 5px 16px' }" -->
  <a-card :bodyStyle="{ padding: '10px 10px 0 10px' }">
    <a-spin :spinning="loading">
      <template v-if="!model">
        <a-empty />
      </template>
      <CusAuditOrInfoComponents
        v-else
        ref="cusAuditOrInfoComponents"
        :model="model"
        :isEdit="isAudit"
        @onSwitch="handleSwitch"
        @load="getDetailInfo"
      />
    </a-spin>
  </a-card>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { getAction } from '@/api/manage'
export default {
  name: 'CusAuditQualificationDetail',
  mixins: [EditMixin],
  components: {},
  data() {
    return {
      description: '客户审核',
      cusId: '',
      model: null,
      loading: false,
      isAudit: false, //是否是审核 false则表示是详情
      httpHead: 'P36002',
      approvalModel: null,
      url: {
        detail: '/v1/CustomerAudit/Detail',
        auditOccupy: '/v1/CustomerAudit/DeleteAuditOccupy', //审核占用
        approval: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
      },
    }
  },
  mounted() {},
  computed: {},
  created() {
    if (this.$route.query) {
      this.cusId = this.$route.query.id
      this.isAudit = this.$route.query.isAudit == 1 ? true : false
      this.getDetailInfo()
      if (this.isAudit) {
        this.setTitle('商销客户审核')
      } else {
        this.setTitle('商销客户审核详情')
      }
    }
  },
  methods: {
    /**
     * @description: 获取详情
     * @param {*}
     * @return {*}
     */
    getDetailInfo() {
      if (!this.cusId) {
        return
      }
      this.model = undefined
      this.loading = true
      getAction(this.url.detail, { id: this.cusId, isOccupy: this.isAudit }, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.model = res.Data || {}

            if (this.model && this.model.QualificationVersion) {
              this.$set(this.model, 'Remark', this.model.QualificationVersion.Remarks)
            }

            if (this.model && this.model.Invoice) {
              this.$set(this.model, 'Title', this.model.Invoice.Title)
              this.$set(this.model, 'TaxNumber', this.model.Invoice.TaxNumber)
              this.$set(this.model, 'BankName', this.model.Invoice.BankName)
              this.$set(this.model, 'BankCardNumber', this.model.Invoice.BankCardNumber)
              this.$set(this.model, 'BankAccount', this.model.Invoice.BankAccount)
              this.$set(this.model, 'RegPhone', this.model.Invoice.RegPhone)
              this.$set(this.model, 'Email', this.model.Invoice.Email)
            } else {
              this.$set(this.model, 'Title', '')
              this.$set(this.model, 'TaxNumber', '')
              this.$set(this.model, 'BankName', '')
              this.$set(this.model, 'BankCardNumber', '')
              this.$set(this.model, 'BankAccount', '')
              this.$set(this.model, 'RegPhone', '')
              this.$set(this.model, 'Email', '')
            }

            if (this.isAudit) {
              if (this.model.NowAuditBy && this.model.NowAuditBy != this.getLoginUserId()) {
                this.$message.warning('当前客户已被审核人' + (this.model.AuditorName || '') + '占用')
                this.handleGoBack()
              }
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    /**
     * 占用
     */
    auditOccupy(id) {
      getAction(this.url.auditOccupy, { id: id }, this.httpHead)
    },

    handleGoBack() {
      this.$router.go(-1)
    },
    /*
     * 数据切换
     * type = 1 上一条，type = 2 下一条
     */
    handleSwitch(type, id) {
      if (id) {
        this.auditOccupy(this.model.Id)
        this.cusId = id
        // 获取详情
        this.getDetailInfo()
      } else {
        this.handleGoBack()
      }
    },
  },
}
</script>
<style></style>
