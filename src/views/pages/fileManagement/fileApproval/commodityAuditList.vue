<!-- 商品审核 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="returnColumns" :isTableInitData="isTableInitData" @operate="operate" @changeTab="changeTab" @getListAmount="getListAmount" />
  </a-row>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商品审核',
  name: 'commodityAuditList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '商品',
          type: 'input',
          value: '',
          key: 'KeyWord',
          defaultVal: '',
          placeholder: '名称/编号/拼音码',
        },
        {
          name: '商品分类',
          type: 'selectLink',
          vModel: 'GoodsManageClassify2',
          dataKey: { name: 'Name', value: 'Id' },
          httpParams: { Name: '', Level: 2 },
          keyWord: 'Name',
          defaultVal: '',
          placeholder: '请选择',
          httpType: 'POST',
          url: '/{v}/GoodsManage/QueryGoodsManageClassifyList',
          httpHead: 'P36006',
        },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        { name: '审核人', type: 'input', value: '', key: 'AuditorName', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '释放我的占用', type: '', icon: '', key: '释放我的占用', id: '0a572b06-0fd2-4843-ae99-5eb35211b1bb' },
        ],
        hintArray: [],
        status: 1,
        statusKey: '待审核', //标签的切换关键字
        statusList: [
          {
            name: '待审核',
            value: 1,
            count: '',
          },
          {
            name: '审核记录',
            value: 4,
            count: '',
          },
        ],
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品分类',
          dataIndex: 'ManageClassifyStr2',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '提交时间',
          dataIndex: 'SubmitTime',
          width: 150,
          ellipsis: true,
          sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '审核状态',
        //   dataIndex: 'AuditStatusStr',
        //   width: 150,
        //   ellipsis: true,
        //   // sorter: true,
        //   scopedSlots: { customRender: 'component' }
        // }
      ],
      columnsAuditor: [
        {
          title: '审核人',
          dataIndex: 'AuditOccupyName',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columnsAction: [
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtn: [
            {
              name: '审核',
              icon: '',
              id: 'f05407b0-2bec-434f-993b-3177b714aab8',
              specialShowFuc: (e) => {
                let record = e || {}
                if (this.$refs.table.tab.status == 1) {
                  if (record['AuditOccupyId'] && record['AuditOccupyId'] != Vue.ls.get(USER_ID)) {
                    return false
                  }
                  return true
                } else {
                  return false
                }
              },
            },
            {
              name: '详情',
              icon: '',
              id: '70df481e-9bb8-4b91-a927-0618def375b3',
              specialShowFuc: (e) => {
                let record = e || {}
                if (this.$refs.table.tab.status == 0) {
                  return true
                } else {
                  return false
                }
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      returnColumns: [],
      queryParam: {
        AuditStatus: 1,
      },
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36006',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '/{v}/GoodsManage/QueryGoodsAuditList',
        listAmountType: 'POST',
        listAmount: '/{v}/GoodsManage/QueryGoodsAuditListCount',
        releaseAuditOccupyByUser: '/{v}/GoodsManage/ReleaseAuditOccupyByUser',
      },
    }
  },
  created() { },
  mounted() {
    this.queryParam['AuditStatus'] = 1
  },
  activated() {
    this.returnColumns = this.getColumns(this.tab.status)
    console.log(this.queryParam, this.$refs.SimpleSearchArea.queryParam)
    let queryParam = {
      ...this.queryParam,
      ...this.$refs.SimpleSearchArea.queryParam,
    }
    this.$refs.table.loadDatas(null, queryParam)
  },
  methods: {
    getColumns(status) {
      return status == 0
        ? []
          .concat(this.columns)
          .concat([
            {
              title: '审核状态',
              dataIndex: 'AuditStatusStr',
              width: 150,
              ellipsis: true,
              // sorter: true,
              scopedSlots: { customRender: 'component' },
            },
          ])
          .concat(this.columnsAction)
        : [].concat(this.columns).concat(this.columnsAuditor).concat(this.columnsAction)
    },
    getListAmount(data) {
      // console.log(data)
      if ((data || []).length) {
        this.tab.statusList = []
        data.map((v) => {
          this.tab.statusList.push({
            name: v['Name'],
            value: v['AuditStatus'],
            count: v['Count'],
          })
        })
      }
    },
    changeTab(value) {
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      this.queryParam.AuditStatus = value
      this.returnColumns = this.getColumns(value)
      this.$nextTick(() => {
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    searchQuery(queryParam, type) {
      if (type == 'searchReset') {
        this.$refs.table.tab.status = 1
      }
      queryParam['AuditStatus'] = this.$refs.table.tab.status
      if (!queryParam['GoodsManageClassify2']) delete queryParam['GoodsManageClassify2']
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '审核') {
        this.onDetailClick('commodityAuditPage', {
          id: record.Id,
          audit: 1,
          BusinessSerialNumber: record['BusinessSerialNumber'],
        })
      } else if (type == '详情') {
        this.onDetailClick('commodityAuditPage', {
          id: record.Id,
          audit: 0,
          BusinessSerialNumber: record['BusinessSerialNumber'],
        })
      } else if (type == '释放我的占用') {
        this.getReleaseAuditOccupyByUser()
      }
    },
    // 释放我的占用
    getReleaseAuditOccupyByUser() {
      putAction(this.linkUrl.releaseAuditOccupyByUser, {}, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功！')
      })
    },
  },
}
</script>

<style></style>
