<!-- 商销客户七彩认证 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="onSetValid"
      @operate="operate"
      @changeTab="changeTab"
      @getListAmount="getListAmount"
    />
    <ViewSignatureInfoModal ref="iViewSignatureInfoModal" :allList="allList" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销客户七彩认证',
  name: 'cusColorCertificationList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchItems: [
        {
          name: '客户',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'KeyWord',
        },
        {
          name: '是否活动',
          type: SEnum.SELECT,
          key: 'IsValid',
          items: [
            { label: '是', value: true },
            { label: '否', value: false },
          ],
          placeholder: '请选择',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '提交时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: '0',
        statusKey: '', //标签的切换关键字
        statusList: [
          {
            name: '全部',
            value: '',
            count: '',
            key: 'Count0',
          },
          {
            name: '提交失败',
            value: '1',
            count: '',
            key: 'Count1',
          },
          {
            name: '认证中',
            value: '2',
            count: '',
            key: 'Count2',
          },
          {
            name: '认证通过',
            value: '3',
            count: '',
            key: 'Count3',
          },
          {
            name: '认证失败',
            value: '4',
            count: '',
            key: 'Count4',
          },
        ],
      },
      columns: [
        {
          title: '客户名称',
          dataIndex: 'Name',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户编号',
          dataIndex: 'Code',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '注册地址',
          dataIndex: 'RegAddress',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '联系电话',
          dataIndex: 'LinkPhone',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认证状态',
          dataIndex: 'CaAuditStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '提交时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          fixed: 'right',
          actionBtn: [
            {
              name: '编辑',
              icon: '',
              id: '51fc5e12-e8dd-4fc1-a719-906a54f1a6be',
              specialShowFuc: (record) => {
                return record.CaAuditStatus == 1 || record.CaAuditStatus == 4
              },
            },
            {
              name: '详情',
              icon: '',
              id: '156b64fb-b668-4bfe-a543-0c99d7faa38e',
              specialShowFuc: (record) => {
                return record.CaAuditStatus != 1 && record.CaAuditStatus != 4
              },
            },
            {
              name: '查看签章资料',
              icon: '',
              id: 'af411924-8b03-46eb-9b72-790b57cdcc4c',
              specialShowFuc: (record) => {
                return record.CaAuditStatus == 3
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {
        CaAuditStatus: undefined,
      },
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36004',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/v1/Customer/List',
        listAmount: '/v1/Customer/Count',
      },
      allList: [],
    }
  },
  created() {},
  activated() {
    // this.$refs.table.loadDatas(null, this.queryParam)
    this.searchQuery(this.queryParam)
  },
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '编辑') {
        this.onDetailClick('/pages/fileManagement/fileApproval/cusColorCertificationEdit', {
          id: record.Id,
          acType: 2,
        })
      } else if (type == '详情') {
        this.onDetailClick('/pages/fileManagement/fileApproval/cusColorCertificationDetail', {
          id: record.Id,
          acType: 4,
        })
      } else if (type == '查看签章资料') {
        getAction(
          '/v1/Customer/CertFiles',
          {
            id: record.Id,
          },
          'P36004'
        )
          .then((res) => {
            if (res.IsSuccess) {
              if (res.Data && res.Data.length) {
                this.allList = res.Data
                this.$refs.iViewSignatureInfoModal.show()
              } else {
                this.$message.warning('暂无签章资料')
              }
            } else {
              this.$message.warning(res.Msg)
            }
          })
          .finally(() => {
            this.spinning = false
          })
      }
    },
    getListAmount(data) {
      this.tab.statusList = [
        {
          name: '全部',
          value: '0',
          count: data.Count0,
        },
        {
          name: '提交失败',
          value: '1',
          count: data.Count1,
        },
        {
          name: '认证中',
          value: '2',
          count: data.Count2,
        },
        {
          name: '认证通过',
          value: '3',
          count: data.Count3,
        },
        {
          name: '认证失败',
          value: '4',
          count: data.Count4,
        },
      ]
    },
    changeTab(val) {
      if (val == '0') {
        this.queryParam.CaAuditStatus = undefined
      } else {
        this.queryParam.CaAuditStatus = val
      }

      this.$refs.table.loadDatas(1, this.queryParam)
    },
    searchQuery(queryParam, type) {
      if (type == 'searchReset') {
        this.$refs.table.tab.status = '0'
      }
      let status = undefined
      if (this.$refs.table.tab.status != '0') {
        status = this.$refs.table.tab.status
      }
      queryParam['CaAuditStatus'] = status
      this.$refs.table.loadDatas(null, queryParam)
    },
  },
}
</script>

<style></style>
