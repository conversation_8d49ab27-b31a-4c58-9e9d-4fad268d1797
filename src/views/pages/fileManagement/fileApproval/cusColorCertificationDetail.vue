<!--
 * @Author: LP
 * @Description: 商销客户七彩详情
 * @Date: 2023/06/12
-->
<template>
  <a-card>
    <a-tabs v-model="activeTabKey">
      <a-tab-pane :key="1" tab="基础信息">
        <CusColorCertificationInfo ref="CusColorCertificationInfo" :info="info" v-if="info" :opType="acType"/>
      </a-tab-pane>
      <a-tab-pane :key="2" tab="首营信息">
        <CusFirstCampInformation ref="iCusFirstCampInformation" :opType="acType" :info="info" :remark="remark" :hasList="qList" />
      </a-tab-pane>
      <a-tab-pane :key="3" tab="审核信息" v-if="info&&info.Records&&info.Records.length">
        <CusColorAuditInformation ref="CusAuditInformation" :info="info" />
      </a-tab-pane>
    </a-tabs>
    <template v-if="!info && activeTabKey != 3">
      <a-empty />
    </template>
    <a-affix :offset-bottom="10" class="flr">
      <a-button @click="$router.go(-1)">返回</a-button>
    </a-affix>
  </a-card>
</template>
<script>
import { getAction, putAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'cusColorCertificationDetail',
  mixins: [EditMixin],
  data() {
    return {
      activeTabKey: 1,
      model: {},
      remark:'',
      qList: [], //资质list
      id:'',
      info:undefined,
      loading:false,
      acType: 4, //1 新增（此界面不使用）  2 采购编辑（此界面不使用） 3质管编辑 4详情  (详情界面只会出现 3 和 4)
    }
  },
  computed: {
    isEdit() {
      return this.acType === 3
    }
  },
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || '';
      if(this.id){
        this.getDetail();
      }
      
      this.acType = this.$route.query.acType ? Number(this.$route.query.acType) : 4
    }
  },
  created() {},
  methods: {
    getDetail(){
      getAction('/v1/Customer/Detail', {
        id:this.id
      },'P36004').then(res => {
        if (res.IsSuccess) {
          if(res.Data){
            this.info = res.Data;
            this.info.Id = this.id;

            if (res.Data.QualificationVersion && res.Data.QualificationVersion.QualificationGroups) {
              this.qList = res.Data.QualificationVersion.QualificationGroups
            }

            this.remark = res.Data.QualificationVersion? res.Data.QualificationVersion.Remarks || '': ''
          }
        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
    savePage(){
      this.loading = true;
      if(this.activeTabKey == 3){
        this.$refs.cusBusinessScope.onSubmit(()=>{
          this.loading = false;
        })
      }else{
        this.$refs.CusColorCertificationInfo.onSubmit(false,this.acType,this.activeTabKey,()=>{
          this.loading = false;
        })
      }
    }
  }
}
</script>

<style scoped>

</style>
