
<!-- 特殊认款审核 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="httpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="onSetValid"
      @operate="operate"
      @changeTab="changeTab"
      @getListAmount="getListAmount">
      <!-- 促销时间 -->
      <span slot="AgreementStartTimeVal" slot-scope="{text,record}">
        <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
      </span>
    </SimpleTable>
    <!-- 特殊认款审核弹窗 -->
    <SpecialSubsScriptionInfoModal ref="SpecialSubsScriptionInfoModal" @ok="modalOk"></SpecialSubsScriptionInfoModal>
  </a-row>
</template>

<script>
import Vue from 'vue'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
import { USER_ID } from '@/store/mutation-types'
export default {
  description: '特殊认款审核',
  name: 'SpecialSubscriptionAuditList',
  mixins: [SimpleMixin, EditMixin],
  data() {
    return {
      searchItems: [
        {
          name: '认款单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'RecognitionOrderNo'
        },
        {
          name: '折扣类型',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/SystemTool/GetEnumsByName',
          key: 'PurchaseAgreementType',
          params: { enumName: 'EnumSpecialRecognitionRebateType' },
          dataKey: { name: 'ItemDescription', value: 'ItemValue' }
        },
        {
          name: '收款单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'ReceiptNo'
        }, {
          name: '采购订单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'PurchaseOrderNo'
        }, {
          name: '打款方/供应商',
          placeholder: '名称/编号/拼音码',
          type: SEnum.INPUT,
          key: 'PayerKey'
        }, {
          name: '协议编号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'AgreementNo'
        }, {
          name: '甲方',
          placeholder: '名称/编号/拼音码',
          type: SEnum.INPUT,
          key: 'PartyFirstKey'
        }, {
          name: '认款人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'OwnerByName'
        }, {
          name: '创建日期',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime'
        }
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '释放占用', type: '', icon: '', key: '释放占用', }
        ],
        hintArray: [],
        status: '1',
        statusKey: 'ApproavalStatus', //标签的切换关键字
        statusList: [
          {
            name: '待审核',
            value: '1',
            count: '',
            key: 'WaitAuditCount'
          },
          {
            name: '审核记录',
            value: '2',
            count: '',
            key: 'AuditRecordCount'
          },
        ],
      },
      columns: [
        {
          title: '认款单号',
          dataIndex: 'RecognitionOrderNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '折扣类型',
          dataIndex: 'RebateTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '采购订单号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '打款方/供应商',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '认款数量',
          dataIndex: 'TotalRecognitionCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '抵扣金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' }
        },
        {
          title: '归属人',
          dataIndex: 'OwnerByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          sorter: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          fixed: 'right',
          actionBtn: [
            {
              name: '审核', icon: '', id: 'f9b59c2e-fd62-445e-bc79-474ddb743318', specialShowFuc: record => {
                return this.tab.status == '1'
              }
            },
            {
              name: '详情', icon: '', id: '1dd5a45e-a6aa-4974-a3a6-f84f53ae5f31', specialShowFuc: record => {
                return this.tab.status != '1'
              }
            }
          ],
          scopedSlots: { customRender: 'action' }
        }
      ],
      isTableInitData: false, //是否自动加载
      httpHead: 'P36012',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/RecognitionSpecial/GetAuditListAsync',
        listAmount: '/{v}/RecognitionSpecial/GetAuditCountAsync',
        delOccupy: '/{v}/PurchaseAgreement/DeleteAllAuditOccupyAsync',
      },
      queryParam: {
        ApproavalStatus: '1'
      }
    }
  },
  created() { },
  activated() {
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '审核') {
        // 占用
        this.singleOccupyFun('/{v}/RecognitionSpecial/AuditOccupyAsync', {}, 'P36012', record.Id, () => {
          this.$refs.SpecialSubsScriptionInfoModal.show(record, 'audit');
        })
      } else if (type == '详情') {
        this.$refs.SpecialSubsScriptionInfoModal.show(record, 'audit', null, true);
      } else if (type == '释放占用') {
        let that = this
        this.$confirm({
          title: '确定要释放占用吗?',
          content: '',
          onOk() {
            that.delOccupyFun('/{v}/RecognitionSpecial/DeleteAllAuditOccupyAsync', {}, 'P36012')
          },
          onCancel() { },
        });
      }
    },
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.$refs.table.tab.status = '1'
      }
      queryParam['ApproavalStatus'] = this.$refs.table.tab.status
      this.$refs.table.loadDatas(null, queryParam)
    },
    modalOk() {
      this.$refs.table.loadDatas(null, this.queryParam)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-table-tbody tr {
  height: 46px;
}
</style>