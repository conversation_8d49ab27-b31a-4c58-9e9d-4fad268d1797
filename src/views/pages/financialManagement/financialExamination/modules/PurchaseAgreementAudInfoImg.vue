<template>
  <!--
		* 审核组件
		* 包含查看/编辑
	-->
  <a-spin class="page" :spinning="boxLoading">
    <div class="audit-body">
      <!-- 头部左右切换，取消审核，统计 -->
      <div class="audit-body-header" style="padding-top: 16px">
        <!-- 统计 -->
        <div class="flex-row flex-l">
          <div>
            <div class="statistical-tirm">
              待审核数:
              <span>{{ rowData.AllCount }}</span>
            </div>
          </div>
        </div>
        <!-- 切换 -->
        <div class="flex-row flex-c" style="display: flex;flex-direction: row;align-items: center;justify-content: center;">
          <a-icon type="left-circle" @click="handleSwitch(1, rowData.LastId)" v-if="rowData.LastId && isEdit" />
          <div style="margin:0 10px;">
            <j-ellipsis :value="rowData.PartyFirstName" style="margin: 0 16px" />
            <div style="font-size: 16px;">{{ rowData.AgreementNo || '--' }}</div>
          </div>
          <a-icon type="right-circle" @click="handleSwitch(2, rowData.NextId)" v-if="rowData.NextId && isEdit" />
        </div>
        <!-- 返回 -->
        <div class="flex-row flex-r">
          <a-button type="primary" @click="handleReturn(viewType == 'sm' ? 'sm' : 'lg')">
            {{ viewType == 'sm' ? '缩小' : '全屏' }}</a-button>
          <a-button @click="handleGoBack" style="margin-left: 16px" v-if="viewType == 'sm'">返回列表</a-button>
        </div>
      </div>
      <!-- 页面中部，左右划分，左（资质列表，图片查看组件）；右（基础信息，经营范围组件）-->
      <div class="audit-body-main">
        <div class="flex-main-l">
          <a-card class="box-card" size="small">
            <div class="title-text" slot="title">原件照片</div>
            <div class="children-box">
              <!-- 图片资质组件-->
              <QualificationsListComponents ref="qualificationsListComponents" :showTitleCol="false" :row-data="rowData" :isEdit="isEdit" />
            </div>
          </a-card>
        </div>
        <div class="flex-main-r">
          <!-- 基础信息 -->
          <a-card size="small">
            <PurchaseInfoComponents ref="PurchaseInfoComponents" :row-data="rowData" :isEdit="isEdit" />
          </a-card>
          <!-- 通过，不通过 提交按钮 "-->
          <div class="submit-box" v-if="isEdit">
            <a-button style="margin-right: 16px" type="primary" v-if="IsShowBtn" @click="passAuth">通过审核</a-button>
            <a-button type="danger" v-if="IsShowBtn" @click="passNoAuth">审核驳回</a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 审核弹框 -->
    <AuditRemarkModal ref="iAuditRemarkModal" passTitle="提交审核通过备注" noPassTitle="提交审核驳回原因" @ok="handleAuditModalOk" />
  </a-spin>
</template>

<script>
// 引入api请求配置
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import moment from 'moment'
import { postAction, getAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'PurchaseAgreementAudInfoImg',
  components: {
    JEllipsis
  },
  props: {
    // 监听父组件传递的row对象
    rowData: {
      type: Object,
      default: () => { }
    },
    // 全屏 、 缩小 按钮显示
    viewType: {
      type: String,
      default: 'lg'
    },
    // 是否可编辑
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      modalTitle: '提交审核通过备注',
      boxLoading: false,
      model: {
        value: 1,
      },
      userId: '',
      ApprovalWorkFlowInstanceId: '',
      IsHasSuperior: false,//是否有上级
      IsShowBtn: false,
      isAudit: true,
      url: {
        getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
      }
    }
  },
  computed: {
    rules() {
      return {
        Name: [{ required: true, message: "请输入角色!" }],
      };
    },
  },
  created() {
    this.getApprovalResults(this.rowData.Id)
  },
  methods: {
    moment,
    /*
     * 全屏| 缩小|关闭
     */
    handleReturn(type) {
      this.$emit('on-trigger-return', {
        hasViewType: type
      })
    },
    /*
     * 返回列表
     */
    handleGoBack() {
      this.$emit('back')
    },
    handleSwitch(type, id) {
      this.$emit('onSwitch', type, id)
    },
    // IsHasSuperior
    getApprovalResults(bussinessNo) {
      if (!bussinessNo) return
      getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
        .then(res => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.IsHasSuperior = res.Data['IsHasSuperior'] //是否有上级 true 有上级 false 没有上级
              this.ApprovalWorkFlowInstanceId = res.Data['ApprovalWorkFlowInstanceId']
              let userId = Vue.ls.get(USER_ID)
              console.log(userId)
              if ((res.Data['ApprovalUserIds'] || []).length && this.isAudit) {
                this.IsShowBtn = res.Data['ApprovalUserIds'].some(v => v == userId)
              }
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => { })
    },
    // 审核通过
    passAuth() {
      this.$refs.iAuditRemarkModal.show(1, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId
      })
    },
    // 审核拒绝
    passNoAuth() {
      this.$refs.iAuditRemarkModal.show(2, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
        IsHasSuperior: this.IsHasSuperior
      })
    },
    // 审核弹框回调
    handleAuditModalOk(AuditModalOkformData) {
      if (AuditModalOkformData) {
        // 审核通过
        console.log('AuditModalOkformData', AuditModalOkformData)
        this.$refs.iAuditRemarkModal.postAuditInstance(AuditModalOkformData, bool => {
          console.log('bool', bool)
          this.handleSwitch(1, this.rowData.LastId)
        })
      } else {
        // 审核拒绝
        this.handleSwitch(1, this.rowData.LastId)
      }
    },
    handleOk() {
      this.visible = false
    },
    handleCancel() {
      this.visible = false
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .a-tag-header {
  .label-font {
    position: relative;
    padding-left: 10px;
    font-weight: bold;
  }
  .label-font:after {
    position: absolute;
    width: 5px;
    height: 100%;
    background-color: #409eff;
    left: 0;
    top: 0;
    content: '';
  }
}
::v-deep .audit-body {
  .audit-body-header {
    display: flex;
    border-radius: 4px;
    .flex-l {
      flex: 0 0 24%;
    }
    .flex-c {
      flex: 0 0 52%;
      text-align: center;
      font-size: 24px;
      font-weight: bold;
      label {
        padding: 0 30px;
        max-width: 360px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
        vertical-align: bottom;
      }
    }
    .flex-r {
      flex: 0 0 24%;
      text-align: right;
    }
    .statistical-tirm {
      font-size: 16px;
      span {
        color: red;
        font-weight: bold;
        padding: 0 3px;
      }
    }
  }
  .audit-body-main {
    margin: 20px 0 0 0;
    display: flex;
    justify-content: space-between;
    .flex-main-l {
      flex: 0 0 60%;
      width: 60%;
    }
    .flex-main-r {
      width: 39%;
      flex: 0 0 39%;
      // padding: 0 14px;
      border-radius: 4px;
      background-color: #ffffff;
      .submit-box {
        text-align: center;
        margin: 1vh 0;
      }
    }
  }
  .a-card__header {
    padding: 10px 20px !important;
  }
  .a-card__body {
    padding: 12px;
  }
}
.title-text {
  font-weight: 600;
  font-size: 15px;
  color: #333333;
  margin-bottom: 6px;
}
.a-divider--horizontal {
  margin: 15px 0;
}
</style>
