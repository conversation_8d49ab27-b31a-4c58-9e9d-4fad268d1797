<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @getListAmount="getListAmount" @changeTab="changeTab" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'PurchaseAgreementAuditBase',
  mixins: [SimpleMixin],
  props: {
    type: {
      type: Number,
      default: 1, //1年度协议 2短期销售
    },
    actionBtnId: {
      type: Object,
      default: () => {
        return { auth: '1398e9e2-1908-4a07-aeb7-dc3f462f489d', info: 'e0a61819-161e-45ee-8df2-a113d0b4bdd3' }
      },
    },
  },
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '协议编号', type: 'input', value: '', key: 'AgreementNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '甲方',
          type: 'input',
          value: '',
          key: 'PartyFirstKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码',
        },
        { 
          name: '甲方类型',
          type: 'select',
          value: '',
          key: 'PartyFirstType',
          defaultVal: [{ title: '供应商', value: 1 }, { title: '生产厂家', value: 2 }],
          placeholder: '请选择'
        },
        {
          name: '责任人',
          type: 'input',
          value: '',
          key: 'ResponsiblePersonName',
          defaultVal: '',
          placeholder: '请输入',
        },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [{ name: '释放占用', type: '', icon: '', key: '释放占用' }],
        hintArray: [],
        status: '1',
        statusKey: 'ApproavalStatus', //标签的切换关键字
        statusList: [
          {
            name: '待审核',
            value: '1',
            count: 0,
          },
          {
            name: '审核记录',
            value: '2',
            count: 0,
          },
        ],
      },
      columns: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方类型',
          dataIndex: 'PartyFirstTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '核算方式',
          dataIndex: 'AccountingMethodStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利形式',
          dataIndex: 'RebateTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '政策数量',
          dataIndex: 'PolicyCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'ResponsiblePersonName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          fixed: 'right',
          actionBtn: [
            {
              name: '审核',
              icon: '',
              id: this.actionBtnId.auth,
              specialShowFuc: (e) => {
                let record = e || {}
                return this.queryParam.ApproavalStatus == 1
              },
            },
            {
              name: '详情',
              icon: '',
              id: this.actionBtnId.info,
              specialShowFuc: (e) => {
                let record = e || {}
                return this.queryParam.ApproavalStatus == 2
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36007',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/PurchaseAgreement/GetAuditListAsync',
        listAmount: '/{v}/PurchaseAgreement/GetAuditCountAsync',
        delOccupy: '/{v}/PurchaseAgreement/DeleteAllAuditOccupyAsync',
      },
    }
  },
  watch: {
    'tab.status': {
      handler(newVal, oldVal) {
        let auditStatusIndex = this.columns.findIndex(item => {
          return item.title == '审核状态'
        })
        if (newVal == '1') {
          if (auditStatusIndex > -1) {
            this.columns.splice(auditStatusIndex, 1)
          }
        } else {
          if (auditStatusIndex == -1) {
            this.columns.splice(6, 0, {
              title: '审核状态',
              dataIndex: 'AuditStatusStr',
              width: 100,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            })
          }
        }
      },
      immediate: true,
    }
  },
  created() {

  },
  mounted() {

  },
  activated() {
    this.queryParam = this.$refs.SimpleSearchArea.queryParam
    this.queryParam.ApproavalStatus = this.queryParam.ApproavalStatus || 1
    this.queryParam.PurchaseAgreementType = this.type
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    getListAmount(data) {
      this.tab.statusList[0].count = (data && data.WaitAuditCount) || 0
      this.tab.statusList[1].count = (data && data.AuditRecordCount) || 0
    },
    // 删除占用
    delOccupyFun() {
      postAction(this.linkUrl.delOccupy, {}, this.linkHttpHead).then((res) => {
        if (res.IsSuccess && res.Data) {
          this.$message.success('操作成功')
          this.$refs.table.loadDatas(1, this.queryParam)
        }
      })
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      queryParam.ApproavalStatus = this.queryParam.ApproavalStatus
      queryParam.PurchaseAgreementType = this.type
      if (type == 'searchReset') {

      }
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 状态切换
    changeTab(value, statusKey) {
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      this.queryParam.PurchaseAgreementType = this.type
      if (statusKey) {
        this.queryParam[statusKey] = value
      }
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 单个占用
    singleOccupyFun(AuditId, callBack) {
      let url = '/{v}/PurchaseAgreement/AuditOccupyAsync?id=' + AuditId
      postAction(url, {}, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        } else {
          this.$message.success('占用成功')
          callBack && callBack()
        }
      })
    },
    // 列表操作
    operate(record, type) {
      if (type == '审核') {
        this.singleOccupyFun(record.Id, () => {
          this.goPage('/pages/contractManagement/purchaseAuth/purchaseAgreementAudInfo', { type: this.type, id: record.Id })
        })
      } else if (type == '详情') {
        this.goPage('/pages/contractManagement/purchaseAuth/purchaseAgreementAudInfo', { type: this.type, id: record.Id, isInfo: true })
      } else if (type == '释放占用') {
        let that = this
        this.$confirm({
          title: '确定要释放占用吗?',
          content: '',
          onOk() {
            that.delOccupyFun()
          },
          onCancel() { },
        })
      }
    },
  },
}
</script>

<style></style>
