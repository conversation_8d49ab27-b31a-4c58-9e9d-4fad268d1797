<!-- 资质详细信息或审核组件 -->
<template>
  <div v-if="rowData">
    <a-row :gutter="10">
      <a-col :md="24">
        <div class="title-text">基础信息</div>
        <a-descriptions :column="2">
          <template>
            <a-descriptions-item label="甲方">
              {{ rowData.PartyFirstName || ' -- ' }}
            </a-descriptions-item>
            <a-descriptions-item label="甲方类型">
              {{ rowData.PartyFirstTypeStr || ' -- ' }}
            </a-descriptions-item>
            <a-descriptions-item v-if="type == 4" label="是否按照品种设置" >
              {{ rowData.IsLimitGoods ? '是':'否' }}
            </a-descriptions-item>
            <template v-else>
              <a-descriptions-item label="促销时间">
                {{ rowData.AgreementStartTime || ' -- ' }} 至 {{ rowData.AgreementEndTime || ' -- ' }}
              </a-descriptions-item>
              <a-descriptions-item label="核算方式" v-if="type == 1">
                {{ rowData.AccountingMethodStr || ' -- ' }}
              </a-descriptions-item>
              <a-descriptions-item label="指定采购渠道">
                <span v-for="(item,index) in rowData.ChanneList" :key="index">
                  {{ item.ChannelName }}
                  <span v-if="index < rowData.ChanneList.length-1">、</span>
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="流向来源" v-if="rowData.AccountingMethod == 2">
                {{ rowData.FlowDataSourceStr || ' -- ' }}
              </a-descriptions-item>
              <a-descriptions-item label="流向卡" v-if="rowData.AccountingMethod == 2 && rowData.FlowDataSource == 1">
                <template v-if="rowData.FlowDirectionCardList && rowData.FlowDirectionCardList.length">
                  <a-tag v-for="(item, index) in rowData.FlowDirectionCardList" 
                    :key="index" 
                    style="margin-right: 5px">
                    {{ item.FlowDirectionCardCode }}
                  </a-tag>
                </template>
              </a-descriptions-item>
            </template>
            <!-- 通用协议 非按照品种设置 显示-->
            <template v-if="type == 4 && !rowData.IsLimitGoods">
              <a-descriptions-item label="促销内容">
                {{ rowData.PolicyContent || ' -- ' }}
              </a-descriptions-item>
              <a-descriptions-item label="返利金额">
                ￥{{ rowData.RebateAmount || 0 }}
              </a-descriptions-item>
            </template>
          </template>
        </a-descriptions>
      </a-col>

      <!-- 通用协议 按照品种设置  或者非通用协议 显示促销政策-->
      <template v-if="(type == 4 && rowData.IsLimitGoods) || type != 4">
        <div class="title-val" style="margin-left:6px;">促销政策：</div>
        <a-table
          id="resetSetFixedHeight"
          :bordered="true"
          ref="table"
          style="margin-left:5px;"
          :rowKey="(record,index)=>index"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="false"
          :loading="loading"
          @change="handleTableChange"
          :scroll="{ x: dataSource.length === 0 ? false:'100%',y:'350px' }">
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
          <!-- 金额 -->
          <template slot="price" slot-scope="text">
            <j-ellipsis :value="'￥' + (text || 0)" />
          </template>
          <!-- 操作 -->
          <span slot="action" slot-scope="text, record">
            <a style="margin:0 5px;" @click="operate(record,'copy')">详情</a>
          </span>
        </a-table>
      </template>
      <!-- <div class="title-val">返利形式：{{rowData.AgreementRebateTypeStr || '--'}}</div> -->
      <div class="title-val">返利时间：{{ rowData.RebateTime || '--' }}</div>
      <div class="title-text" style="margin-left:6px;margin-bottom:10px;">审核信息</div>
      <div style="margin-left:8px;">
        <CusAuditInformation ref="CusAuditInformation" :approval="{}" v-if="rowData.Id" :AuditId="rowData.Id" />
      </div>
    </a-row>

    <!-- 详情 -->
    <PurchaseDealInfoModal ref="PurchaseDealInfoModal"></PurchaseDealInfoModal>
  </div>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
import { getAction } from '@/api/manage'
import { set } from 'vue'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')

export default {
  name: 'PurchaseInfoComponents',
  mixins: [ListMixin],
  components: {
    JEllipsis
  },
  props: {
    // 监听父组件传递的row对象
    rowData: {
      type: Object,
      default: () => {
        return null
      }
    },
    // 是否可编辑 true 审核  false 详情
    isEdit: {
      type: Boolean,
      default: false
    },
    //类型 1 委托书 2 特殊回执
    // type: {
    //   type: Number,
    //   default: () => {
    //     return 1
    //   }
    // }
  },
  data() {
    return {
      longTimeStr: '2999-12-31',
      submitModel: {
        ValidityBeginDate: null,
        ValidityBeginDate2: null,
        ValidityDate: null,
        ValidityDate2: null,
      },
      fileTypeObj1: {},
      fileTypeObj2: {},
      type: null, //1年度协议 2短期销售协议
      isLongValid: false,
      httpHead: 'P31406',
      rangeDate: [],
      rangeDate2: [],
      columns: [],
      columnsStart: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          // fixed: "left",
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        }
      ],
      columns1: [
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          width: 150,
          ellipsis: false,
          // fixed: "right",
          customRender: (text, record, index) => {
            const obj = {
              children: (<j-ellipsis value={String(text) || ''} />),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '返利形式',
          dataIndex: 'AgreementRebateType',
          width: 100,
          ellipsis: false,
          // fixed: "right",
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: (<j-ellipsis value={String(text) || ''} />),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          fixed: "right",
          customRender: (text, record, index) => {
            let childrenVal
            childrenVal = (
              <div>
                <a onclick={() => this.operate(record, 'info', index)}>详情</a>
              </div>
            )
            const obj = {
              children: childrenVal,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        }
      ],
      columns2: [
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          width: 300,
          ellipsis: false,
          customRender: (text, record, index) => {
            const obj = {
              children: (<j-ellipsis value={String(text) || ''} />),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '返利形式',
          dataIndex: 'AgreementRebateType',
          width: 100,
          ellipsis: false,
          // scopedSlots: { customRender: 'component' },
          // fixed: "right",
          customRender: (text, record, index) => {
            const obj = {
              children: (<j-ellipsis value={String(text) || ''} />),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          // fixed: "right",
          customRender: (text, record, index) => {
            let childrenVal
            childrenVal = (
              <div>
                <a onclick={() => this.operate(record, 'info', index)}>详情</a>
              </div>
            )
            const obj = {
              children: childrenVal,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        }
      ],
      url: {
        ckOrder: '/{v}/Order/GetSpecialOrderOutboundBill'
      }
    }
  },
  watch: {},
  computed: {
    rules() {
      return {
        ValidityDate: [{ required: true, message: '请选择!' }],
        AuditReason: [{ required: false, message: '请输入!' }]
      }
    }
  },
  mounted() {
    this.type = this.$route.query.type
    this.initSubmitModel()
    this.initPolicyTable()
  },
  methods: {
    moment,
    initPolicyTable() {
      // console.log('type   ',this.type )
      if (this.type == 4) {
        this.columns = this.columnsStart.concat(
          [
            {
              title: '商品编号',
              dataIndex: 'ErpGoodsCode',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '返利金额',
              dataIndex: 'RebateAmount',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'price' },
            },
          ]
        )

        this.dataSource = this.rowData.RebateGoods || []
        // console.log('dataSource   ',this.dataSource )
        return
      }
      // 重组数据
      if (this.rowData.AccountingMethod == 3) {
        // 打款金额表格列
        // this.columns = this.columns2
        this.columns = this.columnsStart.concat(this.columns1)
      } else {
        // 购进和销售表格列
        this.columns = this.columnsStart.concat(this.columns1)
      }
      this.setCxTable()
    },
    // 还原促销政策表格
    setCxTable() {
      if (this.rowData.PolicyList && this.rowData.PolicyList.length > 0) {
        let array = []
        this.rowData.PolicyList.map(item => {
          if (JSON.stringify(item.GoodsList) == '{}' || item.GoodsList.length == 0) {
            item.AgreementRebateType = item.PolicyRebateMethod == 4 ? '返货' : '返钱'
            item.GoodsList = [{}]
            array.push(item)
          } else {
            item.GoodsList.map((rItem, rIndex) => {
              if (rIndex == 0) {
                rItem.rowSpan = item.GoodsList.length
              } else {
                rItem.rowSpan = 0
              }
              rItem.action = ''
              rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
              rItem.PolicyContent = item.PolicyContent
              rItem.AgreementRebateType = item.PolicyRebateMethod == 4 ? '返货' : '返钱'
              rItem.PurchaseAgreementPolicyConfigId = item.PurchaseAgreementPolicyConfigId
              array.push(rItem)
            })
          }
        })

        this.dataSource = array
        this.resetSetFixedHeight(this.dataSource,'resetSetFixedHeight')
      }

    },
    initSubmitModel() {
      this.submitModel = {
        ValidityBeginDate: this.rowData.ValidityBeginDate ? this.rowData.ValidityBeginDate : null,
        ValidityDate: this.rowData.ValidityDate ? this.rowData.ValidityDate : null,
        LongTimeCheck: false,
        AuditReason: '',
        AuditStatus: 3
      }
      if (this.rowData.tabList && this.rowData.tabList.length > 0) {
        this.rowData.tabList.map((item) => {
          // 委托书信息
          if (item.FileType == 1) {
            this.fileTypeObj1 = item
            this.submitModel.ValidityBeginDate = item.ValidityBeginDate
            this.submitModel.ValidityDate = item.ValidityDate
            this.rangeDate = [moment(item.ValidityBeginDate), moment(item.ValidityDate)]
          }
          // 身份证信息
          if (item.FileType == 2) {
            this.fileTypeObj2 = item
            if (item.ValidityDate == '2999-12-31' || item.ValidityDate == '2999-12-31 23:59:59') {
              this.isLongValid = true
            } else {
              this.isLongValid = false
            }
            this.submitModel.ValidityBeginDate2 = item.ValidityBeginDate
            this.submitModel.ValidityDate2 = item.ValidityDate
            this.rangeDate2 = [moment(item.ValidityBeginDate), moment(item.ValidityDate)]
            this.submitModel.Name = item.Name
          }

        })
      }
    },
    operate(record, type, index) {
      // 选择品种
      if (type == 'info') {
        let clickPolicyObj = this.setClickPolicyObj(record)
        this.$refs.PurchaseDealInfoModal.show(record, clickPolicyObj, this.rowData)
      }
    },
    setClickPolicyObj(record, setGoodsArray) {
      // 重组政策的表格
      let goodsArray = []
      if (this.rowData.PolicyList && this.rowData.PolicyList.length > 0) {
        this.rowData.PolicyList.map((item, index) => {
          item.GoodsList.map((rItem) => {
            if (rItem.length === 0 || JSON.stringify(rItem) === '{}') {
              rItem = item
              rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
              rItem.index = index
              goodsArray.push(rItem)
            } else {
              rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
              rItem.index = index
              goodsArray.push(rItem)
            }

          })
        })
      }
      if (setGoodsArray) {
        this.rowData.dataSource = goodsArray
      }

      let PolicyIndex = null
      let clickPolicyObj = {}
      if (this.rowData.PolicyList && this.rowData.PolicyList.length > 0) {
        for (let i = 0; i < this.rowData.PolicyList.length; ++i) {
          if (record.PurchaseAgreementPolicyId == this.rowData.PolicyList[i].PurchaseAgreementPolicyId) {
            PolicyIndex = record.index
            break
          }
        }
        clickPolicyObj = this.rowData.PolicyList[PolicyIndex]
      }
      return clickPolicyObj
    },

  }
}
</script>

<style lang="scss" scoped>
::v-deep .a-tag-header {
  .label-font {
    position: relative;
    padding-left: 10px;
    font-weight: bold;
  }
  .label-font:after {
    position: absolute;
    width: 5px;
    height: 100%;
    background-color: #409eff;
    left: 0;
    top: 0;
    content: '';
  }
}
::v-deep .a-tag-box {
  margin: 0 8px 8px 0;
  .a-tag-box-type {
    span {
      display: block;
      text-align: center;
      padding: 10px 5px;
      border: 1px solid #ecf5ff;
      background-color: #ecf5ff;
      border-radius: 4px;
      margin-bottom: 10px;
      color: #409eff;
      cursor: pointer;
    }
    span:last-child {
      margin-bottom: 0;
    }
    .active {
      background-color: #409eff !important;
      color: #ffffff !important;
      font-weight: bold;
    }
    .warning {
      color: red !important;
      background-color: #fff0f0 !important;
      border: 1px solid #fff0f0 !important;
    }
    .warningActive {
      color: #ffffff !important;
      background-color: #f56c6c !important;
      border: 1px solid #f56c6c !important;
    }
  }
  .preview-box {
    margin: 0 0 10px 0;
  }
  .a-carousel__item h3 {
    color: #475669;
    font-size: 14px;
    opacity: 0.75;
    line-height: 150px;
    margin: 0;
  }
  .flex-box {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .text-center {
    text-align: center;
  }
}
.title-text {
  font-weight: 600;
  font-size: 15px;
  color: #333333;
  margin-bottom: 6px;
}
.title-val {
  margin: 5px 0 5px 6px;
}
.a-icon-circle-close {
  color: #000000 !important;
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 16px;
}
.container {
  height: 60vh;
  position: relative;
  overflow: hidden;
  .page-prev {
    position: absolute;
    top: 28vh;
    left: 5px;
    z-index: 999;
    border-radius: 100%;
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 30px;
    box-shadow: 0 0 10px #ccc;
    background: #ffffff;
    cursor: pointer;
  }
  .page-next {
    position: absolute;
    top: 28vh;
    right: 5px;
    z-index: 999;
    border-radius: 100%;
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 30px;
    box-shadow: 0 0 10px #ccc;
    background: #ffffff;
    cursor: pointer;
  }
  .container-box {
    height: 58vh;
  }
  .container-page {
    text-align: center;
    span {
      display: inline-block;
      background: #dcdfe6;
      color: #dcdfe6;
      margin: 0 6px;
      height: 4px;
      width: 25px;
    }
    .page-active {
      background: #409eff;
      color: #409eff;
    }
  }
}
</style>
