<!-- 管理授权品种 -->
<template>
  <a-modal :footer="null" :title="title" :width="1000" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        // padding: '10px 16px',
        background: '#fff',
      }">
      <a-descriptions title="基本信息" :column="3">
        <a-descriptions-item label="认款单号">{{ model.Name || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="抵扣类型">{{ model.TypeStr || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="归属人">{{ model.SecondTypeStr || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ model.LegalPerson || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
      <div class="des-title">返货采购订单：</div>
      <a-table :bordered="false" ref="table" rowKey="Id" size="small" :columns="columns" :dataSource="dataSource" :pagination="false" :scroll="{ y: '300px', x: '100%' }">
        <!-- 字符串超长截取省略号显示-->
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </span>
      </a-table>
      <div class="des-title">本次认款明细：(本次合计认款数量：XXXX)</div>
      <a-table :bordered="false" ref="table" rowKey="Id" size="small" :columns="columns1" :dataSource="dataSource" :pagination="false" :scroll="{ y: '300px', x: '100%' }">
        <!-- 字符串超长截取省略号显示-->
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </span>
      </a-table>
      <div class="des-title">抵扣认款单：</div>
      <a-table :bordered="false" ref="table" rowKey="Id" size="small" :columns="columns2" :dataSource="dataSource" :pagination="false" :scroll="{ y: '300px', x: '100%' }">
        <!-- 字符串超长截取省略号显示-->
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </span>
      </a-table>
      <a-descriptions :column="1" class="des-title">
        <a-descriptions-item label="本次抵扣金额">{{ model.Name || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="备注">{{ model.TypeStr || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
    </div>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
import moment from "moment";

export default {
  name: "SpecialSubscriptionAuditInfoModal",
  mixins: [ListMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "审核特殊认款单",
      visible: false,
      model: {},
      confirmLoading: false,
      httpHead: 'P36001',
      isTableInitData:false,
      linkUrlType:'GET',
      IsAdd:false,
      url: {
        list: '',
      },
      queryParam:{
        SupplierId:'',
        UserId:'',
        GoodsKey:'',
        IsAdd:false
      },
      columns: [
          {
            title: '采购订单',
            dataIndex: 'ErpGoodsName',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '供应商',
            dataIndex: 'PackingSpecification',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '返货数量',
            dataIndex: 'PackageUnit',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '未认款数量',
            dataIndex: 'BrandManufacturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
      ],
      columns1: [
          {
            title: '商品名称',
            dataIndex: 'ErpGoodsName',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '规格',
            dataIndex: 'PackingSpecification',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '单位',
            dataIndex: 'PackageUnit',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '生产厂商',
            dataIndex: 'BrandM1anufa3cturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '商品编号',
            dataIndex: 'BrandManu2facturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '返货数量',
            dataIndex: 'BrandManufa2cturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '本次认款数量',
            dataIndex: 'BrandManuf1acturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
      ],
      columns2: [
          {
            title: '认款单编号',
            dataIndex: 'ErpGoodsName',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '甲方',
            dataIndex: 'PackingSpecification',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '促销时间',
            dataIndex: 'PackageUnit',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '核销方式',
            dataIndex: 'BrandM1anufa3cturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '现金应收金额',
            dataIndex: 'BrandManu2facturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
      ],
    };
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record) {
      this.url.list = '/{v}/SupplierSystem/QueryUserRelationSupplierGoodses';
      this.queryParam.SupplierId = record.SupplierId;
      this.queryParam.UserId = record.UserId;
      this.visible = true;
      this.$nextTick(()=>{
        // this.$refs.table.loadDatas(1,this.queryParam);
      })
    },
    modalOk(data, updateIndex, type) {
      if (updateIndex == null) {
        this.dataSource.push(data)
      } else if (updateIndex !== null && type == 'sort') {
        let array = this.dataSource
        let index = updateIndex
        if (array[index + 1]) {
          array.splice(index + 1, 1, data, array[index + 1])
        } else {
          this.dataSource.push(data)
        }

      } else {
        this.dataSource.splice(updateIndex, 1, data)
      }
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
<style scoped>
.title-box {
  border: 1px solid #e8e8e8;
  border-bottom: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 15px;
}

.des-title{
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.85);
  padding: 20px 0 10px 0;
}
</style>
