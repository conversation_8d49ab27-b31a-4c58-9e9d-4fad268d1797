<template>
  <div>
    <a-spin :spinning="confirmLoading">
      <div style="background: #fbe6e9; padding: 10px 16px; color: red" v-if="model.SalesOffsetInfo && (model.SalesOffsetInfo.AuditStatus == 3 && model.SalesOffsetInfo.AuditOpinion)" :md="24">
        <span>驳回原因：{{ model.SalesOffsetInfo.AuditOpinion || '' }}</span>
      </div>
      <a-card size="small" style="margin-bottom: 10px" :bodyStyle="{ padding: '16px' }" title="基础信息">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="8">
              <a-form-model-item label="对抵方：" prop="PartyId">
                <SingleInputSearchView placeholder="请输入对抵方名称" :disabled="isDisabled" width="100%" :httpParams="{
                    pageIndex: 1,
                    pageSize: 100,
                    IsValid: true,
                  }" keyWord="KeyWord" :dataKey="{ name: 'SupplierName', value: 'PartyId' }" :httpHead="httpHead" :Url="url.supplierList" :value="model.PartyId" :name="model.PartyName" :isAbsolute="true" @clear="
                    () => {
                      model.PartyId = ''
                      ;(model.PartyName = ''), (dataSource0 = [])
                    }
                  " @change="(val, txt, item) => changeSearchInput(val, txt, item, 'PartyName')" />
              </a-form-model-item>
            </a-col>
            <a-col :md="8">
              <a-form-model-item style="margin: 0 15px" label="业务员：" prop="SalesId">
                <SingleInputSearchView placeholder="请选择" width="100%" :httpParams="{
                    pageIndex: 1,
                    pageSize: 100,
                    IsValid: true,
                  }" keyWord="KeyWord" :disabled="isDisabled" :dataKey="{ name: 'Name', value: 'Id' }" :httpHead="httpHead" :Url="url.salesmanList" :value="model.SalesId" :name="model.SalesName" :isAbsolute="true" @clear="
                    () => {
                      model.SalesId = ''
                      model.SalesName = ''
                    }
                  " @change="(val, txt, item) => changeSearchInput(val, txt, item, 'SalesName')" />
              </a-form-model-item>
            </a-col>
            <a-col :md="8">
              <a-form-model-item style="position: relative;" label="对抵金额：" prop="OffsetAmount">
                <span style="color:red;position: absolute;top: -29px;left: 86px;">最大对抵金额：{{maxCounterMoney || 0}}</span>
                <div>
                  <a-input-number placeholder="请输入" v-model="model.OffsetAmount" :disabled="isDisabled" style="width: 92%" @blur="blurInput" :min="0" :max="99999999999.99" />
                  <a @click="seduceOrder" :disabled="isDisabled" style="margin-left: 2%">勾单</a>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
        <a-table :bordered="false" ref="table" :rowKey="(record, index) => index" size="small" :columns="columns0" :dataSource="dataSource0" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource0.length > 0 ? '50px' : false, x: '100%' }">
          <!-- 序号 -->
          <span slot="Order" slot-scope="text, record, order">
            {{ getOrder(record, order) }}
          </span>
          <!-- 字符串超长截取省略号显示-->
          <span slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </span>
          <!-- 价格显示-->
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text ? '¥' + text : text == 0 ? '0' : '--'" />
          </span>
        </a-table>
      </a-card>
      <!-- 应付明细 -->
      <a-card style="margin-bottom: 10px" :bodyStyle="{ padding: '16px' }" title="应付明细">
        <div slot="extra">
          <a-button style="margin-right: 10px" v-if="!isRowSelect" :disabled="isDisabled" @click="batchRemoveData(1, 'batchDel')">批量移除</a-button>
          <a-button type="primary" style="margin-right: 10px" v-if="isRowSelect" :disabled="isDisabled" @click="subBatchRemoveData(1)">确定批量移除</a-button>
          <a-button style="margin-right: 10px" v-if="isRowSelect" :disabled="isDisabled" @click="batchRemoveData(1, 'cancelBatchDel')">取消批量移除</a-button>
          <a-button type="primary" :disabled="isDisabled" @click="detailSet(1)">应付明细调整</a-button>
        </div>
        <a-table :bordered="false" ref="table" rowKey="BillId" size="small" :columns="columns" :dataSource="dataSource" :pagination="false" :rowClassName="rowClassNameFun" :row-selection="rowSelectionInfo" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length > 0 ? '300px' : false, x: '100%' }">
          <!-- 序号 -->
          <span slot="Order" slot-scope="text, record, order">
            {{ getOrder(record, order) }}
          </span>
          <!-- 字符串超长截取省略号显示-->
          <span slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </span>
          <!-- 价格显示-->
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
          </span>
          <!-- 操作 -->
          <span slot="action" slot-scope="text, record, index">
            <a-popconfirm title="您确定要移除当前单据吗?" ok-text="确定" cancel-text="取消" @confirm="removeData(1, record, index)">
              <a :disabled="isDisabled"> 移除 </a>
            </a-popconfirm>
          </span>
        </a-table>
        <div class="num-box" style="margin-top: 10px">
          <span>合计数量：{{ model.listObj1.TotalCount || '0' }}条</span>
          <span>应付对抵合计：¥{{ model.listObj1.PayableOffsetAmount || '0' }}</span>
          <span>采购退货：<span style="color: red">¥{{ model.listObj1.RefundOrderAmount || '0' }}</span></span>
          <span>差价调整：<span :style="{ color: model.listObj1.PriceAdjustmentAmount < 0 ? 'red' : '' }">¥{{ model.listObj1.PriceAdjustmentAmount || '0' }}</span></span>
          <span>订单票折：<span style="color: red">¥{{ model.listObj1.InvoiceDiscountAmount || '0' }}</span></span>
          <!-- <span>买赔入库：¥{{model.listObj1.BuyCompensationAmount || '0'}}</span> -->
        </div>
      </a-card>
      <!-- 应收明细 -->
      <a-card style="margin-bottom: 10px" :bodyStyle="{ padding: '16px' }" title="应收明细">
        <div slot="extra">
          <a-button style="margin-right: 10px" v-if="!isRowSelect2" :disabled="isDisabled" @click="batchRemoveData(2, 'batchDel')">批量移除</a-button>
          <a-button type="primary" style="margin-right: 10px" v-if="isRowSelect2" :disabled="isDisabled" @click="subBatchRemoveData(2)">确定批量移除</a-button>
          <a-button style="margin-right: 10px" v-if="isRowSelect2" :disabled="isDisabled" @click="batchRemoveData(2, 'cancelBatchDel')">取消批量移除</a-button>
          <a-button type="primary" :disabled="isDisabled" @click="detailSet(2)">应收明细调整</a-button>
        </div>
        <a-table :bordered="false" ref="table" rowKey="BillId" size="small" :columns="columns2" :dataSource="dataSource2" :pagination="false" :rowClassName="rowClassNameFun" :row-selection="rowSelectionInfo2" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource2.length > 0 ? '300px' : false, x: '100%' }">
          <!-- 序号 -->
          <span slot="Order" slot-scope="text, record, order">
            {{ getOrder(record, order) }}
          </span>
          <!-- 字符串超长截取省略号显示-->
          <span slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </span>
          <!-- 操作 -->
          <span slot="action" slot-scope="text, record, index">
            <a-popconfirm title="您确定要移除当前单据吗?" ok-text="确定" cancel-text="取消" @confirm="removeData(2, record, index)">
              <a :disabled="isDisabled"> 移除 </a>
            </a-popconfirm>
          </span>
        </a-table>
        <div class="num-box" style="margin-top: 10px">
          <span>合计数量：{{ model.listObj2.TotalCount || '0' }}条</span>
          <span>应收对抵合计：¥{{ model.listObj2.PayableOffsetAmount || '0' }}</span>
          <span>商销退货：<span style="color: red">¥{{ model.listObj2.RefundOrderAmount || '0' }}</span></span>
          <span>差价调整：<span :style="{ color: model.listObj2.PriceAdjustmentAmount < 0 ? 'red' : '' }">¥{{ model.listObj2.PriceAdjustmentAmount || '0' }}</span></span>
        </div>
      </a-card>
      <!-- 备注信息 -->
      <a-card size="small" style="margin-bottom: 10px" :bodyStyle="{ padding: '16px' }" title="备注信息">
        <a-textarea placeholder="请输入备注信息" v-model="model.Remark" @change="onRemarkChange" :disabled="isDisabled" :rows="4" :maxLength="100" />
      </a-card>
      <!-- 审核信息 -->
      <a-card size="small" style="margin-bottom: 10px" :bodyStyle="{ padding: '16px' }" title="审核信息">
        <CusAuditInformation ref="CusAuditInformation" :approval="{}" v-if="model.SalesOffsetInfo && model.SalesOffsetInfo.AuditId" :AuditId="model.SalesOffsetInfo.AuditId" />
        <a-empty v-else />
      </a-card>

      <a-card>
        <a-row :style="{ textAlign: 'right' }">
          <a-button :style="{ marginRight: '8px' }" @click="handleCancel">
            {{ isDisabled ? '返回' : '取消' }}
          </a-button>
          <a-button :style="{ marginRight: '8px' }" v-if="!isDisabled" :disabled="confirmLoading" @click="handleOk(true)" type="primary">保存</a-button>
          <a-button v-if="!isDisabled" :disabled="confirmLoading" @click="handleOk(false)" type="primary">保存并提交审核</a-button>
        </a-row>
      </a-card>
    </a-spin>
    <!-- 明细调整 -->
    <SalesButtApplicationTableModal ref="SalesButtApplicationTableModal" @ok="modalOk"></SalesButtApplicationTableModal>
  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
const columns0 = [
  // {
  //   title: '总余额',
  //   dataIndex: 'TotalBalance',
  //   width: 150,
  //   ellipsis: true,
  //   scopedSlots: { customRender: 'price' },
  // },
  {
    title: '应收余额',
    dataIndex: 'ReceivableBalance',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '应付余额',
    dataIndex: 'PayableBalance',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '预付余额',
    dataIndex: 'PrepaymentBalance',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '预收余额',
    dataIndex: 'AdvancePaymentBalance',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
]
const columns = [
  {
    title: '单据编号',
    dataIndex: 'BillNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '创建人',
    dataIndex: 'CreateByName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '单据类型',
    dataIndex: 'BusinessOrderStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '对应订单编号',
    dataIndex: 'OrderNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单付款方式',
    dataIndex: 'PaymentModeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '发生日期',
    dataIndex: 'TransactionTime',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '应付金额',
    dataIndex: 'PayableAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '剩余应付金额',
    dataIndex: 'ResiduePayableAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '本次对抵金额',
    dataIndex: 'OffsetAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 100,
    fixed: 'right',
    scopedSlots: { customRender: 'action' },
  },
]
const columns2 = [
  {
    title: '单据编号',
    dataIndex: 'BillNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '创建人',
    dataIndex: 'CreateByName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '单据类型',
    dataIndex: 'BusinessOrderStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '对应订单编号',
    dataIndex: 'OrderNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单收款方式',
    dataIndex: 'PaymentModeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '发生日期',
    dataIndex: 'TransactionTime',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '应收金额',
    dataIndex: 'PayableAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '剩余应收金额',
    dataIndex: 'ResiduePayableAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '本次对抵金额',
    dataIndex: 'OffsetAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 100,
    fixed: 'right',
    scopedSlots: { customRender: 'action' },
  },
]

export default {
  name: 'SalesButtApplicationAddBase',
  title: '新增销货对抵',
  mixins: [ListMixin, EditMixin],
  components: {
    JEllipsis,
  },
  data() {
    return {
      model: {
        PartyId: '',
        PartyName: '',
        SalesId: '',
        SalesName: '',
        listObj1: {}, //应付明细
        listObj2: {}, //应收明细
        SalesOffsetInfo: {},
      },
      columns0: columns0,
      columns: columns,
      columns2: columns2,
      confirmLoading: false,
      maxCounterMoney: 0,//最大对抵金额
      uploadText: '上传图片',
      listImageUrl: [],
      dataSource0: [],
      dataSource: [],
      dataSource2: [],
      selectedRowKeys: [],
      selectedRowKeys2: [],
      selectedRowKeysBusiness: [],
      selectedRowKeysBusiness2: [],
      delArray: [],
      delArray2: [],
      isRowSelect: false,
      isRowSelect2: false,
      isClickOrder: false, //应付明细是否勾单
      isClickOrder2: false, //应收明细是否勾单
      id: '',
      isEdit: false,
      isInfo: false,
      isDisabled: false,
      IsDraft: null, //是否是草稿
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
      httpHead: 'P36012',
      url: {
        supplierList: '/{v}/SalesOffset/GetOffsetPartyList',
        salesmanList: '/{v}/SalesOffset/GetSaleMan',
        add: '/{v}/SalesOffset/Create',
        update: '/{v}/SalesOffset/Update',
        list1: '/{v}/SalesOffset/GetOffsetPayableBill',
        list2: '/{v}/SalesOffset/GetOffsetReceivableBill',
        getTotalBalanceAsync: '/{v}/Account/GetTotalBalanceAsync',
        info: '/{v}/SalesOffset/Detail',
        createAudit: '/{v}/ApprovalWorkFlowInstance/CreateApproval',
        maxCounterMoney: '/{v}/SalesOffset/GetMaxOffsetAmount',
      },
    }
  },
  computed: {
    rules() {
      return {
        PartyId: [{ required: true, message: '请选择对抵方!' }],
        SalesId: [{ required: true, message: '请选择业务员!' }],
        OffsetAmount: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请输入对抵金额!'))
              } else if (this.maxCounterMoney && (value > this.maxCounterMoney)) {
                callback(new Error('对抵金额最大为' + this.maxCounterMoney + '!'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    },
    rowSelectionInfo() {
      if (this.isRowSelect) {
        return { selectedRowKeys: this.selectedRowKeys, onChange: this.onSelectChange }
      } else {
        return null
      }
    },
    rowSelectionInfo2() {
      if (this.isRowSelect2) {
        return { selectedRowKeys: this.selectedRowKeys2, onChange: this.onSelectChange2 }
      } else {
        return null
      }
    },
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.id = this.$route.query.id || null
      this.isEdit = this.$route.query.isEdit || null
      this.isInfo = this.$route.query.isInfo || null
      if (this.isInfo) {
        this.isDisabled = true
        this.getInfo()
      } else if (this.isEdit) {
        this.$root.$emit('setPageTitle', null, '编辑销货对抵', this.$route.fullPath)
        this.getInfo()
      } else {
        this.$root.$emit('setPageTitle', null, '新增销货对抵', this.$route.fullPath)
        this.isDisabled = false
      }

      this.isRowSelect = false
      this.selectedRowKeysBusiness = []
      this.selectedRowKeysBusiness2 = []
      this.selectedRowKeys = []
      this.selectedRowKeys2 = []
    },
    getInfo() {
      getAction(this.url.info, { id: this.id }, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        // 数据还原
        this.model = {
          PartyId: res.Data.SalesOffsetInfo.OffsetPartyId,
          PartyName: res.Data.SalesOffsetInfo.SupplierName,
          SalesId: res.Data.SalesOffsetInfo.SalesId,
          SalesName: res.Data.SalesOffsetInfo.SalesManName,
          OffsetAmount: res.Data.SalesOffsetInfo.OffsetAmount,
          Remark: res.Data.SalesOffsetInfo.Remark,
          listObj1: res.Data.PayableBill || {},
          listObj2: res.Data.ReceivableBill || {},
          SalesOffsetInfo: res.Data.SalesOffsetInfo || {}
        }
        this.dataSource = res.Data.PayableBill.Bills || []
        this.dataSource2 = res.Data.ReceivableBill.Bills || []
        // 应收/应付信息
        this.getMaxCounterMoney(this.model.PartyId)
        this.getTotalBalanceAsync()
        this.isClickOrder = true
        this.isClickOrder2 = true
      })
    },
    getMaxCounterMoney(PartyId) {
      let params = {
        PartyId: PartyId,
        PageIndex: 1,
        PageSize: 100,
      }
      postAction(this.url.maxCounterMoney, params, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.maxCounterMoney = res.Data ? Number(res.Data) : 0
      })
    },
    // table行的颜色
    rowClassNameFun(record, index) {
      if (record.PayableAmount < 0 || record.ResiduePayableAmount < 0) {
        return 'table-color-red-dust'
      }
    },
    changeSearchInput(val, txt, item, type) {
      if (type == 'PartyName') {
        this.model.PartyId = val
        this.model.PartyName = txt
        this.dataSource = []
        this.dataSource2 = []
        this.delArray = []
        this.delArray2 = []
        this.isClickOrder = false
        this.getTotalBalanceAsync()
        this.getMaxCounterMoney(this.model.PartyId)
      } else if (type == 'SalesName') {
        this.model.SalesId = val
        this.model.SalesName = txt
      }
    },
    // 应收/应付信息
    getTotalBalanceAsync() {
      if (!this.model.PartyId) return
      getAction(this.url.getTotalBalanceAsync, { SupplierId: this.model.PartyId }, 'P36011').then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        res.Data.TotalBalance = Number(
          (res.Data.ReceivableBalance + res.Data.PayableBalance + res.Data.PrepaymentBalance).toFixed(2)
        )
        this.dataSource0 = [res.Data]
      })
    },
    // 勾单
    seduceOrder() {
      if (!this.model.PartyId) {
        this.$message.warning('请先选择对抵方')
        return
      }
      if (!this.model.OffsetAmount) {
        this.$message.warning('请先输入对抵金额')
        return
      }
      if (this.model.OffsetAmount > this.maxCounterMoney) {
        this.$message.warning('对抵金额最大为' + this.maxCounterMoney + '，请重新输入')
        return
      }
      if (
        this.dataSource0.length > 0 &&
        (this.model.OffsetAmount > this.dataSource0[0].ReceivableBalance ||
          this.model.OffsetAmount > this.dataSource0[0].PayableBalance)
      ) {
        this.$message.warning('对抵金额不能大于应收余额和应付余额的最小值，请重新填写')
        return
      }
      this.delArray = []
      this.delArray2 = []
      // 应付明细
      this.getList1()
      // 应收明细
      this.getList2()
    },
    // 明细调整
    detailSet(source) {
      if (!this.model.PartyId) {
        this.$message.warning('请先选择对抵方')
        return
      }
      if (!this.model.OffsetAmount) {
        this.$message.warning('请先输入对抵金额')
        return
      }
      if (source == 1) {
        if (!this.isClickOrder) {
          this.$message.warning('请先勾单')
          return
        }
        this.$refs.SalesButtApplicationTableModal.show(source, this.dataSource, this.model)
      } else if (source == 2) {
        if (!this.isClickOrder2) {
          this.$message.warning('请先勾单')
          return
        }
        this.$refs.SalesButtApplicationTableModal.show(source, this.dataSource2, this.model)
      }
    },
    getList1(obj, type) {
      // 应付明细
      let formData = {
        PartyId: this.model.PartyId,
        OffsetAmount: this.model.OffsetAmount,
        IsAdjust: false,
        ...obj,
      }
      if (type == 'adjust') {
        formData.IsValid = true
      }
      this.confirmLoading = true
      postAction(this.url.list1, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.model.listObj1 = res.Data
            this.dataSource = this.model.listObj1.Bills
            if (type == 'batchRemove') {
              this.isRowSelect = false
              this.$message.success('批量移除成功')
            }
            if (type == 'adjust') {
              this.$refs.SalesButtApplicationTableModal.close()
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.confirmLoading = false
          this.isClickOrder = true
        })
    },
    getList2(obj, type) {
      let formData2 = {
        PartyId: this.model.PartyId,
        OffsetAmount: this.model.OffsetAmount,
        IsAdjust: false,
        ...obj,
      }
      if (type == 'adjust') {
        formData2.IsValid = true
      }
      this.confirmLoading = true
      postAction(this.url.list2, formData2, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.model.listObj2 = res.Data
            this.dataSource2 = this.model.listObj2.Bills
            if (type == 'batchRemove') {
              this.isRowSelect2 = false
              this.$message.success('批量移除成功')
            }
            if (type == 'adjust') {
              this.$refs.SalesButtApplicationTableModal.close()
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.confirmLoading = false
          this.isClickOrder2 = true
        })
    },
    modalOk(source, selectedRowKeysRecord, selectedRowKeys) {
      console.log('选中的单据', selectedRowKeysRecord, selectedRowKeys)
      //   选中的单据
      let array = []
      if (selectedRowKeysRecord.length > 0) {
        selectedRowKeysRecord.map((item) => {
          array.push({
            BusinessOrder: item.BusinessOrder,
            BusinessId: item.BillId,
            ResiduePayableAmount: item.ResiduePayableAmount ? Number(item.ResiduePayableAmount) : 0,
          })
        })
      }
      let obj = {
        SelectedBills: array,
      }
      if (source == 1) {
        this.delArray = []
        this.selectedRowKeysBusiness = array
        this.getList1(obj, 'adjust')
      } else if (source == 2) {
        this.delArray2 = []
        this.selectedRowKeysBusiness2 = array
        this.getList2(obj, 'adjust')
      }
    },
    blurInput() {
      this.$refs.form.validateField(['OffsetAmount'])
    },
    onSelectChange(selectedRowKeys, record) {
      console.log('selectedRowKeys changed: ', selectedRowKeys, record)
      let array = []
      if (record.length > 0) {
        record.map((item) => {
          array.push({
            BusinessOrder: item.BusinessOrder,
            BusinessId: item.BillId,
          })
        })
      }
      this.selectedRowKeysBusiness = array
      this.selectedRowKeys = selectedRowKeys
    },
    onSelectChange2(selectedRowKeys, record) {
      console.log('selectedRowKeys changed: ', selectedRowKeys, record)
      let array = []
      if (record.length > 0) {
        record.map((item) => {
          array.push({
            BusinessOrder: item.BusinessOrder,
            BusinessId: item.BillId,
          })
        })
      }
      this.selectedRowKeysBusiness2 = array
      this.selectedRowKeys2 = selectedRowKeys
    },
    subBatchRemoveData(source) {
      if (source == 1) {
        if (this.selectedRowKeysBusiness.length == 0) {
          this.$message.warning('请先选择要批量删除的数据')
          return
        }
        let obj = {
          RemoveBills: this.selectedRowKeysBusiness,
        }
        this.getList1(obj, 'batchRemove')
      } else if (source == 2) {
        if (this.selectedRowKeysBusiness2.length == 0) {
          this.$message.warning('请先选择要批量删除的数据')
          return
        }
        let obj = {
          RemoveBills: this.selectedRowKeysBusiness2,
        }
        this.getList2(obj, 'batchRemove')
      }
    },
    batchRemoveData(source, type) {
      if (source == 1) {
        if (type == 'batchDel') {
          if (this.dataSource.length == 0) {
            this.$message.warning('应付明细暂无数据，暂时无法批量移除')
            return
          }
          this.selectedRowKeysBusiness = []
          this.selectedRowKeys = []
          this.isRowSelect = true
        } else if (type == 'cancelBatchDel') {
          this.isRowSelect = false
        }
      } else if (source == 2) {
        if (type == 'batchDel') {
          if (this.dataSource2.length == 0) {
            this.$message.warning('应收明细暂无数据，暂时无法批量移除')
            return
          }
          this.selectedRowKeysBusiness2 = []
          this.selectedRowKeys2 = []
          this.isRowSelect2 = true
        } else if (type == 'cancelBatchDel') {
          this.isRowSelect2 = false
        }
      }
    },
    removeData(source, record, index) {
      if (source == 1) {
        let haveIndex = this.delArray.findIndex((item) => {
          return item.BusinessId == record.BillId
        })
        if (haveIndex >= 0) {
          this.delArray[haveIndex] = {
            BusinessOrder: record.BusinessOrder,
            BusinessId: record.BillId,
          }
        } else {
          this.delArray.push({
            BusinessOrder: record.BusinessOrder,
            BusinessId: record.BillId,
          })
        }
        let obj = {
          RemoveBills: this.delArray,
          SelectedBills: this.selectedRowKeysBusiness,
        }
        this.getList1(obj)
      } else if (source == 2) {
        let haveIndex = this.delArray2.findIndex((item) => {
          return item.BusinessId == record.BillId
        })
        if (haveIndex >= 0) {
          this.delArray2[haveIndex] = {
            BusinessOrder: record.BusinessOrder,
            BusinessId: record.BillId,
          }
        } else {
          this.delArray2.push({
            BusinessOrder: record.BusinessOrder,
            BusinessId: record.BillId,
          })
        }
        let obj = {
          RemoveBills: this.delArray2,
          SelectedBills: this.selectedRowKeysBusiness2,
        }
        this.getList2(obj)
      }
    },
    // 创建审核实例
    createApproval(params, callback) {
      this.loading = true
      postAction(this.url.createAudit, params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            this.$message.success('提交审核成功')
            callback && callback(true)
            this.handleCancel()
          } else {
            this.$message.error(res.Msg)
            callback && callback(false)
          }
        })
        .catch((e) => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 确定
    handleOk(IsDraft) {
      this.IsDraft = IsDraft
      const that = this
      // 触发表单验证
      let validateFieldList = []
      if (IsDraft) {
        this.$refs.form.validateField(['PartyId'], (err) => {
          if (!err) {
            validateFieldList.push(err)
          }
          if (validateFieldList.length == 1 && validateFieldList.every(item => item === '')) {
            //校验通过的业务逻辑
            that.handleOkData(IsDraft)
          }
        })
      } else {
        let that = this
        this.$refs.form.validate((err, values) => {
          if (err) {
            if (!that.model.listObj1) {
              that.model.listObj1 = {}
            }
            if (!that.model.listObj2) {
              that.model.listObj2 = {}
            }
            if (that.model.OffsetAmount === that.model.listObj1.PayableOffsetAmount && that.model.OffsetAmount === that.model.listObj2.PayableOffsetAmount) {
              that.handleOkData(IsDraft)
            } else {
              that.$message.warning('对抵金额不一致无法提交审核')
            }
          }
        })
      }
    },
    handleOkData(IsDraft) {
      if (this.model.Remark) {
        if (this.getStrMaxLength(this.model.Remark) > 100) {
          this.$message.warning('备注最多100个字符')
          return
        }
      }
      const that = this
      that.confirmLoading = true
      let formData = {}
      let url = ''
      let sourceArray1 = this.dataSource.map((item) => {
        return {
          MainOrderId: item.OrderId,
          MainOrderCode: item.OrderNo,
          BusinessNo: item.BillNo,
          BusinessId: item.BillId,
          BusinessCreator: item.CreateByName,
          BusinessOrderType: item.BusinessOrder,
          PaymentMode: item.PaymentMode,
          TotalAmount: item.PayableAmount,
          ResidueAmount: item.ResiduePayableAmount,
          OffsetAmount: item.OffsetAmount,
          TransactionTime: item.TransactionTime,
          OrderCreateTime: item.OrderCreateTime,
        }
      })
      let sourceArray2 = this.dataSource2.map((item) => {
        return {
          MainOrderId: item.OrderId,
          MainOrderCode: item.OrderNo,
          BusinessNo: item.BillNo,
          BusinessId: item.BillId,
          BusinessCreator: item.CreateByName,
          BusinessOrderType: item.BusinessOrder,
          PaymentMode: item.PaymentMode,
          TotalAmount: item.PayableAmount,
          ResidueAmount: item.ResiduePayableAmount,
          OffsetAmount: item.OffsetAmount,
          TransactionTime: item.TransactionTime,
          OrderCreateTime: item.OrderCreateTime,
        }
      })
      if (this.id) {
        url = this.url.update
        formData = {
          Id: this.id,
          PartyId: this.model.PartyId,
          SalesId: this.model.SalesId || null,
          OffsetAmount: this.model.OffsetAmount,
          Remark: this.model.Remark,
          IsDraft: IsDraft,
          PayableBills: sourceArray1 || [],
          ReceivableBills: sourceArray2 || [],
        }
      } else {
        url = this.url.add
        formData = {
          PartyId: this.model.PartyId,
          SalesId: this.model.SalesId || null,
          OffsetAmount: this.model.OffsetAmount,
          Remark: this.model.Remark,
          IsDraft: IsDraft,
          PayableBills: sourceArray1 || [],
          ReceivableBills: sourceArray2 || [],
        }
      }
      postAction(url, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('保存成功')
            if (!IsDraft) {
              let params = {
                BussinessNo: res.Data.AuditId,
                MainBussinessNo: null,
                Scenes: 26,
                OpratorId: Vue.ls.get(USER_ID),
                Remark: '销货对抵审核',
              }
              this.createApproval(params)
            }
            this.id = res.Data.OffsetOrderId ? res.Data.OffsetOrderId : null
            that.$emit('ok')
            if (IsDraft) {
              that.handleCancel()
            }
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    handleCancel() {
      this.$root.$emit(
        'removeTabsAndBack',
        this.$route.fullPath,
        '/pages/financialManagement/financialSettlement/salesButtApplicationList'
      )
    },
    onRemarkChange(e) {
      let value = e.target.value
      if (value && value.length > 50) {
        if (this.getStrMaxLength(this.model.Remark) > 100) {
          this.$message.warning('备注最多100个字符')
        }
      }
    },
  },
}
</script>
<style lang="scss">
.num-box span {
  margin-right: 10px;
}
</style>
