<template>
  <a-modal :title="title" :width="1100" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form>
          <a-row :gutter="0">
            <a-col :md="queryCol">
              <a-form-item label="单据编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input style="width:100%;" placeholder="请输入" v-model="queryParam.BillNo"></a-input>
              </a-form-item>
            </a-col>
            <a-col :md="queryCol">
              <a-form-item label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <EnumSingleChoiceView style="width: 100%;" placeholder="请选择" isNumber :dictCode="source==1?'EnumBusinessOrderOffsetPay':'EnumBusinessOrderOffsetReceive'" v-model="queryParam.BusinessOrder" />
              </a-form-item>
            </a-col>
            <a-col :md="queryCol">
              <a-form-item label="日期" :labelCol="{xs: { span: 24 },sm: { span: 5 },}" :wrapperCol="{xs: { span: 24 },sm: { span: 19 },}">
                <a-range-picker v-model="rangeDate" @change="onTimeChange" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col style="text-align: right;margin-top:3px;" :md="5">
              <span class="table-page-search-submit-btns">
                <a-button @click="searchReset" icon="reload" style="margin-right: 8px;">重置</a-button>
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
        <a-form-model ref="form">
          <a-row :getter="10">
            <a-col :md="24">
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>{return setRowKey(record)}" size="small" :columns="columns" :dataSource="dataSource" :pagination="false" :rowClassName="rowClassNameFun" :row-selection="rowSelectionInfo" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length>0?'450px':false, x: '100%' }">
                <!-- 序号 -->
                <span slot="Order" slot-scope="text, record, order">
                  {{ getOrder(record, order) }}
                </span>
                <!-- 字符串超长截取省略号显示-->
                <span slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </span>
                <!-- 操作 -->
                <span slot="action" slot-scope="text, record,index">
                  <a-popconfirm title="您确定要移除当前单据吗?" ok-text="确定" cancel-text="取消" @confirm="removeData(2,record,index)">
                    <a> 移除 </a>
                  </a-popconfirm>
                </span>
              </a-table>
              <a-button v-if="dataSource.length > 0" style="margin: 0px 30px 0px 0px;position: relative;top: 8px;" @click="chooseAll">
                所有单据全选
              </a-button>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <div style="display: flex;align-items: center;justify-content: space-between;" slot="footer">
      <div>
        <span style="margin-right:30px;">合计数量：{{model.TotalCount || '0'}}条</span>
        <span v-if="source == 1">应付合计：<span style="color:red;">¥{{model.ResiduePayableAmount || '0'}}</span></span>
        <span v-if="source == 2">应收合计：<span style="color:red;">¥{{model.ResiduePayableAmount || '0'}}</span></span>
      </div>
      <div :style="{ textAlign: 'right' }">
        <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
        <a-button @click="handleCancel">
          取消
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from "@/api/manage";
import { Item } from 'ant-design-vue/es/vc-menu';
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
const columns = [
  {
    title: '单据编号',
    dataIndex: 'BillNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '创建人',
    dataIndex: 'CreateByName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '单据类型',
    dataIndex: 'BusinessOrderStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '对应订单编号',
    dataIndex: 'OrderNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单付款方式',
    dataIndex: 'PaymentModeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '发生日期',
    dataIndex: 'TransactionTime',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '应付金额',
    dataIndex: 'PayableAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '剩余应付金额',
    dataIndex: 'ResiduePayableAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns2 = [
  {
    title: '单据编号',
    dataIndex: 'BillNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '创建人',
    dataIndex: 'CreateByName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '单据类型',
    dataIndex: 'BusinessOrderStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '对应订单编号',
    dataIndex: 'OrderNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单收款方式',
    dataIndex: 'PaymentModeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '发生日期',
    dataIndex: 'TransactionTime',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '应收金额',
    dataIndex: 'PayableAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '剩余应收金额',
    dataIndex: 'ResiduePayableAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]

export default {
  name: "SalesButtApplicationTableModal",
  mixins: [ListMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "应付明细调整",
      visible: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {
        Bills: [],
      },
      modelData: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      queryCol: 6,
      queryParam: {},
      dataSource: [],
      columns: columns,
      source: '', //1 应付明细 2 应收明细
      selectedRowKeys: [],
      selectedRowKeysRecord: [],
      rangeDate: [],
      isCanGo: true,//是否能继续勾稽
      confirmLoading: false,
      httpHead: 'P36012',
      url: {
        list: '',
        listType: 'POST',
        list1: '/{v}/SalesOffset/GetOffsetPayableBill',
        list2: '/{v}/SalesOffset/GetOffsetReceivableBill',
      },
    };
  },
  computed: {
    rowSelectionInfo() {
      return { selectedRowKeys: this.selectedRowKeys, onChange: this.onSelectChange }
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(source, dataSourceData, modelData) {
      this.source = source
      this.title = source == 1 ? '应付明细调整' : '应收明细调整'
      this.modelData = modelData
      this.selectedRowKeysRecord = []
      this.selectedRowKeys = []
      if (this.source == 1) {
        this.columns = columns
        this.url.list = this.url.list1
      } else {
        this.columns = columns2
        this.url.list = this.url.list2
      }
      this.dataSource = []
      this.visible = true;
      // 重组选中的table数据
      if (dataSourceData && dataSourceData.length > 0) {
        dataSourceData = dataSourceData.map((item) => {
          return {
            BusinessOrder: item.BusinessOrder,
            BusinessId: item.BillId,
          }
        })
      }
      this.queryParam = {}
      this.rangeDate = []
      this.queryParam.IsAdjust = true
      this.ipagination.pageSize = 9999
      this.queryParam.CheckedBills = dataSourceData
      this.queryParam.PartyId = this.modelData.PartyId
      this.setLoadData()
    },
    setLoadData(type) {
      let page = null
      if (type == 'change') {
        page = null
      } else {
        page = 1
      }
      this.loadData(page, (data) => {
        this.model = data
        this.dataSource = data.Bills
        // 还原选中的数据
        this.dataSource.map((item) => {
          if (this.selectedRowKeys.indexOf(this.setRowKey(item)) == -1) {
            if (item.IsChecked) {
              this.selectedRowKeys.push(this.setRowKey(item))
              this.selectedRowKeysRecord.push(item)
            }
          }
        })
        this.setTotalNum()
      })
    },
    setRowKey(record) {
      return (record.BillId || '') + '_' + (record.BusinessOrder || '') + '_' + (record.PayableAmount || '') + '_' + (record.ResiduePayableAmount || '')
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        if (this.sort) {
          // 订单列表 排序方式
          this.queryParam[this.sort] = 'ascend' == sorter.order ? '1' : '2'
        }
      }
      this.ipagination = pagination
      this.setLoadData('change')
    },
    // table行的颜色
    rowClassNameFun(record, index) {
      if (record.ResiduePayableAmount < 0) {
        return 'table-color-red-dust'
      }
    },
    onSelectChange(selectedRowKeys, selectedRowKeysRecord) {
      // 重组数据
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRowKeysRecord = []
      this.selectedRowKeys.map((item) => {
        let itemArray = item.split('_')
        this.selectedRowKeysRecord.push({
          BillId: itemArray[0],
          BusinessOrder: itemArray[1] ? Number(itemArray[1]) : 0,
          PayableAmount: itemArray[2],
          ResiduePayableAmount: itemArray[3],
        })
      })
      // 计算合计
      this.setTotalNum()
    },
    getDiffer(arr1, arr2) {
      return arr1.concat(arr2).filter(function (value, index, arr) {
        return arr.indexOf(value) === arr.lastIndexOf(value);
      });
    },
    // 计算合计
    setTotalNum() {
      this.model.ResiduePayableAmount = 0
      this.model.TotalCount = 0
      if (this.selectedRowKeysRecord.length > 0) {
        this.model.TotalCount = this.selectedRowKeysRecord.length
        this.selectedRowKeysRecord.map((item) => {
          this.model.ResiduePayableAmount += Number(item.ResiduePayableAmount)
        })
        this.model.ResiduePayableAmount = Number(this.model.ResiduePayableAmount).toFixed(2)
      }
    },
    onTimeChange(dates, dateStrings) {
      if (dateStrings.length == 2) {
        this.queryParam.CreateTimeBegin = dateStrings[0]
        this.queryParam.CreateTimeEnd = dateStrings[1]
      } else {
        this.queryParam.CreateTimeBegin = null
        this.queryParam.CreateTimeEnd = null
      }
    },
    chooseAll() {
      this.dataSource.map((item) => {
        if (this.selectedRowKeys.indexOf(this.setRowKey(item)) == -1) {
          this.selectedRowKeys.push(this.setRowKey(item))
          this.selectedRowKeysRecord.push(item)
        }
      })
      // 计算合计
      this.setTotalNum()
    },
    // 确定
    handleOk() {
      if (this.selectedRowKeysRecord.length == 0) {
        this.$message.warning('请至少选择一条数据');
        return
      }
      this.$emit('ok', this.source, this.selectedRowKeysRecord, this.selectedRowKeys)
      // this.close()
    },
    searchQuery() {
      // this.queryParam.BusinessOrder = parseInt(this.queryParam.BusinessOrder)
      this.setLoadData()
    },
    searchReset() {
      this.queryParam = {
        IsAdjust: this.queryParam.IsAdjust,
        CheckedBills: this.queryParam.CheckedBills,
        PartyId: this.queryParam.PartyId,
      }
      this.rangeDate = []
      this.setLoadData()
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
