<template>
  <a-spin :spinning="confirmLoading" tip="请稍后">
    <div>
      <a-card style="margin-bottom:10px;">
        <a-row :gutter="20">
          <a-col style="text-align: center;" :md="4">
            <a-icon style="color:#1ABC9C;font-size: 30px;" type="clock-circle" />
            <div style="color:#1ABC9C;">{{model.SaleAfterInfo.SaleAfterPayStatusStr || '--'}}</div>
          </a-col>
          <a-col style="text-align: center;" :md="5">
            <div style="color:#999999;">客户名称</div>
            <div style="margin-top:3px;">{{model.SaleAfterInfo.CustomerName || '--'}}</div>
          </a-col>
          <a-col style="text-align: center;" :md="5">
            <div style="color:#999999;">销退单号</div>
            <div style="margin-top:3px;">{{model.SaleAfterInfo.SaleAfterNo || '--'}}</div>
          </a-col>
          <a-col style="text-align: center;" :md="5">
            <div style="color:#999999;">创建人</div>
            <div style="margin-top:3px;">{{model.SaleAfterInfo.CreateByName || '--'}}</div>
          </a-col>
          <a-col style="text-align: center;" :md="5">
            <div style="color:#999999;">创建时间</div>
            <div style="margin-top:3px;">{{model.SaleAfterInfo.CreateTime || '--'}}</div>
          </a-col>
        </a-row>
      </a-card>
      <a-card size="small" style="margin-bottom:10px;" :bodyStyle="{ padding: '16px' }" title="商品列表">
        <a-form-model style="margin-top:5px;" ref="form" layout="inline">
          <a-row :getter="10">
            <a-col :md="8">
              <a-form-model-item label="退款原因：" required>
                <span style="color:#1890ff;">{{model.SaleAfterInfo.RefundReason || '--'}}</span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
        <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="small" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ y: '300px', x: '100%' }">
          <!-- 字符串超长截取省略号显示-->
          <span slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </span>
          <span slot="IsGift" slot-scope="text">
            <j-ellipsis :value="text == true?'是':'否'" />
          </span>
          <!-- 操作 -->
          <span slot="action" slot-scope="text, record">
            <a @click="removeRole(record)"> 移除 </a>
          </span>
        </a-table>
        <a-form-model style="margin-top:5px;" ref="form" layout="inline">
          <a-row :getter="10">
            <a-col :md="8">
              <a-form-model-item label="退款金额：" required>
                {{model.SaleAfterInfo.RefundAmount}}
              </a-form-model-item>
            </a-col>
            <a-col :md="8">
              <a-form-model-item style="margin:0 15px;" label="结算方式：" required>
                {{model.SaleAfterInfo.SaleAfterRefundTypeStr || '--'}}
              </a-form-model-item>
            </a-col>
            <a-col :md="8">
              <a-form-model-item label="备注：" required>
                {{model.SaleAfterInfo.Remarks}}
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-card>
      <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
        <!-- 供应商收款信息 -->
        <a-card size="small" style="margin-bottom:10px;" :bodyStyle="{ padding: '16px' }" title="客户收款信息">
          <a-row :getter="10">
            <a-col :md="8">
              <a-form-model-item label="开户人：" prop="BankAccount">
                <a-input placeholder="请输入" v-model="model.BankAccount" style="width: 100%" disabled :maxLength="50" />
              </a-form-model-item>
            </a-col>
            <a-col :md="8">
              <a-form-model-item style="margin:0 15px;" label="开户银行：" prop="BankName">
                <a-select style="width: 100%" placeholder="请选择" v-model="model.BankName" :disabled="isInfo?true:(model.Banks.length == 1?true:false)" @change="changeBank">
                  <a-select-option value="">请选择</a-select-option>
                  <a-select-option :value="index" v-for="(item,index) in model.Banks" :key="index">{{item.BankName}}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :md="8">
              <a-form-model-item label="银行卡号：" prop="BankCardNumber">
                <a-input placeholder="请输入" v-model="model.BankCardNumber" style="width: 100%" disabled :maxLength="50" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-card>
        <!-- 打款信息 -->
        <a-card size="small" style="margin-bottom:10px;" :bodyStyle="{ padding: '16px' }" title="打款信息">
          <a-row :getter="15">
            <a-col :md="24" v-for="(item,index) in paymentInfo" :key="index">
              <a-col :md="8">
                <a-form-model-item label="账簿名称：" prop="AccountBookName">
                  <SingleChoiceView style="width: 100%;" placeholder="请选择" :httpParams="{
                 groupPY: remittanceSource == 1?'CGZB':'XSZB'
              }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'Id' }" :disabled="isInfo?true:false" v-model="item.AccountBookName" :Url="'/{v}/Global/GetListDictItemForCustomer'" @change="(val, txt, r) => changeZhangBo(val, txt, r,item)" />
                </a-form-model-item>
              </a-col>
              <a-col :md="8">
                <a-form-model-item style="margin:0 15px;" label="打款日期：" prop="PayTime">
                  <a-date-picker v-model="item.PayTime" valueFormat="YYYY-MM-DD" :disabled="isInfo?true:false" style="width: 100%;" />
                </a-form-model-item>
              </a-col>
              <a-col :md="8">
                <a-form-model-item label="打款凭证图片：">
                  <MultiUpload :max="5" :images="item.VoucherFiles" :uploadText="uploadText" :fileType='4' :bName="BName" :dir="Dir" :readOnly="isInfo?true:false" :fileSize="50" />
                  <span>注：只支持png、jpg、jpeg、bmp格式的图片</span>
                </a-form-model-item>
              </a-col>
            </a-col>

          </a-row>
        </a-card>
      </a-form-model>

      <a-card>
        <a-row :style="{ textAlign: 'right' }">
          <a-button :style="{ marginRight: '8px' }" @click="handleCancel">
            {{isInfo?'返回':'取消'}}
          </a-button>
          <a-button :disabled="confirmLoading" v-if="!isInfo" @click="handleOk" type="primary">确认打款</a-button>
        </a-row>
      </a-card>
    </div>
  </a-spin>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
const columns = [
  {
    title: '商品名称',
    dataIndex: 'ErpGoodsName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '商品编号',
    dataIndex: 'ErpGoodsCode',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '规格',
    dataIndex: 'GoodsPackingSpecification',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '单位',
    dataIndex: 'GoodsPackageUnit',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '生产厂商',
    dataIndex: 'GoodsBrandManufacturer',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '件装量',
    dataIndex: 'GoodsPackageCount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '批号',
    dataIndex: 'GoodsBatchNumber',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '有效期',
    dataIndex: 'GoodsPeriod',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '是否赠品',
    dataIndex: 'IsGift',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'IsGift' },
  },
  {
    title: '销售单价',
    dataIndex: 'Price',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '退货数量',
    dataIndex: 'SaleAfterCount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '退款小计',
    dataIndex: 'GoodsRefundTotalAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]

export default {
  name: 'CashierPayAddRefundAddModule',
  title: '打款-销售退货',
  mixins: [ListMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      model: {
        "SaleAfterInfo": {

        },
        "SaleAfterBills": {

        },
        "Banks": [

        ],
        "Pay": [

        ],
      },
      subModel: {
        VoucherFiles: [],
      },
      todayTime: '',
      columns: columns,
      dataSource: [],
      paymentInfo: this.paymentInfoList(),//打款信息
      confirmLoading: false,
      uploadText: '上传图片',
      listImageUrl: [],
      listImageUrl2: [],
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
      id: '',
      remittanceSource: null, //1 采购付款 2 销售退货
      isInfo: null,
      httpHead: 'P36012',
      url: {
        info: '/{v}/Settlement/GetRemittanceDetailSaleAfter',
        addSub: '/{v}/Settlement/ConfirmPayment',//确认打款
      },
    }
  },
  computed: {
    rules() {
      return {
        BankAccount: [{ required: true, message: "请输入开户人!" }],
        BankName: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.BankName) {
                callback(new Error('请选择开户银行!'))
              } else {
                callback()
              }
            },
          },
        ],
        BankCardNumber: [{ required: true, message: "请输入银行卡号!" }],
        AccountBookName: [{
          required: true, validator: (rule, value, callback) => {
            if (!this.paymentInfo[0].AccountBookName) {
              callback(new Error('请选择账簿名称!'))
            } else {
              callback()
            }
          },
        }],
        PayTime: [{
          required: true,
          validator: (rule, value, callback) => {
            if (!this.paymentInfo[0].PayTime) {
              callback(new Error('请选择打款日期!'))
            } else {
              callback()
            }
          },
        },],
        VoucherFiles: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.paymentInfo[0].VoucherFiles || this.paymentInfo[0].VoucherFiles.length == 0) {
                callback(new Error('请上传打款凭证图片!'))
              } else {
                callback()
              }
            },
          },
        ],
      };
    },
  },
  created() {
    this.id = this.$route.query.id
    this.remittanceSource = this.$route.query.remittanceSource
    this.isInfo = this.$route.query.isInfo
    if (this.id) {
      this.getInfo(this.id)
    }
  },
  methods: {
    getInfo(id) {
      let formData = {
        id: id,
      }
      getAction(this.url.info, formData, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          this.model = res.Data
          this.dataSource = this.model.SaleAfterBills
          if (this.model.Banks && this.model.Banks.length == 1) {
            this.$set(this.model, "BankName", this.model.Banks[0].BankName) //开户银行
            this.$set(this.model, "BankAccount", this.model.Banks[0].BankAccount) //开户人
            this.$set(this.model, "BankCardNumber", this.model.Banks[0].BankCardNumber) //银行卡号
          } else {
            this.$set(this.model, "BankName", "") //开户银行
            this.$set(this.model, "BankAccount", "") //开户人
            this.$set(this.model, "BankCardNumber", "") //银行卡号
          }
          //   详情还原
          if (this.isInfo) {
            this.model.BankAccount = this.model.Banks[0].BankAccount
            this.model.BankName = this.model.Banks[0].BankName
            this.model.BankCardNumber = this.model.Banks[0].BankCardNumber
            // 还原打款信息
            if (this.model.Pays && this.model.Pays.length > 0) {
              this.model.Pays.map(item => {
                item.VoucherFiles = item.Files
              })
            }
            this.paymentInfo = this.model.Pays.length == 0 ? this.paymentInfoList() : (this.model.Pays || [])
          }
          // 销售退货
          if (this.remittanceSource == 2) {
            if (this.paymentInfo.length > 0) {
              this.paymentInfo[0].PayAmount = this.model.SaleAfterInfo.RefundAmount || 0
            }
          }
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {

      });
    },
    getTadayTime() {
      var date = new Date();
      var seperator1 = "-";
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var strDate = date.getDate();
      if (month >= 1 && month <= 9) { month = "0" + month; }
      if (strDate >= 0 && strDate <= 9) { strDate = "0" + strDate; }
      var currentdate = year + seperator1 + month + seperator1 + strDate;
      return currentdate
    },
    changeZhangBo(val, txt, r, item) {
      console.log(val, txt, r, item)
      item.AccountBookId = r.Id
      item.AccountBookName = r.ItemValue
      item.AccountBookCode = r.ItemPym
    },
    paymentInfoList() {
      let list = [
        this.paymentInfoItem()
      ]
      return list
    },
    paymentInfoItem() {
      return { AccountBookId: '', PayAmount: 0, PayTime: this.getTadayTime(), AccountBookName: '', AccountBookCode: '', VoucherFiles: [] }
    },
    changeBank(e) {
      this.$set(this.model, "BankName", this.model.Banks.length > 0 ? this.model.Banks[e].BankName : '') //开户银行
      this.$set(this.model, "BankAccount", this.model.Banks.length > 0 ? this.model.Banks[e].BankAccount : '') //开户人
      this.$set(this.model, "BankCardNumber", this.model.Banks.length > 0 ? this.model.Banks[e].BankCardNumber : '') //银行卡号
      this.$refs.form.validateField(['BankName'])
      this.$refs.form.validateField(['BankAccount'])
      this.$refs.form.validateField(['BankCardNumber'])
    },
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          let formData = {
            "RemittanceId": this.id,
            "BankAccount": this.model.BankAccount,
            "BankName": this.model.BankName,
            "BankCardNumber": this.model.BankCardNumber,
            "AccountBooks": this.paymentInfo
          }
          postAction(this.url.addSub, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              this.handleCancel()
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    handleCancel() {
      this.$root.$emit("removeTabsAndBack", this.$route.fullPath, '/pages/financialManagement/financialSettlement/cashierPayList');
    },
  },
}
</script>
<style lang="scss">
.num-box span {
  margin-right: 10px;
}
</style>