<template>
  <div>
    <a-card style="margin-bottom:10px;">
      <a-row :gutter="20">
        <a-col style="text-align: center;" :md="4">
          <a-icon style="color:#1ABC9C;font-size: 30px;" type="clock-circle" />
          <div style="color:#1ABC9C;">{{model.PurchaseInfo.PaymentModeStr || '--'}}</div>
        </a-col>
        <a-col style="text-align: center;" :md="5">
          <div style="color:#999999;">供应商名称</div>
          <div style="margin-top:3px;">{{model.PurchaseInfo.SupplierName || '--'}}</div>
        </a-col>
        <a-col style="text-align: center;" :md="5">
          <div style="color:#999999;">付款单号</div>
          <div style="margin-top:3px;">{{model.PurchaseInfo.RemittedOrderNo || '--'}}</div>
        </a-col>
        <a-col style="text-align: center;" :md="5">
          <div style="color:#999999;">创建人</div>
          <div style="margin-top:3px;">{{model.PurchaseInfo.CreateByName || '--'}}</div>
        </a-col>
        <a-col style="text-align: center;" :md="5">
          <div style="color:#999999;">创建时间</div>
          <div style="margin-top:3px;">{{model.PurchaseInfo.CreateTime || '--'}}</div>
        </a-col>
      </a-row>
    </a-card>
    <a-card size="small" style="margin-bottom:10px;" :bodyStyle="{ padding: '16px' }" title="付款单据列表">
      <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="small" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ y: '300px', x: '100%' }">
        <!-- 字符串超长截取省略号显示-->
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </span>
        <!-- 操作 -->
        <span slot="action" slot-scope="text, record">
          <a @click="removeRole(record)"> 移除 </a>
        </span>
      </a-table>
    </a-card>
    <!-- 付款信息 -->
    <a-card size="small" style="margin-bottom:10px;" :bodyStyle="{ padding: '16px' }" title="付款信息">
      <a-form-model ref="form" :rules="rules" :model="model" layout="inline">
        <a-row :gutter="15">
          <a-col :md="24">
            <a-col :md="8">
              <a-form-model-item label="总付款金额：" required>
                <a-input placeholder="请输入" v-model="model.PurchaseInfo.TotalPaymentAmount" disabled style="width: 100%" :maxLength="50" />
              </a-form-model-item>
            </a-col>
            <a-col :md="8">
              <a-form-model-item label="预付余额抵扣：">
                <a-input placeholder="请输入" v-model="model.PurchaseInfo.PrepayDeductAmount" disabled style="width: 100%" :maxLength="50" />
              </a-form-model-item>
            </a-col>
          </a-col>
          <a-col :md="8">
            <a-form-model-item label="实际付款金额：" required>
              <a-input placeholder="请输入" v-model="model.Payment.ActualPaymentAmount" disabled style="width: 100%" :maxLength="50" />
            </a-form-model-item>
          </a-col>
          <a-col :md="8">
            <a-form-model-item label="结算方式：" required>
              {{model.Payment.SettlementTypeStr || '--'}}
            </a-form-model-item>
          </a-col>
          <a-col :md="8">
            <a-form-model-item label="备注：">
              {{model.Payment.Remarks || '--'}}
            </a-form-model-item>
          </a-col>
          <a-col :md="8">
            <a-form-model-item style="width:100%;" label="合同图片" required>
              <MultiUpload max="maxNum" :images="listImageUrl" :uploadText="uploadText" :fileType='4' :bName="BName" :dir="Dir" :readOnly="true" :fileSize="50" @change="multiUploadChange" />
            </a-form-model-item>
          </a-col>
          <a-col :md="8" v-if="PaymentMode == 2">
            <a-form-model-item style="width:100%;" label="对账函图片" required>
              <MultiUpload max="maxNum" :images="listImageUrl3" :uploadText="uploadText" :fileType='4' :bName="BName" :dir="Dir" :readOnly="true" :fileSize="50" @change="multiUploadChange" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-card>
    <!-- 发票信息 -->
    <a-card size="small" v-if="PaymentMode == 2" style="margin-bottom:10px;" :bodyStyle="{ padding: '16px' }" title="发票信息">
      <a-row :gutter="15">
        <span>本次付款包含的发票号：{{model.Payment.RemittedInvoiceNoStr || '--'}}</span>
        <!-- <a-col :md="12" v-for="(item,index) in model.Invoices" :key="index">
          <a-form-model ref="form" :label-col="{ span: 5 }" :wrapper-col="{ span: 12 }" labelAlign="right">
            <a-row :gutter="15">
              <a-col :md="24">
                <a-form-model-item label="发票类型：" required>
                  {{item.InvoiceTypeStr || '--'}}
                </a-form-model-item>
              </a-col>
              <a-col :md="24">
                <a-form-model-item label="发票代码：" required>
                  <a-input placeholder="请输入" v-model="item.InvoiceCode" style="width: 100%" disabled :maxLength="100" />
                </a-form-model-item>
              </a-col>
              <a-col :md="24">
                <a-form-model-item label="发票号：" required>
                  <a-input placeholder="请输入" v-model="item.InvoiceNo" style="width: 100%" disabled :maxLength="100" />
                </a-form-model-item>
              </a-col>
              <a-col :md="24">
                <a-form-model-item label="发票金额：" required>
                  <a-input placeholder="请输入" v-model="item.InvoiceAmount" style="width: 100%" disabled :maxLength="100" />
                </a-form-model-item>
              </a-col>
              <a-col :md="24">
                <a-form-model-item label="发票地址：">
                  <a-input placeholder="请输入" v-model="item.InvoiceAddr" style="width: 100%" disabled :maxLength="100" />
                </a-form-model-item>
              </a-col>
              <a-col :md="24">
                <a-form-model-item label="本次勾稽金额：">
                  <a-input placeholder="请输入" v-model="item.CurrentVerificationAmount" style="width: 100%" disabled :maxLength="100" />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </a-col> -->

      </a-row>
    </a-card>
    <a-form-model ref="inputForm" :rules="rules" :model="model" layout="vertical">
      <!-- 供应商收款信息 -->
      <a-card size="small" style="margin-bottom:10px;" :bodyStyle="{ padding: '16px' }" title="供应商收款信息">
        <a-row :gutter="15">
          <a-col :md="8">
            <a-form-model-item label="开户人：" prop="BankAccount">
              <a-input placeholder="请输入" v-model="model.BankAccount" style="width: 100%" disabled :maxLength="50" />
            </a-form-model-item>
          </a-col>
          <a-col :md="8">
            <a-form-model-item label="开户银行：" prop="BankName">
              <a-select style="width: 100%" placeholder="请选择" v-model="model.BankName" :disabled="isInfo?true:(model.Banks.length == 1?true:false)" @change="changeBank">
                <a-select-option value="">请选择</a-select-option>
                <a-select-option :value="index" v-for="(item,index) in model.Banks" :key="index">{{item.BankName}}</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :md="8">
            <a-form-model-item label="银行卡号：" prop="BankCardNumber">
              <a-input placeholder="请输入" v-model="model.BankCardNumber" style="width: 100%" disabled :maxLength="50" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-card>
      <!-- 打款信息 -->
      <a-card size="small" style="margin-bottom:10px;" :bodyStyle="{ padding: '16px' }">
        <div slot="title">
          <span>打款信息</span>
          <a-button style="margin-left:10px;" v-if="!isInfo" type="primary" size="small" @click="addRemittance">增加打款信息</a-button>
        </div>
        <a-row :gutter="15">
          <a-col style="border:1px solid #e8e8e8;padding-top:10px;padding-bottom:10px;margin-bottom:12px;" :md="24" v-for="(item,index) in paymentInfo" :key="index">
            <a-col :md="5">
              <a-form-model-item label="打款金额：" required>
                <a-input-number placeholder="请输入" v-model="item.PayAmount" :disabled="isInfo?true:false" style="width: 100%" :max="model.Payment.ActualPaymentAmount?numFixed(model.Payment.ActualPaymentAmount):0" />
              </a-form-model-item>
            </a-col>
            <a-col :md="5">
              <a-form-model-item label="账簿名称：" required>
                <SingleChoiceView style="width: 100%;" placeholder="请选择" :httpParams="{
                groupPY: remittanceSource == 1?'CGZB':'XSZB'
              }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'Id' }" v-if="remittanceSource" :disabled="isInfo?true:false" v-model="item.AccountBookName" :Url="'/{v}/Global/GetListDictItemForCustomer'" @change="(val, txt, r) => changeZhangBo(val, txt, r,item)" />
              </a-form-model-item>
            </a-col>
            <a-col :md="6">
              <a-form-model-item label="打款日期：" required>
                <a-date-picker v-model="item.PayTime" valueFormat="YYYY-MM-DD" :disabled="isInfo?true:false" style="width: 100%;" @change="onTimeChange" />
              </a-form-model-item>
            </a-col>
            <a-col :md="6">
              <a-form-model-item label="打款凭证图片：">
                <MultiUpload :max="5" :images="item.VoucherFiles" :uploadText="uploadText" :fileType='4' :bName="BName" :dir="Dir" :readOnly="isInfo?true:false" :fileSize="50" />
                <span>注：只支持png、jpg、jpeg、bmp格式的图片</span>
              </a-form-model-item>
            </a-col>
            <a-col v-if="index > 0 && !isInfo" style="text-align: right;margin-top: 60px;" :md="2">
              <a-button @click="delPaymentInfo(index)" type="danger">删除</a-button>
            </a-col>
          </a-col>
        </a-row>
      </a-card>
    </a-form-model>

    <a-card>
      <a-row :style="{ textAlign: 'right' }">
        <a-button :style="{ marginRight: '8px' }" @click="handleCancel">
          {{isInfo?'返回':'取消'}}
        </a-button>
        <a-button :style="{ marginRight: '8px' }" :disabled="confirmLoading" v-if="!isInfo" @click="handleOk" type="primary">确认打款</a-button>
        <a-button :disabled="confirmLoading" v-if="!isInfo" @click="rejectPayment" type="danger">驳回打款</a-button>
      </a-row>
    </a-card>
    <!-- 驳回原因 -->
    <CashierPayRejectPaymentModal ref="CashierPayRejectPaymentModal" @ok="modalOk" />
  </div>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
const columns = [
  {
    title: '单据编号',
    dataIndex: 'DocumentNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '单据类型',
    dataIndex: 'BusinessEventTypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '付款金额',
    dataIndex: 'CurrentPaymentAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns2 = [
  {
    title: '序号',
    dataIndex: 'Order',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'Order' },
  },
  {
    title: '单据编号',
    dataIndex: 'Name',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '创建人',
    dataIndex: 'TypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '单据类型',
    dataIndex: 'MenuDataPermissionStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '对应订单编号',
    dataIndex: 'MenuDataPermissionStr2',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单付款方式',
    dataIndex: 'MenuDataPermissionStr3',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '发生日期',
    dataIndex: 'MenuDataPermissionStr4',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '应付金额',
    dataIndex: 'MenuDataPermissionStr5',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '剩余应付金额',
    dataIndex: 'MenuDataPermissionStr6',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '本次对抵金额',
    dataIndex: 'MenuDataPermissionStr7',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]

export default {
  name: 'CashierPayAddModule',
  title: '打款-采购付款',
  mixins: [ListMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      model: {
        "PurchaseInfo": {

        },
        "Payment": {

        },
        "PurchaseBills": [

        ],
        "Banks": [

        ],
        "Invoices": [

        ],
        "Pay": {

        }
      },
      subModel: {
        VoucherFiles: [],
      },
      columns: columns,
      columns2: columns2,
      dataSource: [],
      confirmLoading: false,
      uploadText: '上传图片',
      listImageUrl: [],//合同图片
      listImageUrl3: [],//对账函图片
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
      id: '',
      paymentInfo: this.paymentInfoList(),//打款信息
      remittanceSource: null, //1 采购付款 2 销售退货
      isInfo: null,
      PaymentMode: null, //1 预付款 2 月结
      httpHead: 'P36012',
      url: {
        info: '/{v}/Settlement/GetRemittanceDetailPurchase',
        addSub: '/{v}/Settlement/ConfirmPayment',//确认打款
      },
    }
  },
  computed: {
    rules() {
      return {
        BankAccount: [{ required: true, message: "请输入开户人!" }],
        BankName: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.BankName) {
                callback(new Error('请上传打款凭证图片!'))
              } else {
                callback()
              }
            },
          },
        ],
        BankCardNumber: [{ required: true, message: "请输入银行卡号!" }],
      };
    },
  },
  created() {
    this.id = this.$route.query.id
    this.isInfo = this.$route.query.isInfo
    this.remittanceSource = this.$route.query.remittanceSource
    if (this.id) {
      this.getInfo(this.id)
    }
  },
  methods: {
    moment,
    getInfo(id) {
      let formData = {
        id: id,
      }
      getAction(this.url.info, formData, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          this.model = res.Data
          this.PaymentMode = this.model.PurchaseInfo.PaymentMode
          this.dataSource = this.model.PurchaseBills
          this.listImageUrl = this.model.Payment.ContractImages //合同照片
          this.listImageUrl3 = this.model.Payment.LetterImages //对账函图片
          if (this.model.Banks && this.model.Banks.length >= 1) {
            this.$set(this.model, "BankName", this.model.Banks[0].BankName) //开户银行
            this.$set(this.model, "BankAccount", this.model.Banks[0].BankAccount) //开户人
            this.$set(this.model, "BankCardNumber", this.model.Banks[0].BankCardNumber) //开户银行账号
          } else {
            this.$set(this.model, "BankName", "") //开户银行
            this.$set(this.model, "BankAccount", "") //开户人
            this.$set(this.model, "BankCardNumber", "") //开户银行账号
          }
          //   详情还原
          if (this.isInfo) {
            this.model.BankAccount = this.model.Banks[0].BankAccount
            this.model.BankName = this.model.Banks[0].BankName
            this.model.BankCardNumber = this.model.Banks[0].BankCardNumber
            // 还原打款信息
            if (this.model.Pays && this.model.Pays.length > 0) {
              this.model.Pays.map(item => {
                item.VoucherFiles = item.Files
              })
            }
            this.paymentInfo = this.model.Pays.length == 0 ? this.paymentInfoList() : (this.model.Pays || [])
          }
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {

      });
    },
    disabledDate(current) {
      return current && current < moment().subtract(1, 'days').endOf('day')
    },
    changeZhangBo(val, txt, r, item) {
      console.log(val, txt, r, item)
      item.AccountBookId = r.Id
      item.AccountBookName = r.ItemValue
      item.AccountBookCode = r.ItemPym
    },
    paymentInfoList() {
      let list = [
        this.paymentInfoItem()
      ]
      return list
    },
    paymentInfoItem() {
      return { PayAmount: 0, AccountBookId: '', PayTime: this.getTadayTime(), AccountBookName: '', AccountBookCode: '', VoucherFiles: [] }
    },
    getTadayTime() {
      var date = new Date();
      var seperator1 = "-";
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var strDate = date.getDate();
      if (month >= 1 && month <= 9) { month = "0" + month; }
      if (strDate >= 0 && strDate <= 9) { strDate = "0" + strDate; }
      var currentdate = year + seperator1 + month + seperator1 + strDate;
      return currentdate
    },
    changeBank(e) {
      this.$set(this.model, "BankName", this.model.Banks[e].BankName) //开户银行
      this.$set(this.model, "BankAccount", this.model.Banks[e].BankAccount) //开户人
      this.$set(this.model, "BankCardNumber", this.model.Banks[e].BankCardNumber) //开户人
      this.$refs.inputForm.validateField(['BankName'])
      this.$refs.inputForm.validateField(['BankAccount'])
      this.$refs.inputForm.validateField(['BankCardNumber'])
    },
    onTimeChange() {

    },
    addRemittance() {
      let item = this.paymentInfoItem()
      this.paymentInfo.push(item)
    },
    delPaymentInfo(index) {
      this.paymentInfo.splice(index, 1)
    },
    // 上传回调
    multiUploadChange(images) {
      this.listImageUrl = images
    },
    // 驳回打款
    rejectPayment() {
      let data = {
        Id: this.id,
      }
      this.$refs.CashierPayRejectPaymentModal.show(data)
    },
    modalOk() {
      this.handleCancel();
    },
    // 打款信息检测
    checkPaymentInfo() {
      let errList = []
      let allNum = 0
      this.paymentInfo.map(item => {
        allNum += this.numFixed((item.PayAmount || 0))
        if (!item.PayAmount) {
          errList.push('PayAmount')
        }
        if (!item.PayTime) {
          errList.push('PayTime')
        }
        if (!item.AccountBookId) {
          errList.push('AccountBookId')
        }
        if (!item.AccountBookName) {
          errList.push('AccountBookName')
        }
        if (!item.AccountBookCode) {
          errList.push('AccountBookCode')
        }
        // if (!item.VoucherFiles || item.VoucherFiles.length === 0) {
        //   errList.push('VoucherFiles')
        // }
      })
      console.log(errList)
      if (errList.length > 0) {
        this.$message.warning('请完善打款信息!')
        return false
      } else if (this.numFixed(allNum) !== this.numFixed(this.model.Payment.ActualPaymentAmount)) {
        this.$message.warning('打款金额之合需等于实际付款金额，请调整后提交!')
        return false
      } else {
        return true
      }

    },
    handleOk() {
      if (!this.checkPaymentInfo()) {
        return
      }
      const that = this;
      // 触发表单验证
      this.$refs.inputForm.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          let formData = {
            "RemittanceId": this.id,
            "BankAccount": this.model.BankAccount,
            "BankName": this.model.BankName,
            "BankCardNumber": this.model.BankCardNumber,
            "AccountBooks": this.paymentInfo
          }
          postAction(this.url.addSub, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("出纳打款成功");
              that.$emit("ok");
              that.handleCancel();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    handleCancel() {
      this.$root.$emit("removeTabsAndBack", this.$route.fullPath, '/pages/financialManagement/financialSettlement/cashierPayList');
    },
  },
}
</script>
<style lang="scss">
.num-box span {
  margin-right: 10px;
}
</style>