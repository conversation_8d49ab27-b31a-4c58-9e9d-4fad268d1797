<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab" />

  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "salesButtApplicationList",
  title: '销售对抵申请',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '销货对抵编号', type: 'input', value: '', key: 'SalesOffsetOrderNo', defaultVal: '', placeholder: '请输入' },
        { name: '对抵方', type: 'input', value: '', key: 'OffsetParty', defaultVal: '', placeholder: '请输入' },
        {
          name: '业务员',
          type: 'selectLink',
          vModel: 'SalesId',
          dataKey: { name: 'Name', value: 'Id' },
          httpParams: {},
          keyWord: 'KeyWord',
          defaultVal: '',
          placeholder: '请选择',
          httpType: 'GET',
          url: '/{v}/SalesOffset/GetSaleMan',
          httpHead: 'P36012'
        },
        { name: '创建时间', type: 'timeInput', value: '', key: ['CreateTimeBegin', 'CreateTimeEnd'], defaultVal: ['', ''], placeholder: ['开始日期', '结束日期'] },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择'
        },
        {
          name: '执行状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'ExecuteStatus',
          dictCode: 'EnumExecuteStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '同步状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PushErpStatus',
          dictCode: 'EnumPushERPStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增销货对抵', type: 'primary', icon: 'plus', key: 'add', id: '5167c2e2-a384-4cf9-9008-09d30b9da185', }
        ],
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
      columns: [
        {
          title: '销货对抵编号',
          dataIndex: 'SalesOffsetOrderNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对抵方',
          dataIndex: 'SupplierName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '业务员',
          dataIndex: 'SalesManName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对抵金额',
          dataIndex: 'OffsetAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Remark',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '执行状态',
          dataIndex: 'ExecuteStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushErpStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对抵单状态',
          dataIndex: 'SalesOffsetStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: "right",
          actionBtn: [
            // 0 未提交 1审核中 2审核通过 3审核失败
            {
              name: '详情', icon: '', id: '61b1d062-219a-4c14-8c2a-ff8327e983d1', specialShowFuc: e => {
                let record = e || {}
                return [0, 1, 2].includes(record.AuditStatus)
              }
            },
            {
              name: '编辑', icon: '', id: '98b4919f-42fa-4739-a7f0-0260518faced', specialShowFuc: e => {
                let record = e || {}
                return [0, 3].includes(record.AuditStatus)
              }
            },
            {
              name: '删除', icon: '', id: '8c6845b9-57c4-48df-b2f4-5dee91e86d7d', specialShowFuc: e => {
                let record = e || {}
                return [0, 3].includes(record.AuditStatus)
              }
            },],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {

      },
      IsListMixin: true,
      isTableInitData: true,//是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/SalesOffset/List',
        del: '/{v}/SalesOffset/Delete',
      },
    }
  },
  methods: {
    // 确定删除
    subDelInfo(record) {
      let params = {}
      let url = this.linkUrl.del + '?id=' + record.Id
      getAction(url, params, this.linkHttpHead)
        .then(res => {
          if (res.Data) {
            this.$message.success('删除成功')
            this.$refs.table.loadDatas(1, this.queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(err => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        this.goPage('salesButtApplicationAdd', {})
      } else if (type == '编辑') {
        this.goPage('salesButtApplicationAdd', { id: record.Id, isEdit: true })
      } else if (type == '详情') {
        this.goPage('salesButtApplicationInfo', { id: record.Id, isInfo: true })
      } else if (type == '删除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确定删除本销货对抵？删除后不能恢复',
          onOk() {
            that.subDelInfo(record)
          },
          onCancel() { }
        })
      }
    },

  }

}
</script>

<style>
</style>