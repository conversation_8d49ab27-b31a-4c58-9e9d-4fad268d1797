<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" :key="key" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @selectedRows="selectedRows" @operate="operate" @getListAmount="getListAmount" @changeTab="changeTab" />
    <!-- 驳回原因 -->
    <CashierPayRejectPaymentModal ref="CashierPayRejectPaymentModal" @ok="modalOk" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'cashierPayList',
  title: '出纳打款',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [],
      searchInput1: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '供应商/客户',
          type: 'input',
          value: '',
          key: 'KeyWord',
          defaultVal: '',
          placeholder: '名称/拼音码/编号',
        },
        { name: '付款/销退单号', type: 'input', value: '', key: 'BusinessNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '结算方式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'SettlementType',
          dictCode: 'EnumSaleAfterRefundType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '打款来源',
          type: 'selectBasicLink',
          value: '',
          vModel: 'RemittanceSource',
          dictCode: 'EnumRemittanceSource',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '同步状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PushErpStatus',
          dictCode: 'EnumPushERPStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
      ],
      searchInput2: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '供应商/客户',
          type: 'input',
          value: '',
          key: 'KeyWord',
          defaultVal: '',
          placeholder: '名称/拼音码/编号',
        },
        { name: '付款/销退单号', type: 'input', value: '', key: 'BusinessNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '结算方式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'SettlementType',
          dictCode: 'EnumSaleAfterRefundType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '执行状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'ExecuteStatus',
          dictCode: 'EnumExecuteStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '同步状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PushErpStatus',
          dictCode: 'EnumPushERPStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
      ],
      searchInput3: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '供应商/客户',
          type: 'input',
          value: '',
          key: 'KeyWord',
          defaultVal: '',
          placeholder: '名称/拼音码/编号',
        },
        { name: '付款/销退单号', type: 'input', value: '', key: 'BusinessNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '结算方式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'SettlementType',
          dictCode: 'EnumSaleAfterRefundType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '执行状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'ExecuteStatus',
          dictCode: 'EnumExecuteStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '取消导出', icon: '', isHide: true, key: '取消导出', },
          { name: '确定导出', type: 'primary', icon: '', isHide: true, key: '确定导出', },
          { name: '导出', type: 'primary', icon: 'download', key: '导出', id: '941bcc2b-0609-49f8-8141-1d584223db70', }
        ],
        hintArray: [],
        rowSelection: null,
        rowSelectionCheckbox: {
          type: 'checkbox',
          getCheckboxProps: this.getCheckboxProps,
        },
        rowKey: 'Id',
        status: 1,
        statusKey: 'RemittanceStatus', //标签的切换关键字
        statusList: [
          {
            name: '待打款',
            value: 1,
            key: 'UnPayCount',
            count: 0,
          },
          {
            name: '已打款',
            value: 3,
            key: 'PayedCount',
            count: 0,
          },
          {
            name: '已驳回',
            value: 4,
            key: 'RejectionCount',
            count: 0,
          },
          {
            name: '已冲红',
            value: 4101,
            key: 'RedOffSetCount',
            count: 0,
          },
        ],
      },
      columns: [],
      columns1: [
        {
          title: '供应商/客户名称',
          dataIndex: 'PayerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款/销退单号',
          dataIndex: 'BusinessNo',
          width: 170,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款金额',
          dataIndex: 'ActualPaymentAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款来源',
          dataIndex: 'RemittanceSourceStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '结算方式',
          dataIndex: 'SettlementTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushErpStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '提交时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          fixed: 'right',
          actionBtn: [
            { name: '打款', icon: '', id: '1580efe0-6b18-4ee1-a05a-b653da465efa' },
            {
              name: '驳回打款', icon: '', id: 'a07da025-7272-4fd0-b085-1d6fbc438ea4', specialShowFuc: record => {
                return record.RemittanceSource == 1
              }
            },
            {
              name: '重置导出', icon: '', id: 'c9bb7161-2691-42ce-8773-c1bf26356e04', specialShowFuc: record => {
                return record.ExportTime ? true : false
              }
            }
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      columns2: [
        {
          title: '供应商/客户名称',
          dataIndex: 'PayerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款/销退单号',
          dataIndex: 'BusinessNo',
          width: 170,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款金额',
          dataIndex: 'ActualPaymentAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款来源',
          dataIndex: 'RemittanceSourceStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '结算方式',
          dataIndex: 'SettlementTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '执行状态',
          dataIndex: 'ExecuteStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushErpStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 160,
          fixed: 'right',
          actionBtn: [
            { name: '详情', icon: '', id: '8999200d-b7cd-4bfb-a5ed-248ab2d18f43' },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      columns3: [
        {
          title: '供应商/客户名称',
          dataIndex: 'PayerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款/销退单号',
          dataIndex: 'BusinessNo',
          width: 170,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款金额',
          dataIndex: 'ActualPaymentAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '结算方式',
          dataIndex: 'SettlementTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '执行状态',
          dataIndex: 'ExecuteStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 160,
          fixed: 'right',
          actionBtn: [
            { name: '详情', icon: '', id: '8999200d-b7cd-4bfb-a5ed-248ab2d18f43' },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      columns4: [
        {
          title: '供应商/客户名称',
          dataIndex: 'PayerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款/销退单号',
          dataIndex: 'BusinessNo',
          width: 170,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款金额',
          dataIndex: 'ActualPaymentAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款来源',
          dataIndex: 'RemittanceSourceStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '结算方式',
          dataIndex: 'SettlementTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '执行状态',
          dataIndex: 'ExecuteStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushErpStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 160,
          fixed: 'right',
          actionBtn: [
            { name: '详情', icon: '', id: '8999200d-b7cd-4bfb-a5ed-248ab2d18f43' },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      key: 0,
      selectedRowKeys: [],
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/Settlement/GetRemittanceList',
        listAmount: '/{v}/Settlement/GetRemittanceCount',
        exportPDFUrl: '/{v}/Settlement/PrintRemittanceList',
        resetExport: '/{v}/Settlement/ExecuteExportTime/ExecuteExportTime',
      },
    }
  },
  created() {
    this.searchInput = this.searchInput1
  },
  activated() {
    this.columns = this.columns1
    this.queryParam = this.$refs.SimpleSearchArea.queryParam || {}
    let from = this.$root.fromPgae || null
    let page = null
    if (from && from === 'againRemit') {
      this.tab.status = 1
      page = 1
    }
    this.queryParam.RemittanceStatus = this.tab.status
    this.key = 0
    this.setColumns(this.tab.status)
    this.$refs.table.loadDatas(page, this.queryParam)
    this.$root.fromPgae = null
  },
  methods: {
    getListAmount(data) {
      this.tab.statusList[0].count = (data && data.UnPayCount) || 0
      this.tab.statusList[1].count = (data && data.PayedCount) || 0
      this.tab.statusList[2].count = (data && data.RejectionCount) || 0
      this.tab.statusList[3].count = (data && data.RedOffSetCount) || 0
    },
    // 状态切换
    changeTab(value, statusKey) {
      this.key++
      this.$set(this.$refs.SimpleSearchArea.queryParam, 'RemittanceSource', '') //打款来源
      this.$set(this.$refs.SimpleSearchArea.queryParam, 'ExecuteStatus', '') //执行状态
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      if (statusKey) {
        this.queryParam[statusKey] = value
      }
      this.setColumns(value)
      if (this.tab.operateBtn[0].isHide == false) {
        if (this.tab.status == false) {
          this.tab.rowSelection = this.tab.rowSelectionCheckbox
        } else {
          this.closeImport()
        }
      }
      this.$refs.table.loadDatas(1, this.queryParam)
      this.$nextTick(() => {
        this.$refs.SimpleSearchArea.queryParam = this.queryParam
      })
    },
    selectedRows(selectedRowKeys, selectedRows) {
      if (selectedRowKeys.length == 0) {
        this.tab.operateBtn[1].isHide = true
      } else {
        this.tab.operateBtn[1].isHide = false
      }
      this.selectedRowKeys = selectedRowKeys
    },
    setColumns(value) {
      if (value == 1) {
        // 待打款
        this.searchInput = this.searchInput1
        this.columns = this.columns1
      } else if (value == 3) {
        // 已打款
        this.searchInput = this.searchInput2
        this.columns = this.columns2
      } else if (value == 4) {
        // 已驳回
        this.searchInput = this.searchInput3
        this.columns = this.columns3
      } else if (value == 4101) {
        // 已冲红
        this.searchInput = this.searchInput3
        this.columns = this.columns4
      }
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (type == 'searchReset') {
        this.key++
        this.tab.status = 1
        queryParam.RemittanceStatus = 1
        this.setColumns(1)
      } else {
        queryParam.RemittanceStatus = this.queryParam.RemittanceStatus
      }
      this.$refs.table.loadDatas(1, queryParam)
    },
    closeImport() {
      this.tab.operateBtn[0].isHide = true
      this.tab.operateBtn[1].isHide = true
      this.tab.rowSelection = null
      this.selectedRowKeys = []
      this.$refs.table.selectedRowKeys = []
    },
    getCheckboxProps(record) {
      return {
        props: {
          disabled: !!record['ExportTime'],
        },
      }
    },
    modalOk() {
      this.$refs.table.loadDatas(1, queryParam)
    },
    subResetExport(record) {
      let params = {}
      let url = this.linkUrl.resetExport + '?businessNo=' + record.BusinessNo
      postAction(url, params, this.linkHttpHead)
        .then(res => {
          if (res.Data) {
            this.$message.success('操作成功')
            this.$refs.table.loadDatas(null, this.queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(err => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    // 列表操作
    operate(record, type) {
      if (type == '打款') {
        // RemittanceSource 1 采购付款 2 销售退货
        if (record.RemittanceSource == 1) {
          this.goPage('cashierPayAdd', { id: record.Id, remittanceSource: record.RemittanceSource })
        } else if (record.RemittanceSource == 2) {
          this.goPage('cashierPayRefundAdd', { id: record.Id, remittanceSource: record.RemittanceSource })
        }
      } else if (type == '驳回打款') {
        this.$refs.CashierPayRejectPaymentModal.show(record)
      } else if (type == '重置导出') {
        let that = this
        this.$confirm({
          title: '确认重置打款单的导出功能吗?',
          content: '',
          onOk() {
            that.subResetExport(record)
          },
          onCancel() { },
        });
      } else if (type == '详情') {
        this.goPage('cashierPayInfo', { id: record.Id, remittanceSource: record.RemittanceSource, isInfo: true })
      } else if (type == '导出') {
        if (this.tab.status != 1) {
          this.$message.warning('请切换到待打款状态再导出')
          return
        }
        this.tab.rowSelection = this.tab.rowSelectionCheckbox
        this.tab.operateBtn[0].isHide = false
      } else if (type == '取消导出') {
        this.closeImport()
      } else if (type == '确定导出') {
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning('请选择要导出的数据')
          return
        }
        let params = {
          IdList: this.selectedRowKeys
        }
        let queryParam = this.$refs.SimpleSearchArea.queryParam
        queryParam.RemittanceStatus = this.queryParam.RemittanceStatus
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth() + 1;
        const day = now.getDate();
        const hour = now.getHours();
        const minute = now.getMinutes();
        const second = now.getSeconds();
        const nowTime = `${year}${month < 10 ? '0' + month : month}${day < 10 ? '0' + day : day}${hour}${minute < 10 ? '0' + minute : minute}${second < 10 ? '0' + second : second}`
        let exportTitle = '出纳打款' + nowTime.toString() + '(共' + this.selectedRowKeys.length + '条记录)'
        this.handleExportXls(
          exportTitle,
          'post',
          this.linkUrl.exportPDFUrl,
          null,
          this.linkHttpHead,
          params,
          '.pdf', () => {
            // this.$message.success('导出成功')
            this.closeImport()
            this.$refs.table.loadDatas(1, queryParam)
          }
        )

      }


    },
  },
}
</script>

<style></style>
