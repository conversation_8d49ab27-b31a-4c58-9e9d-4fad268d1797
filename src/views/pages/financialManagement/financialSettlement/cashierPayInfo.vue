<template>
  <div>
    <CashierPayAddModule v-if="remittanceSource == 1" ref="CashierPayAddModule" />
    <CashierPayAddRefundAddModule v-if="remittanceSource == 2" ref="CashierPayAddRefundAddModule" />
  </div>
</template>

<script>
export default {
  title:'出纳打款详情',
  name: 'cashierPayInfo',
  data() {
    return {
      remittanceSource: null, //1 采购付款 2 销售退货
    }
  },
  created() {
    this.remittanceSource = this.$route.query.remittanceSource
  },
  methods: {

  },
}
</script>

<style>
</style>