<template>
    <!-- 客户对账（审核/详情）页 -->
    <ReconciliationReviewBase :ReconciliationType="ReconciliationType" :isEdit="isEdit"/>
  </template>
  <script>
  
  export default {
    name: 'customerReconciliationAuditDetail',
    data() {
      return {
        ReconciliationType: 2,  // 1-供应商对账 2-客户对账
        isEdit: false // true-对账审核 2-对账详情查看
      }
    },
    computed: {},
    created() {
      if (this.$route.query) {
        this.isEdit = Boolean(this.$route.query.isEdit === 'true')
      }
    },
    mounted() {
    },
    activated() {},
  
    methods: {
    },
  }
  </script>
  <style scoped lang="scss">
  </style>
  