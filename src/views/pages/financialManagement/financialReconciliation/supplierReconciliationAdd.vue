<template>
  <!-- 供应商对账编辑页 -->
  <div>
    <div
      style="background: #fbe6e9; padding: 10px 16px; color: red"
      v-if="form.AuditStatus == 3 && form.AuditOpinion"
      :md="24"
    >
      <span>驳回原因：{{ form.AuditOpinion || ' -- ' }}</span>
    </div>
    <ReconciliationDetails ref="ReconciliationDetails" @getBasicInfo="getBasicInfo" :ReconciliationType="1" />
  </div>
</template>
<script>
export default {
  name: 'supplierReconciliationAdd',
  data() {
    return {
      form: {}
    }
  },
  computed: {},
  created() {},
  mounted() {},
  activated() {},
  beforeRouteLeave(to, form, next) {
    this.$refs.ReconciliationDetails.stopLoop()
    next()
  },
  methods: {
    // 获取基本信息回调
    getBasicInfo(info) {
      this.form = info
    },
  },
}
</script>
<style scoped lang="scss">
</style>
