<!-- 供应商对账审核 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInputData" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" 
    :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" 
    :columns="columnsData" :isTableInitData="isTableInitData" @operate="operate" 
    @getListAmount="getListAmount" @changeTab="changeTab" >
      <!-- 账期 -->
      <span slot="PeriodStart" slot-scope="{ text, record }">
        <j-ellipsis
          :value="`${text ? text.substring(0, 11) : ''} 到 ${
            record['PeriodEnd'] ? record['PeriodEnd'].substring(0, 11) : ''
          }`"
          :length="40"
        />
      </span>
    </SimpleTable>
    
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, deleteAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')

export default {
  name: "supplierReconciliationAudit",
  mixins: [SimpleMixin],
  components: { JEllipsis },
  data() {
    return {
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '供应商名称', type: 'input', value: '', key: 'CooperationCompanyName', defaultVal: '', placeholder: '请输入供应商名称/编号' },
        { name: '对账单号', type: 'input', value: '', key: 'StatementNo', defaultVal: '', placeholder: '请输入对账单号' },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入创建人' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '审核时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['AuditTimeBegin', 'AuditTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: '1',
        statusKey: 'ApproavalStatus',//标签的切换关键字
        statusList: [
          {
            name: '待审核',
            value: '1',
            count: 0,
          },
          {
            name: '审核记录',
            value: '2',
            count: 0,
          },
        ],
        rowKey: 'Id',
        sortArray: ['PeriodStart'],
      },
      columns: [
        {
          title: '对账单号',
          dataIndex: 'StatementNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '账期',
          dataIndex: 'PeriodStart',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'PeriodStart' },
        },
        {
          title: '供应商名称',
          dataIndex: 'CooperationCompanyName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '账单金额',
          dataIndex: 'TotalAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核时间',
          dataIndex: 'ApprovalSubmitDate',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: "right",
          actionBtn: [
            { name: '审核', icon: '', id: '403a4ea0-6e28-48fb-9d4c-a90d77989d06',
              specialShowFuc: (record) => {
                return this.tab.status == 1 ? true :false
              },
             },
            { name: '详情', icon: '', id: '96426d51-d10c-4871-a910-6232ec0ec6b3', 
              specialShowFuc: (record) => {
                return this.tab.status == 2 ? true :false
              },
            },
          ],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {
        StatementSource:1,//对账单来源 供应商对账=1,客户对账=2
      },
      isTableInitData: false,//是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/PurchaseSalesStatementAudit/GetPurchaseSalesStatementAuditlist',
        listAmount: '/{v}/PurchaseSalesStatementAudit/GetPurchaseSalesStatementAuditQuantityStatistics',
      },
    }
  },
  created() {

  },
  activated(){
    
    this.queryParam = this.$refs.SimpleSearchArea.queryParam
    this.queryParam.StatementSource = 1
    this.queryParam.ApproavalStatus = this.queryParam.ApproavalStatus || 1
    // console.log('activated',this.queryParam)
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  mounted() {
   
  },
  computed: {
    searchInputData() {
      // console.log(this.tab.status)
      return this.tab.status==1 ? this.searchInput.filter(v=>v.name !='审核状态' && v.name !='审核时间') : this.searchInput
    },
    columnsData() {
      // console.log(this.tab.status)
      return this.tab.status==1 ? this.columns.filter(v=>v.title !='审核状态' && v.title !='审核时间') : this.columns
    }
  },
  methods: {
    changeTab(val) {
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      this.queryParam.ApproavalStatus = val

      this.$refs.table.loadDatas(1, this.queryParam)
    },
    getListAmount(data) {
      this.tab.statusList[0].count = (data && data.WaitAuditCount) || 0
      this.tab.statusList[1].count = (data && data.AuditRecordCount) || 0
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      // console.log('type', type)
      // console.log('queryParam', this.queryParam)
      if (!this.$refs.table) {
        return
      }
      queryParam.StatementSource =1
      queryParam.ApproavalStatus = this.queryParam.ApproavalStatus || 1
      if (type == 'searchReset') {
        // queryParam.StatementSource = 1
        // this.tab.status = '1'
        // queryParam.ApproavalStatus = 1
      }
      this.queryParam.StatementSource = queryParam.StatementSource
      this.queryParam.ApproavalStatus = queryParam.ApproavalStatus
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 列表操作
    operate(record, type) {
      switch (type) {
        case '审核':
        this.onDetailClick('supplierReconciliationAuditDetail', {id:record.Id,isEdit:true})
          break
        case '详情':
          this.onDetailClick('supplierReconciliationAuditDetail', {id:record.Id})
          break
      }
     
    },

  }

}
</script>

<style>
</style>