<!-- 供应商对账列表 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate"  >
      <!-- 账期 -->
      <span slot="PeriodStart" slot-scope="{ text, record }">
        <j-ellipsis
          :value="`${text ? text.substring(0, 11) : ''} 到 ${
            record['PeriodEnd'] ? record['PeriodEnd'].substring(0, 11) : ''
          }`"
          :length="40"
        />
      </span>
    </SimpleTable>
     <!-- 新增对账单 -->
     <SupplierReconciliationListAddModal 
      :StatementSource="1"
      ref="iSupplierReconciliationListAddModal" @ok="$refs.table.loadDatas(1, $refs.SimpleSearchArea.queryParam)" />
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, deleteAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')

export default {
  name: "supplierReconciliationList",
  mixins: [SimpleMixin],
  components: { JEllipsis },
  data() {
    return {
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '供应商名称', type: 'input', value: '', key: 'CooperationCompanyName', defaultVal: '', placeholder: '请输入供应商名称/编号' },
        { name: '对账单号', type: 'input', value: '', key: 'StatementNo', defaultVal: '', placeholder: '请输入对账单号' },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入创建人' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增对账单', type: 'primary', icon: 'plus', key: 'add', id: '4a27ca01-1272-4eea-8b31-0b8eb7e344b1' },
        ],
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
        rowKey: 'Id',
        sortArray: ['PeriodStart'],
      },
      columns: [
        {
          title: '对账单号',
          dataIndex: 'StatementNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '账期',
          dataIndex: 'PeriodStart',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'PeriodStart' },
        },
        {
          title: '供应商名称',
          dataIndex: 'CooperationCompanyName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '账单金额',
          dataIndex: 'TotalAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: "right",
          actionBtn: [
            { name: '对账', icon: '', id: 'b57ecca9-bc4e-44a0-b762-d604f61b071a',
              specialShowFuc: (record) => {
                return [0,3].includes(record.AuditStatus) ?true :false
              },
             },
            { name: '删除', icon: '', id: 'f3101b58-2c3d-416e-a0f8-f5f0e1ab241a',
              specialShowFuc: (record) => {
                return [0,3].includes(record.AuditStatus)?true :false
              },
             },
            { name: '详情', icon: '', id: 'c6abb4c8-1469-4853-b6cf-bde6459e4eb7', 
              specialShowFuc: (record) => {
                return record.AuditStatus > 0 && record.AuditStatus != 3 ?true :false
              },
            },
          ],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {
        StatementSource:1,//对账单来源 供应商对账=1,客户对账=2
      },
      isTableInitData: false,//是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/PurchaseSalesStatement/GetPurchaseSalesStatementlist',
        del:'/{v}/PurchaseSalesStatement/DeletePurchaseSalesStatement'
      },
    }
  },
  created() {

  },
  activated(){
    this.queryParam = this.$refs.SimpleSearchArea.queryParam
    this.queryParam.StatementSource = 1
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  mounted() {
  },
  methods: {
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      queryParam.StatementSource =1
      if (type == 'searchReset') {
        queryParam.StatementSource = 1
      }
      this.queryParam.StatementSource = queryParam.StatementSource
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 列表操作
    operate(record, type) {
      switch (type) {
        case 'add':
          this.$refs.iSupplierReconciliationListAddModal.add()
          break
        case '对账':
        this.onDetailClick('supplierReconciliationAdd', {id:record.Id})
          break
        case '删除':
          let that = this
          this.$confirm({
            title: '提示',
            content: '您确定要删除该对账单吗？',
            onOk() {
              that.subDel(record)
            },
            onCancel() { },
          })
          break
        case '详情':
          this.onDetailClick('supplierReconciliationDetailInfo', {id:record.Id})
          break
      }
     
    },
    // 确定删除
    subDel(record) {
    // let params = {
    //   id: record.Id
    // }
    let url = this.linkUrl.del
    deleteAction(url + '?id='+record.Id, {}, this.linkHttpHead)
      .then((res) => {
        if (res.Data) {
          this.$message.success('删除成功')
          this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
        } else {
          this.$message.error(res.Msg)
        }
      })
      .catch((err) => {
        this.$message.error(err.Msg || err)
      })
      .finally(() => { })
  },

  }

}
</script>

<style>
</style>