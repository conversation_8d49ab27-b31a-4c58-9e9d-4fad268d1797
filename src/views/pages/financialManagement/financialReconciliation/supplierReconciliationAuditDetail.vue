<template>
  <!-- 供应商对账（审核/详情）页 -->
  <ReconciliationReviewBase :ReconciliationType="ReconciliationType" :isEdit="isEdit"/>
</template>
<script>

export default {
  name: 'supplierReconciliationAuditDetail',
  data() {
    return {
      ReconciliationType: 1,  // 1-供应商对账 2-客户对账
      isEdit: false // true-对账审核 false-对账详情查看
    }
  },
  computed: {},
  created() {
    if (this.$route.query) {
      this.isEdit = Boolean(this.$route.query.isEdit === 'true')
    }
  },
  mounted() {
  },
  activated() {},

  methods: {
  },
}
</script>
<style scoped lang="scss">
</style>
