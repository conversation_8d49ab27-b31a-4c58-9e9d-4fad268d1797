<!-- 新增对账单 -->
<template>
  <a-modal :title="title" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="供应商名称：" prop="CooperationCompanyId" v-if="StatementSource === 1">
                <!-- <a-input placeholder="请输入" v-model="model.AccountName" style="width: 100%" /> -->
                <SingleChoiceSearchView style="width: 100%" httpType='POST' placeholder="请选择" :httpParams="{ SourceCategory: 2}" httpHead="P36012" :keyWord="'KeyWord'" Url="/{v}/CooperationCompany/GetOutCooperationCompanys" :dataKey="{ name: 'Name', value: 'Id' }" v-model="model.CooperationCompanyId" :name="model.CooperationCompanyName" @change="(val, txt, item) => changeGys(val, txt, item)" />
              </a-form-model-item>
              <a-form-model-item label="委托人：" prop="AgentCode" v-if="StatementSource === 1">
                <SingleChoiceView ref="singleChoiceViewTWR" style="width: 100%" :placeholder="!model.CooperationCompanyId?'请先选择供应商名称':'请选择'" :httpParams="{ SupplierId: model.SupplierId, OrderType: 1 }" httpHead="P36003" :dataKey="{ name: 'DelegateName', value: 'DelegateCode' }" v-model="model.AgentCode" :disabled="!model.CooperationCompanyId" Url="/{v}/Supplier/GetSupplierDelegateScope"  @change="handleAgentCode" />
              </a-form-model-item>
              <a-form-model-item label="客户名称：" prop="CooperationCompanyId" v-if="StatementSource === 2">
                <!-- <a-input placeholder="请输入" v-model="model.AccountName" style="width: 100%" /> -->
                <SingleChoiceSearchView style="width: 100%" httpType='POST' placeholder="请输入" :httpParams="{SourceCategory:1 }" :dataKey="{ name: 'Name', value: 'Id' }"  httpHead="P36012" keyWord="KeyWord" :Url="'/{v}/CooperationCompany/GetOutCooperationCompanys'" v-model="model.CooperationCompanyId" :name="model.CooperationCompanyName" @change="handleCustomer" />
              </a-form-model-item>
              <a-form-model-item label="账期：" prop="PeriodStart">
                <a-range-picker @change="
                    (dates, dateStrings) => onTimeChange(dates, dateStrings, 'PeriodStart', 'PeriodEnd', true)
                  " v-model="rangeDate" style="width: 100%" />
              </a-form-model-item>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">确定</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "SupplierReconciliationListAddModal",
  components: {},
  props:{
    StatementSource:{
      type:Number,
      default:null
    }
  },
  mixins: [EditMixin],
  data() {
    return {
      title: this.StatementSource === 1? "新增对账单" : "新增账期",
      visible: false,
      isEdit: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      rangeDate:[],
      model: {
        StatementSource:this.StatementSource, //对账单来源 供应商对账=1,客户对账=2
        CooperationCompanyId:null,
        SupplierId:null,
        CooperationCompanyName:'',
        AgentCode:null,
        AgentName:'',
        PeriodStart:'',
        PeriodEnd:'',
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      httpHead: 'P36012',
      url: {
        getCustomerListForOrder: '/{v}/Customer/ListForOrder',
        add: "/{v}/PurchaseSalesStatement/CreatePurchaseSalesStatement",
        update: "/{v}/CashReceiptNew/EditReceiptAccount",
      },
    };
  },
  computed: {
    rules() {
      return {
        CooperationCompanyId: [{ required: true,message:'请选择', trigger: 'change' }],
        AgentCode: [{ required: false , trigger: 'change' ,message:'请选择',}],
        PeriodStart: [
          {
            required: true,
            validator: (rule, value, callback) =>{
              if (!this.model.PeriodStart || !this.model.PeriodEnd) {
                callback('请选择')
              } else {
                callback()
              }
            }}],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add() {
      this.title = '新增对账单'
      this.model = {}
      this.isEdit = false;
      this.visible = true;
    },
    edit(record) {
      this.title = '编辑对账单'
      this.model = Object.assign({}, record);
      this.isEdit = true;
      this.visible = true;
    },
    // 选择 供应商名称
    changeGys(val, txt, item) {
      this.model.AgentCode = ''
      this.model.AgentName = ''
      this.model.CooperationCompanyName = txt
      this.model.CooperationCompanyId = val
      if(item) this.model.SupplierId = item.SupplierId
      if(val){
        this.$refs.singleChoiceViewTWR.getData('SupplierId', this.model.SupplierId)
      }
     
    },
    // 选择委托人
    handleAgentCode(val, txt, item){
      if (val) {
        this.model.AgentName = txt || ''
        this.model.AgentCode = val || ''
      } else {
        this.model.AgentName = ''
        this.model.AgentCode = ''
      }
      this.$nextTick(() => {
        this.$forceUpdate()
        this.$refs.form.validateField('AgentCode')
      })
    },
    // 选择客户
    handleCustomer(val, txt, item) {
      // console.log(val, txt, item)
      this.$set(this.model, 'CooperationCompanyId', val)
      this.$set(this.model, 'CooperationCompanyName', txt)
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          let formData = {}
          let url = ''
          // if (this.isEdit) {
          //   url = this.url.update
          //   formData = {
          //     Id: this.model.Id,
          //     AccountName: this.model.AccountName,
          //     BankName: this.model.BankName,
          //     BankNo: this.model.BankNo.toString(),
          //     IsValid: true,
          //   }
          // } else {
            url = this.url.add
            formData = {
              StatementSource:this.StatementSource, //对账单来源 供应商对账=1,客户对账=2
              CooperationCompanyId: this.model.CooperationCompanyId,
              CooperationCompanyName: this.model.CooperationCompanyName,
              AgentCode: this.model.AgentCode,
              AgentName: this.model.AgentName,
              PeriodStart: this.model.PeriodStart,
              PeriodEnd: this.model.PeriodEnd,
            }
          // }
          postAction(url, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
      this.rangeDate = []
    },
  },
};
</script>
