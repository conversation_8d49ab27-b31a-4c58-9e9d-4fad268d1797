<template>
  <!-- 未匹配商品记录 -->
  <div>
    <a-spin :spinning="confirmLoading">
      <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
      <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
      <div class="noBorder">
        <div class="flex">
          <div>合计金额：<span v-if="priceInfo.TotalAmount < 0">-</span>￥{{ Math.abs(priceInfo.TotalAmount) }}</div>
          <template v-if="!ifAudit">
            <div v-if="ReconciliationType === supplierReconciliationType">
              <YYLButton
                :menuId="'e8624061-15d2-4e38-88ba-38c2549d0202'"
                text="导出"
                @click="exportNow"
                type="primary"
              />
              <YYLButton
                :menuId="'c473e9c3-e558-4fb3-9856-674a99fbb314'"
                text="模糊匹配"
                @click="showFuzzy"
                type="primary"
              />
              <YYLButton
                :menuId="'927f8e4d-08b2-4f6e-81cf-d890aac0fff8'"
                text="批量移除"
                type="primary"
                @click="batchRemoval"
              />
            </div>
            <div v-if="ReconciliationType === customerReconciliationType">
              <YYLButton
                :menuId="'b366de30-5ed4-4811-854b-e889c5e48263'"
                text="导出"
                @click="exportNow"
                type="primary"
              />
              <YYLButton
                :menuId="'6a340df1-d573-42ee-96aa-a3ded3a8883c'"
                text="模糊匹配"
                @click="showFuzzy"
                type="primary"
              />
              <YYLButton
                :menuId="'28331595-beb1-4bb3-ae7c-453a19cc6e8f'"
                text="批量移除"
                type="primary"
                @click="batchRemoval"
              />
            </div>
          </template>
        </div>
      </div>
      <!-- 列表 -->
      <TableNewView
        ref="tableView"
        :table="table"
        :columns="columns"
        :tabDataList="tabDataList"
        :dataList="dataSource"
        @operate="operate"
      ></TableNewView>
    </a-spin>
    <!-- 模糊匹配弹窗 -->
    <FuzzyMatching
      ref="FuzzyMatching"
      v-if="showVague"
      :thirdPartyName="thirdPartyName"
      @close="showVague = false"
      @confirmBind="confirmBind"
    />
    <!-- 未匹配商品编辑弹窗 -->
    <UnmatchedRecordAddModal ref="UnmatchedRecordAddModal" @confirmEdit="confirmEdit" />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'UnmatchedProductRecord',
  inject: [
    'ReconciliationType',
    'supplierReconciliationType',
    'customerReconciliationType',
    'ifAudit',
    'reconciliationId',
    'isExamine',
  ],
  mixins: [ListMixin],
  props: {
    thirdPartyName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      confirmLoading: false,
      searchItems: [
        {
          name: '商品',
          type: this.$SEnum.INPUT,
          key: 'GoodsCodeOrName',
          disabled: true,
          placeholder: '请输入商品名称/编号',
        },
        {
          name: '生产厂家',
          type: this.$SEnum.INPUT,
          key: 'Manufacturer',
          placeholder: '请输入生产厂家',
        },
      ],
      tabDataList: [],
      dataSource: [],
      httpHead: 'P36012',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/PurchaseSalesStatement/GetUnmatchedGoodsQuery', // 当前列表数据接口
        exportUrl: '/{v}/PurchaseSalesStatement/ExportUnmatchedGoods', // 导出未匹配商品
        delete: '/{v}/PurchaseSalesStatement/RemoveUnmatchedGoods', // 删除未匹配商品
      },
      urlExamine: {
        list: '/{v}/PurchaseSalesStatementAudit/GetUnmatchedGoodsQuery', // 当前列表数据接口
      },
      priceInfo: {}, //价格信息
      showVague: false, // 是否显示模糊匹配弹窗
    }
  },
  computed: {
    columns() {
      let ifAudit = this.ifAudit
      let basicArry = [
        {
          title: '商品编号',
          dataIndex: 'OtherPartyErpGoodsCode',
          width: 150,
          ellipsis: true,
          align: 'center',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'OtherPartyErpGoodsName',
          width: 150,
          align: 'center',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'OtherPartyPackingSpecification',
          width: 150,
          align: 'center',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'OtherPartyBrandManufacturer',
          width: 200,
          align: 'center',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '合计金额',
          dataIndex: 'TotalAmount',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '编辑',
              icon: '',
              id: this.editMenuId,
              isShow: (record) => {
                return true
              },
            },
            {
              name: '移除',
              icon: '',
              id: this.removeId,
              isShow: (record) => {
                return true
              },
            },
          ],
        },
      ]

      if (ifAudit) {
        basicArry = basicArry.filter((f) => f.title !== '操作')
      }

      return basicArry
    },
    // 表格编辑按钮Id
    editMenuId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return 'ad0ce0a5-76ab-45f8-b270-c0981e127dcc'
        case customerReconciliationType:
          return 'bfcb2c57-2d2c-47e4-9f2f-08141abba3f7'
      }
    },
    // 表格移除按钮Id
    removeId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return '7c17f876-056b-469a-a3c6-2bfa56fa5544'
        case customerReconciliationType:
          return '44caaa89-3186-41aa-9a30-abb9f092c216'
      }
    },
  },
  watch: {
    ReconciliationType: {
      handler(val) {
        let table = {
          rowKey: 'Id',
          selectType: 'checkbox',
          isSelectCurPage: true,
        }
        let ifAudit = this.ifAudit
        // 针对审核处理
        if (ifAudit) {
          table.selectType = null
        }
        this.table = table
      },
      immediate: true,
    },
  },
  created() {
    if (this.isExamine) {
      // 审核页面进入需要替换对应的接口
      this.url = Object.assign({}, this.url, this.urlExamine)
    }
  },
  mounted() {},
  activated() {},
  methods: {
    loadBefore() {
      const isExamine = this.isExamine
      this.queryParam[isExamine ? 'purchaseSalesStatementRecordId' : 'purchaseSalesStatementId'] = this.reconciliationId
      this.priceInfo = {}
    },
    loadAfter(Data) {
      let total = this.ipagination.total
      this.dataSource = Data.UnmatchedGoodses
      const { TotalAmount } = Data
      this.priceInfo = {
        TotalAmount,
      }
      // this.dataListTest = Data.ItemList
      this.$emit('updateCount', 4, total)
    },
    // 操作
    operate(record, key, index) {
      console.log(record, key, index)
      if (key == '移除') {
        this.deleteAll([record.Id])
      } else if (key == '编辑') {
        this.$refs.UnmatchedRecordAddModal.show(record)
      }
    },
    // 批量移除按钮点击事件
    batchRemoval() {
      let selectedRowKeys = this.selectedRowKeys
      this.deleteAll(selectedRowKeys)
    },
    // 批量移除
    deleteAll(ids) {
      let that = this
      if (ids.length < 1) {
        this.$message.warning('请先勾选需要操作的数据')
        return false
      }
      this.$confirm({
        title: '提示',
        content: '您确定移除该数据么?',
        onOk() {
          let url = that.url.delete
          let formData = ids
          that.confirmLoading = true
          postAction(url, formData, that.httpHead)
            .then((res) => {
              that.confirmLoading = false
              if (res.IsSuccess) {
                that.$message.success('操作成功!')
                that.loadData(1)
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        },
        onCancel() {},
      })
    },
    // 导入未匹配修正记录保存
    handleBatchImportModalOk(data, type) {
      console.log(data)
      // const ids =
      //   this.storedataSource.map((f) => {
      //     return f.GoodsSpuId
      //   }) || []
      // let newData = data.filter((f) => !ids.includes(f))
      // if (data.length > 0) {
      //   if (type == 'goods') {
      //     this.storedataSource = this.storedataSource.concat(newData)
      //   }
      // }
      // this.showFilterdataSource()
    },
    // 导入修正记录
    importRecord() {
      this.$refs.iBatchImportRecordModal.show(this.dataSource)
    },
    // 显示模糊匹配
    showFuzzy() {
      this.showVague = true
    },
    // 导出按钮点击事件
    exportNow() {
      const param = this.getQueryParams() //查询条件
      console.log(param)
      this.handleExportXls('对账未匹配商品记录表', 'Get', this.url.exportUrl, null, this.httpHead, param)
    },
    // 确认编辑回调
    confirmEdit() {
      this.loadData(1)
    },
    // 模糊匹配绑定/取消绑定回调
    confirmBind() {
      this.loadData(1)
    },
  },
}
</script>
<style scoped lang="scss">
.noBorder {
  padding-bottom: 15px;
  & .flex {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    padding: 0 16px;
    padding-top: 6px;
    span {
      // color: red;
    }
  }
  .mr8:last-child {
    margin-right: -2px;
  }
}
</style>
