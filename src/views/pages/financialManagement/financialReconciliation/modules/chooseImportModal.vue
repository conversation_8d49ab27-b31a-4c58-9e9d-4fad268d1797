<!-- 选择导入商品明细/票折类型弹窗 -->
<template>
    <a-modal
      :title="title"
      :visible="visible"
      :width="500"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
      @close="handleCancel"
      :maskClosable="false"
      cancelText="关闭"
      :destroyOnClose="true"
    >
      <div
        :style="{
          width: '100%',
          padding: '10px 16px',
          background: '#fff',
        }"
      >
        <a-spin :spinning="confirmLoading">
          <a-form-model ref="form" :rules="rules" :model="model">
            <a-form-model-item
              label=""
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              prop="importType"
            >
              <a-radio-group name="radioGroup" v-model="model.importType">
                <a-radio :value="1"> 商品明细导入 </a-radio>
                <a-radio :value="2" style="margin-left: 50px;"> 票折导入 </a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-form-model>
        </a-spin>
      </div>
      <a-row :style="{ textAlign: 'right' }" slot="footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button
          :style="{ marginRight: '8px' }"
          @click="handleOk"
          type="primary"
          >确定</a-button
        >
      </a-row>
    </a-modal>
  </template>
  
  <script>
  import { EditMixin } from '@/mixins/EditMixin'
  export default {
    name: 'ChooseImportModal',
    components: {},
    mixins: [EditMixin],
    data() {
      return {
        title: '选择导入类型',
        visible: false,
        model: {
          importType: 1
        },
        confirmLoading: false,
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        }
      }
    },
    computed: {
      rules() {
        return {
        }
      },
    },
    mounted() {},
    created() {},
    methods: {
      show(record, PurchaseInquiryOrderId) {
        this.visible = true
      },
      // 确定
      handleOk() {
        this.$emit('confirmType', this.model)
        this.close()
      },
      // 关闭
      handleCancel() {
        this.close()
      },
      close() {
        this.$emit('close')
        this.visible = false
        this.model = { importType: 1 }
      }
    },
  }
  </script>
  
  <style scoped lang="scss">
  </style>
  