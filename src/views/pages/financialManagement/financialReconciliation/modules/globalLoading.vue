<!-- 对账中弹窗 -->
<template>
  <a-modal
    :getContainer="getContainer"
    centered
    :title="title"
    :visible="visible"
    :width="300"
    :class="{ hideenBox: canDo ? false : true }"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :closable="false"
  >
    <div class="loadBox">
      <a-icon type="loading" />
      <div class="text">{{ text }}</div>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <div v-if="canDo">
        <a-button @click="handleCancel">关闭</a-button>
        <a-button @click="handleOk" type="primary">再次查询</a-button>
      </div>
    </a-row>
  </a-modal>
</template>

<script>
export default {
  name: 'GlobalLoading',
  components: {},
  props: {
    getContainer: {
      type: Function,
      default: () => document.boy,
    },
    title: {
      // 标题
      type: String,
      default: '提示',
    },
    text: {
      // 提示文案
      type: String,
      default: '提示',
    },
  },
  data() {
    return {
      canDo: false, //是否可以操作
      visible: false,
    }
  },
  mounted() {},
  created() {},
  methods: {
    show() {
      this.canDo = false
      this.visible = true
    },
    // 显示底部操作按钮
    showBtn() {
      this.canDo = true
    },
    handleOk() {
      this.$emit('continueSearch')
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>

<style scoped lang="scss">
.loadBox {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .text {
    margin-top: 20px;
    color: red;
  }
}
.hideenBox {
  ::v-deep .ant-modal-footer {
    padding: 0 16px !important;
    border-top: none;
  }
}
::v-deep .ant-modal-mask{
  z-index: 1001 !important;
}
::v-deep .ant-modal-wrap {
  z-index: 1002 !important;
}
</style>
