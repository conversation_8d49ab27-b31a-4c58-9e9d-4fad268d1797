<template>
  <!-- 插入跨月商品明细 -->
  <drag-modal :title="'插入跨月商品明细'" :width="'80vw'" :visible="visible" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <!--插入跨月商品明细弹窗 -->
    <div :style="{
        width: '100%',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <div>
          <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
          <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
          <a-row :gutter="10">
            <a-col :md="24">
              <!-- 数据列表 -->
              <!-- 列表 -->
              <TableNewView ref="tableView" :table="table" :columns="columns" :tabDataList="tabDataList" :dataList="dataSource" @operate="operate">
              </TableNewView>
            </a-col>
          </a-row>
        </div>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button :style="{ marginRight: '8px' }" @click="handleOk" type="primary">确认</a-button>
    </a-row>
  </drag-modal>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, postAction, putAction } from '@/api/manage'
import DragModal from '@/components/DragModal'
import moment from 'moment'
export default {
  name: 'InsertCrossMonthDetails',
  inject: ['ReconciliationType', 'supplierReconciliationType', 'customerReconciliationType', 'reconciliationId'],
  mixins: [ListMixin],
  components: { DragModal },
  data() {
    return {
      visible: true,
      confirmLoading: false,
      table: {
        rowKey: 'DocumentDetailId',
        selectType: 'checkbox',
        isSelectCurPage: true,
      },
      tabDataList: [],
      dataSource: [],
      // isInitData: false,
      httpHead: 'P36012',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/PurchaseSalesStatement/GetCrossMonthGoodsList', // 当前列表数据接口
        add: '/{v}/PurchaseSalesStatement/SaveCrossMonthGoods',
      },
    }
  },
  computed: {
    columns() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      let basicColumn = [
        {
          title: '单据编号',
          width: 150,
          align: 'center',
          dataIndex: 'DocumentNo',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据责任人',
          width: 150,
          align: 'center',
          dataIndex: 'OwnerByName',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据类型',
          width: 150,
          align: 'center',
          dataIndex: 'BusinessEventTypeStr',
        },
        {
          title: '订单编号',
          width: 150,
          align: 'center',
          dataIndex: 'MainBusinessNo',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'OccurrenceDate',
          align: 'center',
          width: 160,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          width: 150,
          align: 'center',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '品种责任人',
          dataIndex: 'GoodsOwnerBy',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购人',
          dataIndex: 'OwnerByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '数量',
          dataIndex: 'Quantity',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '含税价',
          dataIndex: 'TaxIncludedUnitPrice',
          width: 150,
        },
        {
          title: '含税金额',
          dataIndex: 'TaxIncludedAmount',
          width: 150,
        },
      ]
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return basicColumn
        case customerReconciliationType:
          basicColumn = basicColumn.filter((f) => f.title !== '采购人')
          return basicColumn
      }
    },
  },
  watch: {
    // 一些属性无法设置成计算属性，需要通过监听ReconciliationType变化而设置在data里的值
    ReconciliationType: {
      handler(val) {
        const supplierReconciliationType = this.supplierReconciliationType
        const customerReconciliationType = this.customerReconciliationType
        let searchItems = [
          {
            name: '单据编号', //输入框名称 必填
            type: this.$SEnum.INPUT, //类型 必填
            key: 'DocumentNo', //搜索key 必填
            placeholder: '请输入',
          },
          {
            name: '订单编号', //输入框名称 必填
            type: this.$SEnum.INPUT, //类型 必填
            key: 'OrderNo', //搜索key 必填
            placeholder: '请输入',
          },
          {
            name: '单据类型',
            type: this.$SEnum.SELECT,
            key: 'BusinessEventType',
            items: [
              {
                label: '采购入库',
                value: 2,
              },
              {
                label: '采购退货',
                value: 4,
              },
              {
                label: '采购调价',
                value: 8,
              },
            ],
          },
          {
            name: '商品', //输入框名称 必填
            type: this.$SEnum.INPUT, //类型 必填
            key: 'GoodsKey', //搜索key 必填
            placeholder: '请输入商品名称/编号',
          },
          {
            name: '生产厂家', //输入框名称 必填
            type: this.$SEnum.INPUT, //类型 必填
            key: 'ManufacturerName', //搜索key 必填
            placeholder: '请输入',
          },
          {
            name: '发生日期',
            type: this.$SEnum.DATE,
            rangeDate: this.rangeDate,
            key: 'OccurrenceDateBegin',
            keyEnd: 'OccurrenceDateEnd',
            showTime: true,
            placeholder: ['开始日期', '结束日期'],
          },
        ]
        switch (val) {
          case customerReconciliationType:
            const index = searchItems.findIndex((f) => f.name === '单据类型')
            const item = searchItems[index]
            searchItems.splice(index, 1, {
              ...item,
              items: [
                {
                  label: '商销出库 ',
                  value: 20,
                },
                {
                  label: '商销售后',
                  value: 22,
                },
                {
                  label: '商销调价',
                  value: 24,
                },
              ],
            })
            break
          default:
            break
        }
        this.searchItems = searchItems
      },
      immediate: true,
    },
  },
  created() {
    this.setThisMonthTime(this.searchItems, '发生日期', 'OccurrenceDateBegin', 'OccurrenceDateEnd')
  },
  mounted() { },
  activated() { },

  methods: {
    moment,
    loadBefore() {
      this.queryParam['PurchaseSalesStatementId'] = this.reconciliationId
      // this.setThisMonthTime(this.searchItems, '发生日期', 'OccurrenceDateBegin', 'OccurrenceDateEnd')
      // var params = this.getQueryParams()
      // if (!params.OccurrenceDateBegin || !params.OccurrenceDateEnd) {
      //   let OccurrenceDateBegin = '2022-01-01'
      //   let OccurrenceDateEnd = this.queryParam.OccurrenceDateEnd
      //   this.filters = {
      //     // 当时间为空的时候要默认赋值时间查询
      //     OccurrenceDateBegin,
      //     OccurrenceDateEnd,
      //   }
      // }
    },
    loadAfter() {
      // this.filters = {} // 查询完清掉默认赋值时间
    },
    searchQuery(param) {
      var params = this.getQueryParams()
      if (!params.OccurrenceDateBegin || !params.OccurrenceDateEnd) {
        let OccurrenceDateBegin = '2022-01-01'
        let OccurrenceDateEnd = this.queryParam.OccurrenceDateEnd
        this.$refs.searchView.queryParam.OccurrenceDateBegin = OccurrenceDateBegin
        this.$refs.searchView.queryParam.OccurrenceDateEnd = OccurrenceDateEnd
      }
      this.loadData()
    },
    // 操作
    operate(record, key, index) {
      console.log(record, key, index)
      if (key == 'down') {
      }
    },
    show() {
      this.visible = true
      // 设置搜索时间默认值 当月1号到今天
      // this.selectionRows = []
      // this.selectedRowKeys = []
      // this.setThisMonthTime(this.searchItems,'发生日期','OccurrenceDateBegin','OccurrenceDateEnd')
      // this.loadData(1)
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      // this.visible = false
      this.queryParam = {}
      this.$emit('close')
    },
    handleOk() {
      const that = this
      let selectionRows = this.selectionRows
      if (selectionRows.length < 1) {
        this.$message.warning('请先勾选要操作的数据')
        return false
      }
      let url = that.url.add
      let ItemList = selectionRows
      let formData = {
        PurchaseSalesStatementId: this.reconciliationId,
        ItemList,
      }
      that.confirmLoading = true
      postAction(url, formData, that.httpHead)
        .then((res) => {
          that.confirmLoading = false
          if (res.IsSuccess) {
            this.$message.success('操作成功!')
            this.close()
            this.$emit('confirmInsert')
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
  },
}
</script>
<style scoped lang="scss"></style>
