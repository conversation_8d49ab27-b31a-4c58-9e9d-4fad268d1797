<!-- 新增商品对照 -->
<template>
  <a-modal :title="title" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :gutter="10">
            <a-col :md="24">
              <!-- 往来单位： -->
              <a-form-model-item label="往来单位：" prop="CooperationCompanyId" >
                <SingleChoiceSearchView 
                style="width: 100%" 
                placeholder="请选择" 
                :httpParams="{ PageIndex: 1, PageSize: 100}" 
                httpHead="P36012" 
                :keyWord="'KeyWord'" 
                httpType="POST"
                Url="/{v}/CooperationCompany/GetOutCooperationCompanys" 
                :dataKey="{ name: 'Name', value: 'Id' }" 
                v-model="model.CooperationCompanyId" 
                :name="model.CooperationCompanyName" 
                @change="(val, txt, item) => changeGys(val, txt, item)" />
              </a-form-model-item>
            </a-col>
            <!-- 对方商品信息 -->
            <template>
              <a-col :md="24">
                <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">对方商品信息</div>
              </a-col>
              <a-col :md="24">
                <a-form-model-item label="商品编号：" prop="OtherPartyErpGoodsCode" >
                  <a-input placeholder="请输入" v-model="model.OtherPartyErpGoodsCode" style="width: 100%" :maxLength="50" />
                </a-form-model-item>
                <a-form-model-item label="商品名称：" prop="OtherPartyErpGoodsName" >
                  <a-input placeholder="请输入" v-model="model.OtherPartyErpGoodsName" style="width: 100%" :maxLength="100" />
                </a-form-model-item>
                <a-form-model-item label="规格：" prop="OtherPartyPackingSpecification" >
                  <a-input placeholder="请输入" v-model="model.OtherPartyPackingSpecification" style="width: 100%" :maxLength="50" />
                </a-form-model-item>
                <a-form-model-item label="生产厂家：" prop="OtherPartyBrandManufacturer" >
                  <a-input placeholder="请输入" v-model="model.OtherPartyBrandManufacturer" style="width: 100%" :maxLength="100"/>
                </a-form-model-item>
              </a-col>
            </template>
            <!-- 我方商品信息 -->
            <template>
              <a-col :md="24">
                <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">我方商品信息</div>
              </a-col>
              <a-col :md="24">
                <a-form-model-item label="商品信息：" prop="GoodsSpuId" >
                  <a-input placeholder="请输入" v-model="model.GoodsInfo" 
                  style="width: 80%;margin-right: 10px;" :readOnly="true"/>
                  <a @click="chooseGoods">选择</a>
                </a-form-model-item>
              </a-col>
            </template>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">确定</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
      <!-- 表格选择商品数据弹窗 -->
      <TableSelectDataModal
        ref="TableSelectDataModal"
        :type="'radio'"
        hideRecordKeyMsg
        :selectTableData="selectTableData"
        @chooseData="chooseData"
        isToSelectClose
      ></TableSelectDataModal>
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "TradeUnitCommodityComparisonAddModal",
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      title: '新增商品对照',
      visible: false,
      isEdit: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      rangeDate:[],
      storedataSource:[],
      // 选择商品 配置
      selectTableData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          {
            name: '商品名称',
            type: 'input',
            value: '',
            key: 'KeyWord',
            defaultVal: '',
            placeholder: '请输入商品名称/编号',
          },
          {
            name: '生产厂家',
            type: 'input',
            value: '',
            key: 'ManufacturerName',
            defaultVal: '',
            placeholder: '请输入厂家名称',
          },
        ],
        title: '选择商品',
        name: '商品',
        recordKey: 'GoodsSpuId',
        httpHead: 'P36012',
        isInitData: false,
        url: {
          list: '/{v}/CompanyGoodsMapping/GetPurchaseSaleGoodsList',
          listType: 'GET',
        },
        columns: [
          {
            title: '商品名称',
            dataIndex: 'ErpGoodsName',
            width: 180,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '商品编号',
            dataIndex: 'ErpGoodsCode',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '规格',
            dataIndex: 'PackingSpecification',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '生产厂商',
            dataIndex: 'BrandManufacturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '采购人员',
            dataIndex: 'GoodsPurchaser',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
            showKey:'IsBound'
          },
        ],
      },
      model: {
        CooperationCompanyId:null,
        CooperationCompanyName:'',
        GoodsSpuId:null,
        ErpGoodsId:null,
        ErpGoodsCode:'',
        ErpGoodsName:'',
        PinYinCode:'',
        PackingSpecification:'',
        BrandManufacturerId:null,
        BrandManufacturer:'',
        OtherPartyErpGoodsCode:'',
        OtherPartyErpGoodsName:'',
        OtherPartyPackingSpecification:'',
        OtherPartyBrandManufacturer:'',
        GoodsInfo:'',
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      httpHead: 'P36012',
      url: {
        add: "/{v}/CompanyGoodsMapping/Save",
        getDetails: "/{v}/CompanyGoodsMapping/GetDetails",
      },
    };
  },
  computed: {
    rules() {
      return {
        CooperationCompanyId: [{ required: true,message:'请选择', trigger: 'change' }],
        OtherPartyErpGoodsCode: [{ required: false,message:'请输入', trigger: 'change' }],
        OtherPartyErpGoodsName: [{ required: true,message:'请输入', trigger: 'change' }],
        OtherPartyPackingSpecification: [{ required: true,message:'请输入', trigger: 'change' }],
        OtherPartyBrandManufacturer: [{ required: true,message:'请输入', trigger: 'change' }],
        GoodsSpuId: [{ required: true,message:'请选择', trigger: 'change' }],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add() {
      this.title = '新增商品对照'
      this.model = {}
      this.isEdit = false;
      this.visible = true;
    },
    edit(record) {
      this.title = '编辑商品对照'
      this.isEdit = true;
      this.visible = true;
      this.getDetails(record.Id)
    },
    // 获取详情
    getDetails(id){
      this.confirmLoading = true
      getAction(this.url.getDetails, { id:id},this.httpHead).then(res=>{
        if(res.IsSuccess){
          this.model = Object.assign({}, res.Data);
          this.$set(this.model, 'GoodsInfo', `${this.model.ErpGoodsCode}/${this.model.ErpGoodsName}/${this.model.PackingSpecification}/${this.model.BrandManufacturer}`)
        }else{
          this.$message.error(res.Msg)
        }
      }).finally(()=>{
        this.confirmLoading = false
      })
    },
    // 选择 往来单位名称
    changeGys(val, txt, item) {
      this.model.CooperationCompanyName = txt
      this.model.CooperationCompanyId = val
    },
    chooseGoods() {
      if(!this.model.CooperationCompanyId){
        this.$message.error('请先选择往来单位名称')
        return
      }
      if(!this.model.OtherPartyErpGoodsName){
        this.$message.error('请先输入对方商品名称')
        return
      }
      if(!this.model.OtherPartyPackingSpecification){
        this.$message.error('请先输入对方商品规格')
        return
      }
      if(!this.model.OtherPartyBrandManufacturer){
        this.$message.error('请先输入对方商品生产厂家')
        return
      }
      let queryParam = {
        CooperationCompanyId: this.model.CooperationCompanyId,
        OtherPartyErpGoodsCode: this.model.OtherPartyErpGoodsCode,
        OtherPartyErpGoodsName: this.model.OtherPartyErpGoodsName,
        OtherPartyPackingSpecification: this.model.OtherPartyPackingSpecification,
        OtherPartyBrandManufacturer: this.model.OtherPartyBrandManufacturer,
        // IsAll: true
      }
      this.$refs.TableSelectDataModal.show(this.storedataSource, queryParam)
    },
    // 选择商品选择数据回调
    chooseData(data, type) {
      if (data && data.length > 0) {
        if (type == 'radio') {
          this.storedataSource = data
        } else {
          this.storedataSource = this.storedataSource.concat(data)
        }
        // console.log('选择商品选择数据回调  ',this.storedataSource)
        this.$set(this.model, 'GoodsSpuId', data[0].GoodsSpuId)
        this.$set(this.model, 'ErpGoodsId', data[0].ErpGoodsId)
        this.$set(this.model, 'ErpGoodsCode', data[0].ErpGoodsCode)
        this.$set(this.model, 'ErpGoodsName', data[0].ErpGoodsName)
        this.$set(this.model, 'PinYinCode', data[0].PinYinCode)
        this.$set(this.model, 'PackingSpecification', data[0].PackingSpecification)
        this.$set(this.model, 'BrandManufacturerId', data[0].BrandManufacturerId)
        this.$set(this.model, 'BrandManufacturer', data[0].BrandManufacturer)
        this.$set(this.model, 'GoodsInfo', `${data[0].ErpGoodsCode}/${data[0].ErpGoodsName}/${data[0].PackingSpecification}/${data[0].BrandManufacturer}`)
        this.$refs.form.validateField('GoodsSpuId')
      }
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          let formData = {}
          let url = ''
          url = this.url.add
          formData = {
            CooperationCompanyId: this.model.CooperationCompanyId,
            CooperationCompanyName: this.model.CooperationCompanyName,
            GoodsSpuId: this.model.GoodsSpuId,
            // ErpGoodsId: this.model.ErpGoodsId,
            // ErpGoodsCode: this.model.ErpGoodsCode,
            // ErpGoodsName: this.model.ErpGoodsName,
            // PinYinCode: this.model.PinYinCode,
            // PackingSpecification: this.model.PackingSpecification,
            // BrandManufacturerId: this.model.BrandManufacturerId,
            // BrandManufacturer: this.model.BrandManufacturer,
            OtherPartyErpGoodsCode: this.model.OtherPartyErpGoodsCode,
            OtherPartyErpGoodsName: this.model.OtherPartyErpGoodsName,
            OtherPartyPackingSpecification: this.model.OtherPartyPackingSpecification,
            OtherPartyBrandManufacturer: this.model.OtherPartyBrandManufacturer,
          }
          if(this.isEdit){
            formData.Id = this.model.Id
          }
          postAction(url, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
      this.storedataSource = []
    },
  },
};
</script>
