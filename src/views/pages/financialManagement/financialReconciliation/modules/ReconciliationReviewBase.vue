<template>
  <!--对账审核基础组件 -->
  <a-spin :spinning="loading">
    <a-card :bodyStyle="{ padding: '0' }" v-if="model">
      <div :style="{ padding: '16px 16px 0 16px' }">
        <a-descriptions :column="4">
          <a-descriptions-item>
            <div class="flc hcenter">
              <a-icon
                :type="getAuditIcon(model.AuditStatus)"
                theme="twoTone"
                :two-tone-color="getAuditColor(model.AuditStatus)"
                style="font-size: 32px"
              />
              <span :style="{ color: getAuditColor(model.AuditStatus) }">{{ model.AuditStatusStr || '--' }}</span>
            </div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">对账金额</span>
            <div>¥{{ model.TotalAmount }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">对账单号</span>
            <div>{{ model.StatementNo || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">创建时间</span>
            <div>{{ model.CreateTime || '--' }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </div>
      <div class="myDivider"></div>
      <a-tabs v-model="activeTabKey">
        <a-tab-pane :key="1" tab="基本信息">
          <ReconciliationDetails
            @getBasicInfo="getBasicInfo"
            :ReconciliationType="ReconciliationType"
            ifAudit
            isExamine
            v-if="activeTabKey == 1"
          />
        </a-tab-pane>
        <a-tab-pane :key="2" tab="审核信息">
          <div style="margin: 5px 15px">
            <SupAuditInformation :bussinessNo="model.Id" v-if="activeTabKey == 2" />
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
    <!-- 底部区域 -->
    <a-affix :offset-bottom="0" v-if="isEdit" style="float: right; width: 100%; text-align: right">
      <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
        <YYLButton
          :menuId="reviewRejectedId"
          v-if="IsShowBtn"
          text="审核驳回"
          type="danger"
          :loading="confirnLoading"
          @click="onRejectClick"
        />
        <YYLButton
          :menuId="reviewPassId"
          v-if="IsShowBtn"
          text="审核通过"
          type="primary"
          :loading="confirnLoading"
          @click="onPassClick"
        />
        <span style="margin-right: 15px" v-if="!IsShowBtn">无审核权限</span>
        <a-button
          @click="
            goBack(
              true,
              ReconciliationType === supplierReconciliationType
                ? '/pages/financialManagement/financialReconciliation/supplierReconciliationAudit'
                : '/pages/financialManagement/financialReconciliation/customerReconciliationAudit'
            )
          "
          >返回</a-button
        >
      </a-card>
    </a-affix>
    <!-- 审核弹框 -->
    <AuditRemarkModal ref="auditRemarkModal" @ok="onAuditModalOk" />
  </a-spin>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import { ACCESS_TOKEN, USER_ID, USER_NAME } from '@/store/mutation-types'
import Vue from 'vue'
export default {
  name: 'ReconciliationReviewBase',
  mixins: [ListMixin],
  data() {
    return {
      loading: false,
      supplierReconciliationType: 1, // 供应商对账所属类型值
      customerReconciliationType: 2, // 客户对账所属类型值
      confirnLoading: false,
      model: {},
      activeTabKey: 1,
      IsHasSuperior: false,
      IsShowBtn: false,
      url: {
        getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
        check: '/{v}/PurchaseOrder/VerifyPurchaseOrderSubmit',
      },
      httpHead: 'P36009',
    }
  },
  props: {
    ReconciliationType: {
      //对账类型
      type: Number,
      default: 1,
    },
    isEdit: {
      //是否可编辑
      type: Boolean,
      default: false,
    },
  },
  computed: {
    // 审核驳回按钮id
    reviewRejectedId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return '687b44bd-676a-4894-8def-3868417c2f5b'
        case customerReconciliationType:
          return 'ca8beb18-9041-4b55-8056-5224bac29946'
      }
    },
    // 审核通过按钮id
    reviewPassId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return '86247f14-9a1a-4f0d-8f3e-993afd536ff8'
        case customerReconciliationType:
          return 'd4323fec-9b52-453e-b645-4c1d6deda415'
      }
    },
  },
  created() {},
  mounted() {
    if (this.$route.query) {
      this.orderId = this.$route.query.id || ''
      // this.setTitle('财务对账审核' + (this.opType == 1 ? '详情' : ''))
    }
  },
  activated() {},

  methods: {
    getInfo() {
      console.log('获取页面信息')
    },
    getAuthInfo() {
      this.getApprovalResults(this.model.Id)
    },
    // 获取审核权限和信息
    getApprovalResults(bussinessNo) {
      if (!bussinessNo) return
      getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.IsHasSuperior = res.Data['IsHasSuperior'] //是否有上级 true 有上级 false 没有上级
              this.ApprovalWorkFlowInstanceId = res.Data['ApprovalWorkFlowInstanceId']
              let userId = Vue.ls.get(USER_ID)
              if ((res.Data['ApprovalUserIds'] || []).length && this.isEdit) {
                this.IsShowBtn = res.Data['ApprovalUserIds'].some((v) => v == userId)
              }
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {})
    },
    // 通过
    onPassClick() {
      this.showAuditModel(1)
    },
    // 驳回
    onRejectClick() {
      this.showAuditModel(2)
    },
    // 显示审核弹窗 type 1通过 2驳回
    showAuditModel(type) {
      let that = this
      if (this.$refs.auditRemarkModal) {
        let param = {
          ApprovalWorkFlowInstanceId: that.ApprovalWorkFlowInstanceId,
          IsHasSuperior: that.IsHasSuperior,
        }
        this.$refs.auditRemarkModal.show(type, param)
      }
    },
    // 审核弹窗回调
    onAuditModalOk(formData) {
      if (formData) {
        this.$refs.auditRemarkModal.postAuditInstance(formData, (bool) => {
          if (bool) {
            this.auditOK()
          }
        })
      } else {
        this.auditOK()
      }
    },
    auditOK(isBool) {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      this.goBack(
        true,
        ReconciliationType === supplierReconciliationType
          ? '/pages/financialManagement/financialReconciliation/supplierReconciliationAudit'
          : '/pages/financialManagement/financialReconciliation/customerReconciliationAudit'
      )
    },
    // 获取基本信息回调
    getBasicInfo(info) {
      this.model = info
      if (this.isEdit) {
        this.getAuthInfo()
      } else {
        this.getInfo()
      }
    },
  },
}
</script>
<style scoped lang="scss">

.myDivider {
  border-top: 1px solid #e8e8e8;
}
</style>
