<template>
  <!-- 票折对账记录 -->
  <div>
    <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
    <a-spin :spinning="confirmLoading">
      <div class="noBorder">
        <div class="flex">
          <div class="warp">
            <span class="red"
              >票折合计金额：<span v-if="priceInfo.TotalThirdFinalAmount < 0">-</span>￥{{
                Math.abs(priceInfo.TotalThirdFinalAmount)
              }}</span
            >；供应商含税总金额：<span v-if="priceInfo.TotalThirdInvoiceDiscountAmount < 0">-</span>￥{{
              Math.abs(priceInfo.TotalThirdInvoiceDiscountAmount)
            }}; 供应商修正金额：<span v-if="priceInfo.TotalThirdPriceAdjustmentAmount < 0">-</span>￥{{
              Math.abs(priceInfo.TotalThirdPriceAdjustmentAmount)
            }}；采销系统合计金额：<span v-if="priceInfo.TotalScmInvoiceDiscountAmount < 0">-</span>￥{{
              Math.abs(priceInfo.TotalScmInvoiceDiscountAmount)
            }}
          </div>
          <div v-if="!ifAudit">
            <YYLButton
              :menuId="'606bc567-16d7-4a5d-8628-137826e5c1e9'"
              text="插入跨月票折"
              type="primary"
              @click="insertTicket"
            />
            <YYLButton
              :menuId="'1fff2213-d82a-4099-b25a-032ebffea07f'"
              text="批量修正价格"
              @click="batchPriceCorrection"
              type="primary"
            />
            <YYLButton
              :menuId="'d1fdc53f-deea-4f0c-9fba-ca239e958a39'"
              text="批量取消价格修正"
              @click="cancelBatchPriceCorrection"
              type="primary"
            />
          </div>
        </div>
      </div>
      <MultiTableScrolling
        ref="MultiTableScrolling"
        :columnsList="columnsList"
        :tabList="tabList"
        @operate="operate"
        :dataSourceList="dataSourceList"
      />
    </a-spin>
    <!-- 插入跨月票折明细弹窗 -->
    <InsertCrossMonthlyTicketDetails
      v-if="showInsert"
      @close="showInsert = false"
      @confirmInsert="confirmInsert"
      ref="InsertCrossMonthlyTicketDetails"
    />
    <!-- 票折价格修正弹窗 -->
    <TicketPriceCorrectionModal
      ref="TicketPriceCorrectionModal"
      @batchPriceCorrectionConfirm="batchPriceCorrectionConfirm"
    />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, postAction, deleteAction } from '@/api/manage'
export default {
  name: 'TicketDiscountReconciliationRecord',
  inject: ['ifAudit', 'reconciliationId', 'isExamine'],
  mixins: [ListMixin, EditMixin],
  components: {
    JEllipsis,
  },
  props: {
    thirdPartyName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      confirmLoading: false,
      searchItems: [
        {
          name: '厂家',
          type: this.$SEnum.INPUT,
          key: 'Manufacturer',
          disabled: true,
          placeholder: '请输入生产厂家',
        },
        {
          name: '金额调整',
          type: this.$SEnum.SELECT,
          key: 'IsAdjusted',
          items: [
            {
              label: '有调整',
              value: true,
            },
            {
              label: '无调整',
              value: false,
            },
          ],
        },
        {
          name: '单据日期',
          type: this.$SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'ThirdDocumentDateBegin',
          keyEnd: 'ThirdDocumentDateEnd',
          showTime: true,
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '发生日期',
          type: this.$SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'ScmDocumentDateBegin',
          keyEnd: 'ScmDocumentDateEnd',
          showTime: true,
          placeholder: ['开始日期', '结束日期'],
        },
      ],
      tabList: [
        {
          rowKey: 'Id',
          rowSelection: this.ifAudit ? null : {
            type: 'checkbox',
          },
        },
        {
          rowKey: 'Id',
          rowSelection: null,
        },
      ],
      dataSourceList: [],
      httpHead: 'P36012',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/PurchaseSalesStatement/GetInvoiceDiscountStatementList', // 当前列表数据接口
        cancelUpdate: '/{v}/PurchaseSalesStatement/CancelThridInvoiceDiscountBillAmount',
        remove1: '/{v}/PurchaseSalesStatement/DelThridInvoiceDiscountBills',
        remove2: '/{v}/PurchaseSalesStatement/DelScmInvoiceDiscountBills',
      },
      urlExamine: {
        list: '/{v}/PurchaseSalesStatementAudit/GetInvoiceDiscountStatementList', // 当前列表数据接口
      },
      priceInfo: {}, // 价格统计信息
      showInsert: false, // 是否显示插入跨越票折弹窗
    }
  },
  computed: {
    columnsList() {
      let ifAudit = this.ifAudit
      let basicArry = [
        [
          {
            title: this.thirdPartyName,
            children: [
              {
                title: '厂家',
                dataIndex: 'Manufacturer',
                width: 300,
                ellipsis: true,
                align: 'center',
                // scopedSlots: { customRender: 'component' },
                customRender: (text, record, index) => {
                  const obj = {
                    children: <j-ellipsis value={text == 0 || text ? String(text) : ''} />,
                    attrs: {},
                  }
                  obj.attrs.rowSpan = record.rowSpan
                  return obj
                },
              },
              {
                title: '单据日期',
                dataIndex: 'DocumentDate',
                width: 200,
                align: 'center',
                scopedSlots: { customRender: 'date' },
              },
              {
                title: '含税金额',
                dataIndex: 'DiscountAmount',
                align: 'center',
                width: 200,
                scopedSlots: { customRender: 'price' },
              },
              {
                title: '修正后金额',
                dataIndex: 'FinalAmount',
                align: 'center',
                width: 200,
                scopedSlots: { customRender: 'priceUpdate' },
              },
              {
                title: '操作',
                dataIndex: 'action1',
                align: 'center',
                width: 200,
                // fixed: 'right',
                actionBtns: [
                  {
                    name: '修正价格',
                    icon: '',
                    id: 'e55b0b49-0956-4370-ad88-f461afc5c892',
                    isShow: (record) => {
                      return !record.IsAdjusted
                    },
                  },
                  {
                    name: '取消价格修正',
                    icon: '',
                    id: 'e76c923a-d16b-46bf-8560-09f007252208',
                    isShow: (record) => {
                      return record.IsAdjusted
                    },
                  },
                  {
                    name: '移除',
                    icon: '',
                    id: '82dda31d-a492-471d-8ba2-5b360f47bc9c',
                    isShow: (record) => {
                      return true
                    },
                  },
                ],
                scopedSlots: { customRender: 'action' },
              },
            ],
          },
        ],
        [
          {
            title: '采销系统',
            children: [
              {
                title: '厂家',
                dataIndex: 'Manufacturer',
                align: 'center',
                width: 300,
                ellipsis: true,
                customRender: (text, record, index) => {
                  const obj = {
                    children: <j-ellipsis value={text == 0 || text ? String(text) : ''} />,
                    attrs: {},
                  }
                  obj.attrs.rowSpan = record.rowSpan
                  return obj
                },
              },
              {
                title: '单据编号',
                dataIndex: 'DocumentNo',
                align: 'center',
                width: 200,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '单据责任人',
                dataIndex: 'OwnerByName',
                align: 'center',
                width: 200,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '发生日期',
                dataIndex: 'DocumentDate',
                align: 'center',
                width: 200,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '含税金额',
                dataIndex: 'DiscountAmount',
                align: 'center',
                width: 200,
                scopedSlots: { customRender: 'price' },
              },
              {
                title: '操作',
                dataIndex: 'action2',
                align: 'center',
                width: 200,
                // fixed: 'right',
                actionBtns: [
                  {
                    name: '移除',
                    icon: '',
                    id: '82dda31d-a492-471d-8ba2-5b360f47bc9c',
                    isShow: (record) => {
                      return true
                    },
                  },
                  {
                    name: '详情',
                    icon: '',
                    id: this.detailMenuId,
                    isShow: (record) => {
                      return true
                    },
                  },
                ],
                scopedSlots: { customRender: 'action' },
              },
            ],
          },
        ],
      ]

      if (ifAudit) {
        basicArry.map((item, index) => {
          item[0].children = item[0].children.filter((f) => f.title !== '操作')
          if (index === 1) {
            item[0].children.push({
              title: '操作',
              dataIndex: 'action2',
              align: 'center',
              width: 200,
              // fixed: 'right',
              actionBtns: [
                {
                  name: '详情',
                  icon: '',
                  id: this.detailMenuId,
                  isShow: (record) => {
                    return true
                  },
                },
              ],
              scopedSlots: { customRender: 'action' },
            })
          }
        })
      }
      return basicArry
    },
  },
  created() {
    if (this.isExamine) {
      // 审核页面进入需要替换对应的接口
      this.url = Object.assign({}, this.url, this.urlExamine)
    }
  },
  mounted() {},
  activated() {},

  methods: {
    loadBefore() {
      this.confirmLoading = true
      const isExamine = this.isExamine
      this.queryParam[isExamine ? 'purchaseSalesStatementRecordId' : 'purchaseSalesStatementId'] = this.reconciliationId
      this.dataSourceList = []
      this.priceInfo = {}
    },
    loadAfter(Data) {
      this.confirmLoading = false
      this.dataSourceList.push(Data.ThirdInvoiceDiscountBillList)
      this.dataSourceList.push(Data.ScmInvoiceDiscountBillList)
      // 设置表格合并
      this.dataSourceList[0] = this.setTableMerge(this.dataSourceList[0], 'Manufacturer', true)
      this.dataSourceList[1] = this.setTableMerge(this.dataSourceList[1], 'Manufacturer', true)
      const {
        TotalThirdInvoiceDiscountAmount,
        TotalThirdPriceAdjustmentAmount,
        TotalThirdFinalAmount,
        TotalScmInvoiceDiscountAmount,
      } = Data
      this.priceInfo = {
        TotalThirdInvoiceDiscountAmount,
        TotalThirdPriceAdjustmentAmount,
        TotalThirdFinalAmount,
        TotalScmInvoiceDiscountAmount,
      }
      let lg =  this.dataSourceList[0].length
      this.$emit('updateCount', 6, lg)
    },
    // 插入跨月票折按钮点击事件
    insertTicket() {
      this.showInsert = true
    },
    // 操作
    operate(record, key, index, sub) {
      console.log(record, key, index, sub)
      if (key === '修正价格') {
        this.priceCorrection([record])
      } else if (key === '取消价格修正') {
        this.cancelPriceCorrection([record.Id], [record])
      } else if (key === '移除') {
        this.remove([record.Id], sub + 1)
      } else if (key === '详情') {
        this.goPage('/pages/purchasingManagement/PurchaTicketPassbookDetail', { id: record.InvoiceDiscountId, type: 1 })
      }
    },
    // 批量修正价格按钮点击事件
    batchPriceCorrection() {
      const selectedRows = this.$refs.MultiTableScrolling.selectedRows
      this.priceCorrection(selectedRows)
    },
    priceCorrection(list) {
      if (list.length < 1) {
        this.$message.warning('请先勾选需要操作的数据')
        return false
      }
      let forbiddenList = list.filter((f) => f.IsAdjusted) || []
      if (forbiddenList.length > 0) {
        this.$message.warning('请先取消已修正价格数据的勾选')
        return false
      }
      this.$refs.TicketPriceCorrectionModal.show(list)
    },
    // 批量修正价格成功回调
    batchPriceCorrectionConfirm() {
      this.loadData(1)
    },
    // 取消批量修正价格按钮点击事件
    cancelBatchPriceCorrection() {
      const selectedRowKeys = this.$refs.MultiTableScrolling.selectedRowKeys
      const selectedRows = this.$refs.MultiTableScrolling.selectedRows
      this.cancelPriceCorrection(selectedRowKeys, selectedRows)
    },
    cancelPriceCorrection(ids, rows) {
      let that = this
      if (ids.length < 1) {
        this.$message.warning('请先勾选需要操作的数据')
        return false
      }
      let forbiddenList = rows.filter((f) => !f.IsAdjusted) || []
      if (forbiddenList.length > 0) {
        this.$message.warning('请先取消未修正价格数据的勾选')
        return false
      }
      this.$confirm({
        title: '取消价格修正',
        content: '您确定要取消价格修正么?',
        onOk() {
          let url = that.url.cancelUpdate
          let formData = ids
          postAction(url, formData, that.httpHead)
            .then((res) => {
              that.confirmLoading = false
              if (res.IsSuccess) {
                that.$message.success('操作成功!')
                that.loadData(1)
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        },
        onCancel() {},
      })
    },
    // 第三方和采销系统的移除
    remove(ids, type) {
      const that = this
      this.$confirm({
        title: '移除',
        content: '您确定要移除该数据么?',
        onOk() {
          let url = that.url[`remove${type}`]
          let formData = ids
          deleteAction(url, formData, that.httpHead)
            .then((res) => {
              if (res.IsSuccess) {
                that.$message.success('操作成功!')
                that.loadData(1)
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {})
        },
        onCancel() {},
      })
    },
    // 确认插入
    confirmInsert() {
      this.loadData(1)
    },
  },
}
</script>
<style scoped lang="scss">
.noBorder {
  padding-bottom: 16px;
  & .flex {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    padding: 0 16px;
    padding-top: 6px;
    .red {
      color: red;
    }
    .warp{
      flex: 1;
      flex-shrink: 1;
    }
  }
  .mr8:last-child {
    margin-right: -2px;
  }
}
</style>
