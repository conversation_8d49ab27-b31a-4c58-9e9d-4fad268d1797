<template>
  <!-- 多表格滚动组件 -->
  <div class="MultiTableScrollingBox">
    <div v-for="(item, sub) in columnsList" :key="sub">
      <a-table
        :bordered="true"
        :columns="item"
        :dataSource="dataSourceList[sub]"
        :pagination="false"
        :rowSelection="
          tabList[sub].rowSelection
            ? {
                selectedRowKeys: selectedRowKeys,
                onChange: onSelectChange,
                onSelect: onSelect,
                onSelectAll: onSelectAll,
                type: tabList[sub].rowSelection.type || 'checkbox',
              }
            : null
        "
        :rowKey="tabList[sub].rowKey ? tabList[sub].rowKey : (record, index) => index"
      >
        <!-- 日期 -->
        <span slot="date" slot-scope="text, record, index, column">
          <j-ellipsis :value="text ? moment(text).format(column.format || 'YYYY-MM-DD') : ' -- '" :length="100" />
        </span>
        <!-- 字符串超长截取省略号显示 -->
        <span slot="component" slot-scope="text, record, index, column">
          <!-- 完整展示内容 -->
          <div v-if="column.complete == true" style="word-wrap: break-word; white-space: normal">
            {{ typeof text == 'number' || text ? '' + text : text == undefined ? '' : '--' }}
          </div>
          <!-- 显示部分内容、超过省略号 -->
          <j-ellipsis
            v-else
            :value="typeof text == 'number' || text ? '' + text : text == undefined ? '' : '--'"
            :length="column.length || 20"
          />
        </span>
        <span
          slot="price"
          slot-scope="text, record, index, column"
          :style="{ color: column.color ? column.color(record) : '' }"
        >
          <j-ellipsis
            :value="
              text == undefined || text == null
                ? '--'
                : (text < 0 ? '-' : '') +
                  '￥' +
                  (Math.round(Number(text < 0 ? String(text).replace('-', '') : text) * 100) / 100).toFixed(2)
            "
          />
        </span>
        <span
          slot="priceUpdate"
          slot-scope="text, record, index, column"
          :style="{ color: column.color ? column.color(record) : '' }"
        >
          <j-ellipsis
            :value="
              !record.IsAdjusted
                ? '--'
                : (text < 0 ? '-' : '') +
                  '￥' +
                  (Math.round(Number(text < 0 ? String(text).replace('-', '') : text) * 100) / 100).toFixed(2)
            "
          />
        </span>
        <!-- 操作 -->
        <span slot="action" slot-scope="text, record, index, column">
          <span v-for="(item, i) in column.actionBtns || []" :key="i">
            <!-- 根据条件隐藏显示 -->
            <span style="margin: 0 5px" v-if="actionBtnIsShow(item, record)">
              <template v-if="item.name === '删除'">
                <a-popconfirm
                  v-if="item.showPopconToast"
                  :title="item.title || `确定删除该项吗?`"
                  :disabled="actionBtnIsDisabled(item, record)"
                  @confirm="operate(record, item.name, index, sub)"
                >
                  <a :disabled="actionBtnIsDisabled(item, record)"> {{ item.name }}</a>
                </a-popconfirm>
                <a v-else @click="operate(record, item.name, index, sub)"> {{ item.name }}</a>
              </template>
              <template v-else>
                <a @click="operate(record, item.name, index, sub)" :disabled="item.disabled">
                  <a-icon v-if="item.icon" :type="item.icon" />
                  {{ item.name }}
                </a>
              </template>
            </span>
          </span>
          <span
            class="tm"
            v-if="
              column.actionBtns &&
              column.actionBtns.filter((btn) => btn.isShow == undefined || btn.isShow(record) == true).length == 0
            "
            >-</span
          >
        </span>
      </a-table>
    </div>
  </div>
</template>
<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import moment from 'moment'
export default {
  name: 'MultiTableScrolling',
  components: { JEllipsis },
  props: {
    columnsList: {
      //渲染的多个表格列字段参数的集合（每一列的宽度必须写出确定的值）
      type: Array,
      default: () => [],
    },
    tabList: {
      // 多个表格的初始设置数据
      type: Array,
      default: () => [],
    },
    dataSourceList: {
      // 多个表格的data数据集合
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      selectedRowKeys: [], //表格选中项的key  当前组件只支持其中一个表格左侧出现勾选，如需所有表格可勾选需自行扩展
      selectedRows: [], //选中项的信息
    }
  },
  watch: {
    dataSourceList: {
      handler(val) {
        this.onClearSelected()
      },
    },
  },
  computed: {
    actionBtnIsDisabled() {
      return function (item, record) {
        if (item.disabled) {
          // 特殊条件显示隐藏
          if (item.disabled(record) === true) {
            return true
          } else {
            return false
          }
        } else {
          // 未设置条件直接显示按钮
          return false
        }
      }
    },
    actionBtnIsShow() {
      return function (item, record) {
        //是否有按钮权限优先判断
        if (!this.checkBtnPermissions(item.id)) {
          return false
        }
        if (item.isShow) {
          // 特殊条件显示隐藏
          if (item.isShow(record) === true) {
            return true
          } else {
            return false
          }
        } else {
          // 未设置条件直接显示按钮
          return true
        }
      }
    },
  },
  created() {},
  mounted() {},
  activated() {},

  methods: {
    moment,
    // 清除勾选项
    onClearSelected() {
      this.selectedRowKeys = []
      this.selectedRows = []
    },
    // tab选中项发生变化时的回调
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      console.log(selectedRowKeys)
      this.$emit('selectedRows', selectedRowKeys, selectedRows, this.onlyAllChooseNowPage)
    },
    // 用户手动选择/取消选择某列的回调
    onSelect(record, selected, selectedRows) {
      this.$emit('onSelect', record, selected, selectedRows)
    },
    // 用户手动选择/取消选择所有列的回调
    onSelectAll(selected, selectedRows, changeRows) {
      this.$emit('onSelectAll', selected, selectedRows, changeRows)
    },
    /**
     * 检查按钮是否有权限
     * @param {按钮id} id
     * @returns
     */
    checkBtnPermissions(id) {
      return this.$store.getters.checkButtonPermissions(id)
    },
    // 操作
    operate(record, key, index, sub) {
      this.$emit('operate', record, key, index, sub)
    },
  },
}
</script>
<style scoped lang="scss">
.MultiTableScrollingBox {
  max-height: 600px;
  overflow: auto;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
</style>
