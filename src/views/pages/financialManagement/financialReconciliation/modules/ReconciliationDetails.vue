<template>
  <!-- 供应商和客户对账页 -->
  <div id="reconcliationBOx">
    <a-spin :spinning="confirmLoading">
      <div style="padding: 0 16px; padding-top: 15px">
        <a-form-model ref="ruleForm" :model="form" :rules="rules">
          <template v-if="ReconciliationType === supplierReconciliationType">
            <a-row :gutter="80">
              <a-col :md="queryCol">
                <a-form-model-item label="供应商名称" prop="CooperationCompanyName">
                  <a-input
                    disabled
                    style="width: 100%"
                    placeholder="请输入供应商名称/编号"
                    v-model="form.CooperationCompanyName"
                    :maxLength="50"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :md="queryCol">
                <a-form-model-item label="委托人" prop="AgentName">
                  <a-input
                    disabled
                    style="width: 100%"
                    placeholder="请输入"
                    v-model="form.AgentName"
                    :maxLength="50"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :md="queryCol">
                <a-form-model-item label="账期" prop="rangeDate">
                  <a-range-picker
                    disabled
                    style="width: 100%"
                    v-model="form.rangeDate"
                    :placeholder="['开始时间', '结束时间']"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </template>
          <template v-if="ReconciliationType === customerReconciliationType">
            <a-row :gutter="80">
              <a-col :md="queryCol">
                <a-form-model-item label="客户名称" prop="CooperationCompanyName">
                  <a-input
                    disabled
                    style="width: 100%"
                    placeholder="请输入客户名称/编号"
                    v-model="form.CooperationCompanyName"
                    :maxLength="50"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :md="queryCol">
                <a-form-model-item label="账期" prop="rangeDate">
                  <a-range-picker
                    disabled
                    style="width: 100%"
                    v-model="form.rangeDate"
                    :placeholder="['开始时间', '结束时间']"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </template>
        </a-form-model>
      </div>
      <a-card class="noborderBottom">
        <h1>导入明细</h1>
      </a-card>
      <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
      <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
      <div class="tab-title">
        <template v-if="tabDataList.length > 0">
          <a-radio-group v-model="curStatus" @change="onTabChange">
            <a-radio-button
              v-for="(item, index) in tabDataList"
              :value="item['value']"
              :key="index"
            >{{ item.name != undefined ? item.name : item.Name }} {{ '(' + item.count + ')' }}</a-radio-button
            >
          </a-radio-group>
        </template>
        <div v-else>&nbsp;</div>
        <YYLButton v-if="!noEdit" :menuId="importMenuId" text="导入" @click="showImportModal" />
      </div>
      <!-- 列表 -->
      <TableNewView ref="tableView" :table="table" :columns="columns" :dataList="dataSource">
        <div slot="bottomView" style="position: relative; z-index: 1">
          <div class="positionCount">合计金额：￥{{ totalAmount }}</div>
        </div>
      </TableNewView>
      <div class=""></div>
      <a-card class="borderBottom">
        <div class="flex">
          <h1>对账结果</h1>
          <YYLButton
            style="margin-right: 0"
            :menuId="exportReconciliationResultMenuId"
            text="导出对账结果"
            type="primary"
            @click="exportReconciliationResult"
          />
        </div>
      </a-card>
      <!-- 选择导入类型弹窗 -->
      <ChooseImportModal ref="chooseImportModal" @confirmType="confirmType" />
      <!-- 批量导入 导入商品 -->
      <BatchImportSimpleModal
        ref="iBatchImportGoodsModal"
        modalTitle="导入商品明细"
        :tableColumns="goodsImportColumns"
        :searchParamsList="searchImportGoodsInput"
        :importConfig="goodsImportConfig"
        :importUrl="`/{v}/PurchaseSalesStatement/ImportPurchaseSalesStatementGoodsDataTemp?purchaseSalesStatementId=${reconciliationId}`"
        importHttpHead="P36012"
        :diyStyle="{
          width: 1200,
        }"
        @importLoadAfter="importLoadAfter"
        @ok="(e) => handleBatchImportModalOk(e, 'goods')"
      >
        <div slot="bottomView">
          <div class="importTotal">合计金额：￥{{ importAmount }}</div>
        </div>
      </BatchImportSimpleModal>
      <!-- 批量导入 导入票折 -->
      <BatchImportSimpleModal
        ref="iBatchImportTicketModal"
        modalTitle="导入票折"
        :tableColumns="TicketImportColumns"
        :searchParamsList="searchImportTicketInput"
        :importConfig="TicketImportConfig"
        :importUrl="`/{v}/PurchaseSalesStatement/ImportThirdInvoiceDiscount?purchaseSalesStatementId=${reconciliationId}`"
        importHttpHead="P36012"
        :diyStyle="{
          width: 1200,
        }"
        @importLoadAfter="importLoadAfter"
        @ok="(e) => handleBatchImportModalOk(e, 'ticket')"
      >
        <div slot="bottomView">
          <div class="importTotal">账单金额：￥{{ importAmount }}</div>
        </div>
      </BatchImportSimpleModal>
      <!-- 导出对账结果 -->
      <ExportReconciliationResultsModal
        ref="ExportReconciliationResultsModal"
        modalTitle="导出对账结果"
        exportKey="ReconciliationType"
        :recordArray="recordArray"
        :exportParams="exportParams"
      />
      <!-- loading提示弹窗 -->
      <GlobalLoading
        :text="'对账生成中，请稍等~'"
        :getContainer="getContainer"
        ref="GlobalLoading"
        @continueSearch="continueSearch"
      />
      <a-tabs v-model="activeTab">
        <a-tab-pane key="1" :tab="getTabName(1)" force-render>
          <AccountsReceivableInformation :form="form" v-if="activeTab == 1" />
        </a-tab-pane>
        <a-tab-pane key="2" :tab="getTabName(2)" force-render>
          <StatementStatistics :info="form" @billCorrectionAmount="billCorrectionAmount" v-if="activeTab == 2" />
        </a-tab-pane>
        <a-tab-pane key="3" :tab="getTabName(3)" force-render>
          <NoDifferentiatedProductDetails
            :thirdPartyName="thirdPartyName"
            @updateCount="updateCount"
            v-if="activeTab == 3"
          />
        </a-tab-pane>
        <a-tab-pane key="4" :tab="getTabName(4)" force-render>
          <UnmatchedProductRecord :thirdPartyName="thirdPartyName" v-if="activeTab == 4" @updateCount="updateCount" />
        </a-tab-pane>
        <a-tab-pane key="5" :tab="getTabName(5)" force-render>
          <AbnormalPricesGoods v-if="activeTab == 5" :thirdPartyName="thirdPartyName" @updateCount="updateCount" />
        </a-tab-pane>
        <a-tab-pane v-if="ReconciliationType === supplierReconciliationType" key="6" :tab="getTabName(6)" force-render>
          <TicketDiscountReconciliationRecord
            :thirdPartyName="thirdPartyName"
            @updateCount="updateCount"
            v-if="activeTab == 6"
          />
        </a-tab-pane>
      </a-tabs>
      <a-card class="borderBottom">
        <div class="flex">
          <h1>备注</h1>
        </div>
      </a-card>
      <a-row>
        <a-col :span="12">
          <a-textarea
            :disabled="ifAudit"
            placeholder="请输入备注信息"
            :maxLength="200"
            v-model="form.Remark"
            style="width: 100%; margin: 10px 0; margin-left: 16px"
            :auto-size="{ minRows: 5, maxRows: 7 }"
          />
        </a-col>
      </a-row>
      <a-affix v-if="!noEdit" :offset-bottom="0" style="float: right; width: 100%; text-align: right">
        <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
          <YYLButton
            :menuId="saveMenuId"
            text="保存"
            type="primary"
            :loading="confirmLoading"
            @click="saveReconciliation"
          />
          <YYLButton
            :menuId="saveAndSubmitMenuId"
            text="保存并提交审核"
            :disabled="!showSubmitBtn"
            type="primary"
            @click="submitReconciliation"
            :loading="confirmLoading"
          />
          <a-button
            @click="
              goBack(
                true,
                ReconciliationType === supplierReconciliationType
                  ? '/pages/financialManagement/financialReconciliation/supplierReconciliationList'
                  : '/pages/financialManagement/financialReconciliation/customerReconciliationList'
              )
            "
          >取消</a-button
          >
        </a-card>
      </a-affix>
    </a-spin>
  </div>
</template>
<script>
import { EventBus } from '@/utils/eventBus';
import { getAction, postAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import { result } from 'lodash'
const _ = require('lodash')
const goodsImportColumns = [
  {
    title: '单据日期',
    dataIndex: 'DocumentDate',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '商品编号',
    dataIndex: 'ErpGoodsCode',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '商品名称',
    ellipsis: true,
    width: 150,
    dataIndex: 'ErpGoodsName',
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '规格',
    width: 150,
    dataIndex: 'PackingSpecification',
  },
  {
    title: '生产厂家',
    dataIndex: 'BrandManufacturer',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '数量',
    dataIndex: 'Quantity',
    width: 150,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '含税价',
    width: 200,
    dataIndex: 'TaxIncludedPurchasePrice',
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '含税金额',
    width: 200,
    dataIndex: 'TaxIncludedPurchaseAmount',
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '异常原因',
    dataIndex: 'ErrorInfo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 80,
    actionBtns: [{ name: '移除', icon: '' }],
    scopedSlots: { customRender: 'action' },
  },
]
const TicketImportColumns = [
  {
    title: '单据日期',
    dataIndex: 'DocumentDate',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '生产厂家',
    dataIndex: 'Manufacturer',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '票折金额',
    width: 200,
    dataIndex: 'DiscountAmount',
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '异常原因',
    dataIndex: 'ErrorInfo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 80,
    actionBtns: [{ name: '移除', icon: '' }],
    scopedSlots: { customRender: 'action' },
  },
]

export default {
  name: 'ReconciliationDetails',
  mixins: [ListMixin],
  provide() {
    return {
      ReconciliationType: this.ReconciliationType,
      supplierReconciliationType: this.supplierReconciliationType,
      customerReconciliationType: this.customerReconciliationType,
      ifAudit: this.noEdit,
      reconciliationId: this.reconciliationId,
      isExamine: this.isExamine,
      getContainer: this.getContainer,
    }
  },
  props: {
    ReconciliationType: {
      //对账类型
      type: Number,
      default: 1,
    },
    ifAudit: {
      //是否是审核页进入
      type: Boolean,
      default: false,
    },
    // 是否是审核列表页进入（需要替换某些接口）
    isExamine: {
      type: Boolean,
      default: false,
    },
    isDetail: {
      //是否是对账列表详情页进入
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      confirmLoading: false,
      haveLoad: false, // 页面是否从新加载了
      isInitData: false,
      importAmount: 0, // 导入商品或者导入票折后的总金额
      getContainer: () => document.body,
      timer: null,
      pollingCount: 0, // 轮询查询次数
      supplierReconciliationType: 1, // 供应商对账所属类型值
      customerReconciliationType: 2, // 客户对账所属类型值
      form: {}, // 账期信息表单
      queryCol: 8,
      searchItems: {},
      curStatus: 1, // 当前查询的是商品导入还是票折 1-商品 2-票折
      table: {},
      tabDataList: [],
      resultTabsInfo: {
        1: {
          name: `账单信息`,
          HideCount: true,
          count: 0,
          exportValue: 1,
        },
        2: {
          name: '对账单统计',
          count: 1,
          exportValue: 3,
        },
        3: {
          name: '无差异商品明细',
          count: 0,
          exportValue: 4,
        },
        4: {
          name: '未匹配商品记录',
          count: 0,
          exportValue: 5,
        },
        5: {
          name: '价格异常商品记录',
          count: 0,
          exportValue: 6,
        },
        6: {
          name: '票折对账记录',
          count: 0,
          exportValue: 7,
        },
      },
      httpHead: 'P36012',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/PurchaseSalesStatement/GetPurchaseSalesStatementGoodsList', // 当前列表数据接口
        list1: '/{v}/PurchaseSalesStatement/GetPurchaseSalesStatementGoodsList', // 商品明细列表数据接口
        list2: '/{v}/PurchaseSalesStatement/GetThirdInvoiceDiscountOriginBillList', // 票折列表数据接口
        info: '/{v}/PurchaseSalesStatement/GetPurchaseSalesStatementDetail', // 对账详情基本信息
        statistic: '/{v}/PurchaseSalesStatement/GetPurchaseSalesStatementQuantityStatistics', // 对账单统计信息
        checkStatus: '/{v}/PurchaseSalesStatement/GetPurchaseSalesStatementStatus', // 检查对账状态接口
        save: '/{v}/PurchaseSalesStatement/SavePurchaseSalesStatement', // 对账保存接口
        saveAndSubmit: '/{v}/PurchaseSalesStatement/SubmitPurchaseSalesStatement', // 对账保存并提交审核
        exportReconciliationResultUrl: '/{v}/PurchaseSalesStatement/ExportPurchaseSalesStatement', // 导出对账结果
      },
      urlExamine: {
        list: '/{v}/PurchaseSalesStatementAudit/GetPurchaseSalesStatementGoodsList', // 当前列表数据接口
        list1: '/{v}/PurchaseSalesStatementAudit/GetPurchaseSalesStatementGoodsList', // 商品明细列表数据接口
        list2: '/{v}/PurchaseSalesStatementAudit/GetThirdInvoiceDiscountOriginBillList', // 票折列表数据接口
        info: '/{v}/PurchaseSalesStatementAudit/GetPurchaseSalesStatementDetail', // 对账详情基本信息
        statistic: '/{v}/PurchaseSalesStatementAudit/GetPurchaseSalesStatementQuantityStatistics', // 对账单统计信息
        exportReconciliationResultUrl: '/{v}/PurchaseSalesStatementAudit/ExportPurchaseSalesStatement', // 导出对账结果
      },
      totalAmount: '', // 导入合计金额
      // 导入商品组件参数设置 --------------------------------
      // 导入的参数
      searchImportGoodsInput: [
        {
          name: '商品', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'KeyWord', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '生产厂家', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'ManufacturerName', //搜索key 必填
          placeholder: '请输入',
        },
      ],
      goodsImportColumns: goodsImportColumns,
      // 导入票折组件参数设置 ---------------------------------
      searchImportTicketInput: [
        {
          name: '单据日期',
          type: this.$SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'DocumentDateBegin',
          keyEnd: 'DocumentDateEnd',
          showTime: true,
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '生产厂家', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'Manufacturer', //搜索key 必填
          placeholder: '请输入',
        },
      ],
      TicketImportColumns: TicketImportColumns,
      // 对账结果tabs当前激活项
      activeTab: '1',
    }
  },
  computed: {
    noEdit() {
      return this.ifAudit || this.isDetail
    },
    showSubmitBtn() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      if (ReconciliationType === supplierReconciliationType) {
        const tabDataList = this.tabDataList
        let countAll = 0
        tabDataList.map((f) => {
          countAll += f.count
        })
        return countAll > 0 ? true : false
      } else {
        const dataSource = this.dataSource || []
        return dataSource.length > 0 ? true : false
      }
    },
    thirdPartyName() {
      return this.form.CooperationCompanyName
    },
    reconciliationId() {
      return this.$route.query.id
    },
    rules() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return {
            CooperationCompanyName: [{ required: true, message: '', trigger: 'blur' }],
            rangeDate: [{ required: true, message: '', trigger: 'change' }],
          }
        case customerReconciliationType:
          return {
            CooperationCompanyName: [{ required: true, message: '', trigger: 'blur' }],
            rangeDate: [{ required: true, message: '', trigger: 'change' }],
          }
      }
    },
    columns() {
      const curStatus = this.curStatus
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      const goodColumn = [
        {
          title: '单据日期',
          dataIndex: 'DocumentDate',
          align: 'center',
          width: 160,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'date' },
        },
        {
          title: '商品编号',
          width: 150,
          align: 'center',
          ellipsis: true,
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'Manufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '数量',
          dataIndex: 'Quantity',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '含税价',
          dataIndex: 'TaxIncludedPrice',
          width: 150,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '含税金额',
          dataIndex: 'TaxIncludedAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
        },
      ]
      const TicketColumn = [
        {
          title: '单据日期',
          dataIndex: 'DocumentDate',
          align: 'center',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'date' },
        },
        {
          title: '厂家',
          dataIndex: 'Manufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '含税金额',
          dataIndex: 'DiscountAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
        },
      ]
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return curStatus === 1 ? goodColumn : TicketColumn
        case customerReconciliationType:
          return goodColumn
      }
    },
    goodsImportConfig() {
      return {
        importTitle: '商品',
        templateFileName: '对账商品导入模板', //下载模板名字
        ErrorShowKey: 'ErrorInfo', //异常信息字段名称
        saveKey: 'BatchId',
        saveUrlType: 'POST', //保存接口请求方式
        saveUrl: '/{v}/PurchaseSalesStatement/ImportPurchaseSalesStatementGoodsConfirm', //保存接口
        listQueryKey: 'BatchId', //列表的时候需要用到的字段
        batchRemoveId: '', //批量移除id
        listRemoveType: 'DELETE', //列表删除接口请求方式
        listRemoveUrl: '/{v}/PurchaseSalesStatement/DeletePurchaseSalesStatementGoodsImportDataTemp', //列表删除 接口
        listRemoveKey: '', //列表删除 参数 key
        listHttpHead: 'P36012',
        listUrl: '/{v}/PurchaseSalesStatement/GetPurchaseSalesStatementGoodsImportTempList', //列表的请求接口
        listUrlType: 'GET', //列表接口请求方式
        queryParamStatusKey: 'Status', //列表查询 异常 正常 key
        noLoadData: true, //是否默认弹出时候不加载数据
        importResKey: 'BatchId', //导出完成后返回的key 列表的时候用到
        dataSourceListKey: 'ImportDetailList',
        clearUrlType: 'DELETE',
        clearUrl: '',
        clearSaveKey: 'BatchId',
        haveDataSourceKey: 'Id',
        onlySaveOneData: null,
        bottomBtn: [
          { name: '取消', type: '', funType: 'handleCancel' },
          { name: '模板下载', type: 'primary', funType: 'downloadTempFile' },
          { name: '确认', type: 'primary', funType: 'handleOk' },
        ],
      }
    },
    TicketImportConfig() {
      return {
        importTitle: '票折',
        templateFileName: '票折对账导入模板', //下载模板名字
        ErrorShowKey: 'ErrorInfo', //异常信息字段名称
        saveKey: 'BatchId',
        saveUrlType: 'PUT', //保存接口请求方式
        saveUrl: `/{v}/PurchaseSalesStatement/SaveThirdInvoiceDiscountTempData?purchaseSalesStatementId=${this.reconciliationId}`, //保存接口
        listQueryKey: 'BatchId', //列表的时候需要用到的字段
        batchRemoveId: '', //批量移除id
        listRemoveType: 'DELETE', //列表删除接口请求方式
        listRemoveUrl: '/{v}/PurchaseSalesStatement/DelThirdInvoiceDiscountTempData', //列表删除 接口
        listRemoveKey: '', //列表删除 参数 key
        listHttpHead: 'P36012',
        listUrl: '/{v}/PurchaseSalesStatement/GetThirdInvoiceDiscountImportTempList', //列表的请求接口
        listUrlType: 'GET', //列表接口请求方式
        queryParamStatusKey: 'Status', //列表查询 异常 正常 key
        noLoadData: true, //是否默认弹出时候不加载数据
        importResKey: 'BatchId', //导出完成后返回的key 列表的时候用到
        dataSourceListKey: 'ItemList',
        clearUrlType: 'DELETE',
        clearUrl: '',
        clearSaveKey: 'BatchId',
        haveDataSourceKey: 'Id',
        onlySaveOneData: null,
        bottomBtn: [
          { name: '取消', type: '', funType: 'handleCancel' },
          { name: '模板下载', type: 'primary', funType: 'downloadTempFile' },
          { name: '确认', type: 'primary', funType: 'handleOk' },
        ],
      }
    },
    // 导出对账结果按钮Id
    exportReconciliationResultMenuId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return 'd7a21c3c-1976-43b2-9b80-2b9d908234b0'
        case customerReconciliationType:
          return '31fe153f-4d5c-4a55-bb33-e4e83a3314b9'
      }
    },
    // 导出对账结果复选框数组
    recordArray() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      let resultTabsInfo = JSON.parse(JSON.stringify(this.resultTabsInfo))
      let list = []
      Object.keys(resultTabsInfo).map((key) => {
        list.push(resultTabsInfo[key])
      })
      let List = list.map((f) => {
        return { name: f.name, value: f.exportValue }
      })
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return List
        case customerReconciliationType:
          List.pop()
          return List
      }
    },
    // 导出对账结果url参数
    exportParams() {
      let basicUr = {
        name: `${this.form.CooperationCompanyName}-对账结果导出`,
        url: this.url.exportReconciliationResultUrl,
        methods: 'POST',
        httpHead: 'P36012',
      }
      return basicUr
    },
    // 底部按钮Id
    saveMenuId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return '53e14299-46a0-4085-89bc-886a0c157de5'
        case customerReconciliationType:
          return '2d632c66-176e-4e87-9616-b55ad6b5c3e4'
      }
    },
    // 保存和提交按钮Id
    saveAndSubmitMenuId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return '4614307b-c77f-46cb-85e9-ed17f6cac37f'
        case customerReconciliationType:
          return '617e937c-8fe1-4fa0-850c-1d780cb2fad0'
      }
    },
    // 导入按钮Id
    importMenuId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return '27f495fe-edbc-4a35-a990-e5d2c75cab96'
        case customerReconciliationType:
          return '01e6ca6f-2c18-44cd-9bd8-f87d2b3327a4'
      }
    },
    getTabName() {
      return function (val) {
        const resultTabsInfo = JSON.parse(JSON.stringify(this.resultTabsInfo))
        if (resultTabsInfo[val]) {
          return !resultTabsInfo[val].HideCount
            ? `${resultTabsInfo[val].name}（${resultTabsInfo[val].count}）`
            : `${resultTabsInfo[val].name}`
        } else {
          return ''
        }
      }
    },
  },
  watch: {
    // 一些属性无法设置成计算属性，需要通过监听ReconciliationType变化而设置在data里的值
    ReconciliationType: {
      handler(val) {
        let tabDataList = null
        let table = null
        let ifAudit = this.ifAudit
        const supplierReconciliationType = this.supplierReconciliationType
        const customerReconciliationType = this.customerReconciliationType
        let resultTabsInfo = this.resultTabsInfo
        switch (val) {
          case supplierReconciliationType:
            table = {
              operateBtns: [], //右上角按钮集合
              rowKey: 'Id',
              scrollY: Math.ceil(this.$root.tableHeight / 2),
            }
            tabDataList = [
              {
                name: '商品明细',
                value: 1,
                count: 0,
              },
              {
                name: '票折',
                value: 2,
                count: 0,
              },
            ]
            resultTabsInfo[1].name = '应付账单信息'
            break
          case customerReconciliationType:
            table = {
              operateBtns: [], //右上角按钮集合
              rowKey: 'Id',
              scrollY: Math.ceil(this.$root.tableHeight / 2),
            }
            tabDataList = []
            resultTabsInfo[1].name = '应收账单信息'
            resultTabsInfo[1].exportValue = 2
            break
          default:
            break
        }
        // 针对审核处理
        if (ifAudit) {
          table.operateBtns = []
        }
        this.table = table
        this.tabDataList = tabDataList
        this.resultTabsInfo = resultTabsInfo
      },
      immediate: true,
    },
    curStatus: {
      handler(val) {
        let searchItems = [
          {
            name: '商品',
            type: this.$SEnum.INPUT,
            key: 'GoodsNameOrCode',
            disabled: true,
            placeholder: '请输入商品名称/编号',
          },
          {
            name: '生产厂家',
            type: this.$SEnum.INPUT,
            key: 'Manufacturer',
            placeholder: '请输入生产厂家',
          },
          {
            name: '单据日期',
            type: this.$SEnum.DATE,
            rangeDate: this.rangeDate,
            key: 'DocumentDateBegin',
            keyEnd: 'DocumentDateEnd',
            showTime: true,
            placeholder: ['开始日期', '结束日期'],
          },
        ]
        switch (val) {
          case 2:
            searchItems.shift()
            break
        }
        this.searchItems = searchItems
      },
      immediate: true,
    },
  },
  created() {
    this.init()
    EventBus.$on('startPolling', (val) => {
      this.polling(val)
    })
  },
  mounted() {
    this.getContainer = () => document.getElementById('reconcliationBOx')
  },
  // activated() {
  //   this.init()
  // },
  methods: {
    loadBefore() {
      if (!this.reconciliationId) {
        // 处理异常切换页面，触发接口报错bug
        return false
      }
      const isExamine = this.isExamine
      this.queryParam[isExamine ? 'purchaseSalesStatementRecordId' : 'purchaseSalesStatementId'] = this.reconciliationId
      // 加载前都要查询另外一个的值
    },
    loadAfter(Data) {
      this.dataSource = Data.ItemList
      const count = Data.Count || Data.TotalCount ? Data.Count || Data.TotalCount : 0
      let totalAmount = Data.TotalAmount || Data.TotalDiscountAmount
      this.totalAmount = totalAmount ? totalAmount.toFixed(6) : 0
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      if (ReconciliationType === supplierReconciliationType) {
        // 客户对账不需要查询两外一个接口
        this.getListOther(count)
      }
    },
    // 导入商品和票折后列表查询回调
    importLoadAfter(Data) {
      this.importAmount =
        Data.TotalAmount || Data.TotalDiscountAmount ? (Data.TotalAmount || Data.TotalDiscountAmount).toFixed(6) : 0
    },
    init() {
      if (this.isExamine) {
        // 审核页面进入需要替换对应的接口
        this.url = Object.assign({}, this.url, this.urlExamine)
      }
      if (this.noEdit) {
        // 对账详情页和审核进入不进行对账状态轮询查询
        this.getReconciliationInfo().then((res) => {
          this.loadData(1)
        })
      } else {
        this.getReconciliationInfo().then((res) => {
          if (!res) {
            this.polling()
          } else {
            this.loadData(1)
          }
        })
      }
    },
    // 退出页面暂停轮询
    stopLoop() {
      clearInterval(this.timer)
      this.timer = null
      this.$refs.GlobalLoading.close()
    },
    // 轮询查询对账状态
    polling(ifMonitor) {
      const that = this
      this.pollingCount = 0
      that.$refs.GlobalLoading.show()
      if (that.timer) {
        clearInterval(that.timer)
      }
      that.timer = setInterval(() => {
        if (that.timer) {
          // 超过10次直接弹出提示
          if (this.pollingCount >= 10) {
            clearInterval(that.timer)
            that.timer = null
            that.$refs.GlobalLoading.showBtn()
            return false
          }
          that.checkStatusNow().then((result) => {
            this.pollingCount++
            if (result) {
              clearInterval(that.timer)
              that.timer = null
              that.$refs.GlobalLoading.close()
              if(ifMonitor) {
                EventBus.$emit('endPolling', null) // 某些弹窗需要监听查询对账状态更新完成
              }else {
                that.refreshNowTab()
              }
            }
          })
        }
      }, 1000)
    },
    checkStatusNow() {
      return new Promise((resolve, reject) => {
        const that = this
        let url = that.url.checkStatus
        let formData = {
          purchaseSalesStatementId: this.reconciliationId,
        }
        getAction(url, formData, this.httpHead)
          .then((res) => {
            if (res.IsSuccess) {
              if (res.Data) {
                resolve(true)
              } else {
                resolve(false)
              }
            } else {
              resolve(false)
              this.$message.warning(res.Msg)
            }
          })
          .finally(() => {})
      })
    },
    // 获取页面顶部对账信息和底部应收账单信息
    getReconciliationInfo() {
      if (!this.reconciliationId) {
        // 处理异常切换页面，触发接口报错bug
        return false
      }
      const that = this
      const isExamine = this.isExamine
      return new Promise((resolve, reject) => {
        let url = this.url.info
        let formData = {
          [isExamine ? 'purchaseSalesStatementRecordId' : 'purchaseSalesStatementId']: this.reconciliationId,
        }
        getAction(url, formData, this.httpHead)
          .then((res) => {
            if (res.IsSuccess) {
              this.form = {
                ...res.Data,
                rangeDate: [res.Data.PeriodStart, res.Data.PeriodEnd],
                Remark: this.haveLoad ? this.form.Remark : res.Data.Remark
              }
              this.haveLoad = true
              if (res.Data.StatementStatus == 2) {
                that.getReconciliationStatics().then((f) => {
                  that.$emit('getBasicInfo', res.Data)
                  resolve(true)
                })
              } else {
                if(res.Data && res.Data.Id) {
                  resolve(false)
                }else {
                  resolve(true)  // 这儿可能是版本号修改导致接口返回正确，却StatementStatus不等于2得情况
                }
              }
            } else {
              this.$message.warning(res.Msg)
              if (res.Msg === '未找到对应的对账单') {
                // 对账单不存在便返回上一页
                this.goBack(true)
              } else {
                resolve(false)
              }
            }
          })
          .finally(() => {})
      })
    },
    // 获取对账单各项统计数值
    getReconciliationStatics() {
      return new Promise((resolve, reject) => {
        let url = this.url.statistic
        const isExamine = this.isExamine
        let formData = {
          [isExamine ? 'purchaseSalesStatementRecordId' : 'purchaseSalesStatementId']: this.reconciliationId,
        }
        getAction(url, formData, this.httpHead)
          .then((res) => {
            if (res.IsSuccess) {
              const {
                StatementCount,
                NormalGoodsCount,
                UnmatchedGoodsCount,
                AbnormalGoodsCount,
                DiscountRecordsCount,
              } = res.Data
              this.resultTabsInfo[2].count = StatementCount
              this.resultTabsInfo[3].count = NormalGoodsCount
              this.resultTabsInfo[4].count = UnmatchedGoodsCount
              this.resultTabsInfo[5].count = AbnormalGoodsCount
              this.resultTabsInfo[6].count = DiscountRecordsCount
            } else {
              this.$message.warning(res.Msg)
            }
            resolve()
          })
          .finally(() => {
            resolve()
          })
      })
    },
    // 操作
    showImportModal() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      if (ReconciliationType === supplierReconciliationType) {
        this.$refs.chooseImportModal.show()
      } else if (ReconciliationType === customerReconciliationType) {
        this.showImport(1)
      }
    },
    // 确认导入类型
    confirmType(form) {
      const { importType } = form
      this.showImport(importType)
    },
    // 显示导入弹窗  type-1 商品导入弹窗  type-2 票折导入弹窗
    showImport(type) {
      let queryParam = {
        purchaseSalesStatementId: this.reconciliationId,
      }
      this.importAmount = 0
      if (type === 1) {
        this.checkStatusNow().then((result) => {
          if (result) {
            this.$refs.iBatchImportGoodsModal.show(this.dataSource, queryParam)
          } else {
            this.$message.warning('对账生成中，请稍等~')
          }
        })
      } else if (type === 2) {
        this.$refs.iBatchImportTicketModal.show(this.dataSource, queryParam)
      }
    },
    // 导入商品明细/票折保存回调
    handleBatchImportModalOk(data, type) {
      if (type === 'goods') {
        this.polling()
      } else {
        this.refreshNowTab()
      }
    },
    // 刷新顶部导入信息刷新当前tabs
    refreshNowTab() {
      this.loadData(1)
      const activeTab = this.activeTab
      this.activeTab = '0'
      this.getReconciliationInfo().then(() => {
        this.activeTab = activeTab // 模拟tab切换触发当前tab刷新
      })
    },
    // 导出对账结果按钮点击事件
    exportReconciliationResult() {
      const isExamine = this.isExamine
      let param = {
        [isExamine ? 'purchaseSalesStatementRecordId' : 'purchaseSalesStatementId']: this.reconciliationId,
        StatementSource: this.ReconciliationType,
      }
      this.$refs.ExportReconciliationResultsModal.show(param)
    },
    /**
     * 导入明细顶部切换tab change事件
     */
    onTabChange(e) {
      let queryParam = _.cloneDeep(this.getQueryParams()) //查询条件
      this.rangeDate = [queryParam.DocumentDateBegin, queryParam.DocumentDateEnd]
      const curStatus = e.target.value
      this.getList(curStatus)
    },
    // 获取顶部商品明细或者票折列表数据
    getList(curStatus) {
      this.url.list = this.url[`list${curStatus}`]
      this.loadData(1)
    },
    getListOther(Count1) {
      var params = this.getQueryParams() //查询条件
      let curStatus = this.curStatus
      let url = curStatus === 1 ? this.url.list2 : this.url.list1
      let formData = {
        ...params,
      }
      getAction(url, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            let Data = res.Data
            const Count2 = Data.Count || Data.TotalCount ? Data.Count || Data.TotalCount : 0
            if (curStatus === 1) {
              this.tabDataList[0].count = Count1
              this.tabDataList[1].count = Count2
            } else if (curStatus === 2) {
              this.tabDataList[0].count = Count2
              this.tabDataList[1].count = Count1
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {})
    },
    // 账单金额修正回调
    billCorrectionAmount() {
      this.getReconciliationInfo()
    },
    // 对账结果各项统计值回调
    updateCount(type, val) {
      // 刷新总的统计值和账单信息，然后单独更新当前tab数据
      this.getReconciliationInfo().then((res) => {
        if (!res) {
          this.polling()
        } else {
          const item = {
            ...this.resultTabsInfo[type],
            count: val,
          }
          this.$set(this.resultTabsInfo, `${type}`, item)
        }
      })
    },
    // 保存对账
    saveReconciliation() {
      const that = this
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      let url = that.url.save
      let formData = {
        purchaseSalesStatementId: that.reconciliationId,
        Remark: that.form.Remark,
      }
      that.confirmLoading = true
      postAction(url, formData, that.httpHead)
        .then((res) => {
          that.confirmLoading = false
          if (res.IsSuccess) {
            that.$message.success('操作成功!')
            that.goBack(
              true,
              ReconciliationType === supplierReconciliationType
                ? '/pages/financialManagement/financialReconciliation/supplierReconciliationList'
                : '/pages/financialManagement/financialReconciliation/customerReconciliationList'
            )
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    // 保存并提交对账
    submitReconciliation() {
      const that = this
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      let url = that.url.saveAndSubmit
      let formData = {
        purchaseSalesStatementId: that.reconciliationId,
        Remark: that.form.Remark,
      }
      that.confirmLoading = true
      postAction(url, formData, that.httpHead)
        .then((res) => {
          that.confirmLoading = false
          if (res.IsSuccess) {
            that.$message.success('操作成功!')
            that.goBack(
              true,
              ReconciliationType === supplierReconciliationType
                ? '/pages/financialManagement/financialReconciliation/supplierReconciliationList'
                : '/pages/financialManagement/financialReconciliation/customerReconciliationList'
            )
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    // 继续轮询
    continueSearch() {
      this.polling()
    },
  },
  beforeDestroy() {
    EventBus.$off('startPolling', null);
  },
}
</script>
<style scoped lang="scss">
.noborderBottom {
  border-bottom: none;

  h1 {
    font-weight: bolder;
    margin-right: 15px;
    font-size: 14px;
    margin-bottom: 0;
  }
}

.tab-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 10px 16px;

  ::v-deep .mr8 {
    margin-right: 0;
  }
}

.borderBottom {
  .flex {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  h1 {
    font-weight: bolder;
    margin-right: 15px;
    font-size: 14px;
    margin-bottom: 0;
  }
}

.positionCount {
  margin: 0 16px;
  font-size: 16px;
  position: absolute;
  left: 0;
  top: -15px;
  transform: translateY(-100%);
}
.importTotal {
  font-size: 16px;
}
</style>
