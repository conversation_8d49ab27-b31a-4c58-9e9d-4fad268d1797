<template>
  <a-modal
    :title="'模糊匹配'"
    :width="'80vw'"
    :visible="visible"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :footer="null"
    v-drag-modal
  >
    <!--模糊匹配弹窗 -->
    <div
      :style="{
        width: '100%',
        background: '#fff',
      }"
    >
      <a-spin :spinning="confirmLoading">
        <div>
          <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
          <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
          <a-row :gutter="10">
            <a-col :md="24">
              <!-- 数据列表 -->
              <!-- 列表 -->
              <TableNewView
                ref="tableView"
                :table="table"
                :columns="columns"
                :tabDataList="tabDataList"
                :dataList="dataSource"
                @operate="operate"
              >
              </TableNewView>
            </a-col>
          </a-row>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script>
import { EventBus } from '@/utils/eventBus';
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, postAction, putAction } from '@/api/manage'
export default {
  title: '模糊匹配',
  name: 'FuzzyMatching',
  mixins: [ListMixin, EditMixin],
  inject: ['reconciliationId', 'getContainer'],
  components: {
    JEllipsis,
  },
  props: {
    thirdPartyName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      visible: true,
      confirmLoading: false,
      getContainer: () => document.body,
      timer: null,
      pollingCount: 0, // 轮询查询次数
      haveChage: false, // 是否发生过绑定和取消绑定
      searchItems: [
        {
          name: '商品名称', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'GoodsCodeOrName', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '生产厂家', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'Manufacturer', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '品种责任人', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'GoodsOwnerBy', //搜索key 必填
          placeholder: '请输入',
        },
      ],
      table: {
        bordered: true,
        operateBtns: [], //右上角按钮集合
        rowKey: 'newId',
        customSlot: [],
      },
      tabDataList: [],
      dataSource: [],
      httpHead: 'P36012',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/PurchaseSalesStatement/GetAbnormalGoodsList', //列表数据接口,
        save: '/{v}/CompanyGoodsMapping/Save', // 绑定
        unSave: '/{v}/CompanyGoodsMapping/PurchaseSalesStatementUnbind', // 取消绑定
        checkStatus: '/{v}/PurchaseSalesStatement/GetPurchaseSalesStatementStatus', // 检查对账状态接口
      },
    }
  },
  computed: {
    columns() {
      return [
        {
          title: this.thirdPartyName,
          children: [
            {
              title: '商品编号',
              dataIndex: 'OtherPartyErpGoodsCode',
              width: 150,
              ellipsis: true,
              align: 'center',
              // scopedSlots: { customRender: 'component' },
              customRender: (text, record, index) => {
                const obj = {
                  children: <j-ellipsis value={text == 0 || text ? String(text) : ''} />,
                  attrs: {},
                }
                obj.attrs.rowSpan = record.rowSpan
                return obj
              },
            },
            {
              title: '商品名称',
              dataIndex: 'OtherPartyErpGoodsName',
              width: 150,
              align: 'center',
              ellipsis: true,
              // scopedSlots: { customRender: 'component' },
              customRender: (text, record, index) => {
                const obj = {
                  children: <j-ellipsis value={text == 0 || text ? String(text) : ''} />,
                  attrs: {},
                }
                obj.attrs.rowSpan = record.rowSpan
                return obj
              },
            },
            {
              title: '规格',
              dataIndex: 'OtherPartyPackingSpecification',
              width: 150,
              align: 'center',
              ellipsis: true,
              // scopedSlots: { customRender: 'component' },
              customRender: (text, record, index) => {
                const obj = {
                  children: <j-ellipsis value={text == 0 || text ? String(text) : ''} />,
                  attrs: {},
                }
                obj.attrs.rowSpan = record.rowSpan
                return obj
              },
            },
            {
              title: '生产厂家',
              dataIndex: 'OtherPartyBrandManufacturer',
              width: 150,
              align: 'center',
              ellipsis: true,
              // scopedSlots: { customRender: 'component' },
              customRender: (text, record, index) => {
                const obj = {
                  children: <j-ellipsis value={text == 0 || text ? String(text) : ''} />,
                  attrs: {},
                }
                obj.attrs.rowSpan = record.rowSpan
                return obj
              },
            },
          ],
        },
        {
          title: '采销系统',
          children: [
            {
              title: '商品编号',
              dataIndex: 'ErpGoodsCode',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '商品名称',
              dataIndex: 'ErpGoodsName',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '规格',
              dataIndex: 'PackingSpecification',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '生产厂家',
              dataIndex: 'BrandManufacturer',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '品种责任人',
              dataIndex: 'GoodsOwnerBy',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '操作',
              dataIndex: 'action2',
              align: 'center',
              width: 100,
              // fixed: 'right',
              actionBtns: [
                {
                  name: '绑定',
                  icon: '',
                  isShow: (record) => {
                    return true
                  },
                },
                // {
                //   name: '取消绑定',
                //   icon: '',
                //   isShow: (record) => {
                //     return record.IsBound
                //   },
                // },
              ],
              scopedSlots: { customRender: 'action' },
            },
          ],
        },
      ]
    },
  },
  created() {
    const that = this
    EventBus.$on('endPolling', (val) => {
      that.loadData()
    });
  },
  mounted() {},
  methods: {
    loadBefore() {
      this.queryParam['PurchaseSalesStatementId'] = this.reconciliationId
    },
    loadAfter(Data) {
      const newList = Data.map((item, index) => {
        return {
          ...item,
          newId: index + 1,
        }
      })
      this.dataSource = this.setTableMerge(newList, 'Id', true)
    },
    loadFail() {
      EventBus.$emit('startPolling', true)
    },
    // searchQuery(param) {
    //   this.loadData()
    // },
    // 操作
    operate(record, key, index) {
      console.log(record, key, index)
      if (key == '绑定') {
        this.BindGoods(record)
      } else if (key == '取消绑定') {
        this.unBindGoods(record)
      }
    },
    show(record) {
      this.visible = true
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
      this.queryParam = {}
      this.$emit('close')
      if (this.haveChage) {
        this.$emit('confirmBind')
      }
      this.haveChage = false
    },
    // 绑定商品
    BindGoods(record) {
      const that = this
      let url = that.url.save
      let formData = {
        ...record,
        PurchaseSalesStatementId: this.reconciliationId,
        Id: null,
      }
      that.confirmLoading = true
      postAction(url, formData, that.httpHead)
        .then((res) => {
          that.confirmLoading = false
          if (res.IsSuccess) {
            this.$message.success('操作成功!')
            this.haveChage = true
            this.loadData()
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    // 取消绑定商品
    unBindGoods(record) {
      const that = this
      let url = that.url.unSave
      let formData = {
        PurchaseSalesStatementId: this.reconciliationId,
        id: record.CompanyGoodsMappingId,
      }
      that.confirmLoading = true
      getAction(url, formData, that.httpHead)
        .then((res) => {
          that.confirmLoading = false
          if (res.IsSuccess) {
            this.$message.success('操作成功!')
            this.haveChage = true
            this.loadData()
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
  },
  beforeDestroy() {
    EventBus.$off('endPolling', null);
  },
}
</script>

<style scoped lang="scss"></style>
