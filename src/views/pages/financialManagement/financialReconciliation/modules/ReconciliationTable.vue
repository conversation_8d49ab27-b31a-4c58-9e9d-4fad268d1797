<!-- 对账左右数据有差异表格实现 -->
<template>
  <a-spin :spinning="confirmLoading">
    <a-table
      style="margin-top: 15px"
      :bordered="true"
      size="small"
      :columns="columnsNew"
      :dataSource="dataListNew"
      :pagination="false"
      :loading="loading"
      @change="handleTableChange"
      :scroll="{
        y: dataList.length === 0 ? false : tab.scrollY ? tab.scrollY : this.$root.tableHeight,
        x: tableWidth(),
      }"
      :rowKey="tab.rowKey ? tab.rowKey : (record, index) => index"
    >
      <!-- 全选的checkbox -->
      <template slot="tableCheckboxTitle">
        <a-checkbox
          style="margin-top: 1px"
          :value="checkAllCheckbox"
          v-model="checkAllCheckbox"
          @change="onAllcheckboxChange"
        />
      </template>
      <!-- 操作 -->
      <span slot="action" slot-scope="text, record, index, column">
        <span v-for="(item, i) in column.actionBtns || []" :key="i">
          <!-- 根据条件隐藏显示 -->
          <span style="margin: 0 5px" v-if="actionBtnIsShow(item, record)">
            <template v-if="item.name === '删除'">
              <a-popconfirm
                v-if="item.showPopconToast"
                :title="item.title || `确定删除该项吗?`"
                :disabled="actionBtnIsDisabled(item, record)"
                @confirm="operate(record, item.name, index, item)"
              >
                <a :disabled="actionBtnIsDisabled(item, record)"> {{ item.name }}</a>
              </a-popconfirm>
              <a v-else @click="operate(record, item.name, index, item)"> {{ item.name }}</a>
            </template>
            <template v-else>
              <a @click="operate(record, item.name, index, item)" :disabled="item.disabled">
                <a-icon v-if="item.icon" :type="item.icon" />
                {{ item.name }}
              </a>
            </template>
          </span>
        </span>
        <span
          class="tm"
          v-if="
            column.actionBtns &&
              column.actionBtns.filter((btn) => btn.isShow == undefined || btn.isShow(record) == true).length == 0
          "
        ></span>
      </span>
    </a-table>
    <!-- 自定义分页 -->
    <a-pagination
      style="text-align: right; margin: 10px"
      size="small"
      show-quick-jumper
      showSizeChanger
      v-model="ipagination.current"
      :defaultPageSize="15"
      :page-size-options="ipagination.pageSizeOptions"
      :total="ipagination.total"
      :show-total="(total) => `总数 ${ipagination.total} 条`"
      @change="onPageChange"
      @showSizeChange="onPageChange"
    />
  </a-spin>
</template>

<script>
import moment from 'moment'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
const _ = require('lodash')
export default {
  name: 'ReconciliationTable',
  inject: [
    'ifAudit',
  ],
  components: { JEllipsis },
  props: {
    // 表格基础配置
    tab: {
      type: Object,
      default: () => {
        return {}
      },
    },
    /**
     * table数据
     */
    dataList: {
      type: Array,
      required: true,
      default: () => {
        return []
      },
    },
    // 表格基础列名
    columns: {
      type: Array,
      required: true,
      default: () => {
        return []
      },
    },
    // 是否勾选全部只勾选当前页
    onlyAllChooseNowPage: {
      type: Boolean,
      default: true,
    },
    // 左右两个列表数据的key名集合
    leftAndRightListKey: {
      type: Array,
      default: () => {
        return ['list1', 'list2']
      },
    },
    // 表示操作字段列的关键词  同columns里"操作”那一列的dataIndex值（包含关系）
    actionKey: {
      type: String,
      default: 'action',
    },
    // 分页数据
    pageInfo: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      dataListNew: null, // 重新组装的新数据
      /* table加载状态 */
      loading: false,
      confirmLoading: false,
      selectedRowKeys: [], //表格选中项的key
      selectedRowArray: [], //选中项的信息
      checkAllCheckbox: false,
      storeSelectedRowKeys: [], // 分页本地缓存的所有表格选中项的key
      storeSelectedRowArray: [], // 分页本地缓存的所有表格选中项
      ifSearchChange: true, // 是否查询条件改变（查询输入改变或者重置查询）
    }
  },
  computed: {
    actionBtnIsDisabled() {
      return function (item, record) {
        if (item.disabled) {
          // 特殊条件显示隐藏
          if (item.disabled(record) === true) {
            return true
          } else {
            return false
          }
        } else {
          // 未设置条件直接显示按钮
          return false
        }
      }
    },
    // 数据重新组装后的表格列名
    columnsNew() {
      let ifAudit = this.ifAudit 
      let selectColumn = {
        //表格最左侧的勾选框
        // title: '选择',
        slots: { title: 'tableCheckboxTitle' },
        dataIndex: 'checkbox',
        align: 'center',
        width: 120,
        ellipsis: true,
        customCell: (record, index) => {
          return {
            on: {
              change: (e) => {
                this.customCellChange(e)
              },
            },
          }
        },
      }
      let columns = _.cloneDeep(this.columns) // 不能使用JSon.parse(Json.stringify) 因为函数方法会丢失
      if(!ifAudit) {
        columns[0].children.unshift(selectColumn)
      }
      let columnsNew = columns
      const leftAndRightListKey = this.leftAndRightListKey
      columnsNew.map((item, index) => {
        if (item.children.length > 0) {
          item.children.map((son) => {
            son.dataIndex = `${leftAndRightListKey[index]}&${son.dataIndex}`
            if (son.dataIndex.indexOf(this.actionKey) === -1) {
              // 除去操作那一列外进行合并处理
              son.customRender = (value, row, index) => {
                let obj = {}
                if (son.dataIndex.indexOf('checkbox') !== -1) {
                  // 最左侧复选框样式自定义
                  obj = {
                    children: <a-checkbox v-model={row.checkbox} style="margin-right:6px;" />,
                    attrs: {},
                  }
                } else if (
                  son.scopedSlots.customRender &&
                  son.scopedSlots.customRender.indexOf('priceChangeShow') !== -1
                ) {
                  // 价格变化类自定义样式
                  obj = {
                    children: value ? (
                      value < 0 ? (
                        <div>
                          <div class="green">下降</div>
                          <div class="green top">￥{Math.abs(value)}</div>
                        </div>
                      ) : (
                        <div>
                          <div class="red">上涨</div>
                          <div class="red top">￥{Math.abs(value)}</div>
                        </div>
                      )
                    ) : (
                      ''
                    ),
                    attrs: {},
                  }
                } else if (son.scopedSlots.customRender && son.scopedSlots.customRender.indexOf('priceUpdate') !== -1) {
                  // 价格更新类自定义样式
                  obj = {
                    children: (
                      <j-ellipsis
                        value={
                          !row[`${leftAndRightListKey[0]}&IsAdjusted`]
                            ? ((row.whiteColumnLeft) ? '' : '--')
                            : (value < 0 ? '-' : '') +
                              '￥' +
                              (
                                Math.round(Number(value < 0 ? String(value).replace('-', '') : value) * 100) / 100
                              ).toFixed(2)
                        }
                      />
                    ),
                    attrs: {},
                  }
                } else if (son.scopedSlots.customRender && son.scopedSlots.customRender.indexOf('priceLeft') !== -1) {
                  // 价格类自定义样式
                  obj = {
                    children: (
                      <j-ellipsis
                        value={
                          value == undefined || value == null
                            ? row.whiteColumnLeft
                              ? ''
                              : '--'
                            : (value < 0 ? '-' : '') +
                              '￥' +
                              (
                                Math.round(Number(value < 0 ? String(value).replace('-', '') : value) * 100) / 100
                              ).toFixed(2)
                        }
                      />
                    ),
                    attrs: {},
                  }
                } else if (son.scopedSlots.customRender && son.scopedSlots.customRender.indexOf('priceRight') !== -1) {
                  // 价格类自定义样式
                  obj = {
                    children: (
                      <j-ellipsis
                        value={
                          value == undefined || value == null
                            ? row.whiteColumnRight
                              ? ''
                              : '--'
                            : (value < 0 ? '-' : '') +
                              '￥' +
                              (
                                Math.round(Number(value < 0 ? String(value).replace('-', '') : value) * 100) / 100
                              ).toFixed(2)
                        }
                      />
                    ),
                    attrs: {},
                  }
                } else if (son.scopedSlots.customRender && son.scopedSlots.customRender.indexOf('price') !== -1) {
                  // 价格类自定义样式
                  obj = {
                    children: (
                      <j-ellipsis
                        value={
                          value == undefined || value == null
                            ? '--'
                            : (value < 0 ? '-' : '') +
                              '￥' +
                              (
                                Math.round(Number(value < 0 ? String(value).replace('-', '') : value) * 100) / 100
                              ).toFixed(2)
                        }
                      />
                    ),
                    attrs: {},
                  }
                } else if (son.scopedSlots.customRender && son.scopedSlots.customRender.indexOf('dateLeft') !== -1) {
                  obj = {
                    children: (
                      <span>
                        <j-ellipsis
                          value={value ? moment(value).format('YYYY-MM-DD') : (row.whiteColumnLeft ? '' : '--')}
                          length={100}
                        />
                      </span>
                    ),
                    attrs: {},
                  }
                } else if (son.scopedSlots.customRender && son.scopedSlots.customRender.indexOf('dateRight') !== -1) {
                  obj = {
                    children: (
                      <span>
                        <j-ellipsis
                          value={value ? moment(value).format('YYYY-MM-DD') : (row.whiteColumnRight ? '' : '--')}
                          length={100}
                        />
                      </span>
                    ),
                    attrs: {},
                  }
                } else if (son.scopedSlots.customRender && son.scopedSlots.customRender.indexOf('date') !== -1) {
                  obj = {
                    children: (
                      <span>
                        <j-ellipsis
                          value={value ? moment(value).format('YYYY-MM-DD') : '--'}
                          length={100}
                        />
                      </span>
                    ),
                    attrs: {},
                  }
                } else {
                  obj = {
                    children: <j-ellipsis value={value == 0 || value ? String(value) : ''} />,
                    attrs: {},
                  }
                }
                const lfetSpecialMergekeys = row.lfetSpecialMergekeys
                const rightSpecialMergekeys = row.rightSpecialMergekeys || []
                const flag1 = lfetSpecialMergekeys.findIndex((f) => f.keyName === son.dataIndex)
                const flag2 = rightSpecialMergekeys.findIndex((f) => f.keyName === son.dataIndex)
                if (flag1 === -1 && flag2 === -1) {
                  if (row.rowSpan || row.rowSpan === 0) {
                    obj.attrs.rowSpan = row.rowSpan
                  }
                }
                // else if(flag1 === -1) {
                //   if (rightSpecialMergekeys[flag2].rowSpan || rightSpecialMergekeys[flag2].rowSpan === 0) {
                //     // console.log(rightSpecialMergekeys[flag2])
                //     obj.attrs.rowSpan = rightSpecialMergekeys[flag2].rowSpan
                //   }

                // } else if(flag2 === -1) {
                //   if (lfetSpecialMergekeys[flag1].rowSpan || lfetSpecialMergekeys[flag1].rowSpan === 0) {
                //     // console.log(lfetSpecialMergekeys[flag1])
                //     obj.attrs.rowSpan = lfetSpecialMergekeys[flag1].rowSpan
                //   }
                // }
                return obj
              }
            }
          })
        }
      })
      return columnsNew
    },
    actionBtnIsShow() {
      return function (item, record) {
        //是否有按钮权限优先判断
        if (!this.checkBtnPermissions(item.id)) {
          return false
        }
        if (item.isShow) {
          // 特殊条件显示隐藏
          if (item.isShow(record) === true) {
            return true
          } else {
            return false
          }
        } else {
          // 未设置条件直接显示按钮
          return true
        }
      }
    },
  },
  watch: {
    dataList: {
      handler(val) {
        const dataList = JSON.parse(JSON.stringify(val))
        const leftAndRightListKey = this.leftAndRightListKey
        const tab = this.tab
        let newList = []
        dataList.map((item) => {
          const leftList = item[leftAndRightListKey[0]]
          const rigthList = item[leftAndRightListKey[1]]
          let maxList = leftList.length >= rigthList.length ? leftList : rigthList
          let leftSpecialKeyArry = []
          let rightSpecialKeyArry = []
          maxList.map((son, sonIndex) => {
            let newItem = {
              lfetSpecialMergekeys: [], // 左侧列表的特殊合并项
              rightSpecialMergekeys: [], // 右侧列表的特殊合并项
            }
            let lfetItem = leftList[sonIndex] || null
            let rightItem = rigthList[sonIndex] || null

            // 左侧列表列计算合并项目
            if (lfetItem) {
              Object.keys(lfetItem).map((left) => {
                newItem[`${leftAndRightListKey[0]}&${left}`] = lfetItem[left]
                newItem.lfetSpecialMergekeys.push({
                  keyName: `${leftAndRightListKey[0]}&${left}`,
                  rowSpan: sonIndex === leftList.length - 1 ? maxList.length - leftList.length + 1 : null,
                })
              })
              leftSpecialKeyArry = newItem.lfetSpecialMergekeys
            } else {
              if (leftSpecialKeyArry.length > 0) {
                newItem.lfetSpecialMergekeys = leftSpecialKeyArry.map((itemSon) => {
                  return {
                    ...itemSon,
                    rowSpan: 0,
                  }
                })
              }
              newItem.whiteColumnLeft = true
            }
            // 右侧列表列计算合并项
            if (rightItem) {
              Object.keys(rightItem).map((right) => {
                newItem[`${leftAndRightListKey[1]}&${right}`] = rightItem[right]
                newItem.rightSpecialMergekeys.push({
                  keyName: `${leftAndRightListKey[1]}&${right}`,
                  rowSpan: sonIndex === rigthList.length - 1 ? maxList.length - rigthList.length + 1 : null,
                })
              })
              rightSpecialKeyArry = newItem.rightSpecialMergekeys
            } else {
              if (rightSpecialKeyArry.length > 0) {
                newItem.rightSpecialMergekeys = rightSpecialKeyArry.map((itemSon) => {
                  return {
                    ...itemSon,
                    rowSpan: 0,
                  }
                })
              }
              newItem.whiteColumnRight = true
            }
            // 转化普通列数
            Object.keys(item).map((key) => {
              if (key !== tab.rowKey) {
                newItem[`${leftAndRightListKey[0]}&${key}`] = item[key]
                newItem[`${leftAndRightListKey[1]}&${key}`] = item[key]
              }
            })
            newItem = {
              ...newItem,
              checkbox: false,
              [tab.rowKey]: sonIndex === 0 ? item[tab.rowKey] : item[tab.rowKey] + '&' + sonIndex,
              rowSpan: sonIndex === 0 ? maxList.length : 0, // 普通列数就直接从第一行数据合并到尾，其他行就删掉
            }
            newList.push(newItem)
          })
        })
        this.dataListNew = newList
        console.log(newList)
        if (this.ifSearchChange) {
          this.storeSelectedRowKeys = []
          this.storeSelectedRowArray = []
        }
        this.ifSearchChange = true
        this.backfillCheck()
      },
      immediate: true,
    },
    pageInfo: {
      handler(val) {
        this.ipagination = val
      },
      immediate: true,
    },
  },
  methods: {
    moment,
    // 分页时回显勾选
    backfillCheck() {
      const storeSelectedRowKeys = this.storeSelectedRowKeys
      let selectedRowArray = []
      let selectedRowKeys = []
      const tab = this.tab
      this.dataListNew.map((item) => {
        if (storeSelectedRowKeys.includes(item[tab.rowKey])) {
          item.checkbox = true
          selectedRowArray.push(item)
          selectedRowKeys.push(item[tab.rowKey])
        }
      }),
        (this.selectedRowArray = selectedRowArray)
      this.selectedRowKeys = selectedRowKeys
      this.onSelectChange(selectedRowKeys, selectedRowArray)
    },
    // 表格单行勾选
    customCellChange(e) {
      let selectedRowArray = []
      let selectedRowKeys = []
      const tab = this.tab
      this.dataListNew.map((item) => {
        if (item.checkbox == true) {
          selectedRowArray.push(item)
          selectedRowKeys.push(item[tab.rowKey])
        }
      })
      this.selectedRowArray = selectedRowArray
      this.selectedRowKeys = selectedRowKeys
      this.onSelectChange(selectedRowKeys, selectedRowArray)
    },
    onSelectChange(selectedRowKeys, selectedRowArray) {
      const rowKey = this.tab.rowKey
      if (this.selectedRowKeys.length > 0 && this.selectedRowKeys.length === this.dataList.length) {
        this.checkAllCheckbox = true
      } else {
        this.checkAllCheckbox = false
      }
      let dataList = JSON.parse(JSON.stringify(this.dataList))
      let storeSelectedRowKeys = JSON.parse(JSON.stringify(this.storeSelectedRowKeys))
      let storeSelectedRowArray = JSON.parse(JSON.stringify(this.storeSelectedRowArray))
      let newIdList = []
      newIdList = dataList.map((item) => {
        return item[rowKey]
      })
      // 先把勾选dis集合中去掉所有当前页的id数据，再添加当前页勾选的数据
      storeSelectedRowKeys =
        storeSelectedRowKeys.filter((f) => {
          return !newIdList.includes(f)
        }) || []
      storeSelectedRowKeys.push(...selectedRowKeys)

      storeSelectedRowArray =
        storeSelectedRowArray.filter((f) => {
          return !newIdList.includes(f[[rowKey]])
        }) || []
      storeSelectedRowArray.push(...selectedRowArray)

      this.storeSelectedRowKeys = Array.from(new Set(storeSelectedRowKeys))
      this.storeSelectedRowArray = Array.from(new Set(storeSelectedRowArray))
    },
    // 表格顶部批量勾选
    onAllcheckboxChange(e, record) {
      const tab = this.tab
      let status = e.target.checked
      // this.checkAllCheckbox = status
      let selectedRowArray = []
      let selectedRowKeys = []
      const ids = this.dataList.map((item) => {
        // 实际一页真实数据的ids集合
        return item[tab.rowKey]
      })
      this.dataListNew.map((item) => {
        const indexItem = ids.indexOf(item[tab.rowKey])
        if (indexItem !== -1) {
          // this.$set(item, 'checkbox', status)
          item.checkbox = status
          if (status) {
            selectedRowArray.push(item)
            selectedRowKeys.push(item[tab.rowKey])
          }
        }
      })
      this.selectedRowArray = selectedRowArray
      this.selectedRowKeys = selectedRowKeys
      this.onSelectChange(selectedRowKeys, selectedRowArray)
    },
    // 自定义分页数据变化
    onPageChange(current, pageSize) {
      this.ifSearchChange = false
      this.$emit('pageChange', { current, pageSize })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        if (this.sort) {
          // 订单列表 排序方式
          this.queryParam[this.sort] = 'ascend' == sorter.order ? '1' : '2'
        }
      }
      this.ipagination = pagination
      // this.loadData(null, (source) => {
      //   this.dataSource2 = source
      // })
    },
    tableWidth() {
      let w = 0
      this.columns.forEach((x) => {
        if (x.width >= 0) {
          w += x.width
        }
      })
      if (w == 0 || w < 1300) {
        w = '100%'
        if (w < 1000) {
          this.columns.forEach((item) => {
            if (item.dataIndex != 'action' && !item.fixed && item.width > 0) {
              delete item['width']
            }
          })
        }
      }
      return w
    },
    onClearSelected() {
      this.selectedRowKeys = []
      this.selectedRowArray = []
    },
    /**
     * 检查按钮是否有权限
     * @param {按钮id} id
     * @returns
     */
    checkBtnPermissions(id) {
      return this.$store.getters.checkButtonPermissions(id)
    },
    // 操作
    operate(record, key, index, btnInfo) {
      this.$emit('operate', record, key, index, btnInfo)
    },
  },
}
</script>

<style scoped lang="scss">
.green {
  font-size: 14px;
  color: green;
  background: transparent;
}
.red {
  font-size: 14px;
  color: red;
}
.top {
  padding-top: 4px;
}
::v-deep .ant-checkbox-wrapper {
  margin-right: 0 !important;
}
</style>
