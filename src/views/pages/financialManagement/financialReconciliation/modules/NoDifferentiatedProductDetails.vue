<template>
  <!-- 无差异商品明细 -->
  <div>
    <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
    <a-spin :spinning="confirmLoading">
      <div class="noBorder">
        <div class="flex">
          <div v-if="ReconciliationType === supplierReconciliationType">
            <span class="red"
              >供应商合计总金额：<span v-if="priceInfo.ThirdGoodsTotalAmount < 0">-</span>￥{{
                Math.abs(priceInfo.ThirdGoodsTotalAmount)
              }}</span
            >;供应商含税总金额：<span v-if="priceInfo.ThirdTotalAmount < 0">-</span>￥{{ Math.abs(priceInfo.ThirdTotalAmount) }};
            供应商修正总金额：<span v-if="priceInfo.ThirdAdjustedAmount < 0">-</span>¥{{
              Math.abs(priceInfo.ThirdAdjustedAmount)
            }}；采销系统总金额：<span v-if="priceInfo.ScmSystemTotalAmount < 0">-</span>￥{{
              Math.abs(priceInfo.ScmSystemTotalAmount)
            }}；
          </div>
          <div v-if="ReconciliationType === customerReconciliationType">
            <span class="red"
              >客户合计总金额：<span v-if="priceInfo.ThirdGoodsTotalAmount < 0">-</span>￥{{
                Math.abs(priceInfo.ThirdGoodsTotalAmount)
              }}</span
            >；客户含税总金额：<span v-if="priceInfo.ThirdTotalAmount < 0">-</span>￥{{ Math.abs(priceInfo.ThirdTotalAmount) }};
            客户修正总金额：<span v-if="priceInfo.ThirdAdjustedAmount < 0">-</span>￥{{
              Math.abs(priceInfo.ThirdAdjustedAmount)
            }}；采销系统总金额：<span v-if="priceInfo.ScmSystemTotalAmount < 0">-</span>￥{{
              Math.abs(priceInfo.ScmSystemTotalAmount)
            }}；
          </div>
          <YYLButton
            :menuId="menuId"
            text="批量取消价格修正"
            type="primary"
            @click="cancelBatchPriceCorrection"
            v-if="!ifAudit"
          />
        </div>
      </div>
         <ReconciliationTable
        ref="ReconciliationTable"
        :tab="{
          rowKey: 'PurchaseSalesStatementGoodsId',
          rowSelection: {
            type: 'checkbox',
          },
          scrollY: Math.ceil(this.$root.tableHeight - 30),
        }"
        :leftAndRightListKey="leftAndRightListKey"
        :actionKey="'action'"
        :dataList="dataListTest"
        :columns="columnsTest"
        :pageInfo="myPagination"
        @pageChange="ReconciliationTablePageChange"
        @operate="operate"
      />
    </a-spin>
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'NoDifferentiatedProductDetails',
  inject: [
    'ReconciliationType',
    'supplierReconciliationType',
    'customerReconciliationType',
    'ifAudit',
    'reconciliationId',
    'isExamine'
  ],
  mixins: [ListMixin],
  props: {
    thirdPartyName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      confirmLoading: false,
      httpHead: 'P36003',
      dataListTest: [],
      leftAndRightListKey: ['ThirdGoodsDetails', 'ScmBusinessDetails'],
      /* 分页参数 */
      myPagination: {
        current: 1,
        pageSize: 15,
        pageSizeOptions: ['15', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      httpHead: 'P36012',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/PurchaseSalesStatement/GetPurchaseSalesStatementNoDiscrepancyGoodsList', // 当前列表数据接口
        cancelUpdate: '/{v}/PurchaseSalesStatement/CancelCorrectPrice', // 取消价格修正接口
      },
      urlExamine: {
        list: '/{v}/PurchaseSalesStatementAudit/GetPurchaseSalesStatementNoDiscrepancyGoodsList', // 当前列表数据接口
      },
      priceInfo: {}, // 价格统计信息
    }
  },
  computed: {
    columnsTest() {
      let leftAndRightListKey = this.leftAndRightListKey
      let ifAudit = this.ifAudit
      let basicArry = [
        {
          title: this.thirdPartyName,
          children: [
            {
              title: '商品编号',
              dataIndex: 'ThirdGoodsCode',
              width: 150,
              ellipsis: true,
              align: 'center',
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '商品名称',
              dataIndex: 'ThirdGoodsName',
              width: 150,
              align: 'center',
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '规格',
              dataIndex: 'ThirdGoodsSpecification',
              width: 150,
              align: 'center',
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '生产厂家',
              dataIndex: 'ThirdGoodsManufacturer',
              width: 150,
              align: 'center',
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '单据日期',
              dataIndex: 'DocumentDate',
              width: 150,
              align: 'center',
              scopedSlots: { customRender: 'dateLeft' },
            },
            {
              title: '数量',
              dataIndex: 'Quantity',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '含税价',
              dataIndex: 'TaxIncludedPrice',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'priceLeft' },
            },
            {
              title: '含税金额',
              dataIndex: 'TaxIncludedAmount',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'priceLeft' },
            },
            {
              title: '修正后金额',
              dataIndex: 'FinalAmount',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'priceUpdate' },
            },
            {
              title: '合计金额',
              dataIndex: 'ThirdTotalAmount',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'price' },
            },
            {
              title: '操作',
              dataIndex: 'action1',
              align: 'center',
              width: 150,
              // fixed: 'right',
              actionBtns: [
                {
                  name: '取消价格修正',
                  icon: '',
                  id: this.cancelPriceUpdateMenuId,
                  isShow: (record) => {
                    return !record.whiteColumnLeft && record[`${leftAndRightListKey[0]}&IsAdjusted`]
                  },
                },
              ],
              scopedSlots: { customRender: 'action' },
            },
          ],
        },
        {
          title: '采销系统',
          children: [
            {
              title: '商品编号',
              dataIndex: 'ScmGoodsCode',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '商品名称',
              dataIndex: 'ScmGoodsName',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '规格',
              dataIndex: 'ScmGoodsSpecification',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '生产厂家',
              dataIndex: 'ScmGoodsManufacturer',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '品种责任人',
              dataIndex: 'GoodsOwnerBy',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '发生日期',
              dataIndex: 'OccurrenceDate',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '单据类型',
              dataIndex: 'BusinessEventTypeStr',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '单据编号',
              dataIndex: 'DocumentNo',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '所属订单',
              dataIndex: 'MainBusinessNo',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '单据责任人',
              dataIndex: 'OwnerByName',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '数量',
              dataIndex: 'Quantity',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '含税价',
              dataIndex: 'TaxIncludedPrice',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'priceRight' },
            },
            {
              title: '含税金额',
              dataIndex: 'TaxIncludedAmount',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'priceRight' },
            },
            {
              title: '合计金额',
              dataIndex: 'ScmTotalAmount',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'price' },
            },
            {
              title: '操作',
              dataIndex: 'action2',
              align: 'center',
              width: 100,
              // fixed: 'right',
              actionBtns: [
                {
                  name: '详情',
                  icon: '',
                  id: this.detailMenuId,
                  isShow: (record) => {
                    return !record.whiteColumnRight && true
                  },
                },
              ],
              scopedSlots: { customRender: 'action' },
            },
          ],
        },
      ]

      if (ifAudit) {
        basicArry[0].children = basicArry[0].children.filter((f) => f.title !== '操作')
      }

      return basicArry
    },
    // 批量取消价格修正按钮id
    menuId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return '4f800ac8-0316-4790-875d-65501e008907'
        case customerReconciliationType:
          return 'a61a8da3-be34-42f7-b171-37a1e985d070'
      }
    },
    // 取消价格修正按钮id
    cancelPriceUpdateMenuId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return '4d36777c-c7a3-447e-89ca-475237cd3c74'
        case customerReconciliationType:
          return 'ded89ab4-de2e-4148-ad57-f7616a34defa'
      }
    },
    // 详情按钮id
    detailMenuId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return '0188b02d-729d-482e-a844-c91e1a8740e4'
        case customerReconciliationType:
          return 'dc03bb8e-81c3-4819-9627-12c95714f5ec'
      }
    },
  },
  created() {
    if(this.isExamine) {  // 审核页面进入需要替换对应的接口
      this.url = Object.assign({}, this.url, this.urlExamine)
    }
  },
  mounted() {},
  activated() {},
  watch: {
    // 一些属性无法设置成计算属性，需要通过监听ReconciliationType变化而设置在data里的值
    ReconciliationType: {
      handler(val) {
        const supplierReconciliationType = this.supplierReconciliationType
        const customerReconciliationType = this.customerReconciliationType
        let searchItems = [
          {
            name: '商品',
            type: this.$SEnum.INPUT,
            key: 'GoodsCodeOrName',
            disabled: true,
            placeholder: '请输入商品名称/编号',
          },
          {
            name: '金额调整',
            type: this.$SEnum.SELECT,
            key: 'IsAdjusted',
            items: [
              {
                label: '有调整',
                value: true,
              },
              {
                label: '无调整',
                value: false,
              },
            ],
          },
          {
            name: '单据编号',
            type: this.$SEnum.INPUT,
            key: 'DocumentNo',
            disabled: true,
            placeholder: '请输入单据编号',
          },
          {
            name: '订单编号',
            type: this.$SEnum.INPUT,
            key: 'MainBusinessNo',
            disabled: true,
            placeholder: '请输入订单编号',
          },
          {
            name: '单据类型',
            type: this.$SEnum.SELECT,
            key: 'BusinessEventType',
            items: [
              {
                label: '采购入库',
                value: 2,
              },
              {
                label: '采购退货',
                value: 4,
              },
              {
                label: '采购调价',
                value: 8,
              },
            ],
          },
          {
            name: '单据日期',
            type: this.$SEnum.DATE,
            rangeDate: this.rangeDate,
            key: 'ThirdDocumentDateBegin',
            keyEnd: 'ThirdDocumentDateEnd',
            showTime: true,
            placeholder: ['开始日期', '结束日期'],
          },
          {
            name: '发生日期',
            type: this.$SEnum.DATE,
            rangeDate: this.rangeDate,
            key: 'ScmDocumentDateBegin',
            keyEnd: 'ScmDocumentDateEnd',
            showTime: true,
            placeholder: ['开始日期', '结束日期'],
          },
        ]
        switch (val) {
          case customerReconciliationType:
            const index = searchItems.findIndex((f) => f.name === '单据类型')
            const item = searchItems[index]
            searchItems.splice(index, 1, {
              ...item,
              items: [
                {
                  label: '商销出库 ',
                  value: 20,
                },
                {
                  label: '商销售后',
                  value: 22,
                },
                {
                  label: '商销调价',
                  value: 24,
                },
              ],
            })
            break
          default:
            break
        }
        this.searchItems = searchItems
      },
      immediate: true,
    },
  },
  methods: {
    loadBefore() {
      this.confirmLoading = true
      const isExamine = this.isExamine
      this.queryParam[isExamine ? 'purchaseSalesStatementRecordId' : 'purchaseSalesStatementId'] = this.reconciliationId
      this.priceInfo = {}
    },
    loadAfter(Data) {
      let list = []
      this.confirmLoading = false
      let total = this.ipagination.total
      list = Data.ItemList
      this.dataListTest = list
      this.myPagination.total = total
      const { ThirdGoodsTotalAmount, ThirdTotalAmount, ThirdAdjustedAmount, ScmSystemTotalAmount } = Data
      this.priceInfo = {
        ThirdGoodsTotalAmount,
        ThirdTotalAmount,
        ThirdAdjustedAmount,
        ScmSystemTotalAmount,
      }
      this.$emit('updateCount', 3, total)
    },
    searchQuery(val, other, type) {
      this.myPagination.current = 1
      this.loadData(1)
    },
    // 表格底部分页组件数据变化
    ReconciliationTablePageChange(page) {
      const { current, pageSize } = page
      const myPagination = this.myPagination
      this.myPagination = Object.assign({}, myPagination, page)
      // todo
      this.handleTableChange(this.myPagination, null, [])
    },
    // 操作
    operate(record, key, index) {
      const leftAndRightListKey = this.leftAndRightListKey
      console.log(record, key, index)
      if (key === '取消价格修正') {
        this.cancelPriceCorrection(
          [record[`${leftAndRightListKey[0]}&PurchaseSalesStatementGoodsThridBusinessDetailId`]],
          [record[`${leftAndRightListKey[0]}&PurchaseSalesStatementGoodsThridBusinessDetailId`]]
        )
      } else if (key === '详情') {
        this.goDetail(record)
      }
    },
    // 跳转详情页
    goDetail(record) {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      const leftAndRightListKey = this.leftAndRightListKey
      const BusinessEventType = record[`${leftAndRightListKey[1]}&BusinessEventType`]
      const DocumentId = record[`${leftAndRightListKey[1]}&DocumentId`]
      const MainBusinessId = record[`${leftAndRightListKey[1]}&MainBusinessId`]
      if (ReconciliationType === supplierReconciliationType) {
        switch (BusinessEventType) {
          case 2: // 采购入库
            this.goPage('/pages/purchasingManagement/PurchaseOrderDetail', {
              id: MainBusinessId,
              activeTabKey: 2, // 定位到入库tabs
            })
            break
          case 4: //  采购退货 
            this.goPage('/pages/purchasingManagement/PurchaReturnGoodsDetail', {
              id: DocumentId,
              type: 1,
            })
            break
          case 8: // 采购调价
            this.goPage('/pages/purchasingManagement/PurchaSepriceAdjustmentDetail', {
              id: DocumentId,
              type: 1,
            })
            break
          default:
            break
        }
      } else if (ReconciliationType === customerReconciliationType) {
        switch (BusinessEventType) {
          case 20: // 商销出库
            this.goPage('/pages/marketingManagement/ordersManagementListDetail', {
              id: MainBusinessId,
              key: 6, // 定位到出库tabs
            })
            break
          case 22: // 商销售后
            this.goPage('/pages/marketingManagement/merchantAfterSaleListDetail', {
              id: DocumentId
            })
            break
          case 24: // 商销调价
            this.goPage('/pages/marketingManagement/pricesAdjustedDetail', {
              id: DocumentId,
              orderId: MainBusinessId,
            })
            break
          default:
            break
        }
      }
    },
    // 取消批量修正价格按钮点击事件
    cancelBatchPriceCorrection() {
      const leftAndRightListKey = this.leftAndRightListKey
      const selectedRows = this.$refs.ReconciliationTable.storeSelectedRowArray
      const storeSelectedRowKeys = this.$refs.ReconciliationTable.storeSelectedRowKeys
      console.log(selectedRows)
      let ids = []
      if (selectedRows.length > 0) {
        selectedRows.map((item) => {
          if (item[`${leftAndRightListKey[0]}&ThirdGoodsDetails`].length > 0) {
            item[`${leftAndRightListKey[0]}&ThirdGoodsDetails`].map((son) => {
              if (son.IsAdjusted) {
                ids.push(son.PurchaseSalesStatementGoodsThridBusinessDetailId)
              }
            })
          }
        })
      }
      this.cancelPriceCorrection(storeSelectedRowKeys, ids)
    },
    cancelPriceCorrection(checkIds, ids) {
      let that = this
      if (checkIds.length < 1) {
        this.$message.warning('请先勾选需要操作的数据')
        return false
      }
      if (ids.length < 1) {
        this.$message.warning('已勾选项中暂时没有可修改的数据')
        return false
      }
      this.$confirm({
        title: '取消价格修正',
        content: '您确定要取消价格修正么?',
        onOk() {
          let url = that.url.cancelUpdate
          let formData = {
            PurchaseSalesStatementId: that.reconciliationId,
            DetailIds: ids
          }
          that.confirmLoading = true
          postAction(url, formData, that.httpHead)
            .then((res) => {
              that.confirmLoading = false
              if (res.IsSuccess) {
                that.$message.success('操作成功!')
                that.loadData(1)
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        },
        onCancel() {},
      })
    },
  },
}
</script>
<style scoped lang="scss">
.noBorder {
  & .flex {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    padding: 0 16px;
    padding-top: 6px;
    .red {
      color: red;
    }
  }
  .mr8 {
    margin-right: -2px;
  }
}
</style>
