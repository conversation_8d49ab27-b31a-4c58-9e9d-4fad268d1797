<template>
  <!-- 未匹配商品记录编辑弹窗 -->
  <a-modal
    :title="'编辑'"
    :width="800"
    :visible="visible"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <div
      :style="{
        width: '100%',
        padding: '10px 16px',
        background: '#fff',
      }"
    >
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model">
          <a-row :getter="10">
            <a-col :md="24">
              <a-card>
                <a-form-model-item label="往来单位" prop="CooperationCompanyName">
                  <a-input placeholder="请选择" disabled v-model="model.CooperationCompanyName" style="width: 100%" />
                </a-form-model-item>
              </a-card>
              <div class="myTitle">对方商品信息</div>
              <a-card>
                <a-form-model-item label="商品编号" prop="OtherPartyErpGoodsCode">
                  <a-input
                    placeholder="请输入"
                    :maxLength="100"
                    v-model="model.OtherPartyErpGoodsCode"
                    style="width: 100%"
                  />
                </a-form-model-item>
                <a-form-model-item label="商品名称" prop="OtherPartyErpGoodsName">
                  <a-input
                    placeholder="请输入"
                    :maxLength="100"
                    v-model="model.OtherPartyErpGoodsName"
                    style="width: 100%"
                  />
                </a-form-model-item>
                <a-form-model-item label="规格" prop="OtherPartyPackingSpecification">
                  <a-input
                    placeholder="请输入"
                    :maxLength="100"
                    v-model="model.OtherPartyPackingSpecification"
                    style="width: 100%"
                  />
                </a-form-model-item>
                <a-form-model-item label="生产厂家" prop="OtherPartyBrandManufacturer">
                  <a-input
                    placeholder="请输入"
                    :maxLength="100"
                    v-model="model.OtherPartyBrandManufacturer"
                    style="width: 100%"
                  />
                </a-form-model-item>
              </a-card>
              <div class="myTitle">我方商品信息</div>
              <a-card>
                <a-form-model-item label="商品信息" prop="GoodsSpuId">
                  <div class="flex">
                    <a-input placeholder="请选择商品信息" disabled v-model="model.ErpGoodsName" style="width: 100%" />
                    <a-button type="link" @click="chooseGoods" style="padding-right: 0"> 选择 </a-button>
                  </div>
                </a-form-model-item>
              </a-card>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel"> 取消</a-button>
      <a-button :style="{ marginRight: '8px' }" @click="handleOk" type="primary" :loading="confirmLoading">确定</a-button>
    </a-row>
    <!-- 表格选择商品数据弹窗 -->
    <TableSelectDataModal
      ref="TableSelectDataModal"
      :type="'radio'"
      :selectTableData="selectTableData"
      showSelectedData
      @chooseData="chooseData"
    ></TableSelectDataModal>
  </a-modal>
</template>

<script>
import { getAction, postAction, putAction } from '@/api/manage'
export default {
  title: '编辑',
  name: 'UnmatchedRecordAddModal',
  inject: ['ifAudit', 'reconciliationId'],
  components: {},
  data() {
    return {
      visible: false,
      confirmLoading: false,
      PurchaseSalesStatementId: null,
      storedataSource: [],
      httpHead: 'P36012',
      url: {
        // listType: 'GET', //列表接口请求类型
        info: '/{v}/PurchaseSalesStatement/GetUnmatchedGoodsDetails', // 获取商品信息
        save: '/{v}/CompanyGoodsMapping/PurchaseSalesStatementUnbindSave' // 往来单位对照商保存
      },
      model: {},
      // 选择商品 配置
      selectTableData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          {
            name: '商品名称',
            type: 'input',
            value: '',
            key: 'KeyWord',
            defaultVal: '',
            placeholder: '请输入商品名称/编号',
          },
          {
            name: '生产厂家',
            type: 'input',
            value: '',
            key: 'ManufacturerName',
            defaultVal: '',
            placeholder: '请输入厂家名称',
          },
        ],
        title: '选择商品',
        name: '商品',
        recordKey: 'GoodsSpuId',
        httpHead: 'P36012',
        isInitData: false,
        url: {
          list: '/{v}/CompanyGoodsMapping/GetPurchaseSaleGoodsList',
          listType: 'GET',
        },
        columns: [
          {
            title: '商品名称',
            dataIndex: 'ErpGoodsName',
            width: 180,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '商品编号',
            dataIndex: 'ErpGoodsCode',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '规格',
            dataIndex: 'PackingSpecification',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '生产厂商',
            dataIndex: 'BrandManufacturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '采购人员',
            dataIndex: 'GoodsPurchaser',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
    }
  },
  computed: {
    rules() {
      return {
        CooperationCompanyName: [{ required: true, message: '请输入来往单位', trigger: 'blur' }],
        OtherPartyErpGoodsName: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
        OtherPartyPackingSpecification: [{ required: true, message: '请输入规格', trigger: 'blur' }],
        OtherPartyBrandManufacturer: [{ required: true, message: '请输入生产厂家', trigger: 'blur' }],
        GoodsSpuId: [{ required: true, message: '请选择商品信息', trigger: 'change' }],
      }
    },
  },
  created() {
  },

  methods: {
    loadBefore() {},
    loadAfter() {},
    // searchQuery(param) {
    //   this.loadData()
    // },
    show(record) {
      this.visible = true
      const { Id } = record
      this.getInfo(Id)
    },
    // 获取商品详情
    getInfo(Id) {
      const that = this
      let url = that.url.info
      let formData = {
        id: Id,
      }
      getAction(url, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.model = res.Data
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {})
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let url = that.url.save
          let formData = {
            ...this.model,
            PurchaseSalesStatementId: this.reconciliationId
          }
          postAction(url, formData, that.httpHead)
            .then((res) => {
              that.confirmLoading = false
              if (res.IsSuccess) {
                this.$message.success('操作成功!')
                this.close()
                this.$emit('confirmEdit')
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
      this.$emit('close')
      this.storedataSource = []
      this.model = {}
    },
    // 选择商品弹出
    chooseGoods() {
      let queryParam = {
        ...this.model
        // IsAll: true,
      }
      this.$refs.TableSelectDataModal.show(this.storedataSource, queryParam)
    },
    // 选择商品选择数据回调
    chooseData(data, type) {
      if (data && data.length > 0) {
        if (type == 'radio') {
          this.storedataSource = data
        } else {
          this.storedataSource = this.storedataSource.concat(data)
        }
        // console.log('选择商品选择数据回调  ',this.storedataSource)
        this.$set(this.model, 'GoodsSpuId', data[0].GoodsSpuId)
        this.$set(this.model, 'ErpGoodsName', `${data[0].ErpGoodsCode}/${data[0].ErpGoodsName}/${data[0].PackingSpecification}/${data[0].BrandManufacturer}`)
        this.$refs.form.validateField('GoodsSpuId')
      }
    },
  },
}
</script>

<style scoped lang="scss">
.myTitle {
  font-size: 14px;
  font-weight: bold;
  color: black;
  padding: 8px 10px;
  position: relative;
  &::before {
    content: '';
    display: block;
    width: 4px;
    height: 16px;
    background: #666666;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
