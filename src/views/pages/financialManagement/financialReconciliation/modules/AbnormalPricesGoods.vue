<template>
  <!-- 价格异常商品 -->
  <div>
    <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
    <a-spin :spinning="confirmLoading">
      <div class="noBorder">
        <div class="flex">
          <div class="warp">
            {{ thirdPartyName }}合计金额：<span v-if="priceInfo.ThirdGoodsTotalAmount < 0">-</span>￥{{
              Math.abs(priceInfo.ThirdGoodsTotalAmount)
            }}; 采销系统合计金额：<span v-if="priceInfo.ScmSystemTotalAmount < 0">-</span>￥{{
              Math.abs(priceInfo.ScmSystemTotalAmount)
            }}
          </div>
          <template v-if="!ifAudit">
            <div v-if="ReconciliationType === supplierReconciliationType">
              <YYLButton
                :menuId="'749b8c72-76c2-439d-95a0-6c4bd134f3e5'"
                text="插入跨月明细"
                type="primary"
                @click="insertGoods"
              />
              <YYLButton
                :menuId="'f3dbbcd0-079a-4990-934c-68abdb3b0a75'"
                text="批量修正价格"
                @click="batchPriceCorrection"
                type="primary"
              />
              <YYLButton
                :menuId="'b1c548a3-bdfe-4630-b035-6ca49c4ef73e'"
                @click="exportNow"
                text="导出"
                type="primary"
              />
              <YYLButton
                :menuId="'d44ba413-36b5-4e91-8c01-4da5449fe61d'"
                text="导入修正数据"
                type="primary"
                @click="importUpdate"
              />
            </div>
            <div v-if="ReconciliationType === customerReconciliationType">
              <YYLButton
                :menuId="'d8354a77-ab32-47b9-8d3a-4eb5e95b9843'"
                text="插入跨月明细"
                type="primary"
                @click="insertGoods"
              />
              <YYLButton
                :menuId="'cc610628-8148-4fa3-9fe8-4af30b680bfe'"
                text="批量修正价格"
                @click="batchPriceCorrection"
                type="primary"
              />
              <YYLButton
                :menuId="'521b5e13-b3a5-4d5f-ab9e-1062b6b92c32'"
                @click="exportNow"
                text="导出"
                type="primary"
              />
              <YYLButton
                :menuId="'dfb82b34-e7a3-42bf-a3c1-c4374470659e'"
                text="导入修正数据"
                type="primary"
                @click="importUpdate"
              />
            </div>
          </template>
        </div>
      </div>
         <ReconciliationTable
        ref="ReconciliationTable"
        :tab="{
          rowKey: 'PurchaseSalesStatementGoodsId',
          rowSelection: {
            type: 'checkbox',
          },
          scrollY: Math.ceil(this.$root.tableHeight - 30),
        }"
        :leftAndRightListKey="leftAndRightListKey"
        :actionKey="'action'"
        :dataList="dataListTest"
        :columns="columnsTest"
        :pageInfo="myPagination"
        @pageChange="ReconciliationTablePageChange"
        @operate="operate"
      />
    </a-spin>
    <!-- 批量导入 导入价格异常修正记录 -->
    <BatchImportSimpleModal
      ref="iBatchImportAbnormalModal"
      modalTitle="导入价格异常修正记录"
      :tableColumns="abnormalImportColumns"
      :searchParamsList="abnormalImportRecordsInput"
      :importConfig="abnormalImportConfig"
      :importUrl="`/{v}/PurchaseSalesStatement/ImportPurchaseSalesStatementAbnormalGoodsCorrectImportTemp?purchaseSalesStatementId=${this.reconciliationId}`"
      importHttpHead="P36012"
      @ok="(e) => handleBatchImportModalOk(e, 'goods')"
      @importLoadAfter="importLoadAfter"
    >
      <div slot="bottomView">
        <div class="importTotal">合计金额：￥{{ importAmount }}</div>
      </div>
    </BatchImportSimpleModal>
    <!-- 插入跨月商品明细 -->
    <InsertCrossMonthDetails ref="InsertCrossMonthDetails"       
    v-if="showInsertGoods"
    @close="showInsertGoods = false"
     @confirmInsert="confirmInsert" />
    <!-- 价格修正弹窗 -->
    <PriceAnomalyCorrectionModal
      ref="PriceAnomalyCorrectionModal"
      @batchPriceCorrectionConfirm="batchPriceCorrectionConfirm"
    />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, postAction, deleteAction } from '@/api/manage'
export default {
  name: 'AbnormalPricesGoods',
  inject: [
    'ReconciliationType',
    'supplierReconciliationType',
    'customerReconciliationType',
    'ifAudit',
    'reconciliationId',
    'isExamine',
  ],
  mixins: [ListMixin],
  props: {
    thirdPartyName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      confirmLoading: false,
      showInsertGoods: false, // 插入跨月商品明细弹窗是否显示
      importAmount: 0, // 导入商品或者导入票折后的总金额
      importSearchStatus: 1,
      dataListTest: [],
      leftAndRightListKey: ['ThirdGoodsDetails', 'ScmBusinessDetails'],
      /* 分页参数 */
      myPagination: {
        current: 1,
        pageSize: 15,
        pageSizeOptions: ['15', '30'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      httpHead: 'P36012',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/PurchaseSalesStatement/GetPurchaseSalesStatementPriceAbnormalGoodsList', // 当前列表数据接口
        // cancelUpdate: '/{v}/PurchaseSalesStatement/CancelCorrectPrice', // 取消价格修正接口
        remove1: '/{v}/PurchaseSalesStatement/DeletePurchaseSalesStatementGoodsThridBusinessDetails', // 第三方移除
        remove2: '/{v}/PurchaseSalesStatement/DeletePurchaseSalesStatementGoodsScmBusinessDetails', // 采销系统移除
        exportUrl: '/{v}/PurchaseSalesStatement/ExportPriceAbnormalGoods', // 导出价格异常商品记录
        updateUrl: '/{v}/PurchaseSalesStatement/UpdateFinalAmount', // 批量修正价格
      },
      urlExamine: {
        list: '/{v}/PurchaseSalesStatementAudit/GetPurchaseSalesStatementPriceAbnormalGoodsList', // 当前列表数据接口
      },
      priceInfo: {}, // 价格信息
      // 导入价格异常修正记录组件参数设置 --------------------------------
      // 导入的参数
      abnormalImportRecordsInput: [
        {
          name: '商品', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'KeyWord', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '生产厂家', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'Manufacturer', //搜索key 必填
          placeholder: '请输入',
        },
      ],
      abnormalImportConfig: {
        importTitle: '商品',
        templateFileName: '价格异常修正导入模板', //下载模板名字
        ErrorShowKey: 'ErrorInfo', //异常信息字段名称
        saveKey: 'BatchId',
        saveUrlType: 'GET', //保存接口请求方式
        saveUrl: '/{v}/PurchaseSalesStatement/SubmitPurchaseSalesStatementAbnormalGoodsCorrectImportTemp', //保存接口
        listQueryKey: 'BatchId', //列表的时候需要用到的字段
        batchRemoveId: '', //批量移除id
        listRemoveType: 'DELETE', //列表删除接口请求方式
        listRemoveUrl: '/{v}/PurchaseSalesStatement/DeletePurchaseSalesStatementAbnormalGoodsCorrectImportTemp', //列表删除 接口
        listRemoveKey: '', //列表删除 参数 key
        listHttpHead: 'P36012',
        listUrl: '/{v}/PurchaseSalesStatement/GetListPurchaseSalesStatementAbnormalGoodsCorrectImportTemp', //列表的请求接口
        listUrlType: 'GET', //列表接口请求方式
        queryParamStatusKey: 'Status', //列表查询 异常 正常 key
        noLoadData: true, //是否默认弹出时候不加载数据
        importResKey: 'BatchId', //导出完成后返回的key 列表的时候用到
        dataSourceListKey: 'ImportDetailList',
        clearUrlType: 'DELETE',
        clearUrl: '/{v}/PurchaseSalesStatement/ClearPurchaseSalesStatementAbnormalGoodsCorrectImportTemp',
        clearSaveKey: 'BatchId',
        haveDataSourceKey: 'Id',
        onlySaveOneData: null,
        bottomBtn: [
          { name: '取消', type: '', funType: 'handleCancel' },
          { name: '模板下载', type: 'primary', funType: 'downloadTempFile' },
          { name: '确认', type: 'primary', funType: 'handleOk' },
        ],
      },
    }
  },
  computed: {
    abnormalImportColumns() {
      const importSearchStatus = this.importSearchStatus
      let basicImportColumns = [
        {
          title: '修正后含税金额',
          dataIndex: 'FinalAmount',
          width: 150,
          ellipsis: true,
          bgColor: '#ffff00',
          type: importSearchStatus === 1 ? 'price' : null,
          scopedSlots: { customRender: 'bgComponent' },
        },
        {
          title: '修正原因',
          dataIndex: 'AdjustedRemark',
          width: 150,
          ellipsis: true,
          bgColor: '#ffff00',
          scopedSlots: { customRender: 'bgComponent' },
        },
        {
          title: '单据日期',
          ellipsis: true,
          width: 150,
          dataIndex: 'DocumentDate',
          scopedSlots: { customRender: 'date' },
        },
        {
          title: '商品编号',
          ellipsis: true,
          width: 150,
          dataIndex: 'OtherPartyErpGoodsCode',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          ellipsis: true,
          width: 150,
          dataIndex: 'OtherPartyErpGoodsName',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          width: 150,
          dataIndex: 'OtherPartyPackingSpecification',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'OtherPartyBrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '数量',
          dataIndex: 'Quantity',
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '含税价',
          width: 200,
          dataIndex: 'TaxIncludedPrice',
          scopedSlots: { customRender: importSearchStatus === 1 ? 'price' : 'component' },
        },
        {
          title: '含税金额',
          width: 200,
          dataIndex: 'TaxIncludedAmount',
          scopedSlots: { customRender: importSearchStatus === 1 ? 'price' : 'component' },
        },
        {
          title: '异常原因',
          dataIndex: 'ErrorInfo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtns: [{ name: '移除', icon: '' }],
          scopedSlots: { customRender: 'action' },
        },
      ]
      return basicImportColumns
    },
    columnsTest() {
      let ifAudit = this.ifAudit
      let basicArry = [
        {
          title: this.thirdPartyName,
          children: [
            {
              title: '商品编号',
              dataIndex: 'ThirdGoodsCode',
              width: 150,
              ellipsis: true,
              align: 'center',
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '商品名称',
              dataIndex: 'ThirdGoodsName',
              width: 150,
              align: 'center',
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '规格',
              dataIndex: 'ThirdGoodsSpecification',
              width: 150,
              align: 'center',
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '生产厂家',
              dataIndex: 'ThirdGoodsManufacturer',
              width: 150,
              align: 'center',
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '单据日期',
              dataIndex: 'DocumentDate',
              width: 150,
              align: 'center',
              scopedSlots: { customRender: 'dateLeft' },
            },
            {
              title: '数量',
              dataIndex: 'Quantity',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '含税价',
              dataIndex: 'TaxIncludedPrice',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'priceLeft' },
            },
            {
              title: '含税金额',
              dataIndex: 'TaxIncludedAmount',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'priceLeft' },
            },
            {
              title: '合计金额',
              dataIndex: 'ThirdTotalAmount',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'price' },
            },
            {
              title: '操作',
              dataIndex: 'action1',
              align: 'center',
              width: 150,
              // fixed: 'right',
              actionBtns: [
                {
                  name: '移除',
                  icon: '',
                  type: 1,
                  id: this.removeMenuId,
                  isShow: (record) => {
                    return !record.whiteColumnLeft
                  },
                },
                {
                  name: '修正价格',
                  icon: '',
                  id: this.UpdateMenuId,
                  isShow: (record) => {
                    return !record.whiteColumnLeft
                  },
                },
              ],
              scopedSlots: { customRender: 'action' },
            },
          ],
        },
        {
          title: '采销系统',
          children: [
            {
              title: '商品编号',
              dataIndex: 'ScmGoodsCode',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '商品名称',
              dataIndex: 'ScmGoodsName',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '规格',
              dataIndex: 'ScmGoodsSpecification',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '生产厂家',
              dataIndex: 'ScmGoodsManufacturer',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '品种责任人',
              dataIndex: 'GoodsOwnerBy',
              align: 'center',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '发生日期',
              dataIndex: 'OccurrenceDate',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '单据类型',
              dataIndex: 'BusinessEventTypeStr',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '单据编号',
              dataIndex: 'DocumentNo',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '所属订单',
              dataIndex: 'MainBusinessNo',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '单据责任人',
              dataIndex: 'OwnerByName',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '数量',
              dataIndex: 'Quantity',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '含税价',
              dataIndex: 'TaxIncludedPrice',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'priceRight' },
            },
            {
              title: '含税金额',
              dataIndex: 'TaxIncludedAmount',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'priceRight' },
            },
            {
              title: '合计金额',
              dataIndex: 'ScmTotalAmount',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'priceRight' },
            },
            {
              title: '价格变化',
              dataIndex: 'PriceDifference',
              align: 'center',
              width: 150,
              scopedSlots: { customRender: 'priceChangeShow' },
            },
            {
              title: '操作',
              dataIndex: 'action2',
              align: 'center',
              width: 150,
              // fixed: 'right',
              actionBtns: [
                {
                  name: '移除',
                  icon: '',
                  type: 2,
                  id: this.removeMenuId,
                  isShow: (record) => {
                    return !record.whiteColumnRight
                  },
                },
                {
                  name: '详情',
                  icon: '',
                  isShow: (record) => {
                    return !record.whiteColumnRight
                  },
                },
              ],
              scopedSlots: { customRender: 'action' },
            },
          ],
        },
      ]

      if (ifAudit) {
        basicArry.map((item) => {
          item.children = item.children.filter((f) => f.title !== '操作')
        })
      }

      return basicArry
    },
    // 移除按钮id
    removeMenuId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return '33807691-7b3f-4510-b640-906d32c8c927'
        case customerReconciliationType:
          return 'c869c183-a96c-46e0-83a5-b726c62016ed'
      }
    },
    // 单行修正价格按钮id
    UpdateMenuId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return 'b5a1415c-bcc4-4106-adb4-8aa7072eb9ca'
        case customerReconciliationType:
          return '3cbd2b79-d2cc-47a3-80c6-29c0f8e4d4cb'
      }
    },
  },
  watch: {
    // 一些属性无法设置成计算属性，需要通过监听ReconciliationType变化而设置在data里的值
    ReconciliationType: {
      handler(val) {
        const supplierReconciliationType = this.supplierReconciliationType
        const customerReconciliationType = this.customerReconciliationType
        let searchItems = [
          {
            name: '商品',
            type: this.$SEnum.INPUT,
            key: 'GoodsCodeOrName',
            disabled: true,
            placeholder: '请输入商品名称/编号',
          },
          {
            name: '价格变化',
            type: this.$SEnum.SELECT,
            key: 'PriceChangeType',
            items: [
              {
                label: '上涨',
                value: 1,
              },
              {
                label: '下降',
                value: 2,
              },
            ],
          },
          {
            name: '单据编号',
            type: this.$SEnum.INPUT,
            key: 'DocumentNo',
            disabled: true,
            placeholder: '请输入单据编号',
          },
          {
            name: '订单编号',
            type: this.$SEnum.INPUT,
            key: 'MainBusinessNo',
            disabled: true,
            placeholder: '请输入订单编号',
          },
          {
            name: '单据类型',
            type: this.$SEnum.SELECT,
            key: 'BusinessEventType',
            items: [
              {
                label: '采购入库',
                value: 2,
              },
              {
                label: '采购退货',
                value: 4,
              },
              {
                label: '采购调价',
                value: 8,
              },
            ],
          },
          {
            name: '单据日期',
            type: this.$SEnum.DATE,
            rangeDate: this.rangeDate,
            key: 'ThirdDocumentDateBegin',
            keyEnd: 'ThirdDocumentDateEnd',
            showTime: true,
            placeholder: ['开始日期', '结束日期'],
          },
          {
            name: '发生日期',
            type: this.$SEnum.DATE,
            rangeDate: this.rangeDate,
            key: 'ScmDocumentDateBegin',
            keyEnd: 'ScmDocumentDateEnd',
            showTime: true,
            placeholder: ['开始日期', '结束日期'],
          },
        ]
        switch (val) {
          case customerReconciliationType:
            const index = searchItems.findIndex((f) => f.name === '单据类型')
            const item = searchItems[index]
            searchItems.splice(index, 1, {
              ...item,
              items: [
                {
                  label: '商销出库 ',
                  value: 20,
                },
                {
                  label: '商销售后',
                  value: 22,
                },
                {
                  label: '商销调价',
                  value: 24,
                },
              ],
            })
            break
          default:
            break
        }
        this.searchItems = searchItems
      },
      immediate: true,
    },
  },
  created() {
    if (this.isExamine) {
      // 审核页面进入需要替换对应的接口
      this.url = Object.assign({}, this.url, this.urlExamine)
    }
  },
  mounted() {},
  activated() {},

  methods: {
    loadBefore() {
      this.confirmLoading = true
      const isExamine = this.isExamine
      this.queryParam[isExamine ? 'purchaseSalesStatementRecordId' : 'purchaseSalesStatementId'] = this.reconciliationId
      this.priceInfo = {}
    },
    loadAfter(Data) {
      let list = []
      this.confirmLoading = false
      let total = this.ipagination.total
      list = Data.ItemList
      this.dataListTest = list
      this.myPagination.total = total
      const { ThirdGoodsTotalAmount, ScmSystemTotalAmount } = Data
      this.priceInfo = {
        ThirdGoodsTotalAmount,
        ScmSystemTotalAmount,
      }
      this.$emit('updateCount', 5, total)
    },
    // 导入后列表查询回调
    importLoadAfter(Data) {
      this.importAmount = Data.TotalAmount ? Data.TotalAmount.toFixed(6) : 0
      this.importSearchStatus = this.$refs.iBatchImportAbnormalModal.table.curStatus
    },
    // 插入跨月商品按钮点击事件
    insertGoods() {
      this.showInsertGoods = true
    },
    searchQuery(val, other, type) {
      this.myPagination.current = 1
      this.loadData(1)
    },
    // 表格底部分页组件数据变化
    ReconciliationTablePageChange(page) {
      const { current, pageSize } = page
      const myPagination = this.myPagination
      console.log(current)
      console.log(pageSize)
      this.myPagination = Object.assign({}, myPagination, page)
      // todo
      this.handleTableChange(this.myPagination, null, [])
    },
    // 导入修正数据
    importUpdate() {
      let queryParam = {
        // purchaseSalesStatementId: this.reconciliationId,
      }
      this.$refs.iBatchImportAbnormalModal.show(this.dataListTest, queryParam)
    },
    // 操作
    operate(record, key, index, btnInfo) {
      console.log(record, key, index, btnInfo)
      const leftAndRightListKey = this.leftAndRightListKey
      const type = btnInfo.type
      if (key == '修正价格') {
        this.priceCorrection([record])
      } else if (key == '移除') {
        if (type == 1) {
          // 第三方移除
          this.batchRemove([record[`${leftAndRightListKey[0]}&PurchaseSalesStatementGoodsThridBusinessDetailId`]], type)
        } else if (type == 2) {
          this.batchRemove([record[`${leftAndRightListKey[1]}&PurchaseSalesStatementGoodsScmBusinessDetailId`]], type)
          // 采销系统移除
        }
      } else if (key == '详情') {
        this.goDetail(record)
      }
    },
    // 跳转详情页
    goDetail(record) {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      const leftAndRightListKey = this.leftAndRightListKey
      const BusinessEventType = record[`${leftAndRightListKey[1]}&BusinessEventType`]
      const DocumentId = record[`${leftAndRightListKey[1]}&DocumentId`]
      const MainBusinessId = record[`${leftAndRightListKey[1]}&MainBusinessId`]
      if (ReconciliationType === supplierReconciliationType) {
        switch (BusinessEventType) {
          case 2: // 采购入库
            this.goPage('/pages/purchasingManagement/PurchaseOrderDetail', {
              id: MainBusinessId,
              activeTabKey: 2, // 定位到入库tabs
            })
            break
          case 4: //  采购退货
            this.goPage('/pages/purchasingManagement/PurchaReturnGoodsDetail', {
              id: DocumentId,
              type: 1,
            })
            break
          case 8: // 采购调价
            this.goPage('/pages/purchasingManagement/PurchaSepriceAdjustmentDetail', {
              id: DocumentId,
              type: 1,
            })
            break
          default:
            break
        }
      } else if (ReconciliationType === customerReconciliationType) {
        switch (BusinessEventType) {
          case 20: // 商销出库
            this.goPage('/pages/marketingManagement/ordersManagementListDetail', {
              id: MainBusinessId,
              key: 6, // 定位到出库tabs
            })
            break
          case 22: // 商销售后
            this.goPage('/pages/marketingManagement/merchantAfterSaleListDetail', {
              id: DocumentId,
            })
            break
          case 24: // 商销调价
            this.goPage('/pages/marketingManagement/pricesAdjustedDetail', {
              id: DocumentId,
              orderId: MainBusinessId,
            })
            break
          default:
            break
        }
      }
    },
    // 批量移除
    batchRemove(ids, type) {
      let that = this
      this.$confirm({
        title: '提示',
        content: '您确定要移除该数据么?',
        onOk() {
          let url = that.url[`remove${type}`]
          let formData = ids
          that.confirmLoading = true
          deleteAction(url, formData, that.httpHead)
            .then((res) => {
              that.confirmLoading = false
              if (res.IsSuccess) {
                that.$message.success('操作成功!')
                that.loadData(1)
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        },
        onCancel() {},
      })
    },
    // 批量修正价格按钮点击事件
    batchPriceCorrection() {
      const leftAndRightListKey = this.leftAndRightListKey
      const selectedRows = this.$refs.ReconciliationTable.storeSelectedRowArray
      let list = []
      if (selectedRows.length > 0) {
        selectedRows.map((item) => {
          if (item[`${leftAndRightListKey[0]}&ThirdGoodsDetails`].length > 0) {
            item[`${leftAndRightListKey[0]}&ThirdGoodsDetails`].map((son) => {
              list.push({
                detailRightValue: son,
                ...item,
              })
            })
          }
        })
      }
      this.priceCorrection(list)
    },
    priceCorrection(list) {
      let leftAndRightListKey = [this.leftAndRightListKey[0]] // 只把左边详情里数据拆分出来
      let newList = list.map((item) => {
        const detailRightValue = item.detailRightValue
        let obj = {}
        let keyList = Object.entries(item)
        keyList.map((newItem) => {
          let flage = false
          leftAndRightListKey.forEach((key) => {
            let startName = `${key}&`
            if (newItem[0] && newItem[0].indexOf(startName) !== -1) {
              flage = true
              let newKey = newItem[0].split('&')[1]
              obj[newKey] = newItem[1]
            }
          })
          if (!flage) {
            obj[newItem[0]] = newItem[1]
          }
        })
        if(detailRightValue){ 
          obj = {
            ...obj,
            ...detailRightValue
          }
        }
        return obj
      })
      console.log(newList)
      if (newList.length < 1) {
        this.$message.warning('请先勾选需要操作的数据')
        return false
      }
      this.$refs.PriceAnomalyCorrectionModal.show(newList)
    },
    // 批量修正价格成功回调
    batchPriceCorrectionConfirm() {
      this.loadData(1)
    },
    // 导出按钮点击事件
    exportNow() {
      const param = this.getQueryParams() //查询条件
      console.log(param)
      this.handleExportXls('价格异常商品记录表', 'Get', this.url.exportUrl, null, this.httpHead, param, '.xlsx')
    },
    // 确认插入
    confirmInsert() {
      this.loadData(1)
    },
    // 导入修正数据回调
    handleBatchImportModalOk(data, type) {
      this.loadData(1)
    },
  },
}
</script>
<style scoped lang="scss">
.noBorder {
  & .flex {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    padding: 0 16px;
    padding-top: 6px;
    .warp{
      flex: 1;
      flex-shrink: 1;
    }
    span {
      // color: red;
    }
  }
  .mr8:last-child {
    margin-right: -2px;
  }
}
.importTotal {
  font-size: 16px;
}
</style>
