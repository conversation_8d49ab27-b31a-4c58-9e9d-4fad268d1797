<template>
  <!-- 应付/收账单信息 -->
  <div>
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="formRef" :model="form">
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="对账单位" prop="CooperationCompanyName">
          <a-input disabled placeholder="" v-model="form.CooperationCompanyName" style="width: 100%" />
        </a-form-model-item>
        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="账期" prop="rangeDate">
          <a-range-picker
            disabled
            style="width: 100%"
            v-model="form.rangeDate"
            :placeholder="['开始时间', '结束时间']"
          />
        </a-form-model-item>
        <a-form-model-item
          v-if="ReconciliationType === supplierReconciliationType"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="委托人"
          prop="AgentName"
        >
          <a-input disabled style="width: 100%" placeholder="" v-model="form.AgentName" />
        </a-form-model-item>
        <a-form-model-item v-if="ReconciliationType === supplierReconciliationType" :labelCol="labelCol" :wrapperCol="wrapperCol" label="实际应付金额" prop="TotalAmount">
        <a-input disabled style="width: 100%" placeholder="" v-model="form.TotalAmount" />
      </a-form-model-item>
      <a-form-model-item v-if="ReconciliationType === customerReconciliationType" :labelCol="labelCol" :wrapperCol="wrapperCol" label="实际应收金额" prop="TotalAmount">
        <a-input disabled style="width: 100%" placeholder="" v-model="form.TotalAmount" />
      </a-form-model-item>
      </a-form-model>
    </a-spin>
  </div>
</template>
<script>
export default {
  name: 'AccountsReceivableInformation',
  inject: ['ReconciliationType', 'supplierReconciliationType', 'customerReconciliationType'],
  props: {
    form: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      confirmLoading: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 2 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
    }
  },
  computed: {},
  created() {},
  mounted() {},
  activated() {},

  methods: {},
}
</script>
<style scoped lang="scss"></style>
