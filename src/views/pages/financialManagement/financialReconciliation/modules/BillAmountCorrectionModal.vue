<!-- 账单金额修正弹窗 -->
<template>
  <a-modal
    :title="title"
    :visible="visible"
    :width="600"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <a-form-model ref="form" :rules="rules" :model="model">
      <a-form-model-item
        label="修正金额"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        prop="StatementAdjustmentAmount"
      >
        <a-input-number
          style="width: 100%"
          v-model="model.StatementAdjustmentAmount"
          placeholder="请输入"
          :maxLength="20"
          step="1"
          :precision="2"
        />
      </a-form-model-item>
      <a-form-model-item label="修正原因" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="CorrectReason">
        <a-textarea
          placeholder="200字符"
          :maxLength="200"
          v-model.trim="model.CorrectReason"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-form-model-item>
    </a-form-model>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button :style="{ marginRight: '8px' }" :loading="confirmLoading" @click="handleOk" type="primary"
        >保存</a-button
      >
    </a-row>
  </a-modal>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { postAction } from '@/api/manage'
export default {
  name: 'BillAmountCorrectionModal',
  inject: ['reconciliationId'],
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      title: '账单金额修正',
      visible: false,
      model: {},
      confirmLoading: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      httpHead: 'P36012',
      url: {
        update: '/{v}/PurchaseSalesStatement/PurchaseSalesStatementAdjustmentAmount',
      },
    }
  },
  computed: {
    rules() {
      return {
        StatementAdjustmentAmount: [{ required: true, message: '请输入修正金额', trigger: 'blur' }],
        CorrectReason: [{ required: true, message: '请输入修正原因', trigger: 'blur' }],
      }
    },
  },
  mounted() {},
  created() {},
  methods: {
    show(record) {
      const { StatementAdjustmentAmount, CorrectReason } = record
      this.visible = true
      this.model = Object.assign({}, this.model, {
        StatementAdjustmentAmount,
        CorrectReason
      })
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let url = that.url.update
          let formData = {
            ...this.model,
            PurchaseSalesStatementId: this.reconciliationId,
          }
          postAction(url, formData, that.httpHead)
            .then((res) => {
              that.confirmLoading = false
              if (res.IsSuccess) {
                this.$message.success('操作成功!')
                this.close()
                this.$emit('billCorrectionAmount')
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
      this.model = {}
    },
  },
}
</script>

<style scoped lang="scss"></style>
