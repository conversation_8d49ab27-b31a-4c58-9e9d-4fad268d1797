<template>
  <!-- 票折价格修正弹窗 -->
  <a-modal
    :title="'价格异常修正'"
    :width="'80vw'"
    :visible="visible"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <!--模糊匹配弹窗 -->
    <div
      :style="{
        width: '100%',
        background: '#fff',
      }"
    >
      <a-spin :spinning="confirmLoading">
        <div>
          <a-row :gutter="10">
            <a-col :md="24">
              <!-- 数据列表 -->
              <!-- 列表 -->
              <TableNewView
                ref="tableView"
                :table="table"
                :columns="columns"
                :tabDataList="tabDataList"
                :dataList="dataSource"
                @operate="operate"
              >
                <span slot="RevisedAmount" slot-scope="{ text, record }">
                  <a-input-number
                    style="width: 100%"
                    v-model="record['FinalAmount']"
                    @blur="inputChange($event, record)"
                    placeholder="请输入"
                    :maxLength="20"
                    step="1"
                    :precision="2"
                  />
                </span>
              </TableNewView>
            </a-col>
          </a-row>
          <div style="margin-top: 20px">
            <a-form-model ref="form" :rules="rules" :model="model">
              <a-form-model-item label="价格修正原因" prop="AdjustedRemark">
                <a-textarea
                  placeholder="200字符"
                  :maxLength="200"
                  style="width: 60%"
                  v-model="model.AdjustedRemark"
                  :auto-size="{ minRows: 5, maxRows: 7 }"
                />
              </a-form-model-item>
            </a-form-model>
          </div>
        </div>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button :style="{ marginRight: '8px' }" @click="handleOk" type="primary" :loading="confirmLoading"
        >确定</a-button
      >
    </a-row>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, postAction, putAction } from '@/api/manage'
export default {
  title: '价格异常修正',
  name: 'TicketPriceCorrectionModal',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      visible: false,
      confirmLoading: false,
      table: {
        bordered: true,
        operateBtns: [], //右上角按钮集合
        rowKey: 'Id',
        showPagination: false,
        customSlot: ['RevisedAmount'],
      },
      tabDataList: [],
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          width: 100,
          align: 'center',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据日期',
          dataIndex: 'DocumentDate',
          width: 150,
          align: 'center',
          scopedSlots: { customRender: 'date' },
        },
        {
          title: '厂家',
          dataIndex: 'Manufacturer',
          width: 150,
          align: 'center',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '含税金额',
          dataIndex: 'DiscountAmount',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '修正后含税金额',
          dataIndex: 'FinalAmount',
          align: 'center',
          width: 150,
          scopedSlots: { customRender: 'RevisedAmount' },
        },
      ],
      dataSource: [],
      httpHead: 'P36012',
      url: {
        listType: 'GET', //列表接口请求类型
        update: '/{v}/PurchaseSalesStatement/EditThridInvoiceDiscountBills',
      },
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
    }
  },
  computed: {
    rules() {
      return {
        AdjustedRemark: [{ required: true, message: '请输入价格修正原因', trigger: 'blur' }],
      }
    },
  },
  created() {},

  methods: {
    loadBefore() {},
    loadAfter() {},
    // searchQuery(param) {
    //   this.loadData()
    // },
    // 操作
    operate(record, key, index) {
      console.log(record, key, index)
    },
    show(list) {
      this.dataSource = list.map((f, index) => {
        return {
          ...f,
          number: index + 1,
        }
      })
      this.visible = true
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
      this.model = {}
    },
    //  求购数量输入框失去焦点事件
    inputChange(e, record) {
      const val = e.target.value
      // 同步真实本地数据
      this.dataSource.map((item) => {
        if (item.Id === record.Id) {
          item.FinalAmount = val
        }
      })
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let model = that.model
          let url = that.url.update
          let ItemList = this.dataSource.map((f) => {
            return {
              Id: f.Id,
              FinalAmount: Number(f.FinalAmount),
            }
          })
          let formData = {
            ...model,
            ItemList,
          }
          postAction(url, formData, that.httpHead)
            .then((res) => {
              that.confirmLoading = false
              if (res.IsSuccess) {
                this.$message.success('操作成功!')
                this.close()
                this.$emit('batchPriceCorrectionConfirm')
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
  },
}
</script>

<style scoped lang="scss"></style>
