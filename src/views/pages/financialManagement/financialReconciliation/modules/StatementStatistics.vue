<template>
  <!-- 对账单统计 -->
  <div>
    <a-spin :spinning="confirmLoading">
      <!-- 列表 -->
      <TableNewView
        ref="tableView"
        :table="table"
        :columns="columns"
        :tabDataList="tabDataList"
        :dataList="dataSourceNew"
        @operate="operate"
      >
      </TableNewView>
      <BillAmountCorrectionModal ref="BillAmountCorrectionModal" @billCorrectionAmount="billCorrectionAmount"/>
    </a-spin>
  </div>
</template>
<script>

export default {
  name: 'StatementStatistics',
  inject: ['ReconciliationType', 'supplierReconciliationType', 'customerReconciliationType', 'ifAudit'],
  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      confirmLoading: false,
      queryParam: {},
      table: {
        // curStatus: 1,
        // tabStatusKey: 'ApproavalStatus',
        operateBtns: [], //右上角按钮集合
        rowKey: 'ExpressCompanyId',
        tabTitles: [''],
        showPagination: false,
      },
      tabDataList: [],
      httpHead: 'P36012',
      url: {
      },
    }
  },
  computed: {
    dataSourceNew() {
      return [this.info]
    },
    columns() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      const ifAudit = this.ifAudit
      let supplierColumn = [
        {
          title: '合计账单金额',
          dataIndex: 'TotalAmount',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '供应商商品含税总金额',
          dataIndex: 'GoodsTotalAmount',
          width: 200,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '商品修正总金额',
          dataIndex: 'GoodsAdjustmentAmount',
          width: 200,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '商品价格修正数',
          dataIndex: 'GoodsAdjustmentCount',
          width: 200,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商票折含税总金额',
          dataIndex: 'DiscountTotalAmount',
          width: 200,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '票折修正金额',
          dataIndex: 'DiscountAdjustmentAmount',
          width: 200,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '账单修正金额',
          dataIndex: 'StatementAdjustmentAmount',
          width: 200,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '账单金额修正',
              isShow: (record) => {
                return true
              },
            },
          ],
        },
      ]
      let customerColumn = [
        {
          title: '合计账单金额',
          dataIndex: 'TotalAmount',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '客户商品含税总金额',
          dataIndex: 'GoodsTotalAmount',
          width: 200,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '商品修正总金额',
          dataIndex: 'GoodsAdjustmentAmount',
          width: 200,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '商品价格修正数',
          dataIndex: 'GoodsAdjustmentCount',
          width: 200,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '账单修正金额',
          dataIndex: 'StatementAdjustmentAmount',
          width: 200,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '账单金额修正',
              id: this.upDateId,
              isShow: (record) => {
                return true
              },
            },
          ],
        },
      ]
      switch (ReconciliationType) {
        case supplierReconciliationType:
          if(ifAudit) {
            supplierColumn = supplierColumn.filter(f => f.title !== '操作')
          }
          return supplierColumn
        case customerReconciliationType:
        if(ifAudit) {
          customerColumn = customerColumn.filter(f => f.title !== '操作')
          }
          return customerColumn
      }
    },
        // 表格编辑按钮Id
   upDateId() {
      const ReconciliationType = this.ReconciliationType
      const supplierReconciliationType = this.supplierReconciliationType
      const customerReconciliationType = this.customerReconciliationType
      switch (ReconciliationType) {
        case supplierReconciliationType:
          return 'ccbafb60-390e-4c22-93f6-0835c8824b6b'
        case customerReconciliationType:
          return '611ccf98-e485-4705-a2f4-7fa182880f0c'
      }
    },
  },
  watch: {
    // 一些属性无法设置成计算属性，需要通过监听ReconciliationType变化而设置在data里的值
    ReconciliationType: {
      handler(val) {
        let table = null
        const supplierReconciliationType = this.supplierReconciliationType
        const customerReconciliationType = this.customerReconciliationType
        switch (val) {
          case supplierReconciliationType:
            table = {
              operateBtns: [], //右上角按钮集合
              rowKey: 'Id',
              tabTitles: ['合计账单金额= 供应商商品含税总金额+商品修正总金额+供应商票折含税总金额+票折修正金额+账单修正金额'],
              showPagination: false,
            }
            break
          case customerReconciliationType:
            table = {
              operateBtns: [], //右上角按钮集合
              rowKey: 'Id',
              tabTitles: ['合计账单金额= 客户商品含税总金额+商品修正金额+账单修正金额'],
              showPagination: false,
            }
            break
          default:
            break
        }
        this.table = table
      },
      immediate: true,
    }
  },
  created() {},
  mounted() {},
  activated() {},

  methods: {
    // 操作
    operate(record, key, index) {
      console.log(record, key, index)
      if (key == '账单金额修正') {
        this.$refs.BillAmountCorrectionModal.show(record)
      }
    },
    billCorrectionAmount() {
      this.$emit('billCorrectionAmount')
    }
  },
}
</script>
<style scoped lang="scss"></style>
