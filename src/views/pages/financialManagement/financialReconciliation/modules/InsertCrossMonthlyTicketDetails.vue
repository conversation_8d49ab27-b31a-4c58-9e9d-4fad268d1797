<template>
  <!-- 插入跨月票折明细 -->
  <a-modal
    :title="'插入跨月票折明细'"
    :width="'80vw'"
    :visible="visible"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <!--插入跨月商品明细弹窗 -->
    <div
      :style="{
        width: '100%',
        background: '#fff',
      }"
    >
      <a-spin :spinning="confirmLoading">
        <div>
          <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
          <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
          <a-row :gutter="10">
            <a-col :md="24">
              <!-- 数据列表 -->
              <!-- 列表 -->
              <TableNewView
                ref="tableView"
                :table="table"
                :columns="columns"
                :tabDataList="tabDataList"
                :dataList="dataSource"
                @operate="operate"
              >
              </TableNewView>
            </a-col>
          </a-row>
        </div>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button :style="{ marginRight: '8px' }" @click="handleOk" :loading="confirmLoading" type="primary"
        >确认</a-button
      >
    </a-row>
  </a-modal>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, postAction, putAction } from '@/api/manage'
export default {
  name: 'InsertCrossMonthlyTicketDetails',
  inject: ['ReconciliationType', 'supplierReconciliationType', 'customerReconciliationType', 'reconciliationId'],
  mixins: [ListMixin],
  data() {
    return {
      visible: true,
      confirmLoading: false,
      searchItems: [
        {
          name: '票折单号', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'InvoiceDiscountNo', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '所属订单', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'PurchaseOrderNo', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '生产厂家', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'Manufacturer', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '发生日期',
          type: this.$SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTimeBegin',
          keyEnd: 'CreateTimeEnd',
          showTime: true,
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '责任人', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'OwnerByName', //搜索key 必填
          placeholder: '请输入',
        },
      ],
      table: {
        rowKey: 'InvoiceDiscountId',
        selectType: 'checkbox',
        isSelectCurPage: true,
      },
      columns: [
        {
          title: '票折单号',
          width: 150,
          align: 'center',
          dataIndex: 'InvoiceDiscountNo',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '所属订单',
          width: 150,
          align: 'center',
          dataIndex: 'PurchaseOrderNo',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'DocumentDate',
          align: 'center',
          width: 160,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据责任人',
          width: 150,
          align: 'center',
          dataIndex: 'OwnerByName',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'Manufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '票折金额',
          dataIndex: 'DiscountAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
        },
      ],
      tabDataList: [],
      dataSource: [],
      httpHead: 'P36012',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/PurchaseSalesStatement/QueryInsertScmInvoiceDiscountBillList', // 当前列表数据接口
        add: '/{v}/PurchaseSalesStatement/InsertScmInvoiceDiscountBills',
      },
    }
  },
  computed: {},
  created() {},
  mounted() {},
  activated() {},

  methods: {
    loadBefore() {
      this.queryParam['PurchaseSalesStatementId'] = this.reconciliationId
    },
    loadAfter() {},
    // searchQuery(param) {
    //   this.loadData()
    // },
    // 操作
    operate(record, key, index) {
      console.log(record, key, index)
      if (key == 'down') {
      }
    },
    show(record) {
      this.visible = true
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      // this.visible = false
      this.queryParam = {}
      this.$emit('close')
    },
    handleOk() {
      const that = this
      let selectionRows = this.selectionRows
      if(selectionRows.length < 1) {
        this.$message.warning('请先勾选要操作的数据')
        return false
      }
      let url = that.url.add
      let ItemList = selectionRows.map((f) => {
        return {
          InvoiceDiscountId: f.InvoiceDiscountId,
          DiscountAmount: f.DiscountAmount,
          Manufacturer: f.Manufacturer,
        }
      })
      let formData = {
        PurchaseSalesStatementId: this.reconciliationId,
        ItemList,
      }
      that.confirmLoading = true
      postAction(url, formData, that.httpHead)
        .then((res) => {
          that.confirmLoading = false
          if (res.IsSuccess) {
            this.$message.success('操作成功!')
            this.close()
            this.$emit('confirmInsert')
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
  },
}
</script>
<style scoped lang="scss"></style>
