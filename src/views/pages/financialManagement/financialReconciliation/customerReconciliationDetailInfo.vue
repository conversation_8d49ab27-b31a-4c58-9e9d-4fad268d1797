<template>
  <!-- 客户对账详情页 -->
  <div>
    <a-card :bodyStyle="{ padding: '0' }">
      <template>
        <div :style="{ padding: '16px 16px 0 16px' }">
          <a-descriptions :column="4">
            <a-descriptions-item>
              <div class="flc hcenter">
                <a-icon
                  :type="getAuditIcon(form.AuditStatus)"
                  theme="twoTone"
                  :two-tone-color="getAuditColor(form.AuditStatus)"
                  style="font-size: 32px"
                />
                <span :style="{ color: getAuditColor(form.AuditStatus) }">{{ form.AuditStatusStr || '--' }}</span>
              </div>
            </a-descriptions-item>
            <a-descriptions-item>
              <span class="c999">对账金额</span>
              <div>¥{{ form.TotalAmount }}</div>
            </a-descriptions-item>
            <a-descriptions-item>
              <span class="c999">对账单号</span>
              <div>{{ form.StatementNo || '--' }}</div>
            </a-descriptions-item>
            <a-descriptions-item>
              <span class="c999">创建时间</span>
              <div>{{ form.CreateTime || '--' }}</div>
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <div class="myDivider"></div>
      </template>
      <a-tabs v-model="activeTabKey">
        <a-tab-pane :key="1" tab="基本信息">
          <ReconciliationDetails
            v-if="activeTabKey == 1"
            :ReconciliationType="2"
            isDetail
            @getBasicInfo="getBasicInfo"
          />
        </a-tab-pane>
        <a-tab-pane :key="2" tab="审核信息">
          <div style="margin: 5px 15px">
            <SupAuditInformation :bussinessNo="form.AuditId" v-if="activeTabKey == 2" />
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'customerReconciliationDetailInfo',
  mixins: [ListMixin],
  data() {
    return {
      activeTabKey: 1,
      form: {},
    }
  },
  computed: {},
  created() {},
  mounted() {},
  activated() {},

  methods: {
    // 获取基本信息回调
    getBasicInfo(info) {
      this.form = info
    },
  },
}
</script>
<style scoped lang="scss">

.myDivider {
  border-top: 1px solid #e8e8e8;
}
</style>
