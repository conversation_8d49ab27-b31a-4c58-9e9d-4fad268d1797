<!-- 往来单位商品对照 -->
<template>
  <!-- 内容 -->
  <a-row :gutter="12">
    <a-col :md="24">
      <a-card :bodyStyle="{ padding: '10px' }" :headStyle="{ padding: '0 10px ' }">
        <!-- 搜索 -->
        <a-card :bodyStyle="{ padding: '10px 10px 0 10px' }" style="margin-bottom: 10px">
          <div class="table-page-search-wrapper">
            <a-form layout="inline">
              <a-row :gutter="10">
                <a-col :md="queryCol">
                  <a-form-item label="商品" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input placeholder="名称/编号" v-model="queryParam.KeyWord"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :md="queryCol">
                  <a-form-item label="生产厂家" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input placeholder="请输入" v-model="queryParam.OtherPartyBrandManufacturer"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :md="queryCol">
                  <a-form-item label="往来单位名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <!-- <a-input placeholder="请输入" v-model="queryParam.CooperationCompanyId"></a-input> -->
                    <SingleChoiceSearchView 
                    style="width: 100%" 
                    placeholder="请输入" 
                    :httpParams="{ PageIndex: 1, PageSize: 100 }" 
                    httpHead="P36012" 
                    httpType="POST" 
                    keyWord="KeyWord" 
                    :Url="'/{v}/CooperationCompany/GetOutCooperationCompanys'" 
                    v-model="queryParam.CooperationCompanyId" 
                    :name="queryParam.CooperationCompanyName"  />
                  </a-form-item>
                </a-col>
                <a-col :md="queryCol">
                  <a-form-item label="品种责任人" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input placeholder="请输入" v-model="queryParam.GoodsOwnerBy"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :md="24">
                  <span style="float: right; overflow: hidden; margin-bottom: 10px" class="table-page-search-submitButtons">
                    <a-button @click="searchReset" icon="reload" style="margin-right: 8px">重置</a-button>
                    <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </a-card>
        <!--/ 搜索 -->
        <a-card :bodyStyle="{ padding: '10px' }" :headStyle="{ padding: '0 10px ' }" title="查询结果">
          <!-- 操作按钮 -->
          <div slot="extra">
            <template v-if="tab.operateBtn && tab.operateBtn.length > 0">
              <YYLButton v-for="(item, index) in tab.operateBtn" :key="index" :menuId="item.id" :text="item.name" v-show="!item.isHide" :type="item.type" :icon="item.icon" :class="{ 'operate-btn': index == 0 }" @click="operate('', item.key)" />
            </template>
            <!-- <a-button :disabled="dataSource.length < 1 ? true : false" icon="export" style="margin-right: 10px" @click="exportData()">导出</a-button> -->
          </div>
          <a-row>
            <a-col :span="24">
              <!-- 数据列表 -->
              <a-table :bordered="true" ref="table" rowKey="Id" size="middle" :columns="columnsData" :dataSource="dataSource" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length === 0 ? false : '37vh', x: '100%' }">
                <!-- 字符串超长截取省略号显示-->
                <span slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" :length="50" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else :length="50" />
                </span>
                <!-- 操作 -->
                <span slot="action" slot-scope="text, record, index">
                  <span v-for="(item, index) in columns[columns.length - 1].actionBtn" :key="index">
                    <!-- 根据条件隐藏显示 -->
                    <span style="margin: 0 5px; line-height: 1.2;" v-if="actionShowBtn(item, record)">
                      <a @click="operate(record, item.name)" :disabled="item.disabled">
                        <a-icon v-if="item.icon" :type="item.icon" />
                        {{ item.name }}
                      </a>
                    </span>
                  </span>
                  <span class="tm" v-if="columns[columns.length - 1].actionBtn && columns[columns.length - 1].actionBtn.filter((btn) => actionShowBtn(btn, record)).length == 0">-</span>
                </span>
              </a-table>
              <!--/ 数据列表 -->
            </a-col>
          </a-row>
        </a-card>
      </a-card>
    </a-col>
    <!-- 新增 -->
    <TradeUnitCommodityComparisonAddModal 
    ref="iTradeUnitCommodityComparisonAddModal" @ok="modalFormOk" />
    <!--  导入 -->
    <BatchImportSimpleModal ref="iBatchImportGoodsModal" 
    :modalTitle="'导入往来单位商品对照'" 
    :tableColumns="tableColumns" 
    :searchParamsList="searchImportInput" 
    :importConfig="goodsImportConfig"
    :bordered="true"
    :importUrl="`/{v}/CompanyGoodsMapping/ImportTempData`" 
    importHttpHead='P36012'
    @ok="(e)=>handleBatchImportModalOk(e,'goods')" />
  </a-row>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '往来单位商品对照',
  name: 'tradeUnitCommodityComparison',
  mixins: [ListMixin],
  components: { JEllipsis },
  data() {
    return {
      dateFormat: 'YYYY-MM-DD',
      dateFormatPoint: 'YYYY.MM.DD',
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增', type: 'primary', icon: 'plus', key: 'add', id: 'de494241-46ad-4199-93f1-42714d0a018c' },
          { name: '导出', type: '', icon: 'download', key: '导出', id: 'b512e0c8-d2fd-4b8f-9e65-6f615ba5f729' },
          { name: '导入', type: 'primary', icon: '', key: '导入', id: '86977131-53b6-4f2c-92f9-5edfabdf3c94' }
        ],
      },
      searchImportInput: [
        {
          name: '商品', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'KeyWord', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '生产厂家', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'BrandManufacturer', //搜索key 必填
          placeholder: '请输入',
        }
      ],
      goodsImportConfig: {
        templateFileName: '往来单位商品对照导入模板',//下载模板名字
        haveDataSourceKey:'Id',
        ErrorShowKey: 'ErrorInfo',//异常信息字段名称
        saveKey: 'BatchId',
        saveUrlType: 'GET',//保存接口请求方式
        saveUrl: '/{v}/CompanyGoodsMapping/ImportConfirmData',//保存接口
        listQueryKey: 'BatchId',//列表的时候需要用到的字段
        batchRemoveId: '',//批量移除id
        listRemoveType: 'DELETE',//列表删除接口请求方式
        listRemoveUrl: '/{v}/CompanyGoodsMapping/ImportDeleteData',//列表删除 接口
        listRemoveKey: '',//列表删除 参数 key
        listHttpHead: 'P36012',
        listUrl: '/{v}/CompanyGoodsMapping/GetTempDataList',//列表的请求接口
        listUrlType: 'GET',//列表接口请求方式
        queryParamStatusKey: 'Status',//列表查询 异常 正常 key
        noLoadData: true,//是否默认弹出时候不加载数据
        importResKey: 'BatchId',//导出完成后返回的key 列表的时候用到
        dataSourceListKey: 'ImportDetailList',
        clearUrlType: 'DELETE',
        clearUrl: '',
        clearSaveKey: 'BatchId',
      },
      // 表头
      columns: [
        {
          title: '往来单位名称',
          dataIndex: 'CooperationCompanyName',
          key: 'CooperationCompanyName',
          width: 200,
          // fixed: 'left',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对方',
          align: 'center',
          children: [
            {
              title: '商品编号',
              dataIndex: 'OtherPartyErpGoodsCode',
              key: 'OtherPartyErpGoodsCode',
              width: 100,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '商品名称',
              dataIndex: 'OtherPartyErpGoodsName',
              key: 'OtherPartyErpGoodsName',
              width: 200,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '商品规格',
              dataIndex: 'OtherPartyPackingSpecification',
              key: 'OtherPartyPackingSpecification',
              width: 120,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '生产厂家',
              dataIndex: 'OtherPartyBrandManufacturer',
              key: 'OtherPartyBrandManufacturer',
              width: 180,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
          ],
        },
        {
          title: '我方',
          align: 'center',
          children: [
            {
              title: '商品编号',
              dataIndex: 'ErpGoodsCode',
              key: 'ErpGoodsCode',
              width: 100,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '商品名称',
              dataIndex: 'ErpGoodsName',
              key: 'ErpGoodsName',
              width: 200,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '商品规格',
              dataIndex: 'PackingSpecification',
              key: 'PackingSpecification',
              width: 120,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '生产厂家',
              dataIndex: 'BrandManufacturer',
              key: 'BrandManufacturer',
              width: 180,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '品种责任人',
              dataIndex: 'GoodsOwnerBy',
              key: 'GoodsOwnerBy',
              width: 180,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
          ],
        },
        {
          title: '异常原因',
          dataIndex: 'ErrorInfo',
          key: 'ErrorInfo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          // fixed: 'right',
          actionBtn: [
            {
              name: '编辑',
              icon: '',
              id: '8493622a-7658-4072-838b-f4ea03c1f782',
              specialShowFuc: (e) => {
                // let record = e || {}
                return true
              },
            },
            {
              name: '解绑',
              icon: '',
              id: 'aa965e21-648f-4afe-aac2-662d66ccf431',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.IsBound?true:false
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
       
      ],
      queryParam: {
      },
      labelCol: {
        xs: { span: 8 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 16 },
        sm: { span: 19 },
      },
      queryCol: 6,
      httpHead: 'P36012',
      isInitData: false,
      url: {
        listType: 'GET',
        list: '/{v}/CompanyGoodsMapping/GetList',
        exportUrl: '/{v}/CompanyGoodsMapping/ExportList',//导出
        unbindUrl: '/{v}/CompanyGoodsMapping/Unbind',//解绑
      },
    }
  },
  created() { },
  activated() {
    this.loadData(1)
  },
  computed: {
    columnsData() {
      return this.columns.filter((item) => {
        return item.title !== '异常原因'
      })
    },
    tableColumns() {
      let arr = JSON.parse(JSON.stringify(this.columns))
      arr[arr.length - 1].actionBtns = [{ name: '移除', icon: '' }];
      console.log(arr)
      return  arr
    },
  },
  methods: {
    moment,
    searchReset() {
      this.queryParam = {}
      this.loadData(1)
    },
    searchQuery(){
      this.loadData(1)
    },
    // 导入保存
    handleBatchImportModalOk(data, type) {
      // console.log('导入保存')
      this.loadData(1)
    // console.log(data, type)
    // if (data.length > 0) {
    //   if (type == 'goods') {
    //     if ((this.model.PolicyList || []).length) {
    //       this.dataSource = this.dataSource.concat(data)
    //     } else {
    //       this.dataSource = data
    //     }
    //     this.$set(this.model, 'PolicyList', this.dataSource)
    //     this.$refs.form.clearValidate()
    //   }

    // }
  },
    // 列表操作
    operate(record, type) {
      let that = this
      switch (type) {
        case 'add':
          this.$refs.iTradeUnitCommodityComparisonAddModal.add()
          break;
        case '导入':
          this.$refs.iBatchImportGoodsModal.show()
          break;
        case '导出':
          this.handleExportXls(
            '往来单位商品对照',
            'Get',
            this.url.exportUrl,
            null,
            this.httpHead,
            this.queryParam
          )
          break;
        case '解绑':
          this.$confirm({
            title: '提示',
            content: '解绑后对账时将不能找到对应商品，是否继续取消？',
            okText: '继续',
            onOk() {
              that.subUnbind(record)
            },
            onCancel() { },
          })
          break;
        case '编辑':
          this.$refs.iTradeUnitCommodityComparisonAddModal.edit(record)
          break;

        }
    },
    // 解绑
    subUnbind(record) {
      // let params = {
      //   id: record.Id
      // }
      let url = this.url.unbindUrl
      getAction(url + '?id='+record.Id, {}, this.httpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('操作成功')
            this.loadData()
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    actionShowBtn(item, record) {
      //是否有按钮权限优先判断
      if (!this.checkBtnPermissions(item.id)) {
        return false
      }
      if (item.specialShowFuc) {
        // 特殊条件显示隐藏
        if (item.specialShowFuc(record) === true) {
          return true
        } else {
          return false
        }
      } else {
        // 未设置条件直接显示按钮
        return true
      }
    },
  },
}
</script>

<style lang="less" scoped>

/deep/.ant-table-middle {
  border: none;
}
.centerBox {
  text-align: center;
}
</style>
