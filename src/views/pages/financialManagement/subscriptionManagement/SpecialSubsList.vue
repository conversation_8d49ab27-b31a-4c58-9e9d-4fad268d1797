<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate">
      <!-- 促销时间 -->
      <span slot="AgreementStartTimeVal" slot-scope="{ text, record }">
        <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
      </span>
    </SimpleTable>

    <!-- 新增 -->
    <SpecialSubsScriptionNewModal ref="SpecialSubsScriptionNewModal" @ok="modalOk"></SpecialSubsScriptionNewModal>
    <!--详情-->
    <SpecialSubsScriptionInfoModal ref="SpecialSubsScriptionInfoModal" />
  </a-row>
</template>

<script>
import Vue from 'vue'
import { USER_TYPE } from '@/store/mutation-types'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'SpecialSubsList',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  data() {
    return {
      searchItems: [
        {
          name: '认款单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'RecognitionOrderNo',
        },
        {
          name: '折扣类型',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/SystemTool/GetEnumsByName',
          key: 'RebateType',
          params: { enumName: 'EnumSpecialRecognitionRebateType' },
          dataKey: { name: 'ItemDescription', value: 'ItemValue' },
        },
        {
          name: '收款单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'ReceiptNo',
        },
        {
          name: '采购订单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'PurchaseOrderNo',
        },
        {
          name: '打款方/供应商',
          placeholder: '名称/编号/拼音码',
          type: SEnum.INPUT,
          key: 'PayerKey',
        },
        {
          name: '协议编号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'AgreementNo',
        },
        {
          name: '甲方',
          placeholder: '名称/编号/拼音码',
          type: SEnum.INPUT,
          key: 'PartyFirstKey',
        },
        {
          name: '审核状态',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/SystemTool/GetEnumsByName',
          key: 'AuditStatus',
          params: { enumName: 'EnumAuditStatus' },
          dataKey: { name: 'ItemDescription', value: 'ItemValue' },
        },
        {
          name: '责任人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'OwnerByName',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
        {
          name: '协议类型',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/SystemTool/GetEnumsByName',
          key: 'PurchaseAgreementType',
          params: { enumName: 'EnumPurchaseAgreementType' },
          dataKey: { name: 'ItemDescription', value: 'ItemValue' },
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增', type: 'primary', icon: 'plus', key: 'add', id: '6295d3d9-f6dc-4651-8824-bd8c57be3cd6' },
        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '认款单号',
          dataIndex: 'RecognitionOrderNo',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '抵扣类型',
          dataIndex: 'RebateTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购订单号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方/供应商',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款/抵扣金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '认款/抵扣数量',
          dataIndex: 'TotalRecognitionCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 190,
          fixed: 'right',
          actionBtn: [
            {
              //userType 1采购人员 2商销人员 3财务人员 RebateType 1返货抵现金 2现金抵返货 3票折抵返货 AuditStatus 0待审核 1审核中 2审核通过 3审核失败
              name: '删除',
              icon: '',
              id: '8b088c6c-b568-482b-a137-8df27cae5c1b',
              specialShowFuc: (e) => {
                let record = e || {}
                if ([3].includes(record.AuditStatus)) {
                  return true
                } else return false
              },
            },
            {
              name: '撤销',
              icon: '',
              id: 'c86403b6-fac9-4d30-badc-85b0447fd72e',
              specialShowFuc: (e) => {
                let record = e || {}
                if ([2].includes(record.AuditStatus) && [1, 7].includes(record.RecognitionStatus)) {
                  return true
                } else return false
              },
            },
            {
              name: '详情',
              icon: '',
              id: '5768a842-57ac-4b29-8626-0a3d0cb46655',
            },
            {
              name: '编辑',
              icon: '',
              id: '9c28e0e3-f707-4496-8e87-e7489d722925',
              specialShowFuc: (e) => {
                let record = e || {}
                if ([3].includes(record.AuditStatus)) {
                  return true
                } else return false
              },
            },
          ],
          // actionBtn: [
          //   {
          //     //userType 1采购人员 2商销人员 3财务人员 RebateType 1返货抵现金 2现金抵返货 3票折抵返货 AuditStatus 0待审核 1审核中 2审核通过 3审核失败
          //     name: '删除',
          //     icon: '',
          //     id: '8b088c6c-b568-482b-a137-8df27cae5c1b',
          //     specialShowFuc: (e) => {
          //       let record = e || {}
          //       if (this.userType == 3) {
          //         // 财务人员 无 编辑 删除
          //         return false
          //       } else if (this.userType == 1 && [1].includes(record.RebateType) && [3].includes(record.AuditStatus)) {
          //         return true
          //       } else return false
          //     },
          //   },
          //   {
          //     name: '撤销',
          //     icon: '',
          //     id: 'c86403b6-fac9-4d30-badc-85b0447fd72e',
          //     specialShowFuc: (e) => {
          //       let record = e || {}
          //       if (this.userType == 1) {
          //         // 采购人员 无 撤销
          //         return false
          //       } else if (this.userType == 3 && [2].includes(record.AuditStatus)) {
          //         return true
          //       } else return false
          //     },
          //   },
          //   {
          //     name: '详情',
          //     icon: '',
          //     id: '5768a842-57ac-4b29-8626-0a3d0cb46655',
          //     specialShowFuc: (e) => {
          //       let record = e || {}
          //       if (this.userType == 1 && [3].includes(record.AuditStatus)) {
          //         return false
          //       } else return true
          //     },
          //   },
          //   {
          //     name: '编辑',
          //     icon: '',
          //     id: '9c28e0e3-f707-4496-8e87-e7489d722925',
          //     specialShowFuc: (e) => {
          //       let record = e || {}
          //       if (this.userType == 3) {
          //         // 财务人员 无 编辑 删除
          //         return false
          //       } else if (this.userType == 1 && [1].includes(record.RebateType) && [3].includes(record.AuditStatus)) {
          //         return true
          //       } else return false
          //     },
          //   },
          // ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      userType: null, //1采购人员 2商销人员 3财务人员
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/RecognitionSpecial/GetListAsync',
        del: '/{v}/RecognitionSpecial/DeleteAsync', //删除
        backout: '/{v}/RecognitionSpecial/CancelSpecialAsync', //撤销
      },
    }
  },
  activated() {
    this.userType = Vue.ls.get(USER_TYPE)
    this.$refs.table.loadDatas(null, this.$refs.SimpleSearchArea.queryParam)
  },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '撤销') {
        let that = this
        this.$confirm({
          title: '您确定要撤销该认款单吗?',
          content: '',
          onOk() {
            let url = that.linkUrl.backout + '?id=' + record.Id
            postAction(url, {}, 'P36012').then((res) => {
              if (res.IsSuccess && res.Data) {
                that.$message.success('撤销成功')
                that.$refs.table.loadDatas(1, that.queryParam)
              } else {
                that.$message.warning(res.Msg)
              }
            })
          },
          onCancel() { },
        })
      } else if (type == 'add') {
        this.$refs.SpecialSubsScriptionNewModal.show()
      } else if (type == '删除') {
        let that = this
        this.$confirm({
          title: '您确定要删除该认款单吗?',
          content: '',
          onOk() {
            let url = that.linkUrl.del + '?id=' + record.Id
            postAction(url, {}, 'P36012').then((res) => {
              if (res.IsSuccess && res.Data) {
                that.$message.success('删除成功')
                that.$refs.table.loadDatas(1, that.queryParam)
              } else {
                that.$message.warning(res.Msg)
              }
            })
          },
          onCancel() { },
        })
      } else if (type == '详情') {
        this.$refs.SpecialSubsScriptionInfoModal.show(record)
      } else if (type == '编辑') {
        let isUpdate = true
        this.$refs.SpecialSubsScriptionNewModal.show(record, isUpdate)
      }
    },
    modalOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
  },
}
</script>

<style></style>
