<!-- 返货批量认款 -->
<template>
  <a-modal :title="title" :width="1300" :visible="visible" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="saveLoading">
      <a-form-model ref="form" :rules="rules" :model="model">
        <div class="xieyi">收款单：</div>
        <a-table :bordered="false" ref="table" :rowKey="(record, index) => index" size="middle" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource.length === 0 ? false : '200px', x: '1000px' }">
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
          <!-- 价格显示-->
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text == 0 ? (text != 0 ? '¥' + text : '0') : '--'" />
          </span>
        </a-table>
        <a-form-model-item label="兑付方：" style="width: 600px" prop="PayerId">
          <SingleInputSearchView v-if="source == 1" placeholder="请输入客户名称/编号/拼音码" width="600px" ref="cusListSelect" :httpParams="{
              pageIndex: 1,
              pageSize: 200,
              IsValid: true,
            }" keyWord="keyWord" httpHead="P36002" :Url="url.cusList" :value="model.PayerId" :name="model.Payer" :isAbsolute="true" @clear="clearSearchInput" @change="changeSearchInput" />
          <div style="display: flex;width: 100%;" v-else-if="[3, 4, 5, 6,21].includes(source)">
            <SingleInputSearchView class="party-search" placeholder="请选择供应商/生产厂家" width="100%" :httpParams="{
              pageIndex: 1,
              pageSize: 200,
              AuthBusiness: 2,
              IsValid: true,
            }" keyWord="KeyWord" disabled httpHead="P36003" :Url="url.supplierList" :noShowList="true" :value="model.PayerId" :dataKey="{ name: 'Name', value: 'Id' }" :name="model.Payer" :isAbsolute="true" @clear="clearSearchInput" @change="changeSearchInput" />
            <a @click="searchDataNewInput">选择</a>

          </div>
          <div style="display: flex; width: 100%" v-else>
            <SingleInputSearchView :placeholder="source == 23 ? '请输入供应商名称/供应商编号/拼音码' : '请输入供应商名称/编号/拼音码'" width="600px" :httpParams="{
              pageIndex: 1,
              pageSize: 200,
              AuditStatus: 2,
              IsValid: true,
            }" keyWord="KeyWord" httpHead="P36003" :Url="url.supplierList" :value="model.PayerId" :dataKey="{ name: 'Name', value: 'Id' }" :name="model.Payer" :isAbsolute="true" @clear="clearSearchInput" @change="changeSearchInput" />
          </div>
          <div v-if="source == 23 && model.PayerId">可用预付余额：¥{{PrepayBalance || 0}}</div>
        </a-form-model-item>
        <!-- 优利泰认款 -->
        <div v-if="source == 20">
          <div class="xieyi">协议编号:</div>
          <a-form-model-item label="" prop="AgreementNo">
            <!-- 协议编号 -->
            <a-input v-model="model.AgreementNo" style="width: 100%" placeholder="请输入" :maxLength="100" />
          </a-form-model-item>
          <div class="xieyi">认款单号:</div>
          <a-form-model-item label="" prop="RecognitionNo">
            <!-- 认款单号 -->
            <a-input v-model="model.RecognitionNo" style="width: 100%" placeholder="请输入" :maxLength="100" />
          </a-form-model-item>
          <div class="xieyi">认款金额:</div>
          <a-form-model-item label="" prop="RecognitioAmount">
            <!-- 认款金额 -->
            <a-input-number placeholder="请输入认款金额" v-model="model.RecognitioAmount" style="width: 100%" @change="handleChange" />
          </a-form-model-item>
          <a-form-model-item label="备注" prop="Remark">
            <!-- 备注 -->
            <a-textarea placeholder="请输入备注" v-model="model.Remark" style="width: 100%" :rows="4" :maxLength="100" />
          </a-form-model-item>
        </div>
        <a-form-model-item v-if="source == 1" label="账簿：" prop="AccountBookName">
          <SingleChoiceView style="width: 80%" placeholder="请选择" :httpParams="{
            groupPY: 'XSZB',
          }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'Id' }" v-model="model.AccountBookName" :Url="'/{v}/Global/GetListDictItemForCustomer'" @change="(val, txt, item) => ((model.AccountBookName = txt ? txt : ''), (model.AccountBookNo = item ? item.ItemPym : null))" />
        </a-form-model-item>
        <!-- 过账认款 -->
        <div v-if="source == 6 || source == 23">
          <a-form-model-item :label="source == 23?'本次认款金额：':'认款金额：'" prop="RemainingAmount">
            <!-- <a-input-number placeholder="请输入认款金额" v-model="model.RemainingAmount" style="width: 100%" :max="RemainingAmount" /> -->
            <a-input-number :placeholder="source == 23?'请输入':'请输入认款金额'" v-model="model.RemainingAmount" :precision="source == 23 ? 2 : null" style="width: 100%" />
          </a-form-model-item>
          <a-form-model-item label="备注：">
            <a-textarea placeholder="请输入备注" v-model="model.Note" style="width: 100%" :rows="4" :maxLength="200" />
          </a-form-model-item>
        </div>
        <!-- 通用协议 -->
        <div v-if="source == 21">
          <div class="xieyi">是否按品种：</div>
          <a-form-model-item label="" prop="IsCreateByGoods">
            <a-select placeholder="请选择" v-model="model.IsCreateByGoods" :disabled="!model.PayerId?true:false" style="width: 100%;" @change="getListBySelect">
              <a-select-option value="true" :key="1">是</a-select-option>
              <a-select-option value="false" :key=2>否</a-select-option>
            </a-select>
          </a-form-model-item>
        </div>
        <div v-if="source == 5">
          <div class="xieyi" style="margin: 15px 0 10px 0;">抵扣协议
            <a-button v-if="source == 5" style="float: right;" type="primary" @click="chooseYearDeal">选择年度协议</a-button>
          </div>
          <a-card :bodyStyle="{ padding: '16px' }" style="margin-bottom: 16px">
            <a-table :bordered="false" ref="table" :rowKey="(record, index) => index" size="middle" :columns="columns3" :dataSource="dataSource3" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource3.length === 0 ? false : '150px', x: '1000px' }">
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 促销时间 -->
              <span slot="AgreementStartTimeVal" slot-scope="text, record">
                <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
              </span>
            </a-table>
          </a-card>
        </div>

        <!-- 协议标题 -->
        <div v-if="source !== 6 && source !== 20 && source !== 23">
          <div class="xieyi">{{ xyTitle }}</div>
          <a-card :bodyStyle="{ padding: '16px' }" style="margin-bottom: 16px">
            <!-- 搜索 -->
            <SimpleSearchArea ref="SimpleSearchArea" v-if="searchInput.length > 0" :queryCol="8" :searchInput="searchInput" @searchQuery="searchQuery" />
            <a-table :bordered="false" ref="table" :rowKey="(record, index) => index" size="middle" :columns="columns2" :dataSource="dataSource2" :pagination="false" :rowClassName="rowClassNameFun" :row-selection="rowSelectionInfo" :loading="confirmLoading2" :scroll="{ y: dataSource2.length === 0 ? false : '350px', x: '1000px' }">
              <!-- 全选的checkbox -->
              <template slot="tableCheckboxTitle">
                <a-checkbox style="margin-top:2px;margin-right:4px;" :value="checkAllCheckbox" v-model="checkAllCheckbox" @change="onAllcheckboxChange" />
              </template>
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 日期 -->
              <span slot="date" slot-scope="text">
                <j-ellipsis :value="text ? moment(text).format('YYYY-MM-DD') : ' -- '" :length="100" />
              </span>
              <!-- 文本输入框 -->
              <template slot="Note" slot-scope="text, record">
                <a-input v-model="record.Note" :disabled="record.inputDisabled" style="width: 100%" placeholder="请输入" :maxLength="source == 7 ? 40 : 100" />
              </template>
              <!-- 金额输入框 -->
              <template slot="NumberNote" slot-scope="text, record">
                <a-input-number v-model="record.NumberNote" :min="record.RemainingAmount < 0 ? record.RemainingAmount : 0" :max="record.RemainingAmount > 0 ? record.RemainingAmount : 0" :disabled="record.inputDisabled" style="width: 100%" placeholder="请输入" @change="inputNumberNote($event, record)" @blur="changeNumberNote($event, record)" />
              </template>
              <!-- 额外收入金额 -->
              <template slot="NumAddberNote" slot-scope="text, record">
                <a-input-number v-model="record.NumAddberNote" :min="0" :max="99999999999.99" :disabled="record.addInputDisabled" style="width: 100%" placeholder="请输入" @blur="changeNumberAddNote($event, record)" @change="chanNumberAddNote($event, record)" />
              </template>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text == 0 ? (text != 0 ? '¥' + text : '0') : '--'" />
              </span>
            </a-table>
          </a-card>
          <div>认款合计金额：¥{{ AmountAllNumVal || 0 }}</div>
        </div>
      </a-form-model>
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button @click="handleOk" type="primary" :loading="saveLoading">确认</a-button>
    </a-row>

    <!-- 选择年度协议 -->
    <TableSelectDataModal ref="TableSelectDataModal" type="radio" :selectTableData="selectTableData" isToSelectClose @chooseData="chooseData"></TableSelectDataModal>
    <!-- 表格选择兑付方弹窗 -->
    <TableSelectDataModal ref="duiFuTableSelectDataModal" type="radio" :selectTabData="selectTabData" @chooseData="chooseNewData">
    </TableSelectDataModal>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'

export default {
  name: 'AcceptanceFunModal',
  mixins: [ListMixin],
  components: {
    JEllipsis,
  },
  data() {
    return {
      title: '认款',
      visible: false,
      PrepayBalance: 0,//可用预付余额
      model: {
        PayerId: '',
        Payer: '',
      },
      confirmLoading: false,
      confirmLoading2: false,
      saveLoading: false,
      xyTitle: '',
      source: null,
      checkAllCheckbox: false,
      dataSource: [],
      dataSource2: [],
      dataSource3: [],
      searchInput: [],
      selectTableData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '协议编号', type: 'input', value: '', key: 'AgreementNo', defaultVal: '', placeholder: '请输入' },
          {
            name: '甲方',
            type: 'input',
            value: '',
            key: 'PartyFirstKey',
            defaultVal: '',
            placeholder: '名称/编号/拼音码',
          },
        ],
        title: '选择年度协议',
        name: '年度协议',
        recordKey: 'Id',
        httpHead: 'P36007',
        isInitData: false,
        url: {
          list: '/{v}/PurchaseAgreement/GetUnsubscribedRecognitionListAsync',
        },
        columns: [
          {
            title: '协议编号',
            dataIndex: 'AgreementNo',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '甲方',
            dataIndex: 'PartyFirstName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '核算方式',
            dataIndex: 'AccountingMethodStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '促销时间',
            dataIndex: 'AgreementStartTime',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'AgreementStartTimeVal' },
          },
          {
            title: '返利方式',
            dataIndex: 'AgreementRebateTypeStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '返利时间',
            dataIndex: 'RebateTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '责任人',
            dataIndex: 'ResponsiblePersonName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建时间',
            dataIndex: 'CreateTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
      selectTabData: [
        {
          tabTitle: '供应商',
          selectTableData: {
            searchInput: [
              //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
              {
                name: '供应商',
                type: 'input',
                value: '',
                key: 'KeyWord',
                defaultVal: '',
                placeholder: '名称/编号/拼音码',
              },
            ],
            title: '选择供应商',
            name: '供应商',
            recordKey: 'Id',
            httpHead: 'P36003',
            isInitData: false,
            queryParam: {
              AuthBusiness: 2,
            },
            url: {
              list: '/{v}/Supplier/GetSuppliersForSelect',
              listType: 'GET',
            },
            columns: [
              {
                title: '供应商名称',
                dataIndex: 'Name',
                width: 200,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '供应商编号',
                dataIndex: 'Code',
                width: 150,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '注册地址',
                dataIndex: 'RegAddress',
                width: 150,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '操作',
                dataIndex: 'action',
                align: 'center',
                width: 100,
                fixed: 'right',
                scopedSlots: { customRender: 'action' },
              },
            ],
          },
        },
        {
          tabTitle: '生产厂家',
          selectTableData: {
            searchInput: [
              //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
              {
                name: '生产厂家',
                type: 'input',
                value: '',
                key: 'KeyWord',
                defaultVal: '',
                placeholder: '名称/编号/拼音码',
              },
            ],
            title: '选择生产厂家',
            name: '生产厂家',
            recordKey: 'Id',
            httpHead: 'P36006',
            isInitData: false,
            url: {
              list: '/{v}/BrandManufacturer/QueryBrandManufacturerList',
              listType: 'GET',
            },
            columns: [
              {
                title: '生产厂家名称',
                dataIndex: 'Name',
                width: 200,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '操作',
                dataIndex: 'action',
                align: 'center',
                width: 100,
                fixed: 'right',
                scopedSlots: { customRender: 'action' },
              },
            ],
          },
        }
      ],
      // 商销认款
      searchInput1: [
        {
          name: '单据编号',
          type: 'input',
          value: '',
          key: 'BusinessNo',
          defaultVal: '',
          placeholder: '请输入单据编号',
        },
        {
          name: '对应订单编号',
          type: 'input',
          value: '',
          key: 'OrderNo',
          defaultVal: '',
          placeholder: '请输入订单编号',
        },
        {
          name: '发生时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],
      // 采购退货认款
      searchInput2: [
        {
          name: '退货编号',
          type: 'input',
          value: '',
          key: 'BusinessNo',
          defaultVal: '',
          placeholder: '请输入退货编号',
        },
        {
          name: '退款时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],
      // 短期采购协议
      searchInput3: [
        {
          name: '订单编号',
          type: 'input',
          value: '',
          key: 'PurchaseOrderNo',
          defaultVal: '',
          placeholder: '请输入订单编号',
        },
        {
          name: '商品名称',
          type: 'input',
          value: '',
          key: 'ErpGoodsName',
          defaultVal: '',
          placeholder: '请输入商品名称',
        },
        {
          name: '商品编号',
          type: 'input',
          value: '',
          key: 'ErpGoodsCode',
          defaultVal: '',
          placeholder: '请输入商品编号',
        },
        {
          name: '生产厂商',
          type: 'input',
          value: '',
          key: 'BrandManufacturer',
          defaultVal: '',
          placeholder: '请输入生产厂商',
        },
      ],
      // 短期销售协议
      searchInput4: [
        {
          name: '协议编号',
          type: 'input',
          value: '',
          key: 'AgreementNo',
          defaultVal: '',
          placeholder: '请输入协议编号',
        },
        {
          name: '商品',
          type: 'input',
          value: '',
          key: 'GoodsNameKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码',
        },
      ],
      // 采购预付款调价认款
      searchInput7: [
        {
          name: '单据编号',
          type: 'input',
          value: '',
          key: 'BusinessNo',
          defaultVal: '',
          placeholder: '请输入单据编号',
        },
        {
          name: '对应订单编号',
          type: 'input',
          value: '',
          key: 'OrderNo',
          defaultVal: '',
          placeholder: '请输入订单编号',
        },
        {
          name: '发生时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],
      // 通用协议按品种认款
      searchInput21: [
        {
          name: '协议编号',
          type: 'input',
          value: '',
          key: 'GeneralAgreementNo',
          defaultVal: '',
          placeholder: '请输入协议编号',
        },
        {
          name: '商品名称',
          type: 'input',
          value: '',
          key: 'ErpGoodsName',
          defaultVal: '',
          placeholder: '请输入商品名称',
        },
        {
          name: '商品编号',
          type: 'input',
          value: '',
          key: 'ErpGoodsCode',
          defaultVal: '',
          placeholder: '请输入商品编号',
        },
        {
          name: '生产厂商',
          type: 'input',
          value: '',
          key: 'BrandManufacturer',
          defaultVal: '',
          placeholder: '请输入生产厂商',
        }
      ],
      // 通用协议不按品种认款
      searchInput22: [
        {
          name: '协议编号',
          type: 'input',
          value: '',
          key: 'GeneralAgreementNo',
          defaultVal: '',
          placeholder: '请输入协议编号',
        },
      ],
      // columns: [
      //   {
      //     title: '收款单号',
      //     dataIndex: 'ReceiptNo',
      //     width: 180,
      //     ellipsis: true,
      //     scopedSlots: { customRender: 'component' },
      //   },
      //   {
      //     title: '收款日期',
      //     dataIndex: 'ReceiptTime',
      //     width: 180,
      //     ellipsis: true,
      //     scopedSlots: { customRender: 'component' },
      //   },
      //   {
      //     title: '收款方式',
      //     dataIndex: 'ReceiptTypeStr',
      //     width: 120,
      //     ellipsis: true,
      //     scopedSlots: { customRender: 'component' },
      //   },
      //   {
      //     title: '打款方',
      //     dataIndex: 'Payer',
      //     width: 120,
      //     ellipsis: true,
      //     scopedSlots: { customRender: 'component' },
      //   },
      //   {
      //     title: '收款账户',
      //     dataIndex: 'AccountName',
      //     width: 120,
      //     ellipsis: true,
      //     scopedSlots: { customRender: 'component' },
      //   },
      //   {
      //     title: '备注',
      //     dataIndex: 'Note',
      //     width: 120,
      //     ellipsis: true,
      //     scopedSlots: { customRender: 'component' },
      //   },
      //   {
      //     title: '收款金额',
      //     dataIndex: 'ReceiptAmount',
      //     width: 120,
      //     ellipsis: true,
      //     scopedSlots: { customRender: 'price' },
      //   },
      //   {
      //     title: '未认款金额',
      //     dataIndex: 'RemainingAmount',
      //     width: 120,
      //     ellipsis: true,
      //     scopedSlots: { customRender: 'price' },
      //   },
      // ],
      columns2: [],
      columns21: [
        {
          title: '单据编号',
          dataIndex: 'BusinessNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '类型',
          dataIndex: 'BusinessOrderTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'SaleOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BusinessTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收金额',
          dataIndex: 'TotalAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '剩余应收金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RemainingAmountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'NumberNote' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      columns22: [
        {
          title: '退货编号',
          dataIndex: 'RefundOrderNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退款时间',
          dataIndex: 'PurchaseRefundTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退货金额',
          dataIndex: 'TotalRefundAmout',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '剩余认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RemainingAmountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'NumberNote' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      columns23: [
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '日期',
          dataIndex: 'CreateTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '到期日',
          dataIndex: 'ExpirationTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '实际入库数量',
          dataIndex: 'InboundQuantity',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '每盒返利金额',
          dataIndex: 'RebatePrice',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款金额',
          dataIndex: 'UnrecognizedAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RemainingAmountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'NumberNote' },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'NumAddberNote' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      columns24: [
        {
          // title: '选择',
          slots: { title: 'tableCheckboxTitle' },
          dataIndex: 'checkbox',
          align: 'center',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children:
                <a-checkbox
                  v-model={record.checkbox}
                  style="margin-right:6px;"
                />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
          customCell: (record, index) => {
            return {
              on: {
                change: (e) => {
                  let selectedRowArray = []
                  let selectedRowKeys = []
                  this.dataSource2.map(item => {
                    if (item.checkbox == true) {
                      selectedRowArray.push(item)
                      selectedRowKeys.push(item.index)
                    }
                  })
                  this.selectedRowArray = selectedRowArray
                  this.selectedRowKeys = selectedRowKeys
                  this.onSelectChange(selectedRowKeys, selectedRowArray)
                },
              },
            };
          },
        },
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 100,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text ? moment(text).format('YYYY-MM-DD') : ' -- '} length={100} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '已认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RemainingAmountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          // scopedSlots: { customRender: 'NumberNote' },
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <a-input-number
                  style="width:100%"
                  value={record.NumberNote}
                  onchange={(e) => this.inputNumberNote(e, record)}
                  onblur={(e) => this.changeNumberNote(e, record)}
                  min={record.RemainingAmount < 0 ? record.RemainingAmount : 0}
                  max={record.RemainingAmount > 0 ? record.RemainingAmount : 0}
                  placeholder="请输入"
                  disabled={record.inputDisabled}
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          // scopedSlots: { customRender: 'NumAddberNote' },
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <a-input-number
                  style="width:100%"
                  value={record.NumAddberNote}
                  onchange={(e) => this.chanNumberAddNote(e, record)}
                  onblur={(e) => this.changeNumberAddNote(e, record)}
                  disabled={record.addInputDisabled}
                  min={0}
                  max={99999999999.99}
                  placeholder="请输入"
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          // scopedSlots: { customRender: 'Note' },
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <a-input
                  style="width:100%"
                  value={record.Note}
                  disabled={record.inputDisabled}
                  onblur={(e) => this.changeNote(e, record)}
                  maxLength={this.source == 7 ? 40 : 100}
                  placeholder="请输入"
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      columns25: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          width: 120,
          ellipsis: false,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '增加返利金额',
          dataIndex: 'RealTotalAddRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '已认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RemainingAmountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          // scopedSlots: { customRender: 'NumberNote' },
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <a-input-number
                  style="width:100%"
                  value={record.NumberNote}
                  onchange={(e) => this.inputChage(e, text, record, index)}
                  onblur={(e) => this.inputOnblur(e, text, record, index)}
                  max={record.RemainingAmount}
                  placeholder="请输入"
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          // scopedSlots: { customRender: 'Note' },
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <a-input
                  style="width:100%"
                  value={record.Note}
                  onblur={(e) => this.inputOnblur2(e, text, record, index)}
                  placeholder="请输入"
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      columns27: [
        {
          title: '单据编号',
          dataIndex: 'PriceAdjustmentNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '类型',
          dataIndex: 'BusinessOrderTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'PurchasePriceAdjustTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调价金额',
          dataIndex: 'TotalPriceAdjustment',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '剩余未认款金额',
          dataIndex: 'RemainingAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RemainingAmountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'NumberNote' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      columns29: [
        {
          title: '商品名称',
          dataIndex: 'BusinessNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'BusinessOrderTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'SaleOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'PaymentModeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返货数量',
          dataIndex: 'BusinessTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款数量',
          dataIndex: 'TotalAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'RemainingAmountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'NumberNote' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      columns3: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '核销方式',
          dataIndex: 'AccountingMethodStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '政策数量',
          dataIndex: 'PolicyCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      //通用协议按品种
      columns30: [
        {
          title: '协议编号',
          dataIndex: 'GeneralAgreementNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利金额',
          dataIndex: 'RebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款金额',
          dataIndex: 'UnRecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RemainingAmountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'NumberNote' },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'NumAddberNote' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      //通用协议不按品种
      columns31: [
        {
          title: '协议编号',
          dataIndex: 'GeneralAgreementNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利金额',
          dataIndex: 'RebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款金额',
          dataIndex: 'UnRecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RemainingAmountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'NumberNote' },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'NumAddberNote' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      columns32: [
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款日期',
          dataIndex: 'ReceiptTime',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款方式',
          dataIndex: 'ReceiptTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方',
          dataIndex: 'Payer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款账户',
          dataIndex: 'AccountName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
      columns33: [
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款日期',
          dataIndex: 'ReceiptTime',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款方式',
          dataIndex: 'ReceiptTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款账户',
          dataIndex: 'AccountName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方',
          dataIndex: 'Payer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
      PayerId: null,
      RemainingAmount: null, //计算的未认款金额
      AmountAllNumVal: 0,
      curCheckXy: undefined,
      rowKey: 'BusinessId',
      selectedRowKeys: [],
      selectedRowArray: [],
      xyInfo5Data: {},
      funCount: 0,
      httpHead: 'P36012',
      xyListHttpHead: 'P36012',
      url: {
        xyInfo: '',
        xyInfo1: '/{v}/Recognition/GetUnsubscribedSaleListAsync', //商销认款
        xyInfo2: '/{v}/Recognition/GetUnsubscribedPurchaseRefundListAsync', //采购退货退款
        xyInfo3: '/{v}/PurchaseAgreement/GetSupplierPurchaseShortAgreements', //短期采购协议
        xyInfo4: '/{v}/PurchaseAgreement/GetPolicyGoodsList', //短期销售协议认款
        xyInfo5: '/{v}/PurchaseAgreement/GetInfoAsync', //年度协议认款
        xyInfo6: '', //过账认款
        xyInfo7: '/{v}/PurchasePriceAdjust/GetUnsubscribedPurchasePriceAdjustList', //采购预付款调价认款
        xyInfo9: '', //返货池
        xyInfo10: '/{v}/RecognitionGeneralAgreement/GetRecognitionGeneralAgreementGoodsDetailList',//通用协议按品种
        xyInfo11: '/{v}/RecognitionGeneralAgreement/GetRecognitionGeneralAgreementList',//通用协议不按品种
        supplierList: '/{v}/Supplier/GetSuppliersForSelect',
        cusList: '/{v}/Customer/ListForBusiness',
        add: '',
        add1: '/{v}/Recognition/CreateSaleAsync', //商销认款
        add2: '/{v}/Recognition/CreatePurchaseRefundAsync', //退货认款
        add3: '/{v}/Recognition/CreateShortAgreementAsync', //短期采购
        add4: '/{v}/Recognition/CreateAgreementAsync', //短期销售
        add5: '/{v}/Recognition/CreateAgreementAsync', //年度协议
        add6: '/{v}/Recognition/CreatePostingAsync', //过账认款
        add7: '/{v}/PurchasePriceAdjust/Create', //采购预付款调价认款
        add9: '', //返货池
        add20: '/{v}/RecognitionYLT/Create', //优利泰
        add21: '/{v}/RecognitionGeneralAgreement/Create',//通用协议
        add23: '/{v}/RecognitionPrepaymentRefund/Create',//预付款退款认款
        GetAvailableBalanceAsync: '/{v}/ReleasedBalance/GetAvailableBalanceAsync', //获取可用抵扣余额
      },
    }
  },
  mounted() { },
  computed: {
    columns() {
      if ([20, 21, 23].includes(this.source)) {
        return this.columns33
      } else {
        return this.columns32
      }
    },
    rowSelectionInfo() {
      if (this.source == 4) {
        return null
      } else if (this.source == 5) {
        return null
      } else {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange,
          onSelect: this.source == 1 ? this.onSelectOneChange : null,
        }
      }
    },
    rules() {
      return {
        PayerId: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.PayerId) {
                callback(new Error('请选择兑付方!'))
              } else {
                callback()
              }
            },
          },
        ],
        AccountBookName: [{ required: true, message: '请选择账簿!' }],
        RemainingAmount: [{
          required: true, validator: (rule, value, callback) => {
            if (!value) {
              callback(new Error('请输入认款金额!'))
            } else if (value > this.RemainingAmount && this.source != 23) {
              callback(new Error('认款金额不能大于未认款金额!'))
            } else if (this.source == 23 && value < 0) {
              callback(new Error('本次认款金额必须大于0!'))
            } else if (this.source == 23 && value > this.PrepayBalance) {
              callback(new Error('本次认款金额只能填小于等于可用预付余额金额!'))
            } else {
              callback()
            }
          },
        }],
      }
    }
  },
  watch: {
    'model.PayerId': function (val) {
      // console.log('watch model.PayerId', val)
      // 获取可用预付余额
      if (val && this.source == 23) {
        // console.log('获取可用预付余额')
        this.getPrepaidBalance()
      }
    }
  },
  methods: {
    moment,
    show(record, columns, source) {
      console.log('source=', source)
      this.source = source
      this.dataSource = [record]
      this.RemainingAmount = record.RemainingAmount
      this.initData()
      this.title = '认款'
      //1 商销认款 2采购退货认款 3短期采购协议 4短期销售协议 5年度协议 6过账认款 7采购预付款调价认款 9返货池已经其他页面处理 20优利泰 21通用协议 23：预付款退款认款
      switch (source) {
        case 1:
          this.xyTitle = '商销订单'
          this.xyListHttpHead = 'P36012'
          this.columns2 = this.columns21
          this.searchInput = this.searchInput1
          this.url.xyInfo = this.url.xyInfo1
          this.url.add = this.url.add1
          break
        case 2:
          this.xyTitle = '采购退货单'
          this.xyListHttpHead = 'P36012'
          this.columns2 = this.columns22
          this.searchInput = this.searchInput2
          this.url.xyInfo = this.url.xyInfo2
          this.url.add = this.url.add2
          break
        case 3:
          this.xyTitle = '短期采购协议'
          this.xyListHttpHead = 'P36007'
          this.columns2 = this.columns23
          this.searchInput = this.searchInput3
          this.url.xyInfo = this.url.xyInfo3
          this.url.add = this.url.add3
          break
        case 4:
          this.xyTitle = '短期销售协议'
          this.xyListHttpHead = 'P36007'
          this.columns2 = this.columns24
          this.searchInput = this.searchInput4
          this.url.xyInfo = this.url.xyInfo4
          this.url.add = this.url.add4
          break
        case 5:
          this.xyTitle = '本次认款明细'
          this.xyListHttpHead = 'P36007'
          this.columns2 = this.columns25
          this.url.xyInfo = this.url.xyInfo5
          this.url.add = this.url.add5
          break
        case 6:
          this.xyTitle = ''
          this.url.add = this.url.add6
          break
        case 7:
          this.xyTitle = '单据明细'
          this.xyListHttpHead = 'P36012'
          this.columns2 = this.columns27
          this.searchInput = this.searchInput7
          this.url.xyInfo = this.url.xyInfo7
          this.url.add = this.url.add7
          break
        case 9:
          this.xyTitle = '返货池'
          this.xyListHttpHead = 'P36007'
          this.columns2 = this.columns29
          this.url.xyInfo = this.url.xyInfo9
          this.url.add = this.url.add9
          break
        case 20:
          this.xyTitle = '优利泰认款'
          this.xyListHttpHead = 'P36012'
          this.columns2 = this.columns29
          this.url.xyInfo = this.url.xyInfo9
          this.url.add = this.url.add20
          break
        case 21:
          this.xyTitle = '通用协议'
          this.xyListHttpHead = 'P36012'
          this.columns2 = this.columns30
          // this.url.xyInfo = this.url.xyInfo9
          this.url.add = this.url.add21
          break
        case 23:
          this.xyTitle = ''
          // this.url.xyInfo = this.url.xyInfo9
          this.url.add = this.url.add23
          break
        default:
          this.xyTitle = ''
      }
      this.visible = true

    },
    //通用协议是否按品种查询
    getListBySelect(value) {
      this.searchInput = value === 'true' ? this.searchInput21 : this.searchInput22
      this.dataSource2 = []
      this.selectedRowArray = []
      this.selectedRowKeys = []
      this.columns2 = value === 'true' ? this.columns30 : this.columns31
      this.url.xyInfo = value === 'true' ? this.url.xyInfo10 : this.url.xyInfo11
      const params = {
        IsCreateByGoods: this.model.IsCreateByGoods == 'true',
        PayerId: this.model.PayerId,
        IsOwnerBy: false
      }
      this.getNewDetail(params)
    },
    getNewDetail(paramVal) {
      let params = {
        pageIndex: 1,
        pageSize: 99999,
        ...paramVal,
      }
      this.dataSource2 = []
      this.confirmLoading2 = true
      postAction(this.url.xyInfo, params, this.xyListHttpHead).then(res => {
        this.confirmLoading2 = false
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        res.Data.map((item, index) => {
          item.NumberNote = null
          item.NumAddberNote = null
          item.updateNumber = null
          item.RemainingAmount = item.UnRecognitionAmount
        })
        this.dataSource2 = res.Data
        // 设置不能填写input
        this.dataSource2.map((item, index) => {
          item.index = index
          item.inputDisabled = true
          item.addInputDisabled = true
        })
      })
    },
    initData() {
      this.model = {
        PayerId: '',
        Payer: '',
      }
      this.AmountAllNumVal = 0
      this.dataSource2 = []
      this.dataSource3 = []
      this.selectedRowKeys = []
      this.selectedRowArray = []
      
    },
    getXyDetail(paramVal, id) {
      let params = {}
      if (this.source == 1) {
        // 商销认款
        params = {
          pageIndex: 1,
          pageSize: 99999,
          PayerId: this.model.PayerId,
          ...paramVal,
        }
      } else if (this.source == 2) {
        // 采购退货
        params = {
          pageIndex: 1,
          pageSize: 99999,
          PayerId: this.model.PayerId,
          ...paramVal,
        }
      } else if (this.source == 3) {
        // 短期采购
        params = {
          pageIndex: 1,
          pageSize: 99999,
          PartyFirstId: this.model.PayerId,
          IsCanRecognition: true,
          IsOwnerBy: false,
          ...paramVal,
        }
      } else if (this.source == 4) {
        // 短期销售
        params = {
          pageIndex: 1,
          pageSize: 99999,
          SupplierId: this.model.PayerId,
          IsCanRecognition: true,
          PurchaseAgreementType: 2,
          AgreementRebateType: 1,//1返钱 2返货
          IsOwnerBy: false,
          ...paramVal,
        }
      } else if (this.source == 5) {
        // 年度协议
        params = {
          pageIndex: 1,
          pageSize: 99999,
          Id: this.xyInfo5Data.PurchaseAgreementId,
          ...paramVal,
        }
      } else if (this.source == 7) {
        // 采购预付款调价认款
        params = {
          pageIndex: 1,
          pageSize: 99999,
          PayerId: this.model.PayerId,
          ...paramVal,
        }
      } else if (this.source == 9) {
        // 返货池
        params = {
          pageIndex: 1,
          pageSize: 99999,
          SupplierId: this.model.PayerId,
          ...paramVal,
        }
      }
      this.dataSource2 = []
      this.dataSource3 = []
      this.confirmLoading2 = true
      getAction(this.url.xyInfo, params, this.xyListHttpHead)
        .then((res) => {
          if (!res.IsSuccess) {
            this.$message.warning(res.Msg)
            return
          }
          // 年度协议
          if (this.source == 5) {
            let resData = [res.Data]
            // 重组数据
            // 先重组过滤调返钱或者返货数据
            let array1 = []
            resData[0].PolicyList.map((item) => {
              // 返钱
              if (item.RebateType == 1) {
                array1.push(item)
              }
            })
            resData[0].PolicyList = array1
            // console.log('resData ', resData[0].PolicyList)
            // 重组年度协议数据
            this.AmountAllNumVal = 0
            let goodsArray = []
            resData[0].PolicyList.map((item, index) => {
              if (resData[0].AccountingMethod == 3 && item.GoodsList.length == 0) {
                item.GoodsList = [{}]
              }
              if (item.GoodsList.length > 0) {
                item.GoodsList.map((rItem, rIndex) => {
                  rItem.PolicyContent = item.PolicyContent
                  rItem.TotalRebateAmount = item.TotalRebateAmount
                  rItem.CancelRebateAmount = item.CancelRebateAmount
                  rItem.TotalRecognitionAmount = item.TotalRecognitionAmount
                  rItem.RemainingAmount = item.RemainingAmount
                  rItem.RemainingAmountVal = null
                  rItem.Note = ''
                  rItem.NumberNote = item.RemainingAmount
                  rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
                  rItem.AgreementId = item.PurchaseAgreementId
                  rItem.PolicyCount = item.PolicyCount
                  rItem.RealTotalAddRebateAmount = item.RealTotalAddRebateAmount
                  if (rIndex == 0) {
                    rItem.rowSpan = item.GoodsList.length
                    this.AmountAllNumVal += Number(rItem.NumberNote || 0)
                    this.AmountAllNumVal = Number(this.AmountAllNumVal.toFixed(2))
                  } else {
                    rItem.rowSpan = 0
                  }
                  goodsArray.push(rItem)
                })
              } else {
                let obj = {}
                obj.PolicyContent = item.PolicyContent
                obj.TotalRebateAmount = item.TotalRebateAmount
                obj.CancelRebateAmount = item.CancelRebateAmount
                obj.TotalRecognitionAmount = item.TotalRecognitionAmount
                obj.RemainingAmount = item.RemainingAmount
                obj.RemainingAmountVal = null
                obj.Note = ''
                obj.NumberNote = item.RemainingAmount
                obj.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
                obj.AgreementId = item.PurchaseAgreementId
                obj.PolicyCount = item.PolicyCount
                obj.RealTotalAddRebateAmount = item.RealTotalAddRebateAmount
                obj.rowSpan = 1
                this.AmountAllNumVal += Number(obj.NumberNote || 0)
                this.AmountAllNumVal = Number(this.AmountAllNumVal.toFixed(2))
                goodsArray.push(obj)
                // console.log('item.GoodsList  ',item.GoodsList)
              }

            })
            this.dataSource2 = goodsArray
            this.dataSource3 = resData
            let selectedRowArray = []
            goodsArray.map((item) => {
              if (item.rowSpan > 0) {
                selectedRowArray.push(item)
              }
            })
            this.selectedRowArray = selectedRowArray
          } else if (this.source == 4) {
            // PurchaseAgreementPolicyId 根据政策Id合并
            res.Data = this.setSource4RowSpan(res.Data) || []
          }

          if (this.source != 5 && res.Data && res.Data.length > 0) {
            res.Data.map((item, index) => {
              item.NumberNote = null
              item.NumAddberNote = null
              item.Note = ''
              if (this.source == 3) {
                item.RemainingAmount = item.UnrecognizedAmount
              }
            })
            this.dataSource2 = res.Data
          }
          // 设置不能填写input
          this.dataSource2.map((item, index) => {
            item.index = index
            item.inputDisabled = true
            item.addInputDisabled = true
          })
        })
        .finally(() => {
          this.confirmLoading2 = false
        })
    },
    handleChange(value) {
      this.model.RecognitioAmount = this.getAmountOfMoney(value)
    },
    setSource4RowSpan(resData) {
      if (resData && resData.length > 0) {
        // 相同的值排序
        resData = resData.sort((a, b) => {
          return a.PurchaseAgreementPolicyId > b.PurchaseAgreementPolicyId ? 1 : -1
        })

        let groupedArrObj = resData.reduce((acc, obj) => {
          let key = obj.PurchaseAgreementPolicyId;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push(obj);
          return acc;
        }, {});
        let samePolicyIdArray = Object.entries(groupedArrObj) || []
        if (samePolicyIdArray.length > 0) {
          resData.map(item => {
            let rt = samePolicyIdArray.find(r => {
              return item.PurchaseAgreementPolicyId == r[0] && r[1].length > 1
            })
            if (rt) {
              item.GoodsList = rt[1]
            }

          })
          resData.map((item, index) => {
            if (item.GoodsList && item.GoodsList.length > 0) {
              item.GoodsList.map((rItem, rIndex) => {
                if (rIndex == 0) {
                  rItem.rowSpan = item.GoodsList.length
                } else {
                  rItem.rowSpan = 0
                }
              })
            }
          })
          console.log('resData', resData)
        }
        return resData
      }
    },
    inputOnblur(e, text, record, index) {
      record.NumberNote = e.target.value
      this.changeNumberNote(e, record)
    },
    inputChage(e, text, record, index) {
      record.NumberNote = e
    },
    inputOnblur2(e, text, record, index) {
      record.Note = e.target.value
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      queryParam.PayerId = this.model.PayerId
      if (!queryParam.PayerId) {
        this.$message.warning('请选择兑付方再操作')
        return
      }
      //通用协议下需要判断是否已选择是否按品种, 接口增加IsOwnerBy入参
      if (this.source == 21) {
        queryParam.IsCreateByGoods = this.model.IsCreateByGoods
        queryParam.IsOwnerBy = false
        if (!queryParam.IsCreateByGoods) {
          this.$message.warning('请选择是否按品种再操作')
          return
        }
      }

      // if (type != 'searchReset') {
      //   if (Object.keys(queryParam).length == 1 && Object.keys(queryParam)[0] === 'PayerId') {
      //     this.$message.warning('请输入要查询的条件再操作')
      //     return
      //   }
      // }
      if (this.source != 5) {
        this.selectedRowKeys = []
        this.selectedRowArray = []
        this.AmountAllNumVal = 0
      }
      if (this.source == 21) {
        this.getNewDetail(queryParam)
        return
      }
      this.getXyDetail(queryParam)
    },
    // table行的颜色
    rowClassNameFun(record, index) {
      if (record.RemainingAmount < 0) {
        return 'table-color-red-dust'
      }
    },
    changeSearchInput(val, name) {
      this.model.PayerId = val
      this.model.Payer = name
      this.selectedRowKeys = []
      this.selectedRowArray = []
      this.$refs.form.validateField(['PayerId'])
      if ([1, 2, 3, 4, 7].indexOf(this.source) >= 0 && val) {
        this.getXyDetail()
      }
    },
    clearSearchInput() {
      this.model.PayerId = ''
      this.model.Payer = ''
      this.selectedRowKeys = []
      this.selectedRowArray = []
      this.dataSource2 = []
    },
    clearNewInput() {
      this.model.GeneralAgreementNo = ''
    },
    inputNumberNote(e, record) {
      //认款金额保留两位小数
      const num = this.getAmountOfMoney(e - 0) - 0
      record.updateNumber = num
      record.NumberNote = num
      if (e == record.RemainingAmount) {
        record.addInputDisabled = false
      } else {
        record.addInputDisabled = true
        record.NumAddberNote = null
      }
    },
    onAllcheckboxChange(e, record) {
      let status = e.target.checked
      this.checkAllCheckbox = status
      let selectedRowArray = []
      let selectedRowKeys = []
      this.dataSource2.map(item => {
        this.$set(item, 'checkbox', status)
        item.checkbox = status
        if (item.checkbox == true) {
          selectedRowArray.push(item)
          selectedRowKeys.push(item.index)
        }
      })
      this.selectedRowArray = selectedRowArray
      this.selectedRowKeys = selectedRowKeys
      this.onSelectChange(selectedRowKeys, selectedRowArray, true)
    },
    searchDataNewInput() {
      let data = [
        {
          Name: this.model.Payer,
          Id: this.model.PayerId,
        }
      ]
      this.$refs.duiFuTableSelectDataModal.show(data)
    },
    changeNumberNote(e, record) {
      let value = e.target.value
      if (record.RemainingAmount < 0) {
        if (value > 0) {
          this.$message.warning('剩余应收金额为负数，本次认款金额不能大于0')
          this.countPriceNum()
          return
        }
        if (value < record.RemainingAmount) {
          this.$message.warning('本次认款金额不能小于剩余应收金额' + record.RemainingAmount)
          this.countPriceNum()
          return
        }
      } else if (record.RemainingAmount > 0) {
        if (value < 0) {
          this.$message.warning('剩余应收金额为正数，本次认款金额不能小于0')
          this.countPriceNum()
          return
        }
        if (value > record.RemainingAmount) {
          this.$message.warning('本次认款金额不能大于剩余应收金额' + record.RemainingAmount)
          this.countPriceNum()
          return
        }
      }
      this.countTotalNum()
    },
    // change值内容改变的时候 只赋值
    chanNumberAddNote(e, record) {
      let value = e
      record.NumAddberNote = value
      // this.countTotalNum()
    },
    // blur 失去焦点时 计算合计金额
    changeNumberAddNote(e, record) {
      // let value = e.target.value
      // record.NumAddberNote = value
      this.countTotalNum()
    },
    chooseNewData(data) {
      // console.log('xuanz   ',data)
      if (data && data.length > 0) {
        let val = data[0].Id
        let name = data[0].Name

        this.model.PayerId = val
        this.model.Payer = name
        this.selectedRowKeys = []
        this.selectedRowArray = []
        this.$refs.form.validateField(['PayerId'])
        if ([1, 2, 3, 4, 7].indexOf(this.source) >= 0 && val) {
          this.getXyDetail()
        }
        // 过账认款
        if (this.source === 6) {
          //  兑付方类型（供应商=1, 生产厂家=2）
          this.model.PayerType = data[0].tabActiveKey === 0 ? 1 : 2
        }
      }
    },
    changeNote(e, record) {
      let value = e.target.value
      record.Note = value
    },
    onSelectChange(selectedRowKeys, selectedRowArray, isCheckedAll) {
      // 商销认款全选
      if ((selectedRowKeys.length == 0 || selectedRowKeys.length == this.dataSource2.length) && this.source == 1) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRowArray = selectedRowArray
        this.dataSource2.map((item) => {
          item.inputDisabled = true
          item.addInputDisabled = true
          if (selectedRowKeys.length == 0) {
            item.NumberNote = null
            item.NumAddberNote = null
            item.updateNumber = null
          }
        })
        this.countPriceNum()
      } else {
        // 其他认款
        let differenceList = this.dataSource2.filter((item) => selectedRowArray.indexOf(item) === -1)
        if (differenceList.length > 0) {
          differenceList.map((item) => {
            if (!item.checkbox) {
              item.NumberNote = null
              item.NumAddberNote = null
              item.updateNumber = null
              delete item.Note
            }
          })
        }
        this.selectedRowKeys = selectedRowKeys
        this.selectedRowArray = selectedRowArray
        this.dataSource2.map((item) => {
          item.inputDisabled = true
          item.addInputDisabled = true
        })
        this.$nextTick(() => {
          this.dataSource2 = [...this.dataSource2]
        })
        // 短期销售协议设置默认全选和全不选
        if (this.source == 4) {
          let dataSourceArray = this.dataSource2.filter(item => {
            return item.rowSpan !== 0
          })
          console.log('selectedRowKeys ', this.selectedRowKeys.length)
          console.log('dataSourceArray ', dataSourceArray.length)
          if (!isCheckedAll) {
            if (this.selectedRowKeys.length > 0 && (this.selectedRowKeys.length == dataSourceArray.length)) {
              this.checkAllCheckbox = true
            } else {
              this.checkAllCheckbox = false
            }
          }
        }
        if (this.source != 1) {
          this.countPriceNum()
        }
      }
    },
    onSelectOneChange(record, selected, selectedRows) {
      console.log('onSelectOneChange')
      this.selectedRowArray = []
      this.selectedRowKeys = []
      let sameCodeArray = []
      this.dataSource2.map((item, index) => {
        if (item.SaleOrderCode === record.SaleOrderCode && record.index !== item.index) {
          sameCodeArray.push(item)
        }
      })
      if (sameCodeArray.length > 0) {
        this.selectedRowArray = selectedRows.concat(sameCodeArray)
      } else {
        this.selectedRowArray = selectedRows
      }
      if (!selected) {
        let obj = null
        obj = this.selectedRowArray.find((item) => {
          return item.SaleOrderCode === record.SaleOrderCode
        })
        if (obj) {
          let newArray = []
          this.selectedRowArray.map((item) => {
            if (item.SaleOrderCode !== obj.SaleOrderCode) {
              newArray.push(item)
            }
          })
          this.selectedRowArray = newArray
        }
      }

      let differenceList = this.dataSource2.filter((item) => this.selectedRowArray.indexOf(item) === -1)
      if (differenceList.length > 0) {
        differenceList.map((item) => {
          item.NumberNote = null
          item.NumAddberNote = null
          item.updateNumber = null
          delete item.Note
        })
      }

      this.dataSource2.map((item) => {
        item.inputDisabled = true
        item.addInputDisabled = true
      })

      let selectedRowKeys = []
      this.selectedRowArray.map((item) => {
        selectedRowKeys.push(item.index)
      })
      this.selectedRowKeys = selectedRowKeys

      this.countPriceNum()
    },
    countPriceNum() {
      this.AmountAllNumVal = 0
      let AmountAllNum = 0
      let otherNum = 0
      let otherNumArray = []
      let isFirstCount = false
      this.selectedRowArray.map((item, index) => {
        item.NumberNote = item.updateNumber ? item.updateNumber : item.RemainingAmount
        item.inputDisabled = item.RemainingAmount == 0 ? true : false
        AmountAllNum += Number(item.NumberNote || 0)
        // 计算合计金额
        if (this.source == 3 || this.source == 4) {
          let num = Number(item.NumberNote || 0) + Number(item.NumAddberNote || 0)
          this.AmountAllNumVal += num
        } else {
          this.AmountAllNumVal += Number(item.NumberNote)
        }
        this.AmountAllNumVal = Number(this.AmountAllNumVal.toFixed(2))
        // 若未认款金额等于本次认款金额则能输入额外金额
        if (item.NumberNote == item.RemainingAmount) {
          item.addInputDisabled = false
        }
      })
    },
    // 计算合计金额
    countTotalNum() {
      this.AmountAllNumVal = 0
      this.selectedRowArray.map((item) => {
        if (item.NumAddberNote) {
          let num = Number(item.NumberNote || 0) + Number(item.NumAddberNote || 0)
          this.AmountAllNumVal += num
        } else {
          this.AmountAllNumVal += Number(item.NumberNote || 0)
        }
        this.AmountAllNumVal = Number(this.AmountAllNumVal.toFixed(2))
      })
    },
    chooseYearDeal() {
      if (!this.model.PayerId) {
        this.$message.warning('请先选择兑付方')
        return
      }
      let dataSource = []
      let queryParam = {
        SupplierId: this.model.PayerId,
        PurchaseAgreementType: 1,
        IsOwnerBy: false
      }
      this.$refs.TableSelectDataModal.show(dataSource, queryParam)
    },
    chooseData(data) {
      this.xyInfo5Data = data[0]
      this.getXyDetail()
    },
    handleCheck(e, record) {
      record.IsAll = e.target.value
      if (record.IsAll) {
        record.Count = record.RemainingAmount
      }
    },
    // 可用预付余额
    getPrepaidBalance() {
      let params = {
        merchantId: this.model.PayerId,
      }
      getAction(this.url.GetAvailableBalanceAsync, params, 'P36011')
        .then((res) => {
          if (res.IsSuccess) {
            this.$set(this, 'PrepayBalance', res.Data || 0)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => { })
    },
    modelOk(list) { },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.model = {}
      this.visible = false
    },
    handleOk() {
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          // 验证金额不能超过总的未认款金额

          if (![6, 20, 23].includes(this.source) && (this.AmountAllNumVal > this.dataSource[0].RemainingAmount)) {
            this.$message.warning('合计认款金额[' + this.AmountAllNumVal + ']不能大于收款单的未认款金额[' + this.dataSource[0].RemainingAmount + ']!')
            return
          }

          if (![6, 20, 23].includes(this.source) && (this.AmountAllNumVal <= 0)) {
            this.$message.warning('认款合计金额不能小于或等于0，请调整本次认款金额或额外收入金额后再确认')
            return
          }
          //优利泰验证
          if (this.source === 20) {
            if (!this.model.AgreementNo) {
              this.$message.warning('请输入协议编号')
              return
            }
            if (!this.model.RecognitionNo) {
              this.$message.warning('请输入认款编号')
              return
            }
            if (!this.model.RecognitioAmount && this.model.RecognitioAmount !== 0) {
              this.$message.warning('请输入认款金额')
              return
            }
            if (this.model.RecognitioAmount > this.dataSource[0].RemainingAmount) {
              this.$message.warning('合计认款金额[' + this.model.RecognitioAmount + ']不能大于收款单的未认款金额[' + this.dataSource[0].RemainingAmount + ']!')
              return
            }
            if ((this.model.RecognitioAmount <= 0)) {
              this.$message.warning('认款合计金额不能小于或等于0，请调整本次认款金额或额外收入金额后再确认')
              return
            }
          }
          let url = this.url.add
          let formData = {}
          if ([1, 2, 3, 4, 5].indexOf(this.source) >= 0) {
            if (this.selectedRowArray.length === 0) {
              this.$message.warning('请选择' + this.xyTitle)
              return
            }
            if (this.AmountAllNumVal < 0) {
              this.$message.warning('认款失败，认款合计金额不能小于0')
              return
            }
          }
          // 过滤本次认款金额为0的数据
          let selectedNotZeroRowArray = []
          this.selectedRowArray.map((item) => {
            if (item.NumberNote) {
              selectedNotZeroRowArray.push(item)
            }
          })
          if (![6, 20, 23].includes(this.source) && selectedNotZeroRowArray.length == 0) {
            this.$message.warning('本次认款金额不能全部为0或为空')
            return
          }
          console.log('认款的数据', selectedNotZeroRowArray)

          if (this.source == 1) {
            // 商销订单认款
            let RecognitionTypeBySaleOrderList = selectedNotZeroRowArray.map((item) => {
              return {
                SaleOrderId: item.SaleOrderId,
                BusinessId: item.BusinessId,
                BusinessEventType: item.BusinessOrderType,
                RecognitionAmount: item.NumberNote || 0,
                Note: item.Note,
              }
            })
            formData = {
              ReceiptId: this.dataSource[0].Id,
              PayerId: this.model.PayerId,
              Payer: this.model.Payer,
              AccountBookNo: this.model.AccountBookNo,
              AccountBookName: this.model.AccountBookName,
              RecognitionTypeBySaleOrderList: RecognitionTypeBySaleOrderList || [],
            }
          } else if (this.source == 2) {
            // 采购退货认款
            let RecognitionPurchaseRefundOrderList = selectedNotZeroRowArray.map((item) => {
              return {
                PurchaseRefundOrderId: item.PurchaseRefundOrderId,
                RecognitionAmount: item.NumberNote || 0,
                Note: item.Note,
              }
            })
            formData = {
              ReceiptId: this.dataSource[0].Id,
              PayerId: this.model.PayerId,
              Payer: this.model.Payer,
              RecognitionPurchaseRefundOrderList: RecognitionPurchaseRefundOrderList || [],
            }
          } else if (this.source == 3) {
            // 短期采购协议认款
            let ShortAgreementList = selectedNotZeroRowArray.map((item) => {
              return {
                ShortAgreementId: item.Id,
                RecognitionAmount: item.NumberNote || 0,
                AdditionalAmount: item.NumAddberNote || 0,
                Note: item.Note || '',
              }
            })
            formData = {
              ReceiptId: this.dataSource[0].Id,
              PayerId: this.model.PayerId,
              Payer: this.model.Payer,
              ShortAgreementList: ShortAgreementList || [],
            }
          } else if (this.source == 4) {
            // 短期销售协议认款
            let AgreementGoodsList = selectedNotZeroRowArray.map((item) => {
              return {
                AgreementId: item.PurchaseAgreementId,
                PurchaseAgreementPolicyId: item.PurchaseAgreementPolicyId,
                RecognitionAmount: item.NumberNote || 0,
                AdditionalAmount: item.NumAddberNote || 0,
                Note: item.Note,
              }
            })
            formData = {
              ReceiptId: this.dataSource[0].Id,
              PayerId: this.model.PayerId,
              Payer: this.model.Payer,
              PurchaseAgreementType: 2,
              AgreementGoodsList: AgreementGoodsList || [],
            }
          } else if (this.source == 5) {
            // 年度协议认款
            let AgreementGoodsList = selectedNotZeroRowArray.map((item) => {
              return {
                AgreementId: item.AgreementId,
                PurchaseAgreementPolicyId: item.PurchaseAgreementPolicyId,
                RecognitionAmount: item.NumberNote || 0,
                AdditionalAmount: 0,
                Note: item.Note,
              }
            })
            formData = {
              ReceiptId: this.dataSource[0].Id,
              PayerId: this.model.PayerId,
              Payer: this.model.Payer,
              PurchaseAgreementType: 1,
              AgreementGoodsList: AgreementGoodsList || [],
            }
          } else if (this.source == 6) {
            // console.log('过账 ',this.model)
            // return
            // 过账认款
            formData = {
              ReceiptId: this.dataSource[0].Id,
              PayerId: this.model.PayerId,
              Payer: this.model.Payer,
              RecognitionAmount: this.model.RemainingAmount,
              Note: this.model.Note,
              PayerType: this.model.PayerType,
            }
          } else if (this.source == 7) {
            // 采购预付款调价认款
            let RecognitionPurchasePriceAdjustOrderList = selectedNotZeroRowArray.map((item) => {
              return {
                PurchasePriceAdjustmentId: item.PurchasePriceAdjustOrderId,
                RecognitionAmount: item.NumberNote || 0,
                Note: item.Note,
              }
            })
            formData = {
              ReceiptId: this.dataSource[0].Id,
              PayerId: this.model.PayerId,
              Payer: this.model.Payer,
              RecognitionPurchasePriceAdjustOrderList: RecognitionPurchasePriceAdjustOrderList || [],
            }
          } else if (this.source == 20) {
            // 优利泰认款
            formData = {
              ReceiptId: this.dataSource[0].Id,//收款单ID
              PayerId: this.model.PayerId,//兑付方ID
              Payer: this.model.Payer,//兑付方/客户名称
              AgreementNo: this.model.AgreementNo,//协议编号
              RecognitionNo: this.model.RecognitionNo,//认款单号
              RecognitioAmount: this.model.RecognitioAmount,//认款金额
              Remark: this.model.Remark,//备注
            }
          } else if (this.source == 21) {
            const IsCreateByGoods = this.model.IsCreateByGoods === 'true'
            // IsCreateByGoods
            // 协议品种列表
            let GeneralAgreementGoodsList = []
            // 协议列表
            let GeneralAgreementList = []
            // 通用协议认款
            if (IsCreateByGoods) {
              GeneralAgreementGoodsList = selectedNotZeroRowArray.map(item => {
                return {
                  ...item,
                  RecognitionAmount: item.NumberNote || 0,
                  AdditionalAmount: item.NumAddberNote || 0,
                }
              })
            } else {
              GeneralAgreementList = selectedNotZeroRowArray.map(item => {
                return {
                  GeneralAgreementId: item.GeneralAgreementId,
                  TotalRecognitionAmount: item.NumberNote || 0,
                  TotalAdditionalAmount: item.NumAddberNote || 0,
                  Note: item.Note
                }
              })
            }
            formData = {
              ReceiptId: this.dataSource[0].Id,
              PayerId: this.model.PayerId,
              Payer: this.model.Payer,
              IsCreateByGoods,
              GeneralAgreementList: GeneralAgreementList || [],
              GeneralAgreementGoodsList: GeneralAgreementGoodsList || [],
            }
          } else if (this.source == 23) { //预付款退款认款
            formData = {
              ReceiptId: this.dataSource[0].Id,
              PayerId: this.model.PayerId,
              Payer: this.model.Payer,
              RecognitionAmount: this.model.RemainingAmount,
              AvailablePrepaidBalance: this.PrepayBalance,
              Note: this.model.Note,
            }
          }
          this.saveLoading = true
          postAction(url, formData, this.httpHead)
            .then((res) => {
              if (res.IsSuccess) {
                this.$message.success('操作成功')
                this.$emit('ok')
                this.close()
              } else {
                this.$message.warning(res.Msg)
                if (this.source == 7) {
                  // 如果占用了刷新详情
                  this.selectedRowKeys = []
                  this.selectedRowArray = []
                  this.getXyDetail()
                }
              }
            })
            .finally(() => {
              this.saveLoading = false
            })
        }
      })
    },
  },
}
</script>
<style lang="less" scoped>
.party-search {
  flex: 1;
  margin-right: 5px;
}

/deep/.party-search .ant-input:disabled {
  color: rgba(0, 0, 0, 0.65);
  background-color: #ffffff;
}

.xieyi,
.skd {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}

.xieyi::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

.title-btn {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  justify-items: center;
}

.title-btn .xieyi {
  padding-bottom: 0;
}

.recognition {
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  padding: 2px 5px;
}

.recognition::-webkit-input-placeholder {
  color: #bfbfbf;
  font-size: 14px;
}
</style>
