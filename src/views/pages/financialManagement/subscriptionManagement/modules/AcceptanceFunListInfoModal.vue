<!-- 认款详情 -->
<template>
  <a-modal :title="title" :width="1000" :visible="visible" :saveLoading="saveLoading" @cancel="handleCancel" @close="handleCancel" :footer="null" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form">
        <!-- 基本信息 -->
        <div class="xieyi">基本信息</div>
        <a-descriptions title="">
          <a-descriptions-item label="认款单号">{{ model.RecognitionOrderNo || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="认款方式">{{ model.RecognitionOrderModeStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="认款时间">{{ model.CreateTime || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="认款人">{{ model.CreateByName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="兑付方">{{ model.Payer || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="账簿" v-if="source == 1">{{ model.AccountBookName || ' -- ' }}</a-descriptions-item>
        </a-descriptions>

        <div class="xieyi">{{ source == 9 ? '返货采购订单' : '收款单' }}</div>
        <a-table :bordered="false" ref="table" :rowKey="(record, index) => index" size="middle" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource.length === 0 ? false : '200px', x: '1000px' }">
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
          <!-- 日期 -->
          <span slot="dayDate" slot-scope="text">
            <j-ellipsis :value="text ? text.substring(0, 11) : ''" />
          </span>
          <!-- 价格显示-->
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text == 0 ? (text != 0 ? '¥' + text : '0') : '--'" />
          </span>
        </a-table>

        <div style="margin-top:20px;color:rgba(0, 0, 0, 0.85);" v-if="source == 9">协议类型：{{ fanhuoPurchaseAgreementType ==
          1 ? '年度协议' :'短期销售协议'}}</div>

        <!-- 年度协议抵扣协议 -->
        <div style="margin-top:16px;" v-if="source == 5 || (source == 9 && fanhuoPurchaseAgreementType == 1)">
          <div class="xieyi">{{ source == 9 ? '年度协议：' : '抵扣协议：' }}</div>
          <a-table :bordered="false" ref="table" :rowKey="(record, index) => index" size="middle" :columns="columnsXY" :dataSource="dataSourceXY" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSourceXY.length === 0 ? false : '200px', x: '1000px' }">
            <!-- 字符串超长截取省略号显示-->
            <template slot="component" slot-scope="text">
              <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
              <j-ellipsis :value="text" v-else />
            </template>
            <!-- 促销时间 -->
            <span slot="AgreementStartTimeVal" slot-scope="text,record">
              <j-ellipsis v-if="text || record['AgreementEndTime']" :value="`${text.substring(0, 11) || ''}~${record['AgreementEndTime'].substring(0, 11) || ''}`" :length="40" />
              <span v-if="!text">{{ text }}</span>
            </span>
            <!-- 日期 -->
            <span slot="dayDate" slot-scope="text">
              <j-ellipsis :value="text ? text.substring(0, 11) : ''" />
            </span>
            <!-- 价格显示-->
            <span slot="price" slot-scope="text">
              <j-ellipsis :value="text || text == 0 ? (text != 0 ? '¥' + text : '0') : '--'" />
            </span>
          </a-table>
        </div>

        <!-- 协议标题 -->
        <div style="margin-top:16px;" v-if="source !== 6 && source !== 20 && source !== 23">
          <div class="xieyi">{{ xyTitle }}</div>
          <a-card :bodyStyle="{ padding: '16px' }" style="margin-bottom: 16px">
            <a-table :bordered="source == 5 ? true : false" ref="table" :rowKey="(record, index) => index" :size="source == 5 ? null : 'middle'" :columns="columns2" :dataSource="dataSource2" :pagination="false" :rowClassName="rowClassNameFun" :loading="confirmLoading2" :scroll="{ y: dataSource2.length === 0 ? false : '350px', x: '1000px' }">
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 日期 -->
              <span slot="dayDate" slot-scope="text">
                <j-ellipsis :value="text ? text.substring(0, 11) : ''" />
              </span>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text == 0 ? (text != 0 ? '¥' + text : '0') : '--'" />
              </span>
            </a-table>
          </a-card>
          <div v-if="source != 9">认款合计金额：¥{{ TotalRecognitionAmount }}</div>
          <div v-if="source == 9">认款合计数量：{{ TotalRecognitionCount }}</div>
        </div>
        <div v-if="source == 20 && YLTDeatil">
          <div class="newXieyi">协议编号:</div>
          <a-form-model-item label="">
            <!-- 协议编号 -->
            <a-input v-model="YLTDeatil.OldAgreementNo" style="width: 100%" :disabled="true" />
          </a-form-model-item>
          <div class="newXieyi">认款单号:</div>
          <a-form-model-item label="">
            <!-- 认款单号 -->
            <a-input v-model="YLTDeatil.OldRecognitionNo" style="width: 100%" :disabled="true" />
          </a-form-model-item>
          <div class="newXieyi">认款金额:</div>
          <a-form-model-item label="">
            <!-- 认款金额 -->
            <a-input-number v-model="model.TotalRecognitionAmount" style="width: 100%" :disabled="true" />
          </a-form-model-item>
          <a-form-model-item label="备注">
            <!-- 备注 -->
            <a-textarea v-model="model.Note" style="width: 100%" :rows="4" :disabled="true" />
          </a-form-model-item>
        </div>
        <div v-if="source == 6 || source == 23">
          <div style="margin:20px 0 10px 0;color:rgba(0, 0, 0, 0.85);">{{source == 23?'本次':''}}认款金额：{{ '¥' + model.TotalRecognitionAmount || 0 }}
          </div>
          <div style="color:rgba(0, 0, 0, 0.85);">备注：{{ model.Note || ' -- '}}</div>
        </div>

      </a-form-model>
    </a-spin>

  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";

export default {
  name: "AcceptanceFunListInfoModal",
  mixins: [ListMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "认款详情",
      visible: false,
      model: {
        PayerId: '',
        Payer: '',
      },
      recordModel: {},
      confirmLoading: false,
      confirmLoading2: false,
      saveLoading: false,
      xyTitle: '',
      source: null,
      record: {},
      dataSource: [],
      dataSource2: [],
      dataSourceXY: [],
      columns: [],
      columnsCommon: [
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款日期',
          dataIndex: 'ReceiptTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款方式',
          dataIndex: 'ReceiptTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方',
          dataIndex: 'Payer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款账户',
          dataIndex: 'AccountName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        // {
        //   title: '未认款金额',
        //   dataIndex: 'RemainingAmount',
        //   width: 120,
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'price' },
        // },
      ],
      columnsPurchase: [
        {
          title: '采购订单号',
          dataIndex: 'PurchaseOrderNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalReturnCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'PreRemainingCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns2: [],
      columns21: [
        {
          title: '单据编号',
          dataIndex: 'BusinessNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'BusinessCreator',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '类型',
          dataIndex: 'BusinessOrderTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'SalesOrderNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BusinessTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收金额',
          dataIndex: 'TotalAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        // {
        //   title: '剩余应收金额',
        //   dataIndex: 'PreRemainingAmount',
        //   width: 120,
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'price' },
        // },
        {
          title: '本次认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns22: [
        {
          title: '退货编号',
          dataIndex: 'RefundNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'BusinessCreator',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退款时间',
          dataIndex: 'RefundTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退货金额',
          dataIndex: 'RefundAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns23: [
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '日期',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '到期日',
          dataIndex: 'ExpirationTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'dayDate' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '实际入库数量',
          dataIndex: 'InboundQuantity',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '每盒返利金额',
          dataIndex: 'RebatePrice',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns24: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 160,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'dayDate' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '本次认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text || '') || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      columns25: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          width: 120,
          ellipsis: false,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: (<j-ellipsis value={String(text) || ''} />),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: '¥' + text,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '增加返利金额',
          dataIndex: 'RealTotalAddRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: '¥' + text,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: '¥' + text,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          // fixed: "right",
          // scopedSlots: { customRender: 'NumberNote' },
          customRender: (text, record, index) => {
            const obj = {
              children: (<span>¥{{ text }}</span>),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          // fixed: "right",
          // scopedSlots: { customRender: 'Note' },
          customRender: (text, record, index) => {
            const obj = {
              children: (<span>{{ text }}</span>),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
      ],
      columns26Year: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货应收数量',
          dataIndex: 'TotalAgreementRebateCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'RecognitionCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns26ShortSale: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'dayDate' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货应收数量',
          dataIndex: 'TotalAgreementRebateCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单可认款数量',
          dataIndex: 'TotalOrderRemainingCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'RecognitionCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columnsXY: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '核销方式',
          dataIndex: 'AccountingMethodStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'dayDate' },
        },
        {
          title: '政策数量',
          dataIndex: 'PolicyCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns_yufuTiaojia: [
        {
          title: '调价编号',
          dataIndex: 'PriceAdjustmentNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'BusinessCreator',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调价时间',
          dataIndex: 'AdjustmentTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调价金额',
          dataIndex: 'TotalPriceAdjustment',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columnsNoGoods: [
        {
          title: '协议编号',
          dataIndex: 'GeneralAgreementNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'dayDate' },
        },
        {
          title: '应返利金额',
          dataIndex: 'RebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '已认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'UnRecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'CurrentRecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columsGoods: [
        {
          title: '协议编号',
          dataIndex: 'GeneralAgreementNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利金额',
          dataIndex: 'RebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '已认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'UnRecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'CurrentRecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      PayerId: null,
      RemainingAmount: null,//计算的未认款金额
      curCheckXy: undefined,
      fanhuoPurchaseAgreementType: null,//返货认款协议类型 1年度 2短期销售
      TotalRecognitionAmount: 0,
      TotalRecognitionCount: null,
      selectedRowKeys: [],
      selectedRowArray: [],
      httpHead: 'P36012',
      url: {
        info: '',
        info1: '/{v}/Recognition/GetSaleInfoAsync',//商销
        info2: '/{v}/Recognition/GetPurchaseRefundInfoAsync',//采购退货
        info3: '/{v}/Recognition/GetShortAgreementInfoAsync',//短期采购
        info4: '/{v}/Recognition/GetShortSaleAgreementInfoAsync',//短期销售
        info5: '/{v}/Recognition/GetYearAgreementInfoAsync',//年度协议
        info6: '/{v}/Recognition/GetPostingInfoAsync',//过账认款
        info7: '',//现金池 无详情
        info8: '',//票折池 无详情
        info9: '/{v}/RecognitionReturnGoods/GetInfoAsync',//返货池
        info10: '/{v}/RecognitionSpecial/GetReturnGoodsInfoAsync',//特殊认款返货抵钱
        info11: '/{v}/RecognitionSpecial/GetSpecialReceiptInfoAsync',//特殊认款现金和票折
        info_yufuTiaojia: '/{v}/PurchasePriceAdjust/GetInfo',//采购预付款调价认款
        info20: '/{v}/RecognitionYLT/GetRecognitionInfo',//优利泰
        info21: '/{v}/RecognitionGeneralAgreement/GetRecognitionGeneralAgreementDetail',//通用协议
        info23: '/{v}/RecognitionPrepaymentRefund/GetRecognitionInfo',//预付款退款认款
      },
      YLTDeatil: null
    };
  },
  mounted() {

  },
  created() { },
  methods: {
    moment,
    show(source, record, isRecord = false) {
      console.log('source ====  ',source)
      this.source = source
      this.record = record
      this.initData()
      this.title = '认款详情';
      //1 商销认款 2采购退货认款 3短期采购协议 4短期销售协议 5年度协议 6过账认款 7现金 8 票折 9过账
      // 认款记录详情 RecognitionOrderMode 详情也可以判断认款是什么类型
      // 返货抵钱 = 1, //特殊
      // 现金抵返货 = 2, //特殊
      // 票折抵返货 = 3, //特殊
      // 商销认款 = 10,
      // 返货认款 = 16,
      // 采购退货认款 = 11,
      // 短期采购协议认款 = 12,
      // 短期销售协议认款 = 13,
      // 年度协议认款 = 14,
      // 过账 = 15
      // 采购预付调价认款 = 17
      // 优利泰 = 18
      // 通用协议 = 21
      // 预付款退款认款 = 23
      switch (source) {
        case 1: this.xyTitle = '商销订单'; this.columns = this.columnsCommon; this.columns2 = this.columns21; this.url.info = this.url.info1; break;
        case 2: this.xyTitle = '采购退货单'; this.columns = this.columnsCommon; this.columns2 = this.columns22; this.url.info = this.url.info2; break;
        case 3: this.xyTitle = '短期采购协议'; this.columns = this.columnsCommon; this.columns2 = this.columns23; this.url.info = this.url.info3; break;
        case 4: this.xyTitle = '短期销售协议'; this.columns = this.columnsCommon; this.columns2 = this.columns24; this.url.info = this.url.info4; break;
        case 5: this.xyTitle = '本次抵扣明细'; this.columns = this.columnsCommon; this.columns2 = this.columns25; this.url.info = this.url.info5; break;//年度协议
        case 6: this.xyTitle = '过账认款'; this.columns = this.columnsCommon; this.url.info = this.url.info6; break;
        case 7: this.xyTitle = '现金池'; //注：特殊1,2,3在特殊认款详情里面展示
          break;//无详情
        case 8: this.xyTitle = '票折池'
          break;//无详情
        case 9: this.url.info = this.url.info9; break;//返货池
        case 11: this.xyTitle = '采购预付款调价'; this.columns = this.columnsCommon; this.columns2 = this.columns_yufuTiaojia; this.url.info = this.url.info_yufuTiaojia; break;//采购预付款调价认款
        case 20: this.columns = this.columnsCommon; this.url.info = this.url.info20; break;//优利泰认款
        default: this.columns = this.columnsCommon; this.xyTitle = ''
      }
      switch (record.RecognitionOrderMode) {
        case 10: this.source = 1; this.xyTitle = '商销订单'; this.columns = this.columnsCommon; this.columns2 = this.columns21; this.url.info = this.url.info1; break;//商销
        case 16: this.url.info = this.url.info9; break;//返货
        case 11: this.source = 2; this.xyTitle = '采购退货单'; this.columns = this.columnsCommon; this.columns2 = this.columns22; this.url.info = this.url.info2; break;//采购退货
        case 12: this.source = 3; this.xyTitle = '短期采购协议'; this.columns = this.columnsCommon; this.columns2 = this.columns23; this.url.info = this.url.info3; break;//短期采购协议
        case 13: this.source = 4; this.xyTitle = '短期销售协议'; this.columns = this.columnsCommon; this.columns2 = this.columns24; this.url.info = this.url.info4; break;//短期销售协议
        case 14: this.source = 5; this.xyTitle = '本次抵扣明细'; this.columns = this.columnsCommon; this.columns2 = this.columns25; this.url.info = this.url.info5; break;//年度协议
        case 15: this.source = 6; this.xyTitle = '过账认款'; this.columns = this.columnsCommon; this.url.info = this.url.info6; break;//过账
        case 17: this.source = 11; this.xyTitle = '采购预付调价认款'; this.columns = this.columnsCommon; this.columns2 = this.columns_yufuTiaojia; this.url.info = this.url.info_yufuTiaojia; break;//采购预付调价认款
        case 18: this.source = 20; this.columns = this.columnsCommon; this.url.info = this.url.info20; break;//优利泰
        case 19: this.source = 21; this.columns = this.columnsCommon; this.url.info = this.url.info21; break;//通用协议
        case 20: this.source = 23; this.columns = this.columnsCommon; this.url.info = this.url.info23; break;//预付款退款认款

      }
      this.getInfo()
      this.visible = true;
    },
    initData() {
      this.model = {
        PayerId: '',
        Payer: '',
      }
      this.dataSource = []
      this.dataSource2 = []
    },
    getInfo() {
      let params = {
        Id: this.record.Id,
      }
      if (this.source == 21) {
        params.recognitionGeneralAgreementId = this.record.Id
      }
      this.confirmLoading2 = true
      getAction(this.url.info, params, this.httpHead).then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.model = res.Data
        switch (this.source) {
          case 1://商销订单
            this.dataSource = [res.Data.ReceiptOrder]
            this.dataSource2 = res.Data.SaleOrderList || []
            break;
          case 2://采购退货单
            this.dataSource = [res.Data.ReceiptOrder]
            this.dataSource2 = res.Data.PurchaseRefundOrderList || []
            break;
          case 3://短期采购协议
            this.dataSource = [res.Data.ReceiptOrder]
            this.dataSource2 = res.Data.ShortAgreementDetail || []
            break;
          case 4://短期销售协议
            this.dataSource = [res.Data.ReceiptOrder]
            // 重组数据
            let resData = res.Data.ShortSaleAgreementDetail || []
            this.dataSource2 = this.setSource4RowSpan(resData) || []
            break;
          case 5://年度协议
            this.dataSource = [res.Data.ReceiptOrder]
            // 数据重组
            let goodsArray = []
            this.AmountAllNumVal = 0
            if (res.Data.PolicyList.length > 0) {
              res.Data.PolicyList.map((item) => {
                // console.log('res.Data.PolicyList',res.Data.PolicyList)
                if(item.GoodsList.length === 0){
                  item.GoodsList = [item]
                }
                item.GoodsList.map((rItem, rIndex) => {
                  if (rIndex == 0) {
                    rItem.PolicyContent = item.PolicyContent //促销内容
                    rItem.TotalRebateAmount = item.TotalRebateAmount //应返利金额
                    rItem.RealTotalAddRebateAmount = item.RealTotalAddRebateAmount //增加返利金额
                    rItem.CancelRebateAmount = item.CancelRebateAmount //取消返利金额
                    rItem.RecognitionAmount = item.RecognitionAmount //本次认款金额
                    rItem.Note = item.Note
                    rItem.rowSpan = item.GoodsList.length
                    this.AmountAllNumVal += rItem.NumberNote
                  } else {
                    rItem.rowSpan = 0
                    rItem.TotalRebateAmount = 0//应返利金额
                    // rItem.CancelRebateAmount = 0//取消返利金额
                    rItem.RecognitionAmount = 0 //本次认款金额
                  }
                  goodsArray.push(rItem)
                })
              })
            }
            this.dataSource2 = goodsArray || []
            let dataSourceXY = [
              {
                AgreementNo: res.Data.AgreementNo,
                PartyFirstName: res.Data.PartyFirstName,
                PartyFirstId: res.Data.PartyFirstId,
                AccountingMethodStr: res.Data.AccountingMethodStr,
                AgreementStartTime: res.Data.AgreementStartTime,
                AgreementEndTime: res.Data.AgreementEndTime,
                RebateTime: res.Data.RebateTime,
                PolicyCount: res.Data.PolicyCount,
              }
            ]
            this.dataSourceXY = dataSourceXY
            break;
          case 9:
            this.dataSource = [res.Data.PurchaseOrderInfoDto] //返货采购订单
            this.columns = this.columnsPurchase //返货采购订单
            this.fanhuoPurchaseAgreementType = res.Data.PurchaseAgreementType
            if (res.Data.PurchaseAgreementType == 1) {
              this.xyTitle = '协议明细';
              this.dataSourceXY = [res.Data.SpecialyearAgreementDto]
              this.columns2 = this.columns26Year;
              this.dataSource2 = res.Data.SpecialyearAgreementDto.AgreementDetail
            } else if (res.Data.PurchaseAgreementType == 2) {
              this.xyTitle = '短期销售协议';
              this.dataSourceXY = []
              this.columns2 = this.columns26ShortSale;
              this.dataSource2 = res.Data.ShortSaleAgreementList
            }
            break;
          case 11://采购预付款调价
            this.dataSource = [res.Data.ReceiptOrder]
            this.dataSource2 = res.Data.PurchasePriceAdjustList || []
            break;
          case 20://优利泰
            this.dataSource = [res.Data.ReceiptOrder]
            this.YLTDeatil = res.Data
            break;
          case 21://通用协议
            this.xyTitle = '通用协议';
            this.dataSource = [res.Data.ReceiptOrder]
            if (res.Data.IsCreateByGoods) {
              this.columns2 = this.columsGoods
              this.dataSource2 = res.Data.RecognitionGeneralAgreementGoodsDetailList || []
            } else {
              this.columns2 = this.columnsNoGoods
              this.dataSource2 = res.Data.RecognitionGeneralAgreementItemList || []
            }
            break;
          default:
            this.dataSource = [res.Data.ReceiptOrder]
        }
        this.setTotalRecognitionAmount()

      }).finally(() => {
        this.confirmLoading2 = false
      })
    },
    setSource4RowSpan(resData) {
      if (resData && resData.length > 0) {
        // 相同的值排序
        resData = resData.sort((a, b) => {
          return a.PurchaseAgreementPolicyId > b.PurchaseAgreementPolicyId ? 1 : -1
        })

        let groupedArrObj = resData.reduce((acc, obj) => {
          let key = obj.PurchaseAgreementPolicyId;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push(obj);
          return acc;
        }, {});
        let samePolicyIdArray = Object.entries(groupedArrObj) || []
        if (samePolicyIdArray.length > 0) {
          resData.map(item => {
            let rt = samePolicyIdArray.find(r => {
              return item.PurchaseAgreementPolicyId == r[0] && r[1].length > 1
            })
            if (rt) {
              item.GoodsList = rt[1]
            }

          })
          resData.map((item, index) => {
            if (item.GoodsList && item.GoodsList.length > 0) {
              item.GoodsList.map((rItem, rIndex) => {
                if (rIndex == 0) {
                  rItem.rowSpan = item.GoodsList.length
                } else {
                  rItem.rowSpan = 0
                }
              })
            }
          })
          console.log('resData', resData)
        }
        return resData
      }
    },
    // 计算合计
    setTotalRecognitionAmount() {
      console.log(this.source, this.dataSource2)
      if (this.dataSource2.length > 0) {
        this.TotalRecognitionAmount = 0
        this.TotalRecognitionCount = 0
        let dataSource2 = []
        if (this.source == 4) {
          dataSource2 = this.dataSource2.filter((item) => {
            return item.rowSpan > 0 || item.rowSpan == undefined
          })
        } else {
          dataSource2 = this.dataSource2
        }
        dataSource2.map((item) => {
          let num = (item.TotalRecognitionAmount || item.RecognitionAmount) + (item.AdditionalAmount || 0)
          this.TotalRecognitionAmount += num
          this.TotalRecognitionCount += item.RecognitionCount
        })
      }
    },
    // table行的颜色
    rowClassNameFun(record, index) {
      if (record.RecognitionAmount < 0) {
        return 'table-color-red-dust'
      }
    },
    modelOk(list) {

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.model = {};
      this.visible = false;
    },
  },
};
</script>
<style lang="less" scoped>
.xieyi,
.skd {
  font-size: 14px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}

.title-btn {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  justify-items: center;
}

.title-btn .xieyi {
  padding-bottom: 0;
}

.edit-btn {
  color: #1890ff;
  margin-left: 10px;
  cursor: pointer;
}

.newXieyi::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
  margin-top: 20px;
}
</style>
