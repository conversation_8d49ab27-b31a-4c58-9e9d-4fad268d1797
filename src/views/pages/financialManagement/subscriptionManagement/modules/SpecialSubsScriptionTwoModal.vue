<template>
  <a-form-model ref="form" :model="model" layout="vertical">
    <div>
      <template>
        <div class="title-btn">
          <div class="xieyi">收款单：</div>
          <div>
            <a-button type="primary" @click="chooseTableData(1)">选择</a-button>
          </div>
        </div>
        <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns1" :dataSource="dataSource1" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource1.length === 0 ? false:'350px',x:'1000px' }">
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
          <!-- 价格显示-->
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
          </span>
        </a-table>
      </template>
      <!-- 本次认款金额 -->
      <template>
        <a-form-model-item style="margin-top:20px;" label="本次认款金额：" required>
          <a-input-number style="width:100%;" placeholder="请输入" v-model="model.RecognitionAmount" :min="0" :max="dataSource1.length>0?dataSource1[0].UnRecognizedAmount:10000000" @change="handleChange"/>
        </a-form-model-item>
      </template>
      <hr style="border: none;border-top: 1px dashed #ccc;">
      <!-- 选择协议 -->
      <SpecialSubTypeBase ref="SpecialSubTypeBase" :rebateType="rebateType"></SpecialSubTypeBase>

      <!--现金抵票折、现金抵返货 收款单单选-->
      <TableSelectDataModal type="radio" ref="TableSelectDataModal" :width="1100" :selectTableData="selectTableData" isToSelectClose @chooseData="chooseData"></TableSelectDataModal>

    </div>
  </a-form-model>

</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  mixins: [EditMixin],
  name: "SpecialSubsScriptionTwoModal",
  components: {
    JEllipsis
  },
  props: {
    // 1返货抵钱 2现金抵返货 3票折抵返货
    rebateType: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      confirmLoading: false,
      confirmLoading2: false,
      saveLoading: false,
      AmountAllNumVal: null,
      model: {
        Id: '',
        RecognitionAmount: '',
        Note: '',
      },
      rebateTypeList: [],
      selectTableData: {
        searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '收款单号', type: 'input', value: '', key: 'ReceiptNo', defaultVal: '', placeholder: '请输入' },
          { name: '打款方', type: 'input', value: '', key: 'Payer', defaultVal: '', placeholder: '请输入' },
        ],
        title: '选择收款单',
        name: '收款单',
        recordKey: 'Id',
        httpHead: 'P36012',
        isInitData: false,
        url: {
          listType: 'POST',
          list: '/{v}/CashReceiptNew/GetCashList',
        },
        columns: [
          {
            title: '收款单号',
            dataIndex: 'ReceiptNo',
            width: 180,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '收款日期',
            dataIndex: 'ReceiptTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '收款方式',
            dataIndex: 'ReceiptTypeStr',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '打款方',
            dataIndex: 'Payer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '收款账户',
            dataIndex: 'AccountName',
            width: 188,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },

          {
            title: '备注',
            dataIndex: 'Note',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '收款金额',
            dataIndex: 'ReceiptAmount',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'price' }
          },
          {
            title: '认款金额',
            dataIndex: 'RecognizedAmount',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'price' }
          },
          {
            title: '待认款金额',
            dataIndex: 'RemainingAmount',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'price' }
          },
          {
            title: '创建人',
            dataIndex: 'CreateByName',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '创建时间',
            dataIndex: 'CreateTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '状态',
            dataIndex: 'RecognitionStatusStr',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' }
          }
        ],
      },
      columns1: [
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款日期',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款方式',
          dataIndex: 'ReceiptTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款账户',
          dataIndex: 'AccountName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'UnRecognizedAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },

      ],
      columns2: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalOrderRebateCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'TotalOrderRemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'Count',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'Count' },
        },
      ],
      dataSource1: [],
      dataSource2: [],
      xyInfo5Data: {},
      xyListHttpHead: 'P36007',
      url: {
        xyInfo: '/{v}/PurchaseAgreement/GetInfoAsync',//年度协议认款
      }
    }
  },
  created() {

  },
  methods: {
    // 重组数据
    setSelectTableDataUrl() {
      if (this.rebateType == 2) {
        this.selectTableData.url.list = '/{v}/CashReceiptNew/GetCashList'
      } else if (this.rebateType == 3) {
        this.selectTableData.url.list = '/{v}/CashReceiptNew/GetInvoiceDiscountReceipList'
      }
    },
    // 清空数据
    clearInfoData() {
      this.dataSource1 = []
      this.dataSource2 = []
      this.AmountAllNumVal = null
      this.model = {
        Id: '',
        RecognitionAmount: '',
        Note: '',
      }
      this.$nextTick(() => {
        this.$refs.SpecialSubTypeBase.clearInfoData()
      })
    },
    chooseTableData(type) {
      this.tableType = type
      let queryParam = {
        NeedToRecognize: true
      }
      this.$refs.TableSelectDataModal.show(this.dataSource1, queryParam)
    },
    handleChange(value) {
      this.model.RecognitionAmount = this.getAmountOfMoney(value)
    },
    // 选择的数据
    chooseData(data) {
      if (data && data.length > 0) {
        this.dataSource1 = data;
        this.dataSource3 = [];
      }
      // 初始化清空数据
      this.model = {
        Id: '',
        RecognitionAmount: '',
        Note: '',
      }
      this.$refs.SpecialSubTypeBase.init()
    },
    checkDataOne() {
      if (this.dataSource1.length == 0) {
        this.$message.warning('请选择收款单')
        return false
      } else if (!this.model.RecognitionAmount) {
        this.$message.warning('请填写本次认款金额')
        return false
      } else if (this.dataSource1.length > 0 && (!this.model.RecognitionAmount > this.dataSource1[0].UnRecognizedAmount)) {
        this.$message.warning('本次认款金额不能大于未认款金额')
        return false
      }
      else {
        return true
      }
    },
    checkData(callback) {
      if (this.checkDataOne()) {
        this.$refs.SpecialSubTypeBase.checkDataTwo((data) => {
          data.dataSource1 = this.dataSource1
          data.dataSource2 = this.dataSource2
          data.thisRecognitionAmount = this.model.RecognitionAmount
          callback && callback(data)
        })
      }
    },
  },
}
</script>

<style lang="less" scoped>
.xieyi {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}
.xieyi::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

.title-btn {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  justify-items: center;
  align-items: center;
}

.title-btn .xieyi {
  padding-bottom: 0;
}
</style>