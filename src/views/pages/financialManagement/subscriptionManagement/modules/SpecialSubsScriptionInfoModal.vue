<!-- 特殊认款详情 -->
<template>
  <a-modal :title="title" :width="1000" :visible="visible" :saveLoading="saveLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form">
        <!-- 基本信息 -->
        <div class="xieyi">基本信息：</div>
        <a-descriptions title="">
          <a-descriptions-item label="认款单号">{{ model.RecognitionOrderNo || ' -- ' }}</a-descriptions-item>
          <!-- <a-descriptions-item label="抵扣类型">{{ model.RecognitionOrderTypeStr || ' -- ' }}</a-descriptions-item> -->
          <!-- <a-descriptions-item label="审核状态">{{ model.AuditStatusStr || ' -- ' }}</a-descriptions-item> -->
          <a-descriptions-item label="认款方式">{{ model.RecognitionOrderModeStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="认款人">{{ model.CreateByName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ model.CreateTime || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="兑付方">{{ model.Payer || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="协议类型">{{ model.PurchaseAgreementTypeStr || ' -- ' }}</a-descriptions-item>
          <!-- <a-descriptions-item label="备注">{{ model.Note || ' -- ' }}</a-descriptions-item> -->
        </a-descriptions>
        <!-- 抵钱 -->
        <div v-if="RebateType == 1">
          <div class="xieyi">返货采购订单：</div>
          <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns1" :dataSource="dataSource1" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource1.length === 0 ? false:'200px',x:'1000px' }">
            <!-- 字符串超长截取省略号显示-->
            <template slot="component" slot-scope="text">
              <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
              <j-ellipsis :value="text" v-else />
            </template>
            <!-- 日期 -->
            <span slot="dayDate" slot-scope="text">
              <j-ellipsis :value="text?text.substring(0,11):''" />
            </span>
            <!-- 价格显示-->
            <span slot="price" slot-scope="text">
              <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
            </span>
          </a-table>

          <div class="xieyi" style="margin-top:16px;">本次认款明细：(本次合计认款数量：{{model.TotalRecognitionCount || 0}})</div>
          <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns2" :dataSource="dataSource2" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource2.length === 0 ? false:'200px',x:'1000px' }">
            <!-- 字符串超长截取省略号显示-->
            <template slot="component" slot-scope="text">
              <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
              <j-ellipsis :value="text" v-else />
            </template>
            <!-- 日期 -->
            <span slot="dayDate" slot-scope="text">
              <j-ellipsis :value="text?text.substring(0,11):''" />
            </span>
            <!-- 价格显示-->
            <span slot="price" slot-scope="text">
              <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
            </span>
          </a-table>

          <!-- 年度协议标题 -->
          <div style="margin-top:16px;" v-if="RebateType == 1 && PurchaseAgreementType == 1">
            <div class="xieyi">抵扣协议</div>
            <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns3" :dataSource="dataSource3" :pagination="false" :rowClassName="rowClassNameFun" :loading="confirmLoading" :scroll="{ y: dataSource3.length === 0 ? false:'350px',x:'1000px' }">
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 日期 -->
              <span slot="dayDate" slot-scope="text">
                <j-ellipsis :value="text?text.substring(0,11):''" />
              </span>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
              </span>
            </a-table>
          </div>

          <!-- 协议类型明细 -->
          <div style="margin-top:16px;">
            <div class="xieyi">{{xyTitle}}</div>
            <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns4" :dataSource="dataSource4" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource4.length === 0 ? false:'350px',x:'1000px' }">
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 日期 -->
              <span slot="dayDate" slot-scope="text">
                <j-ellipsis :value="text?text.substring(0,11):''" />
              </span>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
              </span>
            </a-table>
          </div>

          <!-- 审核信息 -->
          <div class="xieyi" style="margin-top:16px;">审核信息</div>
          <div>
            <CusAuditInformation ref="CusAuditInformation" :isDefault="false" v-if="model.AuditId" :AuditId="model.AuditId" />
          </div>
        </div>
        <!-- 抵货 -->
        <div v-if="RebateType == 2 || RebateType == 3">
          <div class="xieyi">收款单：</div>
          <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns1Goods" :dataSource="dataSource1" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource1.length === 0 ? false:'200px',x:'1000px' }">
            <!-- 字符串超长截取省略号显示-->
            <template slot="component" slot-scope="text">
              <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
              <j-ellipsis :value="text" v-else />
            </template>
            <!-- 日期 -->
            <span slot="dayDate" slot-scope="text">
              <j-ellipsis :value="text?text.substring(0,11):''" />
            </span>
            <!-- 价格显示-->
            <span slot="price" slot-scope="text">
              <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
            </span>
          </a-table>

          <div style="margin-top: 15px;color: rgba(0, 0, 0, 0.85);">本次认款金额：{{model.TotalRecognitionAmount || 0}}</div>

          <!-- 年度协议标题 -->
          <div style="margin-top:16px;" v-if="PurchaseAgreementType == 1">
            <div class="xieyi">抵扣协议</div>
            <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns3Goods" :dataSource="dataSource3" :pagination="false" :rowClassName="rowClassNameFun" :loading="confirmLoading" :scroll="{ y: dataSource3.length === 0 ? false:'350px',x:'1000px' }">
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 日期 -->
              <span slot="dayDate" slot-scope="text">
                <j-ellipsis :value="text?text.substring(0,11):''" />
              </span>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
              </span>
            </a-table>
          </div>

          <!-- 协议类型明细 -->
          <div style="margin-top:16px;">
            <div class="xieyi">{{xyTitle}}</div>
            <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns4" :dataSource="dataSource4" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource4.length === 0 ? false:'350px',x:'1000px' }">
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 日期 -->
              <span slot="dayDate" slot-scope="text">
                <j-ellipsis :value="text?text.substring(0,11):''" />
              </span>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
              </span>
            </a-table>
          </div>

        </div>

      </a-form-model>
    </a-spin>

    <!-- 审核弹框 -->
    <AuditRemarkModal ref="iAuditRemarkModal" passFormTitle="备注" @ok="handleAuditModalOk" />
    <!-- 审核按钮 -->
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <div v-if="IsShowBtn">
        <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handlePass" type="primary">审核通过</a-button>
        <a-button type="danger" @click="handleNoPass">
          审核驳回
        </a-button>
      </div>
      <a-button v-else @click="handleCancel">
        关闭
      </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import Vue from 'vue'
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { USER_ID } from '@/store/mutation-types'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";

export default {
  name: "SpecialSubsScriptionInfoModal",
  mixins: [ListMixin, EditMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "审核特殊认款单",
      visible: false,
      model: {
        PayerId: '',
        Payer: '',
      },
      recordModel: {},
      confirmLoading: false,
      confirmLoading2: false,
      saveLoading: false,
      xyTitle: '',
      record: {},
      dataSource1: [],
      dataSource2: [],
      dataSource3: [],
      dataSource4: [],
      columns: [],
      columns1: [
        {
          title: '采购订单号',
          dataIndex: 'PurchaseOrderNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalReturnCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'PreRemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns1Goods: [
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款日期',
          dataIndex: 'ReceiptTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款方式',
          dataIndex: 'ReceiptTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款账户',
          dataIndex: 'AccountName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '未认款金额',
        //   dataIndex: 'RemainingAmount',
        //   width: 150,
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'component' },
        // },
      ],
      columns2: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalReturnCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'RecognitionCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns3: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '核销方式',
          dataIndex: 'AccountingMethodStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 300,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'dayDate' },
        },
        {
          title: '政策数量',
          dataIndex: 'PolicyCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns3Goods: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '核销方式',
          dataIndex: 'AccountingMethodStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 300,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'dayDate' },
        },
        {
          title: '政策数量',
          dataIndex: 'PolicyCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns4: [],
      columns4Year: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '增加返利金额',
          dataIndex: 'RealTotalAddRebateAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '已认款金额',
        //   dataIndex: 'RecognitionAmount',
        //   width: 120,
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'component' },
        // },
        {
          title: '本次认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns4ShortSale: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'dayDate' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns4ShortPurchase: [
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '日期',
          dataIndex: 'CreateTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '到期日',
          dataIndex: 'ExpirationTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '实际入库数量',
          dataIndex: 'InboundQuantity',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '每盒返利金额',
          dataIndex: 'RebatePrice',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns4GoodsYear: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返货数量',
          dataIndex: 'TotalRebateCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返货数量',
          dataIndex: 'CancelRebateCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '已认款数量',
        //   dataIndex: 'PreRemainingCount',
        //   width: 150,
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'component' },
        // },
        {
          title: '本次认款数量',
          dataIndex: 'RecognitionCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns4GoodsShortSale: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'dayDate' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利数量',
          dataIndex: 'TotalReturnCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利数量',
          dataIndex: 'CancelRebateCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'RecognitionCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      PayerId: null,
      RemainingAmount: null,//计算的未认款金额
      RebateType: null,//1返货抵钱	2现金抵返货 3票折抵返货
      PurchaseAgreementType: null,//1年度协议 2短期销售 3短期采购
      ApprovalWorkFlowInstanceId: null,
      IsHasSuperior: false,
      IsShowBtn: false,
      isAudit: false,
      isSingleOccupy: false,//是否成功占用
      isAuditInfo: false,//是否是审核的详情
      httpHead: 'P36012',
      url: {
        info: '',
        info1: '/{v}/RecognitionSpecial/GetReturnGoodsInfoAsync',//返货抵钱
        info2: '/{v}/RecognitionSpecial/GetSpecialReceiptInfoAsync',//现金和票折
        authInfo: '/{v}/RecognitionSpecial/GetAuditsInfoAsync',//审核
        getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',//获取审核节点
      },
    };
  },
  mounted() {

  },
  created() { },
  methods: {
    moment,
    show(record, type, RebateType, isAuditInfo) {
      this.record = record
      this.RebateType = RebateType ? RebateType : record.RebateType
      this.PurchaseAgreementType = record.PurchaseAgreementType
      this.isAudit = type == 'audit' ? true : false
      this.isAuditInfo = isAuditInfo
      this.title = type == 'audit' ? '审核特殊认款单' : '特殊认款单详情'
      this.initData()
      //1 商销认款 2采购退货认款 3短期采购协议 4短期销售协议 5年度协议 6过账认款
      // 认款记录详情 RecognitionOrderMode
      // 返货抵钱 = 1,
      // 现金抵返货 = 2,
      // 票折抵返货 = 3,
      // 商销认款 = 10,
      // 返货认款 = 16,
      // 采购退货认款 = 11,
      // 短期采购协议认款 = 12,
      // 短期销售协议认款 = 13,
      // 年度协议认款 = 14,
      // 过账 = 15
      // if (this.RebateType == 1) {
      //   this.url.info = this.url.info1;
      //   switch (this.PurchaseAgreementType) {
      //     case 1: this.xyTitle = '本次抵扣明细'; this.columns4 = this.columns4Year; break;
      //     case 2: this.xyTitle = '短期销售协议'; this.columns4 = this.columns4ShortSale; break;
      //     case 3: this.xyTitle = '短期采购协议'; this.columns4 = this.columns4ShortPurchase; break;
      //     default: this.xyTitle = ''
      //   }
      // } else {
      //   this.url.info = this.url.info2;
      //   switch (this.PurchaseAgreementType) {
      //     case 1: this.xyTitle = '本次抵扣明细'; this.columns4 = this.columns4GoodsYear; break;
      //     case 2: this.xyTitle = '短期销售协议'; this.columns4 = this.columns4GoodsShortSale; break;
      //     default: this.xyTitle = ''
      //   }
      // }
      this.getInfo()
      this.visible = true;
    },
    initData() {
      this.model = {}
      this.dataSource1 = []
      this.dataSource2 = []
      this.dataSource3 = []
      this.dataSource4 = []
    },
    handlePass() {
      this.$refs.iAuditRemarkModal.show(1, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
        AuditOpinion: this.model.AuditOpinion,
        IsHasSuperior: this.IsHasSuperior
      })
    },
    /**
  * @description: 驳回审核
  * @param {*}
  * @return {*}
  */
    handleNoPass() {
      this.$refs.iAuditRemarkModal.show(2, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
        AuditOpinion: this.model.AuditOpinion,
        IsHasSuperior: this.IsHasSuperior
      })
    },
    // IsHasSuperior
    getApprovalResults(bussinessNo) {
      if (!bussinessNo) return
      getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
        .then(res => {
          if (res.IsSuccess) {
            if (res.Data) {
              if (this.$refs.CusAuditInformation) {
                this.$refs.CusAuditInformation.setAuditList(res.Data)
              }
              this.IsHasSuperior = res.Data['IsHasSuperior'] //是否有上级 true 有上级 false 没有上级
              this.ApprovalWorkFlowInstanceId = res.Data['ApprovalWorkFlowInstanceId']
              let userId = Vue.ls.get(USER_ID)
              if ((res.Data['ApprovalUserIds'] || []).length && this.isAudit) {
                this.IsShowBtn = res.Data['ApprovalUserIds'].some(v => v == userId)
              } else {
                this.IsShowBtn = false
              }
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => { })
    },
    // 审核弹框回调
    handleAuditModalOk(AuditModalOkformData) {
      if (AuditModalOkformData) {
        this.$refs.iAuditRemarkModal.postAuditInstance(AuditModalOkformData, bool => {
          if (bool) {
            this.$emit('ok')
            this.handleCancel()
          }
        })
      } else {
        this.$emit('ok')
        this.handleCancel()
      }
    },
    getInfo() {
      let params = {
        id: this.record.Id,
      }
      this.confirmLoading2 = true
      if (this.RebateType == 1) {
        this.url.info = this.url.info1;
      } else {
        this.url.info = this.url.info2;
      }
      if (this.isAudit) {
        this.url.info = this.url.authInfo
      }
      getAction(this.url.info, params, this.httpHead).then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.model = res.Data
        this.PurchaseAgreementType = res.Data.PurchaseAgreementType
        // if (this.isAudit) this.getApprovalResults(this.model.AuditId)
        this.getApprovalResults(this.model.AuditId)
        if (this.RebateType == 1) {
          switch (this.PurchaseAgreementType) {
            case 1:
              this.xyTitle = '本次抵扣明细'; this.columns4 = this.columns4Year;
              this.dataSource1 = [res.Data.PurchaseOrderInfoDto] || [] //返货采购订单
              this.dataSource2 = res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails || [] //本次认款明细
              this.dataSource3 = [res.Data.SpecialyearAgreementDto] || [] //抵扣协议 年度协议
              // 还原政策和商品
              let goodsArray = []
              res.Data.SpecialyearAgreementDto.AgreementDetail.map((item, index) => {
                if(item.AgreementGoodsList.length){
                  item.AgreementGoodsList.map((rItem, rIndex) => {
                    rItem.PolicyContent = item.PolicyContent
                    rItem.TotalRebateAmount = item.TotalRebateAmount //应收的返利金额
                    rItem.CancelRebateAmount = item.CancelRebateAmount //取消的返利金额
                    rItem.PreRemainingAmount = item.PreRemainingAmount //应收的返利数量
                    rItem.CancelRebateCount = item.CancelRebateCount //取消的返利数量
                    rItem.RecognitionAmount = item.RecognitionAmount //认款金额
                    rItem.RebateType = item.RebateType//1返钱 2返货
                    rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
                    rItem.AgreementId = item.PurchaseAgreementId
                    rItem.Note = item.Note
                    rItem.RealTotalAddRebateAmount = item.RealTotalAddRebateAmount
                    if (rIndex == 0) {
                      rItem.rowSpan = item.AgreementGoodsList.length
                    } else {
                      rItem.rowSpan = 0
                    }
                    goodsArray.push(rItem)
                  })
                }else{
                  let obj = {}
                  obj.PolicyContent = item.PolicyContent
                  obj.TotalRebateAmount = item.TotalRebateAmount //应收的返利金额
                  obj.CancelRebateAmount = item.CancelRebateAmount //取消的返利金额
                  obj.PreRemainingAmount = item.PreRemainingAmount //应收的返利数量
                  obj.CancelRebateCount = item.CancelRebateCount //取消的返利数量
                  obj.RecognitionAmount = item.RecognitionAmount //认款金额
                  obj.RebateType = item.RebateType//1返钱 2返货
                  obj.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
                  obj.AgreementId = item.PurchaseAgreementId
                  obj.Note = item.Note
                  obj.RealTotalAddRebateAmount = item.RealTotalAddRebateAmount
                  obj.rowSpan = 0
                  goodsArray.push(obj)
                }
              })
              this.dataSource4 = goodsArray || [] //抵扣协议 年度协议明细
              break;
            case 2:
              this.xyTitle = '短期销售协议'; this.columns4 = this.columns4ShortSale;
              this.dataSource1 = [res.Data.PurchaseOrderInfoDto] || [] //返货采购订单
              this.dataSource2 = res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails || [] //本次认款明细
              this.dataSource4 = res.Data.ShortSaleAgreementList || []//短期销售协议
              break;
            case 3:
              this.xyTitle = '短期采购协议'; this.columns4 = this.columns4ShortPurchase;
              this.dataSource1 = [res.Data.PurchaseOrderInfoDto] || [] //返货采购订单
              this.dataSource2 = res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails || [] //本次认款明细
              this.dataSource4 = res.Data.ShortPurchaseAgreementList || []//短期采购协议
              break;
          }
        } else if (this.RebateType == 2 || this.RebateType == 3) {
          switch (this.PurchaseAgreementType) {
            case 1:
              this.xyTitle = '本次抵扣明细'; this.columns4 = this.columns4GoodsYear;
              this.dataSource1 = [res.Data.ReceiptOrder] || [] //收款单
              this.dataSource3 = [res.Data.YearAgreementReceiptInfo] || [] //抵扣协议 年度协议
              // 还原政策和商品
              this.dataSource4 = res.Data.YearAgreementReceiptInfo.AgreementGoods || [] //抵扣协议 年度协议明细
              break;
            case 2:
              this.xyTitle = '短期销售协议'; this.columns4 = this.columns4GoodsShortSale;
              this.dataSource1 = [res.Data.ReceiptOrder] || [] //收款单
              this.dataSource4 = res.Data.ShortSaleAgreementReceiptList || []//短期销售协议
              break;
          }
        }
      }).finally(() => {
        this.confirmLoading2 = false
      })
    },
    // table行的颜色
    rowClassNameFun(record, index) {
      if (record.RecognitionAmount < 0) {
        return 'table-color-red-dust'
      }
    },
    modelOk(list) {

    },
    // 关闭
    handleCancel() {
      if (this.isAudit && !this.isAuditInfo) {
        this.delSingleOccupyFun('/{v}/RecognitionSpecial/DeleteAuditOccupyAsync', {}, 'P36012', this.model.AuditId)
      }
      this.close();
    },
    close() {
      this.model = {};
      this.visible = false;
    },
  },
};
</script>
<style lang="less" scoped>
.xieyi,
.skd {
  font-size: 14px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}

.title-btn {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  justify-items: center;
}

.title-btn .xieyi {
  padding-bottom: 0;
}

.edit-btn {
  color: #1890ff;
  margin-left: 10px;
  cursor: pointer;
}
</style>
