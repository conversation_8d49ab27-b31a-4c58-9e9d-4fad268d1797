<!-- 新增特殊认款 -->
<template>
  <a-modal :title="title" :width="1000" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
        <a-row :getter="10">
          <a-col :md="24">
            <a-form-model-item label="认款类型：" prop="RebateType">
              <a-radio-group @change="handleInput" :value="model.RebateType">
                <template v-for="(item, key) in rebateTypeList">
                  <a-radio :key="key" :value="item.ItemValue">{{ item.ItemDescription }}</a-radio>
                </template>
              </a-radio-group>
            </a-form-model-item>
            <template v-if="model.RebateType==1||model.RebateType==2">
              <div class="title-btn">
                <div class="xieyi">返货采购订单：</div>
                <div>
                  <!-- <YYLButton menuId="" text="选择" @click="chooseTableData(1)" type="primary"/> -->
                  <a-button type="primary" @click="chooseTableData(1)">选择</a-button>
                </div>
              </div>
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns1" :dataSource="dataSource1" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource1.length === 0 ? false:'350px',x:'1000px' }">
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <template slot="action" slot-scope="text, record, index">
                  <a style="margin:0 5px;" @click="operate(record,'del1',index)">移除</a>
                </template>
                <!-- 价格显示-->
                <span slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </span>
              </a-table>
            </template>
            <template v-if="model.RebateType==3||model.RebateType==4">
              <div class="title-btn">
                <div class="xieyi">收款单：</div>
                <div>
                  <!-- <YYLButton menuId="" text="选择" @click="chooseTableData(4)" type="primary" /> -->
                  <a-button type="primary" @click="chooseTableData(4)">选择</a-button>
                </div>
              </div>
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns4" :dataSource="dataSource4" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource4.length === 0 ? false:'350px',x:'1000px' }">
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <template slot="action" slot-scope="text, record, index">
                  <a style="margin:0 5px;" @click="operate(record,'del4',index)">移除</a>
                </template>
                <!-- 价格显示-->
                <span slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </span>
              </a-table>
            </template>
            <template v-if="model.RebateType==5||model.RebateType==6">
              <div class="title-btn">
                <div class="xieyi">收款单：</div>
                <div>
                  <!-- <YYLButton menuId="" text="选择" @click="chooseTableData(7)" type="primary" /> -->
                  <a-button type="primary" @click="chooseTableData(7)">选择</a-button>
                </div>
              </div>
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns7" :dataSource="dataSource7" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource7.length === 0 ? false:'350px',x:'1000px' }">
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <template slot="action" slot-scope="text, record, index">
                  <a style="margin:0 5px;" @click="operate(record,'del7',index)">移除</a>
                </template>
                <!-- 价格显示-->
                <span slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </span>
              </a-table>
            </template>
            <template v-if="model.RebateType==1||model.RebateType==2">
              <div class="title-btn" style="padding-top: 20px;">
                <div class="xieyi">本次认款明细：（本次合计认款数量：{{ Total2 }}）</div>
              </div>
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns2" :dataSource="dataSource2" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource2.length === 0 ? false:'350px',x:'1000px' }">
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <!-- 价格显示-->
                <span slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </span>
                <template slot="Count" slot-scope="text, record">
                  <a-input-number v-model="record.Count" style="width:100%" :min="0" placeholder="请输入" @blur="(e)=>{onCountBlur(record,2)}" />
                </template>
              </a-table>
            </template>
            <template v-if="model.RebateType==1||model.RebateType==2">
              <div style="padding-top: 20px;" class="title-btn">
                <div class="xieyi">协议类型：</div>
                <div></div>
              </div>
              <a-select style="width: 60%" v-model="purchaseAgreementType" placeholder="请选择">
                <a-select-option value="">请选择</a-select-option>
                <a-select-option :value="1">年度协议</a-select-option>
                <a-select-option :value="2">短期销售协议</a-select-option>
                <a-select-option :value="3">短期采购协议</a-select-option>
              </a-select>
              <a-button style="position: relative;top: -1px;left: 10px;" type="primary" @click="chooseTypeTableData()">选择{{purchaseAgreementType==1?'年度协议':(purchaseAgreementType==2?'短期销售协议':(purchaseAgreementType==3?'短期采购协议':''))}}</a-button>
            </template>
            <template v-if="model.RebateType==1||model.RebateType==2">
              <div class="title-btn" style="padding-top: 20px;">
                <div class="xieyi">抵扣协议1：</div>
              </div>
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns3" :dataSource="dataSource3" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource3.length === 0 ? false:'350px',x:'1000px' }">
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <!-- 操作 -->
                <template slot="action" slot-scope="text, record, index">
                  <a style="margin:0 5px;" @click="operate(record,'del3',index)">移除</a>
                </template>
                <!-- 促销时间 -->
                <span slot="AgreementStartTimeVal" slot-scope="text, record">
                  <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
                </span>
                <!-- 价格显示-->
                <span slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </span>
              </a-table>
              <div style="margin-top:10px;">认款合计金额：¥{{AmountAllNumVal?parseFloat(AmountAllNumVal).toFixed(2):0}}</div>

              <div class="title-btn" style="padding-top: 20px;">
                <div class="xieyi">本次抵扣明细1：</div>
              </div>
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns6" :dataSource="dataSource2" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource2.length === 0 ? false:'350px',x:'1000px' }">
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <!-- 价格显示-->
                <span slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </span>
                <template slot="Count" slot-scope="text, record">
                  <a-input-number v-model="record.Count" style="width:100%" :min="0" placeholder="请输入" @blur="(e)=>{onCountBlur(record,2)}" />
                </template>
              </a-table>
            </template>
            <!-- <a-form-model-item :label="model.RebateType==1||model.RebateType==2?'本次抵扣金额：':'本次认款金额：'" prop="RecognitionAmount" style="padding-top: 20px;">
              <a-input placeholder="请输入" v-model="model.RecognitionAmount" :maxLength="50"></a-input>
            </a-form-model-item> -->
            <template v-if="model.RebateType==1||model.RebateType==2">
              <a-form-model-item style="margin-top:20px;" label="备注：" prop="Note">
                <a-textarea placeholder="请输入" v-model="model.Note" :maxLength="100" :rows="4"></a-textarea>
              </a-form-model-item>
            </template>
            <template v-if="model.RebateType==3||model.RebateType==5">
              <div class="title-btn" style="padding-top: 20px;">
                <div class="xieyi">抵扣协议：(本次合计抵扣金额：{{ Total5?'¥'+Total5:0 }})</div>
                <div>
                  <!-- <YYLButton menuId="" text="选择" @click="chooseTableData(5)" type="primary" /> -->
                  <a-button type="primary" @click="chooseTableData(5)">选择</a-button>
                </div>
              </div>
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns5" :dataSource="dataSource5" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource5.length === 0 ? false:'350px',x:'1000px' }">
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <!-- 操作 -->
                <template slot="Count" slot-scope="text, record">
                  <a-input-number v-model="record.Count" :precision="2" style="width:100%" :min="0" placeholder="请输入" @blur="(e)=>{onCountBlur(record,5)}" />
                </template>
                <template slot="Note" slot-scope="text, record">
                  <a-input v-model="record.Note" style="width:100%" placeholder="请输入" :maxLength="200" />
                </template>
                <!-- 促销时间 -->
                <span slot="AgreementStartTimeVal" slot-scope="text, record">
                  <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
                </span>
                <!-- 价格显示-->
                <span slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </span>
              </a-table>
            </template>
            <template v-if="model.RebateType==4||model.RebateType==6">
              <div class="title-btn" style="padding-top: 20px;">
                <div class="xieyi">抵扣协议：</div>
                <div>
                  <!-- <YYLButton menuId="" text="选择" @click="chooseTableData(33)" type="primary" /> -->
                  <a-button type="primary" @click="chooseTableData(33)">选择</a-button>
                </div>
              </div>
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns3" :dataSource="dataSource3" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource3.length === 0 ? false:'350px',x:'1000px' }">
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <!-- 操作 -->
                <template slot="action" slot-scope="text, record, index">
                  <a style="margin:0 5px;" @click="operate(record,'del3',index)">移除</a>
                </template>
                <!-- 促销时间 -->
                <span slot="AgreementStartTimeVal" slot-scope="text, record">
                  <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
                </span>
                <!-- 价格显示-->
                <span slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </span>
              </a-table>
              <div class="title-btn" style="padding-top: 20px;">
                <div class="xieyi">本次抵扣明细：（本次合计抵扣数量：{{ Total2 }}）</div>
              </div>
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns6" :dataSource="dataSource2" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource2.length === 0 ? false:'350px',x:'1000px' }">
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <!-- 价格显示-->
                <span slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </span>
                <template slot="Count" slot-scope="text, record">
                  <a-input-number v-model="record.Count" style="width:100%" :min="0" placeholder="请输入" @blur="(e)=>{onCountBlur(record,2)}" />
                </template>
              </a-table>
            </template>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button @click="save" type="primary" :loading="saveLoading">提交审核</a-button>
    </a-row>
    <!-- 表格选择数据弹窗 -->
    <!--返货抵现金、返货抵票折 返货采购订单单选-->
    <TableSelectDataModal type="radio" ref="TableSelectDataModal1" :selectTableData="selectTableData1" @chooseData="chooseData1"></TableSelectDataModal>
    <!--返货抵现金、返货抵票折 抵扣协议单选-->
    <TableSelectDataModal type="radio" ref="TableSelectDataModal3" :selectTableData="selectTableData3" @chooseData="chooseData3"></TableSelectDataModal>
    <!--现金抵票折、现金抵返货 收款单单选-->
    <TableSelectDataModal type="radio" ref="TableSelectDataModal4" :selectTableData="selectTableData4" @chooseData="chooseData4"></TableSelectDataModal>
    <!--票折抵现金、票折抵返货 收款单单选-->
    <TableSelectDataModal type="radio" ref="TableSelectDataModal7" :selectTableData="selectTableData7" @chooseData="chooseData7"></TableSelectDataModal>
    <!--现金抵票折、现金抵返货 抵扣协议多选-->
    <TableSelectDataModal ref="TableSelectDataModal5" :selectTableData="selectTableData5" @chooseData="chooseData5"></TableSelectDataModal>

    <!-- 协议类型选择 -->
    <TableSelectDataModal ref="TableSelectDataTypeModal" :selectTableData="selectTableTypeData" @chooseData="chooseTypeData"></TableSelectDataModal>
  </a-modal>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  mixins: [EditMixin],
  name: "SpecialSubsScriptionModal",
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "新增特殊认款",
      visible: false,
      model: {
        Id: '',
        RebateType: '1',
        RecognitionAmount: '',
        Note: '',
      },
      confirmLoading: false,
      saveLoading: false,
      httpHead: 'P36012',
      url: {
        add: '/{v}/SpecialRecognition/Recognition/RecognitionAsync',
        info: '/{v}/SpecialRecognition/GetInfo/GetInfoAsync',
        createAudit: '/{v}/ApprovalWorkFlowInstance/CreateApproval'
      },
      dataSource1: [],//返货采购订单-单选
      dataSource2: [],//本次认款明细-多选
      dataSource3: [],//抵扣协议-单选
      dataSource4: [],//收款单现金-单选
      dataSource5: [],//抵扣协议-多选
      dataSource7: [],//收款单票折-单选
      columns1: [
        {
          title: '采购订单号',
          dataIndex: 'PurchaseOrderNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalReturnGoodsCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'RemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 120,
          ellipsis: true,
          align: 'center',
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
        }
      ],
      columns2: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalOrderRebateCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'TotalOrderRemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'Count',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'Count' },
        },
      ],
      columns3: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '核算方式',
          dataIndex: 'AccountingMethodStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '现金应收金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RecognitionAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: "right",
          scopedSlots: { customRender: 'action' },
        }
      ],
      columns4: [
        {
          title: '收款单号',
          dataIndex: 'CashReceiptNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '兑付方',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: "right",
          scopedSlots: { customRender: 'action' },
        }
      ],
      columns5: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '核算方式',
          dataIndex: 'AccountingMethodStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '现金应收金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RecognitionAmount',
          width: 150,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '本次抵扣金额',
          dataIndex: 'Count',
          width: 150,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Count' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 150,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      columns6: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货应收数量',
          dataIndex: 'TotalRebateCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未抵扣数量',
          dataIndex: 'TotalAgreementRemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次抵扣数量',
          dataIndex: 'Count',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'Count' },
        },
      ],
      columns7: [
        {
          title: '收款单号',
          dataIndex: 'InvoiceDiscountNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '票折金额',
          dataIndex: 'TotalPriceInvoiceDiscount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RecognitionAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: "right",
          scopedSlots: { customRender: 'action' },
        }
      ],
      selectTableTypeData: {
        searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '协议编号', type: 'input', value: '', key: 'AgreementNo', defaultVal: '', placeholder: '请输入' },
          { name: '甲方', type: 'input', value: '', key: 'PartyFirstKey', defaultVal: '', placeholder: '名称/编号/拼音码' },
        ],
        title: '选择年度协议',
        name: '年度协议',
        recordKey: 'Id',
        httpHead: 'P36007',
        isInitData: false,
        url: {
          list: '/{v}/PurchaseAgreement/GetUnsubscribedRecognitionListAsync',
        },
        columns: [
          {
            title: '协议编号',
            dataIndex: 'AgreementNo',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '甲方',
            dataIndex: 'PartyFirstName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '核算方式',
            dataIndex: 'AccountingMethodStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '促销时间',
            dataIndex: 'AgreementStartTime',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'AgreementStartTimeVal' }
          },
          {
            title: '返利方式',
            dataIndex: 'AgreementRebateTypeStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '返利时间',
            dataIndex: 'RebateTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '责任人',
            dataIndex: 'ResponsiblePersonName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '创建时间',
            dataIndex: 'CreateTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: "right",
            scopedSlots: { customRender: 'action' },
          }
        ]
      },

      selectTableData1: {
        searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '采购订单号', type: 'input', value: '', key: 'PurchaseOrderNo', defaultVal: '', placeholder: '请输入' },
          { name: '供应商', type: 'input', value: '', key: 'SupplierKey', defaultVal: '', placeholder: '请输入' },
        ],
        title: '返货采购订单',
        name: '返货采购订单',
        recordKey: 'Id',
        httpHead: 'P36009',
        isInitData: false,
        url: {
          list: '/{v}/PurchaseOrder/GetRecognizedListV2Async',
        },
        columns: [
          {
            title: '采购订单号',
            dataIndex: 'PurchaseOrderNo',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '供应商',
            dataIndex: 'SupplierName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '返货数量',
            dataIndex: 'TotalReturnGoodsCount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '已认款数量',
            dataIndex: 'RecognizedCount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '未认款数量',
            dataIndex: 'RemainingCount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '订单归属人',
            dataIndex: 'OwnerByName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '入库时间',
            dataIndex: 'WarehouseInTime',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: "right",
            scopedSlots: { customRender: 'action' },
          }
        ],
      },
      selectTableData3: {
        searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '协议编号', type: 'input', value: '', key: 'AgreementNo', defaultVal: '', placeholder: '请输入' },
          { name: '甲方', type: 'input', value: '', key: 'PartyFirstName', defaultVal: '', placeholder: '请输入' },
        ],
        title: '选择采购协议',
        name: '采购协议',
        recordKey: 'PurchaseAgreementId',
        httpHead: 'P36007',
        isInitData: false,
        url: {
          list: '/{v}/PurchaseAgreement/GetUnsubscribedRecognitionListAsync',
        },
        columns: [
          {
            title: '协议编号',
            dataIndex: 'AgreementNo',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '甲方',
            dataIndex: 'PartyFirstName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '核算方式',
            dataIndex: 'AccountingMethodStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '促销时间',
            dataIndex: 'AgreementStartTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'AgreementStartTimeVal' },
          },
          {
            title: '返利方式',
            dataIndex: 'AgreementRebateTypeStr',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '返利时间',
            dataIndex: 'RebateTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '归属人',
            dataIndex: 'CreateByName',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '创建时间',
            dataIndex: 'CreateTime',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: "right",
            scopedSlots: { customRender: 'action' },
          }
        ],
      },
      selectTableData4: {
        searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '收款单号', type: 'input', value: '', key: 'ReceiptNo', defaultVal: '', placeholder: '请输入' },
          { name: '打款方', type: 'input', value: '', key: 'PartyFirstName', defaultVal: '', placeholder: '请输入' },
        ],
        title: '选择收款单',
        name: '收款单',
        recordKey: 'PurchaseOrderId',
        httpHead: 'P36012',
        isInitData: false,
        url: {
          list: '/{v}/CashReceipt/GetListAsync',
        },
        columns: [
          {
            title: '收款单号',
            dataIndex: 'CashReceiptNo',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '打款方',
            dataIndex: 'Payer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '收款银行',
            dataIndex: 'BankName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '收款卡号',
            dataIndex: 'AccountNumber',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '票折金额',
            dataIndex: 'ReceiptAmount',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '已认款金额',
            dataIndex: 'RecognizedAmount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '未认款金额',
            dataIndex: 'RemainingAmount',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '备注',
            dataIndex: 'Note',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '归属人',
            dataIndex: 'CreateByName',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建时间',
            dataIndex: 'CreateTime',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: "right",
            scopedSlots: { customRender: 'action' },
          }
        ],
      },
      selectTableData7: {
        searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '收款单号', type: 'input', value: '', key: 'InvoiceDiscountNo', defaultVal: '', placeholder: '请输入' },
          { name: '供应商', type: 'input', value: '', key: 'SupplierKeyWords', defaultVal: '', placeholder: '请输入' },
        ],
        title: '选择收款单',
        name: '收款单',
        recordKey: 'PurchaseOrderId',
        httpHead: 'P36009',
        isInitData: false,
        url: {
          list: '/{v}/PurchaseBusiness/QueryPurchaseInvoiceDiscountRecognizedList',
        },
        columns: [
          {
            title: '收款单号',
            dataIndex: 'InvoiceDiscountNo',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '供应商',
            dataIndex: 'SupplierName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '票折金额',
            dataIndex: 'TotalPriceInvoiceDiscount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '已认款金额',
            dataIndex: 'RecognizedAmount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '未认款金额',
            dataIndex: 'RecognitionAmount',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '归属人',
            dataIndex: 'OwnerByName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建时间',
            dataIndex: 'CreateTime',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: "right",
            scopedSlots: { customRender: 'action' },
          }
        ],
      },
      selectTableData5: {
        searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '协议编号', type: 'input', value: '', key: 'AgreementNo', defaultVal: '', placeholder: '请输入' },
          { name: '甲方', type: 'input', value: '', key: 'PartyFirstName', defaultVal: '', placeholder: '请输入' },
        ],
        title: '选择采购协议',
        name: '采购协议',
        recordKey: 'PurchaseAgreementId',
        httpHead: 'P36007',
        isInitData: false,
        url: {
          list: '/{v}/PurchaseAgreement/GetUnsubscribedRecognitionListAsync',
        },
        columns: [
          {
            title: '协议编号',
            dataIndex: 'AgreementNo',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '甲方',
            dataIndex: 'PartyFirstName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '核算方式',
            dataIndex: 'AccountingMethodStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '促销时间',
            dataIndex: 'AgreementStartTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'AgreementStartTimeVal' },
          },
          {
            title: '返利方式',
            dataIndex: 'AgreementRebateTypeStr',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '返利时间',
            dataIndex: 'RebateTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '归属人',
            dataIndex: 'CreateByName',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '创建时间',
            dataIndex: 'CreateTime',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: "right",
            scopedSlots: { customRender: 'action' },
          }
        ],
      },
      rebateTypeList: [],
      tableType: '',
      AmountAllNumVal: null,
      purchaseAgreementType: '',//1年度协议 2短期销售协议 3短期采购协议
    };
  },
  mounted() {

  },
  created() { },
  watch: {
    'model.RecognitionAmount'(val) {
      if (this.dataSource3 && this.dataSource3.length) {
        if (val > this.dataSource3[0].RecognitionAmount) {
          this.model.RecognitionAmount = this.dataSource3[0].RecognitionAmount;
          this.$message.warning('本次抵扣金额不能大于协议未认款金额');
        }
      }

      if (this.dataSource4 && this.dataSource4.length) {
        if (val > this.dataSource4[0].RemainingAmount) {
          this.model.RecognitionAmount = this.dataSource4[0].RemainingAmount;
          this.$message.warning('本次认款金额不能大于收款单未认款金额');
        }
      }

      if (this.dataSource7 && this.dataSource7.length) {
        let amount = Math.abs(this.dataSource7[0].RecognitionAmount)
        if (val > amount) {
          this.model.RecognitionAmount = amount;
          this.$message.warning('本次认款金额不能大于收款单未认款金额');
        }
      }
    },
    edit: false
  },
  computed: {
    rules() {
      return {
        RebateType: [{ required: true, message: '请选择!' }],
        RecognitionAmount: [{ required: true, validator: this.checkMoney }],
      }
    },
    Total2() {
      if (this.dataSource2 && this.dataSource2.length) {
        let total = 0
        this.dataSource2.forEach(e => {
          total += e.Count;
        });
        return total
      } else {
        return 0
      }
    },
    Total5() {
      if (this.dataSource5 && this.dataSource5.length) {
        let total = 0
        this.dataSource5.forEach(e => {
          total += e.Count;
        });
        return total
      } else {
        return 0
      }
    }
  },
  methods: {
    moment,
    show(record) {
      if (record && record.Id) {
        if (record.RebateType && (record.RebateType == 1 || record.RebateType == 2)) {
          this.getXyDetail(record.Id);
        }

        this.model.Id = record.Id;
        this.title = '编辑特殊认款';
        this.visible = true;
        this.edit = true;
        this.getType();
      } else {
        this.model.RebateType = '1';
        this.purchaseAgreementType = '';
        this.title = '新增特殊认款';
        this.visible = true;
        this.edit = false;
        this.getType();
      }
    },
    getXyDetail(id) {
      this.confirmLoading = true;
      getAction(this.url.info, { id: id, }, 'P36012').then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (!res.Data) return
        this.model.RebateType = res.Data.RebateType.toString();
        this.model.Note = res.Data.Note;
        if (this.model.RebateType == 1 || this.model.RebateType == 2) {
          this.model.RecognitionAmount = res.Data.TotalRecognitionAmount;
        }

        if (res.Data.ReturnGoodsInfo && res.Data.ReturnGoodsInfo.BusinessId) {
          this.dataSource1 = [{
            Id: res.Data.ReturnGoodsInfo.BusinessId,
            PurchaseOrderNo: res.Data.ReturnGoodsInfo.PurchaseOrderNo,
            SupplierName: res.Data.ReturnGoodsInfo.SupplierName,
            RemainingCount: res.Data.ReturnGoodsInfo.RemainingCount,
            SupplierId: res.Data.ReturnGoodsInfo.SupplierId,
            TotalReturnGoodsCount: res.Data.ReturnGoodsInfo.TotalOrderReturnCount
          }]

          this.getRecognizedDetail(res.Data.ReturnGoodsInfo.BusinessId, res.Data.ReturnGoodsInfo.GoodsList);
        }

        if (res.Data.AgreementInfo && res.Data.AgreementInfo.AgreementId) {
          if (res.Data.RebateType == 1 || res.Data.RebateType == 2) {
            this.dataSource3 = [{
              PurchaseAgreementId: res.Data.AgreementInfo.AgreementId,
              AgreementNo: res.Data.AgreementInfo.AgreementNo,
              PartyFirstName: res.Data.AgreementInfo.PartyFirstName,
              AccountingMethodStr: res.Data.AgreementInfo.AccountingMethodStr,
              AgreementStartTime: res.Data.AgreementInfo.AgreementStartTime,
              AgreementEndTime: res.Data.AgreementInfo.AgreementEndTime,
              TotalRebateAmount: res.Data.AgreementInfo.TotalRebateValue,
              RecognitionAmount: res.Data.AgreementInfo.PreRemainingValue,

            }]
          } else if (res.Data.RebateType == 3 || res.Data.RebateType == 4) {
            this.dataSource5 = [{
              PurchaseAgreementId: res.Data.AgreementInfo.AgreementId,
              AgreementNo: res.Data.AgreementInfo.AgreementNo,
              PartyFirstName: res.Data.AgreementInfo.PartyFirstName,
              AccountingMethodStr: res.Data.AgreementInfo.AccountingMethodStr,
              AgreementStartTime: res.Data.AgreementInfo.AgreementStartTime,
              AgreementEndTime: res.Data.AgreementInfo.AgreementEndTime,
              TotalRebateAmount: res.Data.AgreementInfo.TotalRebateValue,
              Note: res.Data.Note,
              Count: res.Data.TotalRecognitionAmount
            }]
          }

        }
      }).finally(() => {
        this.confirmLoading = false;
      })
    },
    getType() {
      getAction('/v1/SystemTool/GetEnumsByName', { enumName: 'EnumSpecialRecognitionRebateType' }, 'P36001').then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (!res.Data) return
        this.rebateTypeList = res.Data || [];
      })
    },
    handleInput(e) {
      if (this.model.RebateType != e.target.value) {
        this.model.RebateType = e.target.value;
        this.model.RecognitionAmount = '';
        this.model.Note = '';
        this.dataSource1 = [];
        this.dataSource2 = [];
        this.dataSource3 = [];
        this.dataSource4 = [];
        this.dataSource5 = [];
        this.dataSource7 = [];
      }

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.model = {
        RebateType: '1',
        RecognitionAmount: '',
        Note: '',
      };
      this.dataSource1 = [];
      this.dataSource2 = [];
      this.dataSource3 = [];
      this.dataSource4 = [];
      this.dataSource5 = [];
      this.dataSource7 = [];
      this.visible = false;
    },
    onCountBlur(record, type) {
      //本次认款明细 数量
      if (type == 2) {
        if (this.model.RebateType == 4) {
          if (record.Count > record.TotalAgreementRemainingCount) {
            record.Count = record.TotalAgreementRemainingCount;
          }
        } else {
          if (record.Count > record.RecognitionCount) {
            record.Count = record.RecognitionCount;
          }
        }
      } else if (type == 3) {
        if (record.Count > record.RecognitionAmount) {
          record.Count = record.RecognitionAmount;
        }
      } else if (type == 5) {
        if (record.Count > record.RecognitionAmount) {
          record.Count = record.RecognitionAmount;
        }
      }
    },
    //请求本次认款明细
    getRecognizedDetail(id, goods) {
      this.dataSource2 = [];
      getAction('/{v}/PurchaseAgreement/GetUnsubscribedRecognitionReturnGoodsDetailAsync', { purchaseOrderId: id }, 'P36007').then(res => {
        if (res.IsSuccess && res.Data) {
          if (res.Data.length) {
            if (goods && goods.length) {
              res.Data.forEach(e => {
                e.Count = '';
              })
              goods.forEach(e => {
                res.Data.forEach(e1 => {
                  if (e.GoodsId == e1.GoodsId) {
                    e1.Count = e.RecognitionCount
                  }
                })
              })
              this.dataSource2 = JSON.parse(JSON.stringify(res.Data))
            } else {
              this.dataSource2 = JSON.parse(JSON.stringify(res.Data))
            }
          }
        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
    //请求本次抵扣明细 现金抵返货 票折抵返货
    getRecognizedFhDetail(id) {
      this.dataSource2 = [];
      getAction('/{v}/PurchaseAgreement/GetUnsubscribedRecognitionAgreementDetailAsync', { agreementId: id }, 'P36007').then(res => {
        if (res.IsSuccess && res.Data) {
          if (res.Data.length) {
            res.Data.forEach(e => {
              e.Count = '';
            })
            this.dataSource2 = JSON.parse(JSON.stringify(res.Data))
          }
        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
    chooseTypeTableData() {
      if (!this.purchaseAgreementType) {
        this.$message.warning('请先选择协议类型')
        return
      }
      let queryParam = {}
      this.$refs.TableSelectDataTypeModal.show([], queryParam)
    },
    chooseTableData(type) {
      this.tableType = type;
      let queryParam = {};
      let AgreementRebateType = 1;//1现金 2票折 3返货;
      if (this.model.RebateType == 1 || this.model.RebateType == 5) {
        AgreementRebateType = 1
      } else if (this.model.RebateType == 2 || this.model.RebateType == 3) {
        AgreementRebateType = 2
      } else if (this.model.RebateType == 4 || this.model.RebateType == 6) {
        AgreementRebateType = 3
      }

      if (type == 1) {
        this.$refs.TableSelectDataModal1.show(this.dataSource1, queryParam)
      } else if (type == 3) {
        if (this.dataSource1 && this.dataSource1.length && this.dataSource1[0].SupplierId) {
          queryParam = {
            SupplierId: this.dataSource1[0].SupplierId,
            AgreementRebateType: AgreementRebateType
          }
        } else {
          this.$message.warning('请先选择返货采购订单');
          return false
        }

        this.$refs.TableSelectDataModal3.show(this.dataSource3, queryParam)
      } else if (type == 33) {
        queryParam = {
          AgreementRebateType: AgreementRebateType
        }

        this.$refs.TableSelectDataModal3.show(this.dataSource3, queryParam)
      } else if (type == 4) {
        this.$refs.TableSelectDataModal4.show(this.dataSource4, queryParam)
      } else if (type == 5) {
        queryParam = {
          AgreementRebateType: AgreementRebateType
        }

        this.$refs.TableSelectDataModal5.show(this.dataSource5, queryParam)
      } else if (type == 7) {
        this.$refs.TableSelectDataModal7.show(this.dataSource7, queryParam)
      }
    },
    chooseTypeData(data) {
      console.log(data)
    },
    // 选择的数据
    chooseData1(data) {
      if (data && data.length > 0) {
        this.dataSource1 = data;
        if (this.dataSource1 && this.dataSource1.length) {
          this.getRecognizedDetail(this.dataSource1[0].Id);
        }
        this.dataSource3 = [];
      }
    },
    chooseData3(data) {
      if (data && data.length > 0) {
        this.dataSource3 = data;
        if (this.tableType == 33) {
          this.getRecognizedFhDetail(this.dataSource3[0].PurchaseAgreementId)
        }
      }
    },
    chooseData4(data) {
      if (data && data.length > 0) {
        this.dataSource4 = data;
      }
    },
    chooseData5(data) {
      if (data && data.length > 0) {
        this.dataSource5 = this.dataSource5.concat(data);
      }
    },
    chooseData7(data) {
      if (data && data.length > 0) {
        this.dataSource7 = data;
      }
    },
    operate(record, type, index) {
      if (type == 'del1') {
        let that = this
        this.$confirm({
          title: '您确定要移除该订单吗?',
          content: '',
          onOk() {
            that.dataSource1.splice(index, 1);
            that.dataSource2 = [];
            that.dataSource3 = [];
          },
          onCancel() { },
        });
      } else if (type == 'del3') {
        let that = this
        this.$confirm({
          title: '您确定要移除该协议吗?',
          content: '',
          onOk() {
            that.dataSource3.splice(index, 1)
          },
          onCancel() { },
        });
      } else if (type == 'del4') {
        let that = this
        this.$confirm({
          title: '您确定要移除该收款单吗?',
          content: '',
          onOk() {
            that.dataSource4.splice(index, 1)
          },
          onCancel() { },
        });
      } else if (type == 'del7') {
        let that = this
        this.$confirm({
          title: '您确定要移除该收款单吗?',
          content: '',
          onOk() {
            that.dataSource7.splice(index, 1)
          },
          onCancel() { },
        });
      }
    },
    // 创建审核实例
    createApproval(params, callback) {
      this.saveLoading = true
      postAction(this.url.createAudit, params, 'P36005')
        .then(res => {
          if (res.IsSuccess) {
            this.$message.success('提交审核成功')
            callback && callback(true)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(e => {
          this.saveLoading = false
        })
        .finally(() => {
          this.saveLoading = false
        })
    },
    save() {
      this.$refs.form.validate((err, values) => {
        if (err) {
          let AgreementList = [];
          let OrderGoodsList = [];
          let BusinessId = ''
          if (this.model.RebateType == 1 || this.model.RebateType == 2) {
            if (this.dataSource1.length == 0) {
              this.$message.warning('请选择返货采购订单');
              return false;
            }

            BusinessId = this.dataSource1[0].Id;

            if (this.dataSource2.length == 0) {
              this.$message.warning('本次认款明细为空');
              return false;
            }

            let goodsList = this.dataSource2.filter(e => e.Count);
            if (goodsList.length) {
              let total = 0
              goodsList.forEach(e => {
                total += e.Count;
              });

              if (total > this.dataSource1[0].RemainingCount) {
                this.$message.warning('本次认款明细-未认款数量不能大于返货采购订单未认款数量');
                return false;
              }

              OrderGoodsList = this.dataSource2.map(e => {
                return {
                  GoodsId: e.GoodsId,
                  RecognitionCount: e.Count || 0,
                }
              })
            } else {
              this.$message.warning('本次认款明细-请输入本次商品认款数量');
              return false;
            }

            if (this.dataSource3.length == 0) {
              this.$message.warning('请选择抵扣协议');
              return false;
            }

            AgreementList = [
              {
                AgreementId: this.dataSource3[0].PurchaseAgreementId,
                Note: this.model.Note,
                RecognitionAmount: Number(this.model.RecognitionAmount),
                AgreementGoodsList: []
              }
            ]
          } else if (this.model.RebateType == 3 || this.model.RebateType == 5) {
            if (this.model.RebateType == 3) {
              if (this.dataSource4.length == 0) {
                this.$message.warning('请选择收款单');
                return false;
              }

              BusinessId = this.dataSource4[0].Id;
            } else {
              if (this.dataSource7.length == 0) {
                this.$message.warning('请选择收款单');
                return false;
              }

              BusinessId = this.dataSource7[0].Id;
            }


            if (this.dataSource5.length == 0) {
              this.$message.warning('请选择抵扣协议');
              return false;
            }

            let xyList5 = this.dataSource5.filter(e => e.Count);
            if (xyList5.length) {
              let total5 = 0
              xyList5.forEach(e => {
                total5 += e.Count;
              });
              if (total5 > this.model.RecognitionAmount) {
                this.$message.warning('抵扣协议-本次认款金额大于未认款金额');
                return false;
              }

              AgreementList = xyList5.map(e => {
                return {
                  AgreementId: e.PurchaseAgreementId,
                  Note: e.Note,
                  RecognitionAmount: e.Count,
                  AgreementGoodsList: []
                }
              })
            } else {
              this.$message.warning('抵扣协议-请输入本次认款金额');
              return false;
            }
          } else if (this.model.RebateType == 4 || this.model.RebateType == 6) {
            if (this.model.RebateType == 4) {
              if (this.dataSource4.length == 0) {
                this.$message.warning('请选择收款单');
                return false;
              }

              BusinessId = this.dataSource4[0].Id;
            } else {
              if (this.dataSource7.length == 0) {
                this.$message.warning('请选择收款单');
                return false;
              }

              BusinessId = this.dataSource7[0].Id;
            }

            if (this.dataSource2.length == 0) {
              this.$message.warning('本次认款明细为空');
              return false;
            }

            let goodsList = this.dataSource2.filter(e => e.Count);
            if (goodsList.length) {
              let total = 0
              goodsList.forEach(e => {
                total += e.Count;
              });

              // if(this.model.RebateType == 4){
              //   if(total>this.dataSource4[0].RemainingAmount){
              //     this.$message.warning('本次认款明细-未认款数量不能大于返货采购订单未抵扣数量');
              //     return false;
              //   }
              // }else{
              //   if(total>this.dataSource7[0].RecognitionAmount){
              //     this.$message.warning('本次认款明细-未认款数量不能大于返货采购订单未抵扣数量');
              //     return false;
              //   }
              // }

              AgreementList = [
                {
                  AgreementId: this.dataSource3[0].PurchaseAgreementId,
                  Note: '',
                  RecognitionAmount: Number(this.model.RecognitionAmount),
                  AgreementGoodsList: this.dataSource2.map(e => {
                    return {
                      PurchaseAgreementPolicyId: e.PurchaseAgreementPolicyId,
                      GoodsId: e.GoodsId,
                      RecognitionCount: e.Count || 0,
                    }
                  })
                }
              ]
            } else {
              this.$message.warning('本次认款明细-请输入本次商品抵扣数量');
              return false;
            }
          }
          this.saveLoading = true;
          postAction(this.url.add, {
            RebateType: Number(this.model.RebateType),
            AgreementList: AgreementList,
            ReturnGoodsOrderDetail: OrderGoodsList,
            BusinessId: BusinessId,
            Id: this.model.Id || undefined
          }, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              if (this.model.RebateType == '1' || this.model.RebateType == '2') {
                let params = {
                  BussinessNo: res.Data.AuditId,
                  MainBussinessNo: null,
                  Scenes: 23,
                  OpratorId: Vue.ls.get(USER_ID),
                  Remark: '特殊认款审核',
                }
                this.createApproval(params, () => {
                  this.$emit("ok");
                  this.close();
                })
              } else {
                this.$message.success("操作成功");
                this.$emit("ok");
                this.close();
              }
            } else {
              this.$message.warning(res.Msg);
            }
          }).finally(() => {
            this.saveLoading = false;
          });
        }
      });
    }
  },
};
</script>
<style lang="less" scoped>
.xieyi {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}
.xieyi::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

.title-btn {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  justify-items: center;
  align-items: center;
}

.title-btn .xieyi {
  padding-bottom: 0;
}
</style>
