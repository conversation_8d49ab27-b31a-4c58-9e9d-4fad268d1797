<template>
  <a-modal :title="title" :width="1100" :visible="visible" :saveLoading="saveLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="saveLoading">
      <div>
        <template>
          <div class="title-btn">
            <div class="xieyi">返货采购订单：</div>
          </div>
          <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns1" :dataSource="dataSource1" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource1.length === 0 ? false:'350px',x:'1000px' }">
            <!-- 字符串超长截取省略号显示-->
            <template slot="component" slot-scope="text">
              <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
              <j-ellipsis :value="text" v-else />
            </template>
            <!-- 价格显示-->
            <span slot="price" slot-scope="text">
              <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
            </span>
          </a-table>
        </template>
        <!-- 选择协议 -->
        <SpecialSubTypeBase ref="SpecialSubTypeBase" :rebateType="4" :source="9" :renkuanNum="dataSource1.length>0?(dataSource1[0].RemainingCount):''" :supplierId="dataSource1.length>0?(dataSource1[0].SupplierId):''" :purchaseOrderId="dataSource1.length>0?dataSource1[0].Id:''"></SpecialSubTypeBase>
      </div>
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button @click="handleOk" type="primary" :loading="saveLoading">确认</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import { USER_ID } from '@/store/mutation-types'
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  mixins: [EditMixin],
  name: "ReturnGoodsFunModal",
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "认款",
      visible: false,
      saveLoading: false,
      confirmLoading: false,
      source: 9,
      model: {
        Id: '',
        RebateType: '',
        RecognitionAmount: '',
        PayerId: '',
        Payer: '',
        Note: '',
      },
      columns1: [
        {
          title: '采购订单号',
          dataIndex: 'PurchaseOrderNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalReturnGoodsCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'RemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      dataSource1: [],
      httpHead: 'P36012',
      url: {
        add: '/{v}/RecognitionReturnGoods/CreateAsync',
      }
    }
  },
  created() {

  },
  methods: {
    show(record, columns, source) {
      this.source = 9
      this.dataSource1 = [record]
      this.visible = true
    },
    handleOk() {
      this.$refs.SpecialSubTypeBase.checkDataTwo((data) => {
        let Detials = []
        if (data.selectedRowArray && data.selectedRowArray.length > 0) {
          data.selectedRowArray.map((item) => {
            Detials.push({
              PurchaseAgreementId: item.PurchaseAgreementId,
              PurchaseAgreementPolicyId: item.PurchaseAgreementPolicyId,
              GoodsId: item.GoodsId,
              RecognitionCount: item.CountNote,
              Note: item.Note,
              RowIndex: item.RowIndex,
            })
          })
        }
        let formData = {
          "PurchaseOrderId": this.dataSource1[0].Id,
          "PurchaseAgreementType": data.purchaseAgreementType,
          "PayerId": data.PayerId,
          "Payer": data.Payer,
          "Note": data.Note,
          "Detials": Detials
        }
        this.saveLoading = true;
        postAction(this.url.add, formData, this.httpHead).then((res) => {
          if (res.IsSuccess) {
            this.$message.success("认款成功");
            this.$emit('ok')
            this.handleCancel();
          } else {
            this.$message.warning(res.Msg);
          }
        }).finally(() => {
          this.saveLoading = false;
        });

      })
    },
    handleCancel() {
      this.visible = false
    },
  },
}
</script>

<style lang="less" scoped>
.xieyi {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}
.xieyi::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

.title-btn {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  justify-items: center;
  align-items: center;
}

.title-btn .xieyi {
  padding-bottom: 0;
}
</style>