<!-- 现金认款单详情 -->
<template>
  <a-modal :title="title" :width="1000" :footer="null" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <a-descriptions :column="3">
        <a-descriptions-item label="认款单号">{{ model.RecognitionOrderNo || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="对付方">{{ model.Payer || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="认款类型">{{ model.CashRecognitionTypeStr||' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="认款人">{{ model.CreateByName||' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="认款时间">{{ model.CreateTime||' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="状态">{{ model.IsCancel?'已取消':'正常' }}</a-descriptions-item>
      </a-descriptions>
      <div class="xieyi" style="padding-top: 20px;">收款单：</div>
      <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns1" :dataSource="dataSource1" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource1.length === 0 ? false:'350px',x:'1000px' }">
        <!-- 字符串超长截取省略号显示-->
        <template slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </template>
        <span slot="price" slot-scope="text">
          <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
        </span>
      </a-table>
      <template v-if="model.CashRecognitionType == 1">
        <div class="xieyi" style="padding-top: 20px;">单据信息：</div>
        <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns2" :dataSource="dataSource2" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource2.length === 0 ? false:'350px',x:'1000px' }">
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
          </span>

          <!-- 促销时间 -->
          <span slot="AgreementStartTimeVal" slot-scope="{text,record}">
            <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
          </span>
        </a-table>
      </template>
      <template v-if="model.CashRecognitionType == 2">
        <div class="xieyi" style="padding-top: 20px;">单据信息：</div>
        <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns3" :dataSource="dataSource3" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource3.length === 0 ? false:'350px',x:'1000px' }">
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
          </span>
        </a-table>
      </template>
      <template v-if="model.CashRecognitionType == 3">
        <div class="xieyi" style="padding-top: 20px;">单据信息：</div>
        <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns4" :dataSource="dataSource4" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource4.length === 0 ? false:'350px',x:'1000px' }">
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
          </span>
        </a-table>
      </template>
      <a-descriptions style="padding-top: 20px;" :column="1">
        <a-descriptions-item label="本次认款金额"><span style="color: red">{{ model.RecognitionAmount?'¥'+model.RecognitionAmount:' -- ' }}</span></a-descriptions-item>
        <a-descriptions-item label="备注">{{ model.Note || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
    </a-spin>
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  mixins: [EditMixin],
  name: "CashScriptionInfo",
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "现金认款单详情",
      visible: false,
      model: {
        Note: ''
      },
      confirmLoading: false,
      httpHead: 'P36012',
      url: {
        info: '/{v}/CashRecognition/GetOrderInfoAsync',
      },
      dataSource1: [],
      dataSource2: [],
      dataSource3: [],
      dataSource4: [],
      columns1: [
        {
          title: '收款时间',
          dataIndex: 'ReceiptTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '银行名称',
          dataIndex: 'BankName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '收款账号',
          dataIndex: 'AccountNumber',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '可认款金额',
          dataIndex: 'RemainingAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
      columns2: [
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '核销方式',
          dataIndex: 'AccountingMethodStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '现金应收金额',
          dataIndex: 'TotalRebateAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
      columns3: [
        {
          title: '退货编号',
          dataIndex: 'RefundOrderNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'PurchaseRefundTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退货金额',
          dataIndex: 'TotalRefundAmout',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '剩余应退金额',
          dataIndex: 'RecognitionAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns4: [
        {
          title: '单据编号',
          dataIndex: 'BusinessNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessOrderTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'MainOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BusinessTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收金额',
          dataIndex: 'TotalAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: '[rice]' },
        },
        {
          title: '剩余应收金额',
          dataIndex: 'RecognitionAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
    };
  },
  mounted() {

  },
  created() { },
  methods: {
    moment,
    show(record) {
      this.model = record;
      this.visible = true;
      this.getXyDetail(record.RecognitionId);
    },
    getXyDetail(id) {
      getAction(this.url.info, { cashRecognitionId: id, }, 'P36012').then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (!res.Data) return

        this.model.Note = res.Data.Note || '';
        if (res.Data.outCashReceipt && res.Data.outCashReceipt.ReceiptTime) {
          this.dataSource1 = [{
            ReceiptTime: res.Data.outCashReceipt.ReceiptTime,
            Payer: res.Data.outCashReceipt.Payer,
            BankName: res.Data.outCashReceipt.BankName,
            AccountNumber: res.Data.outCashReceipt.AccountNumber,
            ReceiptAmount: res.Data.outCashReceipt.ReceiptAmount,
            RecognizedAmount: res.Data.outCashReceipt.RecognizedAmount,
            RemainingAmount: res.Data.outCashReceipt.RemainingAmount,
            RecognitionStatus: res.Data.outCashReceipt.RecognitionStatus,
            Note: res.Data.outCashReceipt.Note,
          }]
        }

        if (res.Data.ReturnGoodsInfoDto && res.Data.ReturnGoodsInfoDto.PurchaseOrderNo) {
          this.dataSource2 = [{
            PurchaseOrderNo: res.Data.ReturnGoodsInfoDto.PurchaseOrderNo,
            PurchaseRefundTime: res.Data.ReturnGoodsInfoDto.PurchaseRefundTime,
            RefundOrderNo: res.Data.ReturnGoodsInfoDto.RefundOrderNo,
            TotalRefundAmout: res.Data.ReturnGoodsInfoDto.TotalRefundAmout,
            TotalRecognitionAmount: res.Data.ReturnGoodsInfoDto.TotalRecognitionAmount,
            RecognitionAmount: res.Data.ReturnGoodsInfoDto.RecognitionAmount,
          }]
        }

        if (res.Data.AgreementInfoDto && res.Data.AgreementInfoDto.PurchaseAgreementId) {
          this.dataSource3 = [{
            PurchaseAgreementId: res.Data.AgreementInfoDto.PurchaseAgreementId,
            AgreementNo: res.Data.AgreementInfoDto.AgreementNo,
            PartyFirstName: res.Data.AgreementInfoDto.PartyFirstName,
            AccountingMethod: res.Data.AgreementInfoDto.AccountingMethod,
            AccountingMethodStr: res.Data.AgreementInfoDto.AccountingMethodStr,
            TotalRebateAmount: res.Data.AgreementInfoDto.TotalRebateAmount,
          }]
        }

        this.dataSource4 = res.Data.SalesOrderListDto || [];
      })
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.model = {};
      this.dataSource1 = [];
      this.dataSource2 = [];
      this.dataSource3 = [];
      this.dataSource4 = [];
      this.visible = false;
    },
  },
};
</script>
<style lang="less" scoped>
.xieyi,
.skd {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}
.xieyi::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

.title-btn {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  justify-items: center;
}

.title-btn .xieyi {
  padding-bottom: 0;
}
</style>
