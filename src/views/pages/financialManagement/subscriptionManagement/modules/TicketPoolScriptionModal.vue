<!-- 票折认款 -->
<template>
  <a-modal :title="title" :width="1000" :visible="visible" :saveLoading="saveLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <a-descriptions :column="4">
        <a-descriptions-item label="收款单号">{{ model.InvoiceDiscountNo || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="供应商">{{ model.SupplierName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="票折金额">{{ model.TotalPriceInvoiceDiscount?'¥'+model.TotalPriceInvoiceDiscount:' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="未认款金额">{{ model.RecognitionAmount?'¥'+model.RecognitionAmount:' -- ' }}</a-descriptions-item>
      </a-descriptions>
      <div class="xieyi" style="padding-top: 20px;">认款协议：（本次合计认款金额：{{ Total?'¥'+Total:0 }}）</div>
      <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource.length === 0 ? false:'350px',x:'1000px' }">
        <!-- 字符串超长截取省略号显示-->
        <template slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </template>
        <!-- 操作 -->
        <template slot="Count" slot-scope="text, record,index">
          <a-input-number
            v-model="record.Count"
            :precision="2"
            style="width:100%"
            :min="0"
            placeholder="请输入"
            @blur="(e)=>{onCountBlur(record,e)}"
          />
        </template>
        <template slot="Note" slot-scope="text, record,index">
          <a-input
            v-model="record.Note"
            style="width:100%"
            placeholder="请输入"
            :maxLength="200"
          />
        </template>
        <!-- 促销时间 -->
        <span slot="AgreementStartTimeVal" slot-scope="text, record,index">
          <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
        </span>
        <!-- 价格显示-->
        <span slot="price" slot-scope="text">
          <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
        </span>
      </a-table>
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button @click="save" type="primary" :loading="saveLoading">保存</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  mixins: [EditMixin],
  name: "TicketPoolScriptionModal",
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "票折认款",
      visible: false,
      model: {
        InvoiceDiscountId:'',
        RecognitionTypeByAgreementList:[]
      },
      confirmLoading: false,
      saveLoading:false,
      httpHead: 'P36012',
      url: {
        add:'/{v}/InvoiceDiscount/RecognitionAsync',
        xy:'/{v}/PurchaseAgreement/GetUnsubscribedRecognitionListAsync'
      },
      dataSource:[],
      columns: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '核算方式',
          dataIndex: 'AccountingMethodStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '票折应收金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RecognitionAmount',
          width: 150,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'Count',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Count' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
    };
  },
  mounted() { 

  },
  created() { },
  computed: {
    Total(){
      if(this.dataSource&&this.dataSource.length){
        let total = 0
        this.dataSource.forEach(e => {
          total += e.Count;
        });
        return total
      }else{
        return 0
      }
    }
  },
  methods: {
    moment,
    show(record) {
      this.model = record;
      this.model.InvoiceDiscountId = record.Id;
      this.title = '票折认款';
      this.visible = true;
      this.getXyDetail();
    },
    getXyDetail() {
      getAction(this.url.xy, {
        SupplierId:this.model.SupplierId,
        AgreementRebateType:2
       }, 'P36007').then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (!res.Data) return
        this.dataSource = res.Data||[];
        if(this.dataSource.length){
          this.dataSource.forEach(e=>{
            e.Count = '';
            e.Note = '';
          })
        }

        this.dataSource = JSON.parse(JSON.stringify(this.dataSource))
      })
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.model = {};
      this.visible = false;
    },
    onCountBlur(record){
      let amount = Math.abs(this.model.RecognitionAmount);
      let recognitionAmount = amount>record.RecognitionAmount?record.RecognitionAmount:amount
      if(record.Count>recognitionAmount){
        record.Count = recognitionAmount;
      }
    },
    save(){
      if(this.dataSource&&this.dataSource.length==0){
        this.$message.warning('没有协议不能保存');
        return false
      }

      let xy = this.dataSource.filter(e=>e.Count>0);
      if(xy.length){
        let total = 0
        xy.forEach(e => {
          total += e.Count;
        });

        if(total>Math.abs(this.model.RecognitionAmount)){
          this.$message.warning('认款协议-本次认款金额不能超过未认款金额');
          return false
        }

        this.model.RecognitionTypeByAgreementList = xy.map(e=>{
          return {
            PurchaseAgreementId:e.PurchaseAgreementId,
            RecognitionAmount:e.Count,
            Note:e.Note,
          }
        })
      }else{
        this.$message.warning('认款协议-请填写认款金额');
        return false
      }

      this.saveLoading = true;
      postAction(this.url.add, {
        InvoiceDiscountId:this.model.Id,
        RecognitionTypeByAgreementList:this.model.RecognitionTypeByAgreementList
      }, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          this.$message.success("操作成功");
          this.$emit("ok");
          this.close();
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {
        this.saveLoading = false;
      });
    }
  },
};
</script>
<style lang="less" scoped>
  .xieyi,.skd{
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 1.5;
    padding-bottom: 10px;
  }
  .xieyi::before{
    display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }

  .title-btn{
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    justify-items: center;
  }

  .title-btn .xieyi{
    padding-bottom: 0;
  }
</style>
