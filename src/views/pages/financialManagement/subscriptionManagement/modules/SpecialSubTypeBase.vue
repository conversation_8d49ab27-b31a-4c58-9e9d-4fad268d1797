<template>
  <div>
    <a-form-model ref="form" :model="model" layout="vertical">
      <template>
        <!-- purchaseAgreementType 1年度协议 2短期销售协议 3短期采购协议 -->
        <a-form-model-item style="padding-top: 20px; width: 600px" label="兑付方：" v-if="[2, 3].indexOf(purchaseAgreementType) >= 0 || rebateType != 1" required>
          <!-- <SingleInputSearchView placeholder="请输入供应商名称/编号/拼音码" width="600px;" :httpParams="{
              pageIndex: 1,
              pageSize: 200,
              AuditStatus:2,
              IsValid: true,
            }" keyWord="KeyWord" httpHead="P36003" :Url="url.supplierList" :value="model.PayerId" :dataKey="{ name: 'Name', value: 'Id' }" :name="model.Payer" :isAbsolute="true" @clear="clearSearchInput" @change="changeSearchInput" /> -->
          <div style="display: flex;width:100%;align-items: center">
            <SingleInputSearchView class="party-search" placeholder="请选择供应商/生产厂家" width="100%" :httpParams="{
                      pageIndex: 1,
                      pageSize: 200,
                      AuthBusiness: 2,
                      IsValid: true,
                    }" keyWord="KeyWord" disabled httpHead="P36003" :Url="url.supplierList" :noShowList="true" :value="model.PayerId" :dataKey="{ name: 'Name', value: 'Id' }" :name="model.Payer" :isAbsolute="true" @clear="clearSearchInput" @change="changeSearchInput" />
            <a @click="searchDataInput">选择</a>
          </div>
        </a-form-model-item>
        <div :style="{ paddingTop: purchaseAgreementType == 1 || !purchaseAgreementType ? '10px' : '0' }" class="title-btn">
          <div class="xieyi">协议类型：</div>
          <div></div>
        </div>
        <a-select style="width: 60%" v-model="purchaseAgreementType" @change="changeSelect" placeholder="请选择">
          <a-select-option value="">请选择</a-select-option>
          <a-select-option :value="1">年度协议</a-select-option>
          <a-select-option :value="2">短期销售协议</a-select-option>
          <a-select-option :value="3" v-if="rebateType == 1">短期采购协议</a-select-option>
        </a-select>
        <a-button style="position: relative; top: -1px; left: 10px" v-if="purchaseAgreementType == 1" type="primary" @click="chooseTypeTableData()">选择{{
            purchaseAgreementType == 1
              ? '年度协议'
              : purchaseAgreementType == 2
              ? '短期销售协议'
              : purchaseAgreementType == 3
              ? '短期采购协议'
              : ''
          }}</a-button>
      </template>
      <template v-if="purchaseAgreementType == 1">
        <div class="title-btn" style="padding-top: 20px">
          <div class="xieyi">{{ rebateType == 4 ? '年度协议：' : '抵扣协议：' }}</div>
        </div>
        <a-table :bordered="false" ref="table" :rowKey="(record, index) => index" size="middle" :columns="columns1" :dataSource="dataSource3" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource3.length === 0 ? false : '350px', x: '1000px' }">
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
          <!-- 日期 -->
          <span slot="dayDate" slot-scope="text">
            <j-ellipsis :value="text ? text.substring(0, 11) : ''" />
          </span>
          <!-- 操作 -->
          <template slot="action" slot-scope="text, record, index">
            <a style="margin: 0 5px" @click="operate(record, 'del3', index)">移除</a>
          </template>
          <!-- 促销时间 -->
          <span slot="AgreementStartTimeVal" slot-scope="text, record">
            <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
          </span>
          <!-- 价格显示-->
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
          </span>
        </a-table>
      </template>
      <template>
        <div class="title-btn" style="padding-top: 20px">
          <div class="xieyi">{{ xyTitle }}：</div>
        </div>
        <!-- 搜索 -->
        <SimpleSearchArea ref="SimpleSearchArea" v-if="searchInput.length > 0" :queryCol="8" :searchInput="searchInput" :key="purchaseAgreementType" @searchQuery="searchQuery" />
        <a-table :bordered="purchaseAgreementType == 1 ? true : false" ref="table" :size="purchaseAgreementType == 1 ? null : 'middle'" :rowKey="(record, index) => index" :columns="columns2" :dataSource="dataSource2" :pagination="false" :row-selection="rowSelectionInfo" :loading="confirmLoading" :scroll="{ y: dataSource2.length === 0 ? false : '350px', x: '1000px' }">
          <!-- 全选的checkbox -->
          <template slot="tableCheckboxTitle">
            <a-checkbox style="margin-top:5px" :value="checkAllCheckbox" v-model="checkAllCheckbox" @change="onAllcheckboxChange" />
          </template>
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
          <!-- 日期 -->
          <span slot="dayDate" slot-scope="text">
            <j-ellipsis :value="text ? text.substring(0, 11) : ''" />
          </span>
          <!-- 价格显示-->
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
          </span>
          <!-- 文本输入框 -->
          <template slot="Note" slot-scope="text, record">
            <a-input v-model="record.Note" :disabled="record.inputDisabled" style="width: 100%" placeholder="请输入" :maxLength="100" />
          </template>
          <!-- 金额输入框 -->
          <template slot="NumberNote" slot-scope="text, record">
            <a-input-number v-model="record.NumberNote" :disabled="record.inputDisabled" style="width: 100%" placeholder="请输入" :max="record.RemainingAmount" @change="inputNumberNote($event, record)" @blur="changeNumberNote($event, record)" />
          </template>
          <!-- 数量输入框 -->
          <template slot="CountNote" slot-scope="text, record">
            <a-input-number v-model="record.CountNote" :disabled="record.inputDisabled" style="width: 100%" placeholder="请输入" :min="0" @blur="changeCountNote($event, record)" />
          </template>
        </a-table>
        <div style="margin-top: 10px" v-if="rebateType == 1">
          认款合计金额：¥{{ AmountAllNumVal ? parseFloat(AmountAllNumVal).toFixed(2) : 0 }}
        </div>
        <div style="margin-top: 10px" v-else>
          认款合计数量：{{ AmountAllCountVal ? parseFloat(AmountAllCountVal) : 0 }}
        </div>
      </template>
      <template>
        <a-form-model-item style="margin-top: 20px" label="备注：">
          <a-textarea placeholder="请输入" v-model="model.Note" :maxLength="100" :rows="4"></a-textarea>
        </a-form-model-item>
      </template>
      <!-- 协议类型选择 -->
      <TableSelectDataModal ref="TableSelectDataTypeModal" :selectTableData="selectTableTypeData" isToSelectClose @chooseData="chooseTypeData"></TableSelectDataModal>
      <!-- 表格选择兑付方弹窗 -->
      <TableSelectDataModal ref="TableSelectDataModal" type="radio" :selectTabData="selectTabData" @chooseData="choosePayerData">
      </TableSelectDataModal>
    </a-form-model>
  </div>
</template>

<script>

import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  mixins: [EditMixin],
  name: 'SpecialSubTypeBase',
  components: {
    JEllipsis,
  },
  props: {
    // rebateType 1返货抵钱 2现金抵返货 3票折抵返货 4返货池认款
    // source 1 商销认款 2采购退货认款 3短期采购协议 4短期销售协议 5年度协议 6过账认款 9返货池
    rebateType: {
      type: Number,
      default: null,
    },
    supplierId: {
      type: String,
      default: '',
    },
    purchaseOrderId: {
      type: String,
      default: '',
    },
    source: {
      type: Number,
      default: null,
    },
    // 最大的未认款数量
    renkuanNum: {
      type: Number,
      default: null,
    },
    //编辑时候详情的对象
    infoData: {
      type: [Object, Array],
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      model: {
        PayerId: '',
        Payer: '',
      },
      searchInput: [],
      selectTabData: [
        {
          tabTitle: '供应商',
          selectTableData: {
            searchInput: [
              //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
              {
                name: '供应商',
                type: 'input',
                value: '',
                key: 'KeyWord',
                defaultVal: '',
                placeholder: '名称/编号/拼音码',
              },
            ],
            title: '选择供应商',
            name: '供应商',
            recordKey: 'Id',
            httpHead: 'P36003',
            isInitData: false,
            queryParam: {
              AuthBusiness: 2,
            },
            url: {
              list: '/{v}/Supplier/GetSuppliersForSelect',
              listType: 'GET',
            },
            columns: [
              {
                title: '供应商名称',
                dataIndex: 'Name',
                width: 200,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '供应商编号',
                dataIndex: 'Code',
                width: 150,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '注册地址',
                dataIndex: 'RegAddress',
                width: 150,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '操作',
                dataIndex: 'action',
                align: 'center',
                width: 100,
                fixed: 'right',
                scopedSlots: { customRender: 'action' },
              },
            ],
          },
        },
        {
          tabTitle: '生产厂家',
          selectTableData: {
            searchInput: [
              //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
              {
                name: '生产厂家',
                type: 'input',
                value: '',
                key: 'KeyWord',
                defaultVal: '',
                placeholder: '名称/编号/拼音码',
              },
            ],
            title: '选择生产厂家',
            name: '生产厂家',
            recordKey: 'Id',
            httpHead: 'P36006',
            isInitData: false,
            url: {
              list: '/{v}/BrandManufacturer/QueryBrandManufacturerList',
              listType: 'GET',
            },
            columns: [
              {
                title: '生产厂家名称',
                dataIndex: 'Name',
                width: 200,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '操作',
                dataIndex: 'action',
                align: 'center',
                width: 100,
                fixed: 'right',
                scopedSlots: { customRender: 'action' },
              },
            ],
          },
        }
      ],
      // 短期销售协议
      searchInput1: [
        {
          name: '协议编号',
          type: 'input',
          value: '',
          key: 'AgreementNo',
          defaultVal: '',
          placeholder: '请输入协议编号',
        },
        {
          name: '商品',
          type: 'input',
          value: '',
          key: 'GoodsNameKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码',
        },
      ],
      // 短期采购协议
      searchInput2: [
        {
          name: '订单编号',
          type: 'input',
          value: '',
          key: 'PurchaseOrderNo',
          defaultVal: '',
          placeholder: '请输入订单编号',
        },
        {
          name: '商品名称',
          type: 'input',
          value: '',
          key: 'ErpGoodsName',
          defaultVal: '',
          placeholder: '请输入商品名称',
        },
        {
          name: '商品编号',
          type: 'input',
          value: '',
          key: 'ErpGoodsCode',
          defaultVal: '',
          placeholder: '请输入商品编号',
        },
        {
          name: '生产厂商',
          type: 'input',
          value: '',
          key: 'BrandManufacturer',
          defaultVal: '',
          placeholder: '请输入生产厂商',
        },
      ],
      selectTableTypeData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '协议编号', type: 'input', value: '', key: 'AgreementNo', defaultVal: '', placeholder: '请输入' },
          {
            name: '甲方',
            type: 'input',
            value: '',
            key: 'PartyFirstKey',
            defaultVal: '',
            placeholder: '名称/编号/拼音码',
          },
        ],
        title: '选择年度协议',
        name: '年度协议',
        recordKey: 'Id',
        httpHead: 'P36007',
        isInitData: false,
        url: {
          list: '/{v}/PurchaseAgreement/GetUnsubscribedRecognitionListAsync',
        },
        columns: [
          {
            title: '协议编号',
            dataIndex: 'AgreementNo',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '甲方',
            dataIndex: 'PartyFirstName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '核算方式',
            dataIndex: 'AccountingMethodStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '促销时间',
            dataIndex: 'AgreementStartTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'AgreementStartTimeVal' },
          },
          {
            title: '返利方式',
            dataIndex: 'AgreementRebateTypeStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '返利时间',
            dataIndex: 'RebateTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'dayDate' },
          },
          {
            title: '责任人',
            dataIndex: 'ResponsiblePersonName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建时间',
            dataIndex: 'CreateTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
      columns1: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '核算方式',
          dataIndex: 'AccountingMethodStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'dayDate' },
        },
        {
          title: '政策数量',
          dataIndex: 'PolicyCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns2: [],
      columns21TeYear: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          width: 120,
          ellipsis: false,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '增加返利金额',
          dataIndex: 'RealTotalAddRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '已认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RemainingAmountVal',
          width: 120,
          ellipsis: true,
          // fixed: "right",
          // scopedSlots: { customRender: 'NumberNote' },
          customRender: (text, record, index) => {
            const obj = {
              // children: (<a-input-number style="width:100%" value={record.NumberNote} onchange={(e) => this.inputChage(e, text, record, index)} onblur={(e) => this.inputOnblur(e, text, record, index)} max={record.RemainingAmount} placeholder="请输入" />),
              children: (
                <input
                  type="number"
                  id="add"
                  class="input-class"
                  style="width:100%"
                  value={record.NumberNote}
                  onchange={(e) => this.inputChage(e, text, record, index)}
                  onblur={(e) => this.inputOnblur(e, text, record, index)}
                  max={record.RemainingAmount}
                  placeholder="请输入"
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          // fixed: "right",
          // scopedSlots: { customRender: 'Note' },
          customRender: (text, record, index) => {
            const obj = {
              // children: (<a-input style="width:100%" value={record ? record.Note : ''}  onblur={(e) => this.inputOnblur2(e, text, record, index)} placeholder="请输入" />),
              children: (
                <input
                  class="input-class"
                  style="width:100%"
                  value={record.Note}
                  onblur={(e) => this.inputOnblur2(e, text, record, index)}
                  placeholder="请输入"
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      columns21FanHuo: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货应收数量',
          dataIndex: 'TotalAgreementRebateCount',
          width: 150,
          ellipsis: false,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返货数量',
          dataIndex: 'CancelRebateCount',
          width: 150,
          ellipsis: false,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '协议未认款数量',
          dataIndex: 'TotalAgreementRemainingCount',
          width: 150,
          ellipsis: false,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单可认款数量',
          dataIndex: 'TotalOrderRemainingCount',
          width: 150,
          ellipsis: false,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'RecognitionCountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'CountNote' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      columns21TeYearGoods: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返货数量',
          dataIndex: 'TotalAgreementRebateCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返货数量',
          dataIndex: 'CancelRebateCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款数量',
          dataIndex: 'TotalAgreementRecognizedCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'TotalAgreementRemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'RecognitionCountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'CountNote' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      columns22TeShortSaleMoney: [
        {
          // title: '选择',
          slots: { title: 'tableCheckboxTitle' },
          dataIndex: 'checkbox',
          align: 'center',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children:
                <a-checkbox
                  v-model={record.checkbox}
                  style="margin-right:6px;"
                />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
          customCell: (record, index) => {
            return {
              on: {
                change: (e) => {
                  let selectedRowArray = []
                  let selectedRowKeys = []
                  this.dataSource2.map(item => {
                    if (item.checkbox == true) {
                      selectedRowArray.push(item)
                      selectedRowKeys.push(item.index)
                    }
                  })
                  this.selectedRowArray = selectedRowArray
                  this.selectedRowKeys = selectedRowKeys
                  this.onSelectChange(selectedRowKeys, selectedRowArray)
                },
              },
            };
          },
        },
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 150,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'dayDate' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '已认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RemainingAmountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          // scopedSlots: { customRender: 'NumberNote' },
          // <a-input-number v-model="record.NumberNote" :disabled="record.inputDisabled" style="width: 100%" placeholder="请输入" :max="record.RemainingAmount" @change="inputNumberNote($event, record)" @blur="changeNumberNote($event, record)" />
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <a-input-number
                  style="width:100%"
                  v-model={record.NumberNote}
                  disabled={record.inputDisabled}
                  onchange={(e) => this.inputNumberNote(e, record)}
                  onblur={(e) => this.changeNumberNote(e, record)}
                  min={record.RemainingAmount < 0 ? record.RemainingAmount : 0}
                  max={record.RemainingAmount > 0 ? record.RemainingAmount : 0}
                  placeholder="请输入"
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          // scopedSlots: { customRender: 'Note' },
          // <a-textarea placeholder="请输入" v-model="model.Note" :maxLength="100" :rows="4"></a-textarea>
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <a-input
                  style="width:100%"
                  v-model={record.Note}
                  maxLength={100}
                  placeholder="请输入"
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      // 返货池明细2
      columns22FanHuo: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'dayDate' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货应收数量',
          dataIndex: 'TotalAgreementRebateCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返货数量',
          dataIndex: 'CancelRebateCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '协议未认款数量',
          dataIndex: 'TotalAgreementRemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单可认款数量',
          dataIndex: 'TotalOrderRemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'RecognitionCountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'CountNote' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      // 短期销售协议特殊认款抵货明细2
      columns22TeShortSaleGoods: [
        {
          // title: '选择',
          slots: { title: 'tableCheckboxTitle' },
          dataIndex: 'checkbox',
          align: 'center',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children:
                <a-checkbox
                  v-model={record.checkbox}
                  style="margin-right:6px;"
                />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
          customCell: (record, index) => {
            return {
              on: {
                change: (e) => {
                  let selectedRowArray = []
                  let selectedRowKeys = []
                  this.dataSource2.map(item => {
                    if (item.checkbox == true) {
                      selectedRowArray.push(item)
                      selectedRowKeys.push(item.index)
                    }
                  })
                  this.selectedRowArray = selectedRowArray
                  this.selectedRowKeys = selectedRowKeys
                  this.onSelectChange(selectedRowKeys, selectedRowArray)
                },
              },
            };
          },
        },
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 120,
          ellipsis: true,
          // scopedSlots: { customRender: 'dayDate' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利数量',
          dataIndex: 'TotalRebateCount',
          width: 150,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '取消返货数量',
          dataIndex: 'CancelRebateCount',
          width: 150,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '已认款数量',
          dataIndex: 'RecognitionCount',
          width: 150,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '未认款数量',
          dataIndex: 'RemainingCount',
          width: 150,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '本次认款数量',
          dataIndex: 'RecognitionCountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          // scopedSlots: { customRender: 'CountNote' },
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <a-input-number
                  style="width:100%"
                  v-model={record.CountNote}
                  disabled={record.inputDisabled}
                  onblur={(e) => this.changeCountNote(e, record)}
                  min={0}
                  max={record.RemainingCount}
                  placeholder="请输入"
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          // scopedSlots: { customRender: 'Note' },
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <a-input
                  style="width:100%"
                  v-model={record.Note}
                  maxLength={100}
                  placeholder="请输入"
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      // 短期采购协议特殊认款抵货或者抵钱明细2
      columns23TeShortPurchase: [
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '日期',
          dataIndex: 'CreateTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '到期日',
          dataIndex: 'ExpirationTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '实际入库数量',
          dataIndex: 'InboundQuantity',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '每盒返利金额',
          dataIndex: 'RebatePrice',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款金额',
          dataIndex: 'UnrecognizedAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'RemainingAmountVal',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'NumberNote' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      dataSource3: [],
      dataSource2: [],
      xyInfoData: {}, //选择协议的数据
      selectedRowKeys: [],
      selectedRowArray: [],
      xyDataSource2ResData: [],
      confirmLoading: false,
      purchaseAgreementType: 1, //1年度协议 2短期销售 3短期采购
      AmountAllNumVal: 0, //认款合计金额
      AmountAllCountVal: 0, //认款合计数量
      RemainingAmount: null, //计算的未认款金额
      checkAllCheckbox: null,
      funCount: 0,
      xyTitle: '本次抵扣明细',
      xyListHttpHead: 'P36007',
      url: {
        xyInfo: '/{v}/PurchaseAgreement/GetInfoAsync', //年度协议认款
        supplierList: '/{v}/Supplier/GetSuppliersForSelect',
        xyInfo: '',
        xyInfo1: '/{v}/PurchaseAgreement/GetInfoAsync', //年度协议认款
        xyInfo2: '/{v}/PurchaseAgreement/GetPolicyGoodsList', //短期销售协议认款
        xyInfo3: '/{v}/PurchaseAgreement/GetSupplierPurchaseShortAgreements', //短期采购协议认款
        xyInfo1_1: '/{v}/RecognitionReturnGoods/GetUnsubscribedAgreementReturnGoodsListAsync', //返货池年度协议
        xyInfo2_1: '/{v}/RecognitionReturnGoods/GetUnsubscribedAgreementReturnGoodsListAsync', //返货池短期销售协议
        xyInfoTe1: '/{v}/PurchaseAgreement/GetYearAgreemenRebateGoodsListAsync', //特殊年度返货
        xyInfoTe2: '/{v}/PurchaseAgreement/GetShortAgreementRebateGoodsListAsync', //特殊短期销售返货
      },
    }
  },
  computed: {
    rowSelectionInfo() {
      if (this.purchaseAgreementType == 1) {
        return null
      } else if (this.purchaseAgreementType == 2) {
        return null
      } else {
        return { selectedRowKeys: this.selectedRowKeys, onChange: this.onSelectChange }
      }
    },
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.xyTitle = '本次抵扣明细'
      if (this.rebateType == 4) {
        this.xyTitle = '协议明细'
      }
      // 默认是年度协议
      this.purchaseAgreementType = 1
      this.dataSource3 = []
      this.dataSource2 = []
      this.model = {
        PayerId: '',
        Payer: '',
      }
      if (this.purchaseAgreementType == 1) {
        if (this.rebateType == 4) {
          this.xyListHttpHead = 'P36012'
          this.url.xyInfo = this.url.xyInfo1_1
          this.columns2 = this.columns21FanHuo
        } else if (this.rebateType == 2 || this.rebateType == 3) {
          this.xyListHttpHead = 'P36007'
          this.url.xyInfo = this.url.xyInfoTe1
          this.columns2 = this.columns21TeYearGoods
        } else {
          this.xyListHttpHead = 'P36007'
          this.url.xyInfo = this.url.xyInfo1
          this.columns2 = this.rebateType == 1 ? this.columns21TeYear : this.columns21TeYearGoods
        }
      }
      // 编辑的情况
      if (this.infoData && this.rebateType == 1) {
        this.dataSource3 = []
        this.dataSource2 = []
        this.purchaseAgreementType = this.infoData.PurchaseAgreementType //协议类型
        this.changeSelect() //还原协议类型
        this.model.Note = this.infoData.Note
        this.AmountAllNumVal = this.infoData.TotalRecognitionAmount
        switch (this.purchaseAgreementType) {
          case 1:
            this.dataSource3 = [this.infoData.SpecialyearAgreementDto] || [] //抵扣协议 年度协议
            this.xyInfoData = this.infoData.SpecialyearAgreementDto
            // 协议Id
            this.xyInfoData.PurchaseAgreementId = this.infoData.SpecialyearAgreementDto.AgreementId
            // 还原政策和商品
            let goodsArray = []
            if (this.infoData.SpecialyearAgreementDto.AgreementDetail.length > 0) {
              this.infoData.SpecialyearAgreementDto.AgreementDetail.map((item) => {
                item.AgreementGoodsList.map((rItem, rIndex) => {
                  rItem.PolicyContent = item.PolicyContent
                  rItem.TotalRebateAmount = item.TotalRebateAmount //应返利金额
                  rItem.CancelRebateAmount = item.CancelRebateAmount //取消返利金额
                  // rItem.TotalRecognitionAmount = item.PreRemainingAmount //已认款金额 编辑时
                  // rItem.RemainingAmount = this.numFixed(rItem.TotalRebateAmount - rItem.TotalRecognitionAmount) //未认款 应返利金额-已认款金额
                  rItem.TotalRecognitionAmount = this.numFixed(item.TotalRebateAmount - item.PreRemainingAmount) //已认款金额 编辑时
                  rItem.RemainingAmount = item.PreRemainingAmount //未认款金额编辑时
                  rItem.RebateType = item.RebateType //1返钱 2返货
                  rItem.Key = item.PurchaseAgreementPolicyId + rItem.GoodsId
                  rItem.Note = item.Note
                  rItem.NumberNote = item.RecognitionAmount //本次认款金额 编辑时
                  rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId //政策Id
                  rItem.PurchaseAgreementId = this.xyInfoData.PurchaseAgreementId //协议Id
                  if (rIndex == 0) {
                    rItem.rowSpan = item.AgreementGoodsList.length
                  } else {
                    rItem.rowSpan = 0
                  }
                  goodsArray.push(rItem)
                })
              })
            }
            this.dataSource2 = goodsArray
            let selectedRowArray = []
            goodsArray.map((item) => {
              if (item.rowSpan > 0) {
                selectedRowArray.push(item)
              }
            })
            this.selectedRowArray = selectedRowArray
            break
          case 2:
            // 还原兑付方
            this.model.Payer = this.infoData.Payer
            this.model.PayerId = this.infoData.PayerId
            if (this.infoData.ShortSaleAgreementList.length > 0) {
              this.infoData.ShortSaleAgreementList.map((item) => {
                item.TotalRecognitionAmount = item.RecognitionAmount //已认款金额
                item.RemainingAmount = this.numFixed(item.TotalRebateAmount - item.PreRemainingAmount) //未认款金额
                item.NumberNote = item.RecognitionAmount //本次认款金额赋值
              })
            }
            this.dataSource2 = this.infoData.ShortSaleAgreementList || []
            break
          case 3:
            // 还原兑付方
            this.model.Payer = this.infoData.Payer
            this.model.PayerId = this.infoData.PayerId
            if (this.infoData.ShortPurchaseAgreementList.length > 0) {
              this.infoData.ShortPurchaseAgreementList.map((item) => {
                item.TotalRecognitionAmount = item.RecognitionAmount //已认款金额
                item.RemainingAmount = this.numFixed(item.TotalRebateAmount - item.PreRemainingAmount) //未认款金额
                item.NumberNote = item.RecognitionAmount //本次认款金额赋值
              })
            }
            this.dataSource2 = this.infoData.ShortPurchaseAgreementList || []
        }
      }
    },
    getXyDetail(paramVal, callback) {
      let params = {}
      if (this.purchaseAgreementType == 1) {
        //   年度协议
        if (this.rebateType == 4) {
          // 返货池
          params = {
            agreementId: this.xyInfoData.PurchaseAgreementId,
            purchaseOrderId: this.purchaseOrderId,
            IsCanRecognition: true,
            PayerId: this.model.PayerId,
            purchaseAgreementType: 1,
            IsMyOrder: null,
            ...paramVal,
          }
        } else if (this.rebateType == 2 || this.rebateType == 3) {
          params = {
            purchaseAgreementId: this.xyInfoData.PurchaseAgreementId,
            IsCanRecognition: true,
            ...paramVal,
          }
        } else {
          // 其他
          params = {
            pageIndex: 1,
            pageSize: 99999,
            Id: this.xyInfoData.PurchaseAgreementId,
            IsCanRecognition: true,
            ...paramVal,
          }
        }
      } else if (this.purchaseAgreementType == 2) {
        //   短期销售
        if (this.rebateType == 4) {
          // console.log('返货池this.purchaseAgreementType ',this.purchaseAgreementType)
          // 返货池
          params = {
            agreementId: this.purchaseAgreementType ===1 ?this.xyInfoData.PurchaseAgreementId :null,
            purchaseOrderId: this.purchaseOrderId,
            PayerId: this.model.PayerId,
            purchaseAgreementType: 2,
            IsCanRecognition: true,
            IsMyOrder: true,
            ...paramVal,
          }
        } else if (this.rebateType == 2 || this.rebateType == 3) {
          params = {
            purchaseAgreementType: 2,
            agreementRebateType: this.rebateType == 1 ? 1 : 2, //1返钱 2返货
            partyId: this.model.PayerId,
            IsCanRecognition: true,
            SortFieldName: 'AgreementNo',
            ...paramVal,
          }
        } else {
          // 其他
          params = {
            pageIndex: 1,
            pageSize: 99999,
            SupplierId: this.model.PayerId,
            PurchaseAgreementType: 2,
            AgreementRebateType: this.rebateType == 1 ? 1 : 2,
            IsCanRecognition: true,
            SortFieldName: 'AgreementNo',
            ...paramVal,
          }
        }
      } else if (this.purchaseAgreementType == 3) {
        //   短期采购
        params = {
          pageIndex: 1,
          pageSize: 99999,
          PartyFirstId: this.model.PayerId,
          IsCanRecognition: true,
          ...paramVal,
        }
      }
      this.dataSource2 = []
      this.dataSource3 = []
      this.confirmLoading = true
      getAction(this.url.xyInfo, params, this.xyListHttpHead)
        .then((res) => {
          if (!res.IsSuccess) {
            this.$message.warning(res.Msg)
            return
          }
          if (!res.Data) return
          // 返货抵钱年度协议
          if (this.purchaseAgreementType == 1) {
            if (this.rebateType == 4) {
              // 返货
              this.AmountAllCountVal = 0
              res.Data.map((item, index) => {
                // 返货池
                if (this.rebateType == 4) {
                  if (item.TotalOrderRemainingCount > item.TotalAgreementRemainingCount) {
                    item.RemainingCount = item.TotalAgreementRemainingCount
                  } else {
                    item.RemainingCount = item.TotalOrderRemainingCount
                  }
                }
                item.CountNote = item.RemainingCount
                this.AmountAllCountVal += item.CountNote
              })
              this.dataSource2 = res.Data
              this.dataSource3 = [this.xyInfoData]
              this.selectedRowArray = res.Data
            } else if (this.rebateType == 2 || this.rebateType == 3) {
              // 返货
              this.AmountAllCountVal = 0
              res.Data.map((item, index) => {
                item.CountNote = item.TotalAgreementRemainingCount
                this.AmountAllCountVal += item.CountNote
              })
              this.dataSource2 = res.Data
              this.dataSource3 = [this.xyInfoData]
              this.selectedRowArray = res.Data
            } else {
              this.xyDataSource2ResData = res.Data
              let resData = [res.Data]
              // 重组数据
              this.AmountAllNumVal = 0
              this.AmountAllCountVal = 0
              let goodsArray = []
              // 先重组过滤调返钱或者返货数据
              let array1 = []
              let array2 = []
              resData[0].PolicyList.map((item) => {
                // 返钱
                if (item.RebateType == 1) {
                  array1.push(item)
                } else if (item.RebateType == 2) {
                  // 返货
                  array2.push(item)
                }
              })
              if (this.rebateType == 1) {
                resData[0].PolicyList = array1
              } else {
                resData[0].PolicyList = array2
              }
              // 重组年度协议数据
              resData[0].PolicyList.map((item, index) => {
                if (item.GoodsList.length > 0) {
                  item.GoodsList.map((rItem, rIndex) => {
                    rItem.PolicyContent = item.PolicyContent
                    rItem.TotalRebateAmount = item.TotalRebateAmount //应收的返利金额
                    rItem.CancelRebateAmount = item.CancelRebateAmount //取消返利金额
                    rItem.TotalRebateCount = item.TotalRebateCount //应收的返利数量
                    rItem.CancelRebateCount = item.CancelRebateCount //取消返利数量
                    rItem.TotalRecognitionAmount = item.TotalRecognitionAmount //认款金额
                    rItem.TotalRecognitionCount = item.TotalRecognitionCount //认款返利数量
                    rItem.RemainingAmount = item.RemainingAmount
                    rItem.RebateType = item.RebateType //1返钱 2返货
                    rItem.RemainingAmountVal = null
                    rItem.Key = item.PurchaseAgreementPolicyId + rItem.GoodsId
                    rItem.Note = ''
                    rItem.NumberNote = rItem.RemainingAmount
                    rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
                    rItem.AgreementId = item.PurchaseAgreementId
                    rItem.RealTotalAddRebateAmount = item.RealTotalAddRebateAmount
                    rItem.isHaveGoods = true
                    if (rIndex == 0) {
                      rItem.rowSpan = item.GoodsList.length
                      this.AmountAllNumVal += rItem.NumberNote
                      this.AmountAllCountVal += rItem.CountNote
                    } else {
                      rItem.rowSpan = 0
                    }
                    goodsArray.push(rItem)
                  })
                } else {
                  item.NumberNote = item.RemainingAmount
                  this.AmountAllNumVal += item.NumberNote
                  item.isHaveGoods = false
                  goodsArray.push(item)
                }
              })
              this.dataSource3 = [this.xyInfoData]
              this.dataSource2 = goodsArray
              let selectedRowArray = []
              goodsArray.map((item) => {
                if (item.isHaveGoods == false) {
                  selectedRowArray.push(item)
                } else {
                  if (item.rowSpan > 0) {
                    selectedRowArray.push(item)
                  }
                }
              })
              this.selectedRowArray = selectedRowArray
              callback && callback(this.dataSource2)
            }
          } else {
            res.Data.map((item, index) => {
              item.NumberNote = null
              item.CountNote = null
              if (this.purchaseAgreementType == 3) {
                item.RemainingAmount = item.UnrecognizedAmount
                item.NumberNote = item.UnrecognizedAmount
              }
              // 返货池
              if (this.rebateType == 4) {
                if (item.TotalOrderRemainingCount > item.TotalAgreementRemainingCount) {
                  item.RemainingCount = item.TotalAgreementRemainingCount
                } else {
                  item.RemainingCount = item.TotalOrderRemainingCount
                }
              }
            })
            // 在短期销售协议返货抵钱模式下
            if (this.purchaseAgreementType == 2) {
              res.Data = this.setSource4RowSpan(res.Data) || []
            }
            this.dataSource2 = res.Data
          }
          // 设置不能填写input
          // console.log('this.purchaseAgreementType ',this.purchaseAgreementType)
          if (this.purchaseAgreementType != 1 && this.rebateType != 4) {
            this.dataSource2.map((item) => {
              item.inputDisabled = true
            })
          }
          // 清空备注
          this.dataSource2.map((item) => {
            delete item.Note
          })
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    setSource4RowSpan(resData) {
      if (resData && resData.length > 0) {
        // 相同的值排序
        resData = resData.sort((a, b) => {
          return a.PurchaseAgreementPolicyId > b.PurchaseAgreementPolicyId ? 1 : -1
        })

        let groupedArrObj = resData.reduce((acc, obj) => {
          let key = obj.PurchaseAgreementPolicyId;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push(obj);
          return acc;
        }, {});
        let samePolicyIdArray = Object.entries(groupedArrObj) || []
        if (samePolicyIdArray.length > 0) {
          resData.map(item => {
            let rt = samePolicyIdArray.find(r => {
              return item.PurchaseAgreementPolicyId == r[0] && r[1].length > 1
            })
            if (rt) {
              item.GoodsList = rt[1]
            }

          })
          resData.map((item, index) => {
            if (item.GoodsList && item.GoodsList.length > 0) {
              item.GoodsList.map((rItem, rIndex) => {
                if (rIndex == 0) {
                  rItem.rowSpan = item.GoodsList.length
                } else {
                  rItem.rowSpan = 0
                }
              })
            }
          })
          console.log('resData', resData)
        }
        return resData
      }
    },
    onAllcheckboxChange(e, record) {
      let status = e.target.checked
      this.checkAllCheckbox = status
      let selectedRowArray = []
      let selectedRowKeys = []
      this.dataSource2.map(item => {
        this.$set(item, 'checkbox', status)
        if (item.checkbox == true) {
          selectedRowArray.push(item)
          selectedRowKeys.push(item.index)
        }
      })
      this.selectedRowArray = selectedRowArray
      this.selectedRowKeys = selectedRowKeys
      this.onSelectChange(selectedRowKeys, selectedRowArray)
    },
    clearInfoData() {
      this.model = {
        PayerId: '',
        Payer: '',
      }
      this.dataSource3 = []
      this.dataSource2 = []
      this.purchaseAgreementType = 1
    },
    searchDataInput() {
      let data = [
        {
          Name: this.model.Payer,
          Id: this.model.PayerId,
        }
      ]
      this.$refs.TableSelectDataModal.show(data)
    },
    choosePayerData(data) {
      if (data && data.length > 0) {
        let val = data[0].Id
        let name = data[0].Name

        this.model.PayerId = val
        this.model.Payer = name
      }
    },
    checkDataTwo(callback) {
      let selectedRowArrayNew = this.selectedRowArray

      if(this.rebateType === 4 && this.source === 9 ){
        selectedRowArrayNew = this.dataSource2
      }
      let selectedRowArrayObj = selectedRowArrayNew
      if(this.rebateType === 4 && this.source === 9 && this.purchaseAgreementType === 2){
        selectedRowArrayObj = selectedRowArrayNew.filter(item => item.CountNote)
      }
      // console.log('1955 ',selectedRowArrayNew)
      let dataObj = {
        purchaseAgreementType: this.purchaseAgreementType,
        xyDataSource2: this.dataSource2,
        xyDataSource3: this.dataSource3,
        selectedRowArray: selectedRowArrayObj ,//this.selectedRowArray,
        xyDataSource2ResData: this.xyDataSource2ResData,
        AmountAllNumVal: this.AmountAllNumVal,
        AmountAllCountVal: this.AmountAllCountVal,
        type: this.rebateType == 1 ? 'money' : 'count',
        PayerId: this.model.PayerId,
        Payer: this.model.Payer,
        Note: this.model.Note,
      }
      // 过滤本次认款金额或数量为0的数据
      

      let selectedNotZeroRowArray = []
      selectedRowArrayNew.map((item) => {
        if (this.rebateType == 1) {
          if (item.NumberNote) {
            selectedNotZeroRowArray.push(item)
          }
        } else {
          if (item.CountNote) {
            selectedNotZeroRowArray.push(item)
          }
        }
      })
      this.selectedRowArray = selectedNotZeroRowArray
      if (selectedNotZeroRowArray.length == 0) {
        let text = this.rebateType == 1 ? '金额' : '数量'
        this.$message.warning('本次认款' + text + '不能全部为0或为空')
        return
      }

      if (!this.purchaseAgreementType) {
        this.$message.warning('请选择协议类型')
        return false
      } else if (this.purchaseAgreementType == 1 && this.dataSource2.length == 0) {
        this.$message.warning('协议类型为年度协议，请选择年度协议单')
        return false
      } else if ((this.purchaseAgreementType == 2 || this.purchaseAgreementType == 3) && !this.model.PayerId) {
        this.$message.warning('请选择兑付方')
        return false
      } else if ((this.rebateType == 2 || this.rebateType == 3) && !this.model.PayerId) {
        this.$message.warning('请选择兑付方')
        return false
      } else if (
        (this.purchaseAgreementType == 2 || this.purchaseAgreementType == 3) &&
        this.selectedRowArray.length == 0
      ) {
        this.$message.warning('请点击选择' + this.xyTitle)
        return false
      } else if (this.purchaseAgreementType != 1 && this.dataSource2.length == 0) {
        this.$message.warning('请选择' + this.xyTitle)
        return false
      } else if (this.rebateType == 4 && (this.AmountAllCountVal > this.renkuanNum)) {
        // 返货认款数量
        this.$message.warning('认款合计数量不能大于未认款数量' + this.renkuanNum)
        return false
      } else if (this.rebateType == 1 && this.AmountAllNumVal <= 0) {
        this.$message.warning('认款合计金额不能小于或等于0')
        return false
      } else if (this.rebateType != 1 && this.AmountAllCountVal <= 0) {
        this.$message.warning('认款合计数量不能小于或等于0')
        return false
      } else if ((this.purchaseAgreementType == 1 || this.purchaseAgreementType == 2) && this.dataSource2.length > 0) {
        let isNo = false
        for (let i = 0; i < this.selectedRowArray.length; ++i) {
          if (this.rebateType == 4 || this.rebateType == 2 || this.rebateType == 3) {
            if (!this.selectedRowArray[i].CountNote && this.selectedRowArray[i].CountNote != 0) {
              this.$message.warning('本次认款数量不能为空')
              isNo = true
              break
            }
          } else if (this.rebateType == 1) {
            if (!this.selectedRowArray[i].NumberNote && this.selectedRowArray[i].NumberNote != 0) {
              this.$message.warning('本次认款金额不能为空')
              isNo = true
              break
            }
          }
        }

        if (isNo) {
          return false
        } else {
          callback(dataObj)
          return true
        }
      } else {
        callback(dataObj)
        return true
      }
    },
    changeSelect(e) {
      switch (this.purchaseAgreementType) {
        //年度协议
        case 1:
          this.xyTitle = '本次抵扣明细'
          this.searchInput = []
          if (this.rebateType == 4) {
            this.url.xyInfo = this.url.xyInfo1_1
            this.xyListHttpHead = 'P36012'
            this.columns2 = this.columns21FanHuo
          } else if (this.rebateType == 2 || this.rebateType == 3) {
            this.url.xyInfo = this.url.xyInfoTe1
            this.xyListHttpHead = 'P36007'
            this.columns2 = this.columns21TeYearGoods
          } else {
            this.url.xyInfo = this.url.xyInfo1
            this.xyListHttpHead = 'P36007'
            this.columns2 = this.columns21TeYear
          }
          break
        //短期销售
        case 2:
          this.xyTitle = '短期销售协议'
          this.searchInput = this.searchInput1
          if (this.rebateType == 4) {
            this.url.xyInfo = this.url.xyInfo2_1
            this.xyListHttpHead = 'P36012'
            this.columns2 = this.columns22FanHuo
          } else if (this.rebateType == 2 || this.rebateType == 3) {
            this.url.xyInfo = this.url.xyInfoTe2
            this.xyListHttpHead = 'P36007'
            this.columns2 = this.columns22TeShortSaleGoods
          } else {
            this.url.xyInfo = this.url.xyInfo2
            this.xyListHttpHead = 'P36007'
            this.columns2 = this.columns22TeShortSaleMoney
          }
          break
        case 3:
          this.xyTitle = '短期采购协议'
          this.searchInput = this.searchInput2
          this.columns2 = this.columns23TeShortPurchase
          this.xyListHttpHead = 'P36007'
          this.url.xyInfo = this.url.xyInfo3
          break //短期采购
      }
      if (this.rebateType == 4 && this.purchaseAgreementType == 1) {
        this.xyTitle = '协议明细'
      } else if (this.rebateType == 4 && this.purchaseAgreementType == 2) {
        this.xyTitle = '短期销售协议'
      }
      this.dataSource2 = []
      if (this.model.PayerId && (this.purchaseAgreementType == 2 || this.purchaseAgreementType == 3)) {
        this.getXyDetail()
      }
    },
    onSelectChange(selectedRowKeys, selectedRowArray) {
      let differenceList = this.dataSource2.filter((item) => selectedRowArray.indexOf(item) === -1)
      if (differenceList.length > 0) {
        differenceList.map((item) => {
          item.NumberNote = null
          item.updateNumber = null
          item.CountNote = null
          delete item.Note
        })
      }
      this.selectedRowKeys = selectedRowKeys
      this.selectedRowArray = selectedRowArray

      // 短期销售协议设置默认全选和全不选
      if (this.purchaseAgreementType == 2) {
        let dataSourceArray = this.dataSource2.filter(item => {
          return item.rowSpan !== 0
        })
        // console.log(this.selectedRowKeys.length, dataSourceArray.length)
        if (this.selectedRowKeys.length > 0 && (this.selectedRowKeys.length == dataSourceArray.length)) {
          this.checkAllCheckbox = true
        } else {
          this.checkAllCheckbox = false
        }
      }

      if (this.rebateType == 1) {
        this.dataSource2.map((item) => {
          item.inputDisabled = true
        })
        this.countPriceNum()
      } else {
        this.dataSource2.map((item) => {
          item.inputDisabled = true
        })
        this.countCountNum()
      }
    },
    // 计算金额对抵
    countPriceNum() {
      if (this.selectedRowKeys.length == 0) {
        return
      }
      this.AmountAllNumVal = 0
      this.selectedRowArray.map((item, index) => {
        item.NumberNote = item.updateNumber ? item.updateNumber : item.RemainingAmount
        item.inputDisabled = false
        this.AmountAllNumVal += item.NumberNote
      })
    },
    // 计算数量对抵
    countCountNum() {
      if (this.selectedRowKeys.length == 0) {
        return
      }
      this.AmountAllCountVal = 0
      this.selectedRowArray.map((item, index) => {
        item.CountNote = item.RemainingCount
        item.inputDisabled = false
        this.AmountAllCountVal += item.CountNote
      })
    },
    // 计算合计金额
    countTotalNum() {
      this.AmountAllNumVal = 0
      this.selectedRowArray.map((item) => {
        this.AmountAllNumVal += Number(item.NumberNote)
      })
    },
    // 计算合计数量
    countTotalCount() {
      // console.log('changeCountNote', this.dataSource2)
      let selectedRowArrayNew = this.selectedRowArray
      if(this.rebateType === 4 && this.source === 9){
        selectedRowArrayNew = this.dataSource2
      }
      this.AmountAllCountVal = 0
      selectedRowArrayNew.map((item) => {
        this.AmountAllCountVal += Number(item.CountNote)
      })
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      queryParam.PayerId = this.model.PayerId
      if (!queryParam.PayerId) {
        this.$message.warning('请选择兑付方再操作')
        return
      }
      // if (type != 'searchReset') {
      //   if (Object.keys(queryParam).length == 1 && Object.keys(queryParam)[0] === 'PayerId') {
      //     this.$message.warning('请输入要查询的条件再操作')
      //     return
      //   }
      // }
      if (this.purchaseAgreementType != 1) {
        this.selectedRowKeys = []
        this.selectedRowArray = []
        this.AmountAllNumVal = 0
        this.AmountAllCountVal = 0
      }
      this.getXyDetail(queryParam)
    },
    clearSearchInput() {
      this.model.PayerId = ''
      this.model.Payer = ''
      this.dataSource2 = []
    },
    changeSearchInput(val, name) {
      this.model.PayerId = val
      this.model.Payer = name
      this.selectedRowKeys = []
      this.selectedRowArray = []
      this.$refs.form.validateField(['PayerId'])
      if (this.rebateType == 1) {
        this.getXyDetail()
      }
      if (this.rebateType != 1 && this.purchaseAgreementType == 2) {
        this.getXyDetail()
      }
    },
    chooseTypeTableData() {
      if (!this.purchaseAgreementType) {
        this.$message.warning('请先选择协议类型')
        return
      }
      // 返货抵钱时候用supplierId
      if (this.rebateType == 1) {
        if (!this.supplierId) {
          this.$message.warning('请先选择返货采购订单')
          return
        }
      } else {
        // 其他用PayerId
        if (!this.model.PayerId) {
          this.$message.warning('请先选择兑付方')
          return
        }
      }
      let queryParam = {
        PurchaseAgreementType: this.purchaseAgreementType,
        SupplierId: this.rebateType == 1 ? this.supplierId : this.model.PayerId,
        AgreementRebateType: this.rebateType == 1 ? 1 : 2, //1返钱 2返货
        IsByChannel: this.rebateType == 1 && this.purchaseAgreementType ==1 ? true: null
      }
      this.$refs.TableSelectDataTypeModal.show([], queryParam)
    },
    chooseTypeData(data) {
      this.xyInfoData = data[0]
      this.getXyDetail()
    },
    inputOnblur(e, text, record, index) {
      this.changeNumberNote(e, record)
    },
    inputChage(e, text, record, index) {
      record.NumberNote = e.target.value
    },
    // 年度协议文本
    inputOnblur2(e, text, record, index) {
      record.Note = e.target.value
    },
    // 数量
    changeCountNote(e, record) {
      let value = e.target.value
      if (record.RemainingCount >= 0) {
        if (value > record.RemainingCount) {
          this.$message.warning('本次认款数量不能大于未认款数量' + record.RemainingCount)
          this.countCountNum()
          return
        }
      }
      this.countTotalCount()
    },
    inputNumberNote(e, record) {
      record.updateNumber = e
    },
    // 金额
    changeNumberNote(e, record) {
      let value = e.target.value
      let text = '未认款金额'
      if (record.RemainingAmount < 0) {
        if (value > 0) {
          this.$message.warning(text + '为负数，本次认款金额不能大于0')
          this.countPriceNum()
          return
        }
        if (value < record.RemainingAmount) {
          this.$message.warning('本次认款金额不能小于' + text + record.RemainingAmount)
          this.countPriceNum()
          return
        }
      } else if (record.RemainingAmount >= 0) {
        if (value < 0) {
          this.$message.warning(text + '为正数，本次认款金额不能小于0')
          this.countPriceNum()
          return
        }
        if (value > record.RemainingAmount) {
          this.$message.warning('本次认款金额不能大于' + text + record.RemainingAmount)
          record.NumberNote = record.RemainingAmount
          this.$forceUpdate()
          this.countPriceNum()
          return
        }
      }
      this.countTotalNum()
    },
    handleOk() { },
  },
}
</script>

<style lang="less" scoped>
.xieyi {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}
.xieyi::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}
/* 设置table行的高度防止错位 */
/deep/.ant-table-tbody tr {
  height: 48px !important;
}

.title-btn {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  justify-items: center;
  align-items: center;
}

.title-btn .xieyi {
  padding-bottom: 0;
}
.input-class {
  height: 32px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  padding: 0 11px;
}
.input-class:focus-visible {
  border: 1px solid #d9d9d9;
}
.input-class::placeholder {
  color: #bfbfbf;
}
.input-class:hover {
  border-color: #40a9ff;
  border-right-width: 1px !important;
}
.input-class:focus {
  border-color: #40a9ff;
  border-right-width: 1px !important;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.party-search {
  flex: 1;
  margin-right: 5px;
}

/deep/.party-search .ant-input:disabled {
  color: rgba(0, 0, 0, 0.65);
  background-color: #ffffff;
}
</style>
