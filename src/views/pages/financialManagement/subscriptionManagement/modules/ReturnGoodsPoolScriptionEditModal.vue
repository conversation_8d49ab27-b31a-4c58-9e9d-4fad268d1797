<!-- 协议返货认款 -->
<template>
  <a-modal :title="title" :width="1000" :visible="visible" :saveLoading="saveLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <div class="skd">认款协议：</div>
      <a-descriptions :column="2">
        <a-descriptions-item label="协议编号">{{ model.AgreementNo || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="甲方">{{ model.PartyFirstName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="促销时间">{{ model.AgreementStartTime+'~'+model.AgreementEndTime }}</a-descriptions-item>
        <a-descriptions-item label="核销方式">{{ model.AccountingMethodStr||'--' }}</a-descriptions-item>
        <a-descriptions-item label="返货应收数量">{{ model.TotalRebateAmount||'--' }}</a-descriptions-item>
      </a-descriptions>
      <div class="xieyi" style="padding-top: 20px;">认款明细：（本次合计认款数量：{{ Total }}）</div>
      <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource.length === 0 ? false:'350px',x:'1000px' }">
        <!-- 字符串超长截取省略号显示-->
        <template slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </template>
        <!-- 操作 -->
        <template slot="Count" slot-scope="text, record,index">
          <a-input-number
            v-model="record.Count"
            style="width:100%"
            :min="0"
            placeholder="请输入"
            @blur="(e)=>{onCountBlur(record,2)}"
          />
        </template>
      </a-table>
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button @click="save" type="primary" :loading="saveLoading">保存</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  mixins: [EditMixin],
  name: "ReturnGoodsPoolScriptionEditModal",
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "协议返货认款",
      visible: false,
      model: {},
      confirmLoading: false,
      saveLoading:false,
      httpHead: 'P36012',
      url: {
        goods:'/{v}/ReturnGoods/GetRecognitionDetail/GetRecognitionDetailAsync'
      },
      dataSource:[],
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货应收数量',
          dataIndex: 'AgreementCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '协议未认款数量',
          dataIndex: 'AgreementRemainingCount',
          width: 150,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单可认款数量',
          dataIndex: 'OrderRemainingCount',
          width: 150,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'Count',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Count' },
        },
      ],
      checkList:[],
    };
  },
  mounted() { 

  },
  created() { },
  computed: {
    Total(){
      if(this.dataSource&&this.dataSource.length){
        let total = 0
        this.dataSource.forEach(e => {
          total += e.Count;
        });
        return total
      }else{
        return 0
      }
    }
  },
  methods: {
    moment,
    show(record,list) {
      this.checkList = list;
      this.model = record;
      this.visible = true;
      this.getGoodsList();
    },
    getGoodsList() {
      getAction(this.url.goods, {
        agreementId:this.model.PurchaseAgreementId,
        purchaseOrderId:this.model.purchaseOrderId
       }, 'P36012').then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (!res.Data) return
        this.dataSource = res.Data||[];
        if(this.dataSource.length){
          if(this.checkList&&this.checkList.length){
            this.checkList.forEach(e=>{
              this.dataSource.forEach(e1=>{
                if(e.GoodsSpuId==e1.GoodsSpuId){
                  e1.Count = e.Count
                }
              })
            })
          }else{
            this.dataSource.forEach(e=>{
              e.Count = 0
            })
          }
        }

        this.dataSource = JSON.parse(JSON.stringify(this.dataSource))
      })
    },
    onCountBlur(record){
      let acount = record.AgreementRemainingCount>record.OrderRemainingCount?record.OrderRemainingCount:record.AgreementRemainingCount;
      if(record.Count>acount){
        record.Count = acount;
      }
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.model = {};
      this.visible = false;
    },
    save(){
      if(this.dataSource&&this.dataSource.length==0){
        this.$message.warning('没有协议不能保存');
        return false
      }

      let goods = this.dataSource.filter(e=>e.Count>0);
      if(goods&&goods.length==0){
        this.$message.warning('请填写本次认款数量');
        return false
      }
      this.$emit('ok',this.dataSource);
      this.handleCancel();
    }
  },
};
</script>
<style lang="less" scoped>
  .xieyi,.skd{
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 1.5;
    padding-bottom: 10px;
  }
  .xieyi::before{
    display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }

  .title-btn{
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    justify-items: center;
  }

  .title-btn .xieyi{
    padding-bottom: 0;
  }

  .edit-btn{
    color:#1890ff;
    margin-left: 10px;
    cursor: pointer;
  }
</style>
