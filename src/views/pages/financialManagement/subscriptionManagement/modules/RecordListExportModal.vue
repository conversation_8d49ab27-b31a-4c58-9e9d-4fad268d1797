<!-- 认款记录导出 -->
<template>
  <a-modal :title="title" :width="800" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">

    <a-spin :spinning="confirmLoading">
      <a-form-model-item prop="recordList">
        <a-checkbox-group v-model="checkKeys">
          <a-row>
            <a-col style="margin-bottom: 8px;" :span="8" v-for="(item,index) in recordArray" :key="index">
              <a-checkbox :value="item.value">
                {{item.name}}
              </a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>

      </a-form-model-item>
    </a-spin>

    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel">取消</a-button>
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="chooseAll" type="primary">{{isAll?'反选':'全选'}}</a-button>
      <a-button :disabled="confirmLoading" @click="handleOk" type="primary">确定</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "RecordListExportModal",
  components: {},
  mixins: [ListMixin],
  data() {
    return {
      title: "认款记录导出",
      visible: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      isAll: false,//是否全选
      queryParam: {},
      exportParams: {},
      checkKeys: [],
      recordArray: [
        { name: '采购退货认款导出', value: 11 },
        { name: '商销认款导出', value: 10 },
        { name: '短期采购协议认款', value: 12 },
        { name: '短期销售协议认款', value: 13 },
        { name: '年度协议认款导出', value: 14 },
        { name: '过账认款导出', value: 15 },
        { name: '特殊认款导出', value: '1,2,3' },
        { name: '采购调价认款导出', value: 17 },
        { name: '通用协议认款导出', value: 19 },
        { name: '优利泰认款导出', value: 18 },
        { name: '预付款退款认款导出', value: 20 },
      ],
      confirmLoading: false,
      httpHead: 'P36001',
      url: {
        add: "/{v}/UserPermission/Role/Create",
        update: "/{v}/UserPermission/Role/Update",
      },
    };
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(exportParams, queryParam) {
      this.model = {};
      this.checkKeys = []
      this.exportParams = exportParams
      this.isAll = false
      queryParam = Object.assign({}, queryParam)
      if (queryParam.PushERPStatus) {
        queryParam.PushERPStatus = parseInt(queryParam.PushERPStatus)
      } else {
        delete queryParam.PushERPStatus
      }
      if (queryParam.RecognitionOrderMode) {
        queryParam.RecognitionOrderMode = parseInt(queryParam.RecognitionOrderMode)
      } else {
        delete queryParam.RecognitionOrderMode
      }
      if (queryParam.RecognitionStatus) {
        queryParam.RecognitionStatus = parseInt(queryParam.RecognitionStatus)
      } else {
        delete queryParam.RecognitionStatus
      }
      this.queryParam = queryParam
      this.visible = true;
    },
    chooseAll() {
      this.isAll = !this.isAll
      let allKeys = []
      if (this.isAll) {
        allKeys = this.recordArray.map(item => {
          return item.value
        })
      } else {
        allKeys = []
      }
      this.checkKeys = allKeys
    },
    // 确定
    handleOk() {
      if (this.checkKeys.length == 0) {
        this.$message.warning('请选择导出类型!')
        return
      }
      let array = []
      this.checkKeys.map(item => {
        if (typeof (item) == 'string') {
          array = array.concat(item.split(','))
        }
      })
      let keys = this.checkKeys.concat(array)
      let keysList = []
      keys.map(item => {
        if (isNaN(Number(item)) == false) {
          keysList.push(Number(item))
        }
      })
      this.queryParam['RecognitionOrderModeList'] = keysList
      this.handleExportXls(
        '认款记录',
        this.exportParams.methods,
        this.exportParams.url,
        null,
        this.exportParams.linkHttpHead,
        this.queryParam
      )
      // 关闭
      this.close()
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
