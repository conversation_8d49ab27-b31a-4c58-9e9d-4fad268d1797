<!-- 新增收款 -->
<template>
  <a-modal :title="title" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        // border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="收款时间：" prop="ReceiptTime">
                <a-date-picker valueFormat="YYYY-MM-DD" v-model="model.ReceiptTime" style="width: 100%" placeholder="结束日期">
                </a-date-picker>
              </a-form-model-item>
              <a-form-model-item label="收款方式：" prop="ReceiptType">
                <!-- <EnumSingleChoiceView style="width: 100%;" placeholder="请选择" dictCode="EnumReceiptType" v-model="model.ReceiptType" /> -->
                <a-select style="width: 100%" v-model="model.ReceiptType" placeholder="请选择">
                  <a-select-option value="">请选择</a-select-option>
                  <a-select-option value="1">现金</a-select-option>
                  <a-select-option value="2">微信</a-select-option>
                  <a-select-option value="3">支付宝</a-select-option>
                  <a-select-option value="4">对公转账</a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="打款方：" prop="Payer">
                <a-input placeholder="请输入" v-model="model.Payer" style="width: 100%" :maxLength="30" />
              </a-form-model-item>
              <a-form-model-item label="收款账户：" prop="ReceiptAccountId">
                <SingleChoiceSearchView style="width: 100%;" placeholder="请选择" :httpParams="{
                    PageIndex:1,
                    PageSize:999,
                    KeyWord:''
                  }" httpType="POST" :httpHead="'P36012'" keyWord="KeyWord" :dataKey="{ name: 'AccountName', value: 'Id' }" v-model="model.ReceiptAccountId" :Url="url.getReceiptAccountList" />
              </a-form-model-item>
              <a-form-model-item label="收款金额：" prop="ReceiptAmount">
                <a-input-number v-model="model.ReceiptAmount" :precision="2" style="width:100%" :min="0" placeholder="请输入" :max="***********.99" />
              </a-form-model-item>
              <a-form-model-item label="备注：" prop="Note">
                <a-textarea placeholder="请输入" v-model="model.Note" style="width: 100%" :maxLength="100" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  mixins: [EditMixin],
  name: 'CollectMoneyEditModal',
  components: {},
  data() {
    return {
      title: '新增收款',
      visible: false,
      model: {},
      Edit: false,
      confirmLoading: false,
      httpHead: 'P36012',
      url: {
        add: '/{v}/CashReceiptNew/Submit',
        update: '/{v}/CashReceiptNew/EditReceiptOrder',
        getReceiptAccountList: '/{v}/CashReceiptNew/GetReceiptAccountList'
      }
    }
  },
  computed: {
    rules() {
      return {
        ReceiptTime: [{ required: true, message: '请选择!' }],
        ReceiptType: [{ required: true, message: '请选择!' }],
        // Payer: [{ required: true, message: '请输入!' }],
        ReceiptAmount: [{ required: true, message: '请输入!' }]
      }
    }
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add() {
      this.model = {}
      this.title = '新增收款'
      this.Edit = false
      this.visible = true
    },
    edit(record) {
      this.title = '编辑收款'
      record.ReceiptType = record.ReceiptType.toString()
      this.model = record
      this.Edit = true
      this.visible = true
      if (this.model.ReceiptAccountId === '********-0000-0000-0000-************' || !this.model.ReceiptAccountId) {
        this.model.ReceiptAccountId = ''
      }
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          this.confirmLoading = true
          let params = {

          }
          params.ReceiptType = Number(params.ReceiptType)
          let url = ''
          if (this.Edit) {
            url = this.url.update
            params = {
              "ReceiptAccountId": this.model.ReceiptAccountId || null,
              "ReceiptTime": this.model.ReceiptTime,
              "Payer": this.model.Payer,
              "ReceiptType": parseInt(this.model.ReceiptType),
              "ReceiptAmount": this.model.ReceiptAmount,
              "Note": this.model.Note,
              "Id": this.model.Id,
            }
          } else {
            url = this.url.add
            params = {
              "ReceiptAccountId": this.model.ReceiptAccountId || null,
              "ReceiptTime": this.model.ReceiptTime,
              "Payer": this.model.Payer,
              "ReceiptType": parseInt(this.model.ReceiptType),
              "ReceiptAmount": this.model.ReceiptAmount,
              "Note": this.model.Note,
            }
          }
          postAction(url, params, this.httpHead)
            .then(res => {
              if (res.IsSuccess) {
                this.$message.success('操作成功')
                this.$emit('ok')
                this.close()
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              this.confirmLoading = false
            })
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    }
  }
}
</script>
