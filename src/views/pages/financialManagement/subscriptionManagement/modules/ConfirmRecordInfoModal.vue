<!-- 认款明细 -->
<template>
  <a-modal :title="title" :width="1000" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="httpHead" :linkUrl="linkUrl" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" />
    </a-spin>
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  mixins: [EditMixin],
  name: "ConfirmRecordInfoModal",
  components: {},
  data() {
    return {
      title: "认款明细",
      visible: false,
      confirmLoading: false,
      httpHead: 'P36012',
      queryParam:{},
      linkUrl: {
        list: '/{v}/CashRecognition/GetCashRecognitionRecordBySaleOrderListAsync',
      },
      columns: [
        {
          title: '单据编号',
          dataIndex: 'BusinessNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '类型',
          dataIndex: 'BusinessEventTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款类型',
          dataIndex: 'ActivityCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'MainOrderCode',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款时间',
          dataIndex: 'BusinessTi1me',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BusinessTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收金额',
          dataIndex: 'TotalAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '剩余应收金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      isTableInitData:false,
      tab: {
        bordered: false, //是否显示表格的边框
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
    };
  },
  mounted() { 

  },
  created() { },
  methods: {
    moment,
    show(record) {
      this.model = {};
      this.title = '认款明细';
      this.visible = true;
      this.queryParam = {
        cashRecognitionRecordId:record.CashRecognitionRecorId
      };
      this.$nextTick(()=>{
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
