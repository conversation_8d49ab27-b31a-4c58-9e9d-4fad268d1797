<!-- 现金认款 -->
<template>
  <a-modal :title="title" :width="1000" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <a-descriptions :column="1">
        <a-descriptions-item label="可认款金额">{{ model.RemainingAmount?'¥'+model.RemainingAmount:' -- ' }}</a-descriptions-item>
      </a-descriptions>
      <a-form-model ref="form" :rules="rules" :model="model" layout="vertical" style="min-height: 240px;">
        <a-row :getter="10">
          <a-col :md="24">
            <a-form-model-item label="认款类型：" prop="CashRecognitionType">
                <SingleChoiceView
                  placeholder="请选择"
                  :httpParams="{
                    enumName: 'EnumCashRecognitionType'
                  }"
                  :httpHead="'P36001'"
                  :dataKey="{ name: 'ItemDescription', value: 'ItemValue' }"
                  :Url="'/{v}/SystemTool/GetEnumsByName'"
                  v-model="model.CashRecognitionType"
                  @change="changeType"
                />
            </a-form-model-item>
            <a-form-model-item label="兑付方：" prop="Payer">
              <SingleInputSearchView v-if="model.CashRecognitionType==3" placeholder="搜索名称/编号/拼音码" width="100%" ref="cusListSelect" :httpParams="{
                pageIndex: 1,
                pageSize: 200,
                IsValid: true,
              }" keyWord="keyWord" httpHead="P36002" :Url="url.cusList" :value="model.PayerId" :name="model.Payer" :isAbsolute="true" @clear="()=>{model.PayerId='';model.Payer = ''}" @change="changeSearchInput" />
              <SingleInputSearchView v-else placeholder="搜索名称/编号/拼音码" width="100%" ref="supplierListSelect" :httpParams="{
                pageIndex: 1,
                pageSize: 200,
                AuditStatus:2,
              }" keyWord="keyWord" httpHead="P36003" :Url="url.supplierList" :value="model.PayerId" :name="model.Payer" :isAbsolute="true" @clear="()=>{model.PayerId='';model.Payer = ''}" @change="changeSearchInput" />
            </a-form-model-item>
            <template v-if="model.CashRecognitionType==1&&model.PayerId">
              <div class="xieyi">协议：</div>
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource.length === 0 ? false:'350px',x:'1000px' }">
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <!-- 操作 -->
                <template slot="Count" slot-scope="text, record,index">
                  <a-input-number
                    v-model="record.Count"
                    :precision="2"
                    style="width:100%"
                    :min="0"
                    placeholder="请输入"
                    @blur="(e)=>{onCountBlur(record,e)}"
                  />
                </template>
                <template slot="Note" slot-scope="text, record,index">
                  <a-input
                    v-model="record.Note"
                    style="width:100%"
                    placeholder="请输入"
                    :maxLength="40"
                  />
                </template>
                <!-- 促销时间 -->
                <span slot="AgreementStartTimeVal" slot-scope="text, record,index">
                  <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
                </span>
                <!-- 价格显示-->
                <span slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </span>
              </a-table>
              <a-descriptions :column="1" style="padding-top: 30px;">
                <a-descriptions-item label="认款合计金额">¥{{ Total }}</a-descriptions-item>
              </a-descriptions>
            </template>
            <template v-if="model.CashRecognitionType==2&&model.PayerId">
              <div class="title-btn">
                <div class="xieyi">退货单：</div>
                <div>
                  <YYLButton menuId="" text="批量移除" @click="()=>{dataSource=[];}" />
                  <YYLButton menuId="" text="选择" @click="chooseTableData" type="primary"/>
                </div>
              </div>
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns1" :dataSource="dataSource" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource.length === 0 ? false:'350px',x:'1000px' }">
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <template slot="action" slot-scope="text, record, index, column">
                  <a style="margin:0 5px;" @click="operate(record,'del',index)">移除</a>
                </template>
                <!-- 价格显示-->
                <span slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </span>
              </a-table>
            </template>
            <template v-if="model.CashRecognitionType==3&&model.PayerId">
              <div class="title-btn">
                <div class="xieyi">认款单据：</div>
                <div>
                  <YYLButton menuId="" text="批量移除" @click="()=>{dataSource=[];}" />
                  <YYLButton menuId="" text="选择" @click="chooseTableData" type="primary"/>
                </div>
              </div>
              <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns2" :rowClassName="rowClassNameFun" :dataSource="dataSource" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource.length === 0 ? false:'350px',x:'1000px' }">
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <!-- 价格显示-->
                <span slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </span>
                <template slot="action" slot-scope="text, record, index, column">
                  <a style="margin:0 5px;" @click="operate(record,'del',index)">移除</a>
                </template>
              </a-table>
            </template>
            <template v-if="model.CashRecognitionType==4">
              <a-form-model-item label="认款金额：" prop="RecognitionAmount">
                <a-input placeholder="请输入" v-model="model.RecognitionAmount" :maxLength="50"></a-input>
              </a-form-model-item>
              <a-form-model-item label="备注：" prop="Note">
                <a-textarea placeholder="请输入" v-model="model.Note" :maxLength="100" :rows="4"></a-textarea>
              </a-form-model-item>
            </template>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button @click="save" type="primary" :loading="saveLoading">保存</a-button>
    </a-row>
    <!-- 表格选择数据弹窗 -->
    <TableSelectDataModal ref="TableSelectDataModal" :selectTableData="model&&model.CashRecognitionType==3?selectTableData1:selectTableData" @chooseData="chooseData"></TableSelectDataModal>
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  mixins: [EditMixin],
  name: "CashPoolSubscriptionModal",
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "现金认款",
      visible: false,
      model: {
        RemainingAmount:0,
        CashReceiptId:'',
        CashRecognitionType:'',
        PayerId:'',
        Payer:'',
        RecognitionTypeByAgreementList:[],
        RecognitionTypeByPurchaseRefundOrderList:[],
        RecognitionTypeBySaleOrderList:[],
        RecognitionTypeByPosting:{},
      },
      confirmLoading: false,
      saveLoading:false,
      httpHead: 'P36012',
      url: {
        supplierList: '/{v}/Supplier/GetSuppliersForSelect',
        cusList:'/{v}/Customer/ListForBusiness',
        add:'/{v}/CashRecognition/CreateOrderAsync',
        xy:'/{v}/PurchaseAgreement/GetUnsubscribedRecognitionListAsync'
      },
      dataSource:[],
      columns: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '核算方式',
          dataIndex: 'AccountingMethodStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RecognitionAmount',
          width: 150,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '本次认款金额',
          dataIndex: 'Count',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Count' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      columns1: [
        {
          title: '退货编号',
          dataIndex: 'RefundOrderNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '类型',
          dataIndex: 'BusinessOrderTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'ActivityCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'PurchaseRefundTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退货金额',
          dataIndex: 'TotalRefundAmout',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '剩余退货金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 120,
          ellipsis: true,
          align: 'center',
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
        }
      ],
      columns2: [
        {
          title: '单据编号',
          dataIndex: 'BusinessNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '类型',
          dataIndex: 'BusinessOrderTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'SaleOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BusinessTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收金额',
          dataIndex: 'TotalAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '剩余应收金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 120,
          ellipsis: true,
          align: 'center',
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
        }
      ],
      selectTableData: {
        searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '单据编号', type: 'input', value: '', key: 'BusinessNo', defaultVal: '', placeholder: '请输入' },
          {
            name: '日期',
            type: 'timeInput',
            value: '',
            rangeDate: [],
            key: ['CreateTimeBegin', 'CreateTimeEnd'],
            defaultVal: ['', ''],
            placeholder: ['开始日期', '结束日期']
          },
        ],
        title: '采购退货认款',
        name: '采购退货',
        recordKey: 'PurchaseOrderId',
        httpHead: 'P36012',
        isInitData: false,
        url: {
          list: '/{v}/CashRecognition/GetUnsubscribedPurchaseRefundOrderListAsync',
        },
        columns: [
          {
            title: '退货编号',
            dataIndex: 'RefundOrderNo',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建人',
            dataIndex: 'CreateByName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '类型',
            dataIndex: 'BusinessOrderTypeStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '对应订单编号',
            dataIndex: 'ActivityCount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '付款方式',
            dataIndex: 'PaymentModeStr',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '发生日期',
            dataIndex: 'PurchaseRefundTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '退货金额',
            dataIndex: 'TotalRefundAmout',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '剩余退货金额',
            dataIndex: 'RecognitionAmount',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
        ],
      },
      selectTableData1: {
        searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '单据编号', type: 'input', value: '', key: 'BusinessNo', defaultVal: '', placeholder: '请输入' },
          {
            name: '类型',
            type: 'select',
            value: '',
            key: 'BusinessEventType',
            defaultVal: [
              { title: '销售出库', value: 20 },
              { title: '商销退货', value: 21 },
              { title: '差价调整', value: 22 },
            ],
            placeholder: '请选择'
          },
          {
            name: '日期',
            type: 'timeInput',
            value: '',
            rangeDate: [],
            key: ['CreateTimeBegin', 'CreateTimeEnd'],
            defaultVal: ['', ''],
            placeholder: ['开始日期', '结束日期']
          },
        ],
        title: '商销认款',
        name: '商销认款',
        recordKey: 'BusinessId',
        httpHead: 'P36012',
        isInitData: false,
        url: {
          list: '/{v}/CashRecognition/GetUnsubscribedSaleOrderListAsync',
        },
        columns: [
          {
            title: '单据编号',
            dataIndex: 'BusinessNo',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建人',
            dataIndex: 'CreateByName',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '类型',
            dataIndex: 'BusinessOrderTypeStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '对应订单编号',
            dataIndex: 'SaleOrderCode',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '付款方式',
            dataIndex: 'PaymentModeStr',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '发生日期',
            dataIndex: 'BusinessTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '应收金额',
            dataIndex: 'TotalAmount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '剩余应收金额',
            dataIndex: 'RecognitionAmount',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 120,
            ellipsis: true,
            fixed: 'right',
            align: 'center',
            scopedSlots: { customRender: 'action' },
          }
        ],
      },
    };
  },
  mounted() { 

  },
  created() { },
  watch: {
    'model.RecognitionAmount'(val) {
      if(val > this.model.RemainingAmount){
        this.model.RecognitionAmount = this.model.RemainingAmount;
        this.$message.warning('认款金额不能大于可认款金额');
      }
    },
  },
  computed: {
    rules() {
      return {
        CashRecognitionType: [{ required: true,  message: '请选择!' }],
        Payer: [{ required: true, message: '请选择!' }],
        RecognitionAmount: [{ required: true, validator: this.checkMoney }],
      }
    },
    Total(){
      if(this.dataSource&&this.dataSource.length){
        let total = 0
        this.dataSource.forEach(e => {
          total += e.Count;
        });
        return total
      }else{
        return 0
      }
    }
  },
  methods: {
    moment,
    show(record) {
      this.model.CashReceiptId = record.Id;
      this.model.RemainingAmount = record.RemainingAmount;
      this.title = '现金认款';
      this.visible = true;
    },
    changeType(e){
      this.model.Payer = '';
      this.model.PayerId = '';
      this.dataSource = [];
      if(e==3){
        this.$nextTick(()=>{
          this.$refs.cusListSelect.getData();
        })
      }else{
        this.$nextTick(()=>{
          this.$refs.supplierListSelect.getData();
        })
      }
    },
    getXyDetail() {
      getAction(this.url.xy, {
        SupplierId:this.model.PayerId,
        AgreementRebateType:1
       }, 'P36007').then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (!res.Data) return
        this.dataSource = res.Data||[];
        if(this.dataSource.length){
          this.dataSource.forEach(e=>{
            e.Count = '';
            e.Note = '';
          })
        }

        this.dataSource = JSON.parse(JSON.stringify(this.dataSource))
      })
    },
    changeSearchInput(val, name,item) {
      this.model.PayerId = val;
      this.model.Payer = name;
      this.dataSource = [];
      if(this.model.CashRecognitionType==1){
        //采购返利认款
        this.getXyDetail();//采购返利认款 获取未认款的返利协议
      }else if(this.model.CashRecognitionType==2){
        //采购退货
      }
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.model = {
        RemainingAmount:0,
        CashReceiptId:'',
        CashRecognitionType:'',
        PayerId:'',
        Payer:'',
        RecognitionTypeByAgreementList:[],
        RecognitionTypeByPurchaseRefundOrderList:[],
        RecognitionTypeBySaleOrderList:[],
        RecognitionTypeByPosting:{},
      };
      this.visible = false;
    },
    // 列表操作
    operate(record, type,index) {
      if(type == 'del'){
        this.dataSource.splice(index, 1)
      }
    },
    onCountBlur(record){
      if(record.Count>record.RecognitionAmount){
        record.Count = record.RecognitionAmount;
      }
    },
    chooseTableData() {
      let dataSource = this.dataSource || []
      let queryParam = {
        PayerId: this.model.PayerId,
      }
      this.$refs.TableSelectDataModal.show(dataSource, queryParam)
    },
    // 选择的数据
    chooseData(data) {
      if (data && data.length > 0) {
        this.dataSource = this.dataSource.concat(data)
      }
    },
    save(){
      this.$refs.form.validate((err, values) => {
        if (err) {
          if(this.model.CashRecognitionType == 1){
            if(this.dataSource&&this.dataSource.length==0){
              this.$message.warning('没有协议不能保存');
              return false
            }
            let xy = this.dataSource.filter(e=>e.Count>0);
            if(xy.length==this.dataSource.length){
              this.model.RecognitionTypeByAgreementList = this.dataSource.map(e=>{
                return {
                  PurchaseAgreementId:e.PurchaseAgreementId,
                  RecognitionAmount:e.Count,
                  Note:e.Note,
                }
              })
            }else{
              this.$message.warning('协议-请填写本次认款金额');
              return false
            }
          }else if(this.model.CashRecognitionType == 2){
            if(this.dataSource&&this.dataSource.length==0){
              this.$message.warning('请选择退货单');
              return false
            }
            this.model.RecognitionTypeByPurchaseRefundOrderList = this.dataSource.map(e=>{
              return {
                PurchaseRefundOrderId:e.PurchaseAgreementId,
                PurchaseOrderId:e.PurchaseOrderId,
                PurchaseOrderNo:e.PurchaseOrderNo,
                PurchaseRefundTime:e.PurchaseRefundTime,
                RefundOrderNo:e.RefundOrderNo,
                PaymentMode:e.PaymentMode,
                BusinessCreator:e.BusinessCreator,
                TotalRefundAmout:e.TotalRefundAmout,
                TotalRecognitionAmount:e.TotalRecognitionAmount
              }
            });
          }else if(this.model.CashRecognitionType == 3){
            if(this.dataSource&&this.dataSource.length==0){
              this.$message.warning('请选择认款单据');
              return false
            }

            this.model.RecognitionTypeBySaleOrderList = this.dataSource.map(e=>{
              return {
                SaleOrderId:e.SaleOrderId,
                SaleOrderCode:e.SaleOrderCode,
                BusinessId:e.BusinessId,
                BusinessNo:e.BusinessNo,
                PaymentMode:e.PaymentMode,
                BusinessEventType:e.BusinessEventType,
                BusinessTime:e.BusinessTime,
                BusinessCreator:e.BusinessCreator,
                TotalAmount:e.TotalAmount,
                TotalRecognitionAmount:e.TotalRecognitionAmount,
              }
            });
          }else if(this.model.CashRecognitionType == 4){
            this.model.RecognitionTypeByPosting = {
              RecognitionAmount:this.model.RecognitionAmount,
              Note:this.model.Note,
            }
          }
          this.saveLoading = true;
          this.model.CashRecognitionType = Number(this.model.CashRecognitionType)
          postAction(this.url.add, this.model, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              this.$message.success("操作成功");
              this.$emit("ok");
              this.close();
            } else {
              this.$message.warning(res.Msg);
            }
          }).finally(() => {
            this.saveLoading = false;
          });
        }
      });
    },
    // table行的颜色
    rowClassNameFun(record, index) {
      if (record.TotalAmount<0) {
        return 'table-color-red-dust'
      }
    },
  },
};
</script>
<style lang="less" scoped>
  .xieyi{
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 1.5;
    padding-bottom: 10px;
  }
  .xieyi::before{
    display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }

  .title-btn{
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    justify-items: center;
  }

  .title-btn .xieyi{
    padding-bottom: 0;
  }
</style>
