<!-- 返货认款单详情 -->
<template>
  <a-modal :footer="null" :title="title" :width="1000" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        // padding: '10px 16px',
        background: '#fff',
      }">
      <a-descriptions :column="3">
        <a-descriptions-item label="认款单号">{{ model.RecognitionNo || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="归属人">{{ model.BusinessCreator || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ model.CreateTime || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
      <div class="des-title">返货采购订单：</div>
      <a-table :bordered="false" ref="table" rowKey="Id" size="small" :columns="columns" :dataSource="dataSource" :pagination="false" :scroll="{ y: '300px', x: '100%' }">
        <!-- 字符串超长截取省略号显示-->
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </span>
      </a-table>
      <div class="des-title">认款协议：</div>
      <a-table :bordered="false" ref="table" rowKey="Id" size="small" :columns="columns1" :dataSource="dataSource1" :pagination="false" :scroll="{ y: '300px', x: '100%' }">
        <!-- 字符串超长截取省略号显示-->
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </span>
        <!-- 促销时间 -->
        <span slot="AgreementStartTimeVal" slot-scope="text,record,index">
          <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
        </span>
      </a-table>
      <div class="des-title">本次认款明细：(本次合计认款数量：{{ Total }})</div>
      <a-table :bordered="false" ref="table" rowKey="Id" size="small" :columns="columns2" :dataSource="dataSource2" :pagination="false" :scroll="{ y: '300px', x: '100%' }">
        <!-- 字符串超长截取省略号显示-->
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </span>
      </a-table>
      <a-descriptions :column="1" class="des-title">
        <a-descriptions-item label="备注">{{ model.Note || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
    </div>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
import moment from "moment";

export default {
  name: "ReturnGoodsSubsInfoModal",
  mixins: [ListMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "返货认款单详情",
      visible: false,
      model: {},
      confirmLoading: false,
      httpHead: 'P36012',
      isTableInitData:false,
      linkUrlType:'GET',
      url: {
        detail: '/{v}/ReturnGoods/GetInfo/GetInfoAsync',
      },
      columns: [
          {
            title: '采购订单号',
            dataIndex: 'PurchaseOrderNo',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '供应商',
            dataIndex: 'SupplierName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '返货数量',
            dataIndex: 'TotalOrderReturnCount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '未认款数量',
            dataIndex: 'TotalOrderRemainingCount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
      ],
      columns1: [
          {
            title: '协议编号',
            dataIndex: 'AgreementNo',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '甲方',
            dataIndex: 'PartyFirstName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '促销时间',
            dataIndex: 'AgreementStartTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'AgreementStartTimeVal' }
          },
          {
            title: '核销方式',
            dataIndex: 'AccountingMethodStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '票折应收金额',
            dataIndex: 'TotalRebateCount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
      ],
      columns2: [
          {
            title: '商品名称',
            dataIndex: 'ErpGoodsName',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '规格',
            dataIndex: 'PackingSpecification',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '单位',
            dataIndex: 'PackageUnit',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '生产厂商',
            dataIndex: 'BrandManufacturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '商品编号',
            dataIndex: 'ErpGoodsCode',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '返货应收数量',
            dataIndex: 'AgreementCount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
          {
            title: '本次认款数量',
            dataIndex: 'RecognitionCount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' }
          },
      ],
      dataSource:[],
      dataSource1:[],
      dataSource2:[],
    };
  },
  mounted() { },
  created() { },
  computed: {
    Total(){
      if(this.dataSource2&&this.dataSource2.length){
        let total = 0
        this.dataSource2.forEach(e => {
          total += e.RecognitionCount;
        });
        return total
      }else{
        return 0
      }
    }
  },
  methods: {
    moment,
    show(record) {
      this.visible = true;
      this.getDetail(record.Id)
    },
    getDetail(id){
      getAction(this.url.detail, {
        returnGoodsRecognitionId:id
      }, this.httpHead).then(res => {
        if(res.IsSuccess&&res.Data){
          this.model = res.Data;
          this.dataSource = [{
            PurchaseOrderNo:res.Data.PurchaseOrderNo,
            SupplierName:res.Data.SupplierName,
            TotalOrderReturnCount:res.Data.TotalOrderReturnCount,
            TotalOrderRemainingCount:res.Data.TotalOrderRemainingCount,
          }];
          this.dataSource1 = [{
            AgreementNo:res.Data.AgreementNo,
            PartyFirstName:res.Data.PartyFirstName,
            AgreementStartTime:res.Data.AgreementStartTime,
            AgreementEndTime:res.Data.AgreementEndTime,
            AccountingMethodStr:res.Data.AccountingMethodStr,
            TotalRebateCount:res.Data.TotalRebateCount,
          }]

          this.dataSource2 = res.Data.ReturnGoods||[];
        }else{
          this.$message.warning(res.Msg)
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
<style scoped>
.title-box {
  border: 1px solid #e8e8e8;
  border-bottom: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 15px;
}

.des-title{
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.85);
  padding: 20px 0 10px 0;
}
</style>
