<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="onSetValid"
      @operate="operate"
      @changeTab="changeTab"
      @simpleTableLoadAfter="simpleTableLoadAfter"
    >
      <!-- 自定义插槽 -->
      <!-- 价格显示-->
      <span slot="price" slot-scope="{ text }">
        <j-ellipsis :value="text || text == 0 ? (text != 0 ? '¥' + text : '0') : '--'" />
      </span>
      <div style="margin-top: 5px" slot="bottomBtn" v-if="source != 22">
        <span style="margin-right: 15px">合计收款金额：¥{{ countObj.TotalReceiptAmount || 0 }}</span>
        <span style="margin-right: 15px">已认款金额：¥{{ countObj.TotalRecognizedAmount || 0 }}</span>
        <span style="margin-right: 15px">待认款金额：¥{{ countObj.TotalUnRecognizedAmount || 0 }}</span>
      </div>
    </SimpleTable>
    <!-- 认款 -->
    <AcceptanceFunModal ref="AcceptanceFunModal" @ok="modelOk"></AcceptanceFunModal>
    <!-- 认款记录 -->
    <AcceptanceFunListModal ref="AcceptanceFunListModal" @ok="modelOk"></AcceptanceFunListModal>
    <!-- 详情 -->
    <AcceptanceFunListInfoModal ref="AcceptanceFunListInfoModal"></AcceptanceFunListInfoModal>
    <!-- 特殊认款 返货抵钱现金抵返货和票折抵返货详情 -->
    <SpecialSubsScriptionInfoModal ref="SpecialSubsScriptionInfoModal"></SpecialSubsScriptionInfoModal>
    <!-- 认款记录导出 -->
    <RecordListExportModal ref="RecordListExportModal"></RecordListExportModal>
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'LetterListBase',
  components: { JEllipsis },
  mixins: [SimpleMixin],
  props: {
    searchInputArray: {
      type: Array,
      default: () => {
        return []
      },
    },
    columnsArray: {
      type: Array,
      default: () => {
        return []
      },
    },
    sortColumns: {
      type: Array,
      default: () => {
        return []
      },
    },
    actionBtn: {
      type: Object,
      default: () => {
        return { btn1: '', btn2: '' }
      },
    },
    url: {
      type: String,
      default: '',
    },
    httpHead: {
      type: String,
      default: '',
    },
    // 1 商销认款 2采购退货认款 3短期采购协议 4短期销售协议 5年度协议 6过账认款20.优利泰认款21：通用协议22:认款记录 23:预付款退款认款
    source: {
      type: Number,
      default: null,
    },
    exportParams: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '收款单号', type: 'input', value: '', key: 'ReceiptNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '收款方式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'ReceiptType',
          dictCode: 'EnumReceiptType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '收款日期',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['ReceiptTimeBegin', 'ReceiptTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        { name: '打款方', type: 'input', value: '', key: 'Payer', defaultVal: '', placeholder: '请输入' },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        sortArray: ['price'],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款日期',
          dataIndex: 'ReceiptTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '收款方式',
          dataIndex: 'ReceiptTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方',
          dataIndex: 'Payer',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款账户',
          dataIndex: 'AccountName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognizedAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '待认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          actionBtn: [
            {
              name: '认款',
              icon: '',
              id: this.actionBtn.btn1,
              specialShowFuc: (record) => {
                return record.RecognitionStatus != 3 && record.RemainingAmount > 0
              },
            },
            {
              name: '认款记录',
              icon: '',
              id: this.actionBtn.btn2,
              specialShowFuc: (record) => {
                return true
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      countObj: {}, //统计数据
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '/{v}/CashReceiptNew/GetReceipList',
        getCashReceipStatistics: '/{v}/CashReceiptNew/GetCashReceipStatistics', //现金统计
        getReceipStatistics: '/{v}/CashReceiptNew/GetReceipStatistics', //统计
      },
    }
  },
  created() {
    if (this.sortColumns && this.sortColumns.length > 0) {
      this.sortColumns.forEach((item) => {
        this.columns.splice(item.num, 0, item.val)
      })
    }
    if (this.searchInputArray && this.searchInputArray.length > 0) {
      this.searchInput = this.searchInputArray
    }
    if (this.columnsArray && this.columnsArray.length > 0) {
      this.columns = this.columnsArray
    }
    if (this.source == 22) {
      this.tab.operateBtn = [
        { name: '导出', type: 'primary', icon: '', key: '导出', id: '1e268f2a-7a9f-4c18-843a-7ea7e636fa6a' },
      ]
    }
  },
  activated() {
    // ReceiptType 现金 = 1 微信 = 2 支付宝 = 3 对公转账 = 4 票折 = 5
    if (this.url) {
      this.linkUrl.list = this.url
    }
    if (this.httpHead) {
      this.linkHttpHead = this.httpHead
    }
    this.setNeedToRecognize()
    let queryParam = Object.assign({}, this.$refs.SimpleSearchArea.queryParam)
    if (queryParam.ReceiptType) {
      queryParam.ReceiptType = parseInt(queryParam.ReceiptType)
    }
    if (queryParam.RecognitionOrderMode) {
      queryParam.RecognitionOrderMode = parseInt(queryParam.RecognitionOrderMode)
    }
    if (queryParam.RecognitionStatus) {
      queryParam.RecognitionStatus = parseInt(queryParam.RecognitionStatus)
    }
    if (queryParam.PushERPStatus) {
      queryParam.PushERPStatus = parseInt(queryParam.PushERPStatus)
    }
    if (queryParam.ExecuteStatus) {
      queryParam.ExecuteStatus = parseInt(queryParam.ExecuteStatus)
    }

    this.$refs.table.loadDatas(null, queryParam)
  },
  methods: {
    // 列表加载完成后
    simpleTableLoadAfter() {
      // console.log('simpleTableLoadAfter')
      if (this.source != 22) this.getCashReceipStatistics() //获取统计数据
    },
    // 获取统计数据
    getCashReceipStatistics() {
      let params = this.$refs.table.getQueryParams()
      postAction(
        [3, 4, 5, 20, 21].includes(this.source)
          ? this.linkUrl.getReceipStatistics
          : this.linkUrl.getCashReceipStatistics,
        params,
        this.linkHttpHead
      ).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.countObj = res.Data || {}
      })
    },
    // 原件照片数量
    showAuditOpinion(text, record) {
      this.$refs.PurchaseContractPhotoModal.show(record)
    },
    showOrderCount(text, record) {
      this.$refs.PurchaseContractOrderModal.show(record)
    },
    setNeedToRecognize() {
      // source 1 商销认款 2采购退货认款 3短期采购协议 4短期销售协议 5年度协议 6过账认款20.优利泰认款21：通用协议22:认款记录 23：预付款退款认款
      if ([1, 2, 3, 4, 5, 6, 20, 21, 23].includes(this.source)) {
        this.queryParam.NeedToRecognize = true
        this.$refs.SimpleSearchArea.queryParam.NeedToRecognize = true
      } else {
        this.queryParam.NeedToRecognize = undefined
        this.$refs.SimpleSearchArea.queryParam.NeedToRecognize = undefined
      }
    },
    modelOk() {
      this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      this.setNeedToRecognize()
      if (type == 'searchReset') {
        this.tab.status = ''
        delete queryParam.ReceiptType
      }
      queryParam = Object.assign({}, queryParam)
      if (queryParam.ReceiptType) {
        queryParam.ReceiptType = parseInt(queryParam.ReceiptType)
      }
      if (queryParam.RecognitionOrderMode) {
        queryParam.RecognitionOrderMode = parseInt(queryParam.RecognitionOrderMode)
      }
      if (queryParam.RecognitionStatus) {
        queryParam.RecognitionStatus = parseInt(queryParam.RecognitionStatus)
      }
      if (queryParam.PushERPStatus) {
        queryParam.PushERPStatus = parseInt(queryParam.PushERPStatus)
      }
      if (queryParam.ExecuteStatus) {
        queryParam.ExecuteStatus = parseInt(queryParam.ExecuteStatus)
      }
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '认款') {
        this.$refs.AcceptanceFunModal.show(record, this.columns, this.source)
      } else if (type == '认款记录') {
        this.$refs.AcceptanceFunListModal.show(record, this.columns, this.source)
      } else if (type == '详情') {
        //特殊认款
        if ([1, 2, 3].includes(record.RecognitionOrderMode)) {
          this.$refs.SpecialSubsScriptionInfoModal.show(record, false, record.RecognitionOrderMode)
        } else {
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
        }
      } else if (type == '导出') {
        let queryParam = {
          ...this.queryParam,
          ...this.$refs.SimpleSearchArea.queryParam,
        }
        this.$refs.RecordListExportModal.show(this.exportParams, queryParam)
      }
    },
  },
}
</script>

<style></style>
