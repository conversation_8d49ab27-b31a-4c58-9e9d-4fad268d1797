<template>
  <a-form-model ref="form" :model="model" layout="vertical">
    <div>
      <template>
        <div class="title-btn">
          <div class="xieyi">返货采购订单：</div>
          <div>
            <a-button type="primary" @click="chooseTableData(1)">选择</a-button>
          </div>
        </div>
        <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns1" :dataSource="dataSource1" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource1.length === 0 ? false:'350px',x:'1000px' }">
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
          <!-- 价格显示-->
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
          </span>
        </a-table>
      </template>
      <template>
        <div class="title-btn" style="padding-top: 20px;">
          <div class="xieyi">本次认款明细：（本次合计认款数量：{{ Total2 }}）</div>
        </div>
        <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns2" :dataSource="dataSource2" :pagination="false" :loading="confirmLoading2" :scroll="{ y: dataSource2.length === 0 ? false:'350px',x:'1000px' }">
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
          <!-- 价格显示-->
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
          </span>
          <template slot="Count" slot-scope="text, record">
            <a-input-number v-model="record.Count" style="width:100%" :min="0" :max="maxCount(record)" placeholder="请输入" @blur="(e)=>{onCountBlur(e,record)}" />
          </template>
        </a-table>
      </template>
      <!-- 选择协议的认款明细 -->
      <SpecialSubTypeBase ref="SpecialSubTypeBase" :rebateType="rebateType" v-if="isUpdate?(infoData?true:false):true" :infoData="infoData" :supplierId="dataSource1.length>0?(dataSource1[0].SupplierId):''"></SpecialSubTypeBase>

      <!--返货抵现金、返货抵票折 返货采购订单单选-->
      <TableSelectDataModal type="radio" ref="TableSelectDataModal1" :selectTableData="selectTableData1" isToSelectClose @chooseData="chooseData1"></TableSelectDataModal>

    </div>
  </a-form-model>

</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  mixins: [EditMixin],
  name: "SpecialSubsScriptionOneModal",
  components: {
    JEllipsis
  },
  props: {
    // 1返货抵钱 2现金抵返货 3票折抵返货
    rebateType: {
      type: Number,
      default: null
    },
    isUpdate: {
      type: Boolean,
      default: null,
    },
    record: {
      type: Object,
      default: () => {
        return {}
      },
    }
  },
  data() {
    return {
      confirmLoading: false,
      confirmLoading2: false,
      saveLoading: false,
      AmountAllNumVal: null,
      model: {
        Id: '',
        RecognitionAmount: '',
        Note: '',
      },
      infoData: null,
      rebateTypeList: [],
      //   返货采购订单
      selectTableData1: {
        searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '采购订单号', type: 'input', value: '', key: 'PurchaseOrderNo', defaultVal: '', placeholder: '请输入' },
          { name: '供应商', type: 'input', value: '', key: 'SupplierKey', defaultVal: '', placeholder: '请输入' },
        ],
        title: '返货采购订单',
        name: '返货采购订单',
        recordKey: 'Id',
        httpHead: 'P36009',
        isInitData: false,
        url: {
          list: '/{v}/PurchaseOrder/GetRecognizedListV2Async',
        },
        columns: [
          {
            title: '采购订单号',
            dataIndex: 'PurchaseOrderNo',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '供应商',
            dataIndex: 'SupplierName',
            width: 160,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '返货数量',
            dataIndex: 'TotalReturnGoodsCount',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '已认款数量',
            dataIndex: 'RecognizedCount',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '未认款数量',
            dataIndex: 'RemainingCount',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '订单归属人',
            dataIndex: 'OwnerByName',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '入库时间',
            dataIndex: 'WarehouseInTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 80,
            fixed: "right",
            scopedSlots: { customRender: 'action' },
          }
        ],
      },
      columns1: [
        {
          title: '采购订单号',
          dataIndex: 'PurchaseOrderNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalReturnGoodsCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'RemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns1Update: [
        {
          title: '采购订单号',
          dataIndex: 'PurchaseOrderNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalReturnCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'PreRemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns2: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalOrderCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'RecognitionCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'Count',
          width: 120,
          ellipsis: true,
          fixed: "right",
          scopedSlots: { customRender: 'Count' },
        },
      ],
      columns2Update: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalReturnCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'PreRemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'Count',
          width: 120,
          ellipsis: true,
          fixed: "right",
          scopedSlots: { customRender: 'Count' },
        },
      ],
      dataSource1: [],
      dataSource2: [],
      xyInfo5Data: {},
      xyListHttpHead: 'P36007',
      url: {
        xyInfo: '/{v}/PurchaseAgreement/GetInfoAsync',//年度协议认款
        orderInfo: '/{v}/PurchaseOrder/GetRecognizedDetailAsync',//返货单据明细
        info: '/{v}/RecognitionSpecial/GetReturnGoodsEditInfoAsync',//返货抵钱编辑用
      }
    }
  },
  computed: {
    Total2() {
      if (this.dataSource2 && this.dataSource2.length) {
        let total = 0
        this.dataSource2.forEach(e => {
          total += e.Count;
        });
        return total
      } else {
        return 0
      }
    },
    maxCount() {
      return function (record) {
        if (this.isUpdate) {
          return record.PreRemainingCount
        } else {
          let num1 = Number(this.dataSource1[0].RemainingCount || 0)
          let num2 = Number(record.RecognitionCount || 0)
          let useNum = num1 > num2 ? num2 : num1
          return useNum
        }
      }
    },
  },
  created() {
    if (this.isUpdate) {
      this.getInfo()
    } else {
      this.infoData = null
    }
  },
  methods: {
    getInfo() {
      let params = {
        id: this.record.Id,
      }
      this.confirmLoading2 = true
      this.dataSource2 = []
      getAction(this.url.info, params, 'P36012').then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        // 初始化赋值Count
        if (res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails && res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails.length > 0) {
          res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails.map((item) => {
            item.Count = 0
          })
        }

        this.infoData = res.Data
        this.$emit('setData', this.infoData)
        //PurchaseAgreementType 1年度协议 2短期销售协议 3短期采购协议
        this.PurchaseAgreementType = res.Data.PurchaseAgreementType
        res.Data.PurchaseOrderInfoDto.Id = res.Data.PurchaseOrderInfoDto.PurchaseOrderId //采购单Id
        switch (this.PurchaseAgreementType) {
          case 1: this.columns1 = this.columns1Update
            this.columns2 = this.columns2Update
            this.dataSource1 = [res.Data.PurchaseOrderInfoDto] || [] //返货采购订单
            // 还原本次认款数量
            if (res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails.length > 0) {
              res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails.map((item) => {
                item.Count = item.RecognitionCount
              })
            }
            this.dataSource2 = res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails || [] //本次认款明细
            break;
          case 2: this.columns1 = this.columns1Update
            this.columns2 = this.columns2Update
            this.dataSource1 = [res.Data.PurchaseOrderInfoDto] || [] //返货采购订单
            // 还原本次认款数量
            if (res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails.length > 0) {
              res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails.map((item) => {
                item.Count = item.RecognitionCount
              })
            }
            this.dataSource2 = res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails || [] //本次认款明细
            break;
          case 3: this.columns1 = this.columns1Update
            this.columns2 = this.columns2Update
            this.dataSource1 = [res.Data.PurchaseOrderInfoDto] || [] //返货采购订单
            // 还原本次认款数量
            if (res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails.length > 0) {
              res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails.map((item) => {
                item.Count = item.RecognitionCount
              })
            }
            this.dataSource2 = res.Data.PurchaseOrderInfoDto.ReturnGoodsDetails || [] //本次认款明细
            break;
        }

        // 重新获取dataSource1的数据
        let PurchaseOrderNo = res.Data.PurchaseOrderInfoDto.PurchaseOrderNo || ''
        this.getDataSource1Data(PurchaseOrderNo, (data) => {
          // 编辑重组数据
          data.map((item) => {
            item.TotalReturnCount = item.TotalReturnGoodsCount
            item.PreRemainingCount = item.RemainingCount
          })
          this.dataSource1 = data
        })

      }).finally(() => {
        this.confirmLoading2 = false
      })
    },
    getDataSource1Data(PurchaseOrderNo, callback) {
      let params = {
        PurchaseOrderNo: PurchaseOrderNo,
        IsOnlyRecognition: true,
      }
      getAction('/{v}/PurchaseOrder/GetRecognizedListV2Async', params, 'P36009').then(res => {
        if (res.IsSuccess && res.Data) {
          callback && callback(res.Data)
        } else {
          this.$message.warning(res.Msg)
        }
      }).finally(() => {

      })
    },
    chooseTableData(type) {
      this.tableType = type;
      let queryParam = {
        IsOnlyRecognition: true
      }
      if (type == 1) {
        this.$refs.TableSelectDataModal1.show(this.dataSource1, queryParam)
      }
    },
    // 选择的数据
    chooseData1(data) {
      if (data && data.length > 0) {
        // 编辑重组数据
        if (this.isUpdate) {
          data.map((item) => {
            item.TotalReturnCount = item.TotalReturnGoodsCount
            item.PreRemainingCount = item.RemainingCount
          })
        }
        this.dataSource1 = data;
        if (this.dataSource1 && this.dataSource1.length) {
          this.getRecognizedDetail(this.dataSource1[0].Id);
        }
        this.dataSource3 = [];
      }
      // 初始化清空数据
      this.$refs.SpecialSubTypeBase.init()
    },
    checkDataOne() {
      if (this.dataSource1.length == 0) {
        this.$message.warning('请选择返货采购订单')
        return false
      } else if (this.dataSource2.length > 0) {
        let isNo = false
        if (!this.dataSource2[0].Count) {
          this.$message.warning('本次认款数量不能为空')
          isNo = true
        }
        // for (let i = 0; i < this.dataSource2.length; ++i) {
        //   if (!this.dataSource2[i].Count && this.dataSource2[i].Count != 0) {
        //     this.$message.warning('本次认款数量不能为空')
        //     isNo = true
        //     break;
        //   }
        // }
        if (isNo) {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },
    checkData(callback) {
      if (this.checkDataOne()) {
        this.$refs.SpecialSubTypeBase.checkDataTwo((data) => {
          data.dataSource1 = this.dataSource1
          data.dataSource2 = this.dataSource2
          callback && callback(data)
        })
      }
    },
    onCountBlur(e, record) {
      // console.log(e.target.value, record)
    },
    //请求本次认款明细
    getRecognizedDetail(id) {
      this.dataSource2 = [];
      this.confirmLoading2 = true
      // this.dataSource1[0].RemainingCount = 900
      getAction(this.url.orderInfo, { purchaseOrderId: id }, 'P36009').then(res => {
        if (res.IsSuccess && res.Data) {
          if (res.Data.length) {
            // 计算本次认款数量
            let addCount = 0
            let dataSource1Count = this.dataSource1[0].RemainingCount || 0
            let addCountArray = []
            res.Data.forEach((e, index) => {
              let countNum = e.RecognitionCount
              if (dataSource1Count > 0) {
                addCount += countNum
                addCountArray.push({
                  count: addCount,
                  countIndex: index
                })
                if ((addCount > dataSource1Count) && index > 0) {
                  countNum = this.numFixed(dataSource1Count - addCountArray[index - 1].count)
                }
              }
              e.Count = countNum;
            })
            this.dataSource2 = JSON.parse(JSON.stringify(res.Data))
          }
        } else {
          this.$message.warning(res.Msg)
        }
      }).finally(() => {
        this.confirmLoading2 = false
      })
    },
  },
}
</script>

<style lang="less" scoped>
.xieyi {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}
.xieyi::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

.title-btn {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  justify-items: center;
  align-items: center;
}

.title-btn .xieyi {
  padding-bottom: 0;
}
</style>