<!-- 认款记录 -->
<template>
  <a-modal
    :title="title"
    :width="1000"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <a-descriptions :column="4" v-if="model">
        <a-descriptions-item label="收款金额">{{ model.ReceiptAmount?'¥'+model.ReceiptAmount:' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="收款时间">{{ model.ReceiptTime || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="已认款金额">{{ model.RecognizedAmount?'¥'+model.RecognizedAmount:' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="可认款金额">{{ model.RemainingAmount?'¥'+model.RemainingAmount:' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="打款方">{{ model.Payer || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="银行名称">{{ model.BankName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="银行卡号">{{ model.AccountNumber || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="状态">{{ model.RecognitionStatusStr || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
      <a-table
        :bordered="false"
        ref="table"
        :rowKey="(record,index)=>index"
        size="middle"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="false"
        :loading="confirmLoading"
        :scroll="{ y: dataSource.length === 0 ? false:'350px',x:'1000px' }">
        <!-- 字符串超长截取省略号显示-->
        <template slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </template>
        <template slot="IsCancel" slot-scope="text">
          <span>{{ text ? '取消' : '已正常' }}</span>
        </template>
        <span slot="price" slot-scope="text">
          <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
        </span>
        <!-- 操作 -->
        <span slot="action" slot-scope="text, record,index">
          <a style="margin:0 5px;" @click="operate(record,'edit')" v-if="!record.IsCancel">撤销</a>
        </span>
      </a-table>
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
    </a-row>
    <ConfirmRecordInfoModal ref="ConfirmRecordInfoModal"></ConfirmRecordInfoModal>
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  mixins: [EditMixin],
  name: "ConfirmRecordModal",
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "认款记录",
      visible: false,
      model: {},
      Edit: false,
      confirmLoading: false,
      httpHead: 'P36012',
      url: {
        list: "/{v}/CashRecognition/GetRecordListAsync",
      },
      dataSource: [],
      columns: [
        {
          title: '认款人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '可认款金额',
          dataIndex: 'PreRemainingValue',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '认款类型',
          dataIndex: 'CashRecognitionTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '兑付方',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '状态',
          dataIndex: 'IsCancel',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'IsCancel' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          fixed: "right",
          scopedSlots: { customRender: 'action' }
        }
      ],
    };
  },
  mounted() {

  },
  created() { },
  methods: {
    moment,
    show(record) {
      this.model = {};
      this.title = '认款记录';
      this.visible = true;
      this.model = record || undefined;
      this.getDetail(record)
    },
    getDetail(record) {
      // if (!id) return
      getAction(this.url.list, { cashReceiptId: record.Id }, this.httpHead).then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (!res.Data) return
        this.dataSource = res.Data || []
      })
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
    // 列表操作
    operate(record, type) {
      if (type == 'edit') {
        var that = this
        this.$confirm({
          title: '确认撤销',
          content: '是否撤销选中数据?',
          onOk: function () {
            postAction('/{v}/CashRecognition/CancelAsync?cashRecognitionRecordId=' + record.CashRecognitionRecorId, { cashRecognitionRecordId: record.CashRecognitionRecorId }, 'P36012').then(res => {
              if (!res.IsSuccess) {
                that.$message.warning(res.Msg)
                return
              }
              if (!res.Data) return
              that.getDetail(that.model);
            })
          }
        })
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ant-table-tbody tr {
  height: 46px !important;
}
</style>