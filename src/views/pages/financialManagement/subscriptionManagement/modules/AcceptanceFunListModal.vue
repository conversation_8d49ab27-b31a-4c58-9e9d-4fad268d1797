<!-- 认款记录 -->
<template>
  <a-modal
    :title="title"
    :width="1000"
    :visible="visible"
    :saveLoading="saveLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :footer="null"
    :destroyOnClose="true"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :rules="rules" :model="model">
        <div class="log">收款单：</div>
        <a-table
          :bordered="false"
          ref="table"
          :rowKey="(record, index) => index"
          size="middle"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="false"
          :loading="confirmLoading"
          :scroll="{ y: dataSource.length === 0 ? false : '200px', x: '1000px' }"
        >
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
          <!-- 价格显示-->
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text == 0 ? (text != 0 ? '¥' + text : '0') : '--'" />
          </span>
        </a-table>

        <!-- 协议标题 -->
        <div style="margin-top: 16px">
          <div class="log">认款记录：</div>
          <a-card :bodyStyle="{ padding: '16px' }" style="margin-bottom: 16px">
            <a-table
              :bordered="false"
              ref="table"
              :rowKey="(record, index) => index"
              size="middle"
              :columns="columns2"
              :dataSource="dataSource2"
              :pagination="false"
              :rowClassName="rowClassNameFun"
              :loading="confirmLoading2"
              :scroll="{ y: dataSource2.length === 0 ? false : '350px', x: '1000px' }"
            >
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text == 0 ? (text != 0 ? '¥' + text : '0') : '--'" />
              </span>
              <!-- 认款状态 -->
              <span slot="priceStatus" slot-scope="text">
                <span :style="{ color: 'green' }">{{ text }}</span>
              </span>
              <!-- 操作 -->
              <span slot="action" slot-scope="text, record, index">
                <!-- <a style="margin: 0 5px" @click="operate('info', record, index)">详情</a> -->

                <YYLButton
                  style="padding: 0 5px"
                  :menuId="source == 8 ? 'e7cdc710-08c6-4004-b19a-3bdebaf84089' : ''"
                  text="详情"
                  type="link"
                  @click="operate('info', record, index)"
                />
                <!-- <a style="margin: 0 5px" v-if="checkCx(record)" @click="operate('del', record, index)">撤销</a> -->
                <YYLButton
                  style="padding: 0 5px"
                  :menuId="getMenuId(source, record)"
                  text="撤销"
                  type="link"
                  @click="operate('del', record, index)"
                />
              </span>
            </a-table>
          </a-card>
          <div v-if="source == 9">认款合计数量：{{ AmountAllCountVal }}</div>
          <div v-else>认款合计金额：¥{{ AmountAllNumVal }}</div>
        </div>
      </a-form-model>
    </a-spin>
    <!-- 详情 -->
    <AcceptanceFunListInfoModal ref="AcceptanceFunListInfoModal"></AcceptanceFunListInfoModal>
    <!-- 特殊认款 返货抵钱现金抵返货和票折抵返货详情 -->
    <SpecialSubsScriptionInfoModal ref="SpecialSubsScriptionInfoModal"></SpecialSubsScriptionInfoModal>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'

export default {
  name: 'AcceptanceFunListModal',
  mixins: [ListMixin],
  components: {
    JEllipsis,
  },
  data() {
    return {
      title: '认款记录',
      visible: false,
      model: {
        PayerId: '',
        Payer: '',
      },
      recordModel: {},
      confirmLoading: false,
      confirmLoading2: false,
      saveLoading: false,
      xyTitle: '',
      source: null,
      dataSource: [],
      dataSource2: [],
      columns: [
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款日期',
          dataIndex: 'ReceiptTime',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款方式',
          dataIndex: 'ReceiptTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方',
          dataIndex: 'Payer',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款账户',
          dataIndex: 'AccountName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
      columns19: [
        {
          title: '采购订单号',
          dataIndex: 'PurchaseOrderNo',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalReturnGoodsCount',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'RemainingCount',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns2: [
        {
          title: '认款人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognitionAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '认款方式',
          dataIndex: 'RecognitionOrderModeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '执行状态',
          dataIndex: 'ExecuteStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '状态',
          dataIndex: 'RecognitionStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'priceStatus' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 160,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
        },
      ],
      PayerId: null,
      RemainingAmount: null, //计算的未认款金额
      AmountAllNumVal: 0, //合计金额
      AmountAllCountVal: 0, //合计数量
      curCheckXy: undefined,
      selectedRowKeys: [],
      selectedRowArray: [],
      httpHead: 'P36012',
      url: {
        xyList: '',
        xyList1: '/{v}/Recognition/GetReceitRecognitionListAsync', //商销认款记录
        xyList2: '/{v}/Recognition/GetReceitRecognitionListAsync', //采购退货记录
        xyList3: '/{v}/Recognition/GetReceitRecognitionListAsync', //短期采购协议记录
        xyList4: '/{v}/Recognition/GetReceitRecognitionListAsync', //短期销售记录
        xyList5: '/{v}/Recognition/GetReceitRecognitionListAsync', //年度协议记录
        xyList6: '/{v}/Recognition/GetReceitRecognitionListAsync', //过账认款记录
        xyList7: '/{v}/Recognition/GetReceitRecognitionListAsync', //现金池记录
        xyList8: '/{v}/Recognition/GetReceitRecognitionListAsync', //票折池记录
        xyList9: '/{v}/RecognitionReturnGoods/GetListAsync', //返货池记录
        xyList11: '/{v}/Recognition/GetReceitRecognitionListAsync', //采购预付款调价认款
        xyList20: '/{v}/Recognition/GetReceitRecognitionListAsync', //优利泰认款,
        xyList21: '/{v}/Recognition/GetReceitRecognitionListAsync', //通用协议认款,
        xyList23: '/{v}/Recognition/GetReceitRecognitionListAsync', //预付款退款认款,
        del: '', //撤销
        del1: '/{v}/Recognition/CancelSaleAsync', //商销认款撤销
        del2: '/{v}/Recognition/CancelPurchaseRefundAsync', //采购退货撤销
        del3: '/{v}/Recognition/CancelShortAgreementAsync', //短期采购协议撤销
        del4: '/{v}/Recognition/CancelAgreementAsync', //短期销售协议撤销
        del5: '/{v}/Recognition/CancelAgreementAsync', //年度协议撤销
        del6: '/{v}/Recognition/CancelPostingAsync', //过账认款撤销
        del23: '/{v}/RecognitionPrepaymentRefund/Cancel', //预付款退款认款撤销
        del7: '', //现金池撤销
        del8: '', //票折池撤销
        del9: '/{v}/RecognitionReturnGoods/CancelAsync', //返货池撤销
        del10: '/{v}/RecognitionSpecial/CancelSpecialAsync', //特殊认款撤销
        del11: '', //采购预付款调价认款撤销
        del20: '/{v}/RecognitionYLT/Cancel', //优利泰撤销
        del21: '/{v}/RecognitionGeneralAgreement/Cancel', //通用协议撤销
        del16: '/{v}/RecognitionReturnGoods/CancelAsync', //返货认款撤销
      },
    }
  },
  mounted() {},
  created() {},
  computed: {
    rules() {
      return {
        PayerId: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.PayerId) {
                callback(new Error('请选择兑付方!'))
              } else {
                callback()
              }
            },
          },
        ],
        AccountBookNo: [{ required: true, message: '请选择账薄!' }],
      }
    },
  },
  methods: {
    moment,
    getMenuId(source, record) {      
      if (source == 7 && this.checkCx(record)) {
        // 现金池撤销按钮
        return 'aa9b6250-709e-4596-817e-710a3ff5dcf4'
      } else if (source == 8 && this.checkCx(record)) {
        // 票折池撤销按钮
        return '2297db5f-a4ce-4f3d-9b1a-70a4a838b5fc'
      } else if (source == 9 && this.checkCx(record)) {
        // 返货池撤销按钮
        return '697cf208-fb1b-43e1-997d-4d69b91ad7d8'
      } else {
        return null
      }
      
    },
    show(record, columns, source) {
      console.log('source=', source)
      this.recordModel = record
      this.dataSource = [record]
      this.source = source
      this.RemainingAmount = record.RemainingAmount
      this.initData()
      this.title = '认款记录'
      this.purchaseRecord(source)
      this.getXyDetail()
      this.visible = true
    },
    checkCx(record) {
      let status = true
      // source 1 商销认款 2采购退货认款 3短期采购协议 4短期销售协议 5年度协议 6过账认款 7现金池 8票折池 9返货池 11采购预付款调价认款20优利泰认款 23：预付款退款认款
      // recognitionOrderMode 10商销订单 11采购退货单 20预付款退款认款
      switch (record.RecognitionOrderMode) {
        case 17:
          status = false
          break
        default:
          status =
            this.source != 1 &&
            this.source != 2 &&
            [10, 11].indexOf(record.RecognitionOrderMode) <= -1 &&
            record.RecognitionStatus != 4
          break
      }
      return status
    },
    // 认款记录
    purchaseRecord(source) {
      //1 商销认款 2采购退货认款 3短期采购协议 4短期销售协议 5年度协议 6过账认款 7现金池 8票折池 9返货池 11采购预付款调价认款20优利泰认款 23：预付款退款认款
      switch (source) {
        case 1:
          this.xyTitle = '商销订单'
          this.url.xyList = this.url.xyList1
          this.url.del = this.url.del1
          break
        case 2:
          this.xyTitle = '采购退货单'
          this.url.xyList = this.url.xyList2
          this.url.del = this.url.del2
          break
        case 3:
          this.xyTitle = '短期采购协议'
          this.url.xyList = this.url.xyList3
          this.url.del = this.url.del3
          break
        case 4:
          this.xyTitle = '短期销售协议'
          this.url.xyList = this.url.xyList4
          this.url.del = this.url.del4
          break
        case 5:
          this.xyTitle = '本次认款明细'
          this.url.xyList = this.url.xyList5
          this.url.del = this.url.del5
          break
        case 6:
          this.xyTitle = '过账认款'
          this.url.xyList = this.url.xyList6
          this.url.del = this.url.del6
          break
        case 7:
          this.xyTitle = '现金池'
          this.url.xyList = this.url.xyList7
          break
        case 8:
          this.xyTitle = '票折池'
          this.url.xyList = this.url.xyList8
          break
        case 9:
          this.xyTitle = '返货池'
          this.columns = this.columns19
          if (this.columns2[1].title == '认款金额') {
            let obj = {
              title: '认款数量',
              dataIndex: 'RecognitionCount',
              width: 120,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            }
            this.columns2.splice(1, 1, obj)
          }
          this.url.xyList = this.url.xyList9
          this.url.del = this.url.del10
          break
        case 11:
          this.xyTitle = '采购预付款调价认款'
          this.url.xyList = this.url.xyList11
          this.url.del = this.url.del11
          break
        case 20:
          this.xyTitle = '优利泰认款'
          this.url.xyList = this.url.xyList20
          this.url.del = this.url.del20
          break
        case 21:
          this.xyTitle = '通用协议认款'
          this.url.xyList = this.url.xyList21
          this.url.del = this.url.del21
          break
        case 23:
          this.xyTitle = '预付款退款认款'
          this.url.xyList = this.url.xyList23
          this.url.del = this.url.del23
          break
        default:
          this.xyTitle = ''
      }
    },
    initData() {
      this.model = {}
      this.dataSource2 = []
      this.selectedRowKeys = []
      this.selectedRowArray = []
    },
    getXyDetail(paramVal, id) {
      let params = {
        receiptId: this.dataSource[0].Id,
        ...paramVal,
      }
      switch (this.source) {
        // case 1: params.recognitionOrderMode = 10; break;//商销订单
        // case 2: params.recognitionOrderMode = 11; break;//采购退货单
        // case 3: params.recognitionOrderMode = 12; break;//短期采购协议
        // case 4: params.recognitionOrderMode = 13; break;//短期销售协议
        // case 5: params.recognitionOrderMode = 14; break;//年度协议
        // case 6: params.recognitionOrderMode = 15; break;//过账认款
        case 7:
          params.recognitionOrderType = 10
          break //现金池
        case 8:
          params.recognitionOrderType = 11
          break //票折池
        case 9:
          params = { orderId: this.dataSource[0].Id }
          break //返货池
        default:
          this.xyTitle = ''
      }
      this.confirmLoading2 = true
      // console.log('this.url.xyList  ', this.url.xyList)
      getAction(this.url.xyList, params, this.httpHead)
        .then((res) => {
          if (!res.IsSuccess) {
            this.$message.warning(res.Msg)
            return
          }
          this.dataSource2 = res.Data
          this.AmountAllNumVal = 0
          this.AmountAllCountVal = 0
          if (this.dataSource2.length > 0) {
            this.dataSource2.map((item) => {
              if (item.RecognitionStatus == 1) {
                this.AmountAllNumVal += item.RecognitionAmount
                this.AmountAllCountVal += item.RecognitionCount
              }
            })
          }
        })
        .finally(() => {
          this.confirmLoading2 = false
        })
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (type == 'searchReset') {
      }
      queryParam.PayerId = this.model.PayerId
      this.getXyDetail(queryParam)
    },
    // table行的颜色
    rowClassNameFun(record, index) {
      if (record.RecognitionAmount < 0) {
        return 'table-color-red-dust'
      }
    },

    modelOk(list) {},
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.model = {}
      this.visible = false
      this.$emit('close')
    },
    subDel(record, index) {
      let url = this.url.del + '?id=' + record.Id
      if (this.source == 6 || this.url.del == this.url.del6) {
        url = this.url.del + '?postingid=' + record.Id
      }
      //优利泰撤销和通用协议撤销 和预付款退款认款

      if ([18, 19, 20].includes(record.RecognitionOrderMode)) {
        url = this.url.del + '?recognitionOrderId=' + record.Id
        putAction(url, {}, 'P36012')
          .then((res) => {
            if (!res.IsSuccess) {
              this.$message.warning(res.Msg)
              return
            }
            this.$message.success('撤销成功')
            this.$emit('ok')
            this.getXyDetail()
            this.getUpdateData(record) //更新价格
          })
          .finally(() => {})
        return
      }
      postAction(url, {}, 'P36012')
        .then((res) => {
          if (!res.IsSuccess) {
            this.$message.warning(res.Msg)
            return
          }
          this.$message.success('撤销成功')
          this.$emit('ok')
          this.getXyDetail()
          this.getUpdateData(record) //更新价格
        })
        .finally(() => {})
    },
    //更新页面上的价格
    getUpdateData(record) {
      let url = ''
      let params = {
        PageIndex: 1,
        PageSize: 1,
      }
      let httpHead = 'P36012'
      switch (record.RecognitionOrderMode) {
        case 1: //返货池
          url = '/{v}/PurchaseOrder/GetRecognizedListV2Async'
          httpHead = 'P36009'
          params.PurchaseOrderNo = this.recordModel.PurchaseOrderNo
          break
        case 2: //现金池
          url = '/{v}/CashReceiptNew/GetCashList'
          params.ReceiptNo = this.recordModel.ReceiptNo
          break
        case 3: //票折池
          url = '/{v}/CashReceiptNew/GetInvoiceDiscountReceipList'
          params.ReceiptNo = this.recordModel.ReceiptNo
          break
        case 10: //商销认款
          url = '/{v}/CashReceiptNew/GetCashList'
          params.ReceiptNo = this.recordModel.ReceiptNo
          break
        case 11: //采购退货认款
          url = '/{v}/CashReceiptNew/GetCashList'
          params.ReceiptNo = this.recordModel.ReceiptNo
          break
        case 12: //短期采购协议认款
          url = '/{v}/CashReceiptNew/GetCashList'
          params.ReceiptNo = this.recordModel.ReceiptNo
          break
        case 13: //短期销售协议认款
          url = '/{v}/CashReceiptNew/GetCashList'
          params.ReceiptNo = this.recordModel.ReceiptNo
          break
        case 14: //年度协议认款
          url = '/{v}/CashReceiptNew/GetCashList'
          params.ReceiptNo = this.recordModel.ReceiptNo
          break
        case 15: //过账认款
          url = '/{v}/CashReceiptNew/GetCashList'
          params.ReceiptNo = this.recordModel.ReceiptNo
          break
        case 18: //优利泰认款
          url = '/{v}/CashReceiptNew/GetCashList'
          params.ReceiptNo = this.recordModel.ReceiptNo
          break
        case 19: //通用协议认款
          url = '/{v}/CashReceiptNew/GetCashList'
          params.ReceiptNo = this.recordModel.ReceiptNo
          break
        case 20: //预付款退款认款
          url = '/{v}/CashReceiptNew/GetCashList'
          params.ReceiptNo = this.recordModel.ReceiptNo
          break
      }
      switch (this.source) {
        case 8: //票折池
          url = '/{v}/CashReceiptNew/GetInvoiceDiscountReceipList'
          params.ReceiptNo = this.recordModel.ReceiptNo
          break
      }
      // console.log('url ',url)
      if (!url) return

      if (
        [2, 3, 10, 11, 12, 13, 14, 15, 18, 19, 20].includes(record.RecognitionOrderMode) ||
        [8].includes(this.source)
      ) {
        postAction(url, params, httpHead).then((res) => {
          if (res.IsSuccess && res.Data.length) {
            this.dataSource = res.Data
          }
        })
      } else {
        getAction(url, params, httpHead).then((res) => {
          if (res.IsSuccess && res.Data.length) {
            this.dataSource = res.Data
          }
        })
      }
    },
    operate(type, record, index) {
      if (type == 'info') {
        // 认款记录详情 RecognitionOrderMode
        // 返货抵钱 = 1,
        // 现金抵返货 = 2,
        // 票折抵返货 = 3,
        // 商销认款 = 10,
        // 返货认款 = 16,
        // 采购退货认款 = 11,
        // 短期采购协议认款 = 12,
        // 短期销售协议认款 = 13,
        // 年度协议认款 = 14,
        // 过账 = 15
        switch (record.RecognitionOrderMode) {
          case 1:
            this.$refs.SpecialSubsScriptionInfoModal.show(record, false, 1)
            break //返货抵钱
          case 2:
            this.$refs.SpecialSubsScriptionInfoModal.show(record, false, 2)
            break //现金抵返货
          case 3:
            this.$refs.SpecialSubsScriptionInfoModal.show(record, false, 3)
            break //票折抵返货
          default:
            this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
        }
      } else if (type == 'del') {
        console.log('this.source ', this.source)
        console.log('record.RecognitionOrderMode  ', record.RecognitionOrderMode)
        if ([7, 8, 20, 21, 23].includes(this.source)) {
          switch (record.RecognitionOrderMode) {
            case 13:
              this.url.del7 = this.url.del4
              this.url.del = this.url.del7 //现金短期销售协议认款
              this.url.del8 = this.url.del4
              this.url.del = this.url.del8 //票折撤销
              break
            case 14:
              this.url.del7 = this.url.del5
              this.url.del = this.url.del7 //现金年度协议认款
              this.url.del8 = this.url.del5
              this.url.del = this.url.del8 //票折
              break
            case 11:
              this.url.del7 = this.url.del2
              this.url.del = this.url.del7 //现金采购退货认款
              this.url.del8 = this.url.del2
              this.url.del = this.url.del8 //票折
              break
            case 10:
              this.url.del7 = this.url.del1
              this.url.del = this.url.del7 //现金商销认款
              this.url.del8 = this.url.del1
              this.url.del = this.url.del8 //票折
              break
            case 12:
              this.url.del7 = this.url.del3
              this.url.del = this.url.del7 //现金短期采购协议认款
              this.url.del8 = this.url.del3
              this.url.del = this.url.del8 //票折
              break
            case 15:
              this.url.del7 = this.url.del6
              this.url.del = this.url.del7 //现金过账
              this.url.del8 = this.url.del6
              this.url.del = this.url.del8 //票折
              break
            case 1:
            case 2:
            case 3:
              this.url.del7 = this.url.del10
              this.url.del = this.url.del7 //现金特殊认款
              this.url.del8 = this.url.del10
              this.url.del = this.url.del8 //票折
              break
          }
        }
        switch (record.RecognitionOrderMode) {
          case 18:
            this.url.del = this.url.del20 //优利泰撤销
            break
          case 19:
            this.url.del = this.url.del21 //通用协议撤销
            break
          case 20:
            this.url.del = this.url.del23 //预付款退款认款撤销
            break
          case 16:
            this.url.del = this.url.del16 //返货认款撤销
            break
        }
        // console.log('this.url  ',this.url)

        let that = this
        this.$confirm({
          title: '确定要撤销当前认款?',
          content: '',
          onOk() {
            that.subDel(record, index)
          },
          onCancel() {},
        })
      }
    },
  },
}
</script>
<style lang="less" scoped>
.xieyi,
.log {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}

.xieyi::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

.title-btn {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  justify-items: center;
}

.title-btn .xieyi {
  padding-bottom: 0;
}

.edit-btn {
  color: #1890ff;
  margin-left: 10px;
  cursor: pointer;
}
</style>
