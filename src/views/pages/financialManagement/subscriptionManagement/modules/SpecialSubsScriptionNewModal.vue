<template>
  <a-modal :title="title" :width="1000" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="saveLoading">
      <a-row :getter="10">
        <a-col :md="24">
          <div style="background: #fbe6e9; padding: 10px 16px; color: red" v-if="info.AuditStatus == 3 && info.AuditOpinion" :md="24">
            <span>驳回原因：{{ info.AuditOpinion || '' }}</span>
          </div>
          <a-spin :spinning="rebateLoading" tip="请稍后...">
            <a-form-model-item label="抵扣类型：" required>
              <a-radio-group @change="handleInput" :disabled="isUpdate?true:false" :value="model.RebateType">
                <template v-for="(item, key) in rebateTypeList">
                  <a-radio :key="key" :value="item.ItemValue">{{ item.ItemDescription }}</a-radio>
                </template>
              </a-radio-group>
            </a-form-model-item>
          </a-spin>
          <!-- 返货抵钱 -->
          <SpecialSubsScriptionOneModal v-if="model.RebateType == 1" :isUpdate="isUpdate" :record="record" :rebateType="parseInt(model.RebateType)" ref="SpecialSubsScriptionOneModal" @setData="setData"></SpecialSubsScriptionOneModal>
          <!-- 现金票折抵返货 -->
          <SpecialSubsScriptionTwoModal v-if="model.RebateType == 2 || model.RebateType == 3" :rebateType="parseInt(model.RebateType)" ref="SpecialSubsScriptionTwoModal"></SpecialSubsScriptionTwoModal>
        </a-col>
      </a-row>

    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button @click="handleOk" type="primary" :loading="saveLoading">提交审核</a-button>
    </a-row>

  </a-modal>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
import { number } from 'echarts';

export default {
  mixins: [EditMixin],
  name: "SpecialSubsScriptionNewModal",
  components: {
    JEllipsis
  },
  data() {
    return {
      title: '新增特殊认款',
      visible: false,
      confirmLoading: false,
      rebateLoading: false,
      saveLoading: false,
      purchaseAgreementType: '',//1年度协议 2短期销售协议 3短期采购协议
      model: {
        Id: '',
        RebateType: 1,//1返货抵现金 2现金抵返货
        RecognitionAmount: '',
        Note: '',
      },
      info: {},
      isUpdate: null,
      record: null,
      rebateTypeList: [],
      xyListHttpHead: 'P36007',
      url: {
        add: '/{v}/RecognitionSpecial/CreateSpecialAsync',//创建认款
        update: '/{v}/RecognitionSpecial/UpdateSpecialAsync',//编辑认款 仅限返货抵钱审核拒绝使用
        createAudit: '/{v}/ApprovalWorkFlowInstance/CreateApproval',
      }

    }
  },
  created() {

  },
  methods: {
    moment,
    show(record, isUpdate) {
      this.isUpdate = isUpdate
      this.record = record
      this.info = {}
      this.model.RebateType = 1;
      this.purchaseAgreementType = '';
      this.title = this.isUpdate ? '编辑特殊认款' : '新增特殊认款';
      this.visible = true;
      this.edit = false;
      this.getType();
    },
    setData(data) {
      this.info = data
    },
    getType() {
      this.rebateLoading = true
      getAction('/v1/SystemTool/GetEnumsByName', { enumName: 'EnumSpecialRecognitionRebateType' }, 'P36001').then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (!res.Data) return
        if (res.Data.length > 0) {
          res.Data.map((item) => {
            item.ItemValue = Number(item.ItemValue)
          })
        }
        this.rebateTypeList = res.Data || [];
      }).finally(() => {
        this.rebateLoading = false
      })
    },
    handleInput(e) {
      if (this.model.RebateType != e.target.value) {
        this.model.RebateType = e.target.value;
      }
      if (this.model.RebateType == 2 || this.model.RebateType == 3) {
        this.$nextTick(() => {
          this.$refs.SpecialSubsScriptionTwoModal.setSelectTableDataUrl()
        })
      }
      // 清空数据
      this.clearData()
    },
    clearData() {
      if (this.model.RebateType == 2 || this.model.RebateType == 3) {
        this.$nextTick(() => {
          this.$refs.SpecialSubsScriptionTwoModal.clearInfoData()
        })
      }
    },
    handleCancel() {
      this.visible = false
    },
    handleOk() {
      if (this.model.RebateType == 1) {
        this.$refs.SpecialSubsScriptionOneModal.checkData((data) => {
          console.log('验证成功', data)
          this.subHandleOk(data)
        })
      } else {
        this.$refs.SpecialSubsScriptionTwoModal.checkData((data) => {
          console.log('验证成功', data)
          this.subHandleOk(data)
        })
      }
    },
    subHandleOk(data) {
      if (!this.model.RebateType) {
        this.$message.warning('请选择抵扣类型')
        return
      }
      let AgreementAmountList = []
      let ShortPurchaseAgreementGoodsList = []
      let ReturnGoodsList = []
      let AgreementCountList = []

      // 抵钱
      if (this.model.RebateType == 1) {
        if (data.purchaseAgreementType == 1) {
          // 年度协议
          // console.log('年度协议 ' ,data.xyDataSource2)
          // console.log('年度协议xyDataSource3 ' ,data.xyDataSource3)
          if (data.xyDataSource2.length > 0) {
            data.xyDataSource2.map((item) => {
              // if (item.rowSpan > 0) {
                AgreementAmountList.push({
                  "PurchaseAgreementId": data.xyDataSource3[0].Id || data.xyDataSource3[0].AgreementId || data.xyDataSource2[0].AgreementId || data.xyDataSource2[0].PurchaseAgreementId,//协议ID
                  "PurchaseAgreementPolicyId": item.PurchaseAgreementPolicyId,
                  "RecognitionAmount": item.NumberNote,
                  "Note": item.Note,
                })
              // }
            })
          }
        } else if (data.purchaseAgreementType == 2) {
          // 短期销售协议
          if (data.selectedRowArray.length > 0) {
            data.selectedRowArray.map((item) => {
              AgreementAmountList.push({
                "PurchaseAgreementId": item.PurchaseAgreementId,//协议ID
                "PurchaseAgreementPolicyId": item.PurchaseAgreementPolicyId ? item.PurchaseAgreementPolicyId : null,
                "RecognitionAmount": item.NumberNote,
                "Note": item.Note,
              })
            })
          }
        } else if (data.purchaseAgreementType == 3) {
          // 短期采购协议
          if (data.selectedRowArray.length > 0) {
            data.selectedRowArray.map((item) => {
              ShortPurchaseAgreementGoodsList.push({
                "PurchaseAgreementId": item.Id,//协议ID
                "RecognitionAmount": item.NumberNote,
                "Note": item.Note,
              })
            })
          }
        }
        // 返货采购订单数组重组和过滤没认款的
        if (data.dataSource2.length > 0) {
          data.dataSource2.map((item) => {
            if (item.Count && item.Count != 0) {
              ReturnGoodsList.push({
                GoodsId: item.GoodsSpuId || item.GoodsId,
                RecognitionCount: item.Count,
                RowIndex: item.RowIndex,
              })
            }
          })
        }
      } else if (this.model.RebateType == 2 || this.model.RebateType == 3) {
        // 抵货
        if (data.purchaseAgreementType == 1) {
          // 年度协议
          if (data.xyDataSource2.length > 0) {
            data.xyDataSource2.map((item) => {
              AgreementCountList.push({
                "PurchaseAgreementId": data.xyDataSource3[0].PurchaseAgreementId,//协议ID
                "PurchaseAgreementPolicyId": item.PurchaseAgreementPolicyId,
                "GoodsId": item.GoodsId,
                "RecognitionCount": item.CountNote,
                "Note": item.Note,
              })
            })
          }
        } else if (data.purchaseAgreementType == 2) {
          // 短期销售协议
          if (data.selectedRowArray.length > 0) {
            data.selectedRowArray.map((item) => {
              AgreementCountList.push({
                "PurchaseAgreementId": item.PurchaseAgreementId,//协议ID
                "PurchaseAgreementPolicyId": item.PurchaseAgreementPolicyId,
                "GoodsId": item.GoodsId,
                "RecognitionCount": item.CountNote,
                "Note": item.Note,
              })
            })
          }
        }
      }
      // 分割线
      let params = {
        "RebateType": parseInt(this.model.RebateType),
        "PurchaseAgreementType": data.purchaseAgreementType,
        "ReceiptId": this.model.RebateType == 1 ? null : data.dataSource1[0].Id, //收款单Id
        "PurchaseOrderId": this.model.RebateType == 1 ? data.dataSource1[0].Id : null, //采购单Id
        "RecognitionAmount": this.model.RebateType == 1 ? data.AmountAllNumVal : data.thisRecognitionAmount,//认款金额
        "PayerId": data.PayerId || data.dataSource1[0].SupplierId,
        "Payer": data.Payer || data.dataSource1[0].SupplierName,
        "Note": data.Note,
        "AgreementAmountList": AgreementAmountList || [],
        "ShortPurchaseAgreementGoodsList": ShortPurchaseAgreementGoodsList || [],
        "AgreementCountList": AgreementCountList || [],
        "ReturnGoodsList": ReturnGoodsList || []
      }
      // console.log('分割线  ',params)
      // return;
      if (this.isUpdate) {
        params.AuditId = this.record.AuditId
      }
      this.saveLoading = true
      let url = this.isUpdate ? this.url.update : this.url.add
      postAction(url, params, 'P36012').then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('创建成功')
        this.$emit('ok')
        if (this.model.RebateType == 1) {
          this.createApproval(res.Data)
        }
        this.handleCancel()
      }).finally(() => {
        this.saveLoading = false
      })
    },
    // 创建审核实例
    createApproval(BussinessNo, callback) {
      this.loading = true
      let params = {
        BussinessNo: BussinessNo,
        Scenes: 23,
        OpratorId: Vue.ls.get(USER_ID),
        Remark: "特殊认款"
      }
      postAction(this.url.createAudit, params, 'P36005')
        .then(res => {
          if (res.IsSuccess) {
            this.$message.success('提交审核成功')
            callback && callback(true)
          } else {
            this.$message.error(res.Msg)
            callback && callback(false)
          }
        })
        .catch(e => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },
  }

}
</script>
