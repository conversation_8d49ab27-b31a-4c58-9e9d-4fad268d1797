<!-- 返货批量认款 -->
<template>
  <a-modal :title="title" :width="1000" :visible="visible" :saveLoading="saveLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <div class="skd">返货采购订单：</div>
      <a-descriptions :column="4">
        <a-descriptions-item label="采购订单号">{{ model.PurchaseOrderNo || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="供应商">{{ model.SupplierName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="返货数量">{{ model.TotalReturnGoodsCount || '--' }}</a-descriptions-item>
        <a-descriptions-item label="未认款数量">{{ model.RemainingCount||'--' }}</a-descriptions-item>
      </a-descriptions>
      <div class="xieyi" style="padding-top: 20px;">认款协议：（本次合计认款金额：{{ Total }}）</div>
      <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="middle" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="confirmLoading" :scroll="{ y: dataSource.length === 0 ? false:'350px',x:'1000px' }">
        <!-- 字符串超长截取省略号显示-->
        <template slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </template>
        <!-- 操作 -->
        <template slot="Count" slot-scope="text, record,index">
          {{ text?text:0 }} <a-icon type="edit" style="" class="edit-btn" @click="editXYReturnGoods(record)" />
        </template>
        <template slot="Note" slot-scope="text, record,index">
          <a-input v-model="record.Note" style="width:100%" placeholder="请输入" :maxLength="40" />
        </template>
        <!-- 促销时间 -->
        <span slot="AgreementStartTimeVal" slot-scope="text, record,index">
          <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
        </span>
        <!-- 价格显示-->
        <span slot="price" slot-scope="text">
          <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
        </span>
        <!-- 认款范围 -->
        <template slot="IsAll" slot-scope="text, record,index">
          <a-radio-group name="radioGroup" :value="record.IsAll" @change="(e)=>(handleCheck(e,record))">
            <a-radio :value="true" v-if="model.RemainingCount>=record.RecognitionAmount">全部</a-radio>
            <a-radio :value="false" :disabled="!!model.RemainingCount<record.RecognitionAmount">部分</a-radio>
          </a-radio-group>
        </template>
      </a-table>
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button @click="save" type="primary" :loading="saveLoading">保存</a-button>
    </a-row>
    <ReturnGoodsPoolScriptionEditModal ref="ReturnGoodsPoolScriptionEditModal" @ok="modelOk" />
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  mixins: [EditMixin],
  name: "ReturnGoodsPoolScriptionModal",
  components: {
    JEllipsis
  },
  data() {
    return {
      title: "返货批量认款",
      visible: false,
      model: {
        ReturnGoodsOrderId: '',
        AgreementList: []
      },
      confirmLoading: false,
      saveLoading: false,
      httpHead: 'P36012',
      url: {
        add: '/{v}/ReturnGoods/Recognition/RecognitionAsync',
        xy: '/{v}/PurchaseAgreement/GetUnsubscribedRecognitionListAsync'
      },
      dataSource: [],
      columns: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '核算方式',
          dataIndex: 'AccountingMethodStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货应收数量',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次认款范围',
          dataIndex: 'IsAll',
          width: 180,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'IsAll' },
        },
        {
          title: '本次认款数量',
          dataIndex: 'Count',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Count' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'Note' },
        },
      ],
      curCheckXy: undefined,
    };
  },
  mounted() {

  },
  created() { },
  computed: {
    Total() {
      if (this.dataSource && this.dataSource.length) {
        let total = 0
        this.dataSource.forEach(e => {
          total += e.Count;
        });
        return total
      } else {
        return 0
      }
    }
  },
  methods: {
    moment,
    show(record) {
      this.model = record;
      this.model.ReturnGoodsOrderId = record.Id;
      this.title = '返货批量认款';
      this.visible = true;
      this.getXyDetail();
    },
    getXyDetail() {
      getAction(this.url.xy, {
        SupplierId: this.model.SupplierId,
        AgreementRebateType: 3
      }, 'P36007').then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (!res.Data) return
        this.dataSource = res.Data || [];
        if (this.dataSource.length) {
          this.dataSource.forEach(e => {
            if (this.model.RemainingCount >= e.RecognitionAmount) {
              e.IsAll = true;
            } else {
              e.IsAll = false;
            }
            e.Count = 0;
            e.goodsList = [];
          })
        }

        this.dataSource = JSON.parse(JSON.stringify(this.dataSource))
      })
    },
    handleCheck(e, record) {
      record.IsAll = e.target.value;
      if (record.IsAll) {
        record.Count = record.RecognitionAmount
      }
    },
    //本次认款数量
    editXYReturnGoods(record) {
      record.purchaseOrderId = this.model.Id;

      this.curCheckXy = record;
      this.$refs.ReturnGoodsPoolScriptionEditModal.show(record, record.goodsList);
    },
    modelOk(list) {
      let total = 0;
      list.forEach(e => {
        total += e.Count;
      });
      this.dataSource.forEach(e => {
        if (e.PurchaseAgreementId == this.curCheckXy.PurchaseAgreementId) {
          e.Count = total;
          e.goodsList = list;
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.model = {};
      this.visible = false;
    },
    save() {
      if (this.dataSource && this.dataSource.length == 0) {
        this.$message.warning('没有协议不能保存');
        return false
      }

      let xy1 = this.dataSource.filter(e => e.Count > 0 && !e.IsAll);//部分认款
      if (xy1.length) {
        let total = 0
        xy1.forEach(e => {
          total += e.Count;
        });

        if (total > this.model.RemainingCount) {
          this.$message.warning('认款协议-本次认款数量超过未认款数量');
          return false
        }
      }

      this.model.AgreementList = this.dataSource.map(e => {
        return {
          AgreementId: e.PurchaseAgreementId,
          IsAll: e.IsAll,
          Note: e.Note,
          RecognitionCount: e.Count,
          GoodsList: (e.goodsList || []).map(e1 => {
            return {
              GoodsSpuId: e1.GoodsSpuId,
              RecognitionCount: e1.Count,
              AgreementRemainingCount: e1.AgreementRemainingCount,
              OrderRemainingCount: e1.OrderRemainingCount,
            }
          })
        }
      })
      this.saveLoading = true;
      postAction(this.url.add, {
        ReturnGoodsOrderId: this.model.ReturnGoodsOrderId,
        AgreementList: this.model.AgreementList
      }, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          this.$message.success("操作成功");
          this.$emit("ok");
          this.close();
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {
        this.saveLoading = false;
      });
    }
  },
};
</script>
<style lang="less" scoped>
.xieyi,
.skd {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}
.xieyi::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}

.title-btn {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  justify-items: center;
}

.title-btn .xieyi {
  padding-bottom: 0;
}

.edit-btn {
  color: #1890ff;
  margin-left: 10px;
  cursor: pointer;
}
</style>
