<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab"
      @simpleTableLoadAfter="simpleTableLoadAfter" >
      <div style="margin-top:5px;" slot="bottomBtn">
        <span style="margin-right: 15px;">合计收款金额：¥{{countObj.TotalReceiptAmount || 0}}</span>
        <span style="margin-right: 15px;">已认款金额：¥{{countObj.TotalRecognizedAmount || 0}}</span>
        <span style="margin-right: 15px;">待认款金额：¥{{countObj.TotalUnRecognizedAmount || 0}}</span>
      </div>
    </SimpleTable>
    <!-- 认款 -->
    <AcceptanceFunModal ref="AcceptanceFunModal" @ok="modelOk"></AcceptanceFunModal>
    <!-- 认款记录 -->
    <AcceptanceFunListModal ref="AcceptanceFunListModal" @ok="modelOk"></AcceptanceFunListModal>

  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'PurchaAdvancePriceAdjustmentList',
  title: '采购预付款调价认款',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '收款单号', type: 'input', value: '', key: 'ReceiptNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '收款方式',
          type: 'select',
          value: '',
          key: 'ReceiptType',
          defaultVal: [
            { title: '现金', value: 1 },
            { title: '微信', value: 2 },
            { title: '支付宝', value: 3 },
            { title: '对公转账', value: 4 },
          ],
          placeholder: '请选择',
        },
        {
          name: '收款日期',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['ReceiptTimeBegin', 'ReceiptTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        { name: '打款方', type: 'input', value: '', key: 'Payer', defaultVal: '', placeholder: '请输入' },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [

        ],
        hintArray: [],
        tableTile: '',
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款日期',
          dataIndex: 'ReceiptTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '收款方式',
          dataIndex: 'ReceiptTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款账户',
          dataIndex: 'AccountName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognizedAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '待认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          actionBtn: [
            {
              name: '认款', icon: '', id: '8abd8299-8b48-4c60-86b0-436a10ab6bf0',
              specialShowFuc: (record) => {
                return record.RecognitionStatus != 3 && record.RemainingAmount > 0
              },
            },
            { name: '认款记录', icon: '', id: '0c0c752b-563d-43f7-ba6e-2aa5ce6c4f70' },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      countObj: {},//统计数据
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '/{v}/CashReceiptNew/GetCashList',
        getCashReceipStatistics: '/{v}/CashReceiptNew/GetCashReceipStatistics', //统计
      },
    }
  },
  created() { },
  activated() {
    this.queryParam.NeedToRecognize = true
    this.$refs.SimpleSearchArea.queryParam.NeedToRecognize = true
    let queryParam = {
      ...this.queryParam,
      ...this.$refs.SimpleSearchArea.queryParam
    }
    this.$refs.table.loadDatas(null, queryParam)
  },
  methods: {
    // 列表加载完成后
    simpleTableLoadAfter() {
      // console.log('simpleTableLoadAfter')
      this.getCashReceipStatistics()//获取统计数据
    },
    // 获取统计数据
    getCashReceipStatistics() {
      let params = this.$refs.table.getQueryParams()
      postAction(this.linkUrl.getCashReceipStatistics, params, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.countObj = res.Data || {}
      })
    },
    // 列表操作
    operate(record, type) {
      if (type == '认款') {
        this.$refs.AcceptanceFunModal.show(record, this.columns, 7)
      } else if (type == '认款记录') {
        this.$refs.AcceptanceFunListModal.show(record, this.columns, 11)
      }
    },
    modelOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      this.queryParam.NeedToRecognize = true
      this.$refs.SimpleSearchArea.queryParam.NeedToRecognize = true
      if (type == 'searchReset') {
        this.tab.status = ''
      }
      this.$refs.table.loadDatas(1, queryParam)
    },
  },
}
</script>

<style>
</style>
