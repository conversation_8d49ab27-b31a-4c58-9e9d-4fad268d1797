<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @operate="operate"
      @simpleTableLoadAfter="simpleTableLoadAfter">
      <div style="margin-top:5px;" slot="bottomBtn">
        <span style="margin-right: 15px;">合计收款金额：¥{{ countObj.TotalReceiptAmount || 0 }}</span>
        <span style="margin-right: 15px;">已认款金额：¥{{ countObj.TotalRecognizedAmount || 0 }}</span>
        <span style="margin-right: 15px;">待认款金额：¥{{ countObj.TotalUnRecognizedAmount || 0 }}</span>
      </div>
    </SimpleTable>
    <!-- 新增收款 -->
    <CollectMoneyEditModal ref="CollectMoneyEditModal" @ok="CollectMoneyModalOk"></CollectMoneyEditModal>
    <!-- 认款记录 -->
    <AcceptanceFunListModal ref="AcceptanceFunListModal"></AcceptanceFunListModal>
    <!-- 批量导入 -->
    <BatchImportModal
      ref="iBatchImportAreaModal"
      @ok="handleBatchImportModalOk('table')"
      :searchInput="searchInputCash"
      :columnsD="columnsD"
      ErrorShowKey="ErrMessage"
      HttpHead="P36012"
      linkUrlType="POST"
      linkUrlDelType="POST"
      TempDataRemoveKey="IdList"
      linkUrlAddType="POST"
      importUrl="/v1/CashReceiptNew/Import"
      TempDataType="POST"
      queryParamStatusKey="HasError"
      TempDataUrl="/{v}/CashReceiptNew/GetImportTmpList"
TempDataRemoveUrl="/{v}/CashReceiptNew/RemoveReceiptOrderCashImportTmp" AddUrl="/{v}/CashReceiptNew/CommitReceiptOrderCashImportTmp" batchDelId="f38315e9-33e1-4a1b-87b1-7d8d23019ad2" defaultUrl="导入收款信息模板" downloadFileName="导入收款信息模板" errorMessage="您导入的信息有异常，请移除后再提交" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
import moment from 'moment'
export default {
  name: 'CashPoolList',
  mixins: [SimpleMixin, ListMixin],
  data() {
    return {
      id: '',
      searchItems: [
        {
          name: '收款单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'ReceiptNo',
        },
        {
          name: '收款方式',
          type: SEnum.ENUM,
          key: 'ReceiptType',
          dictCode: 'EnumReceiptType',
          notOption: [5],
        },
        {
          name: '收款日期',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'ReceiptTime',
        },
        {
          name: '打款方',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'Payer',
        },
        {
          name: '状态',
          type: SEnum.ENUM,
          key: 'RecognitionStatus',
          dictCode: 'EnumRecognitionStatus',
        },
        {
          name: '创建人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate1,
          key: 'CreateTime',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        operateBtn: [
          { name: '导出', type: 'primary', icon: '', key: 'export', id: '02624e15-d84f-4802-858c-9a975d62aebd' },
          { name: '导入收款', type: 'primary', icon: '', key: 'upload', id: 'e89baf6e-41e7-4f67-a6c0-758216ee352b' },
          { name: '新增收款', type: 'primary', icon: '', key: 'add', id: '1af4166f-4f18-4f68-89ba-2468acc38a85' },
        ],
      },
      columns: [
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款日期',
          dataIndex: 'ReceiptTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '收款方式',
          dataIndex: 'ReceiptTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款账户',
          dataIndex: 'AccountName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '备注',
          dataIndex: 'Note',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognizedAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '待认款金额',
          dataIndex: 'UnRecognizedAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '状态',
          dataIndex: 'RecognitionStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          actionBtn: [
            {
              name: '编辑',
              icon: '',
              id: 'aec13d16-a61a-4cdd-85ad-49be35f6eba4',
              specialShowFuc: (e) => {
                if (e.RecognitionStatus == 1) {
                  return true
                }
              },
            },
            {
              name: '删除',
              icon: '',
              id: 'df5bfbfe-951e-41bc-83ce-c109a94e7f0b',
              specialShowFuc: (e) => {
                if (e.RecognitionStatus == 1) {
                  return true
                }
              },
            },
            // {
            //   name: '认款',
            //   icon: '',
            //   id: '7d9cb3ab-0660-499d-b167-e15e9814b3fb',
            //   specialShowFuc: e => {
            //     if (e.RecognitionStatus == 1 || e.RecognitionStatus == 2) {
            //       return true
            //     }
            //   }
            // },
            {
              name: '认款记录',
              icon: '',
              id: 'cc2e5890-61c3-4f74-9438-d59b992fe85f',
              specialShowFuc: (e) => {
                if (e.RecognitionStatus == 2 || e.RecognitionStatus == 3) {
                  return true
                }
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      countObj: {},//统计数据
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '/{v}/CashReceiptNew/GetCashList',
        getCashReceipStatistics: '/{v}/CashReceiptNew/GetCashReceipStatistics', //统计
        del: '/{v}/CashReceiptNew/RemoveReceiptOrder',
        exportUrl: '/{v}/CashReceiptNew/ExportGetCashList'
      },
      searchInputCash: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '收款日期',
          type: 'timeInput',
          value: '',
          key: ['ReceiptTimeBegin', 'ReceiptTimeEnd'],
          rangeDate: [],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '收款方式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'ReceiptType',
          dictCode: 'EnumReceiptType',
          defaultVal: '',
          placeholder: '请选择',
        },
        { name: '打款方', type: 'input', value: '', key: 'Payer', defaultVal: '', placeholder: '请输入' },
      ],
      columnsD: [
        {
          title: '收款日期',
          dataIndex: 'ReceiptTime',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '收款方式',
          dataIndex: 'ReceiptType',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方',
          dataIndex: 'Payer',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款账户',
          dataIndex: 'AccountName',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtn: [{ name: '移除', icon: '' }],
          scopedSlots: { customRender: 'action' },
        },
      ],
    }
  },
  created() { },
  mounted() {
  },
  activated(){
    let queryParam = Object.assign({}, this.$refs.SimpleSearchArea.queryParam)
    queryParam.ReceiptType = queryParam.ReceiptType?Number(queryParam.ReceiptType):undefined
    queryParam.RecognitionStatus = queryParam.RecognitionStatus?Number(queryParam.RecognitionStatus):undefined
    this.$refs.table.loadDatas(null, queryParam)
  },
  methods: {
    modalOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    CollectMoneyModalOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 列表加载完成后
    simpleTableLoadAfter() {
      // console.log('simpleTableLoadAfter')
      this.getCashReceipStatistics()//获取统计数据
    },
    // 获取统计数据
    getCashReceipStatistics() {
      let params = this.$refs.table.getQueryParams()
      postAction(this.linkUrl.getCashReceipStatistics, params, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.countObj = res.Data || {}
      })
    },
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        this.$refs.CollectMoneyEditModal.add()
      } else if (type == '编辑') {
        this.$refs.CollectMoneyEditModal.edit(record)
      } else if (type == 'upload') {
        this.$refs.iBatchImportAreaModal.show(null)
      } else if (type == '认款') {
        // this.$refs.CashPoolSubscriptionModal.show(record)
      } else if (type == '认款记录') {
        this.$refs.AcceptanceFunListModal.show(record, this.columns, 7)
      } else if (type == '删除') {
        let that = this
        this.$confirm({
          title: '确认删除',
          content: '是否删除这条数据?',
          onOk: function () {
            let url = that.linkUrl.del + '?id=' + record.Id
            putAction(url, { id: record.Id }, that.linkHttpHead).then((res) => {
              if (res.IsSuccess) {
                that.$message.success('操作成功')
                that.$refs.table.loadDatas(1, that.queryParam)
              } else {
                that.$message.warning(res.Msg)
              }
            })
          },
        })
      } else if (type == 'export') {
        console.log('export', this.$refs.SimpleSearchArea.queryParam)
        const queryParam = this.$refs.SimpleSearchArea.queryParam
        const queryKeys = Object.keys(queryParam)
        const hasParam = queryKeys.some((key) => {
          if(['PageIndex', 'PageSize'].includes(key)) {
            return false
          }
          const val = queryParam[key]
          const isNullAndUndefined = val == null || val == undefined
          const isEmpty = typeof val === 'string' && !val
          if (!isNullAndUndefined && !isEmpty) return true
        })
        if (!hasParam) {
          this.$message.warning('请您先选择创建时间，再导出(导出时间最大跨度为一年)!')
          return
        }
        const {CreateTimeBegin, CreateTimeEnd, RecognitionStatus, ReceiptType} = queryParam
        if (Object.keys(queryParam).length === 2 && CreateTimeBegin) {
          const years = moment(CreateTimeEnd).diff(moment(CreateTimeBegin),'year',true)
          if(years > 1) {
            this.$message.warning('导出时间最大跨度为一年')
            return
          }
        }
        this.handleExportXls(
          '现金池明细',
          'Post',
          this.linkUrl.exportUrl,
          null,
          this.linkHttpHead,
          { ...queryParam,
            RecognitionStatus: RecognitionStatus ? Number(RecognitionStatus): null,
            ReceiptType: ReceiptType ? Number(ReceiptType): null,
          }
        )
      }
    },
    // 导入保存
    handleBatchImportModalOk(key) {
      this.$refs[key].loadDatas(null, this.queryParam)
    },
    searchQuery(queryParam, type) {
      let params = JSON.parse(JSON.stringify(queryParam))
      if (params['RecognitionStatus']) params['RecognitionStatus'] = Number(params['RecognitionStatus'])
      if (params['ReceiptType']) params['ReceiptType'] = Number(params['ReceiptType'])
      if (this.$refs.table) this.$refs.table.loadDatas(1, params)
    },
  },
}
</script>
<style lang="scss" scoped>
/* 设置table行的高度防止错位 */
::v-deep .ant-table-tbody tr {
  height: 48px !important;
}
</style>
