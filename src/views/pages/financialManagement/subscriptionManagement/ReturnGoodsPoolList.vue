<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate">
      <!-- 关联订单数量 -->
      <span slot="order" slot-scope="{text,record}">
        <a @click="showOrder(text,record)">{{text}}</a>
      </span>
    </SimpleTable>
    <!-- 认款 -->
    <ReturnGoodsFunModal ref="ReturnGoodsFunModal" @ok="modalOk"></ReturnGoodsFunModal>
    <!-- 认款记录 -->
    <AcceptanceFunListModal ref="AcceptanceFunListModal" @ok="modalOk"></AcceptanceFunListModal>
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "ReturnGoodsPoolList",
  mixins: [SimpleMixin],
  data() {
    return {
      searchItems: [
        {
          name: '采购订单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'PurchaseOrderNo'
        },
        {
          name: '供应商',
          placeholder: '名称/编码/拼音码',
          type: SEnum.INPUT,
          key: 'SupplierKey'
        },
        {
          name: '商品',
          placeholder: '名称/编码/拼音码/厂商',
          type: SEnum.INPUT,
          key: 'GoodsName'
        },
        {
          name: '订单责任人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'OwnerByName'
        },
        {
          name: '入库时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'InventoryTime'
        }
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        sortArray: ['order'],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
      columns: [
        {
          title: '采购订单号',
          dataIndex: 'PurchaseOrderNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'order' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货数量',
          dataIndex: 'TotalReturnGoodsCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款数量',
          dataIndex: 'RecognizedCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'RemainingCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单责任人',
          dataIndex: 'OwnerByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '入库时间',
          dataIndex: 'WarehouseInTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: "right",
          actionBtn: [
            {
              name: '认款', icon: '', id: '304155d7-b385-47e7-acc2-e991ba65a0c5', specialShowFuc: record => {
                return record.RemainingCount > 0
              }
            },
            { name: '认款记录', icon: '', id: '7d5a20a7-6845-4f40-a7e9-5ea5f7023060' }
          ],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {

      },
      isTableInitData: true,//是否自动加载
      linkHttpHead: 'P36009',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/PurchaseOrder/GetRecognizedListV2Async',
      },
    }
  },
  created() { },
  mounted() { },
  methods: {
    showOrder(text, record) {
      this.$router.push({
        path: '/pages/purchasingManagement/PurchaseOrderDetail',
        query: {
          id: record.Id,
        }
      })
    },
    // 列表操作
    operate(record, type) {
      if (type == '认款') {
        this.$refs.ReturnGoodsFunModal.show(record, this.columns, 9);
      } else if (type == '认款记录') {
        this.$refs.AcceptanceFunListModal.show(record, this.columns, 9)
      }
    },
    modalOk() {
      this.$refs.table.loadData(1)
    }
  }

}
</script>

<style>
</style>