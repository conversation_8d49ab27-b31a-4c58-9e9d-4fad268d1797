<template>
  <LetterListBase
    ref="LetterListBase"
    :url="url"
    :source="23"
    :httpHead="httpHead"
    :searchInputArray="searchInput"
    :columnsArray="columns"
  ></LetterListBase>
</template>

<script>
export default {
  name: 'PrepaymentRefundAcknowledgementList',
  data() {
    return {
      url: '/{v}/CashReceiptNew/GetCashList',
      httpHead: 'P36012',
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '收款单号', type: 'input', value: '', key: 'ReceiptNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '收款方式',
          type: 'select',
          value: '',
          key: 'ReceiptType',
          defaultVal: [
            { title: '现金', value: '1' },
            { title: '微信', value: '2' },
            { title: '支付宝', value: '3' },
            { title: '对公转账', value: '4' },
          ],
          placeholder: '请选择',
        },
        {
          name: '收款日期',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['ReceiptTimeBegin', 'ReceiptTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        { name: '打款方', type: 'input', value: '', key: 'Payer', defaultVal: '', placeholder: '请输入' },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],
      columns: [
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款日期',
          dataIndex: 'ReceiptTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '收款方式',
          dataIndex: 'ReceiptTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款方',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款账户',
          dataIndex: 'AccountName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Note',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognizedAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '待认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          actionBtn: [
            {
              name: '认款',
              icon: '',
              id: 'b2ef4402-adb4-4676-bdbd-544ee6eb8543',
              specialShowFuc: (record) => {
                return record.RecognitionStatus != 3 && record.RemainingAmount > 0
              },
            },
            {
              name: '认款记录',
              icon: '',
              id: 'e7d1d72d-9507-4118-96f9-856c79745ea0',
              specialShowFuc: (record) => {
                return true
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
    }
  },
}
</script>

<style></style>
