<template>
    <LetterListBase ref="LetterListBase" :source="22" :searchInputArray="searchInput" :columnsArray="columns" :url="url" :exportParams="exportParams">
    </LetterListBase>
</template>

<script>
export default {
    name: 'RecordList',
    data() {
        return {
            searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
                { name: '认款编号', type: 'input', value: '', key: 'RecognitionOrderNo', defaultVal: '', placeholder: '请输入' },
                {
                    name: '认款类型',
                    httpType: 'GET',
                    httpParams: { enumName: 'EnumRecognitionOrderMode' },
                    type: 'selectLink',
                    vModel: 'RecognitionOrderMode',
                    defaultVal: '',
                    dataKey: { name: 'ItemDescription', value: 'ItemValue' },
                    url: '/{v}/SystemTool/GetEnumsByName',
                    httpHead: 'P36001'
                },
                { name: '收款单号', type: 'input', value: '', key: 'ReceiptOrderNo', defaultVal: '', placeholder: '请输入' },
                {
                    name: '认款状态',
                    httpType: 'GET',
                    httpParams: { enumName: 'EnumRecognitionCancelStatus' },
                    type: 'selectLink',
                    vModel: 'RecognitionStatus',
                    defaultVal: '',
                    dataKey: { name: 'ItemDescription', value: 'ItemValue' },
                    url: '/{v}/SystemTool/GetEnumsByName',
                    httpHead: 'P36001'
                },
                {
                    name: '认款时间',
                    type: 'timeInput',
                    value: '',
                    rangeDate: [],
                    key: ['CreateTimeBegin', 'CreateTimeEnd'],
                    defaultVal: ['', ''],
                    placeholder: ['开始日期', '结束日期']
                },

                { name: '认款人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
                {
                    name: '同步状态',
                    httpType: 'GET',
                    httpParams: { enumName: 'EnumPushERPStatus' },
                    type: 'selectLink',
                    vModel: 'PushERPStatus',
                    defaultVal: '',
                    dataKey: { name: 'ItemDescription', value: 'ItemValue' },
                    url: '/{v}/SystemTool/GetEnumsByName',
                    httpHead: 'P36001'
                }, 
                {
                    name: '执行状态',
                    httpType: 'GET',
                    httpParams: { enumName: 'EnumExecuteStatus' },
                    type: 'selectLink',
                    vModel: 'ExecuteStatus',
                    defaultVal: '',
                    dataKey: { name: 'ItemDescription', value: 'ItemValue' },
                    url: '/{v}/SystemTool/GetEnumsByName',
                    httpHead: 'P36001'
                },
                { name: '单据编号', type: 'input', value: '', key: 'businessNo', defaultVal: '', placeholder: '请输入' },
            ],
            columns: [
                {
                    title: '认款编号',
                    dataIndex: 'RecognitionOrderNo',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'component' },
                },
                {
                    title: '认款类型',
                    dataIndex: 'RecognitionOrderModeStr',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'dayTime' },
                },
                {
                    title: '单据编号',
                    dataIndex: 'BusinessNo',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'component' },
                },
                {
                    title: '应收金额',
                    dataIndex: 'ReceivableAmount',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'rmbMoney' },
                },
                {
                    title: '本次认款金额',
                    dataIndex: 'RecognitionAmount',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'rmbMoney' },
                },
                {
                    title: '收款单号',
                    dataIndex: 'ReceiptNo',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'component' },
                },
                {
                    title: '收款方式',
                    dataIndex: 'ReceiptTypeStr',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'component' },
                },
                {
                    title: '打款方',
                    dataIndex: 'Payer',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'component' },
                },
                {
                    title: '收款金额',
                    dataIndex: 'ReceiptAmount',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'rmbMoney' },
                },
                {
                    title: '认款人',
                    dataIndex: 'CreateByName',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'component' },
                },
                {
                    title: '认款时间',
                    dataIndex: 'CreateTime',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'component' },
                },
                {
                    title: '认款状态',
                    dataIndex: 'RecognitionStatusStr',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'component' },
                },
                {
                    title: '同步状态',
                    dataIndex: 'PushERPStatusStr',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'component' },
                },
                {
                    title: '执行状态',
                    dataIndex: 'ExecuteStatusStr',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'component' },
                },
                {
                    title: '操作',
                    dataIndex: 'action',
                    align: 'center',
                    width: 180,
                    fixed: "right",
                    actionBtn: [{
                        name: '详情', icon: '', id: 'a5a6aa4f-de1d-48bc-9cbd-6cb821be9f80'
                    },
                    ],
                    scopedSlots: { customRender: 'action' }
                }
            ],
            url:'/{v}/RecognitionGeneralAgreement/QueryRecognitionOrderList',
            exportParams:{
                url:'/{v}/RecognitionGeneralAgreement/ExportRecognitionDetailList',
                methods:'POST',
                linkHttpHead:'P36012'
            }
        }
    }
}
</script>