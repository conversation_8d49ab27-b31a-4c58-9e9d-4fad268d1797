<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate" @simpleTableLoadAfter="simpleTableLoadAfter" >
      <div style="margin-top:5px;" slot="bottomBtn">
        <span style="margin-right: 15px;">合计收款金额：¥{{countObj.TotalReceiptAmount || 0}}</span>
        <span style="margin-right: 15px;">已认款金额：¥{{countObj.TotalRecognizedAmount || 0}}</span>
        <span style="margin-right: 15px;">待认款金额：¥{{countObj.TotalUnRecognizedAmount || 0}}</span>
      </div>
    </SimpleTable>
    <!-- 认款记录 -->
    <AcceptanceFunListModal ref="AcceptanceFunListModal" @ok="modalOk"></AcceptanceFunListModal>
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "TicketPoolList",
  mixins: [SimpleMixin],
  data() {
    return {
      searchItems: [
        {
          name: '收款单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'ReceiptNo'
        },
        {
          name: '供应商',
          placeholder: '名称/编号/拼音码',
          type: SEnum.INPUT,
          key: 'SupplierKeyWords'
        },
        {
          name: '创建人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'CreateByName'
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime'
        }
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
      columns: [
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '票折余额',
          dataIndex: 'InvoiceDiscountAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognizedAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '待认款金额',
          dataIndex: 'UnRecognizedAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: "right",
          actionBtn: [{ name: '认款记录', icon: '', id: 'fac6d3d2-b3bc-428d-a773-74ed4fc626cf' },],
          scopedSlots: { customRender: 'action' }
        }
      ],
      countObj: {},//统计数据
      queryParam: {

      },
      isTableInitData: false,//是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: 'POST',//请求方式
      linkUrl: {
        list: '/{v}/CashReceiptNew/GetInvoiceDiscountReceipList',
        getDiscountReceipStatistics: '/{v}/CashReceiptNew/GetDiscountReceipStatistics', //统计
      },
    }
  },
  created() { },
  mounted() { },
  activated() {
    this.queryParam = this.$refs.SimpleSearchArea.queryParam
    this.queryParam.ExecuteStatus = 3
    this.$refs.table.loadDatas(1, this.queryParam)
  },
  methods: {
    // 列表加载完成后
    simpleTableLoadAfter() {
      // console.log('simpleTableLoadAfter')
      this.getDiscountReceipStatistics()//获取统计数据
    },
    // 获取统计数据
    getDiscountReceipStatistics() {
      let params = this.$refs.table.getQueryParams()
      postAction(this.linkUrl.getDiscountReceipStatistics, params, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.countObj = res.Data || {}
      })
    },
    // 列表操作
    operate(record, type) {
      if (type == '认款记录') {
        this.$refs.AcceptanceFunListModal.show(record, this.columns, 8);
      }
    },
    modalOk() {
      this.$refs.table.loadData(1)
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type, extendParams, searchType) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
      }
      queryParam.ExecuteStatus = 3
      this.$refs.table.loadDatas(1, queryParam, null, type, extendParams, searchType)
    },
  }

}
</script>

<style>
</style>