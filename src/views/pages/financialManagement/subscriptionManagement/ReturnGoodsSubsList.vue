<!-- 返货认款列表 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate">
      <!-- 促销时间 -->
      <span slot="AgreementStartTimeVal" slot-scope="{text,record}">
        <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
      </span>
    </SimpleTable>
    <ReturnGoodsSubsInfoModal ref="ReturnGoodsSubsInfoModal"></ReturnGoodsSubsInfoModal>
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "ReturnGoodsSubsList",
  mixins: [SimpleMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      searchItems: [
        {
          name: '认款单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'RecognitionNo'
        },
        {
          name: '采购订单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'PurchaseOrderNo'
        },
        {
          name: '供应商',
          placeholder: '名称/编号/拼音码',
          type: SEnum.INPUT,
          key: 'SupplierKey'
        },
        {
          name: '商品',
          placeholder: '名称/编号/拼音码',
          type: SEnum.INPUT,
          key: 'GoodsKey'
        },
        {
          name: '甲方',
          placeholder: '名称/编号/拼音码',
          type: SEnum.INPUT,
          key: 'PartyFirstKey'
        },
        {
          name: '归属人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'BusinessCreator'
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime'
        }
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
        sortArray: ['AgreementStartTimeVal'],
      },
      columns: [
        {
          title: '认款单号',
          dataIndex: 'RecognitionNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购订单号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '核算方式',
          dataIndex: 'AccountingMethodStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款数量',
          dataIndex: 'RecognitionCount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: "right",
          actionBtn: [
            { name: '详情', icon: '', id: '9be10e92-504f-488d-ac01-ddd96a6947a0' },
            { name: '撤销', icon: '', id: 'd1d2e7c1-a542-44fe-8281-bc198787ab65',
              specialShowFuc: e => {
                return !e.IsCancel
              }
            }
          ],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {
        PurchaseOrderNo:''
      },
      isTableInitData: false,//是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/ReturnGoods/GetList/GetListAsync',
      },
    }
  },
  created() {},
  mounted() {
    if(this.$route.query){
      this.queryParam.PurchaseOrderNo = this.$route.query.purchaseOrderNo;
      this.$refs.SimpleSearchArea.queryParam={
        PurchaseOrderNo:this.$route.query.purchaseOrderNo
      }
    }
    this.$refs.table.loadDatas(1, this.queryParam);
  },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '详情') {
        this.$refs.ReturnGoodsSubsInfoModal.show(record)
      }else if(type == '撤销'){
        let that = this
        this.$confirm({
          title: '您确定要撤销该认款单吗?',
          content: '',
          onOk() {
            postAction('/{v}/ReturnGoods/Cancel/CancelAsync?RecognitionId='+record.Id, {RecognitionId:record.Id}, 'P36012').then(res => {
              if (res.IsSuccess&&res.Data) {
                that.$message.success("操作成功");
                that.$refs.table.loadDatas(1, that.queryParam);
              }else{
                that.$message.warning(res.Msg)
              }
            })
            
          },
          onCancel() { },
        });
      }
    },
  }

}
</script>

<style>
</style>