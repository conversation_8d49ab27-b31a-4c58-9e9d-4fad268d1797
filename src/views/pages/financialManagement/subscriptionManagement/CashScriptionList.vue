<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate">
      <span slot="IsCancel" slot-scope="{text,record}">
        {{text?'已取消':'正常'}}
      </span>
    </SimpleTable>
    <CashScriptionInfo ref="CashScriptionInfo"></CashScriptionInfo>
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "CashScriptionList",
  mixins: [SimpleMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      searchItems: [
        {
          name: '认款单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'RecognitionNo'
        },
        {
          name: '认款类型',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/SystemTool/GetEnumsByName',
          key: 'CashRecognitionType',
          params: { enumName: 'EnumCashRecognitionType' },
          dataKey: { name: 'ItemDescription', value: 'ItemValue' }
        },
        {
          name: '兑付方',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'PayerKey'
        },
        {
          name: '认款人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'CreateByName'
        },
        {
          name: '认款时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime'
        },
        {
          name: '状态',
          type: SEnum.SELECT,
          key: 'IsCancel',
          items: [
            { label: '是', value: true },
            { label: '否', value: false }
          ],
          placeholder: '请选择'
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        sortArray: ['IsCancel'],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
      columns: [
        {
          title: '认款单号',
          dataIndex: 'RecognitionOrderNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款类型',
          dataIndex: 'CashRecognitionTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '兑付方',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognitionAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '认款人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '状态',
          dataIndex: 'IsCancel',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'IsCancel' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: "right",
          actionBtn: [{ name: '详情', icon: '', id: '18b28e1e-0e57-4150-8f37-2e5784d35d29' },{ name: '撤销', icon: '', id: 'd491ad93-97f4-4c44-89e8-5dd22005d028' ,specialShowFuc: record => {
                return !record.IsCancel
              }},],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {

      },
      isTableInitData: true,//是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/CashRecognition/GetOrderListAsync',
      },
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '撤销') {
        var that = this
        this.$confirm({
          title: '确认撤销',
          content: '是否撤销选中数据?',
          onOk: function() {
            postAction('/{v}/CashRecognition/CancelAsync?cashRecognitionRecordId='+record.RecognitionId, {cashRecognitionRecordId:record.RecognitionId }, 'P36012').then(res => {
              if (!res.IsSuccess) {
                that.$message.warning(res.Msg)
                return
              }
              if (!res.Data) return
              that.$refs.table.loadDatas(1, that.queryParam)
            })
          }
        })
      }else if(type=='详情'){
        this.$refs.CashScriptionInfo.show(record)
      }
    },

  }
}
</script>

<style>
</style>