<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab">

    </SimpleTable>
    <!-- 销货对抵冲红 -->
    <AcceptanceBlushAddBase ref="AcceptanceBlushAddBase" :type="4" @ok="modelOk" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'salesAreInBalanceBlush',
  title: '销货对抵冲红',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '对抵冲红单号', type: 'input', value: '', key: 'SalesOffsetOrderNo', defaultVal: '', placeholder: '请输入' },
        { name: '对抵单号', type: 'input', value: '', key: 'RedOffsetSalesOffsetOrderNo', defaultVal: '', placeholder: '请输入' },
        { name: '对抵方', type: 'input', value: '', key: 'OffsetParty', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增销货对抵冲红', type: 'primary', icon: '', key: '新增销货对抵冲红', id: '04a0d87a-091c-435a-bbb0-e4cbfca71645' },
        ],
        hintArray: [],
        tableTile: '',
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '对抵冲红单号',
          dataIndex: 'SalesOffsetOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对抵单号',
          dataIndex: 'RedOffsetSalesOffsetOrderNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对抵方',
          dataIndex: 'CustomerName',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对抵金额',
          dataIndex: 'OffsetAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '冲红金额',
          dataIndex: 'OffsetRedOffsetAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtn: [
            { name: '详情', icon: '', id: 'c1eeda14-2d98-4960-8b53-63423627fef3' },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/SalesOffset/List',
      },
    }
  },
  created() { },
  activated() {
    this.init()
  },
  methods: {
    init(queryParam) {
      if (queryParam) {
        queryParam.IsRedOffset = true
      } else {
        this.queryParam.IsRedOffset = true
      }
      this.$refs.table.loadDatas(null, queryParam || this.queryParam)
    },
    modelOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '新增销货对抵冲红') {
        this.$refs.AcceptanceBlushAddBase.add()
      } else if (type == '详情') {
        this.onDetailClick('salesAreInBalanceBlushInfo', { id: record.Id })
      }
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
      }
      this.init(queryParam)
    },
  },
}
</script>

<style>
</style>
