<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab">

    </SimpleTable>
    <!-- 采购发票冲红 -->
    <PurchaseInvoiceAddModal ref="PurchaseInvoiceAddModal" @ok="modelOk" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'purchaseInvoiceBlush',
  title: '采购发票冲红',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '供应商', type: 'input', value: '', key: 'SupplierKey', defaultVal: '', placeholder: '名称/拼音码/编号' },
        { name: '上传发票单号', type: 'input', value: '', key: 'OriginRemittedOrderNo', defaultVal: '', placeholder: '请输入' },
        { name: '发票冲红单号', type: 'input', value: '', key: 'RedOffSetRemittedOrderNo', defaultVal: '', placeholder: '请输入' },
        { name: '订单编号', type: 'input', value: '', key: 'PurchaseOrderNo', defaultVal: '', placeholder: '请输入' },
        { name: '发票号', type: 'input', value: '', key: 'InvoiceNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '付款方式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PaymentMode',
          dictCode: 'EnumPurchasePaymentMode',
          defaultVal: '',
          placeholder: '请选择',
        },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        { name: '单据编号', type: 'input', value: '', key: 'DocumentNo', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增发票冲红', type: 'primary', icon: '', key: '新增发票冲红', id: '556924b6-9603-46b7-8f19-fb4fdb1cf4db' },
        ],
        hintArray: [],
        tableTile: '',
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '发票冲红单号',
          dataIndex: 'RedOffSetRemittedOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '上传发票单号',
          dataIndex: 'OriginRemittedOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票号',
          dataIndex: 'InvoiceNo',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '勾稽金额',
          dataIndex: 'InvoiceVerificationAmount',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '冲红金额',
          dataIndex: 'InvoiceRedOffSetVerificationAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          actionBtn: [
            { name: '详情', icon: '', id: '7fb4d05f-0d88-4779-9356-2da3b4321b22' },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36009',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/PurchaseBussinessRedOffset/GetPurchaseInvoiceRedOffSetPageList',
      },
    }
  },
  created() { },
  activated() {
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '新增发票冲红') {
        this.$refs.PurchaseInvoiceAddModal.add(record)
      } else if (type == '详情') {
        this.onDetailClick('purchaseInvoiceBlushInfo', { id: record.Id })
      }
    },
    modelOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
      }
      this.$refs.table.loadDatas(1, queryParam)
    },
  },
}
</script>

<style>
</style>
