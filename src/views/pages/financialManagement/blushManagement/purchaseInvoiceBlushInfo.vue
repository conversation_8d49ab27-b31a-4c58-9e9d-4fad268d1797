<!--
 * @Author: LP
 * @Description: 发票采购冲红明细
 * @Date: 2023/07/07
-->
<template>
  <a-spin :spinning="loading">
    <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }" v-if="model">
      <a-descriptions :column="4">
        <a-descriptions-item>
          <div class="flc hcenter">
            <a-icon :type="getAuditIcon(model.ERPExecuteStatus)" theme="twoTone" :two-tone-color="getAuditColor(model.ERPExecuteStatus)" style="font-size: 32px" />
            <span :style="{ color: getAuditColor(model.ERPExecuteStatus) }">{{ model.ERPExecuteStatusStr || '--' }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">冲红金额</span>
          <div>¥{{ model.RedOffSetAmount || 0 }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">发票冲红编号</span>
          <div>{{ model.RedOffsetRemittedOrderNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">冲红时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
    <a-card :bodyStyle="{ padding: '16px 16px' }" class="mt15">
      <a-row>
        <a-col class="mb5" span="24"><span style="font-size: 16px; font-weight: 600;">基础信息</span></a-col>
        <a-descriptions :column="3">
          <a-descriptions-item>
            <span class="c999">供应商</span>
            <div>{{ model.SupplierName || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">发票号</span>
            <div>{{ model.InvoiceNo || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">付款方式</span>
            <div>{{ model.PaymentModeStr || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">冲红原因</span>
            <div>{{ model.RedOffSetReason || '--' }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </a-row>
      <a-row class="mt20">
        <a-col style="border: 1px solid #e8e8e8;padding: 8px;" class="mb5" span="24">
          <span class="mr5"><strong>上传发票单号：{{ model.InvoiceNo || '--' }}</strong></span>
          <span class="mr5"><strong>发票金额：{{ model.InvoiceAmount || 0 }}</strong></span>
          <span class="mr5"><strong>勾稽金额：{{ model.InvoiceVerificationAmount || 0 }}</strong></span>
        </a-col>
        <a-col span="24">
          <TableView ref="tableView" :showCardBorder="false" :tab="tab" :columns="columns" :dataList="model.PurchaseInvoiceRedOffSetDetails || []">

          </TableView>
        </a-col>
        <a-col span="24" class="mt5">
          <span class="mr5"><strong>冲红金额合计：</strong>{{ model.RedOffSetTotalAmount || 0 }}条</span>
          <span class="mr5"><strong>冲红税额合计：</strong>¥{{ model.RedOffSetTotalTaxAmount || 0 }}</span>
          <span class="mr5"><strong>冲红含税金额合计：</strong>¥{{ model.RedOffSetTotalTaxInclusiveAmount || 0 }}</span>
        </a-col>
      </a-row>

      <!-- 底部区域 -->
      <a-affix :offset-bottom="10" class="affix">
        <a-divider />
        <div>
          <a-button @click="goBack(true,true)" class="mr8">返回</a-button>
        </div>
      </a-affix>
    </a-card>

  </a-spin>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'purchaseInvoiceBlushInfo',
  mixins: [EditMixin, ListMixin],
  components: { JEllipsis },
  data() {
    return {
      model: {
        PurchaseInvoiceRedOffSetDetails: [],
      },
      loading: false,
      confirmLoading: false,
      orderId: '', //采购订单id
      httpHead: 'P36009',
      tab: {
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        rowKey: 'Id',
      },
      columns: [
        {
          title: '单据编号',
          dataIndex: 'DocumentNo',
          scopedSlots: { customRender: 'component' },
          width: 150,
          fixed: 'left',
          ellipsis: true,
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessEventTypeStr',
          scopedSlots: { customRender: 'component' },
          width: 100,
          ellipsis: true,
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          width: 120,
          ellipsis: true,
        },
        {
          title: '未开票数量',
          dataIndex: 'UnInvoicedQuantity',
          scopedSlots: { customRender: 'component' },
          width: 90,
          ellipsis: true,
        },
        {
          title: '单价',
          dataIndex: 'Price',
          scopedSlots: { customRender: 'price' },
          width: 100,
          ellipsis: true,
        },
        {
          title: '金额',
          dataIndex: 'Amount',
          scopedSlots: { customRender: 'price' },
          width: 100,
          ellipsis: true,
        },
        {
          title: '税率',
          dataIndex: 'TaxRate',
          scopedSlots: { customRender: 'component' },
          width: 80,
          ellipsis: true,
        },
        {
          title: '税额',
          dataIndex: 'TaxAmount',
          scopedSlots: { customRender: 'component' },
          width: 80,
          ellipsis: true,
        },
        {
          title: '含税单价',
          dataIndex: 'TaxIncludedPrice',
          scopedSlots: { customRender: 'component' },
          width: 80,
          ellipsis: true,
        },
        {
          title: '含税金额',
          dataIndex: 'TaxIncludedAmount',
          scopedSlots: { customRender: 'component' },
          width: 80,
          ellipsis: true,
        },
        {
          title: '本次开票数量',
          dataIndex: 'CurrentInvoicedQuantity',
          scopedSlots: { customRender: 'component' },
          width: 100,
          ellipsis: true,
        },
        {
          title: '税差',
          dataIndex: 'TaxDifference',
          scopedSlots: { customRender: 'component' },
          width: 80,
          ellipsis: true,
        },
        {
          title: '尾差',
          dataIndex: 'TailDifference',
          scopedSlots: { customRender: 'component' },
          width: 80,
          ellipsis: true,
        },
        {
          title: '冲红金额',
          dataIndex: 'RedOffSetTaxIncludedAmount',
          scopedSlots: { customRender: 'price' },
          width: 100,
          ellipsis: true,
        },
      ],
      url: {
        detail: '/{v}/PurchaseBussinessRedOffset/GetPurchaseInvoiceRedOffsetInfo',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.orderId = this.$route.query.id || ''
      this.setTitle('采购发票冲红明细')
      this.model = {
        PurchaseInvoiceRedOffSetDetails: [],
      }
      this.loadDetail()
    }
  },
  created() { },
  methods: {
    getAuditIcon(status) {
      // 未执行 = 1 执行失败 = 2
      // 执行成功 = 3 执行中 = 4
      if (status == 1) {
        return 'close-circle'
      } else if (status == 2) {
        return 'close-circle'
      } else if (status == 3) {
        return 'check-circle'
      } else {
        return 'clock-circle'
      }
    },
    getAuditColor(status) {
      if (status == 3) {
        return '#52c41a'
      } else {
        return 'red'
      }
    },
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { Id: that.orderId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
  },
}
</script>

<style scoped>

</style>
