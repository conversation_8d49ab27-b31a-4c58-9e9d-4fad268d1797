<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab">

    </SimpleTable>
    <!-- 商销认款冲红 -->
    <AcceptanceBlushAddBase ref="AcceptanceBlushAddBase" :type="1" @ok="modelOk" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'commercialAcceptanceBlush',
  title: '商销认款冲红',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '客户名称', type: 'input', value: '', key: 'Payer', defaultVal: '', placeholder: '名称/拼音码/编号' },
        { name: '商销认款冲红单号', type: 'input', value: '', key: 'RedOffsetOrderNo', defaultVal: '', placeholder: '请输入' },
        { name: '单据编号', type: 'input', value: '', key: 'RedOffsetBusinessNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '单据类型',
          type: 'selectBasicLink',
          value: '',
          vModel: 'BusinessOrderType',
          dictCode: 'EnumBusinessOrder',
          defaultVal: '',
          // notOption: [1,2,3],
          onlyOption: [20, 21, 22, 23],
          placeholder: '请选择',
        },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增商销认款冲红', type: 'primary', icon: '', key: '新增商销认款冲红', id: 'f7d0a6c8-1c56-49c4-8da0-cb6ada42b4f7' },
        ],
        hintArray: [],
        tableTile: '',
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '商销认款冲红单号',
          dataIndex: 'RedOffsetOrderNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据编号',
          dataIndex: 'RedOffsetBusinessNo',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户名称',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessOrderTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognitionAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '冲红金额',
          dataIndex: 'RedOffsetAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          actionBtn: [
            { name: '详情', icon: '', id: '9292a1a7-00d3-4f33-adaf-9e41d9aa6fce' },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/RedOffsetSale/GetRedOffsetSalePageListV2',
      },
    }
  },
  created() { },
  activated() {
    this.init()
  },
  methods: {
    init() {
      this.$refs.table.loadDatas(null, this.queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '新增商销认款冲红') {
        this.$refs.AcceptanceBlushAddBase.add()
      } else if (type == '详情') {
        this.onDetailClick('commercialAcceptanceBlushInfo', { id: record.Id })
      }
    },
    modelOk() {
      this.init()
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
      }
      this.$refs.table.loadDatas(1, queryParam)
    },
  },
}
</script>

<style>
</style>
