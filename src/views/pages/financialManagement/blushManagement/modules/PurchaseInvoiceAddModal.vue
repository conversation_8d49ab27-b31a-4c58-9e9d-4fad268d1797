<!-- 新增发票冲红 -->
<template>
  <a-modal :title="title" :width="800" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div>
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="供应商名称：" prop="SupplierId">
                <SingleInputSearchView placeholder="请输入供应商搜索" width="100%" :httpParams="{
                      pageIndex: 1,
                      pageSize: 100,
                      AuditStatus:2,
                      IsValid: true,
                    }" keyWord="keyWord" :dataKey="{ name: 'Name', value: 'Id' }" httpHead="P36003" Url="/{v}/Supplier/GetSuppliersForSelect" :v-model="model.SupplierId" :name="model.SupplierName" :isAbsolute="true" @clear="()=>{model.SupplierId = '';model.SupplierName = '';model.InvoiceNo='';}" @change="changeInput" />
              </a-form-model-item>
              <a-form-model-item label="发票号：" prop="InvoiceNo">
                <SingleInputSearchView ref="invoiceNo" placeholder="请输入发票号搜索" width="100%" :httpParams="{
                      pageIndex: 1,
                      pageSize: 5,
                      SupplierId: model.SupplierId,
                    }" keyWord="InvoiceNo" :dataKey="{ name: 'InvoiceNo', value: 'InvoiceNo' }" httpHead="P36009" :Url="model.SupplierId?'/{v}/PurchaseBussinessRedOffset/GetPurchaseInvoiceNoList':''" :v-model="model.InvoiceNo" :value="model.InvoiceNo" :name="model.InvoiceNo" :disabled="model.SupplierId?false:true" :isAbsolute="true" @clear="()=>{model.InvoiceNo = '';model.InvoiceNo = ''}" @change="changeInput2" />
              </a-form-model-item>
              <a-form-model-item label="" prop="table">
                <!-- 列表 -->
                <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ x: dataSource.length === 0 ? false : '100%', y: '350px' }">
                  <!-- 字符串超长截取省略号显示-->
                  <template slot="component" slot-scope="text">
                    <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                    <j-ellipsis :value="text" v-else />
                  </template>
                  <template slot="price" slot-scope="text">
                    <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                  </template>
                </a-table>
              </a-form-model-item>
              <a-form-model-item label="冲红原因：" prop="RedReason">
                <a-textarea placeholder="请输入" v-model="model.RedReason" :rows="4" />
              </a-form-model-item>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from "@/api/manage";
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: "PurchaseInvoiceAddModal",
  components: { JEllipsis },
  mixins: [ListMixin, EditMixin],
  data() {
    return {
      title: "发票冲红",
      visible: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      columns: [
        {
          title: '上传发票单号',
          dataIndex: 'RemittedOrderNo',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票金额',
          dataIndex: 'InvoiceAmount',
          width: 90,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '勾稽金额',
          dataIndex: 'CheckAmount',
          width: 90,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      confirmLoading: false,
      isInitData: false, //是否自动加载
      httpHead: 'P36009',
      url: {
        list: '/{v}/ExecutionOrder/GetAgreementExecutionAsync',
        add: "/{v}/PurchaseBussinessRedOffset/CreateRedOffsetPurchaseRemittedInvoice",
      },
    };
  },
  computed: {
    rules() {
      return {
        SupplierId: [{
          required: true,
          validator: (rule, value, callback) => {
            if (!this.model.SupplierId) {
              callback(new Error('请输入!'))
            } else {
              callback()
            }
          },
        },],
        InvoiceNo: [{
          required: true,
          validator: (rule, value, callback) => {
            if (!this.model.InvoiceNo) {
              callback(new Error('请输入!'))
            } else {
              callback()
            }
          },
        },],
        table: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.dataSource.length == 0) {
                callback(new Error('请选择发票号!'))
              } else {
                callback()
              }
            },
          },
        ],
        RedReason: [{ required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200, '冲红原因') }],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add() {
      this.model = {}
      this.dataSource = []
      this.visible = true;
    },
    changeInput(val, txt) {
      this.$set(this.model, 'SupplierId', val)
      this.$set(this.model, 'SupplierName', txt)
      this.$set(this.model, 'InvoiceNo', '')
      this.$refs.invoiceNo.clear()
      this.dataSource = []
      this.$refs.form.clearValidate()
    },
    changeInput2(val, txt) {
      this.$set(this.model, 'InvoiceNo', val)
      this.invoiceResult()
    },
    invoiceResult() {
      if (!this.model.SupplierId || !this.model.InvoiceNo) {
        return
      }
      let url = '/{v}/PurchaseBussinessRedOffset/GetPurchaseInvoiceReoffSetList'
      let formData = {
        SupplierId: this.model.SupplierId,
        InvoiceNo: this.model.InvoiceNo,
      }
      getAction(url, formData, 'P36009').then((res) => {
        if (res.IsSuccess) {
          this.dataSource = res.Data || []
          this.$refs.form.validateField(['table'])
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {
        this.confirmLoading = false;
      });
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          let array = this.dataSource.map(item => {
            return { PurchaseRemittedInvoiceId: item.PurchaseRemittedInvoiceId }
          })
          let formData = {
            SupplierId: this.model.SupplierId,
            InvoiceNo: this.model.InvoiceNo,
            RedReason: this.model.RedReason,
            PurchaseInvoiceDetailRedOffSets: array || [],
          }
          postAction(this.url.add, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
