<!--
 * @Author: LP
 * @Description: 认款冲红明细
 * @Date: 2023/07/07
-->
<template>
  <a-spin :spinning="loading">
    <div class="mb10">
      <page-header>
        <strong slot="Title">冲红详情</strong>
        <a-button slot="title-action" @click="goBack(true,true)">返回</a-button>
      </page-header>
    </div>
    <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }" v-if="model">
      <a-descriptions :column="4">
        <a-descriptions-item>
          <div class="flc hcenter">
            <a-icon :type="getChongAuditIcon(type == 4?model.headerData.SalesOffsetStatus:model.RecognitionStatus)" theme="twoTone" :two-tone-color="getChongAuditColor(type == 4?model.headerData.SalesOffsetStatus:model.RecognitionStatus)" style="font-size: 32px" />
            <span :style="{ color: getChongAuditIcon(type == 4?model.headerData.SalesOffsetStatus:model.RecognitionStatus) }">{{ type == 4?getChongStr(model.headerData.SalesOffsetStatus,model.headerData.SalesOffsetStatusStr):getChongStr(model.RecognitionStatus,model.RecognitionStatusStr) }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item v-for="(item,index) in titleData" :key="index">
          <span class="c999">{{item.name}}</span>
          <div>{{ item.val || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
    <a-card :bodyStyle="{ padding: '16px 16px' }" class="mt10">
      <a-row>
        <a-col class="mb5" span="24"><span style="font-size: 16px; font-weight: 600;">冲红信息</span></a-col>
        <a-descriptions :column="3">
          <a-descriptions-item v-for="(item,index) in baseData" :key="index">
            <span class="c999">{{item.name}}</span>
            <div>{{ item.val || '--' }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </a-row>
      <a-row class="mt20">
        <div v-if="type != 1">
          <a-col v-if="columnsOneTitle" style="border: 1px solid #e8e8e8;padding: 8px;" class="mb5" span="24">
            <strong class="mr5">{{columnsOneTitle}}</strong>
          </a-col>
          <a-col span="24">
            <TableView ref="tableView" :showCardBorder="false" :tab="tab" :columns="columnsOne" :dataList="model.yinFu['Bills'] || []">
              <span slot="clickA" slot-scope="{ text, record,index, column}">
                <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
                  <j-ellipsis :value="'' + text" :length="50" />
                </a>
                <span v-else>--</span>
              </span>
            </TableView>
          </a-col>
          <a-col v-if="type == 4" class="mb5" span="24">
            <span class="mr5"><strong>合计数量：</strong>{{model.yinFu['TotalCount'] || 0}}条</span>
            <span class="mr5"><strong>应付对抵合计：</strong>¥{{model.yinFu['PayableOffsetAmount'] || 0}}</span>
            <span class="mr5"><strong>应付冲红合计：</strong>¥{{model.yinFu['PayableOffsetAmount'] || 0}}</span>
          </a-col>
          <a-col v-if="type == 5" class="mb5" span="24">
            <span class="mr5"><strong>打款合计金额：</strong>¥{{model['RemittanceTotalAmount'] || 0}}</span>
            <span class="mr5"><strong>冲红合计金额：</strong>¥{{model['RedOffSetTotalAmount'] || 0}}</span>
          </a-col>
        </div>

        <div v-if="type != 5">
          <a-col v-if="columnsTwoTitle" style="border: 1px solid #e8e8e8;padding: 8px;" class="mb5 mt5" span="24">
            <strong class="mr5">{{columnsTwoTitle}}</strong>
          </a-col>
          <a-col span="24">
            <TableView ref="tableView" :showCardBorder="false" :tab="tab" :columns="columnsTwo" :dataList="model.yinShou['Bills'] || []">
              <span slot="clickA" slot-scope="{ text, record,index, column}">
                <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
                  <j-ellipsis :value="'' + text" :length="50" />
                </a>
                <span v-else>--</span>
              </span>
            </TableView>
          </a-col>
          <a-col v-if="type == 4" class="mb5" span="24">
            <span class="mr5"><strong>合计数量：</strong>{{model.yinShou['TotalCount'] || 0}}条</span>
            <span class="mr5"><strong>应收对抵合计：</strong>¥{{model.yinShou['PayableOffsetAmount'] || 0}}</span>
            <span class="mr5"><strong>应收冲红合计：</strong>¥{{model.yinShou['PayableOffsetAmount'] || 0}}</span>
          </a-col>
          <a-col v-else span="24">
            <span class="mr5"><strong>认款合计金额：</strong>¥{{model['TotalRecognitionAmount'] || 0}}</span>
            <span class="mr5" v-if="type == 2"><strong>冲红合计金额：</strong>¥{{model['RedoffsetAmount'] || 0}}</span>
            <span class="mr5" v-else><strong>冲红合计金额：</strong>¥{{model['TotalRedOffsetAmount'] || 0}}</span>
          </a-col>
        </div>
        <!-- 出纳打款展示冲红原因 -->
        <div v-if="type == 5">
          <a-col class="mb5" span="24">
            <span style="font-size: 16px; font-weight: 600; color: red">冲红原因</span>
          </a-col>
          <a-col span="24">
            <span>{{ model.Note || '--' }}</span>
          </a-col>
        </div>
      </a-row>

    </a-card>

  </a-spin>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
const PageHeader = () => import('@/components/page/PageHeader')
const columnsOne_1 = [
  {
    title: '收款单号',
    dataIndex: 'ReceiptNo',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '收款日期',
    dataIndex: 'ReceiptTime',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '收款方式',
    dataIndex: 'ReceiptTypeStr',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '打款方',
    dataIndex: 'Payer',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '收款账户',
    dataIndex: 'AccountName',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'Note',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '收款金额',
    dataIndex: 'ReceiptAmount',
    scopedSlots: { customRender: 'price' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '未认款金额',
    dataIndex: 'RemainingAmount',
    scopedSlots: { customRender: 'price' },
    width: 150,
    ellipsis: true,
  },
]
const columnsTwo_1 = [
  {
    title: '收款单号',
    dataIndex: 'ReceiptNo',
    scopedSlots: { customRender: 'component' },
    width: 130,
    ellipsis: true,
  },
  {
    title: '收款金额',
    dataIndex: 'ReceiptAmount',
    scopedSlots: { customRender: 'price' },
    width: 90,
    ellipsis: true,
  },
  {
    title: '认款单号',
    dataIndex: 'RecognitionOrderNo',
    scopedSlots: { customRender: 'component' },
    width: 130,
    ellipsis: true,
  },
  {
    title: '单据类型',
    dataIndex: 'BusinessOrderTypeStr',
    scopedSlots: { customRender: 'component' },
    width: 90,
    ellipsis: true,
  },
  {
    title: '单据编号',
    dataIndex: 'BusinessNo',
    scopedSlots: { customRender: 'component' },
    width: 130,
    ellipsis: true,
  },
  {
    title: '付款方式',
    dataIndex: 'PaymentModeStr',
    scopedSlots: { customRender: 'component' },
    width: 90,
    ellipsis: true,
  },
  {
    title: '认款金额',
    dataIndex: 'RecognitionAmount',
    scopedSlots: { customRender: 'price' },
    width: 90,
    ellipsis: true,
  },
  {
    title: '认款人',
    dataIndex: 'RecognitionByName',
    scopedSlots: { customRender: 'component' },
    width: 90,
    ellipsis: true,
  },
  {
    title: '冲红金额',
    dataIndex: 'RedOffsetAmount',
    scopedSlots: { customRender: 'price' },
    width: 90,
    ellipsis: true,
  },
]
const columnsOne_2 = [
  {
    title: '单据编号',
    dataIndex: 'ReceiptNo',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '收款日期',
    dataIndex: 'ReceiptTime',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '收款方式',
    dataIndex: 'ReceiptTypeStr',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '打款方',
    dataIndex: 'Payer',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '收款账户',
    dataIndex: 'AccountName',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'Note',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '收款金额',
    dataIndex: 'ReceiptAmount',
    scopedSlots: { customRender: 'price' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '未认款金额',
    dataIndex: 'RemainingAmount',
    scopedSlots: { customRender: 'price' },
    width: 150,
    ellipsis: true,
  },
]
const columnsTwo_2 = [
  {
    title: '单据编号',
    dataIndex: 'PriceAdjustmentNo',
    scopedSlots: { customRender: 'component' },
    width: 150,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'BusinessCreator',
    scopedSlots: { customRender: 'component' },
    width: 100,
    ellipsis: true,
  },
  {
    title: '对应订单编号',
    dataIndex: 'PurchaseOrderNo',
    width: 150,
    scopedSlots: { customRender: 'component' },
    ellipsis: true,
  },
  {
    title: '付款方式',
    dataIndex: 'PaymentModeStr',
    width: 150,
    scopedSlots: { customRender: 'component' },
    ellipsis: true,
  },
  {
    title: '发生日期',
    dataIndex: 'AdjustmentTime',
    width: 80,
    scopedSlots: { customRender: 'component' },
    ellipsis: true,
  },
  {
    title: '调价金额',
    dataIndex: 'TotalPriceAdjustment',
    scopedSlots: { customRender: 'price' },
    width: 60,
    ellipsis: true,
  },
  {
    title: '认款金额',
    dataIndex: 'RecognitionAmount',
    scopedSlots: { customRender: 'price' },
    width: 90,
    ellipsis: true,
  },
  {
    title: '认款冲红金额',
    dataIndex: 'RedRecognitionAmount',
    scopedSlots: { customRender: 'price' },
    width: 90,
    ellipsis: true,
  },
]
const columnsOne_3 = [
  {
    title: '收款单号',
    dataIndex: 'ReceiptNo',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '收款日期',
    dataIndex: 'ReceiptTime',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '收款方式',
    dataIndex: 'ReceiptTypeStr',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '打款方',
    dataIndex: 'Payer',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '收款账户',
    dataIndex: 'AccountName',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'Note',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '收款金额',
    dataIndex: 'ReceiptAmount',
    scopedSlots: { customRender: 'price' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '未认款金额',
    dataIndex: 'RemainingAmount',
    scopedSlots: { customRender: 'price' },
    width: 150,
    ellipsis: true,
  },
]
const columnsTwo_3 = [
  {
    title: '退货编号',
    dataIndex: 'RefundNo',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'BusinessCreator',
    scopedSlots: { customRender: 'component' },
    width: 100,
    ellipsis: true,
  },
  {
    title: '对应订单编号',
    dataIndex: 'PurchaseOrderNo',
    width: 100,
    scopedSlots: { customRender: 'component' },
    ellipsis: true,
  },
  {
    title: '付款方式',
    dataIndex: 'PaymentModeStr',
    width: 150,
    scopedSlots: { customRender: 'component' },
    ellipsis: true,
  },
  {
    title: '退货日期',
    dataIndex: 'RefundTime',
    width: 150,
    scopedSlots: { customRender: 'component' },
    ellipsis: true,
  },
  {
    title: '退货金额',
    dataIndex: 'RefundAmount',
    scopedSlots: { customRender: 'price' },
    width: 60,
    ellipsis: true,
  },
  {
    title: '认款金额',
    dataIndex: 'RecognitionAmount',
    scopedSlots: { customRender: 'price' },
    width: 90,
    ellipsis: true,
  },
  {
    title: '认款冲红金额',
    dataIndex: 'RedRecognitionAmount',
    scopedSlots: { customRender: 'price' },
    width: 90,
    ellipsis: true,
  },
]
const columnsOne_4 = [
  {
    title: '单据编号',
    dataIndex: 'BillNo',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'CreateByName',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '单据类型',
    dataIndex: 'BusinessOrderStr',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '对应订单编号',
    dataIndex: 'OrderNo',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '订单付款方式',
    dataIndex: 'PaymentModeStr',
    scopedSlots: { customRender: 'component' },
    width: 100,
    ellipsis: true,
  },
  {
    title: '发生日期',
    dataIndex: 'TransactionTime',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '应付金额',
    dataIndex: 'PayableAmount',
    scopedSlots: { customRender: 'price' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '剩余应付金额',
    dataIndex: 'ResiduePayableAmount',
    scopedSlots: { customRender: 'price' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '本次对抵金额',
    dataIndex: 'OffsetAmount',
    scopedSlots: { customRender: 'price' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '冲红金额',
    dataIndex: 'RedOffsetAmount',
    scopedSlots: { customRender: 'price' },
    width: 150,
    ellipsis: true,
  },
]
const columnsTwo_4 = [
  {
    title: '单据编号',
    dataIndex: 'BillNo',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'CreateByName',
    scopedSlots: { customRender: 'component' },
    width: 100,
    ellipsis: true,
  },
  {
    title: '单据类型',
    dataIndex: 'BusinessOrderStr',
    scopedSlots: { customRender: 'component' },
    width: 100,
    ellipsis: true,
  },
  {
    title: '对应订单编号',
    dataIndex: 'OrderNo',
    width: 100,
    scopedSlots: { customRender: 'component' },
    ellipsis: true,
  },
  {
    title: '订单收款方式',
    dataIndex: 'PaymentModeStr',
    width: 100,
    scopedSlots: { customRender: 'component' },
    ellipsis: true,
  },
  {
    title: '发生日期',
    dataIndex: 'TransactionTime',
    width: 100,
    scopedSlots: { customRender: 'component' },
    ellipsis: true,
  },
  {
    title: '应收金额',
    dataIndex: 'PayableAmount',
    scopedSlots: { customRender: 'price' },
    width: 90,
    ellipsis: true,
  },
  {
    title: '剩余应收金额',
    dataIndex: 'ResiduePayableAmount',
    scopedSlots: { customRender: 'price' },
    width: 90,
    ellipsis: true,
  },
  {
    title: '本次对抵金额',
    dataIndex: 'OffsetAmount',
    scopedSlots: { customRender: 'price' },
    width: 90,
    ellipsis: true,
  },
  {
    title: '冲红金额',
    dataIndex: 'RedOffsetAmount',
    scopedSlots: { customRender: 'price' },
    width: 90,
    ellipsis: true,
  },
]
const columnsOne_5 = [
  {
    title: '单据编号',
    dataIndex: 'DocumentNo',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '单据类型',
    dataIndex: 'BusinessEventTypeStr',
    scopedSlots: { customRender: 'component' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '付款金额',
    dataIndex: 'RemittanceAmount',
    scopedSlots: { customRender: 'price' },
    width: 150,
    ellipsis: true,
  },
  {
    title: '冲红金额',
    dataIndex: 'RedOffSetAmount',
    scopedSlots: { customRender: 'price' },
    width: 150,
    ellipsis: true,
  },
]

export default {
  name: 'AcceptanceBlushInfoBase',
  mixins: [EditMixin, ListMixin],
  components: { JEllipsis, PageHeader },
  props: {
    // 1 商销认款冲红 2 采购预付调价认款冲红 3 采购退货认款冲红
    // 4 销货对抵冲红 5 出纳打款冲红
    type: {
      type: Number,
      default: null,
    }
  },
  data() {
    return {
      model: {
        headerData: {},//头部信息
        yinFu: {},//应付明细
        yinShou: {},//应收明细
      },
      loading: false,
      confirmLoading: false,
      orderId: '', //采购订单id
      httpHead: 'P36012',
      titleData: [],
      baseData: [],
      tab: {
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        rowKey: '',
        customSlot: ['clickA'],
        rowClassName: (record, index) => {
          if (record.PayableAmount < 0 || record.ResiduePayableAmount < 0) {
            return 'table-color-red-dust'
          }
        }
      },
      columnsOne: [],
      columnsTwo: [],
      columnsOneTitle: '',
      columnsTwoTitle: '',
      url: {
        detail: '',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.init()
      this.loadDetail()
    }
  },
  created() { },
  methods: {
    init() {
      // 1 商销认款冲红 2 采购预付调价认款冲红 3 采购退货认款冲红
      // 4 销货对抵冲红 5 出纳打款冲红
      this.orderId = this.$route.query.id || ''
      this.model = {
        headerData: {},//头部信息
        yinFu: {},//应付明细
        yinShou: {},//应收明细
      }
      this.switchTypeData()
      if (this.type == 2) {
        this.setTitle('采购预付调价认款冲红明细')
      }
    },
    getChongAuditIcon(status) {
      let text = ''
      switch (this.type) {
        case 4: {
          //  未完成 = 1 已完成 = 2 已冲红 = 3 已作废 = 4
          if (status == 1) {
            text = 'close-circle'
          } else if (status == 2) {
            text = 'check-circle'
          } else if (status == 3) {
            text = 'check-circle'
          } else if (status == 4) {
            text = 'close-circle'
          } else {
            text = 'clock-circle'
          }
          break;
        }
        case 5: {
          //  未执行 = 1 执行失败 = 2 执行成功 = 3 执行中 = 4
          if (status == 1) {
            text = 'close-circle'
          } else if (status == 2) {
            text = 'close-circle'
          } else if (status == 3) {
            text = 'check-circle'
          } else if (status == 4) {
            text = 'clock-circle'
          } else {
            text = 'clock-circle'
          }
          break;
        }
        default: {
          if (status == 1) {
            text = 'check-circle' //成功
          } else if (status == 6) {
            text = 'close-circle' //失败
          } else {
            text = 'clock-circle' //进行中
          }
          break;
        }
      }
      return text
    },
    getChongAuditColor(status) {
      let text = ''
      switch (this.type) {
        case 4: {
          if (status == 1) {
            text = 'red'
          } else if (status == 2) {
            text = '#52c41a'
          } else if (status == 3) {
            text = '#52c41a'
          } else if (status == 4) {
            text = 'red'
          } else {
            text = '#52c41a'
          }
          break;
        }
        case 5: {
          //  未执行 = 1 执行失败 = 2 执行成功 = 3 执行中 = 4
          if (status == 1) {
            text = 'red'
          } else if (status == 2) {
            text = 'red'
          } else if (status == 3) {
            text = '#52c41a'
          } else if (status == 4) {
            text = '#52c41a'
          } else {
            text = '#52c41a'
          }
          break;
        }
        default: {
          if (status == 6) {
            text = 'red'
          } else {
            text = '#52c41a'
          }
          break;
        }
      }
      return text
    },
    getChongStr(status, str) {
      let text = ''
      switch (this.type) {
        case 4: {
          //  未完成 = 1 已完成 = 2 已冲红 = 3 已作废 = 4
          if (status == 3) {
            text = '冲红成功'
          } else {
            text = str
          }
          break;
        }
        case 5: {
          //  未执行 = 1 执行失败 = 2 执行成功 = 3 执行中 = 4
          if (status == 3) {
            text = '冲红成功'
          } else {
            text = str
          }
          break;
        }
        default: {
          // 1表示成功 6表示失败
          if (status == 1) {
            text = '冲红成功'
          } else {
            text = str
          }
          break;
        }
      }
      return text
    },
    switchTypeData() {
      this.model.headerData = {}
      this.model.yinFu = {}
      this.model.yinShou = {}
      switch (this.type) {
        case 1: {
          this.titleData = [
            { name: '冲红金额', val: '¥' + this.model['RedOffsetAmount'], },
            { name: '商销认款冲红单号', val: this.model['RedOffsetOrderNo'], },
            { name: '创建时间', val: this.model['CreateTime'], },
          ]
          this.baseData = [
            { name: '客户名称', val: this.model['Payer'], },
            { name: '单据编号', val: this.model['RedOffsetBusinessNo'], },
          ]
          this.model.yinFu['Bills'] = this.model.ReceiptOrder ? [this.model.ReceiptOrder] : []
          this.model.yinShou['Bills'] = this.model.RedOffsetList || []
          this.columnsOneTitle = ''
          this.columnsTwoTitle = ''

          this.columnsOne = []
          this.columnsTwo = columnsTwo_1
          this.url.detail = '/{v}/RedOffsetSale/GetRedOffsetSaleDetailV2'
          break;
        }
        case 2: {
          this.titleData = [
            { name: '冲红金额', val: '¥' + this.model['RedoffsetAmount'], },
            { name: '采退认款冲红单号', val: this.model['RedoffsetNo'], },
            { name: '创建时间', val: this.model['CreateTime'], },
          ]
          this.baseData = [
            { name: '供应商名称', val: this.model['Payer'], },
            { name: '认款单号', val: this.model['RecognitionOrderNo'], },
          ]
          this.model.yinFu['Bills'] = this.model.ReceiptOrder ? [this.model.ReceiptOrder] : []
          this.model.yinShou['Bills'] = this.model.PurchasePriceAdjustList || []
          this.columnsOneTitle = '收款单号：' + (this.model.ReceiptOrder ? this.model.ReceiptOrder['ReceiptNo'] : '')
          this.columnsTwoTitle = '认款单号：' + this.model['RecognitionOrderNo']

          this.columnsOne = columnsOne_2
          this.columnsTwo = columnsTwo_2
          this.url.detail = '/{v}/PurchasePriceAdjust/GetRedoffsetInfo'
          break;
        }
        case 3: {
          this.titleData = [
            { name: '冲红金额', val: '¥' + this.model['RedTotalRecognitionAmount'], },
            { name: '采退认款冲红单号', val: this.model['RedRecognitionOrderNo'], },
            { name: '创建时间', val: this.model['CreateTime'], },
          ]
          this.baseData = [
            { name: '供应商名称', val: this.model['Payer'], },
            { name: '认款单号', val: this.model['RecognitionOrderNo'], },
          ]
          this.model.yinFu['Bills'] = this.model.ReceiptOrder ? [this.model.ReceiptOrder] : []
          this.model.yinShou['Bills'] = this.model.PurchaseRefundOrderList || []
          this.columnsOneTitle = '收款单号：' + (this.model.ReceiptOrder ? this.model.ReceiptOrder['ReceiptNo'] : '')
          this.columnsTwoTitle = '认款单号：' + this.model['RecognitionOrderNo']

          this.columnsOne = columnsOne_3
          this.columnsTwo = columnsTwo_3
          this.url.detail = '/{v}/RedOffsetPurchase/GetRedOffsetPurchaseDetail'
          break;
        }
        case 4: {
          this.model.headerData = this.model.SalesOffsetInfo || {}
          let headerData = this.model.headerData
          this.titleData = [
            { name: '冲红金额', val: '¥' + headerData['OffsetRedOffsetAmount'], },
            { name: '对抵冲红单号', val: headerData['SalesOffsetOrderNo'], },
            { name: '创建时间', val: headerData['CreateTime'], },
          ]
          this.baseData = [
            { name: '对抵方', val: headerData['SupplierName'], },
            { name: '对抵单号', val: headerData['RedOffsetSalesOffsetOrderNo'], },
            { name: '对抵金额', val: '¥' + headerData['OffsetAmount'], },
            { name: '冲红原因', val: headerData['Remark'], },
          ]
          this.model.yinFu = this.model.PayableBill || {}
          this.model.yinShou = this.model.ReceivableBill || {}
          this.columnsOne = columnsOne_4
          this.columnsTwo = columnsTwo_4
          this.columnsOneTitle = '应付冲红明细'
          this.columnsTwoTitle = '应收冲红明细'
          this.url.detail = '/{v}/SalesOffset/Detail'
          break;
        }
        case 5: {
          this.titleData = [
            { name: '冲红金额', val: '¥' + this.model['RedOffSetAmount'], },
            { name: '打款冲红单号', val: this.model['RedOffsetRemittedOrderNo'], },
            { name: '创建时间', val: this.model['CreateTime'], },
          ]
          this.baseData = [
            { name: '客户/供应商名称', val: this.model['PayerName'], },
            { name: '付款/销退单号', val: this.model['OriginRemittedOrderNo'], },
          ]
          this.model.RecognitionStatus = this.model.ERPExecuteStatus
          this.model.RecognitionStatusStr = this.model.ERPExecuteStatusStr
          this.model.yinFu['Bills'] = this.model.Details || []
          this.columnsOne = columnsOne_5
          this.columnsOneTitle = ''
          this.columnsTwoTitle = ''
          this.url.detail = '/{v}/SettlementRedOffset/GetRemittanceRedOffSetInfo/GetRemittanceRedOffSetInfo'
          break;
        }
      }
    },
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { Id: that.orderId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            that.switchTypeData()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    clickA(text, record, index, column) {
      let key = column.dataIndex
    },
  },
}
</script>

<style scoped>

</style>
