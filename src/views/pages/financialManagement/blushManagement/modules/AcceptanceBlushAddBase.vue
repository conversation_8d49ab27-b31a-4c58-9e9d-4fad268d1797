<!-- 新增认款冲红 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div>
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item :label="label1+'：'" prop="PayerId">
                <SingleChoiceSearchView style="width: 100%" placeholder="请输入" :dataKey="url.dataKey" :httpParams="url.httpParams" :httpHead="url.cusHttpHead" httpType="GET" :keyWord="url.keyWord?url.keyWord:'keyWord'" :Url="url.cusList" v-model="model.PayerId" :name="model.Payer" @clear="clearSearchInput" @change="handleCustomer" />
              </a-form-model-item>
              <a-form-model-item :label="label2+'：'" prop="table">
                <div v-if="type == 1" style="display: flex;align-items: center;margin-bottom: 10px;">
                  <a-input class="choose-input" :placeholder="'请选择'+label2" v-model="model.BusinessNo" disabled style="width:50%" />
                  <a-button style="margin-left:10px;" type="primary" size="small" @click="chooseOrder">请选择</a-button>
                </div>
                <a-button v-else style="position: absolute;right:0;top:-32px;" type="primary" size="small" @click="chooseOrder">请选择</a-button>
                <!-- 列表 -->
                <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ x: dataSource.length === 0 ? false : '100%', y: '350px' }">
                  <!-- 字符串超长截取省略号显示-->
                  <template slot="component" slot-scope="text">
                    <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                    <j-ellipsis :value="text" v-else />
                  </template>
                  <template slot="price" slot-scope="text">
                    <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                  </template>
                </a-table>
              </a-form-model-item>
              <!-- 出纳打款冲红 start -->
              <template  v-if="RedOffsetOptionList.length>0">
                <a-form-model-item label="冲红类型：" prop="RemittancOrderRedOffsetScope" v-if="RedOffsetOptionList.some(v=>v.RemittancOrderRedOffsetScope === 1)">
                  <!-- RemittancOrderRedOffsetScope 2 全部 1 部分 -->
                  <a-radio-group v-model="model.RemittancOrderRedOffsetScope" v-if="RedOffsetOptionList.length>0">
                    <a-radio :value="item.RemittancOrderRedOffsetScope" v-for="item in RedOffsetOptionList" :key="item.RemittancOrderRedOffsetScope">{{item.RemittancOrderRedOffsetScope ===1 ? '部分冲红':'全部冲红'}}</a-radio>
                    <!-- <a-radio :value="true">部分冲红</a-radio> -->
                  </a-radio-group>
                </a-form-model-item>
                <a-form-model-item label="冲红金额：" prop="RedOffsetAmount">
                  <a-input-number style="width:100%"  placeholder="请输入" v-model="model.RedOffsetAmount" :min="model.RemittancOrderRedOffsetScope === 1?(RedOffsetAmountMin + 0.01):null" :max="model.RemittancOrderRedOffsetScope === 1?RedOffsetAmountMax:9999999999.99" :precision="2" :disabled="model.RemittancOrderRedOffsetScope === 2 ? true:false"/>
                  <div style="font-size: 12px;color: #999999;margin-top: 6px;" v-if="model.RemittancOrderRedOffsetScope === 1">{{RedOffsetAmountMin}}＜可冲红金额 ≤ {{RedOffsetAmountMax}}</div>
                </a-form-model-item>
              </template>
              <!-- 出纳打款冲红 end -->
              <a-form-model-item label="冲红原因：" prop="Note">
                <a-textarea placeholder="请输入" v-model="model.Note" :rows="4" />
              </a-form-model-item>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel">
        取消
      </a-button>
      <a-button :disabled="confirmLoading" @click="handleOk" type="primary">提交</a-button>
    </a-row>

    <!--商销认款单选择-->
    <TableSelectDataModal type="radio" ref="TableSelectDataModal" :width="900" :selectTableData="selectTableData" isToSelectClose @chooseData="chooseData"></TableSelectDataModal>
  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from "@/api/manage";
const JEllipsis = () => import('@/components/jeecg/JEllipsis')

const selectTableData1 = {
  searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
    { name: '单据编号', type: 'input', value: '', key: 'BusinessNo', defaultVal: '', placeholder: '请输入' },
    {
      name: '单据类型',
      type: 'selectBasicLink',
      value: '',
      vModel: 'BusinessOrderType',
      dictCode: 'EnumBusinessOrder',
      defaultVal: '',
      onlyOption: [20, 21, 22, 23],
      placeholder: '请选择',
    },
    { name: '对应订单编号', type: 'input', value: '', key: 'SalesOrderNo', defaultVal: '', placeholder: '请输入' },
  ],
  title: '选择单据编号',
  name: '单据编号',
  recordKey: 'Id',
  httpHead: 'P36012',
  isInitData: false,
  url: {
    listType: 'GET',
    list: '/{v}/RedOffsetSale/GetToRedOffsetSaleOrderPageList',
  },
  columns: [
    {
      title: '单据编号',
      dataIndex: 'BusinessNo',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '单据类型',
      dataIndex: 'OrderTypeStr',
      width: 80,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '对应订单编号',
      dataIndex: 'SalesOrderNo',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '付款方式',
      dataIndex: 'PaymentModeStr',
      width: 80,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '单据金额',
      dataIndex: 'TotalAmount',
      width: 80,
      ellipsis: true,
      scopedSlots: { customRender: 'prcie' },
    },
    {
      title: '已认款金额',
      dataIndex: 'RecognitionAmount',
      width: 90,
      ellipsis: true,
      scopedSlots: { customRender: 'prcie' },
    },
    {
      title: '创建时间',
      dataIndex: 'CreateTime',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 100,
      fixed: 'right',
      scopedSlots: { customRender: 'action' }
    }
  ],
}

const selectTableData2 = {
  searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
    { name: '收款单号', type: 'input', value: '', key: 'ReceiptNo', defaultVal: '', placeholder: '请输入' },
    { name: '认款单号', type: 'input', value: '', key: 'RecognitionOrderNo', defaultVal: '', placeholder: '请输入' },
    { name: '认款人', type: 'input', value: '', key: 'RecognitionUserName', defaultVal: '', placeholder: '请输入' },
  ],
  title: '选择认款单',
  name: '认款单',
  recordKey: 'Id',
  httpHead: 'P36012',
  isInitData: false,
  url: {
    listType: 'GET',
    list: '/{v}/RedOffsetSale/GetToRedOffsetRecognitionOrderPageList',
  },
  columns: [
    {
      title: '收款单号',
      dataIndex: 'ReceiptNo',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '认款单号',
      dataIndex: 'RecognitionOrderNo',
      width: 100,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '认款金额',
      dataIndex: 'RecognitionAmount',
      width: 100,
      ellipsis: true,
      scopedSlots: { customRender: 'price' },
    },
    {
      title: '认款人',
      dataIndex: 'RecognitionUserName',
      width: 100,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '认款时间',
      dataIndex: 'RecognitionTime',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'component' }
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 100,
      fixed: 'right',
      scopedSlots: { customRender: 'action' }
    }
  ],
}
const columns2 = [
  {
    title: '收款单号',
    dataIndex: 'ReceiptNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款单号',
    dataIndex: 'RecognitionOrderNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款金额',
    dataIndex: 'RecognitionAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '认款人',
    dataIndex: 'RecognitionUserName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]

const selectTableData3 = {
  searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
    { name: '收款单号', type: 'input', value: '', key: 'ReceiptNo', defaultVal: '', placeholder: '请输入' },
    { name: '认款单号', type: 'input', value: '', key: 'RecognitionOrderNo', defaultVal: '', placeholder: '请输入' },
    { name: '认款人', type: 'input', value: '', key: 'RecognitionUserName', defaultVal: '', placeholder: '请输入' },
  ],
  title: '选择采退认款单',
  name: '采退认款单',
  recordKey: 'Id',
  httpHead: 'P36012',
  isInitData: false,
  url: {
    listType: 'GET',
    list: '/{v}/RedOffsetSale/GetToRedOffsetRecognitionOrderPageList',
  },
  columns: [
    {
      title: '收款单号',
      dataIndex: 'ReceiptNo',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '认款单号',
      dataIndex: 'RecognitionOrderNo',
      width: 100,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '认款金额',
      dataIndex: 'RecognitionAmount',
      width: 100,
      ellipsis: true,
      scopedSlots: { customRender: 'price' },
    },
    {
      title: '认款人',
      dataIndex: 'RecognitionUserName',
      width: 100,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '认款时间',
      dataIndex: 'RecognitionTime',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'component' }
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 100,
      fixed: 'right',
      scopedSlots: { customRender: 'action' }
    }
  ],
}
const columns3 = [
  {
    title: '收款单号',
    dataIndex: 'ReceiptNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款单号',
    dataIndex: 'RecognitionOrderNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款金额',
    dataIndex: 'RecognitionAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '认款人',
    dataIndex: 'RecognitionUserName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]

const selectTableData4 = {
  searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
    { name: '销货对抵单号', type: 'input', value: '', key: 'SalesOffsetOrderNo', defaultVal: '', placeholder: '请输入' },
    { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
  ],
  title: '选择销货对抵申请',
  name: '销货对抵申请',
  recordKey: 'Id',
  httpHead: 'P36012',
  isInitData: false,
  url: {
    listType: 'GET',
    list: '/{v}/SalesOffset/List',
  },
  columns: [
    {
      title: '销货对抵单号',
      dataIndex: 'SalesOffsetOrderNo',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '对抵金额',
      dataIndex: 'OffsetAmount',
      width: 100,
      ellipsis: true,
      scopedSlots: { customRender: 'price' },
    },
    {
      title: '备注',
      dataIndex: 'Remark',
      width: 120,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '创建人',
      dataIndex: 'CreateByName',
      width: 100,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '创建时间',
      dataIndex: 'CreateTime',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'component' }
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 100,
      fixed: 'right',
      scopedSlots: { customRender: 'action' }
    }
  ],
}
const columns4 = [
  {
    title: '销货对抵单号',
    dataIndex: 'SalesOffsetOrderNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '对抵金额',
    dataIndex: 'OffsetAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '创建人',
    dataIndex: 'CreateByName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]

const selectTableData5 = {
  searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
    { name: '付款/销退单号', type: 'input', value: '', key: 'OriginRemittedOrderNo', defaultVal: '', placeholder: '请输入单号' },
    { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
  ],
  title: '选择打款单',
  name: '打款单',
  recordKey: 'RemittanceOrderId',
  httpHead: 'P36012',
  isInitData: false,
  url: {
    listType: 'GET',
    list: '/{v}/SettlementRedOffset/GetRedOffsetRemittanceOrderList/GetRedOffsetRemittanceOrderList',
  },
  columns: [
    {
      title: '付款/销退单号',
      dataIndex: 'OriginRemittedOrderNo',
      width: 180,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '付款来源',
      dataIndex: 'RemittanceSourceStr',
      width: 100,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '付款金额',
      dataIndex: 'ActualPaymentAmount',
      width: 100,
      ellipsis: true,
      scopedSlots: { customRender: 'price' },
    },
    {
      title: '结算方式',
      dataIndex: 'SettlementTypeStr',
      width: 100,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '创建人',
      dataIndex: 'CreateByName',
      width: 100,
      ellipsis: true,
      scopedSlots: { customRender: 'component' },
    },
    {
      title: '提交时间',
      dataIndex: 'ActualRemittanceTime',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'component' }
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 100,
      fixed: 'right',
      scopedSlots: { customRender: 'action' }
    }
  ],
}
const columns5 = [
  {
    title: '付款/销退单号',
    dataIndex: 'OriginRemittedOrderNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '付款方式',
    dataIndex: 'PaymentModeStr',
    width: 120,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '付款总金额',
    dataIndex: 'ToatalPaymentAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '实付金额',
    dataIndex: 'ActualPaymentAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '预付余额抵扣',
    dataIndex: 'PrepayDeductAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '创建人',
    dataIndex: 'CreateByName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]

export default {
  name: "AcceptanceBlushAddBase",
  components: { JEllipsis },
  mixins: [ListMixin, EditMixin],
  props: {
    // 1 商销认款冲红 2 采购预付调价认款冲红 3 采购退货认款冲红
    // 4 销货对抵冲红 5 出纳打款冲红
    type: {
      type: Number,
      default: null,
    }
  },
  data() {
    return {
      title: "认款冲红",
      visible: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      RedOffsetOptionList:[],
      RedOffsetAmountMin:null,
      RedOffsetAmountMax:null,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      label1: '',
      label2: '',
      selectTableData: {},
      columns: [],
      columns1: [
        {
          title: '收款单号',
          dataIndex: 'ReceiptNo',
          width: 170,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: (<j-ellipsis value={String(text) || ''} />),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '收款金额',
          dataIndex: 'ReceiptAmount',
          width: 90,
          ellipsis: true,
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => {
            const obj = {
              children: (<j-ellipsis value={String(text) || ''} />),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '认款单号',
          dataIndex: 'RecognitionOrderNo',
          width: 130,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据类型',
          dataIndex: 'OrderTypeStr',
          width: 90,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据编号',
          dataIndex: 'BusinessNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 90,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognitionAmount',
          width: 90,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '认款人',
          dataIndex: 'RecognitionUserName',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      confirmLoading: false,
      isInitData: false, //是否自动加载
      httpHead: 'P36012',
      url: {

      },
    };
  },
  computed: {
    rules() {
      return {
        PayerId: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.PayerId) {
                callback(new Error('请选择' + this.label1 + '!'))
              } else {
                callback()
              }
            },
          },
        ],
        table: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.dataSource.length == 0) {
                callback(new Error('请选择' + this.label2 + '!'))
              } else {
                callback()
              }
            },
          },
        ],
        Note: [{ required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200, '冲红原因') }],
        RemittancOrderRedOffsetScope: [{ required: true,message:'请选择'}],
        RedOffsetAmount: [{ required: true,message:'请输入'}],
      };
    },
  },
  watch: {
    'model.RemittancOrderRedOffsetScope'(val) {
      // console.log('model.RemittancOrderRedOffsetScope  ',val)
      if(val){
        let item = this.RedOffsetOptionList.find(item => item.RemittancOrderRedOffsetScope == val)
        this.RedOffsetAmountMin = item['RedOffsetAmountMin']
        this.RedOffsetAmountMax = item['RedOffsetAmountMax']
        this.model.RedOffsetAmount = item['RedOffsetAmount']
        this.$refs.form.clearValidate()
      }
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add() {
      // 1 商销认款冲红 2 采购预付调价认款冲红 3 采购退货认款冲红
      // 4 销货对抵冲红 5 出纳打款冲红
      this.switchTypeData()
      this.init()
      this.visible = true;
    },
    switchTypeData(callback) {
      // 1 商销认款冲红 2 采购预付调价认款冲红 3 采购退货认款冲红
      // 4 销货对抵冲红 5 出纳打款冲红
      let tableSelectDataQueryParam = {}
      let subDataQueryParam = {}
      this.selectTableData = {}
      switch (this.type) {
        case 1:
          this.title = '商销认款冲红'
          this.label1 = '客户名称';
          this.label2 = '单据编号';
          this.url.cusList = '/{v}/Customer/ListForBusiness'
          this.url.dataKey = { name: 'Name', value: 'Id', nameList: [] }
          this.url.cusHttpHead = 'P36002'
          this.url.httpParams = { PageIndex: 1, PageSize: 100 }
          this.url.keyWord = 'keyWord'
          this.selectTableData = selectTableData1
          this.columns = this.columns1
          this.url.add = '/{v}/RedOffsetSale/Submit';

          tableSelectDataQueryParam = {
            PayerId: this.model.PayerId,
            RecognitionOrderMode: 10,
          }

          let IdList = this.dataSource.map(item => item.Id) || []
          subDataQueryParam = {
            PayerId: this.model.PayerId,
            RecognitionSaleId: this.model.RecognitionSaleId || "",
            IdList: IdList,
            Note: this.model.Note,
          }
          break;
        case 2:
          this.title = '采购预付款调价认款冲红'
          this.label1 = '供应商名称';
          this.label2 = '认款单';
          this.url.cusList = '/{v}/Supplier/GetSuppliersForSelect'
          this.url.dataKey = { name: 'Name', value: 'Id', nameList: [] }
          this.url.cusHttpHead = 'P36003'
          this.url.httpParams = { PageIndex: 1, PageSize: 100, AuditStatus: 2 }
          this.url.keyWord = 'keyWord'
          this.selectTableData = selectTableData2
          this.columns = columns2
          this.url.add = '/{v}/PurchasePriceAdjust/Redoffset';

          tableSelectDataQueryParam = {
            PayerId: this.model.PayerId,
            RecognitionOrderMode: 17,
          }

          subDataQueryParam = {
            PayerId: this.model.PayerId,
            RecognitionOrderIds: (this.dataSource.length > 0 && this.dataSource[0].Id) ? [this.dataSource[0].Id] : "",
            Note: this.model.Note,
          }

          break;
        case 3:
          this.title = '采退认款冲红'
          this.label1 = '供应商名称';
          this.label2 = '采退认款单';
          this.url.cusList = '/{v}/Supplier/GetSuppliersForSelect'
          this.url.dataKey = { name: 'Name', value: 'Id', nameList: [] }
          this.url.cusHttpHead = 'P36003'
          this.url.httpParams = { PageIndex: 1, PageSize: 100, IsRedOffset: false, AuditStatus: 2 }
          this.url.keyWord = 'keyWord'
          this.selectTableData = selectTableData3
          this.columns = columns3
          this.url.add = '/{v}/RedOffsetPurchase/Submit';

          tableSelectDataQueryParam = {
            PayerId: this.model.PayerId,
            RecognitionOrderMode: 11,
          }

          subDataQueryParam = {
            RecognitionOrderId: this.dataSource.length > 0 ? this.dataSource[0].Id : "",
            Remark: this.model.Note,
          }
          break;
        case 4:
          this.title = '销货对抵冲红'
          this.label1 = '对抵方';
          this.label2 = '销货对抵申请';
          this.url.cusList = '/{v}/SalesOffset/GetOffsetPartyList'
          this.url.dataKey = { name: 'SupplierName', value: 'PartyId', nameList: [] }
          this.url.cusHttpHead = 'P36012'
          this.url.httpParams = { PageIndex: 1, PageSize: 100, IsRedOffset: false }
          this.url.keyWord = 'keyWord'
          this.selectTableData = selectTableData4
          this.columns = columns4
          this.url.add = '/{v}/SalesOffset/CreateSalesOffSetRedOffset';

          tableSelectDataQueryParam = {
            OffsetParty: this.model.Payer
          }

          subDataQueryParam = {
            SalesOffsetOrderNo: this.dataSource.length > 0 ? this.dataSource[0].SalesOffsetOrderNo : "",
            Remark: this.model.Note,
          }
          break;
        case 5:
          this.title = '打款冲红'
          this.label1 = '客户/供应商';
          this.label2 = '打款单';
          this.url.cusList = '/{v}/SettlementRedOffset/GetRemittanceRedOffsetPayerList/GetRemittanceRedOffsetPayerList'
          this.url.dataKey = { name: 'PayerName', value: 'PayerId', nameList: [] }
          this.url.cusHttpHead = 'P36012'
          this.url.httpParams = { PageIndex: 1, PageSize: 100, IsRedOffset: false }
          this.url.keyWord = 'PayerName'
          this.selectTableData = selectTableData5
          this.columns = columns5
          this.url.add = '/{v}/SettlementRedOffset/GetRedOffsetRemittanceOrderList/CreateRemittancOrdereRedOffSet';
          this.url.getRemittanceOrderRedOffsetScopeOption = '/{v}/SettlementRedOffset/GetRemittanceOrderRedOffsetScopeOptionDetail'
          tableSelectDataQueryParam = {
            PayerId: this.model.PayerId
          }

          subDataQueryParam = {
            RemittanceOrderId: this.dataSource.length > 0 ? this.dataSource[0].RemittanceOrderId : "",
            RedOffSetReason: this.model.Note,
            RedOffsetAmount: this.model.RedOffsetAmount,
            RemittancOrderRedOffsetScope: this.model.RemittancOrderRedOffsetScope,
          }
          break;
      }
      callback && callback(tableSelectDataQueryParam, subDataQueryParam)
    },
    init() {
      this.model = {}
      this.dataSource = []
    },
    // 选择客户
    handleCustomer(val, txt, item) {
      this.model.PayerId = val
      this.model.Payer = txt
      this.$refs.form.clearValidate()
      this.dataSource = []
      this.RedOffsetOptionList = []
    },
    clearSearchInput() {
      this.model.PayerId = ''
      this.model.Payer = ''
      this.dataSource = []
    },
    chooseOrder() {
      if (!this.model.PayerId) {
        this.$message.warning('请先选择' + this.label1)
        return
      }
      this.switchTypeData((queryParam) => {
        this.$refs.TableSelectDataModal.show(this.dataSource, queryParam)
      })
    },
    // 选择的数据
    chooseData(data) {
      console.log('this.type ', this.type )
      console.log('选择的数据', data)
      if (this.type == 1) {
        if (data && data.length > 0) {
          this.getShangXiaoTable(data || [])
        }
      } else {
        this.dataSource = data || []
        this.$refs.form.validateField(['table'])
        // 出纳打款冲红 获取冲红范围
        if(this.type == 5){
          this.$set(this.model, 'RemittancOrderRedOffsetScope', null)
          this.$set(this.model, 'RedOffsetAmount', null)

          this.getRemittanceOrderRedOffsetScopeOption(data)
        }
      }
    },
    // 出纳打款冲红 获取冲红范围
    getRemittanceOrderRedOffsetScopeOption(data=[]){
      let params = {
        remittanceOrderId: data.length ? data[0].RemittanceOrderId : '',
      }
      getAction(this.url.getRemittanceOrderRedOffsetScopeOption, params, this.httpHead).then((res) => {
        if (res.IsSuccess) {
        //  console.log('冲红范围', res.Data)
         this.RedOffsetOptionList = res.Data ? res.Data['RedOffsetOptionList'] : []
         if((this.RedOffsetOptionList || []).length){
          this.model.RemittancOrderRedOffsetScope =  this.RedOffsetOptionList[0].RemittancOrderRedOffsetScope
         }
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {

      });
    },
    getShangXiaoTable(data) {
      let params = {
        Id: data ? data[0].Id : '',
      }
      getAction('/{v}/RedOffsetSale/GetToRedOffsetSaleOrderAssociatedList', params, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          this.model.BusinessNo = data[0].BusinessNo
          this.model.RecognitionSaleId = data[0].Id
          // 列合并处理数据
          let arr = this.getListRowSpanData(res.Data || [], '', 'ReceiptNo', 'rowSpan') || []
          this.dataSource = arr || []
          this.$refs.form.validateField(['table'])
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {

      });
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          // 1 商销认款冲红 2 采购预付调价认款冲红 3 采购退货认款冲红
          // 4 销货对抵冲红 5 出纳打款冲红
          let params = {}
          this.switchTypeData((queryParam, paramsdata) => {
            params = paramsdata
          })
          postAction(this.url.add, params, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
<style scoped>
.choose-input:disabled {
  color: rgba(0, 0, 0, 0.65);
  background-color: white;
}
</style>
