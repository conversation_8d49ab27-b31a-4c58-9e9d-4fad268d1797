<!--
 * @Author: LP
 * @Description: 重新打款
 * @Date: 2023/07/13
-->
<template>
  <a-card :bodyStyle="{ padding: '0 0 10px 0' }" :bordered="false">
    <ApplyForPayment ref="ApplyForPayment" :opType="opType" isEdit :id="id" :orderNo="orderNo" :cLoading="confirmLoading" />
    <a-affix :offset-bottom="10" class="affix">
      <div class="pd1016">
        <YYLButton menuId="c81e89fc-5718-43ed-85ce-02cff0155669" text="提交申请付款" type="primary" :loading="confirmLoading" @click="onSaveAndAuditClick" />
        <a-button :loading="confirmLoading" @click="goBack(true,true)">取消</a-button>
      </div>
    </a-affix>
  </a-card>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'cashierRemitBlushRecredit',
  mixins: [EditMixin, ListMixin],
  components: {},
  data() {
    return {
      confirmLoading: false,
      opType: 1, //类型 1 申请付款 2 上传发票
      id: '',
      orderNo: '',
      httpHead: 'P36009',
      url: {
        save: '/{v}/PurchaseRemittedOrder/CreatePurchaseRemitted',
        editPurchaseRemitted: '/{v}/PurchaseRemittedOrder/EditPurchaseRemitted',
        submitAudit1: '/{v}/PurchaseRemittedOrder/SubmitPurchaseRemitted',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      // 默认付款
      this.opType = this.$route.query.type * 1 || 1
      this.id = this.$route.query.id || ''
      this.orderNo = this.$route.query.orderNo || ''
      this.setTitle('重新打款')
    }
  },
  methods: {
    saveData(data) {
      let that = this
      that.confirmLoading = true
      postAction(that.id ? that.url.editPurchaseRemitted : that.url.save, data, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.goBack(true,true)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    onSaveAndAuditClick() {
      let that = this
      this.$refs.ApplyForPayment.checkForm((data) => {
        if (data) {
          that.saveAndAudit(data)
        }
      })
    },
    saveAndAudit(data) {
      let that = this
      that.confirmLoading = true
      let url = that.url.submitAudit1
      data.IsRemittanceOrderCreate = true
      console.log('saveAndAudit', data)
      postAction(url, data, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.goBack(true,true)
            setTimeout(() => {
              that.$message.success('操作成功，请去打款！')
              that.$root.fromPgae = 'againRemit'
              that.onDetailClick('/pages/financialManagement/financialSettlement/cashierPayList')
            }, 1000)
          } else {
            that.$message.error(res.Msg)
            that.confirmLoading = false
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    
  },
}
</script>
<style lang="less" scoped>

</style>
