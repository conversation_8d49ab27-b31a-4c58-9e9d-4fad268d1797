<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab">

    </SimpleTable>
    <!-- 出纳打款冲红 -->
    <AcceptanceBlushAddBase ref="AcceptanceBlushAddBase" :type="5" @ok="modelOk" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'cashierRemitBlush',
  title: '出纳打款冲红',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '供应商/客户', type: 'input', value: '', key: 'PayerKey', defaultVal: '', placeholder: '请输入' },
        { name: '打款冲红单号', type: 'input', value: '', key: 'RedOffSetRemittedOrderNo', defaultVal: '', placeholder: '请输入' },
        { name: '付款/销退单号', type: 'input', value: '', key: 'OriginRemittedOrderNo', defaultVal: '', placeholder: '请输入' },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增打款冲红', type: 'primary', icon: '', key: '新增打款冲红', id: '82dc03c1-b926-4682-8985-3ab9a77d8c28' },
        ],
        hintArray: [],
        tableTile: '',
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '打款冲红单号',
          dataIndex: 'RedOffSetRemittedOrderNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款/销退单号',
          dataIndex: 'OriginRemittedOrderNo',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商/客户名称',
          dataIndex: 'PayerName',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '打款金额',
          dataIndex: 'RemittanceAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '冲红金额',
          dataIndex: 'RedOffSetAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          actionBtn: [
            { name: '详情', icon: '', id: '7ed4953f-bbe5-49e9-beb0-6eef513b9f31' },
            {
              name: '重新打款', icon: '', id: '23b477e0-6b7f-423c-a219-a7a5833f03af',
              specialShowFuc: e => {
                return e.RemittanceSource == 1 && e.IsRedOffsetCreateOrder == false
              }
            }
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/SettlementRedOffset/GetRemittanceRedOffSetPage/GetRemittanceRedOffSetPageList',
      },
    }
  },
  created() { },
  activated() {
    let queryParam = {
      ...this.queryParam,
      ...this.$refs.SimpleSearchArea.queryParam
    }
    this.$refs.table.loadDatas(null, queryParam)
  },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '新增打款冲红') {
        this.$refs.AcceptanceBlushAddBase.add()
      } else if (type == '详情') {
        this.onDetailClick('cashierRemitBlushInfo', { id: record.Id })
      } else if (type == '重新打款') {
        this.onDetailClick('cashierRemitBlushRecredit', { orderNo: record.OriginRemittedOrderNo })
      }
    },
    modelOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
      }
      this.$refs.table.loadDatas(1, queryParam)
    },
  },
}
</script>

<style>
</style>
