<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab">

    </SimpleTable>
    <!-- 采购预付调价认款冲红 -->
    <AcceptanceBlushAddBase ref="AcceptanceBlushAddBase" :type="2" @ok="modelOk" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'purchaseAdjustmentYuBlush',
  title: '采购预付调价认款冲红',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '供应商名称', type: 'input', value: '', key: 'Payer', defaultVal: '', placeholder: '名称/拼音码/编号' },
        { name: '调价认款冲红单号', type: 'input', value: '', key: 'RedoffsetNo', defaultVal: '', placeholder: '请输入' },
        { name: '认款单号', type: 'input', value: '', key: 'RecognitionOrderNo', defaultVal: '', placeholder: '请输入' },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增调价认款冲红', type: 'primary', icon: '', key: '新增调价认款冲红', id: '94889c9b-db2e-4e0d-af8f-ea171cb8d4ce' },
        ],
        hintArray: [],
        tableTile: '',
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '调价认款冲红单号',
          dataIndex: 'RedoffsetOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调价认款单号',
          dataIndex: 'RecognitionOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商名称',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognitionAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '冲红金额',
          dataIndex: 'RedoffsetAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          actionBtn: [
            { name: '详情', icon: '', id: '050f05a7-55dc-4995-a571-45c754775752' },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/PurchasePriceAdjust/GetRedOffsetList',
      },
    }
  },
  created() { },
  activated() {
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '新增调价认款冲红') {
        this.$refs.AcceptanceBlushAddBase.add()
      } else if (type == '详情') {
        this.onDetailClick('purchaseAdjustmentYuBlushInfo', { id: record.Id })
      }
    },
    modelOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
      }
      this.$refs.table.loadDatas(1, queryParam)
    },
  },
}
</script>

<style>
</style>
