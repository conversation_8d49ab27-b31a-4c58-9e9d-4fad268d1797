<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab" />
    <!-- 新增 -->
    <PlatformPayAccountAddModal ref="PlatformPayAccountAddModal" @ok="modalOk"></PlatformPayAccountAddModal>
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "PlatformPayAccount",
  title: '平台收款账户',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '开户名', type: 'input', value: '', key: 'AccountName', defaultVal: '', placeholder: '请输入' },
        { name: '开户银行', type: 'input', value: '', key: 'BankName', defaultVal: '', placeholder: '请输入' },
        { name: '银行账号', type: 'input', value: '', key: 'BankNo', defaultVal: '', placeholder: '请输入' },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '启用/禁用',
          type: 'select',
          value: '',
          key: 'IsValidVal',
          defaultVal: [
            { title: '启用', value: 1 },
            { title: '禁用', value: 2 },
          ],
          placeholder: '请选择'
        },
        { name: '创建时间', type: 'timeInput', value: '', key: ['CreateTimeBegin', 'CreateTimeEnd'], defaultVal: ['', ''], placeholder: ['开始日期', '结束日期'] },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增', type: 'primary', icon: 'plus', key: 'add', id: 'c881cccd-8173-4991-8457-b07171ed7461', }
        ],
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
      columns: [
        {
          title: '开户名',
          dataIndex: 'AccountName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开户银行',
          dataIndex: 'BankName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '银行账号',
          dataIndex: 'BankNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '启用/禁用',
          dataIndex: 'IsValid',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'IsValid' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: "right",
          actionBtn: [
            {
              name: '编辑', icon: '', id: 'f00cb1c8-389f-4753-a3c4-3ff1f1494ed4'
            },
          ],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {

      },
      isTableInitData: true,//是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: 'POST',//请求方式
      linkUrl: {
        list: '/{v}/CashReceiptNew/GetReceiptAccountList',
        del: '/{v}/CashReceiptNew/RemoveReceiptOrder',
        isValidUrl: '/{v}/CashReceiptNew/SetReceiptAccountIsValid' //若有是否有效需要写这个url
      },
    }
  },
  created() {

  },
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
      }
      queryParam.IsValid = queryParam.IsValidVal == 1 ? true : (queryParam.IsValidVal == 2 ? false : null)
      delete queryParam.IsValidVal
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 确定删除
    subDelInfo(record) {
      let params = {}
      let url = this.linkUrl.del + '?id=' + record.Id
      putAction(url, params, this.linkHttpHead)
        .then(res => {
          if (res.Data) {
            this.$message.success('删除成功')
            this.$refs.table.loadDatas(1, this.queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(err => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    modalOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        this.$refs.PlatformPayAccountAddModal.add()
      } else if (type == '编辑') {
        this.$refs.PlatformPayAccountAddModal.edit(record)
      }
    },

  }

}
</script>

<style>
</style>