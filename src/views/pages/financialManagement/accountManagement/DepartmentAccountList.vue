<template>
  <a-row>
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tableInfo="tableInfo" :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab">
      <!-- 部门负责人 -->
      <span slot="PrincipalName" slot-scope="{ text, record }">
        <j-ellipsis :value="`${text || ''}/${record['PrincipalPhone'] || ''}`" :length="30" />
      </span>
    </SimpleTable>
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'

export default {
  name: 'DepartmentAccountList',
  components: { JEllipsis },
  mixins: [SimpleMixin],
  data() {
    return {
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        rowKey: 'AccountHolderId',
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      tableInfo: {
        scrollY: '550px',
        scrollX: null,
        actionIndex: null, //操作项的位置
        size: 'middle',
      },
      columns: [
        {
          title: '部门名称',
          dataIndex: 'Name',
          width: 300,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '（货款）应收余额',
          dataIndex: 'ReceivableBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '（货款）预收余额',
          dataIndex: 'AdvancePaymentBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '（货款）应付余额',
          dataIndex: 'PayableBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '（货款）预付款余额',
          dataIndex: 'PrepaymentBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '（返利）应收余额',
          dataIndex: 'RebateReceivableBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtn: [
            {
              name: '详情',
              icon: '',
              id: '34af2173-e527-416a-9d09-15bc882ebd95',
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36011',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/Account/GetDepartmentListAsync',
      },
    }
  },
  created() { },
  mounted() {

  },
  activated() {
    this.$refs.table.loadDatas(null, this.queryParam, (data) => {
      // 获取表格数据
      if (data && data.length > 0) {
        // 默认展开第一个
        this.$refs.table.expandedRowKeys = [data[0].Id]
        this.$refs.table.dataSource = this.setTableData(data)
      }
    })
  },
  methods: {
    // 递归重组数据
    setTableData(data) {
      if (data == null || data.length <= 0) {
        return []
      }
      let tempArr = []
      try {
        data.forEach((item) => {
          var temp = item
          if (item.ChildrenDepartments && item.ChildrenDepartments.length > 0) {
            temp['children'] = this.setTableData(item.ChildrenDepartments)
          }
          tempArr.push(temp)
        })
      } catch (e) {
        console.log(e)
      }
      return tempArr
    },
    // 列表操作
    operate(record, type) {
      if (type == '详情') {
        let recordObj = {
          AccountHolderId: record.AccountHolderId,
          ContrastCustomerId: record.ContrastCustomerId,
          Name: record.Name,
          Contacts: record.Contacts,
        }
        this.onDetailClick('DepartmentAccountInfo', { info: JSON.stringify(recordObj) })
      }
    },
  },
}
</script>

<style></style>
