<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab" />
    <!-- 新增 -->
    <ReceivableAndPayableAddModal ref="ReceivableAndPayableAddModal" @ok="modalOk"></ReceivableAndPayableAddModal>
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "ReceivableAndPayable",
  title: '应收/应付调整单',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '调整单号', type: 'input', value: '', key: 'Code', defaultVal: '', placeholder: '请输入' },
        { name: '企业名称', type: 'input', value: '', key: 'MerchantKey', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '调整类型',
          type: 'select',
          value: '',
          key: 'AccountType',
          defaultVal: [
            { title: '应收', value: 1 },
            { title: '应付', value: 3 },
          ],
          placeholder: '请选择'
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '同步状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '执行状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'ExecuteStatus',
          dictCode: 'EnumExecuteStatus',
          defaultVal: '',
          placeholder: '请选择'
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增调整单', type: 'primary', icon: 'plus', key: 'add', id: 'e0e7f3ba-1c4f-4f5a-aac3-f95d08e8f3f2', }
        ],
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
      columns: [
        {
          title: '调整单号',
          dataIndex: 'Code',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '企业名称',
          dataIndex: 'MerchantName',
          width: 220,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调整类型',
          dataIndex: 'AccountTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调整金额',
          dataIndex: 'AdjustmentAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调整后余额',
          dataIndex: 'NewAdjustmentAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '执行状态',
          dataIndex: 'ExecuteStatusStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: "right",
          actionBtn: [
            {
              name: '详情', icon: '', id: '978f2f4e-5174-42eb-a46d-27a6c0441e98', specialShowFuc: (record) => {
                //未提交 = 0, 审核中 = 1, 审核通过 = 2, 审核失败 = 3
                return record.AuditStatus == 1 || record.AuditStatus == 2
              },
            },
            {
              name: '编辑', icon: '', id: 'f7bf79b6-0fad-4c0f-9ec7-7a61fdeb8807', specialShowFuc: (record) => {
                return record.AuditStatus == 0 || record.AuditStatus == 3
              },
            },
            {
              name: '删除', icon: '', id: '8e5bd80d-f9bf-4071-a535-a4d4769e7058', specialShowFuc: (record) => {
                return record.AuditStatus == 0 || record.AuditStatus == 3
              },
            },
          ],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {

      },
      isTableInitData: true,//是否自动加载
      linkHttpHead: 'P36011',
      linkUrlType: 'GET',//请求方式
      linkUrl: {
        list: '/{v}/Adjustment/GetListAsync',
        del: '/{v}/Adjustment/DeleteAsync',
      },
    }
  },
  created() {

  },
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
      }
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 确定删除
    subDelInfo(record, type) {
      let params = {}
      let url = this.linkUrl.del + '?accountAdjustmentId=' + record.Id
      postAction(url, params, this.linkHttpHead)
        .then(res => {
          if (res.Data) {
            this.$message.success('删除成功')
            this.$refs.table.loadDatas(1, this.queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(err => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    modalOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        this.$refs.ReceivableAndPayableAddModal.add()
      } else if (type == '编辑') {
        this.$refs.ReceivableAndPayableAddModal.edit(record)
      } else if (type == '详情') {
        this.onDetailClick('/pages/financialManagement/accountManagement/ReceivableAndPayableInfo', { id: record.Id })
      } else if (type == '删除') {
        let that = this
        this.$confirm({
          title: '温馨提示',
          content: '您确定要删除该调整单吗?',
          onOk() {
            that.subDelInfo(record, type)
          },
          onCancel() { },
        });
      }
    },

  }

}
</script>

<style>
</style>