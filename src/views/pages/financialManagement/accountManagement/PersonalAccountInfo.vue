<template>
  <a-row>
    <a-card v-if="info" style="margin-bottom: 16px">
      <a-descriptions :column="4" title="个人账户详情">
        <a-descriptions-item label="姓名">{{ info.Name || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="手机号">{{ info.Phone || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="所在部门">{{ info.DepartmentName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="人员类型">{{ info.TypeStr || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
      <a-button style="position: absolute;right: 18px;top: 20px;" @click="goBack(true,true)">返回</a-button>
    </a-card>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchItems" @searchQuery="searchQuery" />
    <div class="amount-con">
      <a-row v-if="model">
        <a-col :span="5" class="amount" :class="{ cursor: queryParam && queryParam.AccountType != 1 }" @click="selectCurTable(1)">
          <div class="gg item">
            <div class="num">{{ model.ReceivableBalance }}</div>
            <div>（货款）应收余额(元)</div>
          </div>
          <div class="triangle" v-if="queryParam && queryParam.AccountType == 1"></div>
        </a-col>
        <a-col :span="5" class="amount" :class="{ cursor: queryParam && queryParam.AccountType != 2 }" @click="selectCurTable(2)">
          <div class="gg item">
            <div class="num">{{ model.AdvancePaymentBalance }}</div>
            <div>（货款）预收余额(元)</div>
          </div>
          <div class="triangle" v-if="queryParam && queryParam.AccountType == 2"></div>
        </a-col>
        <a-col :span="5" class="amount" :class="{ cursor: queryParam && queryParam.AccountType != 3 }" @click="selectCurTable(3)">
          <div class="item">
            <div class="num">{{ model.PayableBalance }}</div>
            <div>（货款）应付余额(元)</div>
          </div>
          <div class="triangle" v-if="queryParam && queryParam.AccountType == 3"></div>
        </a-col>
        <a-col :span="5" class="amount" :class="{ cursor: queryParam && queryParam.AccountType != 4 }" @click="selectCurTable(4)">
          <div class="item">
            <div class="num">{{ model.PrepaymentBalance }}</div>
            <div>（货款）预付余额(元)</div>
          </div>
          <div class="triangle" v-if="queryParam && queryParam.AccountType == 4"></div>
        </a-col>
        <a-col :span="5" class="amount" :class="{ cursor: queryParam && queryParam.AccountType != 5 }" @click="selectCurTable(5)">
          <div class="item">
            <div class="num">{{ model.RebateReceivableBalance }}</div>
            <div>（返利）应收余额(元)</div>
          </div>
          <div class="triangle" v-if="queryParam && queryParam.AccountType == 5"></div>
        </a-col>
      </a-row>
    </div>
    <a-card :title="tableTitle">
      <!-- 列表 -->
      <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab :tab="tab" :queryParam="queryParam" isHandleTableChange @handleTableChange="handleTableChange" :columns="columns" :isTableInitData="isTableInitData" />
    </a-card>
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'PersonalAccountInfo',
  mixins: [SimpleMixin],
  data() {
    return {
      searchItems: [
        {
          name: '时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['BusinessBeginTime', 'BusinessEndTime'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期']
        },
        {
          name: '摘要类型',
          type: 'selectBasicLink',
          value: '',
          vModel: 'BusinessEventType',
          dictCode: 'EnumBusinessEventType',
          defaultVal: '',
          placeholder: '请选择'
        },
        { name: '企业名称', type: 'input', value: '', key: 'Payer', defaultVal: '', placeholder: '请输入' },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      tableTitle: '',
      columns: [
        {
          title: '时间',
          dataIndex: 'BusinessTime',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '摘要类型',
          dataIndex: 'BusinessEventTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '企业名称',
          dataIndex: 'Payer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '金额',
          dataIndex: 'Amount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '余额',
          dataIndex: 'Balance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '流水号',
          dataIndex: 'SeriesNumber',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      queryParam: {
        AccountType: 1,
        ContrastCustomerId: '',
      },
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36011',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/AccountRunning/GetUserListAsync',
      },
      info: undefined,
      model: undefined,
    }
  },
  created() { },
  mounted() {
    if (this.$route.query) {
      this.info = JSON.parse(this.$route.query.info)
      this.queryParam = {
        AccountType: 1,
        AccountHolderId: this.info.AccountHolderId || '',
        ContrastCustomerId: this.info.ContrastCustomerId || '',
      }
      this.tableTitle = '应收明细'
      this.loadDatasFun(1)
    }
  },
  methods: {
    loadDatasFun(page, queryParam) {
      if (queryParam) {
        this.queryParam = queryParam
      }
      this.$refs.table.loadDatas(page, this.queryParam, (data, count) => {
        this.$refs.table.ipagination.total = count
        this.model = data.Balance
        this.$refs.table.dataSource = data.RunningList
      })
    },
    handleTableChange() {
      this.loadDatasFun()
    },
    selectCurTable(cur) {
      if (this.queryParam.AccountType == cur) {
        return false
      }
      let queryParam = this.$refs.SimpleSearchArea.queryParam
      queryParam.AccountHolderId = this.info.AccountHolderId || ''
      queryParam.ContrastCustomerId = this.info.ContrastCustomerId || ''
      queryParam.AccountType = cur
      this.queryParam = queryParam
      switch (cur) {
        case 1:
          this.tableTitle = '应收明细'
          break
        case 2:
          this.tableTitle = '预收明细'
          break
        case 3:
          this.tableTitle = '应付明细'
          break
        case 4:
          this.tableTitle = '预付明细'
          break
        case 5:
          this.tableTitle = '应收明细'
          break
      }
      this.loadDatasFun(1)
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
        this.queryParam = {
          AccountHolderId: this.info.AccountHolderId || '',
          ContrastCustomerId: this.info.ContrastCustomerId || '',
          AccountType: this.queryParam.AccountType
        }
        queryParam = this.queryParam
      }
      queryParam.ContrastCustomerId = this.info.ContrastCustomerId
      queryParam.AccountHolderId = this.info.AccountHolderId
      queryParam.AccountType = this.queryParam.AccountType
      this.loadDatasFun(1, queryParam)
    },
  }

}
</script>

<style lang="scss" scoped>
.amount-con {
  background-color: #fff;
  margin-bottom: 16px;
  border: 1px solid #e8e8e8;
  padding: 24px 0 24px 24px;
}
.amount .item {
  background-color: #ebeaea;
  padding: 20px;
  font-size: 16px;
  text-align: center;
  color: red;
  margin-right: 24px;
}
.amount {
  position: relative;
}
.amount .triangle {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  bottom: -16px;
  width: 0;
  height: 0;
  border-top: 16px solid #ebeaea;
  border-right: 20px solid transparent;
  border-left: 20px solid transparent;
}
.amount-con .cursor {
  cursor: pointer;
}

.amount .gg {
  color: green;
}

.amount .num {
  font-size: 20px;
  font-weight: 600;
}
.ant-col-5 {
  width: 20%;
}
</style>
