<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate" />
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "BusinessAccountList",
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '企业名称', type: 'input', value: '', key: 'CompanyName', defaultVal: '', placeholder: '请输入' },
        { name: '创建人', type: 'input', value: '', key: 'ResponsiblePersonName', defaultVal: '', placeholder: '请输入' },
        {
          name: '所属部门', type: 'selectTreeViewLink', vModel: 'DepartmentId', defaultVal: '', disabled: false, placeholder: '请选择', dataKey: {
            value: 'Id',
            label: 'Name',
            selectableKey: 'Id',
            children: 'ChildrenDepartments'
          }, url: '/{v}/UserPermission/Department/List', httpHead: 'P36001'
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '导出', type: '', icon: 'download', key: '导出', id: 'accdc91c-d162-4149-a6e1-9698a9dafd7a' },
        ],
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
        rowKey: 'AccountHolderId'
      },
      columns: [
        {
          title: '企业名称',
          dataIndex: 'MerchantName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收余额',
          dataIndex: 'ReceivableBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '预收余额',
          dataIndex: 'AdvancePaymentBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '应付余额',
          dataIndex: 'PayableBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '预付余额',
          dataIndex: 'PrepaymentBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: "right",
          actionBtn: [{ name: '明细', icon: '', id: '07a15dfe-cd40-4a20-adb2-5c66d9da0261' },],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {

      },
      isTableInitData: true,//是否自动加载
      linkHttpHead: 'P36011',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/Account/GetCompanyListAsync',
        exportUrl: '/{v}/Account/ExportCompanyListAsync'
      },
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '明细') {
        let recordObj = {
          AccountHolderId: record.AccountHolderId,
        }
        this.onDetailClick('BusinessAccountInfo', { info: JSON.stringify(recordObj) })
      } else if (type == '导出') {
        this.handleExportXls(
          '企业账户列表',
          'Get',
          this.linkUrl.exportUrl,
          null,
          this.linkHttpHead,
          this.$refs.SimpleSearchArea.queryParam
        )
      }
    },

  }

}
</script>

<style>
</style>