<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate" />
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "PersonalAccountList",
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        {
          name: '姓名',
          placeholder: '',
          type: 'input',
          key: 'Name'
        },
        {
          name: '手机号',
          placeholder: '',
          type: 'input',
          key: 'Phone'
        },
        {
          name: '人员类型',
          type: 'selectBasicLink',
          dataKey: { name: 'ItemDescription', value: 'ItemValue' },
          value: '',
          vModel: 'Type',
          dictCode: 'EnumUserType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '所属部门', type: 'selectTreeViewLink', vModel: 'DepartmentId', defaultVal: '', disabled: false, placeholder: '请选择', dataKey: {
            value: 'Id',
            label: 'Name',
            selectableKey: 'Id',
            children: 'ChildrenDepartments'
          }, url: '/{v}/UserPermission/Department/List', httpHead: 'P36001'
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [],
        rowKey: 'AccountHolderId'
      },
      columns: [
        {
          title: '姓名',
          dataIndex: 'Name',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '手机号',
          dataIndex: 'Phone',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '人员类型',
          dataIndex: 'TypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '所在部门',
          dataIndex: 'DepartmentName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '（货款）应收余额',
          dataIndex: 'ReceivableBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '（货款）预收余额',
          dataIndex: 'AdvancePaymentBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '（货款）应付余额',
          dataIndex: 'PayableBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '（货款）预付款余额',
          dataIndex: 'PrepaymentBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '（返利）应收余额',
          dataIndex: 'RebateReceivableBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: "right",
          actionBtn: [{ name: '详情', icon: '', id: 'e981e6dc-c498-4933-a08f-92a0d1279c61' },],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {

      },
      isTableInitData: true,//是否自动加载
      linkHttpHead: 'P36011',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/Account/GetUserListAsync',
      },
    }
  },
  created() { },
  mounted() { },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '详情') {
        let recordObj = {
          AccountHolderId: record.AccountHolderId,
          ContrastCustomerId: record.ContrastCustomerId,
          Name: record.Name,
          Phone: record.Phone,
          DepartmentName: record.DepartmentName,
          TypeStr: record.TypeStr,
        }
        this.onDetailClick('PersonalAccountInfo', { info: JSON.stringify(recordObj) })
      }
    },

  }

}
</script>

<style>
</style>