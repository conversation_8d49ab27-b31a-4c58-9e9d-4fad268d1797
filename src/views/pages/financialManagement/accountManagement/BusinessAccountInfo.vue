<template>
  <a-row>
    <a-card style="margin-bottom: 16px;">
      <div style="display: flex;justify-content: space-between;">
        <span style="color: rgba(0, 0, 0, 0.85);font-weight: bold;font-size: 16px;">企业账户详情</span>
        <a-button @click="goBack(true,true)">返回</a-button>
      </div>
    </a-card>
    <div class="amount-con">
      <!-- 搜索 -->
      <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
      <a-row v-if="model">
        <a-col :span="6" class="amount" :class="{'cursor':queryParam&&queryParam.AccountType!=1}" @click="selectCurTable(1)">
          <div class="gg item">
            <div>应收余额</div>
            <div class="num">¥{{ model.ReceivableBalance }}</div>
          </div>
          <div class="triangle" v-if="queryParam&&queryParam.AccountType==1"></div>
        </a-col>
        <a-col :span="6" class="amount" :class="{'cursor':queryParam&&queryParam.AccountType!=2}" @click="selectCurTable(2)">
          <div class="gg item">
            <div>预收余额</div>
            <div class="num">¥{{ model.AdvancePaymentBalance }}</div>
          </div>
          <div class="triangle" v-if="queryParam&&queryParam.AccountType==2"></div>
        </a-col>
        <a-col :span="6" class="amount" :class="{'cursor':queryParam&&queryParam.AccountType!=3}" @click="selectCurTable(3)">
          <div class="item">
            <div>应付余额</div>
            <div class="num">¥{{ model.PayableBalance }}</div>
          </div>
          <div class="triangle" v-if="queryParam&&queryParam.AccountType==3"></div>
        </a-col>
        <a-col :span="6" class="amount" :class="{'cursor':queryParam&&queryParam.AccountType!=4}" @click="selectCurTable(4)">
          <div class="item">
            <div>预付余额</div>
            <div class="num">¥{{ model.PrepaymentBalance }}</div>
          </div>
          <div class="triangle" v-if="queryParam&&queryParam.AccountType==4"></div>
        </a-col>
      </a-row>
    </div>
    <a-card :title="tableTitle">
      <!-- 列表 -->
      <SimpleTable ref="table" @operate="operate" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab :tab="tab" :queryParam="queryParam" isHandleTableChange @handleTableChange="handleTableChange" :columns="columns" :isTableInitData="isTableInitData" />
    </a-card>
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "BusinessAccountInfo",
  mixins: [ListMixin, SimpleMixin],
  data() {
    return {
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '单据编号', type: 'input', value: '', key: 'BusinessCode', defaultVal: '', placeholder: '请输入' },
        { name: '对应订单编号', type: 'input', value: '', key: 'OrderCode', defaultVal: '', placeholder: '请输入' },
        {
          name: '摘要类型',
          type: 'selectBasicLink',
          value: '',
          vModel: 'BusinessEventType',
          dictCode: 'EnumBusinessEventType',
          notOption: [51, 52, 60, 61, 64, 65, 66, 67, 68, 69, 70, 71],
          defaultVal: '',
          placeholder: '请选择'
        },
        {
          name: '订单付款方式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PaymentMode',
          dictCode: 'EnumPaymentMode',
          defaultVal: '',
          placeholder: '请选择'
        },
        {
          name: '发生日期',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['BusinessBeginTime', 'BusinessEndTime'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期']
        },
        { name: '创建人', type: 'input', value: '', key: 'ResponsiblePersonName', defaultVal: '', placeholder: '请输入' },
        {
          name: '所属部门', type: 'selectTreeViewLink', vModel: 'DepartmentId', defaultVal: '', disabled: false, placeholder: '请选择', dataKey: {
            value: 'Id',
            label: 'Name',
            selectableKey: 'Id',
            children: 'ChildrenDepartments'
          }, url: '/{v}/UserPermission/Department/List', httpHead: 'P36001'
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        hintArray: [],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
        operateBtn: [
          { name: '导出', type: '', icon: 'download', key: '导出', id: 'd4de8599-0a53-4715-8ebe-4e716be46d18' }
        ],
      },
      columns: [],
      columns1: [
        {
          title: '单据编号',
          dataIndex: 'BusinessCode',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'ResponsiblePersonName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '所属部门',
          dataIndex: 'DepartmentName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'MainOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单付款方式',
          dataIndex: 'PaymentModeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BusinessTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '摘要类型',
          dataIndex: 'BusinessEventTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收金额',
          dataIndex: 'Amount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '应收余额',
          dataIndex: 'Balance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
      columns2: [
        {
          title: '单据编号',
          dataIndex: 'BusinessCode',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'ResponsiblePersonName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '所属部门',
          dataIndex: 'DepartmentName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'MainOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单付款方式',
          dataIndex: 'PaymentModeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BusinessTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '摘要类型',
          dataIndex: 'BusinessEventTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '预收金额',
          dataIndex: 'Amount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '预收余额',
          dataIndex: 'Balance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
      columns3: [
        {
          title: '单据编号',
          dataIndex: 'BusinessCode',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'ResponsiblePersonName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '所属部门',
          dataIndex: 'DepartmentName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'MainOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单付款方式',
          dataIndex: 'PaymentModeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BusinessTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '摘要类型',
          dataIndex: 'BusinessEventTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应付金额',
          dataIndex: 'Amount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '应付余额',
          dataIndex: 'Balance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
      columns4: [
        {
          title: '单据编号',
          dataIndex: 'BusinessCode',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'ResponsiblePersonName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '所属部门',
          dataIndex: 'DepartmentName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'MainOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单付款方式',
          dataIndex: 'PaymentModeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BusinessTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '摘要类型',
          dataIndex: 'BusinessEventTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '预付金额',
          dataIndex: 'Amount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '预付余额',
          dataIndex: 'Balance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
      tableTitle: '',
      queryParam: {
        AccountType: 1,
        AccountHolderId: '',
      },
      isTableInitData: false,//是否自动加载
      linkHttpHead: 'P36011',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '',
        lists: '/{v}/AccountRunning/GetCompanyListAsync',
        exportUrl: '/{v}/AccountRunning/ExportCompanyListAsync'
      },
      info: undefined,
      model: undefined,
    }
  },
  created() {

  },
  mounted() {
    if (this.$route.query) {
      this.info = JSON.parse(this.$route.query.info);
      this.queryParam = {
        AccountType: 1,
        AccountHolderId: this.info.AccountHolderId || '',
      }
      this.tableTitle = '应收明细'
      this.columns = this.columns1;
      this.linkUrl.list = this.linkUrl.lists
      this.loadDatasFun(1)
    }
  },
  methods: {
    loadDatasFun(page, queryParam) {
      if (queryParam) {
        this.queryParam = queryParam
      }
      this.$refs.table.loadDatas(page, this.queryParam, (data, count) => {
        this.$refs.table.ipagination.total = count
        this.model = data.Balance
        this.$refs.table.dataSource = data.RunningList
      });
    },
    selectCurTable(cur) {
      if (this.queryParam.AccountType == cur) {
        return false
      }
      let queryParam = this.$refs.SimpleSearchArea.queryParam
      queryParam.AccountHolderId = this.info.AccountHolderId || ''
      queryParam.AccountType = cur
      this.queryParam = queryParam
      switch (cur) {
        case 1: this.columns = this.columns1; this.tableTitle = '应收明细'; break;
        case 2: this.columns = this.columns2; this.tableTitle = '预收明细'; break;
        case 3: this.columns = this.columns3; this.tableTitle = '应付明细'; break;
        case 4: this.columns = this.columns4; this.tableTitle = '预付明细'; break;
      }
      this.loadDatasFun(1)
    },
    handleTableChange() {
      this.loadDatasFun()
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
        this.queryParam = {
          AccountHolderId: this.info.AccountHolderId || '',
          AccountType: this.queryParam.AccountType
        }
        queryParam = this.queryParam
      }
      queryParam.AccountHolderId = this.info.AccountHolderId
      queryParam.AccountType = this.queryParam.AccountType
      this.loadDatasFun(1, queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '导出') {
        let searchObj = {
          ...this.queryParam,
          ...this.$refs.SimpleSearchArea.queryParam
        }
        this.handleExportXls(
          '企业账户明细_' + this.tableTitle,
          'Get',
          this.linkUrl.exportUrl,
          null,
          this.linkHttpHead,
          searchObj
        )
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.amount-con {
  background-color: #fff;
  margin-bottom: 16px;
  border: 1px solid #e8e8e8;
  padding: 24px 0 24px 24px;
}
.amount .item {
  background-color: #ebeaea;
  padding: 20px;
  font-size: 16px;
  text-align: center;
  color: red;
  margin-right: 24px;
}
.amount {
  position: relative;
}
.amount .triangle {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  bottom: -16px;
  width: 0;
  height: 0;
  border-top: 16px solid #ebeaea;
  border-right: 20px solid transparent;
  border-left: 20px solid transparent;
}
.amount-con .cursor {
  cursor: pointer;
}

.amount .gg {
  color: green;
}

.amount .num {
  font-size: 20px;
  font-weight: 600;
}
</style>