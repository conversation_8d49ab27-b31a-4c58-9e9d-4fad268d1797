<template>
  <a-modal :title="title" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="开户名：" prop="AccountName">
                <a-input placeholder="请输入开户名" v-model="model.AccountName" style="width: 100%" />
              </a-form-model-item>
              <a-form-model-item label="开户银行：" prop="BankName">
                <a-input placeholder="请输入开户银行" v-model="model.BankName" style="width: 100%" />
              </a-form-model-item>
              <a-form-model-item label="银行账号：" prop="BankNo">
                <a-input placeholder="请输入银行账号" v-model="model.BankNo" style="width: 100%" />
              </a-form-model-item>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">确定</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "PlatformPayAccountAddModal",
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      title: "新增角色",
      visible: false,
      isEdit: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      httpHead: 'P36012',
      url: {
        add: "/{v}/CashReceiptNew/CreateReceiptAccount",
        update: "/{v}/CashReceiptNew/EditReceiptAccount",
      },
    };
  },
  computed: {
    rules() {
      return {
        AccountName: [{ required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 80, '开户名') }],
        BankName: [{ required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 60, '开户银行') }],
        BankNo: [
          {
            required: true,
            validator: (rule, value, callback) =>
              this.checkIntNumber(rule, value, (str) => {
                if (str != undefined) {
                  callback(str)
                  return
                }
                this.validStrMaxLength(rule, value, callback, 20,'银行账号')
              }),
          },
        ],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add() {
      this.title = '新增平台收款账户'
      this.model = {}
      this.isEdit = false;
      this.visible = true;
    },
    edit(record) {
      this.title = '编辑平台收款账户'
      this.model = Object.assign({}, record);
      this.isEdit = true;
      this.visible = true;
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          let formData = {}
          let url = ''
          if (this.isEdit) {
            url = this.url.update
            formData = {
              Id: this.model.Id,
              AccountName: this.model.AccountName,
              BankName: this.model.BankName,
              BankNo: this.model.BankNo.toString(),
              IsValid: true,
            }
          } else {
            url = this.url.add
            formData = {
              AccountName: this.model.AccountName,
              BankName: this.model.BankName,
              BankNo: this.model.BankNo.toString(),
              IsValid: true,
            }
          }
          postAction(url, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
