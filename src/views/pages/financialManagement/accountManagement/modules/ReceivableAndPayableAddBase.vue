<template>
  <div>
    <div style="background: #fbe6e9; padding: 10px 16px;margin-bottom: 10px; color: red" v-if="model.AuditStatus == 3 && model.AuditOpinion" :md="24">
      <span>驳回原因：{{ model.AuditOpinion || '' }}</span>
    </div>
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="企业名称：" prop="MerchantName">
                <SingleInputSearchView placeholder="请输入客户名称/客户编号" width="100%" :httpParams="{
                        pageIndex: 1,
                        pageSize: 100,
                        IsValid: true,
                      }" keyWord="KeyWord" httpHead="P36011" Url="/{v}/Account/GetCompanyListAsync" :dataKey="{name:'MerchantName',value:'MerchantId',nameList: []}" :value="model.MerchantId" :name="model.MerchantName" :disabled="!isEdit" :isAbsolute="true" @clear="
                        () => {
                          model.MerchantId = ''
                          model.MerchantName = ''
                          companyItem = {}
                        }
                      " @change="changeCompany" />
              </a-form-model-item>
              <a-form-model-item prop="dataSource">
                <a-table style="width:100%;" :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ x: '100%', y: dataSource.length === 0 ? false :'450px' }">
                  <!-- 字符串超长截取省略号显示-->
                  <template slot="component" slot-scope="text">
                    <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                    <j-ellipsis :value="text" v-else />
                  </template>
                  <template slot="price" slot-scope="text">
                    <j-ellipsis :value="text?('¥'+text):'0'" v-if="typeof text == 'number'" />
                    <j-ellipsis :value="text" v-else />
                  </template>
                </a-table>
              </a-form-model-item>
              <a-form-model-item label="调整类型：" prop="AccountType" v-if="model.MerchantId && dataSource.length > 0">
                <a-spin :spinning="JSON.stringify(companyItem) === '{}'?true:false" tip="Loading...">
                  <a-radio-group v-model="model.AccountType" :disabled="!isEdit" @change="onRadioChange">
                    <a-radio :value="1" v-if="(!companyItem.RelatedMerchantId && companyItem.MerchantType==2) || companyItem.RelatedMerchantId">
                      应收余额
                    </a-radio>
                    <a-radio :value="3" v-if="(!companyItem.RelatedMerchantId && companyItem.MerchantType==1) || companyItem.RelatedMerchantId">
                      应付余额
                    </a-radio>
                  </a-radio-group>
                </a-spin>
              </a-form-model-item>
              <a-form-model-item :label="(model.AccountType==1?'应收':(model.AccountType==3?'应付':''))+'调整金额：'" v-if="model.MerchantId && dataSource.length > 0 && model.AccountType" prop="AdjustmentAmount">
                <a-input-number placeholder="请输入调整金额" v-model="model.AdjustmentAmount" :disabled="!isEdit" :max="*********.99" :precision="2" :parser="(value) => value.replace(/[^\d.-]/g,'')" style="width: 100%" @change="changeMoney" />
                <div style="margin-top:3px;">调整后预估{{(model.AccountType==1?'应收':(model.AccountType==3?'应付':''))}}余额：<span style="color:red;">{{adjustedAmount || 0}}</span></div>
              </a-form-model-item>
              <a-form-model-item label="备注：">
                <a-textarea placeholder="请输入备注信息" v-model="model.Note" :rows="4" :disabled="!isEdit" :maxLength="200" style="width: 100%" />
              </a-form-model-item>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
  </div>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from "@/api/manage";
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'ReceivableAndPayableAddBase',
  title: '调整单详情',
  components: {
    JEllipsis,
  },
  mixins: [ListMixin, EditMixin],
  props: {
    // 是否是编辑模式
    isEdit: {
      type: Boolean,
      default: false
    },
    infoData: {
      type: Object,
      default: null
    },
  },
  data() {
    return {
      confirmLoading: false,
      model: {

      },
      columns: [
        {
          title: '应收余额',
          dataIndex: 'OldReceivableBalance',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '预收余额',
          dataIndex: 'OldAdvancePaymentBalance',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '应付余额',
          dataIndex: 'OldPayableBalance',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '预付余额',
          dataIndex: 'OldPrepaymentBalance',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
      adjustedAmount: 0,
      companyItem: {},
      dataSource: [],
    }
  },
  computed: {
    rules() {
      return {
        MerchantName: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.MerchantId) {
                callback('请选择企业!')
              } else {
                callback()
              }
            }
          },
        ],
        dataSource: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.dataSource.length === 0) {
                callback('列表数据不能为空!')
              } else {
                callback()
              }
            }
          },
        ],
        AccountType: [{ required: true, message: '请选择调整类型!' }],
        AdjustmentAmount: [{
          required: true,
          validator: (rule, value, callback) => {
            if (!this.model.AccountType) {
              callback('请先选择调整类型再调整!')
            } else if (!this.model.AdjustmentAmount) {
              let text = this.model.AccountType == 1 ? '应收' : '应付'
              callback(text + '调整金额不能为0或空值!')
            } else {
              callback()
            }
          }
        }],
      };
    },
  },
  watch: {
    infoData(val) {
      this.model = this.infoData
      this.dataSource = [
        {
          OldReceivableBalance: this.infoData.OldReceivableBalance,
          OldAdvancePaymentBalance: this.infoData.OldAdvancePaymentBalance,
          OldPayableBalance: this.infoData.OldPayableBalance,
          OldPrepaymentBalance: this.infoData.OldPrepaymentBalance,
        }
      ]
      this.adjustedAmount = this.infoData.NewAdjustmentAmount
      this.getCompanyList()
    }
  },
  methods: {
    onRadioChange() {
      this.clearMoney()
    },
    changeCompany(val, name, item) {
      this.model.MerchantId = val
      this.model.MerchantName = name
      this.companyItem = item || {}
      if (item) {
        this.dataSource = [
          {
            OldReceivableBalance: item.ReceivableBalance,
            OldAdvancePaymentBalance: item.AdvancePaymentBalance,
            OldPayableBalance: item.PayableBalance,
            OldPrepaymentBalance: item.PrepaymentBalance,
          }
        ]
      } else {
        this.dataSource = []
        this.companyItem = {}
      }
      this.clearMoney(1)
      this.adjustedAmount = 0
      this.$set(this.model, 'AccountType', null)
      this.$forceUpdate()
    },
    getCompanyList() {
      let formData = {
        KeyWord: this.model.MerchantName,
      }
      getAction('/{v}/Account/GetCompanyListAsync', formData, 'P36011').then((res) => {
        if (res.IsSuccess) {
          let data = res.Data || []
          if (data && data.length > 0) {
            let merchantItem = data.find(item => {
              return item.MerchantName === this.model.MerchantName
            })
            if (merchantItem) {
              this.companyItem = merchantItem || {}
            }
            if (this.isEdit) {
              this.dataSource = [
                {
                  OldReceivableBalance: merchantItem.ReceivableBalance,
                  OldAdvancePaymentBalance: merchantItem.AdvancePaymentBalance,
                  OldPayableBalance: merchantItem.PayableBalance,
                  OldPrepaymentBalance: merchantItem.PrepaymentBalance,
                }
              ]
              // 编辑模式再次计算金额
              let AdjustmentAmountNum = typeof (this.model.AdjustmentAmount) != 'number' ? 0 : this.model.AdjustmentAmount
              if (this.model.AccountType == 1) {
                // 应收余额
                this.adjustedAmount = this.numMath(AdjustmentAmountNum, this.dataSource[0].OldReceivableBalance, '+', 2) //运算
              } else {
                // 应付余额
                this.adjustedAmount = this.numMath(AdjustmentAmountNum, this.dataSource[0].OldPayableBalance, '+', 2)
              }
            }

          }
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {

      });
    },
    changeMoney(e) {
      if (this.dataSource.length === 0) {
        return
      }
      if (!this.model.AccountType) {
        return
      }
      let AdjustmentAmountNum = typeof (this.model.AdjustmentAmount) != 'number' ? 0 : this.model.AdjustmentAmount
      if (this.model.AccountType == 1) {
        // 应收余额
        this.adjustedAmount = this.numMath(AdjustmentAmountNum, this.dataSource[0].OldReceivableBalance, '+', 2) //运算
      } else {
        // 应付余额
        this.adjustedAmount = this.numMath(AdjustmentAmountNum, this.dataSource[0].OldPayableBalance, '+', 2)
      }
    },
    // 清空金额
    clearMoney(type) {
      if (this.model.AdjustmentAmount) {
        this.model.AdjustmentAmount = null
      }
      this.adjustedAmount = 0
      let array = []
      if (type == 1) {
        array = ['AdjustmentAmount', 'dataSource']
      } else {
        array = ['AdjustmentAmount']
      }
      this.$refs.form.clearValidate(array)
    },
    validate(callback) {
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          this.confirmLoading = true;
          if (this.dataSource.length === 0) {
            this.message.error('列表数据不能为空!');
            return
          }
          this.model.dataSource = this.dataSource || []
          callback && callback(this.closeLoading, this.model)
        }
      });
    },
    closeLoading() {
      this.confirmLoading = false;
    },

  }
}
</script>

<style>
</style>