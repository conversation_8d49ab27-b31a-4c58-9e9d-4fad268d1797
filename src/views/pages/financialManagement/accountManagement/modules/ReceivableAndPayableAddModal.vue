<template>
  <a-modal :title="title" :width="800" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <ReceivableAndPayableAddBase ref="ReceivableAndPayableAddBase" :infoData="infoData" :isEdit="true" />
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">
        取消
      </a-button>
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存并提交审核</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "ReceivableAndPayableAddModal",
  title: '新增调整单',
  data() {
    return {
      title: "新增调整单",
      visible: false,
      isEdit: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      infoData: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      httpHead: 'P36011',
      url: {
        add: "/{v}/Adjustment/CreateAsync",
        update: "/{v}/Adjustment/EditAsync",
        info: '/{v}/Adjustment/GetInfoAsync',
      },
    };
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add() {
      this.title = '新增调整单'
      this.model = {}
      this.isEdit = false;
      this.visible = true;
    },
    edit(record) {
      this.title = '编辑调整单'
      this.model = Object.assign({}, record);
      this.isEdit = true;
      this.visible = true;
      this.$nextTick(() => {
        this.getInfo()
      })
    },
    getInfo() {
      this.confirmLoading = true
      this.$refs.ReceivableAndPayableAddBase.confirmLoading = true
      let formData = {
        id: this.model.Id,
      }
      getAction(this.url.info, formData, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          this.infoData = res.Data || {}
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {
        this.confirmLoading = false
        this.$refs.ReceivableAndPayableAddBase.confirmLoading = false
      });
    },
    // 确定
    handleOk() {
      // 触发表单验证
      const that = this;
      this.$refs.ReceivableAndPayableAddBase.validate((closeLoading, data) => {
        that.confirmLoading = true;
        this.model = data || {}
        if (this.model.dataSource.length === 0) {
          this.message.error('列表数据不能为空!');
          return
        }
        let formData = {}
        let url = ''
        if (this.isEdit) {
          url = this.url.update
          formData = {
            AccountAdjustmentId: this.model.Id,
            MerchantId: this.model.MerchantId,
            AccountType: this.model.AccountType,
            AdjustmentAmount: this.model.AdjustmentAmount,
            OldReceivableBalance: this.model.dataSource[0].OldReceivableBalance,
            OldAdvancePaymentBalance: this.model.dataSource[0].OldAdvancePaymentBalance,
            OldPayableBalance: this.model.dataSource[0].OldPayableBalance,
            OldPrepaymentBalance: this.model.dataSource[0].OldPrepaymentBalance,
            Note: this.model.Note,
          }
        } else {
          url = this.url.add
          formData = {
            MerchantId: this.model.MerchantId,
            AccountType: this.model.AccountType,
            AdjustmentAmount: this.model.AdjustmentAmount,
            OldReceivableBalance: this.model.dataSource[0].OldReceivableBalance,
            OldAdvancePaymentBalance: this.model.dataSource[0].OldAdvancePaymentBalance,
            OldPayableBalance: this.model.dataSource[0].OldPayableBalance,
            OldPrepaymentBalance: this.model.dataSource[0].OldPrepaymentBalance,
            Note: this.model.Note,
          }
        }
        postAction(url, formData, this.httpHead).then((res) => {
          if (res.IsSuccess) {
            that.$message.success("操作成功");
            that.$emit("ok");
            that.close();
          } else {
            that.$message.warning(res.Msg);
          }
        }).finally(() => {
          that.confirmLoading = false;
          closeLoading && closeLoading()
        });
      })

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
