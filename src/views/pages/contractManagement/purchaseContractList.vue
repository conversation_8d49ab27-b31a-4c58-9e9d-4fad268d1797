<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab">
      <!-- 自定义插槽 -->
      <!-- 原件照片数量 -->
      <span slot="FileCount" slot-scope="{ text, record }">
        <a @click="showAuditOpinion(text, record)">{{ text || 0 }}</a>
      </span>
      <!-- 账期 -->
      <span slot="SettlementPeriod" slot-scope="{ text }">
        <j-ellipsis v-if="text" :value="text + '个月'" :length="40" />
      </span>
      <!-- 关联订单数量 -->
      <span slot="OrderCount" slot-scope="{ text, record }">
        <a @click="showOrderCount(text, record)">{{ text }}</a>
      </span>
      <!-- 合同效期 -->
      <span slot="ValidStartTime" slot-scope="{ text, record }">
        <span v-if="!text && !record['ValidEndTime']"></span>
        <j-ellipsis v-else :value="`${text ? text.substring(0, 11) : ''}~${
            record['ValidEndTime'] ? record['ValidEndTime'].substring(0, 11) : ''
          }`" :length="40" />
      </span>
    </SimpleTable>
    <!-- 新增 -->
    <PurchaseContractAddModal ref="PurchaseContractAddModal" @ok="modalOk"></PurchaseContractAddModal>
    <!-- 查看照片 -->
    <PurchaseContractPhotoModal ref="PurchaseContractPhotoModal"></PurchaseContractPhotoModal>
    <!-- 查看采购合同关联订单 -->
    <PurchaseContractOrderModal ref="PurchaseContractOrderModal"></PurchaseContractOrderModal>
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'purchaseContractList',
  components: { JEllipsis },
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '合同编号', type: 'input', value: '', key: 'ContractNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '供应商',
          type: 'input',
          value: '',
          key: 'SupplierNameKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码',
        },
        {
          name: '合同类型',
          type: 'select',
          value: '',
          key: 'ContractType',
          defaultVal: [
            { title: '年度合同', value: 1 },
            { title: '单批次合同', value: 2 },
          ],
          placeholder: '请选择',
        },
        {
          name: '结算方式',
          type: 'select',
          value: '',
          key: 'SettlementMethod',
          defaultVal: [
            { title: '预付款', value: 1 },
            { title: '月结', value: 2 },
            { title: '货到付款', value: 3 },
          ],
          placeholder: '请选择',
        },
        {
          name: '合同效期',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['ValidStartTime', 'ValidEndTime'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        { name: '关联订单', type: 'input', value: '', key: 'OrderNoeKey', defaultVal: '', placeholder: '请输入编号' },
        {
          name: '合同状态',
          type: 'select',
          value: '',
          key: 'ContractStatus',
          defaultVal: [
            { title: '未生效', value: 1 },
            { title: '生效中', value: 2 },
            { title: '已结束', value: 3 },
          ],
          placeholder: '请选择',
        },
        { name: '责任人', type: 'input', value: '', key: 'OwnerNameKey', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增', type: 'primary', icon: 'plus', key: 'add', id: '3035c2cc-15b0-4196-b6ee-4bd141b35377' },
        ],
        hintArray: [],
        sortArray: ['FileCount', 'OrderCount', 'SettlementPeriod', 'ValidStartTime'],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '合同编号',
          dataIndex: 'ContractNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '合同类型',
          dataIndex: 'ContractTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '合同金额(元)',
          dataIndex: 'ContractAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '合同效期',
          dataIndex: 'ValidStartTime',
          width: 200,
          ellipsis: true,
          sorter: true,
          scopedSlots: { customRender: 'ValidStartTime' },
        },
        {
          title: '结算方式',
          dataIndex: 'SettlementMethodStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '结算账期',
          dataIndex: 'SettlementPeriod',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'SettlementPeriod' },
        },
        
        {
          title: '原件照片数量',
          dataIndex: 'FileCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'FileCount' },
        },
        {
          title: '关联订单数量',
          dataIndex: 'OrderCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'OrderCount' },
        },
        {
          title: '合同状态',
          dataIndex: 'ContractStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtn: [
            {
              name: '编辑',
              icon: '',
              id: '6c926986-311f-4022-86e0-1d932c178d21',
              specialShowFuc: (record) => {
                return record.IsCanEdit == true
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36007',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/PurchaseContract/GetListAsync',
      },
    }
  },
  activated() {
    this.queryParam = this.$refs.SimpleSearchArea.queryParam
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    // 原件照片数量
    showAuditOpinion(text, record) {
      this.$refs.PurchaseContractPhotoModal.show(record)
    },
    showOrderCount(text, record) {
      this.$refs.PurchaseContractOrderModal.show(record)
    },
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        this.$refs.PurchaseContractAddModal.add()
      } else if (type == '编辑') {
        this.$refs.PurchaseContractAddModal.edit(record)
      }
    },
  },
}
</script>

<style></style>
