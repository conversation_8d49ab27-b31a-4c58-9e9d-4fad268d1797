<!--
 * @Description: 通用协议 新增/编辑
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: haolin.hu
 * @LastEditTime: 2025-05-09 14:03:49
-->

<template>
  <div>
    <div style="background: #fbe6e9; padding: 10px 16px; color: red" v-if="model.AuditStatus == 3 &&  model.AuditOpinion" :md="24">
      <span>驳回原因：{{ model.AuditOpinion || ' -- ' }}</span>
    </div>
    <div :style="{ width: '100%', border: '1px solid #e9e9e9', padding: '10px 16px', background: '#fff' }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model">
          <a-row>
            <a-row :gutter="10">
              <a-col :md="8">
                <a-form-model-item label="甲方：" prop="PartyFirstId">
                  <div style="display: flex;">
                    <SingleInputSearchView class="party-search" placeholder="请选择供应商/生产厂家" width="100%" :httpParams="{
                      pageIndex: 1,
                      pageSize: 200,
                      AuthBusiness:2,
                      IsValid: true,
                    }" keyWord="KeyWord"  httpHead="P36003" :Url="url.supplierList" :noShowList="true" :value="model.PartyFirstId" :dataKey="{ name: 'Name', value: 'Id' }" :name="model.PartyFirstName" :isAbsolute="true" @clear="
                      () => {
                        model.PartyFirstId = ''
                        model.PartyFirstName = ''
                      }
                    " @change="changeSearchInput" disabled/>
                    <a @click="searchDataInput">选择</a>
                  </div>
                </a-form-model-item>
              </a-col>

              <a-col :md="8">
                <a-form-model-item label="甲方类型：" prop="PartyFirstType">
                  <a-select v-model="model.PartyFirstType" disabled>
                    <a-radio :value="1"> 供应商 </a-radio>
                    <a-radio :value="2"> 生产厂家 </a-radio>
                  </a-select>
                </a-form-model-item>
              </a-col>

              <a-col :md="8">
                <a-form-model-item style="margin: 0 20px" label="是否按照品种配置：" prop="IsLimitGoods">
                  <a-radio-group v-model="model.IsLimitGoods" @change="(e) => onTypeChange(e, 'hsfs')">
                    <a-radio :value="true"> 是 </a-radio>
                    <a-radio :value="false"> 否 </a-radio>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>

              <a-col :md="24" v-if="model.IsLimitGoods">
                <a-form-model-item label="促销政策：" prop="PolicyList">
                  <div style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      border: 1px solid #e8e8e8;
                      border-bottom: none;
                      padding: 5px 10px;
                    ">
                    <div>已配政策</div>
                    <div>
                      <a-button style="margin-right:8px;" type="primary" @click="operate(null, 'importGoods')">导入商品</a-button>
                      <a-button type="primary" @click="operate(null, 'chooseVariety')">选择品种，并配置促销内容</a-button>
                    </div>
                  </div>
                  <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ x: '1200px', y:dataSource.length === 0 ? false : '450px' }">
                    <!-- 字符串超长截取省略号显示-->
                    <template slot="component" slot-scope="text">
                      <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                      <j-ellipsis :value="text" v-else />
                    </template>
                    <template slot="RebateAmount" slot-scope="text, record">
                      <a-input-number placeholder="请输入" v-model="record.RebateAmount" style="width: 100%" :min="0" :precision="2" :max="999999999.99" @change="handleInput(record)" />
                    </template>
                    <!-- 操作 -->
                    <span slot="action" slot-scope="text, record, index">
                      <!-- <a style="margin: 0 5px" @click="operate(record, 'copy', index)">复制</a>
                      <a style="margin: 0 5px" @click="operate(record, 'edit', index)">编辑</a> -->
                      <a style="margin: 0 5px" @click="operate(record, 'del', index)">移除</a>
                    </span>
                  </a-table>
                </a-form-model-item>
              </a-col>
              <a-col span="24" style="text-align: right; font-weight: bold; padding-right: 20px;color: red;" v-if="model.IsLimitGoods">合计返利金额： {{ total < 0 ? '-' : '' }}¥{{ total < 0 ? String(total).replace('-', '') : total }}</a-col>
              <a-col :md="24">
                <a-row :gutter="10">
                  <a-col :md="8" v-if="model.IsLimitGoods == false">
                    <a-form-model-item label="促销内容：" prop="PolicyContent">
                      <a-input placeholder="请输入" v-model="model.PolicyContent" />
                    </a-form-model-item>
                  </a-col>
                  <a-col :md="8" v-if="model.IsLimitGoods == false">
                    <a-form-model-item label="返利金额：" prop="RebateAmount">
                      <a-input-number placeholder="请输入" v-model="model.RebateAmount" style="width: 100%" :min="0" :precision="2" :max="999999999.99" />
                    </a-form-model-item>
                  </a-col>
                  <a-col :md="8">
                    <a-form-model-item label="返利时间：" prop="RebateTime">
                      <a-date-picker style="width: 100%" valueFormat="YYYY-MM-DD" v-model="model.RebateTime" @change="onTimeChange" />
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </a-col>
              <a-col :md="24">
                <a-form-model-item label="原件照片(请在甲方签字盖章后，拍照上传)：" prop="listImageUrl">
                  <MultiUpload :max="10" :images="listImageUrl" :uploadText="uploadText" :fileType="4" :bName="BName" :dir="Dir" :readOnly="false" :fileSize="50" @change="multiUploadChange" />
                  <span>注：只支持png、jpg、jpeg、bmp格式的图片</span>
                </a-form-model-item>
              </a-col>
            </a-row>

          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-affix :offset-bottom="10" class="affix pr16">
      <div style="background-color: white;  padding:20px 20px 10px 0">
        <a-button :disabled="confirmLoading" class="mr8" @click="handleOk(1)" type="primary">提交审核</a-button>
        <a-button :disabled="confirmLoading" class="mr8" @click="handleOk(2)">存为草稿</a-button>
        <a-button @click="goBack(true,true)">返回</a-button>
      </div>
    </a-affix>
    <!-- 表格选择新增促销政策 商品弹窗 -->
    <TableSelectDataModal ref="TableSelectGoodsDataModal" :selectTableData="selectTableData" showSelectedData @chooseData="chooseGoodsData"></TableSelectDataModal>
    <!-- 表格选择甲方弹窗 -->
    <TableSelectDataModal ref="TableSelectDataModal" type="radio" :selectTabData="selectTabData" @chooseData="chooseData"></TableSelectDataModal>
    <!-- 批量导入 导入商品 -->
   <BatchImportSimpleModal ref="iBatchImportGoodsModal" 
    :modalTitle="'通用协议商品导入'" 
    :tableColumns="goodsImportColumns" 
    :searchParamsList="searchImportInput" 
    :importConfig="goodsImportConfig" 
    :importUrl="`/v1/PurchaseAgreement/ImportTempPolicyGoodsListAsync?agreementType=4`" 
    importHttpHead='P36007' 
    @ok="(e)=>handleBatchImportModalOk(e,'goods')" />
  </div>
</template>

<script>
import Vue from 'vue'
import moment from 'moment'
import { USER_ID } from '@/store/mutation-types'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'

export default {
  title: '新增通用协议',
  name: 'purchaseGeneralAdd',
  mixins: [ListMixin],
  components: {
    JEllipsis,
  },
  data() {
    return {
      title: '通用协议',
      visible: false,
      isEdit: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      total: 0,
      goodsImportConfig: {
        templateFileName: '协议商品导入模板',//下载模板名字
        haveDataSourceKey:'ErpGoodsCode',
        ErrorShowKey: 'ErrorShow',//异常信息字段名称
        saveKey: 'BatchId',
        saveUrlType: 'GET',//保存接口请求方式
        saveUrl: '/{v}/PurchaseAgreement/GetTempPolicyGoodsDetailsListAsync',//保存接口
        listQueryKey: 'BatchId',//列表的时候需要用到的字段
        batchRemoveId: '',//批量移除id
        listRemoveType: 'DELETE',//列表删除接口请求方式
        listRemoveUrl: '/{v}/PurchaseAgreement/DeleteSpecifiedTempPolicyGoodsAsync',//列表删除 接口
        listRemoveKey: '',//列表删除 参数 key
        listHttpHead: 'P36007',
        listUrl: '/{v}/PurchaseAgreement/GetTempPolicyGoodsPageListAsync',//列表的请求接口
        listUrlType: 'GET',//列表接口请求方式
        queryParamStatusKey: 'Status',//列表查询 异常 正常 key
        noLoadData: true,//是否默认弹出时候不加载数据
        importResKey: 'BatchId',//导出完成后返回的key 列表的时候用到
        dataSourceListKey: 'PolicyGoodsList',
        clearUrlType: 'DELETE',
        clearUrl: '/{v}/PurchaseAgreement/DeleteBatchTempPolicyGoodsAsync',
        clearSaveKey: 'BatchId',
      },
      model: {
        PartyFirstId: '',
        PartyFirstName: '',
        RebateTime: null,
        FileUrls: [],
        PolicyList: [],
        rItemObj: {},
        dataSource: [],
        PolicyContent: '',
        RebateAmount: null,
        IsLimitGoods: false,
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      searchImportInput: [
        {
          name: '商品编号', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'ErpGoodsCode', //搜索key 必填
          placeholder: '请输入',
        }
      ],
      goodsImportColumns: [
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'commonContent' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtns: [{ name: '移除', icon: '' }],
          scopedSlots: { customRender: 'action' },
        },
      ],
      columns: [],
      columns1: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利金额',
          dataIndex: 'RebateAmount',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'RebateAmount' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          // fixed: "right",
          // scopedSlots: { customRender: 'action' },
          customRender: (text, record, index) => {
            // <a onclick={() => this.operate(record, 'copy', index)}>复制</a>
            // <a style="margin:0 5px;" onclick={() => this.operate(record, 'edit', index)}>
            //   编辑
            // </a>
            let childrenVal
            childrenVal = (
              <div>

                <a onclick={() => this.operate(record, 'del', index)}>移除</a>
              </div>
            )
            const obj = {
              children: childrenVal,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      selectTableData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          {
            name: '商品',
            type: 'input',
            value: '',
            key: 'keyWord',
            defaultVal: '',
            placeholder: '名称/编号/拼音码',
          },
          {
            name: '商品类别',
            type: 'selectLink',
            vModel: 'GoodsManageClassify1',
            dataKey: { name: 'Name', value: 'Id' },
            httpParams: { Name: '', Level: 1 },
            keyWord: 'Name',
            defaultVal: '',
            placeholder: '请选择',
            httpType: 'POST',
            url: '/{v}/GoodsManage/QueryGoodsManageClassifyList',
            httpHead: 'P36006',
          },
          {
            name: '生产厂家',
            type: 'input',
            value: '',
            key: 'BrandManufacturer',
            defaultVal: '',
            placeholder: '请输入厂家名称',
          },
        ],
        title: '选择商品',
        name: '商品',
        recordKey: 'Id',
        httpHead: 'P36006',
        isInitData: false,
        url: {
          list: '/{v}/GoodsManage/QueryGoodsAuditPasssList',
          listType: 'POST',
        },
        columns: [
          {
            title: '商品名称',
            dataIndex: 'ErpGoodsName',
            width: 180,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '商品编号',
            dataIndex: 'ErpGoodsCode',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '商品类别',
            dataIndex: 'ManageClassifyStr1',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '规格',
            dataIndex: 'PackingSpecification',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '单位',
            dataIndex: 'PackageUnit',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '件装量',
            dataIndex: 'PackageCount',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '生产厂商',
            dataIndex: 'BrandManufacturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '采购人员',
            dataIndex: 'PurchaserName',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
      selectTabData: [
        {
          tabTitle: '供应商',
          selectTableData: {
            searchInput: [
              //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
              {
                name: '供应商',
                type: 'input',
                value: '',
                key: 'KeyWord',
                defaultVal: '',
                placeholder: '名称/编号/拼音码',
              },
            ],
            title: '选择供应商',
            name: '供应商',
            recordKey: 'Id',
            httpHead: 'P36003',
            isInitData: false,
            queryParam: {
              AuthBusiness: 2,
            },
            url: {
              list: '/{v}/Supplier/GetSuppliersForSelect',
              listType: 'GET',
            },
            columns: [
              {
                title: '供应商名称',
                dataIndex: 'Name',
                width: 200,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '供应商编号',
                dataIndex: 'Code',
                width: 150,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '注册地址',
                dataIndex: 'RegAddress',
                width: 150,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '操作',
                dataIndex: 'action',
                align: 'center',
                width: 100,
                fixed: 'right',
                scopedSlots: { customRender: 'action' },
              },
            ],
          },
        },
        {
          tabTitle: '生产厂家',
          selectTableData: {
            searchInput: [
              //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
              {
                name: '生产厂家',
                type: 'input',
                value: '',
                key: 'KeyWord',
                defaultVal: '',
                placeholder: '名称/编号/拼音码',
              },
            ],
            title: '选择生产厂家',
            name: '生产厂家',
            recordKey: 'Id',
            httpHead: 'P36006',
            isInitData: false,
            url: {
              list: '/{v}/BrandManufacturer/QueryBrandManufacturerList',
              listType: 'GET',
            },
            columns: [
              {
                title: '生产厂家名称',
                dataIndex: 'Name',
                width: 200,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '操作',
                dataIndex: 'action',
                align: 'center',
                width: 100,
                fixed: 'right',
                scopedSlots: { customRender: 'action' },
              },
            ],
          },
        }
      ],
      uploadText: '上传图片',
      listImageUrl: [],
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
      id: '',
      isCopy: null,
      type: null, //1 年度协议 2 短期销售协议
      confirmLoading: false,
      httpHead: 'P36007',
      url: {
        list: '',
        add: '/{v}/GeneralAgreement/CreateROUpdateGeneralAgreementAsync',
        info: '/{v}/GeneralAgreement/GeneralAgreementDetailsAsync',
        supplierList: '/{v}/Supplier/GetSuppliersForSelect',
        createAudit: '/{v}/ApprovalWorkFlowInstance/CreateApproval',
      },
    }
  },
  computed: {
    rules() {
      return {
        listImageUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if ((this.listImageUrl || []).length == 0) {
                callback(new Error('请上传原件照片!'))
              } else {
                callback()
              }
            },
          },
        ],
        PolicyList: [
          {
            required: true,
            validator: (rule, value, callback) => {
              console.log('验证   ', this.model.PolicyList)
              if ((this.model.PolicyList || []).length == 0) {
                callback(new Error('请选择促销政策!'))
              } else if (this.model.PolicyList.some(v => !v.RebateAmount)) {
                callback(new Error('请填写返利金额!'))
              } else {
                callback()
              }
            },
          },
        ],
        RebateTime: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.model.RebateTime) {
                this.model.RebateTime = this.model.RebateTime.substring(0, 10)
              }
              if (!this.model.RebateTime) {
                callback(new Error('请选择返利时间!'))
              } else {
                callback()
              }
            },
          },
        ],
        PartyFirstId: [{ required: true, message: '请选择甲方!' }],
        PolicyContent: [{ required: true, message: '请输入!' }],
        RebateAmount: [{ required: true, message: '请输入!' }],
        IsLimitGoods: [{ required: true, message: '请选择!' }],
      }
    },
  },
  watch: {
    model: {
      handler(newVal, oldVal) {
        this.getTotalAmount()
      },
      deep: true,
    },
  },
  mounted() { },
  created() {
    this.id = this.$route.query.id
    this.type = Number(this.$route.query.type) //1年度协议 2短期销售协议
    this.isCopy = this.$route.query.isCopy
    this.columns = this.columns1
    if (this.id) {
      this.edit(this.id)
    } else {
      this.add()
    }
  },
  methods: {
    moment,
    add() {
      this.title = '新增' + this.title
      this.$root.$emit('setPageTitle', null, this.title, this.$route.fullPath)
      this.model = {
        PartyFirstId: '',
        PartyFirstName: '',
        RebateTime: null,
        FileUrls: [],
        PolicyList: [],
        rItemObj: {},
        dataSource: [],
        PolicyContent: '',
        RebateAmount: null,
        IsLimitGoods: false,
      }
      this.listImageUrl = []
      this.isEdit = false
      this.visible = true
    },
    edit(id) {
      if (this.isCopy) {
        this.id = ''
        this.title = '新增' + this.title
      } else {
        this.title = '编辑' + this.title
      }
      this.$root.$emit('setPageTitle', null, this.title, this.$route.fullPath)
      this.getInfo(id)
      this.isEdit = true
      this.visible = true
    },
    getInfo(id) {
      let formData = {
        Id: id,
      }
      getAction(this.url.info, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            // 数据源
            this.model = Object.assign({}, res.Data)
            if ((this.model.FileUrls || []).length > 0) {
              this.listImageUrl = this.model.FileUrls
            }
            if ((this.model.RebateGoods || []).length > 0) {
              this.model.RebateGoods.map(v=>{
                v.Id = v.GoodsId
              })
              this.$set(this.model, 'PolicyList', this.model.RebateGoods)
              this.$set(this, 'dataSource', this.model.PolicyList)
            }
            this.columns = this.columns1
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => { })
    },
    searchDataInput() {
      let data = [
        {
          Name: this.model.PartyFirstName,
          Id: this.model.PartyFirstId,
        }
      ]
      this.$refs.TableSelectDataModal.show(data)
    },
    chooseGoodsData(data) {
      // console.log('商品列表chooseGoodsData  ', data)
      
      // 还原促销政策表格
      if (data && data.length > 0) {
        let arr = this.dataSource || [];
        (data || []).map(v => {
          v.RebateAmount = null
          if(!this.dataSource.find(x=>x.Id == v.Id)){
            arr.push(v)
          }
        });
        // console.log('商品列表存在Data  ', this.dataSource)
        // console.log('Data  ', arr)
        
        this.$set(this, 'dataSource', arr || [])
        this.$set(this.model, 'PolicyList', this.dataSource)
      }
      this.$refs.form.clearValidate()
    },
    chooseData(data) {
      if (data && data.length > 0) {
        if (data[0].tabActiveKey == 0) {
          this.model.PartyFirstType = 1
        } else if (data[0].tabActiveKey == 1) {
          this.model.PartyFirstType = 2
        }
        let val = data[0].Id
        let name = data[0].Name

        this.model.PartyFirstId = val
        this.model.PartyFirstName = name
        this.$nextTick(() => {
          this.$refs.form.clearValidate()
        })
      }
    },
    onTypeChange(e, type) {
      this.$refs.form.clearValidate()
    },
    // 上传回调
    multiUploadChange(images) {
      this.listImageUrl = images
    },
    onTimeChange(dates, dateStrings) {
      this.model.RebateTime = dateStrings
    },
    checkPolicyNum() {
      let num = 50
      if ((this.model.PolicyList || []).length > num) {
        this.$message.warning('促销政策最多添加50条,请调整后保存')
        return false
      } else {
        return true
      }
    },
    // 返利金额
    handleInput(record) {
      // console.log('record ' , record)
      this.$nextTick(() => {
        this.$forceUpdate()
        //计算调价金额
        this.getTotalAmount()
        this.$refs.form.clearValidate()
      })
    },
    /**
    * 获取总金额
    */
    getTotalAmount() {
      let sum = 0

      if ((this.model.PolicyList || []).length) {
        this.model.PolicyList.forEach((x) => {
          if (x['RebateAmount']) {
            sum += (x['RebateAmount'] || 0)
          }
        })
      }

      this.total = this.getAmountOfMoney(sum, 2)
    },
    // 导入保存
    handleBatchImportModalOk(data, type) {
      // console.log(data, type)
      if (data.length > 0) {
        if (type == 'goods') {
          if ((this.model.PolicyList || []).length) {
            this.dataSource = this.dataSource.concat(data)
          } else {
            this.dataSource = data
          }
          this.$set(this.model, 'PolicyList', this.dataSource)
          this.$refs.form.clearValidate()
        }

      }
    },
    // 确定 type 1 提交审核 2 草稿
    handleOk(type) {
      if (!this.isCopy && this.model.AuditStatus == 1) {
        this.$message.warning('当前通用协议审核中,暂时无法提交保存')
        return
      }
      if (this.model.IsLimitGoods && !this.checkPolicyNum()) {
        return
      }
      const that = this
      // 触发表单验证
      // type 1 提交审核 2 存为草稿
      // if(type == 2){
      //   this.$refs.form.validateField('PartyFirstId',err => {
      //     if (!err) {
      //       this.postFun(type)
      //     }
      //   })
      //   return
      // }
      this.$refs.form.validate((err, values) => {
        if (err) {
          this.postFun(type)
        }
      })
    },
    // 请求接口
    postFun(type) {
      const that = this
      // 重组照片数据
      let listImageUrlData = []
      if (this.listImageUrl.length > 0) {
        listImageUrlData = this.listImageUrl
      }
      let formData = {
        // "Id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "PartyFirstId": this.model.PartyFirstId,
        "PartyFirstType": this.model.PartyFirstType,
        "PartyFirstName": this.model.PartyFirstName,
        "IsLimitGoods": this.model.IsLimitGoods,
        "RebateTime": this.model.RebateTime,
        "RebateGoods": [],
        "FileUrls": listImageUrlData,
        "IsDraft": type === 2
      }
      if (!this.model.IsLimitGoods) {
        if(!this.model.RebateAmount){
          this.$message.warning('请输入返利金额')
          return
        }
        formData.PolicyContent = this.model.PolicyContent
        formData.RebateAmount = this.model.RebateAmount
      }
      if ((this.model.PolicyList || []).length && this.model.IsLimitGoods) {
        this.model.PolicyList.map(v => {
          formData.RebateGoods.push({
            "GoodsId": v.Id || v.GoodsId,
            "ErpGoodsId": v.ERPGoodsId || v.ErpGoodsId,
            "ErpGoodsCode": v.ErpGoodsCode,
            "ErpGoodsName": v.ErpGoodsName,
            "BrandManufacturer": v.BrandManufacturer,
            "PackingSpecification": v.PackingSpecification,
            "PackageUnit": v.PackageUnit,
            "PackageCount": v.PackageCount,
            "RebateAmount": v.RebateAmount,
          })
        })

      }

      if (this.isEdit && !this.isCopy) {
        formData.Id = this.model.Id
      }
      console.log('提交数据   ', formData)
      that.confirmLoading = true
      postAction(this.url.add, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            let text = type == 1 ? '提交审核成功' : '保存草稿成功'
            if (type == 2) {
              that.$message.success(text)
            }
            if (res.Data) {
              this.id = res.Data['GeneralAgreementId']
            }
            that.$emit('ok')
            // 提交审核
            if (type == 1) {
              let params = {
                BussinessNo: res.Data['AuditId'],
                MainBussinessNo: null,
                Scenes: 37,
                OpratorId: Vue.ls.get(USER_ID),
                Remark: '通用协议审核',
              }
              this.createApproval(params)
            } else {
              // 保存草稿
              setTimeout(() => {
                this.backPage()
              }, 500)
            }
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    // 创建审核实例
    createApproval(params, callback) {
      this.loading = true
      postAction(this.url.createAudit, params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            this.$message.success('提交审核成功')
            callback && callback(true)
            this.backPage()
          } else {
            this.$message.error(res.Msg)
            callback && callback(false)
          }
        })
        .catch((e) => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    backPage() {
      this.$root.$emit('removeTabsAndBack', this.$route.fullPath, '/pages/contractManagement/purchaseGeneralList')
    },
    changeSearchInput(val, name) {
      this.model.PartyFirstId = val
      this.model.PartyFirstName = name
    },
    subDel(record, type, index) {
      // console.log('===== ',index)
      if (type == 'del') {
        // 移除政策数据
        let policyIndex = index
        this.model.PolicyList.splice(policyIndex, 1)
        // console.log('移除政策数据 ',this.model.PolicyList)
        // 移除table数据
        let delArray = []
        this.dataSource.map((item, rIndex) => {
          if (policyIndex == item.index) {
            delArray.push(rIndex)
          }
        })
        let delStartIndex = delArray[0]
        let delStartLength = delArray.length
        this.dataSource.splice(delStartIndex, delStartLength)
      }
    },
    operate(record, type, index) {
      // 选择品种
      if (type == 'chooseVariety') {
        if (!this.checkPolicyNum()) {
          return
        }
        this.model.PolicyList = this.dataSource
        console.log('选择品种   ', this.model)
        let userId = Vue.ls.get(USER_ID) || null
        let queryParam = {
          // AuthBusinessStatus: 2,
          IsAll:  true,
          PartyFirstId: this.model.PartyFirstId,
          // Name: this.model.PartyFirstName,
        }
        // 
        console.log('已选中的品种 ', this.dataSource)
        this.$refs.TableSelectGoodsDataModal.show(this.dataSource, queryParam)
      }
      else if (type == 'importGoods') {
        console.log('导入商品   ',this.dataSource)
        this.$refs.iBatchImportGoodsModal.show(this.dataSource)
      }
      else if (type == 'del') {
        let that = this
        this.$confirm({
          title: '您确定要移除该促销政策吗?',
          content: '',
          onOk() {
            that.subDel(record, type, index)
          },
          onCancel() { },
        })
      }
    },
  },
}
</script>
<style lang="less" scoped>
   .party-search {
    flex: 1;
    margin-right: 5px;
  }
  /deep/.party-search .ant-input:disabled {
    color: rgba(0, 0, 0, 0.65);
    background-color: #ffffff;
  }
  </style>
