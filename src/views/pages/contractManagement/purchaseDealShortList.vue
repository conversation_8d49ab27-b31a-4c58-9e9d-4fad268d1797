<!--
 * @Description: 短期采购协议 列表
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-03-31 15:25:06
-->

<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab">
      <!-- 促销时间 -->
      <span slot="ExpirationTimeVal" slot-scope="{ text, record }">
        <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
      </span>
    </SimpleTable>
    <!-- 作废 -->
    <PurchaseCancellationModal ref="PurchaseCancellationModal" pageN="purchaseDealShortList" aUrl="/{v}/PurchaseAgreement/InvalidateShortAgreement" @ok="handleOk" />
    <!-- 作废原因 -->
    <PurchaseCancellationReasonModal ref="PurchaseCancellationReasonModal" pageN="purchaseDealShortList" aUrl="/{v}/PurchaseAgreement/GetPurchaseShortAgreementInvalidInfo" />
    <!-- 补录协议 -->
    <PurchaseReplacementAgreementModal ref="PurchaseReplacementAgreementModal" />
    <!-- 补录记录 -->
    <PurchaseSupplementaryRecordModal ref="PurchaseSupplementaryRecordModal" />
    <!-- 取消返利 -->
    <PurchaseCancelRebateModal ref="PurchaseCancelRebateModal" source="dqcg" @close="modalOk" />

  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'purchaseDealShortList',
  title: '短期采购协议',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '供应商',
          type: 'input',
          value: '',
          key: 'SupplierKey',
          defaultVal: '',
          placeholder: '名称/拼音码/编号',
        },
        { name: '订单编号', type: 'input', value: '', key: 'PurchaseOrderNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '商品名称',
          type: 'input',
          value: '',
          key: 'GoodsKey',
          defaultVal: '',
          placeholder: '名称/拼音码/编号',
        },
        {
          name: '负责人',
          type: 'input',
          value: '',
          key: 'ResponsiblePersonName',
          defaultVal: '',
          placeholder: '请输入',
        },
        {
          name: '认款状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'RecognitionStatus',
          dictCode: 'EnumRecognitionStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '到期日',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['ExpirationTimeStart', 'ExpirationTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '协议状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'Status',
          dictCode: 'EnumShortAgreementStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '责任人',
          type: 'input',
          value: '',
          key: 'OwnerByName',
          defaultVal: '',
          placeholder: '请输入',
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '补录记录', type: 'primary', icon: 'file-text', key: '补录记录', id: '434035d3-9543-417c-b008-f3c4f4f5075b' },
          { name: '补录协议', type: 'primary', icon: 'plus', key: '补录协议', id: '25930156-4242-4799-9411-641d499a4d10' },
          { name: '导出', type: '', icon: 'download', key: '导出', id: 'd4cbfc96-d2cb-432f-a917-8bd718fc45f6' },
        ],
        hintArray: [],
        sortArray: ['ExpirationTimeVal'],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'PartyFirstName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '日期',
          dataIndex: 'CreateTime',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '负责人',
          dataIndex: 'ResponsiblePersonName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '实际入库数量',
          dataIndex: 'InboundQuantity',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '每盒返利金额',
          dataIndex: 'RebatePrice',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款方式',
          dataIndex: 'RecognitionType',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款金额',
          dataIndex: 'UnrecognizedAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '到期日',
          dataIndex: 'ExpirationTime',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '协议状态',
          dataIndex: 'StatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          fixed: 'right',
          actionBtn: [
            {
              name: '作废',
              icon: '',
              id: '22458499-eca8-4946-b42c-45ee38bbd8a6',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.Status === 1
              },
            },
            {
              name: '作废原因',
              icon: '',
              id: 'e2afb708-9823-42e5-bcfb-b39ed2b7f372',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.Status === 2
              },
            },
            {
              name: '取消返利',
              icon: '',
              id: '9a5e426c-7a64-4d92-8567-b8836afc0252',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.Status !== 2 && record.UnrecognizedAmount > 0
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        }
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36007',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/PurchaseAgreement/GetPurchaseShortAgreements',
        exportUrl: '/{v}/PurchaseAgreement/ExportPurchaseShortAgreements',
      },
    }
  },
  activated() {
    this.queryParam = this.$refs.SimpleSearchArea.queryParam
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    // 列表操作
    operate(record, type) {
      let that = this
      switch (type) {
        case '补录记录':
          this.$refs.PurchaseSupplementaryRecordModal.show()
          break;
        case '补录协议':
          this.$refs.PurchaseReplacementAgreementModal.show()
          break;
        case '导出':
          this.handleExportXls(
            '短期采购协议',
            'Get',
            this.linkUrl.exportUrl,
            null,
            this.linkHttpHead,
            this.$refs.SimpleSearchArea.queryParam
          )
          break;
        case '作废':
          this.$confirm({
            title: '您确定要作废该短期协议吗? 如果已经认款，会自动将认款记录一起作废',
            content: '',
            onOk() {
              that.$refs.PurchaseCancellationModal.show(record)
            },
            onCancel() { },
          })
          break;
        case '作废原因':
          this.$refs.PurchaseCancellationReasonModal.show(record)
          break;
        case '取消返利':
          this.$refs.PurchaseCancelRebateModal.show(record)
          break;
      }
    },
    modalOk() {
      this.$refs.table.loadDatas(null, this.$refs.SimpleSearchArea.queryParam)
    },
    // 刷新列表
    handleOk() {
      // console.log('queryParam === ',this.queryParam)
      // console.log('SimpleSearchArea  === ',this.$refs.SimpleSearchArea.queryParam)
      this.$refs.table.loadDatas(null, this.$refs.SimpleSearchArea.queryParam)
    }
  },
}
</script>

<style></style>
