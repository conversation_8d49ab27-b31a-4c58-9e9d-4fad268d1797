<!-- 补录记录 -->
<template>
  <a-modal :title="title" :width="1100" :visible="visible" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :destroyOnClose="true" :footer="null">
    <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <TableNewView ref="tableView" :table="table" :columns="columns" :dataList="dataSource" @operate="operate">

    </TableNewView>
    <!-- 补录协议 -->
    <PurchaseReplacementAgreementModal ref="PurchaseReplacementAgreementModal" @ok="loadData" />
  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
import { PublicMixin } from '@/mixins/PublicMixin'//非必要 公用的 有需要时候用其他mixin也行
import { getAction, putAction, postAction, deleteAction } from "@/api/manage";
export default {
  name: "PurchaseSupplementaryRecordModal",
  components: {},
  mixins: [ListMixin, PublicMixin],
  data() {
    return {
      title: "补录记录",
      visible: false,
      searchItems: [
        {
          name: '创建时间',
          type: this.$SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTimeBegin',
          keyEnd: 'CreateTimeEnd',
          showTime: true,
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '审核状态',
          type: this.$SEnum.ENUM,
          key: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
        },
        {
          name: '供应商', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'PartyFirstName', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '订单编号', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'PurchaseOrderNo', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '创建人', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'CreateByName', //搜索key 必填
          placeholder: '请输入',
        },


      ],
      queryParam: {
      },
      table: {
        operateBtns: [

        ], //右上角按钮集合
        rowKey: 'Id',
        customSlot: [],
      },
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'PartyFirstName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否已生成协议',
          dataIndex: 'IsRewritten',
          width: 150,
          ellipsis: true,
          customRender: (text) => {
            return text ? '是' : '否'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '编辑',
              isShow: (record) => {
                return !![0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '删除',
              isShow: (record) => {
                return !![0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '详情',
              isShow: (record) => {
                return ![0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '撤回',
              isShow: (record) => {
                return !![1].includes(record.AuditStatus)
              },
            },
          ],
        },

      ],
      isInitData: false,
      httpHead: 'P36007',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/SupplementaryPurchaseShortAgreement/GetSupplementaryPurchaseShortAgreementList',//列表数据接口
        deleteAgreement: '/{v}/SupplementaryPurchaseShortAgreement/DeleteAgreement',//删除接口
        revokeSupplementaryShortAgreement: '/{v}/SupplementaryPurchaseShortAgreement/RevokeSupplementaryShortAgreement',//撤回接口
      },
    };
  },
  computed: {
    rules() {
      return {
        Name: [{ required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200, '作废原因') }],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record) {
      // this.queryParam.id = record.Id || null
      this.loadData(1)
      this.visible = true;
      // this.$nextTick(() => {
      //   // if(this.$refs.searchView) this.$refs.searchView.resetQueryParam()
      // })
    },
    // 列表操作
    operate(record, type) {
      let that = this
      switch (type) {
        case '编辑':
          this.$refs.PurchaseReplacementAgreementModal.show(record.Id)
          break;
        case '删除':
          that.handleDelete(record.Id);
          break;
        case '详情':
          this.onDetailClick('PurchaseSupplementaryRecordInfo', { id: record.Id })
          this.close()
          break;
        case '撤回':
          this.$confirm({
            title: '您确定要撤回吗? ',
            content: '',
            onOk() {
              that.handleRevoke(record.Id)
            },
            onCancel() { },
          })
          break;
      }
    },
    // 删除
    handleDelete(id) {
      deleteAction(this.url.deleteAgreement + '?agreementId=' + id, {}, this.httpHead).then(res => {
        if (res.IsSuccess) {
          this.$message.success('删除成功')
          this.loadData(1)
        } else {
          this.$message.error(res.Msg)
        }
      })
    },
    // 撤回
    handleRevoke(id) {
      postAction(this.url.revokeSupplementaryShortAgreement, { Id: id }, this.httpHead).then(res => {
        if (res.IsSuccess) {
          this.$message.success('撤回成功')
          this.loadData(1)
        } else {
          this.$message.error(res.Msg)
        }
      })
    },
    // 确定
    handleOk() {

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>

<style>
</style>