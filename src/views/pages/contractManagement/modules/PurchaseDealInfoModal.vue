<!-- 促销政策详情 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :footer="null" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-row :getter="10">
          <a-col :md="24">
            <p style="color: rgba(0, 0, 0, 0.85)">促销内容：{{ clickPolicyObj.PolicyContent || '--' }}</p>
          </a-col>

          <a-col :md="24">
            <a-form-model-item label="返利模式：">
              <div class="rebate-choose-box">
                <div class="rebate-choose">
                  <div class="rebate-choose-title" v-html="zcModel.Title"></div>
                  <div class="rebate-choose-text">{{ zcModel.Description }}</div>
                </div>
              </div>
            </a-form-model-item>
          </a-col>

          <a-col :md="24">
            <a-form-model-item label="">
              <div style="margin-bottom: 8px">
                <span>促销品种：</span>
                <span v-if="zcModel.HasIsLimitGoods">{{clickPolicyObj.IsLimitGoods == true?'限制品种':(clickPolicyObj.IsLimitGoods == false?'不限制品种':'--')}}</span>
              </div>
              <a-table :bordered="false" v-if="showGoodTable" ref="table" :rowKey="(record, index) => index" size="middle" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length === 0 ? false : '350px', x: '800px' }">
                <!-- 商品名称 -->
                <template slot="ErpGoodsName" slot-scope="text, record">
                  <a-tag style="font-size:10px;line-height: 16px;margin-right: 2px;" v-if="record.IsRebate == false" color="red">{{record.IsRebate == true?'':(record.IsRebate == false?'不返利':'')}}</a-tag>
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <!-- 字符串超长截取省略号显示-->
                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <!-- 操作 -->
                <span slot="action" slot-scope="text, record">
                  <a style="margin: 0 5px" @click="operate(record, 'copy')">详情</a>
                </span>
              </a-table>
              <a-col :md="24">
                <a-col :md="8">
                  <div style="color: rgba(0, 0, 0, 0.85)">
                    赠品是否参与返利计算：{{ clickPolicyObj.IsGiftGoodsRebate == true?'是':(clickPolicyObj.IsGiftGoodsRebate == false?'否':'--') }}
                  </div>
                </a-col>
                <a-col :md="8" v-if="modelData.AccountingMethod == 2 && zcModel.HasSaleCountStatisticType == true">
                  <div style="color: rgba(0, 0, 0, 0.85)">
                    销售数量：{{ clickPolicyObj.SaleCountStatisticTypeStr || '--' }}
                  </div>
                </a-col>
              </a-col>
            </a-form-model-item>
          </a-col>

          <a-col :md="24">
            <a-form-model-item>
              <div style="margin-bottom: 5px;">

                <div style="color: rgba(0, 0, 0, 0.85)" v-if="zcModel.Title && model.PolicyStepMethod">
                  促销规则配置：{{
                      clickPolicyObj.PolicyStepMethod == 1
                        ? '阶梯'
                        : clickPolicyObj.PolicyStepMethod == 2
                        ? '每满'
                        : clickPolicyObj.PolicyStepMethod == 3
                        ? '区间'
                        : '--'
                    }}
                </div>

                <div style="color: rgba(0, 0, 0, 0.85)" v-if="zcModel.HasReturnGoods">
                  返利品种：{{
                      clickPolicyObj.ReturnGoodsMethod == 1
                        ? '本品'
                        : '赠品' + '（' + clickPolicyObj.ReturnGoodsName + '）'
                    }}
                </div>

                <div style="color: rgba(0, 0, 0, 0.85)" v-if="zcModel.PolicyStepMethod == 2 || clickPolicyObj.RebateMaxValue">
                  返利上限：{{
                      clickPolicyObj.RebateMaxValue ? '大于' + clickPolicyObj.RebateMaxValue + '则不返' : '没有上限'
                    }}
                </div>

                <div style="color: rgba(0, 0, 0, 0.85)" v-if="zcModel.PolicyRebateMethod == 9">
                  单价：{{ flModel9.ReturnGoodsUnitPric }}
                </div>
                <div style="color: rgba(0, 0, 0, 0.85)" v-if="zcModel.PolicyRebateMethod == 9">
                  返利比例(百分比点数)：{{ flModel9.ReturnGoodsRebateRate }}
                </div>

                <div style="color: rgba(0, 0, 0, 0.85)" v-if="modelData.AccountingMethod == 3">
                  打款金额设置：{{ clickPolicyObj.IsCasePaymentType == true?'区分打款类型':(clickPolicyObj.IsCasePaymentType == false?'不区分打款类型':'--') }}
                </div>
                <div style="color: rgba(0, 0, 0, 0.85)" v-if="modelData.AccountingMethod == 3 && model.IsCasePaymentType == false">
                  <span>承兑打款不得超过整体打款金额的：</span>
                  <a-input-number style="margin: 0 5px" disabled v-model="clickPolicyObj.AcceptancePaymentLimitRate" placeholder="请输入" />%,
                  <span>否则只返应收返利的</span>
                  <a-input-number style="margin: 0 5px" disabled v-model="clickPolicyObj.ReceivableReturnRebateRate" placeholder="请输入" />%
                </div>
              </div>
              <a-table :bordered="false" ref="table" :rowKey="(record, index) => index" size="middle" :columns="columns2" :dataSource="dataSource2" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length === 0 ? false : '350px', x: '800px' }">
                <span slot="tableTitle1">
                  {{ tableTitleArray[0] }}
                </span>
                <span slot="tableTitle2">
                  {{ tableTitleArray[1] }}
                </span>
                <span slot="DaKuanJEtableTitle1">
                  {{ DaKuanJETableTitleArray[0] }}
                </span>
                <span slot="DaKuanJEtableTitle2">
                  {{ DaKuanJETableTitleArray[1] }}
                </span>
                <span slot="DaKuanJEtableTitle3">
                  {{ DaKuanJETableTitleArray[2] }}
                </span>
                <span slot="DaKuanJEtableTitle4">
                  {{ DaKuanJETableTitleArray[3] }}
                </span>

                <template slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </template>
                <!-- 区间输入框展示 -->
                <template slot="sectionInput" slot-scope="text,record">
                  <j-ellipsis :value="text?(record.qujianText + text):''" />
                </template>
              </a-table>
            </a-form-model-item>
          </a-col>
          <a-col :md="24" v-if="modelData.AccountingMethod == 2">
            <a-col :md="8">
              <div style="color: rgba(0, 0, 0, 0.85)">
                销售规则配置：{{
                  model.DesignatedType == 1
                    ? '不限制'
                    : model.DesignatedType == 2
                    ? '指定客户销售'
                    : model.DesignatedType == 3
                    ? '按区域/客户类型销售'
                    : '--'
                  }}
              </div>
            </a-col>
          </a-col>
          <a-form-model-item label="销售区域：" v-if="+model.DesignatedType === 3">
            <div style="display: flex;align-items: center;justify-content: space-between;">
              <MultipleChoiceView 
                style="flex: 1;margin-right:8px;" 
                placeholder="请选择区域" 
                :open="false" 
                :maxTagCount="maxTagCount" 
                disabled 
                :localDataList="clickPolicyObj['AllowAreaListData']" 
                v-model="clickPolicyObj['AllowAreaList']" 
                :dataKey="{ name: 'Name', value: 'Code' }" 
                ref="iCustomerType" />
              <a style="white-space: nowrap;" @click="chooseArea()">查看区域</a>
            </div>
          </a-form-model-item>
          <a-form-model-item label="销售客户类型：" v-if="+model.DesignatedType === 3">
            <div style="display: flex;align-items: center;justify-content: space-between;">
              <MultipleChoiceView 
                style="flex: 1;" 
                placeholder="请选择选择客户类型" 
                :open="false" 
                disabled 
                :localDataList="clickPolicyObj['AllowCustomerTypeListData']" 
                v-model="clickPolicyObj['AllowCustomerTypeList']" 
                :dataKey="{ name: 'Name', value: 'Code' }" 
                ref="iCustomerType" />
            </div>
          </a-form-model-item>
          <div v-if="[2,3].includes(+model.DesignatedType)" style="padding: 10px 0;">
            <div>{{+model.DesignatedType===2?'销':'禁'}}售客户：</div>
          </div>
          <a-table v-if="[2,3].includes(+model.DesignatedType)" :bordered="false" ref="table" :rowKey="(record, index) => index" size="middle" :columns="JsSaleColumns" :dataSource="JsSaleDataSource" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: JsSaleDataSource.length === 0 ? false : '350px', x: '800px' }">
            <!-- 字符串超长截取省略号显示-->
            <template slot="component" slot-scope="text">
              <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
              <j-ellipsis :value="text" v-else />
            </template>
            <!-- 操作 -->
            <span slot="action" slot-scope="text, record, index">
              <a style="margin: 0 5px" @click="operate(record, 'delShop', index)">移除</a>
            </span>
          </a-table>
        </a-row>
      </a-spin>
    </div>

    <!-- 选择销售区域 -->
    <SelectAreaTree ref="SelectAreaTree" :title="areaTitle" isInfo />
  </a-modal>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'
const lodash = require('lodash');
const columns2 = [
  {
    //   title: '采购金额(门槛金额)',
    slots: { title: 'tableTitle1' },
    dataIndex: 'num1',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    //   title: '返利金额(固定金额)',
    slots: { title: 'tableTitle2' },
    dataIndex: 'num2',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const JsSaleColumns = [
  {
    title: '客户名称',
    dataIndex: 'CustomerName',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '客户ID',
    dataIndex: 'CustomerKeyValue',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '注册地址',
    dataIndex: 'RegAddress',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const DaKuanJEColumns = [
  {
    slots: { title: 'DaKuanJEtableTitle1' },
    dataIndex: 'AcceptanceThresholdValue',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    slots: { title: 'DaKuanJEtableTitle2' },
    dataIndex: 'AcceptanceRebateValue',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    slots: { title: 'DaKuanJEtableTitle3' },
    dataIndex: 'TelegraphicThresholdValue',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    slots: { title: 'DaKuanJEtableTitle4' },
    dataIndex: 'TelegraphicRebateValue',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]

export default {
  name: 'PurchaseDealInfoModal',
  mixins: [ListMixin],
  components: {
    JEllipsis,
  },
  data() {
    return {
      title: '促销政策详情',
      visible: false,
      model: {},
      modelData: {},
      zcModel: {},
      clickPolicyObj: {
        AllowAreaListData: [],
        AllowAreaList: [],
        AllowCustomerTypeList: [],
        AllowCustomerTypeListData: [],
        LimitCustomerList: [],
      },
      //返利模式9
      flModel9: {
        ReturnGoodsUnitPric: null,
        ReturnGoodsRebateRate: null,
      },
      tableTitleArray: [],
      DaKuanJETableTitleArray: [],
      maxTagCount: 20,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'ErpGoodsName' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购人员',
          dataIndex: 'PurchaserName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns2: columns2,
      JsSaleColumns: JsSaleColumns,
      dataSource: [],
      dataSource2: [],
      JsSaleDataSource: [],
      areaTitle: '查看销售区域',
      showFanliBtn: false, //是否显示返利按钮
      confirmLoading: false,
      httpHead: 'P36007',
      url: {
        list: '',
        zcInfo: '/{v}/PurchaseAgreement/GetPolicyConfigListAsync',
      },
    }
  },
  computed: {
    showGoodTable() {
      let data = (this.modelData.AccountingMethod == 3 && this.clickPolicyObj.IsLimitGoods == true) || (this.modelData.AccountingMethod != 3 && !this.zcModel.HasIsLimitGoods) || (this.modelData.AccountingMethod != 3 && this.zcModel.HasIsLimitGoods && this.clickPolicyObj.IsLimitGoods == true) ? true : false
      return data
    },
  },
  watch: {
    // 打款金额设置
    'clickPolicyObj.IsCasePaymentType'(to) {
      console.log(to, 1111)
      switch (to) {
        case true:
          this.columns2 = DaKuanJEColumns
          let setDaKuanColumnsObj = this.setDaKuanColumns() || {}
          if (this.clickPolicyObj.PolicyStepMethod == 3) {
            this.columns2[setDaKuanColumnsObj.DaKuanJEtableTitle1Index].scopedSlots.customRender = 'sectionInput'
            this.columns2[setDaKuanColumnsObj.DaKuanJEtableTitle3Index].scopedSlots.customRender = 'sectionInput'
          }
          break
        case false:
          this.columns2 = columns2
          let setDaKuanColumnsObj2 = this.setDaKuanColumns() || {}
          if (this.clickPolicyObj.PolicyStepMethod == 3) {
            this.columns2[setDaKuanColumnsObj2.tableTitle1Index].scopedSlots.customRender = 'sectionInput'
          }
          break
      }

      // 初始化数据
      this.initDataSource2Data()
    },
    // 促销规则配置 1 阶梯 2 每满 3 区间
    'clickPolicyObj.PolicyStepMethod'(to) {
      console.log(to, 222)
      let setDaKuanColumnsObj = this.setDaKuanColumns() || {}
      switch (to) {
        case 1:
          break
        case 2:
          break
        case 3:
          if (this.clickPolicyObj.IsCasePaymentType && this.columns2.length > 3) {
            this.columns2[setDaKuanColumnsObj.DaKuanJEtableTitle1Index].scopedSlots.customRender = 'sectionInput'
            this.columns2[setDaKuanColumnsObj.DaKuanJEtableTitle3Index].scopedSlots.customRender = 'sectionInput'
          } else if (!this.clickPolicyObj.IsCasePaymentType) {
            if (setDaKuanColumnsObj.tableTitle1Index > -1) { this.columns2[setDaKuanColumnsObj.tableTitle1Index].scopedSlots.customRender = 'sectionInput' }
          }
          // 初始化数据
          this.initDataSource2Data()
          break
      }
    },
    // 返利模式类型
    'zcModel.PolicyRebateMethod'(to) {
      console.log('PolicyRebateMethod', to)
      let saleIndex = this.columns.findIndex(item => {
        return item.title == '销售门槛数量'
      })
      let operateIndex = this.columns.length
      if (to == 10 || to == 11 || to == 12) {
        this.showFanliBtn = false
        if (operateIndex > -1 && saleIndex == -1) {
          let obj = {
            title: '销售门槛数量',
            dataIndex: 'SaleThresholdCount',
            width: 120,
            ellipsis: true,
            fixed: 'right',
            scopedSlots: { customRender: 'SaleThresholdCountInput' },
          }
          // 插入对象是在inidex的左边
          this.columns.splice(operateIndex, 0, obj)
        }
      } else {
        this.showFanliBtn = true
        if (operateIndex > -1 && saleIndex > -1) {
          // 删除对象元素
          this.columns.splice(operateIndex - 1, 1)
        }
      }
    },
    dataSource2(newV) {
      // 区间
      if (this.clickPolicyObj.PolicyStepMethod == 3) {
        this.dataSource2.map((item, index) => {
          if (index == 0) {
            item.qujianText = '门槛'
            item.hideDel = true
          } else if (index == 1) {
            item.qujianText = '超出'
            item.hideDel = true
          } else if (index > 1) {
            item.qujianText = '超出'
            item.hideDel = undefined
          }
        })

      }
    },
  },
  methods: {
    moment,
    show(record, clickPolicyObjData, modelData) {
      // 初始化的时候就要拷贝防止影响列表当前数据
      // let clickPolicyObj = Object.assign({}, clickPolicyObjData)
      let clickPolicyObj = lodash.cloneDeep(clickPolicyObjData)
      // 还原销售区域
      if (clickPolicyObj.AllowAreaList && clickPolicyObj.AllowAreaList.length > 0) {
        let list = []
        let keys = []
        clickPolicyObj.AllowAreaList.map(item => {
          keys.push(item.AreaCode)
          list.push({
            Code: item.AreaCode,
            Name: item.AreaFullName,
            ...item,
          })
        })
        clickPolicyObj.AllowAreaList = keys
        clickPolicyObj.AllowAreaListData = list
      }
      // 还原销售客户类型
      if (clickPolicyObj.AllowCustomerTypeList && clickPolicyObj.AllowCustomerTypeList.length > 0) {
        let list = []
        let keys = []
        clickPolicyObj.AllowCustomerTypeList.map(item => {
          keys.push(item.CustomerTypeValue)
          list.push({
            Code: item.CustomerTypeValue,
            Name: item.CustomerTypeName,
            ...item,
          })
        })
        clickPolicyObj.AllowCustomerTypeList = keys
        clickPolicyObj.AllowCustomerTypeListData = list
      }
      // 还原禁售客户
      this.JsSaleDataSource = clickPolicyObj.LimitCustomerList || []

      // 数据重组
      this.dataSource = []
      if (clickPolicyObj.GoodsList && clickPolicyObj.GoodsList.length > 0) {
        clickPolicyObj.GoodsList.map((item) => {
          this.dataSource.push(item)
        })
      }
      // 还原金额数据
      this.dataSource2 = []
      let moneyData = []
      if (clickPolicyObj.StepList && clickPolicyObj.StepList.length > 0) {
        moneyData = clickPolicyObj.StepList.map((item) => {
          return {
            num1: item.ThresholdValue,
            num2: item.RebateValue,
          }
        })
      }
      // 还原承兑等金额 IsCasePaymentType true
      if (clickPolicyObj.IsCasePaymentType == true && clickPolicyObj.StepForCasePaymentList && clickPolicyObj.StepForCasePaymentList.length > 0) {
        moneyData = clickPolicyObj.StepForCasePaymentList.map((item) => {
          return item
        })
      }
      this.dataSource2 = moneyData

      this.flModel9 = {
        ReturnGoodsUnitPric: clickPolicyObj.ReturnGoodsUnitPric,
        ReturnGoodsRebateRate: clickPolicyObj.ReturnGoodsRebateRate,
      }

      this.model = clickPolicyObj
      this.modelData = modelData
      this.clickPolicyObj = {}
      this.clickPolicyObj = clickPolicyObj
      this.getZcInfo()
      this.visible = true
    },
    getZcInfo() {
      let formData = {
        accountingMethod: this.modelData.AccountingMethod,
        configId: this.clickPolicyObj.PurchaseAgreementPolicyConfigId,
      }
      this.confirmLoading = true
      getAction(this.url.zcInfo, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data && res.Data.length > 0) {
              this.zcModel = res.Data[0]
              this.tableTitleArray = [this.zcModel.ThresholFieldDescription, this.zcModel.RebateFieldDescription]
              this.DaKuanJETableTitleArray = [
                '承兑' + this.zcModel.ThresholFieldDescription,
                '承兑' + this.zcModel.RebateFieldDescription,
                '电汇' + this.zcModel.ThresholFieldDescription,
                '电汇' + this.zcModel.RebateFieldDescription,
              ]
              this.setAccountingPriceData()
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    setDaKuanColumns() {
      let tableTitle1Index = this.columns2.findIndex(item => {
        return item.slots ? item.slots.title == 'tableTitle1' : false
      })
      let DaKuanJEtableTitle1Index = this.columns2.findIndex(item => {
        return item.slots ? item.slots.title == 'DaKuanJEtableTitle1' : false
      })
      let DaKuanJEtableTitle3Index = this.columns2.findIndex(item => {
        return item.slots ? item.slots.title == 'DaKuanJEtableTitle3' : false
      })
      // 初始化customRender
      if (this.model.IsCasePaymentType && this.columns2.length > 3) {
        this.columns2[DaKuanJEtableTitle1Index].scopedSlots.customRender = 'AcceptanceThresholdValueInput'
        this.columns2[DaKuanJEtableTitle3Index].scopedSlots.customRender = 'TelegraphicThresholdValueInput'
      } else if (!this.model.IsCasePaymentType) {
        if (tableTitle1Index > -1) { this.columns2[tableTitle1Index].scopedSlots.customRender = 'input' }
      }
      return {
        tableTitle1Index: tableTitle1Index,
        DaKuanJEtableTitle1Index: DaKuanJEtableTitle1Index,
        DaKuanJEtableTitle3Index: DaKuanJEtableTitle3Index,
      }
    },
    initDataSource2Data() {
      if (this.clickPolicyObj.StepList && this.clickPolicyObj.StepList.length > 0) {
        return
      }
      if (this.clickPolicyObj.StepForCasePaymentList && this.clickPolicyObj.StepForCasePaymentList.length > 0) {
        return
      }
      if (this.model.PolicyStepMethod == 3) {
        // 区间
        this.dataSource2 = [
          {
            num1: undefined,
            num2: undefined,
            AcceptanceThresholdValue: undefined,
            AcceptanceRebateValue: undefined,
            TelegraphicThresholdValue: undefined,
            TelegraphicRebateValue: undefined,
          },
          {
            num1: undefined,
            num2: undefined,
            AcceptanceThresholdValue: undefined,
            AcceptanceRebateValue: undefined,
            TelegraphicThresholdValue: undefined,
            TelegraphicRebateValue: undefined,
          },
        ]
      } else {
        // 非区间
        this.dataSource2 = [
          {
            num1: undefined,
            num2: undefined,
            AcceptanceThresholdValue: undefined,
            AcceptanceRebateValue: undefined,
            TelegraphicThresholdValue: undefined,
            TelegraphicRebateValue: undefined,
          }
        ]
      }
    },
    // 核算金额
    setAccountingPriceData() {
      if (this.zcModel.PolicyRebateMethod == 6 || this.zcModel.AccountingConditions == 2) {
        if (this.columns.length == 5 || (this.columns[5] && this.columns[5].title !== '供应商核算价')) {
          let obj = {
            title: '供应商核算价',
            dataIndex: 'AccountingPrice',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          }
          this.columns.splice(5, 0, obj)
        }
      } else {
        if (this.columns.length > 5 && this.columns[5] && this.columns[5].title === '供应商核算价') {
          this.columns.splice(5, 1)
        }
      }
    },
    chooseArea() {
      if (this.clickPolicyObj['AllowAreaList'].length === 0) {
        this.$message.warning('暂无销售区域')
        return
      }
      let area = []
      if (this.model.AllowAreaListData && this.model.AllowAreaListData.length > 0) {
        this.model.AllowAreaListData.map(item => {
          area.push({
            ...item,
            AreaCode: item.Code,
            AreaName: item.Name,
          })
        })
      }
      this.$refs.SelectAreaTree.show(area)
    },
    // 确定
    handleOk() { },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>
<style scoped>
.rebate-choose {
  width: 100%;
  background: rgba(242, 242, 242, 1);
  border: 1px solid rgba(215, 215, 215, 1);
  border-radius: 5px;
  padding: 20px 20px;
}
.rebate-choose-title {
  line-height: normal;
}
.rebate-choose-text {
  color: #7f7f7f;
  line-height: normal;
  margin-top: 6px;
}
.rebate-input-box {
  border: 1px solid rgba(215, 215, 215, 1);
  border-radius: 5px;
  padding: 10px 15px;
  border-top: none;
  border-bottom: none;
}
</style>
