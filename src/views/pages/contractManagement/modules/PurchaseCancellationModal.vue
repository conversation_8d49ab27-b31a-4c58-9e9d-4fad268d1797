<!-- 合约作废 -->
<template>
  <a-modal :title="title" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
        <a-row :getter="10">
          <a-col :md="24">
            <a-form-model-item label="" prop="CancelReason">
              <a-textarea placeholder="作废原因(必填)" v-model="model.CancelReason" :rows="4" />
            </a-form-model-item>

          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">
        取消
      </a-button>
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">继续</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "PurchaseCancellationModal",
  components: {},
  mixins: [EditMixin],
  props:{
    pageN:{
      type:String,
      default:''
    },
    aUrl:{
      type:String,
      default:''
    },
    requireType:{
      type:String,
      default:'PUT'
    }
  },
  data() {
    return {
      title: "将作废该条短期协议，需要继续吗？",
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      httpHead: 'P36007',
      url: {
        voidAsync: this.aUrl || '/{v}/PurchaseAgreement/VoidAsync',//作废
      },
    };
  },
  computed: {
    rules() {
      return {
        CancelReason: [{ required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200, '作废原因') }],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record) {
      this.model = Object.assign({}, record);
      this.visible = true;
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          let formData = {}
          let url = ''
          url = this.url.voidAsync
          formData = {
            Id: this.model.Id,
            CancelReason: this.model.CancelReason,
          }
          let obj;
          if(this.pageN == 'purchaseDealShortList'){ //purchaseDealShortList 短期采购协议列表 --- 作废
            obj= postAction(url, {ShortAgreementId: this.model.Id, InvalidReason: this.model.CancelReason}, this.httpHead)
          }else if(this.pageN == 'purchaseGeneralList'){
            obj= getAction(url, {Id: this.model.Id, CancelReason: this.model.CancelReason}, this.httpHead)
          }else{
            obj= putAction(url, formData, this.httpHead)
          }
          obj.then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
