<!--
 * @Description: 促销政策弹窗
 * @Version: 1.0
 * @Author: 继承
 * @Date: 2025-02-11 14:17:02
 * @LastEditors: wenjing<PERSON>ao
 * @LastEditTime: 2025-06-12 14:40:10
-->
<template>
  <a-modal
    :title="title"
    :width="1000"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true">
    <div
      :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="促销内容：" prop="PolicyContent">
                <a-input
                  :placeholder="purchaseType == 2
                    ? '请输入概括政策规则'
                    : '请按照协议签署的促销内容进行填写,这样方便后期政策查询使用'
                  "
                  v-model="model.PolicyContent"
                  style="width: 100%"
                  :maxLength="150" />
              </a-form-model-item>
              <a-form-model-item label="返利模式：" prop="PurchaseAgreementPolicyId">
                <!-- AccountingMethod 1购进 2销售 3打款金额 -->
                <div class="rebate-choose">
                  <div style="width: 92%" v-if="rItemObj.Title">
                    <div style="display: flex;">
                      <div v-if="rItemObj.PolicyRebateMethod == 4" style="color: red;background: #fef0ef;border: 1px solid #ef3b3b;border-radius: 26%;font-size: 10px;zoom:0.9;padding: 2px 3px;margin-right:2px;">
                        返货</div>
                      <div v-html="rItemObj.Title"></div>
                    </div>
                    <div style="color:rgb(127, 127, 127);">{{ rItemObj.Description }}</div>
                  </div>
                  <div v-if="!rItemObj.Title" style="color: #7f7f7f">请选择</div>
                  <a @click="chooseRebate(rItemObj)">选择</a>
                </div>
              </a-form-model-item>

              <!-- 1年度协议 2短期销售 -->
              <div v-if="purchaseType == 1 || purchaseType == 2">
                <div>
                  <a-form-model-item label="" prop="shopData">
                    <div style="margin-bottom: 8px">
                      <a-form-model-item label="促销品种：" prop="IsLimitGoods">
                        <a-radio-group v-if="rItemObj.HasIsLimitGoods" v-model="model.IsLimitGoods" @change="changeIsLimitGoods">
                          <a-radio :value="true">
                            限制品种
                          </a-radio>
                          <a-radio :value="false">
                            不限制品种
                          </a-radio>
                        </a-radio-group>
                      </a-form-model-item>
                    </div>
                    <div v-if="showGoodTable">
                      <div style="display: flex;align-items: center;justify-content: space-between;border: 1px solid #e8e8e8;border-bottom: none;padding: 10px 10px;">
                        <div>已选商品</div>
                        <div>
                          <a-button style="margin-right: 15px" @click="clearTableData">清空</a-button>
                          <a-button style="margin-right: 15px" v-if="rItemObj.IsMultipleSelectGoods" type="primary" @click="importGoods">导入商品</a-button>
                          <a-button type="primary" @click="chooseTableData">选择商品</a-button>
                        </div>
                      </div>
                      <a-table
                        :bordered="false"
                        ref="table"
                        :rowKey="(record, index) => index"
                        size="middle"
                        :columns="columns"
                        :dataSource="dataSource"
                        :pagination="ipagination"
                        :loading="loading"
                        @change="handleTableChange"
                        :scroll="{ y: dataSource.length === 0 ? false : '350px', x: '800px' }">
                        <!-- 商品名称 -->
                        <template slot="ErpGoodsName" slot-scope="text, record">
                          <a-tag style="font-size:10px;line-height: 16px;margin-right: 2px;" v-if="record.IsRebate == false&&rItemObj.AccountingConditions !== 5 && rItemObj.AccountingConditions !== 4" color="red">{{ record.IsRebate == true ? '' :
                            (record.IsRebate ==
                              false ? '不返利' : '') }}</a-tag>
                          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                          <j-ellipsis :value="text" v-else />
                        </template>
                        <!-- 字符串超长截取省略号显示-->
                        <template slot="component" slot-scope="text">
                          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                          <j-ellipsis :value="text" v-else />
                        </template>
                        <!-- 输入框供应商核算价 -->
                        <template slot="input" slot-scope="text, record">
                          <a-input-number
                            placeholder="请输入"
                            v-model="record.AccountingPrice"
                            style="width: 100px"
                            :precision="2"
                            :formatter="formatter"
                            :min="0.01"
                            :max="*********.99" />
                        </template>
                        <!-- 销售门槛数量 -->
                        <template slot="SaleThresholdCountInput" slot-scope="text, record">
                          <a-input-number
                            placeholder="请输入"
                            v-model="record.SaleThresholdCount"
                            style="width: 100px"
                            :formatter="(text) => (/^\d+$/.test(text) ? text : text.slice(0,-1))"
                            :min="0"
                            :max="*********" />
                        </template>
                        <!-- 操作 -->
                        <span slot="action" slot-scope="text, record, index">
                          <a style="margin: 0 5px" @click="operate(record, 'delShop', index)">移除</a>
                          <a style="margin: 0 5px" v-if="!record.IsRebate && showFanliBtn && rItemObj.AccountingConditions !== 5 && rItemObj.AccountingConditions !== 4" @click="operate(record, 'fanLi', index)">返利</a>
                          <a style="margin: 0 5px" v-if="record.IsRebate && showFanliBtn && rItemObj.AccountingConditions !== 5 && rItemObj.AccountingConditions !== 4" @click="operate(record, 'noFanLi', index)">不返利</a>
                        </span>
                      </a-table>
                    </div>
                  </a-form-model-item>
                  <div style="width: 100%; border-bottom: 1px dashed #dddddd; margin-bottom: 10px"></div>
                </div>

                <a-form-model-item label="赠品是否参与返利计算：" prop="IsGiftGoodsRebate">
                  <a-radio-group v-model="model.IsGiftGoodsRebate">
                    <a-radio :value="true">
                      是
                    </a-radio>
                    <a-radio :value="false">
                      否
                    </a-radio>
                  </a-radio-group>
                </a-form-model-item>
              </div>

              <!-- 促销规则配置相关 -->
              <div style="margin-bottom: 10px">
                <div class="rule-box">
                  <div>
                    <!-- StepMethodType 1 无 2 仅阶梯 3 阶梯和每满 4 阶梯和区间 5 阶梯每满和区间 -->
                    <div style="margin-bottom: 10px" v-if="rItemObj.Title && [2,3,4,5].includes(rItemObj.StepMethodType)">
                      <span><span style="color: #f5222d">*</span> 促销规则配置：</span>
                      <a-radio-group v-model="model.PolicyStepMethod" @change="(e) => onTypeChange(e, 'cxgz')">
                        <a-radio :value="1" v-if="[2,3,4,5].includes(rItemObj.StepMethodType)">
                          阶梯
                        </a-radio>
                        <a-radio :value="2" v-if="[3,5].includes(rItemObj.StepMethodType)">
                          每满
                        </a-radio>
                        <a-radio :value="3" v-if="[4,5].includes(rItemObj.StepMethodType)">
                          区间
                        </a-radio>
                      </a-radio-group>
                    </div>
                    <!-- HasRebateMax true 显示 false 不显示 -->
                    <a-form-model-item
                      v-if="model.PolicyStepMethod == 2"
                      :label="'返利上限：(' +
                        (rItemObj.ThresholFieldDescription
                          ? rItemObj.ThresholFieldDescription.substring(
                            0,
                            rItemObj.ThresholFieldDescription.indexOf('(') > -1
                              ? rItemObj.ThresholFieldDescription.indexOf('(')
                              : 4
                          )
                          : '') +
                        '大于上限的部分不参与返利计算）'
                      "
                      required>
                      <a-radio-group v-model="model.RebateMaxValueType" @change="(e) => onTypeChange(e, 'flsx')">
                        <a-radio :value="1"> 没有上限 </a-radio>
                        <a-radio :value="2">
                          <span>大于</span>
                          <a-input-number
                            style="margin: 0 5px"
                            :precision="2"
                            :formatter="formatter"
                            :placeholder="'上限' + ((rItemObj.ThresholFieldDescription || '').indexOf('金额') > -1 ? '金额' : '数量')
                            "
                            v-model="model.RebateMaxValue"
                            :min="0"
                            :max="*********.99"
                            @change="changeMoneyInput" />
                          <span>则不返</span>
                        </a-radio>
                      </a-radio-group>
                    </a-form-model-item>
                    <a-form-model-item label="打款金额设置：" v-if="modelObj.AccountingMethod == 3" prop="IsCasePaymentType">
                      <a-radio-group v-model="model.IsCasePaymentType" @change="changeIsCasePaymentType">
                        <a-radio :value="true">
                          区分打款类型
                        </a-radio>
                        <a-radio :value="false">
                          不区分打款类型
                        </a-radio>
                      </a-radio-group>
                    </a-form-model-item>

                    <a-form-model-item v-if="modelObj.AccountingMethod == 3 && model.IsCasePaymentType == false">
                      <div style="margin-bottom: 10px">
                        <span>承兑打款不得超过整体打款金额的：</span>
                        <a-input-number
                          style="margin: 0 5px"
                          v-model="model.AcceptancePaymentLimitRate"
                          :precision="2"
                          :formatter="formatter"
                          :min="0"
                          :max="100"
                          placeholder="请输入" />%,
                        <span>否则只返应收返利的</span>
                        <a-input-number
                          style="margin: 0 5px"
                          v-model="model.ReceivableReturnRebateRate"
                          :precision="2"
                          :formatter="formatter"
                          :min="0"
                          :max="100"
                          placeholder="请输入" />%
                      </div>
                    </a-form-model-item>

                    <!-- HasReturnGoods true 显示 false 不显示 -->
                    <div v-if="rItemObj.HasReturnGoods">
                      <span><span style="color: #f5222d">*</span> 返利品种：</span>
                      <a-radio-group v-model="model.ReturnGoodsMethod" @change="(e) => onTypeChange(e, 'flpz')">
                        <a-radio :value="1" :disabled="(dataSource.length == 1 && JSON.stringify(dataSource[0]) != '{}') ? false : true">
                          本品
                        </a-radio>
                        <a-radio :value="2">
                          <span style="margin-right: 10px">赠品</span>
                          <a-tooltip>
                            <template slot="title" v-if="model.ReturnGoodsMethod == 2 && giftObj.ReturnGoodsName">
                              {{ giftObj.ReturnGoodsName }}
                            </template>
                            <a-input style="width: 400px" :value="(model.ReturnGoodsMethod == 2 && giftObj.ReturnGoodsName)?giftObj.ReturnGoodsName:''" disabled placeholder="请选择赠品">
                              <a slot="addonAfter" @click="chooseGift">选择</a>
                            </a-input>
                          </a-tooltip>
                        </a-radio>
                      </a-radio-group>
                    </div>
                    <!-- PolicyRebateMethod 返利模式 9 -->
                    <div style="display: flex; align-items: center; margin: 10px 0" v-if="rItemObj.Title && rItemObj.PolicyRebateMethod == 9">
                      <div style="width: 213px"><span style="color: #f5222d">*</span> 单价：</div>
                      <a-input-number
                        placeholder="请输入"
                        v-model="flModel9.ReturnGoodsUnitPric"
                        style="width: 300px"
                        :precision="2"
                        :formatter="formatter"
                        :min="0.01"
                        :max="*********.99" />
                    </div>
                    <div style="display: flex; align-items: center; margin-bottom: 10px" v-if="rItemObj.Title && rItemObj.PolicyRebateMethod == 9">
                      <div style="width: 213px"><span style="color: #f5222d">*</span> 返利比例(单位：%)：</div>
                      <a-input-number
                        placeholder="请输入"
                        v-model="flModel9.ReturnGoodsRebateRate"
                        style="width: 300px"
                        :precision="2"
                        :formatter="formatter"
                        :min="1"
                        :max="100" />
                    </div>
                  </div>
                  <a-button
                    v-if="
                      ([2,3,4,5].includes(rItemObj.StepMethodType) && model.PolicyStepMethod == 1) || model.PolicyStepMethod == 3
                    "
                    type="primary"
                    @click="addRow">增加一行</a-button>
                </div>
                <!-- 打款模式列表 -->
                <a-table
                  v-if="[2,3,4,5].includes(rItemObj.StepMethodType)"
                  style="margin-top: 10px"
                  :bordered="false"
                  ref="rowTable"
                  :rowKey="(record, index) => index"
                  size="middle"
                  :columns="columns2"
                  :dataSource="dataSource2"
                  :pagination="false"
                  :loading="loading2"
                  @change="handleTableChange"
                  :scroll="{ y: dataSource2.length === 0 ? false : '300px', x: 600 }">
                  <span slot="tableTitle1">
                    {{ tableTitleArray[0] }}
                  </span>
                  <span slot="tableTitle2">
                    {{ tableTitleArray[1] }}
                  </span>
                  <span slot="DaKuanJEtableTitle1">
                    {{ DaKuanJETableTitleArray[0] }}
                  </span>
                  <span slot="DaKuanJEtableTitle2">
                    {{ DaKuanJETableTitleArray[1] }}
                  </span>
                  <span slot="DaKuanJEtableTitle3">
                    {{ DaKuanJETableTitleArray[2] }}
                  </span>
                  <span slot="DaKuanJEtableTitle4">
                    {{ DaKuanJETableTitleArray[3] }}
                  </span>
                  <!-- 承兑打款金额(门槛金额)输入框 -->
                  <template slot="AcceptanceThresholdValueInput" slot-scope="text, record,index,column">
                    <a-input-number
                      placeholder="请输入"
                      v-model="record.AcceptanceThresholdValue"
                      style="width: 150px"
                      :precision="inputPrecision(index,column)"
                      :formatter="formatter"
                      :min="0.01"
                      :max="*********.99"
                      @blur="blurInput(record, index, 'AcceptanceThresholdValue')" />
                  </template>
                  <!-- 承兑返利金额(固定金额)输入框 -->
                  <template slot="AcceptanceRebateValueInput" slot-scope="text, record,index,column">
                    <a-input-number
                      placeholder="请输入"
                      v-model="record.AcceptanceRebateValue"
                      style="width: 150px"
                      :precision="inputPrecision(index,column)"
                      :formatter="formatter"
                      :min="0.01"
                      :max="*********.99" />
                  </template>
                  <!-- 电汇打款金额(门槛金额)输入框 -->
                  <template slot="TelegraphicThresholdValueInput" slot-scope="text, record,index,column">
                    <a-input-number
                      placeholder="请输入"
                      v-model="record.TelegraphicThresholdValue"
                      style="width: 150px"
                      :precision="inputPrecision(index,column)"
                      :formatter="formatter"
                      :min="0.01"
                      :max="*********.99"
                      @blur="blurInput(record, index, column.dataIndex,(model.PolicyStepMethod == 3?'区间':''))" />
                  </template>
                  <!-- 电汇返利金额(固定金额)输入框 -->
                  <template slot="TelegraphicRebateValueInput" slot-scope="text, record,index,column">
                    <a-input-number
                      placeholder="请输入"
                      v-model="record.TelegraphicRebateValue"
                      style="width: 150px"
                      :precision="inputPrecision(index,column)"
                      :formatter="formatter"
                      :min="0.01"
                      :max="*********.99" />
                  </template>

                  <!-- 字符串超长截取省略号显示-->
                  <template slot="component" slot-scope="text">
                    <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                    <j-ellipsis :value="text" v-else />
                  </template>
                  <!-- 采购金额输入框 -->
                  <template slot="input" slot-scope="text, record,index,column">
                    <a-input-number
                      placeholder="请输入"
                      v-model="record.num1"
                      style="width: 150px"
                      :precision="inputPrecision(index,column)"
                      :formatter="formatter"
                      :min="0.01"
                      :max="*********.99"
                      @blur="blurInput(record, index, 'num1')" />
                  </template>
                  <!-- 返利金额输入框 -->
                  <template slot="input2" slot-scope="text, record,index,column">
                    <a-input-number
                      placeholder="请输入"
                      v-model="record.num2"
                      style="width: 150px"
                      :precision="inputPrecision(index,column)"
                      :formatter="formatter"
                      :min="0.01"
                      :max="rItemObj.RebateFieldDescription && rItemObj.RebateFieldDescription.indexOf('百分比') > -1
                        ? 99.99
                        : *********.99
                      " />
                  </template>
                  <!-- 区间输入框 -->
                  <template slot="sectionInput" slot-scope="text, record,index,column">
                    <span style="margin-right:5px;">{{ record.qujianText }}</span><a-input-number
                      placeholder="请输入"
                      v-model="record[column.dataIndex]"
                      style="width: 120px"
                      :formatter="(text) => (/^\d+$/.test(text) ? text : text.slice(0,-1))"
                      :min="0"
                      :max="*********"
                      :precision="0"
                      @blur="blurInput(record, index, column.dataIndex,'区间')" />
                  </template>

                  <!-- 操作 -->
                  <span slot="action" slot-scope="text, record, index">
                    <a style="margin: 0 5px" @click="operate(record, 'reset', index)">重置</a>
                    <a v-if="!record.hideDel" style="margin: 0 5px" @click="operate(record, 'delMoney', index)">移除</a>
                  </span>
                </a-table>
              </div>

              <div v-if="modelObj.AccountingMethod == 2">
                <a-form-model-item label="销售数量：" v-if="rItemObj.HasSaleCountStatisticType == true" prop="SaleCountStatisticType">
                  <a-radio-group v-model="model.SaleCountStatisticType">
                    <a-radio :value="1">
                      按总数
                    </a-radio>
                    <a-radio :value="2">
                      按单条
                    </a-radio>
                  </a-radio-group>
                </a-form-model-item>
                <a-form-model-item label="销售规则配置：" prop="DesignatedType">
                  <a-radio-group v-model="model.DesignatedType" @change="() => CustomerDataSource=[]">
                    <a-radio :value="1">
                      不限制
                    </a-radio>
                    <a-radio :value="2">
                      指定客户销售
                    </a-radio>
                    <a-radio :value="3">
                      按区域/客户类型销售
                    </a-radio>
                  </a-radio-group>
                </a-form-model-item>
                <a-form-model-item label="销售区域：" v-if="+model.DesignatedType === 3" prop="jyKeys">
                  <div style="display: flex;align-items: center;justify-content: space-between;">
                    <MultipleChoiceView
                      style="flex: 1;margin-right:8px;"
                      placeholder="请选择区域"
                      :open="false"
                      :maxTagCount="maxTagCount"
                      :localDataList="model['AllowAreaListData']"
                      v-model="model['AllowAreaList']"
                      :dataKey="{ name: 'Name', value: 'Code' }"
                      ref="iCustomerType"
                      @change="(value, contents) => changeAllowArea(value, contents, 'AllowAreaListData')" />
                    <a style="white-space: nowrap;" @click="chooseArea()">选择区域</a>
                  </div>
                </a-form-model-item>
                <a-form-model-item label="销售客户类型：" v-if="+model.DesignatedType === 3" prop="jyKeys">
                  <div style="display: flex;align-items: center;justify-content: space-between;">
                    <MultipleChoiceView
                      style="flex: 1;margin-right:8px;"
                      placeholder="请选择选择客户类型"
                      :open="false"
                      :localDataList="model['AllowCustomerTypeListData']"
                      v-model="model['AllowCustomerTypeList']"
                      :dataKey="{ name: 'Name', value: 'Code' }"
                      ref="iCustomerType"
                      @change="(value, contents) => changeSaleTypeArray(value, contents, 'AllowCustomerTypeListData')" />
                    <a style="white-space: nowrap;" @click="chooseSaleType()">选择客户类型</a>
                  </div>
                </a-form-model-item>
                <div v-if="[2,3].includes(+model.DesignatedType)" style="display: flex;align-items: center;justify-content: space-between;border: 1px solid #e8e8e8;border-bottom: none;padding: 10px 10px;">
                  <div>{{+model.DesignatedType===2?'销':'禁'}}售客户</div>
                  <div>
                    <a-button style="margin-right: 15px" @click="clearCustomerData">清空</a-button>
                    <a-button style="margin-right: 15px" type="primary" @click="importCus">批量导入</a-button>
                    <a-button type="primary" @click="chooseCus">选择客户</a-button>
                  </div>
                </div>
                <a-table
                  v-if="[2,3].includes(+model.DesignatedType)"
                  :bordered="false"
                  ref="table"
                  :rowKey="(record, index) => index"
                  size="middle"
                  :columns="CustomerColumns"
                  :dataSource="CustomerDataSource"
                  :pagination="ipagination"
                  :loading="loading"
                  @change="handleTableChange"
                  :scroll="{ y: CustomerDataSource.length === 0 ? false : '350px', x: '800px' }">
                  <!-- 字符串超长截取省略号显示-->
                  <template slot="component" slot-scope="text">
                    <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                    <j-ellipsis :value="text" v-else />
                  </template>
                  <!-- 操作 -->
                  <span slot="action" slot-scope="text, record, index">
                    <a style="margin: 0 5px" @click="operate(record, 'delCus', index)">移除</a>
                  </span>
                </a-table>
              </div>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel"> 取消 </a-button>
    </a-row>
    <!-- 表格选择商品数据弹窗 -->
    <TableSelectDataModal 
      ref="TableSelectDataModal" 
      :type="rItemObj.IsMultipleSelectGoods ? 'checkbox' : 'radio'" 
      :selectTableData="selectTableData" 
      showSelectedData 
      @chooseData="chooseData"></TableSelectDataModal>
    <!-- 表格选择赠品弹窗 -->
    <TableSelectDataModal ref="TableSelectGiftDataModal" type="radio" :selectTableData="selectTableData2" isToSelectClose @chooseData="chooseData2"></TableSelectDataModal>
    <!-- 选择返利模式 -->
    <PurchaseDealRebateModal ref="PurchaseDealRebateModal" @ok="purchaseDealRebateOk"></PurchaseDealRebateModal>
    <!-- 表格选择销售客户类型弹窗 -->
    <TableSelectDataModal ref="TableSelectSaleTypeDataModal" :width="700" showSelectedData :selectTableData="selectTableDataSale" @chooseData="chooseSaleTypeData"></TableSelectDataModal>
    <!-- 表格选择销/禁售客户弹窗 -->
    <TableSelectDataModal ref="TableSelectCustomerDataModal" showSelectedData :selectTableData="selectCustomerTableData" @chooseData="chooseCustomerData"></TableSelectDataModal>
    <!-- 选择销售区域 -->
    <SelectAreaTree ref="SelectAreaTree" :title="areaTitle" @ok="selectAreasModalOk" />
    <!-- 批量导入 导入商品 -->
    <BatchImportSimpleModal
      ref="iBatchImportGoodsModal"
      :modalTitle="type == 1 ? '年度协议商品导入' : (type == 2 ? '短期销售协议商品导入' : '协议商品导入')"
      v-if="rItemObj.Id"
      :tableColumns="goodsImportColumns"
      :searchParamsList="searchImportGoodsInput"
      :importConfig="goodsImportConfig"
      :importUrl="`/v1/PurchaseAgreement/ImportTempPolicyGoodsListAsync?purchaseAgreementPolicyId=${rItemObj.Id}&agreementType=${this.type}`"
      importHttpHead="P36007"
      @ok="(e) => handleBatchImportModalOk(e, 'goods')" />
    <!-- 批量导入 导入禁售客户 -->
    <BatchImportSimpleModal
      ref="iBatchImportCusModal"
      modalTitle="客户导入"
      v-if="rItemObj.Id"
      :tableColumns="CustomerImportColumns"
      :searchParamsList="searchImportCusInput"
      :importConfig="cusImportConfig"
      :importUrl="`/v1/PurchaseAgreement/ImportLimitCustomers?purchaseAgreementPolicyId=${rItemObj.Id}`"
      importHttpHead="P36007"
      @ok="(e) => handleBatchImportModalOk(e, 'cus')" />
  </a-modal>
</template>

<script>
import Vue from 'vue'
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
import { USER_ID } from '@/store/mutation-types'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
const lodash = require('lodash');
const columns = [
  {
    title: '商品名称',
    dataIndex: 'ErpGoodsName',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'ErpGoodsName' },
  },
  {
    title: '商品编号',
    dataIndex: 'ErpGoodsCode',
    width: 100,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '规格',
    dataIndex: 'PackingSpecification',
    width: 100,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '单位',
    dataIndex: 'PackageUnit',
    width: 100,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '生产厂商',
    dataIndex: 'BrandManufacturer',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '采购人员',
    dataIndex: 'PurchaserName',
    width: 100,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 120,
    fixed: 'right',
    scopedSlots: { customRender: 'action' },
  },
]
const columns2 = [
  {
    // title: '采购金额(门槛金额)',
    slots: { title: 'tableTitle1' },
    dataIndex: 'num1',
    width: 200,
    type: 'cxgz_two',
    ellipsis: true,
    scopedSlots: { customRender: 'input' },
  },
  {
    // title: '返利金额(固定金额)',
    slots: { title: 'tableTitle2' },
    dataIndex: 'num2',
    width: 200,
    type: 'cxgz_two',
    ellipsis: true,
    scopedSlots: { customRender: 'input2' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 120,
    // fixed: "right",
    scopedSlots: { customRender: 'action' },
  },
]
const DaKuanJEColumns = [
  {
    slots: { title: 'DaKuanJEtableTitle1' },
    dataIndex: 'AcceptanceThresholdValue',
    width: 200,
    type: 'cxgz_four',
    ellipsis: true,
    scopedSlots: { customRender: 'AcceptanceThresholdValueInput' },
  },
  {
    slots: { title: 'DaKuanJEtableTitle2' },
    dataIndex: 'AcceptanceRebateValue',
    width: 200,
    type: 'cxgz_four',
    ellipsis: true,
    scopedSlots: { customRender: 'AcceptanceRebateValueInput' },
  },
  {
    slots: { title: 'DaKuanJEtableTitle3' },
    dataIndex: 'TelegraphicThresholdValue',
    width: 200,
    type: 'cxgz_four',
    ellipsis: true,
    scopedSlots: { customRender: 'TelegraphicThresholdValueInput' },
  },
  {
    slots: { title: 'DaKuanJEtableTitle4' },
    dataIndex: 'TelegraphicRebateValue',
    width: 200,
    type: 'cxgz_four',
    ellipsis: true,
    scopedSlots: { customRender: 'TelegraphicRebateValueInput' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 120,
    // fixed: "right",
    scopedSlots: { customRender: 'action' },
  },
]
const CustomerColumns = [
  {
    title: '客户名称',
    dataIndex: 'CustomerName',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '客户ID',
    dataIndex: 'CustomerKeyValue',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '注册地址',
    dataIndex: 'RegAddress',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 120,
    fixed: "right",
    scopedSlots: { customRender: 'action' },
  },
]
const goodsImportColumns = [
  {
    title: '商品编号',
    dataIndex: 'ErpGoodsCode',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'commonContent' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 80,
    actionBtns: [{ name: '移除', icon: '' }],
    scopedSlots: { customRender: 'action' },
  },
]
const CustomerImportColumns = [
  {
    title: '客户名称',
    dataIndex: 'CustomerName',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'commonContent' },
  },
  {
    title: '客户ID',
    dataIndex: 'CustomerKeyValue',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '注册地址',
    dataIndex: 'RegAddress',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 80,
    actionBtn: [{ name: '移除', icon: '' }],
    actionBtns: [{ name: '移除', icon: '' }],
    scopedSlots: { customRender: 'action' },
  },
]

export default {
  name: 'PurchaseDealPromotionAddModal',
  mixins: [ListMixin],
  components: {
    JEllipsis,
  },
  props: {
    //1年度协议 2短期销售
    purchaseType: {
      type: [String, Number],
      default: 1,
    },
  },
  data() {
    return {
      title: '新增促销政策',
      visible: false,
      isEdit: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      goodsImportConfig: {
        templateFileName: '协议商品导入模板',//下载模板名字
        ErrorShowKey: 'ErrorShow',//异常信息字段名称
        saveKey: 'BatchId',
        saveUrlType: 'GET',//保存接口请求方式
        saveUrl: '/{v}/PurchaseAgreement/GetTempPolicyGoodsDetailsListAsync',//保存接口
        listQueryKey: 'BatchId',//列表的时候需要用到的字段
        batchRemoveId: '',//批量移除id
        listRemoveType: 'DELETE',//列表删除接口请求方式
        listRemoveUrl: '/{v}/PurchaseAgreement/DeleteSpecifiedTempPolicyGoodsAsync',//列表删除 接口
        listRemoveKey: '',//列表删除 参数 key
        listHttpHead: 'P36007',
        listUrl: '/{v}/PurchaseAgreement/GetTempPolicyGoodsPageListAsync',//列表的请求接口
        listUrlType: 'GET',//列表接口请求方式
        queryParamStatusKey: 'Status',//列表查询 异常 正常 key
        noLoadData: true,//是否默认弹出时候不加载数据
        importResKey: 'BatchId',//导出完成后返回的key 列表的时候用到
        dataSourceListKey: 'PolicyGoodsList',
        clearUrlType: 'DELETE',
        clearUrl: '/{v}/PurchaseAgreement/DeleteBatchTempPolicyGoodsAsync',
        clearSaveKey: 'BatchId',
        haveDataSourceKey: 'ErpGoodsCode',
        onlySaveOneData: null,
      },
      cusImportConfig: {
        templateFileName: '客户导入模板',//下载模板名字
        ErrorShowKey: 'ErrorShow',//异常信息字段名称
        saveKey: '',
        saveUrlType: 'PUT',//保存接口请求方式
        saveUrl: '/{v}/PurchaseAgreement/SaveLimitCustomerTempData',//保存接口
        listQueryKey: '',//列表的时候需要用到的字段
        batchRemoveId: '',//批量移除id
        listRemoveType: 'POST',//列表删除接口请求方式
        listRemoveUrl: '/{v}/PurchaseAgreement/RemoveLimitCustomerTmps',//列表删除 接口
        listRemoveKey: 'IdList',//列表删除 参数 key
        listHttpHead: 'P36007',
        listUrl: '/{v}/PurchaseAgreement/QueryLimitCustomerTmps',//列表的请求接口
        listUrlType: 'POST',//列表接口请求方式
        listPageSize: 9999999,
        queryParamStatusKey: 'Status',//列表查询 异常 正常 key
        noLoadData: true,//是否默认弹出时候不加载数据
        importResKey: '',//导出完成后返回的key 列表的时候用到
        dataSourceListKey: 'AgreementLimitCustomerList',
        clearUrlType: 'PUT',
        clearUrl: '/{v}/PurchaseAgreement/ClearLimitCustomerTmps',
        clearSaveKey: '',
        haveDataSourceKey: 'CustomerKeyValue',
      },

      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      areaTitle: '选择销售区域',
      maxTagCount: 20,
      selectTableData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          {
            name: '商品',
            type: 'input',
            value: '',
            key: 'keyWord',
            defaultVal: '',
            placeholder: '名称/编号/拼音码',
          },
          {
            name: '商品类别',
            type: 'selectLink',
            vModel: 'GoodsManageClassify1',
            dataKey: { name: 'Name', value: 'Id' },
            httpParams: { Name: '', Level: 1 },
            keyWord: 'Name',
            defaultVal: '',
            placeholder: '请选择',
            httpType: 'POST',
            url: '/{v}/GoodsManage/QueryGoodsManageClassifyList',
            httpHead: 'P36006',
          },
          {
            name: '生产厂家',
            type: 'input',
            value: '',
            key: 'BrandManufacturer',
            defaultVal: '',
            placeholder: '请输入厂家名称',
          },
        ],
        title: '选择商品',
        name: '商品',
        recordKey: 'Id',
        httpHead: 'P36006',
        isInitData: false,
        url: {
          list: '/{v}/GoodsManage/QueryGoodsAuditPasssList',
          listType: 'POST',
        },
        columns: [
          {
            title: '商品名称',
            dataIndex: 'ErpGoodsName',
            width: 180,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '商品编号',
            dataIndex: 'ErpGoodsCode',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '商品类别',
            dataIndex: 'ManageClassifyStr1',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '规格',
            dataIndex: 'PackingSpecification',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '单位',
            dataIndex: 'PackageUnit',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '件装量',
            dataIndex: 'PackageCount',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '生产厂商',
            dataIndex: 'BrandManufacturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '采购人员',
            dataIndex: 'PurchaserName',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
      selectTableData2: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          {
            name: '商品',
            type: 'input',
            value: '',
            key: 'keyWord',
            defaultVal: '',
            placeholder: '名称/编号/拼音码',
          },
        ],
        title: '选择赠品',
        name: '赠品',
        recordKey: 'Id',
        httpHead: 'P36006',
        isInitData: false,
        url: {
          list: '/{v}/GoodsManage/QueryGoodsAuditPasssList',
          listType: 'POST',
        },
        columns: [
          {
            title: '赠品名称',
            dataIndex: 'ErpGoodsName',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '规格',
            dataIndex: 'PackingSpecification',
            width: 80,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '单位',
            dataIndex: 'PackageUnit',
            width: 80,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '生产厂商',
            dataIndex: 'BrandManufacturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '商品编号',
            dataIndex: 'ErpGoodsCode',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          // {
          //   title: '平台商品编号',
          //   dataIndex: 'PlatformProductNo',
          //   width: 150,
          //   ellipsis: true,
          //   scopedSlots: { customRender: 'component' }
          // },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
      selectTableDataSale: {
        searchInput: [],
        title: '选择销售客户类型',
        name: '销售客户类型',
        recordKey: 'ItemValue',
        queryParam: {},
        httpHead: 'P31100',
        isInitData: false,
        url: {
          list: '/{v}/SystemTool/GetEnumsByName',
          listType: 'GET',
        },
        columns: [
          {
            title: '客户类型',
            dataIndex: 'ItemName',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
      selectCustomerTableData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          {
            name: '客户名称',
            type: 'input',
            value: '',
            key: 'KeyWord',
            defaultVal: '',
            placeholder: '请输入',
          },
        ],
        title: `选择客户名单`,
        name: '客户名单',
        recordKey: 'CustomerKeyValue',
        httpHead: 'P36007',
        isInitData: false,
        url: {
          list: '/{v}/PurchaseAgreement/GetErpCustomerList',
          listType: 'POST',
        },
        columns: CustomerColumns
      },
      columns: columns,
      columns2: columns2,
      CustomerColumns: CustomerColumns,
      CustomerImportColumns: CustomerImportColumns,
      DaKuanJEColumns: DaKuanJEColumns,
      searchImportGoodsInput: [
        {
          name: '商品编号', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'ErpGoodsCode', //搜索key 必填
          placeholder: '请输入商品编号',
        },
      ],
      searchImportCusInput: [
        {
          name: '客户名称', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'Name', //搜索key 必填
          placeholder: '请输入客户名称',
        },
      ],
      goodsImportColumns: goodsImportColumns,
      tableTitleArray: [],
      DaKuanJETableTitleArray: [],
      dataSource: [],
      dataSource2: [
        {
          num1: undefined,
          num2: undefined,
          AcceptanceThresholdValue: undefined,
          AcceptanceRebateValue: undefined,
          TelegraphicThresholdValue: undefined,
          TelegraphicRebateValue: undefined,
        },
      ],
      CustomerDataSource: [],
      loading: false,
      loading2: false,
      model: {
        SaleGoodsId: '',
        ErpGoodsName: '',
        AllowCustomerTypeList: [],
        AllowCustomerTypeListData: [],
        AllowAreaList: [],
        AllowAreaListData: [],
      },
      AccountingPriceArray: [],
      rItemObj: {
        GoodsList: [],
      },
      showFanliBtn: false, //是否显示返利按钮
      modelObj: {},
      zcModel: {},
      record: {},
      giftObj: {}, //赠品对象
      isCopy: null,
      recordIndex: null,
      //返利模式9
      flModel9: {
        ReturnGoodsUnitPric: null, //单价
        ReturnGoodsRebateRate: null, //返利百分比
      },
      type: null, //1年度协议 2短期销售协议
      confirmLoading: false,
      httpHead: 'P36007',
      url: {
        list: '',
        zcInfo: '/{v}/PurchaseAgreement/GetPolicyConfigListAsync',
        goodList: '/{v}/GoodsManage/QueryGoodsAuditPasssList',
        addLimitCustomerTmps: '/{v}/PurchaseAgreement/AddLimitCustomerTmps',//禁售客户临时表
      },
    }
  },
  computed: {
    rules() {
      return {
        shopData: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.modelObj.AccountingMethod != 3 && this.model.IsLimitGoods && this.dataSource.length === 0) {
                callback(new Error('请选择促销品种!'))
              } else if (this.modelObj.AccountingMethod == 3 && this.model.IsLimitGoods && this.dataSource.length === 0) {
                callback(new Error('请选择促销品种!'))
              } else {
                callback()
              }
            },
          },
        ],
        PurchaseAgreementPolicyId: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.rItemObj.Id) {
                callback(new Error('请选择返利模式!'))
              } else {
                callback()
              }
            },
          },
        ],
        SaleGoodsId: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.SaleGoodsId) {
                callback(new Error('请选择促销品种!'))
              } else {
                callback()
              }
            },
          },
        ],
        PolicyContent: [{ required: true, message: '请填写促销内容!' }],
        AccountingPrice: [{ required: true, message: '请填写供应商核算价!' }],
        IsGiftGoodsRebate: [{ required: true, message: '请选择赠品是否参与返利计算!' }],
        DesignatedType: [{ required: true, message: '请选择销售规则配置!' }],
        SaleCountStatisticType: [{ required: true, message: '请选择销售数量!' }],
        IsLimitGoods: [{
          required: true,
          validator: (rule, value, callback) => {
            if (this.modelObj.AccountingMethod == 3 && (this.model.IsLimitGoods == undefined || this.model.IsLimitGoods == null)) {
              callback(new Error('请选择促销品种类型!'))
            } else {
              callback()
            }
          },
        },],
        IsCasePaymentType: [{ required: true, message: '请选择打款金额设置!' }],
      }
    },
    showGoodTable() {
      let data = (this.modelObj.AccountingMethod == 3 && this.model.IsLimitGoods == true) || (this.modelObj.AccountingMethod != 3 && !this.rItemObj.HasIsLimitGoods) || (this.modelObj.AccountingMethod != 3 && this.rItemObj.HasIsLimitGoods && this.model.IsLimitGoods == true) ? true : false
      return data
    },
  },
  watch: {
    // PolicyStepMethod 1阶梯 2每满 3区间
    'model.PolicyStepMethod'(to) {
      let setDaKuanColumnsObj = this.setDaKuanColumns() || {}

      switch (to) {
        case 1:
          break;
        case 2:
          this.$set(this.model, 'RebateMaxValueType', 1)
          break;
        case 3:
          if (this.model.IsCasePaymentType && this.columns2.length > 3) {
            this.columns2[setDaKuanColumnsObj.DaKuanJEtableTitle1Index].scopedSlots.customRender = 'sectionInput'
            this.columns2[setDaKuanColumnsObj.DaKuanJEtableTitle3Index].scopedSlots.customRender = 'sectionInput'
          } else if (!this.model.IsCasePaymentType) {
            if (setDaKuanColumnsObj.tableTitle1Index > -1) { this.columns2[setDaKuanColumnsObj.tableTitle1Index].scopedSlots.customRender = 'sectionInput' }
          }
          // 初始化数据
          this.initDataSource2Data()
          break;
      }
    },
    // 打款金额设置
    'model.IsCasePaymentType'(to) {
      switch (to) {
        case true:
          this.columns2 = DaKuanJEColumns
          let setDaKuanColumnsObj = this.setDaKuanColumns() || {}
          if (this.model.PolicyStepMethod == 3) {
            this.columns2[setDaKuanColumnsObj.DaKuanJEtableTitle1Index].scopedSlots.customRender = 'sectionInput'
            this.columns2[setDaKuanColumnsObj.DaKuanJEtableTitle3Index].scopedSlots.customRender = 'sectionInput'
          }
          break
        case false:
          this.columns2 = columns2
          let setDaKuanColumnsObj2 = this.setDaKuanColumns() || {}
          if (this.model.PolicyStepMethod == 3) {
            this.columns2[setDaKuanColumnsObj2.tableTitle1Index].scopedSlots.customRender = 'sectionInput'
          }
          break
      }
      // 初始化数据
      this.initDataSource2Data()
    },
    'model.IsLimitGoods'(to) {
      switch (to) {
        case true:
          if (this.dataSource.length == 1 && JSON.stringify(this.dataSource[0]) == '{}') {
            this.dataSource = []
          }
          break
        case false:
          this.dataSource = []
          // 不限制品种默认是赠品
          if (this.model.HasReturnGoods) {
            this.model.ReturnGoodsMethod = 2
          } else {
            this.model.ReturnGoodsMethod = undefined
          }
          
          break
      }
    },
    // 返利模式类型
    'rItemObj.PolicyRebateMethod'(to) {
      let saleIndex = columns.findIndex(item => {
        return item.title == '销售门槛数量'
      })
      let operateIndex = columns.findIndex(item => {
        return item.title == '操作'
      })
      if (to == 10 || to == 11 || to == 12) {
        this.showFanliBtn = false
        if (operateIndex > -1 && saleIndex == -1) {
          let obj = {
            title: '销售门槛数量',
            dataIndex: 'SaleThresholdCount',
            width: 120,
            ellipsis: true,
            fixed: 'right',
            scopedSlots: { customRender: 'SaleThresholdCountInput' },
          }
          // 插入对象是在inidex的左边
          columns.splice(operateIndex, 0, obj)
        }
      } else {
        this.showFanliBtn = true
        if (operateIndex > -1 && saleIndex > -1) {
          // 删除对象元素
          columns.splice(operateIndex - 1, 1)
        }
      }
    },
    'rItemObj.HasIsLimitGoods'(to) {
      if (to) {
        // 如果没有值就默认IsLimitGoods为true
        if (this.model.IsLimitGoods === undefined) {
          this.$set(this.model, 'IsLimitGoods', true)
        }
      }
    },
    dataSource2(newV) {
      // 区间
      if (this.model.PolicyStepMethod == 3) {
        this.dataSource2.map((item, index) => {
          if (index == 0) {
            item.qujianText = '门槛'
            item.hideDel = true
          } else if (index == 1) {
            item.qujianText = '超出'
            item.hideDel = true
          } else if (index > 1) {
            item.qujianText = '超出'
            item.hideDel = undefined
          }
        })

      }
    },
  },
  mounted() { },
  created() {

  },
  methods: {
    moment,
    add(modelObj) {
      // AccountingMethod 1购进 2销售 3打款金额
      this.initAddShow()
      this.modelObj = modelObj
      this.visible = true
    },
    edit(modelObj, record, index, type) {
      if (type == 'copy') {
        this.isCopy = true
        this.initAddShow()
        this.title = '新增促销政策'
      } else {
        this.isCopy = false
        this.title = '编辑促销政策'
      }
      this.modelObj = modelObj
      this.recordIndex = index
      this.ipagination.current = 1
      this.type = Number(this.$route.query.type) //1年度协议 2短期销售协议
      this.record = Object.assign({}, record)
      this.rItemObj = lodash.cloneDeep(this.modelObj.rItemObj)
      this.model = Object.assign({}, this.modelObj.rItemObj)
      console.log('点击的政策2', modelObj, this.rItemObj, this.recordIndex, this.model)
      // 还原商品表格
      if (this.rItemObj.GoodsList && this.rItemObj.GoodsList.length > 0) {
        this.dataSource = []
        // 回显核算价格
        this.rItemObj.GoodsList.map((item) => {
          this.dataSource.push(item)
        })
        // 短期销售还原商品表格
        this.model.SaleGoodsId = this.dataSource[0].GoodsId
        this.model.ErpGoodsName = this.dataSource[0].ErpGoodsName
      }
      // 还原金额数据
      let moneyData = []
      if (this.rItemObj.StepList && this.rItemObj.StepList.length > 0) {
        moneyData = this.rItemObj.StepList.map((item) => {
          return {
            ...item,
            num1: item.ThresholdValue,
            num2: item.RebateValue,
          }
        })
      }

      // 还原承兑等金额 IsCasePaymentType true
      if (this.rItemObj.IsCasePaymentType == true && this.rItemObj.StepForCasePaymentList && this.rItemObj.StepForCasePaymentList.length > 0) {
        moneyData = this.rItemObj.StepForCasePaymentList.map((item) => {
          return item
        })
      }

      this.dataSource2 = moneyData

      // 还原返利上限
      if (this.model.RebateMaxValue || this.model.RebateMaxValue == 0) {
        this.$set(this.model, 'RebateMaxValueType', 2)
      } else if (this.model.RebateMaxValue == null) {
        this.$set(this.model, 'RebateMaxValueType', 1)
      }
      // 还原赠品
      this.giftObj = {
        ReturnGoodsName: this.rItemObj.ReturnGoodsName,
        id: this.rItemObj.ReturnGoodsId,
      }
      // 还原单价和返利比例
      this.flModel9 = {
        ReturnGoodsUnitPric: this.rItemObj.ReturnGoodsUnitPric, //单价
        ReturnGoodsRebateRate: this.rItemObj.ReturnGoodsRebateRate, //返利百分比
      }
      // 还原供应商核算价
      if (this.purchaseType == 1) {
        // 年度协议还原供应商核算价
        this.setAccountingPriceData(this.rItemObj)
      } else {
        // 短期销售协议还原供应商核算价
        this.$set(this.model, 'AccountingPrice', Number(this.record.AccountingPrice))
      }

      // 还原销售区域
      if (this.rItemObj.AllowAreaList && this.rItemObj.AllowAreaList.length > 0) {
        let list = []
        let keys = []
        this.rItemObj.AllowAreaList.map(item => {
          keys.push(item.AreaCode)
          list.push({
            Code: item.AreaCode,
            Name: item.AreaFullName,
            ...item,
          })
        })
        this.model.AllowAreaList = keys
        this.model.AllowAreaListData = list
      }

      // 还原销售客户类型
      if (this.rItemObj.AllowCustomerTypeList && this.rItemObj.AllowCustomerTypeList.length > 0) {
        let list = []
        let keys = []
        this.rItemObj.AllowCustomerTypeList.map(item => {
          keys.push(item.CustomerTypeValue)
          list.push({
            Code: item.CustomerTypeValue,
            Name: item.CustomerTypeName,
            ...item,
          })
        })
        this.model.AllowCustomerTypeList = keys
        this.model.AllowCustomerTypeListData = list
      }
      // 还原客户数据
      this.CustomerDataSource = this.rItemObj.LimitCustomerList || []
      // 还原核算金额
      this.setAccountingPriceData(this.rItemObj)


      this.getZcInfo()
      this.isEdit = true
      this.visible = true
    },
    initAddShow() {
      this.title = '新增促销政策'
      this.model = {
        SaleGoodsId: '',
        ErpGoodsName: '',
        AllowCustomerTypeList: [],
        AllowCustomerTypeListData: [],
        AllowAreaList: [],
        AllowAreaListData: [],
      }
      this.rItemObj = {}
      this.giftObj = {} //赠品对象
      this.ipagination.current = 1
      this.dataSource = []
      this.CustomerDataSource = []
      this.AccountingPriceArray = []
      this.isEdit = false
      this.type = Number(this.$route.query.type) //1年度协议 2短期销售协议
    },
    setDaKuanColumns() {
      let tableTitle1Index = this.columns2.findIndex(item => {
        return item.slots ? item.slots.title == 'tableTitle1' : false
      })
      let DaKuanJEtableTitle1Index = this.columns2.findIndex(item => {
        return item.slots ? item.slots.title == 'DaKuanJEtableTitle1' : false
      })
      let DaKuanJEtableTitle3Index = this.columns2.findIndex(item => {
        return item.slots ? item.slots.title == 'DaKuanJEtableTitle3' : false
      })
      // 初始化customRender
      if (this.model.IsCasePaymentType && this.columns2.length > 3) {
        this.columns2[DaKuanJEtableTitle1Index].scopedSlots.customRender = 'AcceptanceThresholdValueInput'
        this.columns2[DaKuanJEtableTitle3Index].scopedSlots.customRender = 'TelegraphicThresholdValueInput'
      } else if (!this.model.IsCasePaymentType) {
        if (tableTitle1Index > -1) { this.columns2[tableTitle1Index].scopedSlots.customRender = 'input' }
      }
      return {
        tableTitle1Index: tableTitle1Index,
        DaKuanJEtableTitle1Index: DaKuanJEtableTitle1Index,
        DaKuanJEtableTitle3Index: DaKuanJEtableTitle3Index,
      }
    },
    formatter(value) {
      return value.toString().replace(/\.00$/, '');
    },
    inputPrecision(index, column) {
      // console.log(88888, index, column, this.tableTitleArray)
      let num = 2
      let dataIndex = column.dataIndex
      let inputIndex = null
      let array = []
      switch (column.type) {
        case 'cxgz_two':
          array = ['num1', 'num2']
          inputIndex = array.indexOf(dataIndex)
          if (inputIndex > -1) {
            if (this.tableTitleArray[inputIndex].indexOf('数量') > -1) {
              num = 0
            } else {
              num = 2
            }
          }
          break
        case 'cxgz_four':
          array = ['AcceptanceThresholdValue', 'AcceptanceRebateValue', 'TelegraphicThresholdValue', 'TelegraphicRebateValue']
          inputIndex = array.indexOf(dataIndex)
          if (inputIndex > -1) {
            if (this.DaKuanJETableTitleArray[inputIndex].indexOf('数量') > -1) {
              num = 0
            } else {
              num = 2
            }
          }
          break

      }
      return num
    },
    // 选择客户
    chooseCus() {
      let queryParam = {}
      this.$refs.TableSelectCustomerDataModal.show(this.CustomerDataSource, queryParam)
    },
    initDataSource2Data() {
      if (this.rItemObj.StepList && this.rItemObj.StepList.length > 0) {
        return
      }
      if (this.rItemObj.StepForCasePaymentList && this.rItemObj.StepForCasePaymentList.length > 0) {
        return
      }
      if (this.model.PolicyStepMethod == 3) {
        // 区间
        this.dataSource2 = [
          {
            num1: undefined,
            num2: undefined,
            AcceptanceThresholdValue: undefined,
            AcceptanceRebateValue: undefined,
            TelegraphicThresholdValue: undefined,
            TelegraphicRebateValue: undefined,
          },
          {
            num1: undefined,
            num2: undefined,
            AcceptanceThresholdValue: undefined,
            AcceptanceRebateValue: undefined,
            TelegraphicThresholdValue: undefined,
            TelegraphicRebateValue: undefined,
          },
        ]
      } else {
        // 非区间
        this.dataSource2 = [
          {
            num1: undefined,
            num2: undefined,
            AcceptanceThresholdValue: undefined,
            AcceptanceRebateValue: undefined,
            TelegraphicThresholdValue: undefined,
            TelegraphicRebateValue: undefined,
          }
        ]
      }
    },
    changeArea(value, contents, type) {

    },
    changeIsLimitGoods() {
      this.dataSource = []
    },
    changeSaleTypeArray(value, contents, type) {
      this.model.AllowCustomerTypeList = value
      this.model.AllowCustomerTypeListData = contents
    },
    changeAllowArea(value, contents, type) {
      this.model.AllowAreaList = value
      this.model.AllowAreaListData = contents
    },
    chooseArea() {
      let area = []
      if (this.model.AllowAreaListData && this.model.AllowAreaListData.length > 0) {
        this.model.AllowAreaListData.map(item => {
          area.push({
            ...item,
            AreaCode: item.Code,
            AreaName: item.Name,
          })
        })
      }
      this.$refs.SelectAreaTree.show(area)
    },
    // 选择区域
    selectAreasModalOk(data) {
      if (!this.model.AllowAreaList) {
        this.$set(this.model, 'AllowAreaList', [])
      }
      if (!this.model.AllowAreaListData) {
        this.$set(this.model, 'AllowAreaListData', [])
      }
      let listKeys = []
      let list = []
      data.map(item => {
        listKeys.push(item.Code)
        list.push({
          Code: item.Code,
          Name: item.Name,
          IndexNum: 0,
        })
      })

      this.model.AllowAreaList = listKeys
      this.model.AllowAreaListData = list
    },
    blurInput(record, index, key, source) {
      if (source === null) {
        return
      }
      if (source == '区间') {
        let that = this
        let start = index - 1 < 0 ? 0 : index - 1
        let end = (index + 1 >= this.dataSource2.length) ? (this.dataSource2.length - 1) : index + 1
        // console.log(1111, index, start, end, key, source)
        if ((start == 0 && end == 1)) {
          // 第一行blur
          if ((index !== start && (that.dataSource2[start][key] && record[key] < that.dataSource2[start][key])) || ((that.dataSource2[end][key] && record[key] > that.dataSource2[end][key]) && index !== end)) {
            this.$message.warning('区间模式下门槛金额不能大于超出金额')
            record[key] = null
          }
        } else if ((start == 0 && end == 2)) {
          // 第二行blur
          if ((index !== start && (that.dataSource2[start][key] && record[key] < that.dataSource2[start][key])) || ((that.dataSource2[end][key] && record[key] >= that.dataSource2[end][key]) && index !== end)) {
            this.$message.warning('区间模式下第一个超出金额不能小于门槛金额')
            record[key] = null
          }
        } else {
          // 其他行blur
          if ((index !== start && (that.dataSource2[start][key] && record[key] <= that.dataSource2[start][key])) || ((that.dataSource2[end][key] && record[key] >= that.dataSource2[end][key]) && index !== end)) {
            this.$message.warning('区间模式下数据应为递增添加')
            record[key] = null
          }
        }
      } else {
        if (this.dataSource2.length > 1) {
          let that = this
          let start = index - 1 < 0 ? 0 : index - 1
          let end = (index + 1 >= this.dataSource2.length) ? (this.dataSource2.length - 1) : index + 1
          if ((index !== start && (that.dataSource2[start][key] && record[key] <= that.dataSource2[start][key])) || ((that.dataSource2[end][key] && record[key] >= that.dataSource2[end][key]) && index !== end)) {
            this.$message.warning('阶梯模式下数据应为递增添加')
            record[key] = null
          }
        }
      }

    },
    importGoods() {
      if (!this.rItemObj.Id) {
        this.$message.warning('请先选择返利模式')
        return
      }
      let isRadio = this.rItemObj.IsMultipleSelectGoods ? false : true
      this.goodsImportConfig.onlySaveOneData = isRadio
      this.$refs.iBatchImportGoodsModal.show(this.dataSource)
    },
    importCus() {
      if (!this.rItemObj.Id) {
        this.$message.warning('请先选择返利模式')
        return
      }
      this.$refs.iBatchImportCusModal.show(this.CustomerDataSource)
    },
    // 导入保存
    handleBatchImportModalOk(data, type) {
      if (data.length > 0) {
        if (type == 'goods') {
          this.setOkGoods(data)
          if (this.rItemObj.IsMultipleSelectGoods) {
            this.dataSource = this.dataSource.concat(data)
          } else {
            this.dataSource = data
          }
        } else if (type == 'cus') {
          this.CustomerDataSource = this.CustomerDataSource.concat(data)
        }

      }
    },
    setOkGoods(data) {
      if (data && data.length > 0) {
        // 默认返利
        if (this.showFanliBtn) {
          data.map(item => {
            this.$set(item, 'IsRebate', true)
          })
        }
      }
      // 商品数量大于1，只能选择赠品
      if (this.dataSource && this.dataSource.length > 1) {
        if (this.rItemObj.HasReturnGoods == true) {
          this.model.ReturnGoodsMethod = 2
        }
      }
    },
    chooseSaleType() {
      let data = this.model.AllowCustomerTypeListData || []
      data.map(item => {
        item.ItemValue = item.Code
        item.ItemName = item.Name
      })
      let queryParam = {
        enumName: 'EnumAgentCustomerType',
      }
      this.$refs.TableSelectSaleTypeDataModal.show(data, queryParam)
    },
    changeSearchInput(val, name, item) {
      this.model.SaleGoodsId = val
      this.model.ErpGoodsName = name
      this.dataSource = [item]
      // this.$refs.form.validateField(['SaleGoodsId'])
    },
    // 获取当前的政策数据
    getZcInfo() {
      let formData = {
        accountingMethod: this.modelObj.AccountingMethod,
        configId: this.rItemObj.PurchaseAgreementPolicyConfigId,
      }
      this.confirmLoading = true
      getAction(this.url.zcInfo, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data && res.Data.length > 0) {
              // 重组数据
              this.rItemObj = res.Data[0] || {}
              this.tableTitleArray = [this.rItemObj.ThresholFieldDescription, this.rItemObj.RebateFieldDescription]
              this.DaKuanJETableTitleArray = [
                '承兑' + this.rItemObj.ThresholFieldDescription,
                '承兑' + this.rItemObj.RebateFieldDescription,
                '电汇' + this.rItemObj.ThresholFieldDescription,
                '电汇' + this.rItemObj.RebateFieldDescription,
              ]

              // 还原促销规则配置
              // StepMethodType 1 无 2 仅阶梯 3 阶梯和每满 4 阶梯和区间 5 阶梯每满和区间
              // PolicyStepMethod 1阶梯 2每满
              switch (this.model.PolicyStepMethod) {
                case 3:
                  break
                default:
                  if (this.rItemObj.StepMethodType == 1) {
                    this.model.PolicyStepMethod = null
                  } else if (this.rItemObj.StepMethodType == 2) {
                    // 仅阶梯时候默认为阶梯并且清空返利上限
                    this.model.PolicyStepMethod = 1
                    this.$set(this.model, 'RebateMaxValue', null)
                    this.$set(this.model, 'RebateMaxValueType', null)
                  }
                  break
              }

            }
          } else {
            this.$message.warning(res.Msg)
          }
        }).finally(() => {
          this.confirmLoading = false
        })
    },
    // 新增一行
    addRow() {
      if (!this.model.IsCasePaymentType) {
        let notHaveData = this.dataSource2.find(item => {
          return !item.num1 || !item.num2
        })
        if (notHaveData) {
          this.$message.warning('请先填写完善未填写的数据')
          return
        }
      }
      if (this.model.IsCasePaymentType == true) {
        let notHaveData = this.dataSource2.find(item => {
          return !item.AcceptanceThresholdValue || !item.AcceptanceRebateValue || !item.TelegraphicThresholdValue || !item.TelegraphicRebateValue
        })
        if (notHaveData) {
          this.$message.warning('请先填写完善未填写的数据')
          return
        }
      }
      if (this.dataSource2.length >= 10) {
        this.$message.warning('规则配置最多有10行')
        return
      }
      let index = 0
      this.dataSource2.push({
        index: index++,
        num1: undefined,
        num2: undefined,
        AcceptanceThresholdValue: undefined,
        AcceptanceRebateValue: undefined,
        TelegraphicThresholdValue: undefined,
        TelegraphicRebateValue: undefined,
      })
    },
    subDel(record, index, type) {
      if (type == 'delShop') {
        let haveFindIndex = this.dataSource.findIndex(item => {
          return item.GoodsId == record.GoodsId
        })
        if (haveFindIndex > -1) {
          this.dataSource.splice(haveFindIndex, 1)
        } else {
          this.dataSource.splice(index, 1)
        }
      } else if (type == 'delMoney') {
        this.dataSource2.splice(index, 1)
      } else if (type == 'delCus') {
        let haveFindIndex = this.CustomerDataSource.findIndex(item => {
          return item.CustomerKeyValue == record.CustomerKeyValue
        })
        if (haveFindIndex > -1) {
          this.CustomerDataSource.splice(haveFindIndex, 1)
        } else {
          this.CustomerDataSource.splice(index, 1)
        }
      }
    },
    // 操作
    operate(record, type, index) {
      if (type == 'reset') {
        if (this.model.IsCasePaymentType == true) {
          this.dataSource2[index].AcceptanceThresholdValue = null
          this.dataSource2[index].AcceptanceRebateValue = null
          this.dataSource2[index].TelegraphicThresholdValue = null
          this.dataSource2[index].TelegraphicRebateValue = null
        } else {
          this.dataSource2[index].num1 = null
          this.dataSource2[index].num2 = null
        }
      } else if (type == 'delShop') {
        let that = this
        this.$confirm({
          title: '您确定要移除该商品吗?',
          content: '',
          onOk() {
            that.subDel(record, index, type)
          },
          onCancel() { },
        })
      } else if (type == 'delCus') {
        let that = this
        this.$confirm({
          title: '您确定要移除该客户吗?',
          content: '',
          onOk() {
            that.subDel(record, index, type)
          },
          onCancel() { },
        })
      } else if (type == 'delMoney') {
        if (this.dataSource2.length == 1) {
          this.$message.warning('首行数据不能移除,可点击重置或者手动清除数据')
          return
        }
        let that = this
        this.$confirm({
          title: '您确定要移除这行金额吗?',
          content: '',
          onOk() {
            that.subDel(record, index, type)
          },
          onCancel() { },
        })
      } else if (type == 'fanLi') {

        let that = this
        this.$confirm({
          title: '您确定要设置该商品为返利吗?',
          content: '',
          onOk() {
            that.$set(record, 'IsRebate', true)
          },
          onCancel() { },
        })
      } else if (type == 'noFanLi') {
        let haveAllIsRebate = []
        this.dataSource.map(item => {
          if (item.IsRebate) {
            haveAllIsRebate.push(item)
          }
        })
        if (haveAllIsRebate.length <= 1) {
          this.$message.warning('至少需要有一个商品返利')
          return
        }

        let that = this
        this.$confirm({
          title: '您确定要设置该商品为不返利吗?',
          content: '',
          onOk() {
            that.$set(record, 'IsRebate', false)
          },
          onCancel() { },
        })
      }
    },
    clearTableData() {
      this.dataSource = []
    },
    // 清空客户列表
    clearCustomerData() {
      this.CustomerDataSource = []
    },
    // 选择赠品
    chooseGift() {
      let userId = Vue.ls.get(USER_ID) || null
      let queryParam = {
        AuthBusinessStatus: 2,
        // PurchaserNameId: userId,
        IsAll: true,
        PartyFirstId: this.modelObj.PartyFirstId,
      }
      this.$refs.TableSelectGiftDataModal.show([], queryParam)
    },
    // 选择商品
    chooseTableData() {
      if (!this.rItemObj.Id) {
        this.$message.warning('请先选择返利模式')
        return
      }
      let dataSource = this.dataSource || []
      // Id赋值等同于GoodsId
      dataSource.map((item) => {
        item.Id = item.GoodsId ? item.GoodsId : item.Id
      })
      let userId = Vue.ls.get(USER_ID) || null
      let queryParam = {
        AuthBusinessStatus: 2,
        // PurchaserNameId: userId,
        IsAll: true,
        PartyFirstId: this.modelObj.PartyFirstId,
      }
      this.$refs.TableSelectDataModal.show(dataSource, queryParam)
    },
    // 选择的数据
    chooseData(data, type) {
      if (data && data.length > 0) {
        this.setOkGoods(data)
        if (type == 'radio') {
          this.dataSource = data
        } else {
          this.dataSource = this.dataSource.concat(data)
        }
      }
      // 本品disabled的时候需要默认为赠品
      if (this.rItemObj.HasReturnGoods) {
        let isDisabledBenPin = (this.dataSource.length == 1 && JSON.stringify(this.dataSource[0]) != '{}') ? false : true
        if (isDisabledBenPin) {
          this.model.ReturnGoodsMethod = 2
        }
      }
      this.$refs.form.validateField(['shopData'])
    },
    // 选择赠品的数据
    chooseData2(data) {
      console.log('选择赠品的数据', data)
      let ReturnGoodsName =
        (data[0].ErpGoodsName || '') +
        ' | ' +
        (data[0].PackingSpecification || '') +
        ' | ' +
        (data[0].PackageUnit || '') +
        ' | ' +
        (data[0].BrandManufacturer || '') +
        ' | ' +
        (data[0].ErpGoodsCode || '')
      this.giftObj = {
        Id: data[0].Id,
        ReturnGoodsName: ReturnGoodsName,
      }
      this.model.ReturnGoodsMethod = 2
      this.model.ReturnGoodsId = this.giftObj.Id
      this.model.ReturnGoodsName = this.giftObj.ReturnGoodsName
    },
    chooseSaleTypeData(data) {
      let list = []
      let listKeys = []
      if (data.length > 0) {
        data.map(item => {
          list.push({ Code: item.ItemValue, Name: item.ItemName, EnumCustomerType: item.ItemValue })
          listKeys.push(item.ItemValue)
        })
        if (!this.model.AllowCustomerTypeList) {
          this.$set(this.model, 'AllowCustomerTypeList', [])
        }
        if (!this.model.AllowCustomerTypeListData) {
          this.$set(this.model, 'AllowCustomerTypeListData', [])
        }
        this.model.AllowCustomerTypeList = this.model.AllowCustomerTypeList.concat(listKeys)
        this.model.AllowCustomerTypeListData = this.model.AllowCustomerTypeListData.concat(list)
      }
    },
    // 选择销售/禁售客户
    chooseCustomerData(data) {
      if (data && data.length > 0) {
        this.CustomerDataSource = this.CustomerDataSource.concat(data)
      }
      let formData = this.CustomerDataSource
      postAction(this.url.addLimitCustomerTmps, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {

          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {

        })
    },
    chooseRebate(rItemObj) {
      this.$refs.PurchaseDealRebateModal.show(this.modelObj, rItemObj)
    },
    changeMoneyInput() {
      if (this.model.RebateMaxValue) {
        this.$set(this.model, 'RebateMaxValueType', 2)
      }
    },
    changeIsCasePaymentType() {

    },
    purchaseDealRebateOk(data) {
      console.log('选择的返利模式', data, this.dataSource)
      this.model = {
        PolicyContent: this.model.PolicyContent,
        SaleGoodsId: this.model.SaleGoodsId,
        ErpGoodsName: this.model.ErpGoodsName,
        PurchaseAgreementPolicyId: data.Id,
      }
      this.flModel9 = {
        ReturnGoodsUnitPric: null,
        ReturnGoodsRebateRate: null,
      }
      this.dataSource2 = [
        {
          index: 0,
          num1: undefined,
          num2: undefined,
          AcceptanceThresholdValue: undefined,
          AcceptanceRebateValue: undefined,
          TelegraphicThresholdValue: undefined,
          TelegraphicRebateValue: undefined,
        },
      ]

      // 清空供应商核算价
      if (this.dataSource.length > 0 && JSON.stringify(this.dataSource[0]) != '{}') {
        this.dataSource.map((item) => {
          this.$set(item, 'AccountingPrice', null)
          this.$set(item, 'IsRebate', true)
        })
      }
      
      // 返利模式从【组合条件】变成非组合条件时，即多变少，需要清空商品列表数据
      if (+this.rItemObj.AccountingConditions === 7 && +data.AccountingConditions !== 7) {
        this.dataSource = []
      }
      
      this.rItemObj = data
      // 门槛金额columns
      this.tableTitleArray = [this.rItemObj.ThresholFieldDescription, this.rItemObj.RebateFieldDescription]
      this.DaKuanJETableTitleArray = [
        '承兑' + this.rItemObj.ThresholFieldDescription,
        '承兑' + this.rItemObj.RebateFieldDescription,
        '电汇' + this.rItemObj.ThresholFieldDescription,
        '电汇' + this.rItemObj.RebateFieldDescription,
      ]

      // 默认第一项
      // 阶梯和每满
      // StepMethodType 1 无 2 仅阶梯 3 阶梯和每满 4 阶梯和区间 5 阶梯每满和区间
      if (this.rItemObj.Title && (this.rItemObj.StepMethodType !== 1)) {
        if ([2, 3, 4, 5].includes(this.rItemObj.StepMethodType)) {
          this.$set(this.model, 'PolicyStepMethod', 1)
        } else if (this.rItemObj.StepMethodType !== 2 || [3, 5].includes(this.rItemObj.StepMethodType)) {
          this.$set(this.model, 'PolicyStepMethod', 2)
        } else {
          this.$set(this.model, 'PolicyStepMethod', 1)
        }
      }
      // 本品和赠品
      if (this.rItemObj.HasReturnGoods) {
        if (this.dataSource.length == 1) {
          this.$set(this.model, 'ReturnGoodsMethod', 1)
        } else {
          this.$set(this.model, 'ReturnGoodsMethod', 2)
        }
      } else {
        this.$set(this.model, 'ReturnGoodsMethod', undefined)
      }

      // 默认促销品种
      if (this.rItemObj.HasIsLimitGoods && !this.model.IsLimitGoods) {
        this.$set(this.model, 'IsLimitGoods', true)
      }

      // 默认赠品是否参与返利计算
      if (this.purchaseType == 1 || this.purchaseType == 2) {
        this.$set(this.model, 'IsGiftGoodsRebate', true)
      }
      // 默认打款金额设置
      if (this.modelObj.AccountingMethod == 3) {
        this.$set(this.model, 'IsCasePaymentType', true)
      }

      // 重选政策清空赠品
      this.giftObj = {} //赠品对象
      // 返利上限 每满的时候显示没有上限
      if (this.model.PolicyStepMethod == 2 || this.model.RebateMaxValue) {
        this.model.RebateMaxValueType = 1
      }

      // 核算金额
      this.setAccountingPriceData(data)
      this.tableTitleArray = [this.rItemObj.ThresholFieldDescription, this.rItemObj.RebateFieldDescription]
      this.$refs.form.validateField(['PurchaseAgreementPolicyId'])
    },
    // 核算金额
    setAccountingPriceData(data) {
      if (data.AccountingConditions == 2 || data.PolicyRebateMethod == 6) {
        if (this.columns[5].title !== '供应商核算价') {
          let obj = {
            title: '供应商核算价',
            dataIndex: 'AccountingPrice',
            width: 150,
            ellipsis: true,
            fixed: 'right',
            scopedSlots: { customRender: 'input' },
          }
          this.columns.splice(5, 0, obj)
        }
      } else {
        if (this.columns[5].title === '供应商核算价') {
          this.columns.splice(5, 1)
        }
      }
    },
    onTypeChange(e, type) {
      if (type == 'cxgz') {
        this.dataSource2 = [
          {
            index: 0,
            num1: undefined,
            num2: undefined,
            AcceptanceThresholdValue: undefined,
            AcceptanceRebateValue: undefined,
            TelegraphicThresholdValue: undefined,
            TelegraphicRebateValue: undefined,
          },
        ]
        this.$set(this.model, 'RebateMaxValue', null)
        this.$set(this.model, 'RebateMaxValueType', null)
      } else if (type == 'flpz') {
        // 返利品种
        if (this.model.ReturnGoodsMethod == 1) {
          // 本品
          this.giftObj = {}
          this.model.ReturnGoodsId = null
          this.model.ReturnGoodsName = null
        }
      } else if (type == 'flsx') {
        // 返利上限
        if (this.model.RebateMaxValueType == 1) {
          // 没有上限
          this.model.RebateMaxValue = null
        }
      }
    },
    checkData() {
      // 促销规则配置
      if (this.rItemObj.Title && this.rItemObj.StepMethodType !== 1) {
        let dataSource2CountArray = []
        if (!this.model.PolicyStepMethod) {
          this.$message.warning('请选择促销规则配置')
          return false
        }
        let title1 = ''
        let title2 = ''
        let titleArray = []
        if (this.modelObj.AccountingMethod == 3 && this.model.IsCasePaymentType == true) {
          title1 = this.DaKuanJETableTitleArray[0] + '、' + this.DaKuanJETableTitleArray[1]
          title2 = this.DaKuanJETableTitleArray[2] + '、' + this.DaKuanJETableTitleArray[3]
          titleArray = [this.DaKuanJETableTitleArray[0], this.DaKuanJETableTitleArray[1], this.DaKuanJETableTitleArray[2], this.DaKuanJETableTitleArray[3]]
        } else {
          title1 = this.tableTitleArray[0]
          title2 = this.tableTitleArray[1]
          titleArray = [this.tableTitleArray[0], this.tableTitleArray[1]]
        }
        if (this.dataSource2.length == 0) {
          this.$message.warning('请填写完善' + title1 + '和' + title2)
          return false
        }

        // 校验多行数据是否未填写
        if (this.dataSource2.length >= 1) {
          let emptyArray = []
          for (let i = 0; i < this.dataSource2.length; ++i) {
            for (let j = 0; j < titleArray.length; ++j) {
              dataSource2CountArray.push({
                ...this.dataSource2[i],
                ['title' + [j]]: titleArray[j]
              })
            }

            if (this.modelObj.AccountingMethod == 3 && this.model.IsCasePaymentType == true) {
              if (!this.dataSource2[i].AcceptanceThresholdValue || !this.dataSource2[i].AcceptanceRebateValue || !this.dataSource2[i].TelegraphicThresholdValue || !this.dataSource2[i].TelegraphicRebateValue) {
                emptyArray.push(i)
                break
              }
            } else {
              if (!this.dataSource2[i].num1 || !this.dataSource2[i].num2) {
                emptyArray.push(i)
                break
              }
            }
          }
          if (emptyArray.length > 0) {
            this.$message.warning(
              '请填写完善' + title1 + '和' + title2 + ',多行数据请填写完整'
            )
            return false
          }
          // 判断数量为正整数
          let emptyCountArray = []
          let baifenbiArray = []
          dataSource2CountArray.map(item => {
            if (titleArray.length == 2) {
              titleArray.map((rItem, rIndex) => {
                let num = ''
                switch (rIndex) {
                  case 0: num = 'num1'
                    break;
                  case 1: num = 'num2'
                    break;
                }
                if (item['title' + rIndex] && item['title' + rIndex].indexOf('数量') > -1 && item[num] && (item[num] % 1 !== 0 || item[num] < 0)) {
                  emptyCountArray.push(item['title' + rIndex])
                }
                if (item['title' + rIndex] && (item['title' + rIndex].indexOf('比例') > -1 || item['title' + rIndex].indexOf('%') > -1) && item[num] && item[num] > 100) {
                  baifenbiArray.push(item['title' + rIndex])
                }
              })
            } else if (titleArray.length == 4) {
              titleArray.map((rItem, rIndex) => {
                let num = ''
                switch (rIndex) {
                  case 0: num = 'AcceptanceThresholdValue'
                    break;
                  case 1: num = 'AcceptanceRebateValue'
                    break;
                  case 2: num = 'TelegraphicThresholdValue'
                    break;
                  case 3: num = 'TelegraphicRebateValue'
                    break;
                }
                if (item['title' + rIndex] && item['title' + rIndex].indexOf('数量') > -1 && item[num] && (item[num] % 1 !== 0 || item[num] < 0)) {
                  emptyCountArray.push(item['title' + rIndex])
                }
                if (item['title' + rIndex] && (item['title' + rIndex].indexOf('比例') > -1 || item['title' + rIndex].indexOf('%') > -1) && item[num] && item[num] > 100) {
                  baifenbiArray.push(item['title' + rIndex])
                }
              })
            }
          })
          // 去重
          let uniqueArray = emptyCountArray.filter((item, index) => emptyCountArray.indexOf(item) === index);
          let baifenbUniqueArray = baifenbiArray.filter((item, index) => baifenbiArray.indexOf(item) === index);
          if (emptyCountArray.length > 0) {
            this.$message.warning(uniqueArray.join(',') + '为数量，必须为正整数')
            return
          }
          if (baifenbUniqueArray.length > 0) {
            this.$message.warning(baifenbUniqueArray.join(',') + '为百分比比例,必须小于100%')
            return
          }
        }
        // PolicyStepMethod 1阶梯 2每满 3区间
        switch (this.model.PolicyStepMethod) {
          case 1:
            // 判断阶梯的时候是否递增
            if (this.dataSource2.length > 1) {
              let key = ''
              let title = ''
              if (this.modelObj.AccountingMethod == 3 && this.model.IsCasePaymentType == true) {
                key = 'AcceptanceThresholdValue'
                title = this.DaKuanJETableTitleArray[0]
              } else {
                key = 'num1'
                title = this.tableTitleArray[0]
              }
              const isIncreasing = this.dataSource2.every((item, index, arr) => {
                if (index === 0) return true; // 第一个元素没有前一项
                return arr[index - 1][key] < item[key]; // 判断前一项是否小于当前项
              });
              if (!isIncreasing) {
                this.$message.warning('阶梯模式下,返利规则配置' + title + '必须递增')
                return false
              }
            }
            break;
          case 2:
            break;
          case 3:
            break;

        }

      }

      // 判断是否有供应商核算价
      let haveHeSuanPrice = this.columns.find(item => {
        return item.title === '供应商核算价'
      })

      // 判断是否有销售门槛数量
      let haveSaleCount = this.columns.find(item => {
        return item.title === '销售门槛数量'
      })

      if (this.dataSource.length > 0) {
        let emptyArray = []
        for (let i = 0; i < this.dataSource.length; ++i) {
          if (haveHeSuanPrice) {
            if (!this.dataSource[i].AccountingPrice) {
              emptyArray.push(i)
              break
            }
          } else if (haveSaleCount) {
            if (!this.dataSource[i].SaleThresholdCount) {
              emptyArray.push(i)
              break
            }
          }
        }
        if (emptyArray.length > 0) {
          if (haveHeSuanPrice) {
            this.$message.warning('返利模式为核算金额,请填写完善供应商核算价')
          } else if (haveSaleCount) {
            this.$message.warning('请填写完善销售门槛数量')
          }
          return false
        }
      }

      // 返利品种
      if (this.rItemObj.HasReturnGoods) {
        if (!this.model.ReturnGoodsMethod) {
          this.$message.warning('请选择返利品种')
          return false
        }
        if (this.model.ReturnGoodsMethod == 2) {
          if (!this.model.ReturnGoodsId || !this.model.ReturnGoodsName) {
            this.$message.warning('返利品种选择赠品后请选择赠品')
            return false
          }
        }
      }
      // 返利上限
      if (this.rItemObj.HasRebateMax) {
        if (this.model.PolicyStepMethod == 2 && !this.model.RebateMaxValueType) {
          this.$message.warning('请选择返利上限')
          return false
        }
        if (this.model.RebateMaxValueType == 2) {
          if (!this.model.RebateMaxValue) {
            this.$message.warning('返利上限大于上限金额请填写上限金额')
            return false
          }
        }
      }
      if (this.rItemObj.Title && this.rItemObj.PolicyRebateMethod == 9) {
        if (!this.flModel9.ReturnGoodsUnitPric) {
          this.$message.warning('请填写单价')
          return false
        }
        if (!this.flModel9.ReturnGoodsRebateRate) {
          this.$message.warning('请填写返利比例(百分比点数)')
          return false
        }
      }

      // 判断至少需要有一个商品返利
      if (this.showFanliBtn) {
        let checkFanli = this.dataSource.every(obj => !obj.IsRebate);
        if (this.dataSource.length > 0 && JSON.stringify(this.dataSource[0]) != '{}' && checkFanli) {
          this.$message.warning('至少需要有一个商品返利')
          return false
        }
      }

      return true
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          // 不限制品种的时候清空列表 
          if (this.rItemObj.HasIsLimitGoods && !this.model.IsLimitGoods) {
            this.dataSource = []
          }
          if (this.model.DesignatedType === 2 && !this.CustomerDataSource.length) {
            this.$message.warning('请选择客户')
            return
          }
          if (!this.checkData()) {
            return
          }
          // 购进和销售显示商品
          this.model.dataSource = this.dataSource
          // 返利模式
          // 组装数据
          this.model.rItemObj = this.rItemObj
          this.model.dataSource2 = this.dataSource2
          let isAdd = this.isEdit == true ? false : true
          let zcData = this.setZcData()
          console.log('zcData', zcData)
          that.$emit('ok', this.model, this.record, isAdd, zcData, this.isCopy)
          that.close()
        }
      })
    },
    setZcData() {
      // 商品列表
      let goodsList = []
      if (this.model.dataSource.length > 0) {
        goodsList = this.model.dataSource.map((item) => {
          return {
            ...item,
            GoodsId: item.Id || item.GoodsId,
            ErpGoodsCode: item.ErpGoodsCode,
            ErpGoodsName: item.ErpGoodsName,
            BrandManufacturer: item.BrandManufacturer,
            PackingSpecification: item.PackingSpecification,
            PackageUnit: item.PackageUnit,
            PackageCount: item.PackageCount,
            AccountingPrice: item.AccountingPrice || 0,
          }
        })
      }

      // AccountingConditions
      // 以我方采购金额为条件 = 1
      // 以供应商核算金额为条件 = 2
      // 以我方打款金额为条件 = 6
      // 以我方采购数量为条件 = 3
      // 以我方销售数量为条件 = 4
      // 以我方销售客户数为条件 = 5

      // PolicyRebateMethod
      // 返固定金额 = 1
      // 按每盒商品返金额 = 2
      // 按每家客户返金额 = 3

      // 返指定数量的本品或赠品 = 4

      // 按采购金额或进价的百分比 = 5
      // 按核算金额金额或核算价的百分比 = 6
      // 按打款金额的百分比 = 7

      // 指定数量的商品且每盒返单价的百分比 = 9
      // 金额 返 金额
      let AllowAreaList = []
      let AllowCustomerTypeList = []
      let LimitCustomerList = []
      let StepForCasePaymentList = []
      let stepListArray = []

      if (!this.model.IsCasePaymentType) {
        stepListArray = this.dataSource2.map((item, index) => {
          return {
            IndexNum: index,
            ThresholdValue: item.num1,
            RebateValue: item.num2,
          }
        })
      }

      if (this.model.IsCasePaymentType) {
        if (this.dataSource2 && this.dataSource2.length > 0) {
          StepForCasePaymentList = this.dataSource2.map((item, index) => {
            return {
              ...item,
              IndexNum: index,
            }
          })
        }
      }

      if (this.recordIndex == null) {
        this.recordIndex = this.modelObj.dataSource.length
      }

      if (this.model.AllowAreaListData && this.model.AllowAreaListData.length > 0) {
        AllowAreaList = this.model.AllowAreaListData.map(item => {
          return {
            IndexNum: item.IndexNum || 0,
            AreaCode: item.Code,
            AreaFullName: item.Name,
          }
        })
      }

      if (this.model.AllowCustomerTypeListData && this.model.AllowCustomerTypeListData.length > 0) {
        AllowCustomerTypeList = this.model.AllowCustomerTypeListData.map(item => {
          return {
            IndexNum: item.IndexNum || 0,
            CustomerTypeName: item.Name,
            CustomerTypeValue: item.Code,
          }
        })
      }

      if (this.CustomerDataSource && this.CustomerDataSource.length > 0) {
        LimitCustomerList = this.CustomerDataSource.map(item => {
          return {
            IndexNum: item.IndexNum || 0,
            CustomerName: item.CustomerName,
            CustomerKeyValue: item.CustomerKeyValue,
            RegAddress: item.RegAddress,
          }
        })
      }

      // modelObj.AccountingMethod 1购进 2销售 3打款
      let zcData = {
        IndexNum: 0,
        PurchaseAgreementPolicyConfigId: this.model.rItemObj.PurchaseAgreementPolicyConfigId || this.model.rItemObj.Id, //政策配置ID
        PolicyContent: this.model.PolicyContent, //促销内容
        AccountingConditions: this.model.rItemObj.AccountingConditions, //核算条件
        PolicyRebateMethod: this.model.rItemObj.PolicyRebateMethod, //返利模式
        PolicyStepMethod: this.model.PolicyStepMethod, //促销规则配置
        ReturnGoodsMethod: this.model.ReturnGoodsMethod, //返回商品模式
        RebateMaxValue: this.model.RebateMaxValue, //是否返利上限 上限值
        RebateMaxValueType: this.model.RebateMaxValueType, //返利上限值
        ReturnGoodsId: this.model.ReturnGoodsId, //返利品种商品名称Id,只有选择赠品，才有值
        ReturnGoodsName: this.model.ReturnGoodsName, //返利品种商品名称,只有选择赠品，才有值
        ReturnGoodsUnitPric: this.flModel9.ReturnGoodsUnitPric || null, //单价
        ReturnGoodsRebateRate: this.flModel9.ReturnGoodsRebateRate || null, //返利百分比
        IsGiftGoodsRebate: this.model.IsGiftGoodsRebate,//赠品是否参与返利
        SaleCountStatisticType: this.model.SaleCountStatisticType || undefined,//销售数量统计类型
        SaleCountStatisticTypeStr: this.model.SaleCountStatisticType == 1 ? '按总数' : (this.model.SaleCountStatisticType == 2 ? '按单条' : undefined),//销售数量统计类型值
        IsLimitGoods: this.model.IsLimitGoods,//是否限制品种
        IsCasePaymentType: this.model.IsCasePaymentType,//打款金额设置 - 是否区分打款类型
        AcceptancePaymentLimitRate: this.model.AcceptancePaymentLimitRate,//打款金额设置 - 不区分类型 - 承兑打款不得超过整体打款金额{0}%
        ReceivableReturnRebateRate: this.model.ReceivableReturnRebateRate,//打款金额设置 - 不区分类型 - 承兑打款超过打款金额的，只返应收返利的{0}%
        GoodsList: goodsList, //商品列表
        StepList: stepListArray.length > 0 ? stepListArray : [],
        StepForCasePaymentList: StepForCasePaymentList,//政策配置 - 阶梯配置列表 区分打款类型用该列表
        DesignatedType: this.model.DesignatedType,//销售规则配置
        AllowAreaList: AllowAreaList || [],//政策配置 - 销售区域
        AllowCustomerTypeList: AllowCustomerTypeList || [],//政策配置 - 销售客户类型
        LimitCustomerList: LimitCustomerList || [],//政策配置 - 禁售客户
        RemoveLimitCustomerIdList: [],//删除的禁售客户Id列表

      }
      return zcData
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      console.log(this.rItemObj)
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>
<style lang="scss" scoped>
.rebate-choose {
  width: 100%;
  background: rgba(242, 242, 242, 1);
  border: 1px solid rgba(215, 215, 215, 1);
  border-radius: 5px;
  padding: 20px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.rule-box {
  display: flex;
  align-items: end;
  justify-content: space-between;
  margin-top: 10px;
}

/* 设置table行的高度防止错位 */
::v-deep .ant-table-tbody tr {
  height: 46px !important;
}
</style>
