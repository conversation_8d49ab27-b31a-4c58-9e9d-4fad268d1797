<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :footer="null" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff'
      }">
      <a-spin :spinning="confirmLoading">
        <div>
          <QualificationsListComponents ref="qualificationsListComponents" :row-data="rowData" :showTitleCol="false" :isEdit="isEdit" />
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script>
import moment from 'moment'
const QualificationsListComponents = () => import('@/components/yyl/components/QualificationsListComponents')
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'PurchaseContractPhotoModal',
  components: { QualificationsListComponents, },
  data() {
    return {
      title: '查看采购合同原件照片',
      visible: false,
      isEdit: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 }
      },
      rowData: {},
      confirmLoading: false,
      httpHead: 'P36007',
      url: {
        info: '/{v}/PurchaseContract/GetInfoAsync',
      }
    }
  },
  mounted() { },
  created() {

  },
  methods: {
    moment,
    show(record) {
      this.visible = true
      this.rowData = {}
      this.getInfo(record)
    },
    getInfo(record) {
      let formData = {
        Id: record.Id
      }
      getAction(this.url.info, formData, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          if (res.Data && res.Data.FileList.length > 0) {
            res.Data.FileList = res.Data.FileList.map((item) => {
              return {
                FileUrl: item.FileUrl,
                SignFileUrl: null,
              }
            })
          }
          this.rowData = {
            tabList: [{
              "Title": "",
              "Sum": null,
              "Files": res.Data.FileList
            }]
          }
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {

      });
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    }
  }
}
</script>
