<!--
 * @Description: 短期采购协议 列表 - 补录协议
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: wenjingGao
 * @LastEditTime: 2025-04-23 17:29:47
-->
<template>
  <a-modal
    :title="title"
    :width="'90vw'"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    @ok="handleOK"
    :maskClosable="false"
    :destroyOnClose="true"
    okText="提交审核"
  >
    <div
      style="background: #fbe6e9; padding: 10px 16px; color: red; margin-bottom: 24px"
      v-if="model.AuditStatus == 3 && model.AuditOpinion"
    >
      <span>驳回原因：{{ model.AuditOpinion || '' }}</span>
    </div>
    <a-form-model ref="form" :rules="rules" :model="model">
      <a-row style="margin-bottom: 10px">
        <a-col :span="24">
          <a-form-model-item label="供应商名称：" prop="PartyFirstId" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <SingleInputSearchView
              placeholder="搜索名称/编号/拼音码"
              width="100%"
              :httpParams="{
                pageIndex: 1,
                pageSize: 200,
                AuditStatus: 2,
                IsValid: true,
              }"
              keyWord="KeyWord"
              httpHead="P36003"
              :Url="url.supplierList"
              :value="model.PartyFirstId"
              :dataKey="{ name: 'Name', value: 'Id' }"
              :name="model.PartyFirstName"
              :isAbsolute="true"
              @clear="
                () => {
                  model.PartyFirstId = ''
                  model.PartyFirstName = ''
                  model.SupplementaryPurchaseShortAgreementDetails = []
                }
              "
              @change="changeSearchInput"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="24">
          <a-form-model-item
            label="采购订单："
            prop="SupplementaryPurchaseShortAgreementDetails"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-button
              type="primary"
              @click="
                $refs.SelectSupplementaryPurchaseShortAgreementOrderModal.show(
                  model.PartyFirstId,
                  model.SupplementaryPurchaseShortAgreementDetails || []
                )
              "
              style="margin-bottom: 20px"
              >选择订单</a-button
            >
            <TableView
              :showCardBorder="false"
              :tab="tab"
              :columns="columns"
              :dataList="model.SupplementaryPurchaseShortAgreementDetails || []"
            />
          </a-form-model-item>
        </a-col>
        <a-col span="24" style="text-align: right; font-weight: bold; padding-right: 20px"
          >合计返利金额： {{ total < 0 ? '-' : '' }}¥{{ total < 0 ? String(total).replace('-', '') : total }}</a-col
        >
        <a-col :span="24">
          <a-form-model-item label="补录原因：" prop="Remark" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input placeholder="请输入" v-model="model.Remark" style="width: 100%" :maxLength="100" />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <template slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <YYLButton
        menuId=""
        text="保存草稿"
        type="primary"
        :loading="confirmLoading"
        @click="handleOK(true)"
        class="ml8"
      />
      <YYLButton
        menuId=""
        text="提交审核"
        type="primary"
        :loading="confirmLoading"
        @click="handleOK(false)"
        class="ml8"
      />
    </template>
    <!-- 选择采购订单 -->
    <SelectSupplementaryPurchaseShortAgreementOrderModal
      ref="SelectSupplementaryPurchaseShortAgreementOrderModal"
      @ok="handleSelectSupplementaryPurchaseShortAgreementOrderModalOk"
    />
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { postAction, getAction } from '@/api/manage'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PurchaseReplacementAgreementModal',
  mixins: [ListMixin, EditMixin],
  components: {},
  props: {},
  data() {
    return {
      title: '补录协议',
      visible: false,
      confirmLoading: false,
      tab: {
        tabTitles: [],
        showPagination: false,
      },
      orderId: '',
      model: {
        PartyFirstId: '',
        PartyFirstName: '',
        SupplementaryPurchaseShortAgreementDetails: [],
        Remark: '',
      },
      total: 0,
      labelCol: {
        xs: { span: 2 },
        sm: { span: 2 },
      },
      wrapperCol: {
        xs: { span: 22 },
        sm: { span: 22 },
      },
      isInitData: false,
      httpHead: 'P36007',
      refresh: true,
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          // fixed: 'left',
          width: 150,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 120,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 120,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 180,
        },
        {
          title: '产地',
          dataIndex: 'Producer',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '采购数量',
          dataIndex: 'PurchaseQuantity',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '含税金额',
          dataIndex: 'TaxIncludedPurchasePriceTotal',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '每盒返利金额',
          dataIndex: 'BoxRebateAmount',
          width: 150,
          fixed: 'right',
          precision: 6, //数值精度
          // min: 0,
          onBlur: this.onBlur,
          scopedSlots: { customRender: 'inputNumber' },
          ellipsis: true,
        },
        {
          title: '返利到账天数',
          dataIndex: 'RebateDays',
          width: 120,
          fixed: 'right',
          // precision: 0, //数值精度
          onBlur: this.onBlur,
          scopedSlots: { customRender: 'inputNumber' },
          ellipsis: true,
        },
      ],
      url: {
        supplierList: '/{v}/Supplier/GetSuppliersForSelect', //获取供应商接口
        save: '/{v}/SupplementaryPurchaseShortAgreement/SaveAgreementAsync', //保存草稿接口
        submit: '/{v}/SupplementaryPurchaseShortAgreement/SubmitAsync', //提交审核接口
        detail: '/{v}/SupplementaryPurchaseShortAgreement/GetSupplementaryAgreementDetail', // 查询详情接口
      },
    }
  },
  computed: {
    rules() {
      return {
        PartyFirstId: [
          {
            required: true,
            message: '请选择供应商名称!',
          },
        ],
        // SupplementaryPurchaseShortAgreementDetails: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) => {
        //       if (this.model.SupplementaryPurchaseShortAgreementDetails.length === 0) {
        //         callback('请选择订单')
        //       } else if (this.model.SupplementaryPurchaseShortAgreementDetails.every(v=>!v['BoxRebateAmount'] ||!v['RebateDays'])) {
        //         callback('请填写表格中的信息')
        //       }  else {
        //         callback()
        //       }
        //     },
        //   },
        // ],
        Remark: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (this.model.Remark) {
                this.validStrMaxLength(rule, this.model.Remark, callback, 100)
              } else {
                callback()
              }
            },
          },
        ],
      }
    },
  },
  watch: {
    model: {
      handler(newVal, oldVal) {
        this.getTotalAmount()
      },
      deep: true,
    },
  },
  mounted() {},
  created() {},
  methods: {
    show(Id) {
      this.model = {
        PartyFirstId: '',
        PartyFirstName: '',
        SupplementaryPurchaseShortAgreementDetails: [],
        Remark: '',
      }
      if (Id) {
        // 编辑
        this.getInfo(Id)
      }
      this.visible = true
      // this.loadData(1)
    },
    // 查询详情
    getInfo(Id) {
      this.confirmLoading = true
      getAction(this.url.detail, { id: Id }, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              let item = {
                Id: res.Data['Id'],
                PurchaseOrderNo: res.Data.PurchaseOrderNo,
                PurchaseOrderId: res.Data.PurchaseOrderId,
                PartyFirstId: res.Data.PartyFirstId,
                PartyFirstName: res.Data.PartyFirstName,
                SupplementaryPurchaseShortAgreementDetails: res.Data['Details'] || [],
                Remark: res.Data.Remark,
                AuditOpinion: res.Data.AuditOpinion,
                AuditStatus: res.Data.AuditStatus,
              }
              this.$set(this, 'model', item)
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    changeSearchInput(val, txt) {
      this.$set(this.model, 'PartyFirstId', val)
      this.$set(this.model, 'PartyFirstName', txt)
      if (!val) {
        this.$set(this.model, 'SupplementaryPurchaseShortAgreementDetails', [])
      }
      this.$refs.form.clearValidate()
    },
    // 选择采购订单
    handleSelectSupplementaryPurchaseShortAgreementOrderModalOk(list) {
      console.log('handleSelectSupplementaryPurchaseShortAgreementOrderModalOk', list)
      if ((list || []).length) {
        let Details = list[0].Details || []
        let arr = []
        Details.map((v) => {
          v.RebateDays = null
          v.BoxRebateAmount = null
          arr.push(v)
        })
        this.$set(this.model, 'PurchaseOrderNo', list[0].PurchaseOrderNo)
        this.$set(this.model, 'PurchaseOrderId', list[0].PurchaseOrderId)
        this.$set(this.model, 'SupplementaryPurchaseShortAgreementDetails', arr)
        this.$refs.form.clearValidate()
      }
    },
    /**
     * 获取总金额
     */
    getTotalAmount() {
      let sum = 0

      if ((this.model.SupplementaryPurchaseShortAgreementDetails || []).length) {
        this.model.SupplementaryPurchaseShortAgreementDetails.forEach((x) => {
          if (x['BoxRebateAmount'] && x['PurchaseQuantity']) {
            sum += x['BoxRebateAmount'] * x['PurchaseQuantity']
          }
        })
      }

      this.total = this.getAmountOfMoney(sum, 6)
    },
    /**
     * @param {*} val
     * @param {*} key
     * @param {*} index
     */
    onBlur(val, key, record, index) {
      this.$forceUpdate()
      //计算调价金额
      this.getTotalAmount()
      this.$refs.form.clearValidate()
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
    // IsDraft 是否保存草稿 true 草稿  false 提交审核
    handleOK(IsDraft) {
      if (IsDraft) {
        //草稿只验证供应商
        if (!this.model.PartyFirstId) {
          this.$message.warning('请选择供应商')
          return
        }
        // this.$refs.form.validateField('PartyFirstId',(err) => {
        //   if (!err) {
        this.saveData(IsDraft)
        //   }
        // })
        return
      }

      this.$refs.form.validate((err, values) => {
        if (err) {
          if (this.model.SupplementaryPurchaseShortAgreementDetails.length === 0) {
            this.$message.warning('请选择订单')
            return
          }
          if (
            this.model.SupplementaryPurchaseShortAgreementDetails.every(
              (v) => !v['BoxRebateAmount'] || !v['RebateDays']
            )
          ) {
            this.$message.warning('请填写表格中的信息')
            return
          }
          this.saveData(IsDraft)
        }
      })
    },
    // 组装提交数据
    saveData(IsDraft) {
      let data = {
        // "Id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        PartyFirstId: this.model.PartyFirstId,
        PurchaseOrderNo: this.model.PurchaseOrderNo,
        PurchaseOrderId: this.model.PurchaseOrderId,
        Remark: this.model.Remark,
      }
      if (this.model['Id']) {
        data['Id'] = this.model['Id']
      }
      if (this.model['SupplementaryPurchaseShortAgreementDetails'].length) {
        let arr = []
        this.model['SupplementaryPurchaseShortAgreementDetails'].map((v) => {
          let item = {
            // "Id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
            PurchaseOrderDetailId: v['PurchaseOrderDetailId'],
            PurchaseOrderDetailRowIndex: v['PurchaseOrderDetailRowIndex'],
            BoxRebateAmount: v['BoxRebateAmount'],
            RebateDays: v['RebateDays'],
          }
          if (v['Id']) {
            item['Id'] = v['Id']
          }
          arr.push(item)
        })
        data['SupplementaryPurchaseShortAgreementDetails'] = arr
      }
      // console.log('提交的数据   ',data)
      this.postData(IsDraft, data)
    },
    // 提交数据 请求接口
    postData(IsDraft, data) {
      let that = this
      that.confirmLoading = true
      postAction(IsDraft ? that.url.save : that.url.submit, data, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            // if (IsDraft) { // 草稿
            that.confirmLoading = false
            that.$message.success('操作成功！')
            that.$emit('ok')
            that.handleCancel()
            // } else {
            //   // 创建审批流
            //   // that.createApproval(res.Data)
            // }
          } else {
            that.$message.error(res.Msg)

            that.confirmLoading = false
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    /**
     * 创建审核实例
     */
    createApproval(bNo) {
      let that = this
      let params = {
        BussinessNo: bNo,
        Scenes: 36,
        OpratorId: that.getLoginUserId(),
      }
      if (!that.confirmLoading) {
        that.confirmLoading = true
      }
      postAction('/{v}/ApprovalWorkFlowInstance/CreateApproval', params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.$emit('ok')
            that.handleCancel()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
  },
}
</script>
<style lang="less"></style>
