<template>
  <a-modal :title="title" :width="1100" :visible="visible" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true" :footer="null">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-tabs default-active-key="1" @change="tabsChange">
        <a-tab-pane key="1" tab="汇总统计">
          <a-row :getter="24">
            <a-descriptions :column="2">
              <a-descriptions-item :label="
                  '已完成' +
                  (tableTitleArray[0] && tableTitleArray[0].indexOf('(') > -1
                    ? tableTitleArray[0].substring(0, tableTitleArray[0].indexOf('('))
                    : tableTitleArray[0]||'')
                " v-if="
                  model.PolicyBindConfig.AccountingConditions != 4 && model.PolicyBindConfig.AccountingConditions != 5 && AccountingMethodType != 3
                ">
                {{ model.TotalCompletedValue || model.TotalCompletedValue == 0 ? model.TotalCompletedValue : '--' }}
              </a-descriptions-item>
              <a-descriptions-item label="已完成销售数量" v-if="
                  model.PolicyBindConfig.AccountingConditions == 4 ||
                  (model.PolicyBindConfig.AccountingConditions == 5 && model.PolicyBindConfig.PolicyRebateMethod == 3)
                ">
                {{
                  model.TotalSalesThresholdCount || model.TotalSalesThresholdCount == 0
                    ? model.TotalSalesThresholdCount
                    : '--'
                }}
              </a-descriptions-item>
              <a-descriptions-item label="已完成客户数量" v-if="
                  model.PolicyBindConfig.AccountingConditions == 5 ||
                  (model.PolicyBindConfig.AccountingConditions == 4 && model.PolicyBindConfig.PolicyRebateMethod == 3)
                ">
                {{
                  model.TotalCustomerThresholdCount || model.TotalCustomerThresholdCount == 0
                    ? model.TotalCustomerThresholdCount
                    : '--'
                }}
              </a-descriptions-item>
              <a-descriptions-item label="对应采购金额" v-if="model.IsShowTotalPurchaseAmount">
                {{ model.TotalPurchaseAmount || model.TotalPurchaseAmount == 0 ? model.TotalPurchaseAmount : '--' }}
              </a-descriptions-item>

              <a-descriptions-item label="电汇打款金额" v-if="AccountingMethodType == 3">
                {{ model.TelegraphicPaymentAmount || model.TelegraphicPaymentAmount == 0 ? model.TelegraphicPaymentAmount : '--' }}
              </a-descriptions-item>
              <a-descriptions-item label="承兑打款金额" v-if="AccountingMethodType == 3">
                {{ model.AcceptancePaymentAmount || model.AcceptancePaymentAmount == 0 ? model.AcceptancePaymentAmount : '--' }}
              </a-descriptions-item>
              <a-descriptions-item label="应收返利金额" v-if="model.PolicyBindConfig.PolicyRebateMethod != 4">
                {{ model.TotalRebateAmount || model.TotalRebateAmount == 0 ? model.TotalRebateAmount : '--' }}
              </a-descriptions-item>
              <!-- 应收返货 -->
              <a-descriptions-item :label="'应收' + tableTitleArray[1]" v-if="model.PolicyBindConfig.PolicyRebateMethod == 4">
                {{ model.TotalRebateCount || model.TotalRebateCount == 0 ? model.TotalRebateCount : '--' }}
              </a-descriptions-item>
              <!-- 应收返钱 -->
              <a-descriptions-item :label="'应收' + tableTitleArray[1]" v-if="!model.PolicyBindConfig.PolicyRebateMethod == 4">
                {{ model.TotalRebateAmount || model.TotalRebateAmount == 0 ? model.TotalRebateAmount : '--' }}
              </a-descriptions-item>

            </a-descriptions>
          </a-row>
          <!-- ReturnGoodsList 应收返利品种 返指定数量的本品或赠品 PolicyRebateMethod= 4 -->
          <div v-if="
              model.PolicyBindConfig.PolicyRebateMethod == 4 &&
              model.ReturnGoodsList &&
              model.ReturnGoodsList.length > 0
            ">
            <div style="margin: 8px 0">应收返利品种：</div>
            <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns0" :dataSource="dataSource0" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource0.length === 0 ? false : '350px', x: '1000px' }">
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
            </a-table>
          </div>
          <!-- PolicyStepMethod 1阶梯 2每满 -->
          <!-- AccountingMethod 1 购进 2 销售 3 打款金额
           AccountingConditions
            以我方采购金额为条件 = 1
            以供应商核算金额为条件 = 2
            以我方打款金额为条件 = 6
            以我方采购数量为条件 = 3
            以我方销售数量为条件 = 4
            以我方销售客户数为条件 = 5

            PolicyRebateMethod
            返固定金额 = 1
            按每盒商品返金额 = 2
            按每家客户返金额 = 3
            返指定数量的本品或赠品 = 4
            按采购金额或进价的百分比 = 5
            按核算金额金额或核算价的百分比 = 6
            按打款金额的百分比 = 7
            指定数量的商品且每盒返单价的百分比 = 9 -->
          <div style="margin: 8px 0" v-if="AccountingMethodType == 3">
            打款金额设置：<span>{{ model.PolicyBindConfig.IsCasePaymentType == true?'区分打款类型':(model.PolicyBindConfig.IsCasePaymentType == false?'不区分打款类型':'--') }}</span>
          </div>
          <div style="margin: 8px 0" v-if="AccountingMethodType == 3 && model.PolicyBindConfig.IsCasePaymentType == false">
            <span>承兑打款不得超过整体打款金额的：</span>
            <a-input-number style="margin: 0 5px" disabled v-model="model.PolicyBindConfig.AcceptancePaymentLimitRate" placeholder="请输入" />%,
            <span>否则只返应收返利的</span>
            <a-input-number style="margin: 0 5px" disabled v-model="model.PolicyBindConfig.ReceivableReturnRebateRate" placeholder="请输入" />%
          </div>
          <!-- 单价 PolicyRebateMethod为9 -->
          <div style="margin: 8px 0;color: rgba(0, 0, 0, 0.85)" v-if="model.PolicyBindConfig.PolicyRebateMethod == 9">
            单价：{{ model.PolicyBindConfig.ReturnGoodsUnitPric }}
          </div>
          <div style="margin: 8px 0;color: rgba(0, 0, 0, 0.85)" v-if="model.PolicyBindConfig.PolicyRebateMethod == 9">
            返利比例(百分比点数)：{{ model.PolicyBindConfig.ReturnGoodsRebateRate }}
          </div>
          <div style="margin: 8px 0" v-if="model.PolicyBindConfig.PolicyStepMethod == 1">
            阶梯促销规则完成情况：<span v-if="model.PolicyBindConfig.ReturnGoodsMethod">(返利品种：{{ model.PolicyBindConfig.ReturnGoodsMethod == 1 ? '本品' : giftGoods() }})</span>
          </div>
          <div style="margin: 8px 0" v-if="model.PolicyBindConfig.PolicyStepMethod == 2">
            每满促销规则完成情况：<span v-if="model.PolicyBindConfig.ReturnGoodsMethod">(返利品种：{{ model.PolicyBindConfig.ReturnGoodsMethod == 1 ? '本品' : giftGoods() }})</span>
          </div>
          <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length === 0 ? false : '350px', x: '900px' }">
            <span slot="tableTitle1">
              {{ tableTitleArray[0] }}
            </span>
            <span slot="tableTitle2">
              {{ tableTitleArray[1] }}
            </span>
            <span slot="DaKuanJEtableTitle1">
              {{ DaKuanJETableTitleArray[0] }}
            </span>
            <span slot="DaKuanJEtableTitle2">
              {{ DaKuanJETableTitleArray[1] }}
            </span>
            <span slot="DaKuanJEtableTitle3">
              {{ DaKuanJETableTitleArray[2] }}
            </span>
            <span slot="DaKuanJEtableTitle4">
              {{ DaKuanJETableTitleArray[3] }}
            </span>
            <!-- 完成状态 -->
            <template slot="IsCompleted" slot-scope="text">
              <span :style="{ color: text == true ? 'green' : 'red' }">{{ text == true ? '已完成' : '未完成' }}</span>
            </template>
            <!-- 字符串超长截取省略号显示-->
            <template slot="component" slot-scope="text, record, index, column">
              <j-ellipsis :style="{color:column.color}" :value="'' + text" v-if="typeof text == 'number'" />
              <j-ellipsis :style="{color:column.color}" :value="text" v-else />
            </template>
          </a-table>

          <div style="margin: 8px 0" v-if="model.IsShowStatisticsList">月度完成情况：</div>
          <a-table :bordered="true" ref="table" v-if="model.IsShowStatisticsList" :rowKey="(record, index) => index" :columns="columns2" :dataSource="dataSource1_1" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length === 0 ? false : '350px', x: '900px' }">
            <span slot="tableTitle3"> 已完成{{ tableTitleArray[0] }} </span>
            <!-- 字符串超长截取省略号显示-->
            <template slot="component" slot-scope="text">
              <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
              <j-ellipsis :value="text" v-else />
            </template>
          </a-table>
        </a-tab-pane>

        <a-tab-pane key="2" tab="执行明细" force-render>
          <a-form>
            <a-row :gutter="0">
              <div v-if="AccountingMethodType == 1">
                <a-col :md="queryCol">
                  <a-form-item label="单据编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input style="width: 100%" placeholder="请输入" v-model="queryParam.BusinessNo"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :md="queryCol">
                  <a-form-item label="供应商" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input style="width: 100%" placeholder="名称/编号" v-model="queryParam.SupplierKey"></a-input>
                  </a-form-item>
                </a-col>
              </div>
              <div v-if="AccountingMethodType == 2">
                <a-col :md="queryCol">
                  <a-form-item label="客户" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input style="width: 100%" placeholder="名称" v-model="queryParam.CustomerKey"></a-input>
                  </a-form-item>
                </a-col>
              </div>
              <a-col :md="queryCol" v-if="AccountingMethodType == 3">
                <a-form-item label="打款单号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input style="width: 100%" placeholder="请输入" v-model="queryParam.BusinessNo"></a-input>
                </a-form-item>
              </a-col>

              <a-col :md="queryCol">
                <a-form-item label="商品" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input style="width: 100%" placeholder="请输入" v-model="queryParam.GoodsrKey"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="queryCol">
                <a-form-item label="时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-range-picker v-model="rangeDate" @change="onTimeChange" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :md="queryCol" v-if="AccountingMethodType == 2">
                <a-form-item label="采购渠道" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input style="width: 100%" placeholder="请输入" v-model="queryParam.PurchaseChannel"></a-input>
                </a-form-item>
              </a-col>

              <a-col :md="queryCol" v-if="AccountingMethodType == 3">
                <a-form-item label="打款方式" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <EnumSingleChoiceView style="width: 100%" placeholder="请选择" dictCode="EnumRemittanceType" v-model="queryParam.RemittanceType" />
                </a-form-item>
              </a-col>
              <a-col style="text-align: right" :md="queryCol * ([1,2].includes(AccountingMethodType) ? 2 : 3)">
                <span class="table-page-search-submit-btns">
                  <a-button @click="myHandleExportXls" icon="download" style="margin-right: 8px">导出</a-button>
                  <a-button @click="searchReset" icon="reload" style="margin-right: 8px">重置</a-button>
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
          <a-table style="margin-top: 15px" :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns3" :dataSource="dataSource2" :pagination="ipagination" :rowClassName="rowClassNameFun" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource2.length === 0 ? false : '350px', x: '900px' }">
            <!-- 单据编号 -->
            <template slot="order" slot-scope="text, record">
              <a @click="goOrder(record)">{{ text }}</a>
            </template>
            <!-- 字符串超长截取省略号显示-->
            <template slot="component" slot-scope="text">
              <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
              <j-ellipsis :value="text" v-else />
            </template>
            <!-- 操作 -->
            <span slot="action" slot-scope="text, record, index">
              <a style="margin: 0 5px" @click="operate(record, 'delShop', index)">移除</a>
            </span>
          </a-table>
          <div style="margin-top:5px;">合计金额：¥{{ amountTotalVal == 0 || amountTotalVal?amountTotalVal:'--'}}</div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
const columnsBase = [
  {
    // title: '采购金额(门槛金额)',
    slots: { title: 'tableTitle1' },
    dataIndex: 'ThresholdValue',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    // title: '返利金额(固定金额)',
    slots: { title: 'tableTitle2' },
    dataIndex: 'RebateValue',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '完成状态',
    dataIndex: 'IsCompleted',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'IsCompleted' },
  },
]
const DaKuanJEColumns = [
  {
    slots: { title: 'DaKuanJEtableTitle1' },
    dataIndex: 'AcceptanceThresholdValue',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    slots: { title: 'DaKuanJEtableTitle2' },
    dataIndex: 'AcceptanceRebateValue',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '承兑完成状态',
    dataIndex: 'AcceptanceIsCompleted',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'IsCompleted' },
  },
  {
    slots: { title: 'DaKuanJEtableTitle3' },
    dataIndex: 'TelegraphicThresholdValue',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    slots: { title: 'DaKuanJEtableTitle4' },
    dataIndex: 'TelegraphicRebateValue',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '电汇完成状态',
    dataIndex: 'TelegraphicIsCompleted',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'IsCompleted' },
  },
]

export default {
  name: 'PurchaseDealCarryOutInfo',
  mixins: [ListMixin],
  components: {
    JEllipsis,
  },
  props: {
    type: {
      type: Number,
      default: 1, //1年度协议 2短期销售协议
    },
  },
  data() {
    return {
      title: '促销政策执行情况详情',
      visible: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      model: {
        PurchaseAgreementPolicyId: '',
        CompletedTime: '',
        TotalCompletedValue: 0,
        TotalSalesThresholdCount: 0,
        TotalCustomerThresholdCount: 0,
        TotalRebateAmount: 0,
        TotalRebateCount: 0,
        PolicyBindConfig: {
          GiftGoodsInfo: {},
        },
        PolicyStepList: [],
        StatisticsList: [],
        ReturnGoodsList: [],
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      queryCol: 8,
      queryParam: {},
      rangeDate: [],
      columns0: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          fixed: 'left',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收数量',
          dataIndex: 'ReceivableRebateCount',
          width: 150,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns: columnsBase,
      columns2: [
        {
          title: '月份',
          dataIndex: 'Month',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          // title: '已完成采购金额',
          slots: { title: 'tableTitle3' },
          dataIndex: 'CompletedValue',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns3: [],
      columns3_buy: [
        {
          title: '单据编号',
          dataIndex: 'BusinessNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'order' },
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessOrderTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '进价',
        //   dataIndex: 'Price',
        //   width: 150,
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'component' },
        // },
        {
          title: '采购单价',
          dataIndex: 'Price',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购数量',
          dataIndex: 'Count',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '合计金额',
          dataIndex: 'Amount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'BusinessTime',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns3_Sale: [
        {
          title: '客户',
          dataIndex: 'CustomerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户类型',
          dataIndex: 'CustomerType',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户地区',
          dataIndex: 'CustomerArea',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'GoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackingUnit',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'ManufacturerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'BatchNumber',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'GoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购单价',
          dataIndex: 'PurchasePrice',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '销售单价',
          dataIndex: 'SalePrice',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '销售数量',
          dataIndex: 'Count',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '合计金额',
          dataIndex: 'Amount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '时间',
          dataIndex: 'OrderTime',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购渠道',
          dataIndex: 'PurchaseChannel',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      tableTitleArray: [],
      DaKuanJETableTitleArray: [],
      dataSource0: [],
      dataSource: [],
      dataSource1: [],
      dataSource2: [],
      dataSource1_1: [],
      record: {},
      infoData: {},
      AccountingMethodType: '', //1 购进 2 销售 3 打款金额
      amountTotalVal: null, //执行明细 合计金额
      showFanliBtn: false, //是否显示返利按钮
      isInitData: false,
      httpHead: 'P36007',
      url: {
        info1: '/{v}/ExecutionOrder/GetAgreementPolicyExecutionAsync',
        info2: '/{v}/ExecutionOrder/GetAgreementPolicyExecutionDetailAsync',
        info2DaKuan: '/{v}/ExecutionOrder/GetAgreementPolicyExecutionRemittanceDetailAsync',
        info2Cus: '/{v}/ExecutionOrder/GetAgreementPolicyExecutionFlowDetailAsync',
        list: '',
      },
      exportUrlAll: {
        '2': '/{v}/ExecutionOrder/ExportAgreementPolicyExecutionFlowDetail',
        '1_1': '/{v}/ExecutionOrder/ExportAgreementPolicyExecutionDetailAsync',
        '1_2': '/{v}/ExecutionOrder/ExportAgreementPolicyExecutionFlowDetail',
        '1_3': '/{v}/ExecutionOrder/ExportAgreementPolicyExecutionRemittanceDetailAsync',
      },
      listRecord: {} // 顶层操作表格行数据
    }
  },
  computed: {
    rules() {
      return {
        Name: [{ required: true, message: '请输入角色!' }],
      }
    },
    AccountingMethodTypeName() {
      let result = ''
      const AccountingMethodType = this.AccountingMethodType
      switch (AccountingMethodType) {
        case 1:
          result += '购进'
          break;
        case 2:
          result += '销售'
          break;
        case 3:
          result += '打款'
          break;
      }
      if (this.model.PolicyBindConfig.AccountingConditions == 2 || this.model.PolicyBindConfig.PolicyRebateMethod == 6) {
        result += '-核算'
      } else if (AccountingMethodType === 1) {
        result += '-采购'
      }
      return result
    },
    exportUrlAllKey() {
      let result = ''
      // AccountingMethodType的对应值   //1年度协议 2短期销售协议
      const type = this.type
      // AccountingMethodType的对应值   1 购进 2 销售 3 打款金额
      const AccountingMethodType = this.AccountingMethodType
      if (type === 2) {
        result = type
      } else if (type === 1) {
        result = type + '_' + AccountingMethodType
      }
      return result
    },
    exportName() {
      let result = '年度协议促销政策执行情况详情'
      const type = this.type
      const AccountingMethodType = this.AccountingMethodType
      if (type === 2) {
        result = '短期销售协议促销政策执行情况详情'
      } else if (type === 1) {
        result += `(${this.AccountingMethodTypeName})`
      }
      return result
    }
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    // 导出
    myHandleExportXls() {
      const that = this
      const queryParam = Object.assign({}, that.queryParam)
      const listRecord = this.listRecord
      let AgreementStartTime = moment(listRecord.AgreementStartTime).format('YYYYMMDD')
      let AgreementEndTime = moment(listRecord.AgreementEndTime).format('YYYYMMDD')
      const nameExport = `${listRecord.PartyFirstName}-执行明细${AgreementStartTime}${AgreementEndTime}`
      // 导出年度协议购进的时候需要通过这个IsAccounting字段判断区分采购还是核算
      if (this.model.PolicyBindConfig.AccountingConditions == 2 || this.model.PolicyBindConfig.PolicyRebateMethod == 6) {
        queryParam.IsAccounting = true
      }
      this.handleExportXls(
        nameExport,
        'Get',
        that.exportUrlAll[that.exportUrlAllKey],
        null,
        // this.linkHttpHead,
        that.httpHead,
        queryParam
      )
    },
    show(record, infoData, listRecord) {
      this.visible = true
      this.record = record
      this.listRecord = listRecord
      // AccountingMethod 1购进 2销售
      // console.log('infoData', infoData)
      this.infoData = infoData
      // AccountingMethod 1 购进 2 销售 3 打款金额
      this.AccountingMethodType = this.infoData.AccountingMethod
      this.getInfo()
      switch (this.AccountingMethodType) {
        case 1:
          this.columns3 = this.columns3_buy
          break
        case 2:
          this.columns3 = this.columns3_Sale
          break
        default:
          this.columns3 = this.columns3_buy
          break
      }
    },
    getInfo() {
      let formData = {
        purchaseAgreementPolicyId: this.record.PurchaseAgreementPolicyId,
      }
      getAction(this.url.info1, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.model = res.Data
            this.dataSource0 = this.model.ReturnGoodsList
            this.dataSource1 = this.model.StatisticsList
            this.dataSource1_1 = this.model.StatisticsList
            this.tableTitleArray = [
              this.model.PolicyBindConfig.ThresholFieldDescription,
              this.model.PolicyBindConfig.RebateFieldDescription,
            ]
            this.DaKuanJETableTitleArray = [
              '承兑' + this.model.PolicyBindConfig.ThresholFieldDescription,
              '承兑' + this.model.PolicyBindConfig.RebateFieldDescription,
              '电汇' + this.model.PolicyBindConfig.ThresholFieldDescription,
              '电汇' + this.model.PolicyBindConfig.RebateFieldDescription,
            ]

            // 打款金额设置
            switch (this.model.PolicyBindConfig.IsCasePaymentType) {
              case true:
                this.columns = Object.assign([], DaKuanJEColumns)
                this.dataSource = this.model.PolicyStepForCasePaymentList
                break
              case false:
                this.columns = Object.assign([], columnsBase)
                this.dataSource = this.model.PolicyStepList
                break
            }
            // 条件判断
            // AccountingMethod 1 购进 2 销售 3 打款金额
            // AccountingConditions
            // 以我方采购金额为条件 = 1
            // 以供应商核算金额为条件 = 2
            // 以我方打款金额为条件 = 6
            // 以我方采购数量为条件 = 3
            // 以我方销售数量为条件 = 4
            // 以我方销售客户数为条件 = 5

            // PolicyRebateMethod
            // 返固定金额 = 1
            // 按每盒商品返金额 = 2
            // 按每家客户返金额 = 3

            // 返指定数量的本品或赠品 = 4

            // 按采购金额或进价的百分比 = 5
            // 按核算金额金额或核算价的百分比 = 6
            // 按打款金额的百分比 = 7
            // 指定数量的商品且每盒返单价的百分比 = 9
            if (this.model.PolicyBindConfig.PolicyStepMethod == 1 && !this.model.PolicyBindConfig.IsCasePaymentType) {
              // 阶梯
              let obj = {
                title: '完成状态',
                dataIndex: 'IsCompleted',
                width: 150,
                ellipsis: true,
                scopedSlots: { customRender: 'IsCompleted' },
              }
              this.columns.splice(this.columns.length - 1, 1, obj)
            } else if (this.model.PolicyBindConfig.PolicyStepMethod == 2) {
              // 每满
              if (this.model.PolicyBindConfig.IsCasePaymentType) {
                let index1 = this.columns.findIndex(item => item.dataIndex == 'AcceptanceIsCompleted')//承兑
                let index2 = this.columns.findIndex(item => item.dataIndex == 'TelegraphicIsCompleted')//电汇
                if (index1 > -1) {
                  this.columns[index1] = {
                    title: '承兑完成次数',
                    dataIndex: 'AcceptanceCount',
                    width: 150,
                    ellipsis: true,
                    color: '#238c23',
                    scopedSlots: { customRender: 'component' },
                  }
                }
                if (index2 > -1) {
                  this.columns[index2] = {
                    title: '电汇完成次数',
                    dataIndex: 'TelegraphicCount',
                    width: 150,
                    ellipsis: true,
                    color: '#238c23',
                    scopedSlots: { customRender: 'component' },
                  }
                }
              } else {
                let obj = {
                  title: '完成次数',
                  dataIndex: 'Count',
                  width: 150,
                  ellipsis: true,
                  scopedSlots: { customRender: 'component' },
                }
                this.columns.splice(this.columns.length - 1, 1, obj)
              }
            }
            // 是否显示已完成销售客户数
            if (
              this.model.PolicyBindConfig.AccountingConditions == 5 &&
              this.model.PolicyBindConfig.PolicyRebateMethod == 3
            ) {
              let obj = {
                title: '已完成销售客户数',
                dataIndex: 'Count',
                width: 150,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              }
              this.columns2.push(obj)
            } else if (this.model.PolicyBindConfig.PolicyRebateMethod == 9) {
              if (this.model.PolicyStepList.length > 0) {
                this.model.PolicyStepList.map((item) => {
                  item.ReturnGoodsRebateRate = this.model.PolicyBindConfig.ReturnGoodsRebateRate
                  item.ReturnGoodsUnitPric = this.model.PolicyBindConfig.ReturnGoodsUnitPric
                })
              }
              // 是否显示返利数量和单价
              if (this.columns.length == 3) {
                let objArray = [
                  {
                    title: this.tableTitleArray[1],
                    dataIndex: 'RebateValue',
                    width: 150,
                    ellipsis: true,
                    scopedSlots: { customRender: 'component' },
                  },
                ]
                this.columns.splice(1, 1)
                const arr1 = this.columns
                const arr2 = objArray
                const index = 1 // 在索引位置 2 插入 arr2
                // 分割原始数组
                const before = arr1.slice(0, index)
                const after = arr1.slice(index)
                // 合并数组
                const result = before.concat(arr2, after)
                this.columns = result
                this.dataSource = this.model.PolicyStepList
              }
            } else {
              if (this.columns[this.columns.length - 1].title == '已完成销售客户数') {
                this.columns2.pop()
              }
            }

          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => { })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        if (this.sort) {
          // 订单列表 排序方式
          this.queryParam[this.sort] = 'ascend' == sorter.order ? '1' : '2'
        }
      }
      this.ipagination = pagination
      this.loadData(null, (source) => {
        this.dataSource2 = source
      })
    },
    amountTotal() {
      let url = ''
      let formData = {

      }
      // type 1年度协议 2短期销售协议
      // AccountingMethodType 1 购进 2 销售 3 打款金额
      switch (this.type) {
        case 1:
          if (this.AccountingMethodType == '1') {
            url = '/{v}/ExecutionOrder/GetAgreementPolicyExecutionDetailTotalAmountAsync'
            formData = this.queryParam
          } else if (this.AccountingMethodType == '2') {
            url = '/{v}/ExecutionOrder/GetAgreementPolicyExecutionFlowDetailAmountAsync'
            formData = this.queryParam
          } else if (this.AccountingMethodType == '3') {
            url = '/{v}/ExecutionOrder/GetAgreementPolicyExecutionRemittanceDetailAmountAsync'
            formData = this.queryParam
          }
          break;
        case 2:
          url = '/{v}/ExecutionOrder/GetAgreementPolicyExecutionFlowDetailAmountAsync'
          formData = this.queryParam
          break;
      }

      getAction(url, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.amountTotalVal = res.Data
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {

        })
    },
    getInfo2() {
      if (this.model.PolicyBindConfig && this.model.PolicyBindConfig.AccountingConditions == 6) {
        // 打款情况下
        this.url.list = this.url.info2DaKuan
        this.queryParam.PurchaseAgreementPolicyId = this.record.PurchaseAgreementPolicyId
      } else {
        if (this.AccountingMethodType == 2) {
          this.url.list = this.url.info2Cus
          this.queryParam.PurchaseAgreementExecutionOrderId = this.model.PurchaseAgreementExecutionOrderId
        } else {
          this.url.list = this.url.info2
          this.queryParam.PurchaseAgreementPolicyId = this.record.PurchaseAgreementPolicyId
        }
      }
      // 新添加参数，区分购进是核算还是采购
      if (this.model.PolicyBindConfig.AccountingConditions == 2 || this.model.PolicyBindConfig.PolicyRebateMethod == 6) {
        this.queryParam.IsAccounting = true
      } else {
        this.queryParam.IsAccounting = false
      }

      this.loadData(1, (source) => {
        // 显示进价或者供应商核算价或者单价
        let ThresholAmountIndex = this.columns3.findIndex((item) => {
          return item.dataIndex == 'Price'
        })
        let hsThresholAmountIndex = this.columns3.findIndex((item) => {
          return (item.dataIndex == 'Price' || item.dataIndex == 'AccountingPrice')
        })
        // 以采购金额和数量为条件
        if ([1, 3].indexOf(this.model.PolicyBindConfig.AccountingConditions) >= 0) {
          if (ThresholAmountIndex >= 0) this.columns3[ThresholAmountIndex].title = '采购单价'
        }
        if (this.model.PolicyBindConfig.AccountingConditions == 2 || this.model.PolicyBindConfig.PolicyRebateMethod == 6) {
          if (hsThresholAmountIndex >= 0) {
            this.columns3[hsThresholAmountIndex].title = '供应商核算价'
            this.columns3[hsThresholAmountIndex].dataIndex = 'AccountingPrice'
          }
        }
        if ([4, 5].indexOf(this.model.PolicyBindConfig.AccountingConditions) >= 0) {
          // 以销售为条件
          if (ThresholAmountIndex >= 0) this.columns3[ThresholAmountIndex].title = '单价'
        }
        if ([6].indexOf(this.model.PolicyBindConfig.AccountingConditions) >= 0) {
          // 以打款金额为条件
          this.columns3 = [
            {
              title: '打款单号',
              dataIndex: 'BusinessNo',
              width: 200,
              ellipsis: true,
              scopedSlots: { customRender: 'order' },
            },
            {
              title: '单据类型',
              dataIndex: 'BusinessOrderTypeStr',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '供应商',
              dataIndex: 'SupplierName',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '打款方式',
              dataIndex: 'RemittanceTypeStr',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '金额',
              dataIndex: 'TotalCompletedValue',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
            {
              title: '时间',
              dataIndex: 'BusinessTime',
              width: 150,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            },
          ]
        }
        this.dataSource2 = source
      })
      // 合计金额
      this.amountTotal()
    },
    goOrder(record) {
      let url = ''
      let query = {}
      if (record.BusinessOrderType == 6) {
        // 采购调价
        url = '/pages/purchasingManagement/PurchaSepriceAdjustmentDetail'
        query = {
          id: record.BusinessId,
        }
      } else if (record.BusinessOrderType == 2) {
        // 入库跳转
        url = '/pages/purchasingManagement/PurchaseOrderDetail'
        query = {
          id: record.MainOrderId,
        }
      } else if (record.BusinessOrderType == 4) {
        // 采购退货
        url = '/pages/purchasingManagement/PurchaReturnGoodsDetail'
        query = {
          id: record.BusinessId,
        }
      } else if (record.BusinessOrderType == 60) {
        // 打款单
        url = '/pages/financialManagement/financialSettlement/cashierPayInfo'
        query = {
          id: record.BusinessId,
          remittanceSource: 1,
          isInfo: true,
        }
      } else if (record.BusinessOrderType == 3) {
        // 采购付款
        url = '/pages/financialManagement/financialSettlement/cashierPayAdd'
        query = {
          id: record.BusinessId,
          remittanceSource: 1,
          isInfo: true,
        }
      }
      this.close()
      this.$emit("ok")
      this.$root.$emit('reloadRouter')
      // 入库跳转
      this.$router.push({
        path: url,
        query: query,
      })
    },
    giftGoods() {
      let data = this.model.PolicyBindConfig.GiftGoodsInfo
      data = data
        ? data.ErpGoodsName +
        ' | ' +
        data.PackingSpecification +
        ' | ' +
        data.PackageUnit +
        ' | ' +
        data.BrandManufacturer +
        ' | ' +
        data.ErpGoodsCode
        : '赠品'
      return data
    },
    // table行的颜色
    rowClassNameFun(record, index) {
      // if (record.TotalCompletedValue < 0) {
      //   return 'table-color-red-dust'
      // }
      // 采购退货和采购调价
      if (record.BusinessOrderType == 4 || record.BusinessOrderType == 6) {
        return 'table-color-red-dust'
      }
    },
    onTimeChange(dates, dateStrings) {
      if (dateStrings.length == 2) {
        this.queryParam.CreateTimeBegin = dateStrings[0]
        this.queryParam.CreateTimeEnd = dateStrings[1]
      } else {
        this.queryParam.CreateTimeBegin = null
        this.queryParam.CreateTimeEnd = null
      }
    },
    searchReset() {
      this.queryParam = {
        PurchaseAgreementPolicyId: this.queryParam.PurchaseAgreementPolicyId,
      }
      this.rangeDate = []
      this.getInfo2()
    },
    searchQuery() {
      this.getInfo2()
    },
    tabsChange(e) {
      if (e == 1) {
        this.getInfo()
      } else if (e == 2) {
        this.getInfo2()
      }
    },
    operate(record, type, index) {
      if (type == 'info') {
      }
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>
