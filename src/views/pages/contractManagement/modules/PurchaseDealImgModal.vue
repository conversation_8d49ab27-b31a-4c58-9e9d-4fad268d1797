<!-- 图片查看弹窗 -->
<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :footer="null"
  >
    <ImageViewer ref="ImageViewer" :imgList="imgList" title="照片" :imgIndex="imgIndex" />
  </a-modal>
</template>

<script>
import moment from 'moment'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'PurchaseDealImgModal',
  components: {},
  data() {
    return {
      title: '查看照片',
      visible: false,
      imgList: [],
      imgIndex: 0,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      httpHead: 'P36001',
      url: {
        add: '/{v}/UserPermission/Role/Create',
        update: '/{v}/UserPermission/Role/Update',
      },
    }
  },
  mounted() {},
  created() {},
  methods: {
    moment,
    show(data, item, index) {
      this.imgList = data
      this.imgIndex = index
      this.visible = true
    },
    // 确定
    handleOk() {},
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>
