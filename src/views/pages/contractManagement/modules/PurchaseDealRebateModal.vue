<template>
  <a-modal :title="title" :width="800" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item v-for="(item,index) in rebateList" :key="index" :label="item.AccountingConditionsStr">
                <div style="color:#333333;margin-bottom:10px;font-weight:bold;">{{item[0].AccountingConditionsStr}}</div>
                <div class="box" v-for="(rItem,rIndex) in item" :key="rIndex">
                  <div style="width:92%;">
                    <div style="display: flex;">
                      <div v-if="rItem.PolicyRebateMethod == 4" style="color: red;background: #fef0ef;border: 1px solid #ef3b3b;border-radius: 26%;font-size: 10px;zoom:0.9;padding: 2px 3px;margin-right:2px;">返货</div>
                      <div v-html="rItem.Title"></div>
                    </div>
                    <div style="color:rgb(127, 127, 127);">{{rItem.Description}}</div>
                  </div>
                  <a :disabled="rItem.disabledCheck?rItem.disabledCheck:false" @click="choosePattern(rItem)">选择</a>
                </div>
              </a-form-model-item>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">
        关闭
      </a-button>
    </a-row>
    <!-- 新增促销政策 -->
    <PurchaseDealPromotionAddModal ref="PurchaseDealPromotionAddModal"></PurchaseDealPromotionAddModal>
  </a-modal>
</template>

<script>
import moment from "moment";
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "PurchaseDealRebateModal",
  components: {},
  data() {
    return {
      title: "选择返利模式",
      visible: false,
      isEdit: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      rItemObj: {},
      rebateList: [],
      confirmLoading: false,
      httpHead: 'P36007',
      url: {
        info: '/{v}/PurchaseAgreement/GetPolicyConfigListAsync',
      },
    };
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(modelData, rItemObj) {
      this.title = '选择返利模式'
      this.rItemObj = rItemObj
      let accountingMethod = null
      if (modelData) {
        accountingMethod = modelData.AccountingMethod
      }
      this.getInfo(accountingMethod)
      this.isEdit = true;
      this.visible = true;
    },
    getInfo(accountingMethod) {
      let formData = {
        accountingMethod: accountingMethod,
      }
      this.confirmLoading = true
      getAction(this.url.info, formData, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          if (res.Data && res.Data.length > 0) {
            // 重组数据
            let accountingConditionsArray = res.Data.map((item) => {
              if (item.Id === this.rItemObj.Id) {
                item.disabledCheck = true
              } else {
                item.disabledCheck = false
              }
              return item.AccountingConditions
            })
            let accountingConditionsNoRepeatArray = Array.from(new Set(accountingConditionsArray))
            let obj = {}
            let array = []
            accountingConditionsNoRepeatArray.map((item) => {
              obj['arr' + item] = []
            })
            res.Data.map((item, index) => {
              accountingConditionsNoRepeatArray.map((r, rIndex) => {
                if (item.AccountingConditions == r) {
                  obj['arr' + r].push(item)
                }
              })
            })
            accountingConditionsNoRepeatArray.map((item) => {
              array.push(obj['arr' + item])
            })
            this.rebateList = array
          }
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {
        this.confirmLoading = false
      });
    },
    // 选择模式
    choosePattern(rItem) {
      this.$emit('ok', rItem)
      this.close()
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
<style lang="scss">
.box {
  border: 1px solid rgba(215, 215, 215, 1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
  padding: 20px;
  margin-bottom: 10px;
}
.box:hover {
  background: rgba(242, 242, 242, 1);
}
</style>
