<!-- 合约作废原因 -->
<template>
  <a-modal :title="title" :width="800" :visible="visible" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :destroyOnClose="true" :footer="null">
    <!-- 列表 -->
    <TableNewView ref="tableView" :table="table" :columns="columns" :dataList="dataSource">

    </TableNewView>
  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "PurchaseCancellationReasonModal",
  components: {},
  mixins: [ListMixin],
  props: {
    aUrl: {
      type: String,
      default: ''
    },
    pageN: {
      type: String,
      default: ''
    },
    customColumn:{
      type:Array,
      default:()=>[]
    }
  },

  data() {
    return {
      title: "作废原因",
      visible: false,
      table: {
        operateBtns: [

        ], //右上角按钮集合
        rowKey: 'Id',
        customSlot: [],
      },
      defaultColumns: [
        {
          title: '操作时间',
          dataIndex: 'InvalidTime',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作人',
          dataIndex: 'InvalidPersonName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '作废原因',
          dataIndex: 'InvalidReason',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      queryParam: {

      },
      isInitData: false,
      httpHead: 'P36007',
      url: {
        listType: 'GET', //列表接口请求类型
        list: this.aUrl || '/{v}/PurchaseAgreement/GetPurchaseShortAgreementInvalidInfo',//列表数据接口
      },
    };
  },
  computed: {
    rules() {
      return {
        Name: [{ required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200, '作废原因') }],
      };
    },
    columns(){
      if(this.customColumn.length){
        return this.customColumn
      }else{
        return this.defaultColumns
      }
    }
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    loadAfter() {
      // console.log('loadAfter',this.dataSource)
      if (this.pageN == 'purchaseDealShortList'||(this.pageN == 'purchaseGeneralList')) {
        this.dataSource = [this.dataSource]
      }
    },
    show(record, localData) {
      if (localData) {
        // 直接读取本地数据
        this.dataSource = localData || []
      } else {
        this.queryParam.Id = record.Id || null
        this.loadData(1)
      }
      this.visible = true;
    },
    // 确定
    handleOk() {

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
