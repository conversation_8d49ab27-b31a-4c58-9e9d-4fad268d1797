<!-- 取消返利 -->
<template>
  <a-modal
    :title="source==='nd'?'变更返利记录':'取消返利记录'"
    :width="800"
    :visible="visible"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    :destroyOnClose="true"
    :footer="null"
  >
    <a-tabs v-model="activeTab" @change="changeTab">
      <a-tab-pane key="cancelRecord" tab="取消记录" />
      <a-tab-pane key="addRecord" tab="增加记录" v-if="source === 'nd'" />
    </a-tabs>
    <!-- 列表 -->
    <TableNewView ref="tableView" :table="table" :columns="columns" :dataList="dataSource" @operate="operate">
    </TableNewView>
    <!-- 新增取消返利 -->
    <PurchaseCancelRebateAddModal
      ref="PurchaseCancelRebateAddModal"
      :source="source"
      @ok="modalOk"
      :modalTitle="modalTitle"
      :isAddRebate="isAddRebate"
    />
  </a-modal>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction, deleteAction } from '@/api/manage'

export default {
  name: 'PurchaseCancelRebateModal',
  components: {},
  mixins: [ListMixin],
  props: {
    // dqcg 短期采购协议 nd 年度协议 dqxs 短期销售协议 ty通用协议
    source: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      title: '变更返利记录',
      visible: false,
      queryParam: {},
      table: {
        operateBtns: [], //右上角按钮集合
        rowKey: 'Id',
        customSlot: [],
        showPagination: false,
      },
      columns: [],
      columns1: [
        {
          title: '操作时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利金额/数量',
          dataIndex: 'CancelRebateValue',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消原因',
          dataIndex: 'CancelReason',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns2: [
        {
          title: '操作时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '增加返利金额',
          dataIndex: 'Qty',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '增加原因',
          dataIndex: 'Desc',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtns: [
            {
              name: '作废',
              icon: '',
              id: 'ddcbee4c-132c-4b3c-890f-6883cbe82592',
              isShow: (record) => {
                return record.IsValid
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      recordData: {},
      isInitData: false,
      httpHead: 'P36007',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/AgreementCancelRebate/GetAgreementCancelRebateRecordListAsync', //列表数据接口
      },
      // tabList: [
      //   { tabTitle: '取消记录', key: 'cancelRecord' },
      //   { tabTitle: '增加记录', key: 'addRecord' },
      // ],
      activeTab: 'cancelRecord',
      modalTitle: '取消返利',
      isAddRebate: false,
    }
  },
  computed: {
    ids() {
      return '442a6652-fb05-4926-bded-ede6bc55c0f6'
    },
  },
  // watch: {
  //   activeTab: {
  //     handler(value) {
  //       if (value === 'cancelRecord') {
  //         this.url.list = '/{v}/AgreementCancelRebate/GetAgreementCancelRebateRecordListAsync'
  //         this.columns = this.columns1
  //         this.table.operateBtns = this.recordData.RecognitionStatus === 3 ? []: [
  //           {
  //             name: '取消返利',
  //             type: 'primary',
  //             key: '取消返利',
  //             id:
  //               this.source == 'dqcg'
  //                 ? '442a6652-fb05-4926-bded-ede6bc55c0f6'
  //                 : this.source == 'nd'
  //                 ? '45c82db6-6cac-413c-b871-9dbfa31e72a1'
  //                 : this.source == 'dqxs'
  //                 ? 'b74a7a09-2504-4f41-97ff-d2ddfa694d6c'
  //                 : this.source == 'ty'
  //                 ? 'd8596f7c-18bc-4398-9c5a-dbe4073a8ee1'
  //                 : null,
  //           },
  //         ]
  //         this.modalTitle = '取消返利'
  //         this.isAddRebate = false

  //       } else {
  //         this.url.list = '/{v}/PurchaseAgreement/GetAddRebateListAsync'
  //         this.columns = this.columns2
  //         this.table.operateBtns = [
  //           {
  //             name: '增加返利',
  //             type: 'primary',
  //             key: '增加返利',
  //             id: 'ddcbee4c-132c-4b3c-890f-6883cbe82592'
  //           },
  //         ]
  //         this.modalTitle = '增加返利'
  //         this.isAddRebate = true
  //       }
  //       this.loadData(1)
  //     },
  //     deep: true
  //   },
  // },
  mounted() {},
  created() {},
  methods: {
    moment,
    show(record) {
      this.recordData = record
      this.activeTab = 'cancelRecord'
      this.queryParam.agreementId = record.Id
      this.changeTab('cancelRecord')
      this.init()
      this.loadData(1)
      this.visible = true
    },
    init() {
      switch (this.source) {
        case 'dqcg':
          this.columns[1].title = '取消返利金额'
          break
        case 'nd':
          this.columns[1].title = '取消返利金额/数量'
          break
        case 'dqxs':
          this.columns[1].title = '取消返利金额/数量'
          break
        case 'ty':
          this.columns[1].title = '取消返利金额'
          break
      }
    },
    modalOk() {
      this.loadData(1)
    },
    // 列表操作
    operate(record, type) {
      switch (type) {
        case '取消返利':
          this.$refs.PurchaseCancelRebateAddModal.add(this.queryParam.agreementId, this.recordData)
          break
        case '增加返利':
          this.$refs.PurchaseCancelRebateAddModal.add(this.queryParam.agreementId, this.recordData)
          break
        case '作废':
          let that = this
          this.$confirm({
            title: '您确定要作废本条新增返利吗?',
            content: '',
            onOk() {
              that.cancelRebate(record)
            },
            onCancel() {},
          })
          break
      }
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    changeTab(activeKey) {
      if (activeKey === 'cancelRecord') {
        this.url.list = '/{v}/AgreementCancelRebate/GetAgreementCancelRebateRecordListAsync'
        this.columns = this.columns1
        this.table.operateBtns =
          this.recordData.RecognitionStatus === 3
            ? []
            : [
                {
                  name: '取消返利',
                  type: 'primary',
                  key: '取消返利',
                  id:
                    this.source == 'dqcg'
                      ? '442a6652-fb05-4926-bded-ede6bc55c0f6'
                      : this.source == 'nd'
                      ? '45c82db6-6cac-413c-b871-9dbfa31e72a1'
                      : this.source == 'dqxs'
                      ? 'b74a7a09-2504-4f41-97ff-d2ddfa694d6c'
                      : this.source == 'ty'
                      ? 'd8596f7c-18bc-4398-9c5a-dbe4073a8ee1'
                      : null,
                },
              ]
        this.modalTitle = '取消返利'
        this.isAddRebate = false
      } else {
        this.url.list = '/{v}/PurchaseAgreement/GetAddRebateListAsync'
        this.columns = this.columns2
        this.table.operateBtns = [
          {
            name: '增加返利',
            type: 'primary',
            key: '增加返利',
            id: 'ddcbee4c-132c-4b3c-890f-6883cbe82592',
          },
        ]
        this.modalTitle = '增加返利'
        this.isAddRebate = true
      }
      this.loadData(1)
    },
    cancelRebate(record) {
      postAction(`/{v}/PurchaseAgreement/CancelAddRebateAsync?id=${record.Id}`, {}, 'P36007').then((res) => {
        if (res.IsSuccess) {
          this.loadData(1)
        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
  },
}
</script>

<style></style>
