<!--
 * @Description: 短期销售协议、年度协议 列表
 * @Version: 1.0
 * @Author: 继承
 * @Date: 2025-02-11 14:17:02
 * @LastEditors: wenjingGao
 * @LastEditTime: 2025-06-05 14:45:02
-->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" :sortArray="['rebateType']" :defQueryParam="{RebateTitle:''}" @searchQuery="searchQuery">
      <!-- 返利模式 -->
      <a-input-search slot="rebateType" v-model="queryParam.RebateTitle" placeholder="请选择" enter-button="选择" @search="onSearch" />
    </SimpleSearchArea>
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="returnColumns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab">
      <!-- 促销时间 -->
      <span slot="AgreementStartTimeVal" slot-scope="{ text, record }">
        <j-ellipsis :value="`${text ? text.substring(0, 11) : ''}~${
            record['AgreementEndTime'] ? record['AgreementEndTime'].substring(0, 11) : ''
          }`" :length="40" />
      </span>
    </SimpleTable>
    <!-- 执行情况 -->
    <PurchaseDealCarryOut ref="PurchaseDealCarryOut" :type="type"></PurchaseDealCarryOut>
    <!-- 认款情况 -->
    <PurchaseDealAcceptance ref="PurchaseDealAcceptance" :type="type"></PurchaseDealAcceptance>
    <!-- 返利模式弹窗 -->
    <RebateTypeModal ref="RebateTypeModal" :params="rebateParams" @ok="RebateTypeOk"></RebateTypeModal>
    <!-- 作废 -->
    <PurchaseCancellationModal ref="PurchaseCancellationModal" @ok="modalOk"></PurchaseCancellationModal>
    <!-- 作废原因 -->
    <PurchaseCancellationReasonModal ref="PurchaseCancellationReasonModal"></PurchaseCancellationReasonModal>
    <!-- 表格选择设置流向卡弹窗 -->
    <TableSelectDataModal ref="TableSelectDataModal" :selectTableData="selectTableData" @chooseData="chooseData"></TableSelectDataModal>
    <!-- 变更返利 -->
    <PurchaseCancelRebateModal ref="PurchaseCancelRebateModal" :source="rebateSource" @close="modalOk" />
    <!-- 原件变更弹窗 -->
    <PurchaseOriginalChangeModal ref="PurchaseOriginalChangeModal" @ok="modalOk" />
    <!-- 流向卡设置弹框 -->
    <a-modal
      title="设置流向"
      :visible="flowCardVisible"
      :footer="null"
      @cancel="flowCardVisible = false"
      width="800px"
    >
      <div class="mb5 flexr">
        <a-button 
          type="primary" 
          @click="operate(undefined, '绑定流向卡')" >
          绑定流向卡
        </a-button>
      </div>
      <a-table
        :columns="flowColumns"
        :dataSource="flowCardData"
        :pagination="false"
        :bordered="true"
        :loading="flowCardLoading"
      >
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </span>
        <span slot="operation" slot-scope="text, record">
          <a @click="operate(record, '明细')" style="margin-right: 5px;">明细</a>
          <a @click="operate(record, '解绑')">解绑</a>
        </span>
      </a-table>
    </a-modal>
    <!-- 流向卡明细弹框 -->
    <FlowCardDetailModal ref="FlowCardDetailModal"></FlowCardDetailModal>
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
import { number } from 'echarts'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'PurchaseDealBase',
  title: '协议',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  props: {
    type: {
      type: Number,
      default: 1, //1年度协议 2短期销售协议
    },
  },
  data() {
    return {
      searchInput: [],
      searchInput1: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '协议编号', type: 'input', value: '', key: 'AgreementNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '甲方',
          type: 'input',
          value: '',
          key: 'PartyFirstKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码',
        },
        {
          name: '促销时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['AgreementStartTime', 'AgreementEndTime'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '核算方式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AccountingMethod',
          dictCode: 'EnumAgreementAccountingMethod',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '返利时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['RebateStartTime', 'RebateEndTime'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '商品',
          type: 'input',
          value: '',
          key: 'GoodsNameKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码/生产厂商',
        },
        {
          name: '返利形式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AgreementRebateType',
          dictCode: 'EnumAgreementRebateType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '返利模式',
          type: 'rebateType',
          vModel: 'RebateTitle',
          value: '',
          key: 'PurchaseAgreementPolicyConfigId',
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '协议状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AgreementStatus',
          dictCode: 'EnumAgreementStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '认款状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'RecognitionStatus',
          dictCode: 'EnumRecognitionStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '责任人',
          type: 'input',
          value: '',
          key: 'ResponsiblePersonName',
          defaultVal: '',
          placeholder: '请输入',
        },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '执行情况',
          type: 'selectBasicLink',
          value: '',
          vModel: 'ExecutionStatus',
          dictCode: 'EnumAgreementExecutionStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        { 
          name: '甲方类型',
          type: 'select',
          value: '',
          key: 'PartyFirstType',
          defaultVal: [{ title: '供应商', value: 1 }, { title: '生产厂家', value: 2 }],
          placeholder: '请选择'
        },
      ],
      searchInput2: [
        { name: '协议编号', type: 'input', value: '', key: 'AgreementNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '甲方',
          type: 'input',
          value: '',
          key: 'PartyFirstKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码',
        },
        {
          name: '促销时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['AgreementStartTime', 'AgreementEndTime'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '返利时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['RebateStartTime', 'RebateEndTime'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '商品',
          type: 'input',
          value: '',
          key: 'GoodsNameKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码/厂商',
        },
        {
          name: '返利形式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AgreementRebateType',
          dictCode: 'EnumAgreementRebateType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '返利模式',
          type: 'rebateType',
          value: '',
          key: 'PurchaseAgreementPolicyConfigId',
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '协议状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AgreementStatus',
          dictCode: 'EnumAgreementStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '认款状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'RecognitionStatus',
          dictCode: 'EnumRecognitionStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '责任人',
          type: 'input',
          value: '',
          key: 'ResponsiblePersonName',
          defaultVal: '',
          placeholder: '请输入',
        },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '执行情况',
          type: 'selectBasicLink',
          value: '',
          vModel: 'ExecutionStatus',
          dictCode: 'EnumAgreementExecutionStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        { 
          name: '甲方类型',
          type: 'select',
          value: '',
          key: 'PartyFirstType',
          defaultVal: [{ title: '供应商', value: 1 }, { title: '生产厂家', value: 2 }],
          placeholder: '请选择'
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '导出', type: 'primary', icon: '', key: '导出', id: this.type == 1 ? 'fa4d69a4-a352-454b-8f84-90db2bc0a1eb' : (this.type == 2 ? 'eba1d8b5-ee0a-4389-9ec9-28921f221d96' : null) },
          { name: '新增', type: 'primary', icon: 'plus', key: 'add' }
        ],
        hintArray: [],
        sortArray: ['AgreementStartTimeVal'],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方类型',
          dataIndex: 'PartyFirstTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 200,
          ellipsis: true,
          sorter: true,
          scopedSlots: { customRender: 'AgreementStartTimeVal' },
        },
        {
          title: '核算方式',
          dataIndex: 'AccountingMethodStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利形式',
          dataIndex: 'RebateTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '政策数量',
          dataIndex: 'PolicyCount',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '协议状态',
          dataIndex: 'AgreementStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款状态',
          dataIndex: 'RecognitionStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'ResponsiblePersonName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: this.type == 2 || this.type == 1 ? 450 : 380,
          fixed: 'right',
          actionBtn: [
            {
              //AuditStatus 0 未提交 1审核中 2审核通过 3审核失败
              //AgreementStatus 1 未生效 2生效中 3已结束 4已作废
              //RecognitionStatus 1 未认款 2部分认款 3已认款
              name: '执行情况',
              icon: '',
              id: this.type == 1 ? 'dbe2e35d-bcd9-4836-9b15-15a9fea75a75' : (this.type == 2 ? '98c51266-1e29-4071-8be4-0ad96ec50547' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [1, 2].includes(record.AuditStatus) &&
                  [1, 2, 3].includes(record.AgreementStatus)
                ) {
                  return true
                }
              },
            },
            {
              name: '认款情况',
              icon: '',
              id: this.type == 1 ? '0ba0c9f7-c087-4e75-aed3-aea1cf2be65e' : (this.type == 2 ? '027884f8-a19b-4bf7-9fba-52dc92c889aa' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [2].includes(record.AuditStatus) &&
                  [3].includes(record.AgreementStatus)
                ) {
                  return true
                }
              },
            },
            {
              name: '详情',
              icon: '',
              id: this.type == 1 ? '5dcb3be3-08d9-4ccd-b37a-3387ea0d4b29' : (this.type == 2 ? '2cc58f35-2010-434a-ac21-0e69442a50a4' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [1, 2].includes(record.AuditStatus)
                ) {
                  return true
                }
              },
            },
            {
              name: '复制',
              icon: '',
              id: this.type == 1 ? 'cf8fb740-5e43-4790-8733-bfae20c86374' : (this.type == 2 ? '8ccf7ec5-7cf6-43cc-bff9-542399a173fb' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [1, 2].includes(record.AuditStatus)
                ) {
                  return true
                }
              },
            },
            {
              name: '编辑',
              icon: '',
              id: this.type == 1 ? '5b61b46c-69f0-4b78-a06b-716327aec429' : (this.type == 2 ? '7e1f05b4-a430-49b3-b2aa-97c91bed9443' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [0, 3].includes(record.AuditStatus) &&
                  [1].includes(record.AgreementStatus) &&
                  [1].includes(record.RecognitionStatus)
                ) {
                  return true
                }
              },
            },
            {
              name: '删除',
              icon: '',
              id: this.type == 1 ? '9b55fec4-d8e1-45df-84a1-45a9698fb673' : (this.type == 2 ? '615e66e4-a2f6-46c2-b8e2-70428fb486f7' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [0, 3].includes(record.AuditStatus) &&
                  [1].includes(record.AgreementStatus) &&
                  [1].includes(record.RecognitionStatus)
                ) {
                  return true
                }
              },
            },
            {
              name: '撤回',
              icon: '',
              id: this.type == 1 ? '271cfde5-b99c-405e-a43a-d351e9cce1f1' : (this.type == 2 ? '18347cac-638e-43f9-a1f7-9a78d91f8c55' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [1].includes(record.AuditStatus) &&
                  [1].includes(record.AgreementStatus) &&
                  [1].includes(record.RecognitionStatus)
                ) {
                  return true
                }
              },
            },
            {
              name: '设置流向卡',
              icon: '',
              id: this.type == 1 ? 'b95466ec-1834-4740-914d-71308c03b8e1' : (this.type == 2 ? '5f835a91-703a-40a5-84d6-4fe3548d1fde' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [1].includes(record.FlowDataSource) && 
                  [1, 2].includes(record.AuditStatus) &&
                  [1, 2, 3].includes(record.AgreementStatus) &&
                  [1].includes(record.RecognitionStatus) &&
                  [2].includes(record.AccountingMethod) &&
                  [1].includes(record.FlowDataSource)
                ) {
                  return true
                }
              },
            },
            {
              name: '作废',
              icon: '',
              id: this.type == 1 ? 'ea82c817-2b6b-4250-b0aa-a7a39945ec8a' : (this.type == 2 ? '29aa3edb-8d60-47b6-80bc-f7850eedef68' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [2].includes(record.AuditStatus) &&
                  [1, 2, 3].includes(record.AgreementStatus)
                ) {
                  return true
                }
              },
            },
            {
              name: '作废原因',
              icon: '',
              id: this.type == 1 ? 'cb5235c6-25f0-40af-8b26-cce0c8996678' : (this.type == 2 ? 'bb676ad0-5d01-40ca-be24-7f41c7022f1c' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [2].includes(record.AuditStatus) &&
                  [4].includes(record.AgreementStatus) && 
                  [1].includes(record.RecognitionStatus)
                ) {
                  return true
                }
              },
            },
            {
              name: '原件变更',
              icon: '',
              id: this.type == 1 ? 'feb4866e-40bc-4545-a636-2da32db0249f' : null,
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [2].includes(record.AuditStatus) &&
                  [1,2,3].includes(record.AgreementStatus)
                ) {
                  return true
                }
              },
            },
            {
              name: this.type == 1 ? '变更返利': '取消返利',
              id: this.type == 1 ? '67f1f844-a6f7-4808-a93a-1091b974614c' : (this.type == 2 ? 'eb46b406-7218-4961-bf07-f4cd25930828' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [2].includes(record.AuditStatus) &&
                  [1, 2, 3].includes(record.AgreementStatus)
                ) {
                  return true
                }
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      // 流向卡设置弹框
      flowCardVisible: false,
      flowCardLoading: false,
      flowCardData: [],
      flowColumns: [
        {
          title: '流向卡号',
          dataIndex: 'FlowDirectionCardCode',
          width: 200,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '流向卡名称',
          dataIndex: 'FlowDirectionCardName',
          width: 200,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '绑定时间',
          dataIndex: 'CreateTime',
          width: 200,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          key: 'operation',
          width: 120,
          scopedSlots: { customRender: 'operation' },
        },
      ],
      selectTableData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          {
            name: '流向卡编号',
            type: 'input',
            value: '',
            key: 'FlowDirectionCardCode',
            defaultVal: '',
            placeholder: '请输入编号',
          },
          {
            name: '流向名称',
            type: 'input',
            value: '',
            key: 'FlowDirectionCardName',
            defaultVal: '',
            placeholder: '请输入流向名称',
          },
          {
            name: '创建时间',
            type: 'timeInput',
            value: '',
            rangeDate: [],
            key: ['CreationTimeStart', 'CreationTimeEnd'],
            defaultVal: ['', ''],
            placeholder: ['开始日期', '结束日期'],
          },
        ],
        title: '配置流向',
        name: '流向',
        recordKey: 'FlowDirectionCardCode',
        httpHead: 'P36007',
        isInitData: false,
        url: {
          list: '/{v}/PurchaseAgreement/GetErpFlowDirectionCardPageListAsync',
          listType: 'GET',
        },
        columns: [
          {
            title: '流向卡编号',
            dataIndex: 'FlowDirectionCardCode',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '流向名称',
            dataIndex: 'FlowDirectionCardName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建时间',
            dataIndex: 'CreationTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '最后修改时间',
            dataIndex: 'LastModifyTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '说明',
            dataIndex: 'Description',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
      queryParam: {
        RebateTitle: '',
        PurchaseAgreementPolicyConfigId: null,
      },
      recordObj: {},
      rebateParams: null,
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36007',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/PurchaseAgreement/GetListAsync',
        sub: '/PurchaseOrder/SubmitAsync',
        del: '/{v}/PurchaseAgreement/DeleteAsync',
        isValidUrl: '/YYDepartment/GetDepartmentList', //若有是否有效需要写这个url
        revokeAuditAsync: '/{v}/PurchaseAgreement/RevokeAuditAsync',//撤回
        setErpFlowDirectionCardAsync: '/{v}/ExecutionOrder/SetErpFlowDirectionCardAsync',//设置流向卡
        exportUrl: '/{v}/PurchaseAgreement/ExportYearOrShortSaleAgreementAsync',//导出
      },
    }
  },
  created() {
    this.initInfo()
  },
  computed: {
    // type 1 年度协议 2 短期销售协议
    returnColumns() {
      return this.type == 2 ? this.columns.filter(v => v.title != '核算方式') : this.columns
    },
    rebateSource() {
      let source = this.type == 1 ? 'nd' : (this.type == 2 ? 'dqxs' : '')
      return source
    },
  },
  activated() {
    this.queryParam = this.$refs.SimpleSearchArea.queryParam
    this.initInfo()
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    initInfo() {
      if (this.type == 1) { //年度协议
        this.searchInput = this.searchInput1
        this.queryParam.PurchaseAgreementType = 1
        this.rebateParams = {}
      } else if (this.type == 2) { //短期销售协议
        this.queryParam.PurchaseAgreementType = 2
        this.searchInput = this.searchInput2
        this.rebateParams = {
          accountingMethod: 2,
        }
      }
    },
    onSearch(value) {
      this.$refs.RebateTypeModal.show()
    },
    RebateTypeOk(data) {
      this.$set(this.queryParam, 'RebateTitle', data.Title ? data.Title.replace(/<[^>]+>|&[^>]+;/g, '').trim() : '')
      this.$set(this.$refs.SimpleSearchArea.queryParam, 'RebateTitle', data.Title ? data.Title.replace(/<[^>]+>|&[^>]+;/g, '').trim() : '')
      this.$refs.SimpleSearchArea.queryParam.PurchaseAgreementPolicyConfigId = data.Id ? data.Id : null
    },
    modalOk() {
      this.$refs.table.loadDatas(null, this.$refs.SimpleSearchArea.queryParam)
    },
    // 选择的数据
    chooseData(data) {
      // 合并数组并根据FlowDirectionCardCode去重
      let mergedData = [...data, ...this.flowCardData]
      let uniqueData = Array.from(new Map(mergedData.map(item => [item.FlowDirectionCardCode, item])).values())
      let CardCodeList = uniqueData.map((item) => ({
        CardCode: item.FlowDirectionCardCode,
        CardName: item.FlowDirectionCardName
      }))
      let params = {
        agreementId: this.recordObj.Id,
        CardCodeList: CardCodeList,
      }
      let url = this.linkUrl.setErpFlowDirectionCardAsync
      postAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('操作成功')
          } else {
            this.$message.error(res.Msg)
          }
          this.handleFlowCard(this.recordObj, 'refresh')
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    // 确定提交
    subPurchaseDemand(record) {
      let params = {}
      let url = this.linkUrl.sub + '?orderId=' + record.Id
      postAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('提交成功')
            this.$refs.table.loadDatas(1, this.queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    // 确定删除
    subDelPurchaseDemand(record) {
      let params = {}
      let url = this.linkUrl.del + '?Id=' + record.Id
      postAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('删除成功')
            this.$refs.table.loadDatas(1, this.queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    searchQuery(queryParam, type, extendParams) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
        this.queryParam.RebateTitle = ''
      }
      queryParam.PurchaseAgreementType = this.queryParam.PurchaseAgreementType
      this.$refs.table.loadDatas(1, queryParam, null, type, extendParams)
    },
    // 确认撤回
    subRecall(record) {
      let params = {
        Id: record.Id,
      }
      let url = this.linkUrl.revokeAuditAsync
      putAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('操作成功')
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => {
          this.$refs.table.loadDatas(null, this.queryParam)
        })
    },
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        let path = this.type == 1 ? 'purchaseDealAdd' : 'purchaseShortDealAdd'
        this.goPage(path, { type: this.type })
      } else if (type == '编辑') {
        let path = this.type == 1 ? 'purchaseDealAdd' : 'purchaseShortDealAdd'
        this.goPage(path, { type: this.type, id: record.Id })
      } else if (type == '详情') {
        let path = this.type == 1 ? 'purchaseDealInfo' : 'purchaseShortDealInfo'
        this.goPage(path, { id: record.Id, type: this.type })
      } else if (type == '复制') {
        this.$message.success('复制成功,请点击保存')
        let path = this.type == 1 ? 'purchaseDealAdd' : 'purchaseShortDealAdd'
        this.goPage(path, { id: record.Id, type: this.type, isCopy: true })
      } else if (type == '执行情况') {
        this.$refs.PurchaseDealCarryOut.show(record)
      } else if (type == '认款情况') {
        this.$refs.PurchaseDealAcceptance.show(record)
      } else if (type == '删除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认删除本协议？删除后不能恢复',
          onOk() {
            that.subDelPurchaseDemand(record)
          },
          onCancel() { },
        })
      } else if (type == '作废') {
        this.$refs.PurchaseCancellationModal.show(record)
      } else if (type == '原件变更') {
        this.$refs.PurchaseOriginalChangeModal.show(record)
      } else if (type == '作废原因') {
        let localReasonData = [
          { InvalidTime: record.CancelTime, InvalidPersonName: record.CancelByName, InvalidReason: record.CancelReason },
        ]
        this.$refs.PurchaseCancellationReasonModal.show(record, localReasonData)
      } else if (type == '撤回') {
        let that = this
        this.$confirm({
          title: '您确定要撤回该协议吗?',
          content: '',
          onOk() {
            that.subRecall(record)
          },
          onCancel() { },
        });
      } else if (type == '设置流向卡') {
        this.recordObj = record || {}
        this.handleFlowCard(record)
      } else if (type == '绑定流向卡') {
        this.$refs.TableSelectDataModal.show(this.flowCardData)
      } else if (type == '明细') {
        this.$refs.FlowCardDetailModal.show(record)
      } else if (type == '解绑') {
        postAction(
          `/{v}/ExecutionOrder/CancelFlowDirectionCardAsync?FlowDirectionCardId=${record.Id}`,
          {},
          this.linkHttpHead
        ).then(({ IsSuccess, Msg }) => {
          if (IsSuccess) {
            this.$message.success('解绑成功')
            this.handleFlowCard(this.recordObj, 'refresh')
          } else {
            this.$message.error(Msg || '解绑失败')
          }
        })
      } else if (type == '变更返利'||type == '取消返利') {
        this.$refs.PurchaseCancelRebateModal.show(record)
      } else if (type == '导出') {
        this.handleExportXls(
          this.type == 1 ? '年度协议' : (this.type == 2 ? '短期销售协议' : '协议'),
          'Get',
          this.linkUrl.exportUrl,
          null,
          this.linkHttpHead,
          this.$refs.SimpleSearchArea.queryParam
        )
      }
    },
    // 打开流向卡设置弹框
    handleFlowCard(record, type) {
    console.log(record)
      if (type !== 'refresh') {
        this.flowCardVisible = true;
      }
      this.flowCardData = []
      this.flowCardLoading = true;
      // 获取流向卡列表数据
      getAction('/{v}/PurchaseAgreement/GetFlowDirectionCardPageListAsync', { PurchaseAgreementId: record.Id }, this.linkHttpHead)
        .then(res => {
          if (res.IsSuccess) {
            this.flowCardData = res.Data || [];
          } else {
            this.$message.warning(res.Msg);
          }
        })
        .finally(() => {
          this.flowCardLoading = false;
        });
    },
  },
}
</script>

<style></style>
