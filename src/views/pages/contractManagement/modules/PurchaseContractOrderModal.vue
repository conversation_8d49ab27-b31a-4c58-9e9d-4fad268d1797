<!-- 查看采购合同关联订单 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <div>
        <!-- 搜索 -->
        <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" :queryCol="8" @searchQuery="searchQuery" />
        <!-- 列表 -->
        <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab">
          <!-- 自定义插槽 -->
          <!-- 采购订单号 -->
          <span slot="OrderCount" slot-scope="{text,record}">
            <a @click="showOrderCount(text,record)">{{text}}</a>
          </span>
        </SimpleTable>
        <!-- 合计金额 -->
        <div style="padding: 5px 0; color: red;">
          <span>合计金额：</span>
          <span>￥{{ OrderInventoryTotalAmount }}</span>
        </div>
      </div>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">
        关闭
      </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "PurchaseContractOrderModal",
  components: {},
  mixins: [SimpleMixin],
  data() {
    return {
      title: "查看采购合同关联订单",
      visible: false,
      isEdit: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      searchInput: [
        { name: '订单编号', type: 'input', value: '', key: 'PurchaseOrderNo', defaultVal: '', placeholder: '请输入' },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [

        ],
        hintArray: [],
        sortArray: ['OrderCount'],
        status: '',
        statusKey: '',//标签的切换关键字
        statusList: [

        ],
      },
      columns: [
        {
          title: '采购订单号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'OrderCount' },
        },
        {
          title: '入库金额',
          dataIndex: 'InventoryTotalAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      confirmLoading: false,
      queryParam: {

      },
      OrderInventoryTotalAmount:0,
      isTableInitData: false,//是否自动加载
      linkHttpHead: 'P36007',
      linkUrlType: '',//请求方式
      linkUrl: {
        list: '/{v}/PurchaseContract/GetOrderListAsync',
        getOrderInventoryTotalAmountAsync: '/{v}/PurchaseContract/GetOrderInventoryTotalAmountAsync',
      },
    };
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record) {
      this.model = Object.assign({}, record);
      this.visible = true;
      this.$nextTick(() => {
        this.queryParam.ContractId = record.Id
        this.getOrderInventoryTotalAmountAsync()
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    // 合计金额
    getOrderInventoryTotalAmountAsync(){
      getAction(this.linkUrl.getOrderInventoryTotalAmountAsync,this.queryParam,this.linkHttpHead).then((res) => {
        if(res.IsSuccess){
          this.OrderInventoryTotalAmount = Number(res.Data || 0).toFixed(2)
        }else{
          this.$message.error(res.Msg)
        }
      })
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (type == 'searchReset') {
        this.tab.status = ''
      }
      queryParam.ContractId = this.queryParam.ContractId
      this.getOrderInventoryTotalAmountAsync()
      this.$refs.table.loadDatas(1, queryParam)
    },
    showOrderCount(text, record) {
      this.goPage('/pages/purchasingManagement/PurchaseOrderDetail', { id: record.Id })
      this.handleCancel()
    },
    // 确定
    handleOk() {

    },
    operate() {

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
