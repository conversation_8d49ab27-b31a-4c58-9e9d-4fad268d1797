<!-- 新增采购合同 -->
<template>
  <a-modal :title="title" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff'
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="供应商：" prop="SupplierId">
                <SingleInputSearchView placeholder="搜索名称/编号/拼音码" width="100%" :httpParams="{
                    pageIndex: 1,
                    pageSize: 200,
                    AuditStatus:2,
                    IsValid: true
                  }" keyWord="KeyWord" httpHead="P36003" :Url="url.supplierList" :dataKey="{ name: 'Name', value: 'Id'}" :value="model.SupplierId" :name="model.SupplierName" :isAbsolute="true" :disabled="disabled" @clear="
                    () => {
                      model.SupplierId = ''
                      model.SupplierName = ''
                    }
                  " @change="changeSearchInput" />
              </a-form-model-item>
              <a-form-model-item label="合同金额：" prop="ContractAmount">
                <a-input-number style="width:100%;" v-model="model.ContractAmount" placeholder="请输入" :min="0" :max="999999999.99" :precision="2" />
              </a-form-model-item>
              <a-form-model-item label="合同类型：" prop="ContractType">
                <a-radio-group v-model="model.ContractType" @change="e => onTypeChange(e, 'htlx')">
                  <a-radio :value="1">
                    年度合同
                  </a-radio>
                  <a-radio :value="2">
                    单批次合同
                  </a-radio>
                </a-radio-group>
              </a-form-model-item>
              <a-form-model-item label="合同效期：" prop="rangeDate">
                <a-range-picker @change="onTimeChange" v-model="rangeDate" style="width: 100%" />
              </a-form-model-item>
              <a-form-model-item label="结算方式：" prop="SettlementMethod">
                <a-radio-group v-model="model.SettlementMethod" @change="e => onTypeChange(e, 'jsfs')" :disabled="disabled">
                  <a-radio :value="1">预付款</a-radio>
                  <a-radio :value="2">月结</a-radio>
                  <a-radio :value="3">货到付款</a-radio>
                </a-radio-group>
              </a-form-model-item>
              <a-form-model-item label="结算账期：" v-if="model.SettlementMethod == 2" prop="SettlementPeriod">
                <a-select style="width: 100%" v-model="model.SettlementPeriod" placeholder="请选择">
                  <a-select-option value="">请选择</a-select-option>
                  <a-select-option v-for="i in 12" :key="i" :value="i">{{i}}个月</a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="原件照片：(请在供应商签字盖章后，拍照上传)" prop="listImageUrl">
                <MultiUpload ref="MultiUpload" :max="10" :images="listImageUrl" :uploadText="uploadText" :chooseFileType="chooseFileTypeShow" :fileType="fileType" :bName="BName" :dir="Dir" :readOnly="false" :fileSize="50" @choosefileTypeItem="choosefileTypeItem" @change="multiUploadChange" />
                <span>{{uploadVal}}</span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'PurchaseContractAddModal',
  components: {},
  data() {
    return {
      title: '新增采购合同',
      visible: false,
      isEdit: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      model: {
        SupplierId: '',
        SupplierName: '',
        // 合同金额
        ContractAmount: ''
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 }
      },
      uploadText: '上传文件',
      fileType: null, //3上传PDF 4上传图片
      listImageUrl: [],
      htImages: [],
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
      rangeDate: [],
      confirmLoading: false,
      httpHead: 'P36007',
      disabled: false,
      url: {
        add: '/{v}/PurchaseContract/SubmitAsync',
        info: '/{v}/PurchaseContract/GetInfoAsync',
        supplierList: '/{v}/Supplier/GetSuppliersForSelect',
      }
    }
  },
  computed: {
    rules() {
      return {
        listImageUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.listImageUrl || this.listImageUrl.length == 0) {
                callback(new Error('请上传原件照片!'))
              } else {
                callback()
              }
            }
          }
        ],
        rangeDate: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.rangeDate || this.rangeDate.length == 0) {
                callback(new Error('请选择合同效期!'))
              } else {
                callback()
              }
            }
          }
        ],
        SupplierId: [{ required: true, message: '请选择供应商!' }],
        ContractType: [{ required: true, message: '请选择合同类型!' }],
        SettlementMethod: [{ required: true, message: '请选择结算方式!' }],
        SettlementPeriod: [{ required: true, message: '请选择结算账期!' }],
        ContractAmount: [{ required: true, message: '请输入合同金额!' }],
      }
    },
    uploadVal() {
      if (this.fileType == 3 && (this.listImageUrl.length > 0 || this.htImages.length > 0)) {
        return '注：只支持pdf格式'
      } else if (this.fileType == 4 && (this.listImageUrl.length > 0 || this.htImages.length > 0)) {
        return '注：只支持png、jpg、jpeg、bmp格式的图片'
      } else {
        return '注：只支持png、jpg、jpeg、bmp、pdf格式的文件'
      }
    },
    chooseFileTypeShow() {
      if (this.htImages && this.htImages.length > 0) {
        return false
      } else {
        if (this.listImageUrl && this.listImageUrl.length > 0) {
          return false
        } else {
          return true
        }
      }
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add(supId, supName, setMethod, htImages) {
      this.title = '新增采购合同'
      this.model = {
        SupplierId: supId || '',
        SupplierName: supName || '',
        SettlementMethod: setMethod || '',
        ContractAmount: ''
      }
      this.htImages = htImages || []
      this.rangeDate = []
      this.listImageUrl = []
      this.isEdit = false
      if (supId && setMethod) {
        this.disabled = true
      } else {
        this.disabled = false
      }
      this.initFileType()
      this.visible = true
    },
    edit(record) {
      this.title = '编辑采购合同'
      this.rangeDate = []
      this.getInfo(record)
      this.isEdit = true
      this.visible = true
    },
    choosefileTypeItem(item) {
      this.fileType = item.type
    },
    initFileType() {
      if (this.htImages && this.htImages.length > 0) {
        if (this.htImages[0].indexOf('.pdf' || '.PDF') > -1) {
          this.fileType = 3
          this.uploadText = '上传PDF'
        } else {
          this.fileType = 4
          this.uploadText = '上传图片'
        }
      }
    },
    getInfo(record) {
      let formData = {
        Id: record.Id
      }
      this.fileType = 4
      this.listImageUrl = []
      getAction(this.url.info, formData, this.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            if (res.Data.ValidStartTime || res.Data.ValidEndTime) {
              this.rangeDate = [
                moment(res.Data.ValidStartTime, 'YYYY-MM-DD'),
                moment(res.Data.ValidEndTime, 'YYYY-MM-DD')
              ]
            }
            this.model = Object.assign({}, res.Data)
            if (this.model.FileList && this.model.FileList.length > 0) {
              this.listImageUrl = this.model.FileList.map(item => {
                return item.FileUrl
              })
              if (this.listImageUrl && this.listImageUrl[0].indexOf('.pdf' || '.PDF') > -1) {
                this.fileType = 3
                this.uploadText = '上传PDF'
              } else {
                this.fileType = 4
                this.uploadText = '上传图片'
              }
            } else {
              this.fileType = 4
              this.uploadText = '上传文件'
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => { })
    },
    changeSearchInput(val, name) {
      this.model.SupplierId = val
      this.model.SupplierName = name
    },
    onTypeChange(e, type) {
      console.log(e, type)
    },
    // 上传回调
    multiUploadChange(images) {
      this.listImageUrl = images
    },
    onTimeChange(dates, dateStrings) {
      if (dateStrings.length == 2) {
        this.model.ValidStartTime = dateStrings[0]
        this.model.ValidEndTime = dateStrings[1]
      } else {
        this.model.ValidStartTime = null
        this.model.ValidEndTime = null
        this.rangeDate = []
      }
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let listImageUrlData = []
          if (this.listImageUrl.length > 0) {
            listImageUrlData = this.listImageUrl.map(item => {
              return {
                FileType: 1,
                FileUrl: item
              }
            })
          }
          let formData = {
            IsDraft: false,
            ContractType: this.model.ContractType,
            SupplierId: this.model.SupplierId,
            SupplierName: this.model.SupplierName,
            ValidStartTime: this.model.ValidStartTime,
            ValidEndTime: this.model.ValidEndTime,
            SettlementMethod: this.model.SettlementMethod,
            SettlementPeriod: this.model.SettlementPeriod,
            ContractAmount: this.model.ContractAmount,
            FileList: listImageUrlData
          }
          if (this.isEdit) {
            formData.PurchaseContractId = this.model.Id
          }
          postAction(this.url.add, formData, this.httpHead)
            .then(res => {
              if (res.IsSuccess) {
                that.$message.success('操作成功')
                that.$emit('ok', res.Data)
                that.close()
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    }
  }
}
</script>
