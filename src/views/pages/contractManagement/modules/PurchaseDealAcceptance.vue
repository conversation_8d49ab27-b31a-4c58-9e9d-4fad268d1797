<template>
  <a-modal :title="title" :width="1100" :visible="visible" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true" :footer="null">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-tabs default-active-key="1" @change="tabsChange">
        <a-tab-pane key="1" tab="返钱" v-if="record.RebateType == 1 || record.RebateType == 3">
          <!-- 年度协议 -->
          <div v-if="type == 1">
            <a-row :getter="24">
              <a-descriptions :column="3" v-if="model1.Total">
                <a-descriptions-item label="应收金额">
                  {{
                    model1.Total.ReceivableAmount || model1.Total.ReceivableAmount == 0
                      ? '¥' + model1.Total.ReceivableAmount
                      : '--'
                  }}
                </a-descriptions-item>
                <a-descriptions-item label="已认款金额">
                  {{
                    model1.Total.RecognizedAmount || model1.Total.RecognizedAmount == 0
                      ? '¥' + model1.Total.RecognizedAmount
                      : '--'
                  }}
                </a-descriptions-item>
                <!-- <a-descriptions-item label="额外收入金额">
                  {{
                    model1.Total.AdditionalAmount || model1.Total.AdditionalAmount == 0
                      ? '¥' + model1.Total.AdditionalAmount
                      : '--'
                  }}
                </a-descriptions-item> -->
                <a-descriptions-item label="未认款金额">
                  {{
                    model1.Total.RemainingAmount || model1.Total.RemainingAmount == 0
                      ? '¥' + model1.Total.RemainingAmount
                      : '--'
                  }}
                </a-descriptions-item>
                <a-descriptions-item label="增加返利金额">
                  {{
                    model1.Total.RealTotalAddRebateAmount || model1.Total.RealTotalAddRebateAmount == 0
                      ? '¥' + model1.Total.RealTotalAddRebateAmount
                      : '--'
                  }}
                </a-descriptions-item>
                <a-descriptions-item label="取消返利金额">
                  {{
                    model1.Total.CancelRebateAmount || model1.Total.CancelRebateAmount == 0
                      ? '¥' + model1.Total.CancelRebateAmount
                      : '--'
                  }}
                </a-descriptions-item>
              </a-descriptions>
            </a-row>

            <div style="margin: 10px 0; font-weight: 600; color: #080808">品种认款情况：</div>
            <a-table id="resetSetFixedHeight" :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columnsPzRk" :dataSource="dataSourcePzRk" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ x: '1000px', y: dataSourcePzRk.length === 0 ? false:'350px' }">
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
              </span>
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>

            </a-table>

            <div style="margin: 10px 0; font-weight: 600; color: #080808">认款记录：</div>
            <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns1" :dataSource="dataSource1" :rowClassName="rowClassNameFun" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ x: '1000px', y: dataSource1.length === 0 ? false : '350px' }">
              <!-- 认款单号 -->
              <template slot="order" slot-scope="text, record">
                <a @click="goOrder(record)">{{ text }}</a>
              </template>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
              </span>
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 操作 -->
              <span slot="action" slot-scope="text, record, index">
                <a style="margin: 0 5px" @click="operate(record, 'delShop', index)">移除</a>
              </span>
            </a-table>
          </div>
          <!-- 短期销售协议 -->
          <div v-if="type == 2">
            <a-row :getter="24">
              <a-descriptions :column="3" v-if="model1Sale.Total">
                <a-descriptions-item label="应收金额">
                  {{
                    model1Sale.Total.ReceivableAmount || model1Sale.Total.ReceivableAmount == 0
                      ? '¥' + model1Sale.Total.ReceivableAmount
                      : '--'
                  }}
                </a-descriptions-item>
                <a-descriptions-item label="已认款金额">
                  {{
                    model1Sale.Total.RecognizedAmount || model1Sale.Total.RecognizedAmount == 0
                      ? '¥' + model1Sale.Total.RecognizedAmount
                      : '--'
                  }}
                </a-descriptions-item>
                <a-descriptions-item label="额外收入金额">
                  {{
                    model1Sale.Total.AdditionalAmount || model1Sale.Total.AdditionalAmount == 0
                      ? '¥' + model1Sale.Total.AdditionalAmount
                      : '--'
                  }}
                </a-descriptions-item>
                <a-descriptions-item label="未认款金额">
                  {{
                    model1Sale.Total.RemainingAmount || model1Sale.Total.RemainingAmount == 0
                      ? '¥' + model1Sale.Total.RemainingAmount
                      : '--'
                  }}
                </a-descriptions-item>
                <a-descriptions-item label="取消返利金额">
                  {{
                    model1Sale.Total.CancelRebateAmount || model1Sale.Total.CancelRebateAmount == 0
                      ? '¥' + model1Sale.Total.CancelRebateAmount
                      : '--'
                  }}
                </a-descriptions-item>
              </a-descriptions>
            </a-row>

            <div style="margin: 10px 0; font-weight: 600; color: #080808">品种认款情况：</div>
            <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns1Sale" :dataSource="dataSource1Sale" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ x: '1000px', y: dataSource1Sale.length === 0 ? false : '350px' }">
              <!-- 认款单号 -->
              <template slot="order" slot-scope="text, record">
                <a @click="goOrder(record)">{{ text }}</a>
              </template>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
              </span>
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 操作 -->
              <span slot="action" slot-scope="text, record, index">
                <a style="margin: 0 5px" @click="operate(record, 'delShop', index)">移除</a>
              </span>
            </a-table>

            <div style="margin: 10px 0; font-weight: 600; color: #080808">认款记录：</div>
            <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns2Sale" :dataSource="dataSource2Sale" :rowClassName="rowClassNameFun" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ x: '1000px', y: dataSource2Sale.length === 0 ? false : '350px' }">
              <!-- 认款单号 -->
              <template slot="order" slot-scope="text, record">
                <a @click="goOrder(record)">{{ text }}</a>
              </template>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
              </span>
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 操作 -->
              <span slot="action" slot-scope="text, record, index">
                <a style="margin: 0 5px" @click="operate(record, 'delShop', index)">移除</a>
              </span>
            </a-table>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="返货" v-if="record.RebateType == 2 || record.RebateType == 3">
          <!-- 年度协议 -->
          <div v-if="type == 1">
            <div style="margin: 10px 0; font-weight: 600; color: #080808">返利品种：</div>
            <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columnsFanli" :dataSource="dataSourceFanli" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ x: '1000px', y: dataSourceFanli.length === 0 ? false : '350px' }">
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
              </span>
              <!-- 操作 -->
              <span slot="action" slot-scope="text, record, index">
                <a style="margin: 0 5px" @click="operate(record, 'delShop', index)">移除</a>
              </span>
            </a-table>
            <div style="margin: 10px 0; font-weight: 600; color: #080808">认款记录：</div>
            <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns2" :dataSource="dataSource2" :rowClassName="rowClassNameFun" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ x: '1000px', y: dataSource2.length === 0 ? false : '350px' }">
              <!-- 认款单号 -->
              <template slot="order" slot-scope="text, record">
                <a @click="goOrder(record)">{{ text }}</a>
              </template>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
              </span>
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
            </a-table>
          </div>
          <div v-if="type == 2">
            <div style="margin: 10px 0; font-weight: 600; color: #080808">返利品种：</div>
            <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns1Sale" :dataSource="dataSource1Sale" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ x: '1000px', y: dataSource1Sale.length === 0 ? false : '350px' }">
              <!-- 认款单号 -->
              <template slot="order" slot-scope="text, record">
                <a @click="goOrder(record)">{{ text }}</a>
              </template>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
              </span>
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 操作 -->
              <span slot="action" slot-scope="text, record, index">
                <a style="margin: 0 5px" @click="operate(record, 'delShop', index)">移除</a>
              </span>
            </a-table>

            <div style="margin: 10px 0; font-weight: 600; color: #080808">认款记录：</div>
            <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns2Sale" :dataSource="dataSource2Sale" :rowClassName="rowClassNameFun" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ x: '1000px', y: dataSource2Sale.length === 0 ? false : '350px' }">
              <!-- 认款单号 -->
              <template slot="order" slot-scope="text, record">
                <a @click="goOrder(record)">{{ text }}</a>
              </template>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
              </span>
              <!-- 字符串超长截取省略号显示-->
              <template slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </template>
              <!-- 操作 -->
              <span slot="action" slot-scope="text, record, index">
                <a style="margin: 0 5px" @click="operate(record, 'delShop', index)">移除</a>
              </span>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
    <!-- 认款详情 -->
    <AcceptanceFunListInfoModal ref="AcceptanceFunListInfoModal"></AcceptanceFunListInfoModal>
    <!-- 特殊认款 返货抵钱现金抵返货和票折抵返货详情 -->
    <SpecialSubsScriptionInfoModal ref="SpecialSubsScriptionInfoModal"></SpecialSubsScriptionInfoModal>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'PurchaseDealAcceptance',
  mixins: [ListMixin, EditMixin],
  components: {
    JEllipsis,
  },
  props: {
    type: {
      type: Number,
      default: 1, //1年度协议 2短期销售协议
    },
  },
  data() {
    return {
      title: '查看协议认款情况',
      visible: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      model1: {
        RecognitionList: [],
        Total: {},
      },
      model2: {
        RebateGoodList: [],
        RecognitionGoodList: [],
      },
      model1Sale: {
        AgreementGoodsList: [],
        RecognitionList: [],
        Total: {},
      },
      model2Sale: {
        RebateGoodList: [],
        RecognitionGoodList: [],
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      queryCol: 8,
      queryParam: {},
      rangeDate: [],
      columns1: [
        {
          title: '认款单号',
          dataIndex: 'RecognitionOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'order' },
        },
        {
          title: '认款单类型',
          dataIndex: 'RecognitionOrderTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognitionAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        // {
        //   title: '额外收入金额',
        //   dataIndex: 'AdditionalAmount',
        //   width: 100,
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'price' },
        // },
        {
          title: '归属人',
          dataIndex: 'ResponsiblePersonName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns2: [
        {
          title: '认款单号',
          dataIndex: 'RecognitionOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'order' },
        },
        {
          title: '认款单类型',
          dataIndex: 'RecognitionOrderTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款数量',
          dataIndex: 'RecognitionCount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'ResponsiblePersonName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columnsFanli: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          fixed: 'left',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货应收数量',
          dataIndex: 'TotalRebateCount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利数量',
          dataIndex: 'CancelRebateCount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款数量',
          dataIndex: 'RecognitionCount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'RemainingCount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns1Sale: [],
      columns2Sale: [],
      columnsPzRk: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 180,
          ellipsis: true,
          fixed: 'left',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          width: 150,
          ellipsis: true,
          // fixed: 'right',
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '应收金额',
          dataIndex: 'TotalRebateAmount',
          width: 110,
          ellipsis: true,
          // fixed: 'right',
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text == 0 ? '0' : (text ? '¥' + text : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '已认款金额',
          dataIndex: 'RecognitionAmount',
          width: 110,
          ellipsis: true,
          // fixed: 'right',
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text == 0 ? '0' : (text ? '¥' + text : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '增加返利金额',
          dataIndex: 'RealTotalAddRebateAmount',
          width: 120,
          ellipsis: true,
          // fixed: 'right',
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text == 0 ? '0' : (text ? '¥' + text : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          // fixed: 'right',
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text == 0 ? '0' : (text ? '¥' + text : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          // fixed: 'right',
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text == 0 ? '0' : (text ? '¥' + text : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      columns2SaleMoney: [
        {
          title: '认款单号',
          dataIndex: 'RecognitionOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'order' },
        },
        {
          title: '认款单类型',
          dataIndex: 'RecognitionOrderTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognitionAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '责任人',
          dataIndex: 'ResponsiblePersonName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns1SaleHuo: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          fixed: 'left',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 160,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货应收数量',
          dataIndex: 'TotalRebateCount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利数量',
          dataIndex: 'CancelRebateCount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款数量',
          dataIndex: 'RecognitionCount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'RemainingCount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns2SaleHuo: [
        {
          title: '认款单号',
          dataIndex: 'RecognitionOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'order' },
        },
        {
          title: '认款单类型',
          dataIndex: 'RecognitionOrderTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款数量',
          dataIndex: 'RecognitionCount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'ResponsiblePersonName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      tableTitleArray: [],
      dataSourcePzRk: [],
      dataSource1: [],
      dataSource2: [],
      dataSourceFanli: [],
      dataSource1Sale: [],
      dataSource2Sale: [],
      record: {},
      source: null,
      tabVal: '1',
      isInitData: false,
      httpHead: 'P36012',
      url: {
        list: '',
        list1: '/{v}/Recognition/GetYearAgreementMoneyRecognitionInfoAsync',
        list2: '/{v}/Recognition/GetYearAgreementGoodsRecognitionInfoAsync',
        list1sale: '/{v}/Recognition/GetShortAgreementMoneyRecognitionInfoAsync',
        list2sale: '/{v}/Recognition/GetShortAgreementGoodsRecognitionInfoAsync',
      },
    }
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record) {
      this.visible = true
      setTimeout(() => {
        this.record = record
        this.tabVal = 1
        this.init()
      }, 100)
    },
    init(tabVal) {
      let defTabVal = null
      if (this.type == 1) {
        // 年度协议
        this.model1 = {
          RecognitionList: [],
          Total: {},
        }
        this.model2 = {
          RebateGoodList: [],
          RecognitionGoodList: [],
        }
        this.queryParam.AgreementId = this.record.Id

        if (tabVal) {
          this.queryParam.AgreementRebateType = this.tabVal
          this.url.list = this.url['list' + this.tabVal]
          defTabVal = this.tabVal
        } else {
          // RebateType 1返钱 2返货 3返货,返钱
          if (this.record.RebateType == 1 || this.record.RebateType == 3) {
            defTabVal = 1
            this.queryParam.AgreementRebateType = defTabVal
            this.url.list = this.url['list' + defTabVal]
          } else if (this.record.RebateType == 2) {
            defTabVal = 2
            this.queryParam.AgreementRebateType = defTabVal
            this.url.list = this.url['list' + defTabVal]
          }
        }
        this.loadData(1, (data) => {
          if (defTabVal == 1) {
            this.model1 = data
            this.dataSource1 = data.RecognitionList || []
            // 品种认款情况
            this.dataSourcePzRk = this.setTableMerge((data.AgreementGoodsList || []), 'PurchaseAgreementPolicyId')
            // this.resetSetFixedHeight(this.dataSourcePzRk, 'resetSetFixedHeight')
          } else if (defTabVal == 2) {
            this.model2 = data
            this.dataSourceFanli = data.RebateGoodList || []
            this.dataSource2 = data.RecognitionGoodList || []
          }
        })
      } else if (this.type == 2) {
        // 短期销售协议
        this.model1Sale = {
          AgreementGoodsList: [],
          RecognitionList: [],
          Total: {},
        }
        this.model2Sale = {
          RebateGoodList: [],
          RecognitionGoodList: [],
        }
        this.queryParam.AgreementId = this.record.Id
        if (tabVal) {
          this.queryParam.AgreementRebateType = this.tabVal
          this.url.list = this.url['list' + this.tabVal + 'sale']
          defTabVal = this.tabVal
        } else {
          if (this.record.RebateType == 1 || this.record.RebateType == 3) {
            defTabVal = 1
            this.queryParam.AgreementRebateType = defTabVal
            this.url.list = this.url['list' + defTabVal + 'sale']
          } else if (this.record.RebateType == 2) {
            defTabVal = 2
            this.queryParam.AgreementRebateType = defTabVal
            this.url.list = this.url['list' + defTabVal + 'sale']
          }
        }
        this.loadData(1, (data) => {
          if (defTabVal == 1) {
            this.model1Sale = data
            this.columns1Sale = this.columnsPzRk
            this.columns2Sale = this.columns2SaleMoney
            // 重组数据合并
            this.dataSource1Sale = this.setSource4RowSpan(data.AgreementGoodsList) || []
            this.dataSource2Sale = data.RecognitionList || []
          } else if (defTabVal == 2) {
            this.model2Sale = data
            this.columns1Sale = this.columns1SaleHuo
            this.columns2Sale = this.columns2SaleHuo
            this.dataSource1Sale = data.RebateGoodList || []
            this.dataSource2Sale = data.RecognitionGoodList || []
          }
        })
      }
    },
    setSource4RowSpan(resData) {
      if (resData && resData.length > 0) {
        // 相同的值排序
        resData = resData.sort((a, b) => {
          return a.PurchaseAgreementPolicyId > b.PurchaseAgreementPolicyId ? 1 : -1
        })

        let groupedArrObj = resData.reduce((acc, obj) => {
          let key = obj.PurchaseAgreementPolicyId;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push(obj);
          return acc;
        }, {});
        let samePolicyIdArray = Object.entries(groupedArrObj) || []
        if (samePolicyIdArray.length > 0) {
          resData.map(item => {
            let rt = samePolicyIdArray.find(r => {
              return item.PurchaseAgreementPolicyId == r[0] && r[1].length > 1
            })
            if (rt) {
              item.GoodsList = rt[1]
            }

          })
          resData.map((item, index) => {
            if (item.GoodsList && item.GoodsList.length > 0) {
              item.GoodsList.map((rItem, rIndex) => {
                if (rIndex == 0) {
                  rItem.rowSpan = item.GoodsList.length
                } else {
                  rItem.rowSpan = 0
                }
              })
            }
          })
          console.log('resData', resData)
        }
        return resData
      }
    },
    // table行的颜色
    rowClassNameFun(record, index) {
      if (record.RecognitionOrderType == 1 || record.RecognitionOrderType == 2 || record.RecognitionOrderType == 3) {
        return 'table-color-red-dust'
      }
    },
    tabsChange(e) {
      this.tabVal = e
      this.init(this.tabVal)
    },
    goOrder(record) {
      // 详情
      // RecognitionOrderMode 返货抵钱 = 1
      // 现金抵返货 = 2
      // 票折抵返货 = 3
      // 商销认款 = 10
      // 返货认款 = 16
      // 采购退货认款 = 11
      // 短期采购协议认款 = 12
      // 短期销售协议认款 = 13
      // 年度协议认款 = 14
      // 过账 = 15

      //  source //1 商销认款 2采购退货认款 3短期采购协议 4短期销售协议 5年度协议 6过账认款 9返货池已经其他页面处理 
      switch (record.RecognitionOrderMode) {
        case 1:
          this.$refs.SpecialSubsScriptionInfoModal.show(record, false, 1)
          break //返货抵钱
        case 2:
          this.$refs.SpecialSubsScriptionInfoModal.show(record, false, 2)
          break //现金抵返货
        case 3:
          this.$refs.SpecialSubsScriptionInfoModal.show(record, false, 3)
          break //票折抵返货
        case 10:
          this.source = 1
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //商销认款
        case 11:
          this.source = 2
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //采购退货认款
        case 12:
          this.source = 3
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //短期采购协议认款
        case 13:
          this.source = 4
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //短期销售协议认款
        case 14:
          this.source = 5
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //年度协议认款
        case 15:
          this.source = 6
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //过账
        case 16:
          this.source = 9
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //返货认款
        default:
          if (this.source == null) {
            this.$message.warning('当前协议认款类型未知，暂无详情数据')
            return
          }
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
      }
    },
    // 确定
    handleOk() {
      this.close()
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>
