<!--
 * @Description: 年度协议-原件变更弹窗
 * @Version: 1.0
 * @Author: Haolin.Hu
 * @Date: 2024-01-09
 * @LastEditors: wenji<PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-03-28 16:37:42
-->
<template>
  <a-modal
    title="原件变更"
    :width="600"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
        <a-row :getter="10">
          <a-col :md="24">
            <a-form-model-item label="原件图片：" prop="fileUrls">
              <MultiUpload
                :max="10"
                :images="model.fileUrls"
                :uploadText="'上传图片'"
                :fileType="4"
                :bName="BName"
                :dir="Dir"
                :readOnly="false"
                :fileSize="50"
                @change="multiUploadChange"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { postAction, getAction } from '@/api/manage'

export default {
  name: 'PurchaseOriginalChangeModal',
  data() {
    return {
      title: '原件变更',
      visible: false,
      confirmLoading: false,
      model: {
        fileUrls: []
      },
      rules: {
        fileUrls: [
          { required: true, type: 'array', message: '请上传原件图片', trigger: 'change' }
        ]
      },
      id: null,
      recordData: null,
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir']
    }
  },
  methods: {
    /**
     * 显示原件变更弹窗
     * @param {Object} record - 需要变更的记录对象
     */
    show(record) {
      this.visible = true
      this.id = record.Id
      this.recordData = record
      this.confirmLoading = true
      
      // 获取已有的原件图片
      getAction('/{v}/PurchaseAgreement/GetAgreementFileListAsync', { agreementId: this.id }, 'P36007')
        .then(res => {
          if (res.IsSuccess) {
            console.log('获取原件', res)
            let fileUrls = []
            if(res.Data && res.Data.length > 0) {
              res.Data.forEach(item => {
                fileUrls.push(item.FileUrl)
              });
            }
            this.model.fileUrls = fileUrls
          } else {
            this.$message.error(res.Msg || '获取原件图片失败')
          }
        })
        .finally(() => {
          this.confirmLoading = false
          this.$nextTick(() => {
            this.$refs.form.clearValidate()
          })
        })
    },
    /**
     * 处理多文件上传变更事件
     * @param {Array} images - 上传的图片文件数组
     */
    multiUploadChange(images) {
      this.model.fileUrls = images
    },
    /**
     * 处理确认按钮点击事件
     * 验证表单并提交原件变更请求
     */
    handleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading = true
          const url = '/{v}/PurchaseAgreement/EditPurchaseAgreementFilesAsync'
          let FilesInfo = []
          this.model.fileUrls.forEach((item, index) => {
            FilesInfo.push({
              FileType: 4,
              FileUrl: item,
              IndexNum: index
            })
          })
          const formData = {
            PurchaseAgreementId: this.id,
            Files: FilesInfo
          }

          postAction(url, formData, 'P36007')
            .then(res => {
              if (res.IsSuccess) {
                this.$message.success('操作成功')
                this.$emit('ok')
                this.handleCancel()
              } else {
                this.$message.error(res.Msg || '操作失败')
              }
            })
            .finally(() => {
              this.confirmLoading = false
            })
        }
      })
    },
    /**
     * 处理取消按钮点击事件
     * 关闭弹窗并重置表单数据
     */
    handleCancel() {
      this.visible = false
      this.model = {
        fileUrls: []
      }
    }
  }
}
</script>