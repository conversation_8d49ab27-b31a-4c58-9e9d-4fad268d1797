<!-- 新增取消返利 -->
<template>
  <a-modal :title="modalTitle" :width="width" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
        <a-row :getter="10" v-if="source == 'dqcg' || (source == 'ty' && recordData.IsLimitGoods == false)">
          <a-col :md="24">
            <a-form-model-item label="取消返利金额：" prop="CancelRebateAmount">
              <a-input-number placeholder="请输入" v-model="model.CancelRebateAmount" :min="0" :max="RemainingAmount" :precision="2" style="width: 100%" />
            </a-form-model-item>
            <a-form-model-item label="取消原因：" prop="CancelReason">
              <a-input placeholder="请输入" v-model="model.CancelReason" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <div v-if="['nd','dqxs'].includes(source) || (source == 'ty' && recordData.IsLimitGoods == true)">
          <!-- 列表 -->
          <a-tabs v-if="source != 'ty'" v-model="tabActive" @change="tabChange">
            <a-tab-pane v-if="recordData.RebateType == 1 || recordData.RebateType == 3" :key="1" tab="返钱">

            </a-tab-pane>
            <a-tab-pane v-if="(recordData.RebateType == 2 || recordData.RebateType == 3) && !isAddRebate" :key="2" tab="返货">

            </a-tab-pane>
          </a-tabs>
          <TableNewView id="resetSetFixedHeight" v-if="tabActive == 1" ref="tableView" :table="table" :columns="columns" :dataList="dataSource" @operate="operate">

          </TableNewView>
          <TableNewView id="resetSetFixedHeight2" v-if="tabActive == 2 && source != 'ty'" ref="tableView" :table="table" :columns="['nd','dqxs'].includes(source)?columns2:columns" :dataList="dataSource2" @operate="operate">

          </TableNewView>
          <a-col :md="24" style="margin-top:10px;text-align:right;">
            {{tabActive == 1?`合计${isAddRebate? '增加': '取消'}金额`:(tabActive==2?'合计取消数量':`合计${isAddRebate? '增加': '取消'}金额`)}}：{{tabActive==1?'¥':''}}{{totalNum || 0}}
          </a-col>
          <a-col :md="24" style="margin-top:10px;">
            <a-form-model-item :label="isAddRebate ? '增加原因：': '取消原因：'" prop="CancelReason">
              <a-input placeholder="请输入" v-model="model.CancelReason" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </div>

      </a-form-model>
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">
        取消
      </a-button>
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">确定</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from "@/api/manage";
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: "PurchaseCancelRebateAddModal",
  components: {
    JEllipsis,
  },
  mixins: [ListMixin],
  props: {
    // dqcg 短期采购协议 nd 年度协议 dqxs 短期销售协议 ty通用协议
    source: {
      type: String,
      default: '',
    },
    modalTitle: {
      type: String,
      default: '新增取消返利'
    },
    // 是否是增加返利
    isAddRebate: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      width: this.source == 'dqcg' ? 600 : 1000,
      visible: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      tabActive: 1,
      table: {
        operateBtns: [

        ], //右上角按钮集合
        rowKey: '',
        customSlot: [],
        showPagination: false,
        bordered: true,
      },
      columns: [],
      columns2: [],
      dqcgColumns: [
        {
          title: '操作时间',
          dataIndex: 'PartyFirstName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'PurchaseOrderNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作人',
          dataIndex: 'CreateTime',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消原因',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      ndColumns1: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          width: 200,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text ? text : '') || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '应收金额',
          dataIndex: 'TotalRebateAmount',
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text ? ('¥' + text) : (text == 0 ? '0' : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '已认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text ? ('¥' + text) : (text == 0 ? '0' : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        // {
        //   title: '额外收入金额',
        //   dataIndex: 'AdditionalAmount',
        //   width: 150,
        //   ellipsis: true,
        //   customRender: (text, record, index) => {
        //     const obj = {
        //       children: <j-ellipsis value={String(text ? ('¥' + text) : (text == 0 ? '0' : ''))} />,
        //       attrs: {},
        //     }
        //     obj.attrs.rowSpan = record.rowSpan
        //     return obj
        //   },
        // },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text ? ('¥' + text) : (text == 0 ? '0' : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '已增加返利金额',
          dataIndex: 'RealTotalAddRebateAmount',
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text ? ('¥' + text) : (text == 0 ? '0' : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '已取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text ? ('¥' + text) : (text == 0 ? '0' : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '本次取消返利金额',
          dataIndex: 'NumberNote',
          width: 150,
          ellipsis: true,
          fixed: 'right',
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <a-input-number
                  style="width:100%"
                  value={record.NumberNote}
                  precision={2}
                  onchange={(e) => this.inputNumberNote(e, record, 'NumberNote')}
                  min={0}
                  max={record.RemainingAmount}
                  placeholder="请输入"
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      ndColumns2: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          width: 200,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text ? text : '') || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '返货应收数量',
          dataIndex: 'TotalRebateCount',
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text ? (text) : (text == 0 ? '0' : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '已认款数量',
          dataIndex: 'TotalRecognitionCount',
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text ? (text) : (text == 0 ? '0' : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        // {
        //   title: '额外收入数量',
        //   dataIndex: 'CreateByName6',
        //   width: 150,
        //   ellipsis: true,
        //   customRender: (text, record, index) => {
        //     const obj = {
        //       children: <j-ellipsis value={String(text ? (text) : (text == 0 ? '0' : ''))} />,
        //       attrs: {},
        //     }
        //     obj.attrs.rowSpan = record.rowSpan
        //     return obj
        //   },
        // },
        {
          title: '未认款数量',
          dataIndex: 'RemainingCount',
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text ? (text) : (text == 0 ? '0' : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '已取消返利数量',
          dataIndex: 'CancelRebateCount',
          width: 150,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text ? (text) : (text == 0 ? '0' : ''))} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '本次取消返利数量',
          dataIndex: 'NumberNote',
          width: 150,
          ellipsis: true,
          fixed: 'right',
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <a-input-number
                  style="width:100%"
                  value={text}
                  onchange={(e) => this.inputNumberNote(e, record, 'NumberNote')}
                  min={0}
                  max={record.RemainingCount}
                  formatter={(text) => (/^\d+$/.test(text) ? text : text.slice(0, -1))}
                  placeholder="请输入"
                  precision={0}
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],

      dqxsColumns1: [],
      dqxsColumns2: [],

      tyColumns: [{
        title: '商品名称',
        dataIndex: 'ErpGoodsName',
        width: 200,
        ellipsis: true,
        scopedSlots: { customRender: 'component' },
      },
      {
        title: '规格',
        dataIndex: 'PackingSpecification',
        width: 150,
        ellipsis: true,
        scopedSlots: { customRender: 'component' },
      },
      {
        title: '单位',
        dataIndex: 'PackageUnit',
        width: 150,
        ellipsis: true,
        scopedSlots: { customRender: 'component' },
      },
      {
        title: '生产厂商',
        dataIndex: 'BrandManufacturer',
        width: 200,
        ellipsis: true,
        scopedSlots: { customRender: 'component' },
      },
      {
        title: '商品编号',
        dataIndex: 'ErpGoodsCode',
        width: 150,
        ellipsis: true,
        scopedSlots: { customRender: 'component' },
      },
      {
        title: '应收金额',
        dataIndex: 'RebateAmount',
        width: 150,
        ellipsis: true,
        customRender: (text, record, index) => {
          const obj = {
            children: <j-ellipsis value={String(text ? ('¥' + text) : (text == 0 ? '0' : ''))} />,
            attrs: {},
          }
          obj.attrs.rowSpan = record.rowSpan
          return obj
        },
      },
      {
        title: '已认款金额',
        dataIndex: 'RecognitionAmount',
        width: 150,
        ellipsis: true,
        customRender: (text, record, index) => {
          const obj = {
            children: <j-ellipsis value={String(text ? ('¥' + text) : (text == 0 ? '0' : ''))} />,
            attrs: {},
          }
          obj.attrs.rowSpan = record.rowSpan
          return obj
        },
      },
      {
        title: '额外收入金额',
        dataIndex: 'AdditionalAmount',
        width: 150,
        ellipsis: true,
        customRender: (text, record, index) => {
          const obj = {
            children: <j-ellipsis value={String(text ? ('¥' + text) : (text == 0 ? '0' : ''))} />,
            attrs: {},
          }
          obj.attrs.rowSpan = record.rowSpan
          return obj
        },
      },
      {
        title: '未认款金额',
        dataIndex: 'RemainingAmount',
        width: 150,
        ellipsis: true,
        customRender: (text, record, index) => {
          const obj = {
            children: <j-ellipsis value={String(text ? ('¥' + text) : (text == 0 ? '0' : ''))} />,
            attrs: {},
          }
          obj.attrs.rowSpan = record.rowSpan
          return obj
        },
      },
      {
        title: '已取消返利金额',
        dataIndex: 'CancelRebateAmount',
        width: 150,
        ellipsis: true,
        customRender: (text, record, index) => {
          const obj = {
            children: <j-ellipsis value={String(text ? ('¥' + text) : (text == 0 ? '0' : ''))} />,
            attrs: {},
          }
          obj.attrs.rowSpan = record.rowSpan
          return obj
        },
      },
      {
        title: '本次取消返利金额',
        dataIndex: 'NumberNote',
        width: 150,
        ellipsis: true,
        fixed: 'right',
        customRender: (text, record, index) => {
          const obj = {
            children: (
              <a-input-number
                style="width:100%"
                value={record.NumberNote}
                precision={2}
                onchange={(e) => this.inputNumberNote(e, record, 'NumberNote')}
                min={0}
                max={record.RemainingAmount}
                placeholder="请输入"
              />
            ),
            attrs: {},
          }
          obj.attrs.rowSpan = record.rowSpan
          return obj
        },
      },
      ],

      columnsFanli: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          fixed: 'left',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返货应收数量',
          dataIndex: 'TotalRebateCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '取消返利数量',
          dataIndex: 'CancelRebateCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款数量',
          dataIndex: 'RecognitionCount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'RemainingCount',
          width: 120,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '本次取消返利数量',
          dataIndex: 'NumberNote',
          width: 150,
          ellipsis: true,
          fixed: 'right',
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <a-input-number
                  style="width:100%"
                  value={text}
                  onchange={(e) => this.inputNumberNote(e, record, 'NumberNote')}
                  min={0}
                  max={record.RemainingCount}
                  formatter={(text) => (/^\d+$/.test(text) ? text : text.slice(0, -1))}
                  placeholder="请输入"
                  precision={0}
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],

      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      dataSource2: [],
      recordData: {},
      id: '',
      isInitData: false,
      confirmLoading: false,
      httpHead: 'P36007',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '',//列表数据接口
        list1: '/{v}/PurchaseAgreement/GetInfoAsync',//年度/短期销售协议列表接口
        list2: '/{v}/GeneralAgreement/GeneralAgreementDetailsAsync',//通用协议列表接口
        ndAndDqFanhuoList: '/{v}/PurchaseAgreement/GetAgreementRebateGoodListAsync',//年度协议和短期销售协议返货接口
        add1: '/{v}/AgreementCancelRebate/ShortPurchaseAgreementCancelRebateAsync',//短期采购协议取消返利
        add2: '/{v}/AgreementCancelRebate/YearOrShortSaleAgreementCancelRebateAsync',//年度协议/短期销售协议取消返利
        add3: '/{v}/AgreementCancelRebate/GeneralAgreementCancelRebateAsync',//通用协议取消返利
        add4: '/{v}/PurchaseAgreement/AddRebateAsync',//年度协议/短期销售协议新增返利
      },
    };
  },
  computed: {
    rules() {
      return {
        CancelRebateAmount: [{
          required: true,
          validator: (rule, value, callback) => {
            if (!this.model.CancelRebateAmount) {
              callback(new Error('请输入取消返利金额!'))
            } else {
              callback()
            }
          },
        },],
        CancelReason: [{ required: true, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 200, this.isAddRebate ? '增加原因':'取消原因') }],
      };
    },
    RemainingAmount() {
      let num = 0
      if (this.source == 'dqcg') {
        num = this.recordData.UnrecognizedAmount
      } else if (this.source == 'ty' && this.recordData.IsLimitGoods == false) {
        num = this.recordData.RemainingAmount
      }
      return num
    },
    totalNum() {
      let count = 0
      let array = this.tabActive == 1 ? this.dataSource : (this.tabActive == 2 ? this.dataSource2 : [])
      array.map(item => {
        if (item.NumberNote) {
          count += Number(item.NumberNote)
        }
      })
      if (this.tabActive == 1) {
        count = count.toFixed(2)
      }
      return count
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add(agreementId, recordData) {
      this.visible = true;
      this.id = agreementId
      this.recordData = recordData
      let tabActive = 1
      switch (recordData.RebateType) {
        case 1:
          tabActive = 1
          break;
        case 2:
          tabActive = 2
          break;
        case 3:
          tabActive = 1
          break;
      }
      this.init(tabActive)
      this.model = {}
    },
    init(e, type) {
      this.tabActive = e ? e : 1
      switch (this.source) {
        case 'dqcg':
          this.width = 600
          this.columns = this.dqcgColumns
          this.queryParam.Id = this.id
          break;
        case 'nd':
          if (type == 'change') {
            return
          }
          this.width = 1000
          this.setDataSource()
          break;
        case 'dqxs':
          if (type == 'change') {
            return
          }
          this.width = 1000
          this.setDataSource()
          break;
        case 'ty':
          this.url.list = this.url.list2
          this.columns = this.tyColumns
          this.queryParam.Id = this.id
          if (type != 'change' && this.recordData.IsLimitGoods == true) {
            this.width = 1000
            this.loadData(1)
          } else {
            this.width = 600
          }
          break;
      }
    },
    setDataSource() {
      this.queryParam.Id = this.id
      switch (this.tabActive) {
        case 1:
          this.url.list = this.url.list1
          if (this.source == 'nd') {
            this.columns = this['ndColumns' + this.tabActive]
            if(this.isAddRebate) {
              this.columns[this.columns.length - 1] = {
                title: '本次增加返利金额',
                dataIndex: 'NumberNote',
                width: 150,
                ellipsis: true,
                fixed: 'right',
                customRender: (text, record, index) => {
                  const obj = {
                    children: (
                      <a-input-number
                        style="width:100%"
                        value={record.NumberNote}
                        precision={2}
                        onchange={(e) => this.inputNumberNote(e, record, 'NumberNote')}
                        min={0}
                        placeholder="请输入"
                      />
                    ),
                    attrs: {},
                  }
                  obj.attrs.rowSpan = record.rowSpan
                  return obj
                },
              }
            }
          } else if (this.source == 'dqxs') {
            this['dqxsColumns' + this.tabActive] = this['ndColumns' + this.tabActive]
            this.columns = this['dqxsColumns' + this.tabActive]
          }
          this.loadData(1, (data) => {
            this.setNdAndDqDataSource(data)
            if (this.recordData.RebateType == 2 || this.recordData.RebateType == 3) {
              this.queryParam.Id = undefined
              this.queryParam.agreementId = this.id
              this.url.list = this.url.ndAndDqFanhuoList
              this.columns2 = this.columnsFanli
              this.loadData(1, (data) => {
                this.dataSource2 = data || []
              })
            }
          })
          break;
        case 2:
          this.queryParam.Id = undefined
          this.queryParam.agreementId = this.id
          this.url.list = this.url.ndAndDqFanhuoList
          this.columns2 = this.columnsFanli
          this.loadData(1, (data) => {
            this.dataSource2 = data || []
          })
          break;
      }

    },
    loadAfter(data) {
      if (this.source == 'ty') {
        this.setTyDataSource(data)
      }
      // 重新渲染
      this.resetSetFixedHeight(this.dataSource, 'resetSetFixedHeight')
      this.resetSetFixedHeight(this.dataSource2, 'resetSetFixedHeight2')
    },
    setTyDataSource(data) {
      if (!data) {
        return
      }
      this.dataSource = data.RebateGoods || []
    },
    setNdAndDqDataSource(data) {
      if (!data) {
        return
      }
      let PolicyList = data.PolicyList || []
      let fanQianPolicyArray = []
      let fanHuoPolicyArray = []
      let fanQianArray = []
      let fanHuoArray = []
      PolicyList.map(item => {
        // RebateType 1返钱 2返货
        if (item.RebateType == 1) {
          fanQianPolicyArray.push(item)
        } else if (item.RebateType == 2) {
          fanHuoPolicyArray.push(item)
        }
      })

      fanQianPolicyArray.map(item => {
        if ((item.GoodsList.length == 1 && JSON.stringify(item.GoodsList[0]) == '{}') || item.GoodsList.length == 0) {
          item.GoodsList = [{}]
          fanQianArray.push(item)
        } else {
          item.GoodsList.map((rItem, rIndex) => {
            if (rIndex == 0) {
              rItem.rowSpan = item.GoodsList.length
            } else {
              rItem.rowSpan = 0
            }
            rItem = {
              ...item,
              ...rItem,
            }
            fanQianArray.push(rItem)
          })
        }
      })
      this.dataSource = fanQianArray || []

      // fanHuoPolicyArray.map(item => {
      //   if ((item.GoodsList.length == 1 && JSON.stringify(item.GoodsList[0]) == '{}') || item.GoodsList.length == 0) {
      //     item.GoodsList = [{}]
      //     fanHuoArray.push(item)
      //   } else {
      //     item.GoodsList.map((rItem, rIndex) => {
      //       if (rIndex == 0) {
      //         rItem.rowSpan = item.GoodsList.length
      //       } else {
      //         rItem.rowSpan = 0
      //       }
      //       rItem = {
      //         ...item,
      //         ...rItem,
      //       }
      //       fanHuoArray.push(rItem)
      //     })
      //   }
      // })
      // this.dataSource2 = fanHuoArray || []
    },
    tabChange(e) {
      this.init(e, 'change')
    },
    inputNumberNote(e, record, rowKey) {
      this.$set(record, rowKey, e)
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          let formData = {}
          let url = ''
          let CancelMoneyInfoList = []
          let CancelGoodsInfoList = []
          let CancelRebateDetailList = []
          switch (this.source) {
            case 'dqcg':
              url = this.url.add1
              formData = {
                AgreementId: this.id,
                CancelReason: this.model.CancelReason,
                CancelRebateAmount: this.model.CancelRebateAmount,
              }
              break;
            case 'nd':
              url = this.isAddRebate ? this.url.add4:this.url.add2
              if(this.isAddRebate) {
                this.dataSource.map(item => {
                if (item.NumberNote) {
                  CancelMoneyInfoList.push({
                    PurchaseAgreementPolicyId: item.PurchaseAgreementPolicyId,
                    Qty: item.NumberNote,
                  })
                }
              })
              }else {
                this.dataSource.map(item => {
                if (item.NumberNote) {
                  CancelMoneyInfoList.push({
                    AgreementPolicyId: item.PurchaseAgreementPolicyId,
                    CancelRebateAmount: item.NumberNote,
                  })
                }
              })
              }
              
              this.dataSource2.map(item => {
                if (item.NumberNote) {
                  CancelGoodsInfoList.push({
                    GoodsId: item.GoodsId,
                    CancelRebateCount: item.NumberNote,
                  })
                }
              })
              if(this.isAddRebate) {
                formData = {
                  AgreementId: this.id,
                  Desc: this.model.CancelReason,
                  Details: CancelMoneyInfoList || [],
                }
              } else {
                formData = {
                  AgreementId: this.id,
                  CancelReason: this.model.CancelReason,
                  CancelMoneyInfoList: CancelMoneyInfoList || [],
                  CancelGoodsInfoList: CancelGoodsInfoList || [],
                }
              }
              
              switch (this.recordData.RebateType) {
                case 1:
                  if (CancelMoneyInfoList.length == 0) {
                    that.$message.warning("本次取消返利金额不能都不填或者为0");
                    return
                  }
                  break;
                case 2:
                  if (CancelGoodsInfoList.length == 0) {
                    that.$message.warning("本次取消返利数量不能都不填或者为0");
                    return
                  }
                  break;
                case 3:
                  if (CancelMoneyInfoList.length == 0 && CancelGoodsInfoList.length == 0) {
                    that.$message.warning("本次取消返利金额和数量不能都不填或者为0");
                    return
                  }
                  break;
              }
              break;
            case 'dqxs':
              url = this.url.add2
              this.dataSource.map(item => {
                if (item.NumberNote) {
                  CancelMoneyInfoList.push({
                    AgreementPolicyId: item.PurchaseAgreementPolicyId,
                    CancelRebateAmount: item.NumberNote,
                  })
                }
              })
              this.dataSource2.map(item => {
                if (item.NumberNote) {
                  CancelGoodsInfoList.push({
                    GoodsId: item.GoodsId,
                    CancelRebateCount: item.NumberNote,
                  })
                }
              })
              formData = {
                AgreementId: this.id,
                CancelReason: this.model.CancelReason,
                CancelMoneyInfoList: CancelMoneyInfoList || [],
                CancelGoodsInfoList: CancelGoodsInfoList || [],
              }


              switch (this.recordData.RebateType) {
                case 1:
                  if (CancelMoneyInfoList.length == 0) {
                    that.$message.warning("本次取消返利金额不能都不填或为0");
                    return
                  }
                  break;
                case 2:
                  if (CancelGoodsInfoList.length == 0) {
                    that.$message.warning("本次取消返利数量不能都不填或为0");
                    return
                  }
                  break;
                case 3:
                  if (CancelMoneyInfoList.length == 0 && CancelGoodsInfoList.length == 0) {
                    that.$message.warning("本次取消返利金额和数量不能都不填或为0");
                    return
                  }
                  break;
              }
              break;
            case 'ty':
              url = this.url.add3
              this.dataSource.map(item => {
                if (item.NumberNote) {
                  CancelRebateDetailList.push({
                    GoodsId: item.GoodsId,
                    CancelRebateAmount: item.NumberNote,
                  })
                }
              })
              formData = {
                AgreementId: this.id,
                CancelReason: this.model.CancelReason,
                CancelRebateAmount: this.recordData.IsLimitGoods == true ? undefined : this.model.CancelRebateAmount,
                CancelRebateDetailList: this.recordData.IsLimitGoods == true ? (CancelRebateDetailList || []) : [],
              }
              if (this.recordData.IsLimitGoods == true && CancelRebateDetailList.length == 0) {
                that.$message.warning("本次取消返利金额不能都不填或为0");
                return
              }
              break;
          }

          that.confirmLoading = true;
          postAction(url, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              // 成功后重新更新未认款金额
              if (this.source == 'dqcg') {
                this.recordData.UnrecognizedAmount = res.Data || 0
              } else if (this.source == 'ty' && this.recordData.IsLimitGoods == false) {
                this.recordData.RemainingAmount = res.Data || 0
              }
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    operate() {

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
