<!-- 选择返利模式搜索 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :footer="null" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-table :bordered="false" ref="table" rowKey="Id" size="small" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: '400px', x: false }">
        <!-- 字符串超长截取省略号显示-->
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </span>
        <span slot="Title" slot-scope="text">
          <div v-html="text"></div>
        </span>
        <!-- 操作 -->
        <span slot="action" slot-scope="text, record">
          <a @click="operate(record)"> 选择 </a>
        </span>
      </a-table>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel">
        取消
      </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "RebateTypeModal",
  mixins: [ListMixin],
  components: {
    JEllipsis
  },
  props: {
    params: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  data() {
    return {
      title: "选择返利模式",
      visible: false,
      model: {},
      columns: [
        {
          title: '名称',
          dataIndex: 'Title',
          width: 500,
          ellipsis: true,
          scopedSlots: { customRender: 'Title' },
        },
        // {
        //   title: '说明',
        //   dataIndex: 'Description',
        //   width: 300,
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'component' },
        // },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          // fixed: "right",
          scopedSlots: { customRender: 'action' }
        }
      ],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      httpHead: 'P36007',
      url: {
        info: '/{v}/PurchaseAgreement/GetPolicyConfigListAsync',
      },
    };
  },
  computed: {
    rules() {
      return {
        Name: [{ required: true, message: "请输入角色!" }],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show() {
      this.model = {}
      this.visible = true;
      this.getInfo()
    },
    getInfo() {
      let formData = this.params ? this.params : {}
      this.loading = true
      getAction(this.url.info, formData, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          if (res.Data && res.Data.length > 0) {
            this.dataSource = res.Data
          }
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {
        this.loading = false
      });
    },
    operate(record) {
      this.$emit('ok', record)
      this.close();
    },
    // 确定
    handleOk() {

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
