<!--
 * @Description: 流向卡明细弹框
 * @Version: 1.0
 * @Author: 
 * @Date: 2025-05-12 16:34:47
-->
<template>
  <a-modal
    title="流向卡明细"
    :visible="visible"
    :footer="null"
    @cancel="visible = false"
    width="1300px"
    destroyOnClose
  >
    <!-- 搜索 -->
    <YYLForm
      v-model="searchFormData"
      :formFields="searchFormFields"
      :isSearchMode="true"
      :col="{ md: 6, sm: 12 }"
      @reset="onReset()"
      @search="$refs.tableRef.getList(true)"
    >
      <template #buttons>
        <a-button class="mr-12" @click="handleExport">导出</a-button>
      </template>
    </YYLForm>

    <!-- 数据列表 -->
    <YYLTable
      ref="tableRef"
      url="/{v}/PurchaseAgreement/GetFlowDirectionCardDetailAsync"
      httpHead="P36007"
      method="GET"
      rowKey="Id"
      :queryParams="{ 
        ...searchFormData, 
        AgreementId: (record && record.PurchaseAgreementId) || '',
        FlowDirectionCardCode: record.FlowDirectionCardCode || '',
      }"
      :columns="columns"
      :scrollX="1200"
    />
  </a-modal>
</template>

<script>
import { downFile } from '@/api/manage'
export default {
  name: 'FlowCardDetailModal',
  data() {
    return {
      visible: false,
      record: {},
      areaValue: '',
      searchFormData: {
        CustomerKeyword: '',
        GoodsInfo: '',
        ChannelId: '',
        AreaName: '',
        CreateTime: [],
        CreateTimeBegin: null,
        CreateTimeEnd: null
      },
      searchFormFields: [
        {
          key: 'CustomerKeyword',
          label: '客户',
          type: 'input',
          placeholder: '名称'
        },
        {
          key: 'GoodsInfo',
          label: '商品',
          type: 'input',
          placeholder: '商品名称/编号'
        },
        {
          key: 'CreateTime',
          label: '时间',
          type: 'dateRange',
          placeholder: ['开始日期', '结束日期'],
          valueFormat: 'YYYY-MM-DD',
          onChange: (val) => {
            console.log('val', val)
            if (val && val.length) {
              this.searchFormData.CreateTimeBegin = val[0]
              this.searchFormData.CreateTimeEnd = val[1]
            } else {
              this.searchFormData.CreateTimeBegin = null
              this.searchFormData.CreateTimeEnd = null
            }
          }
        },
        {
          key: 'ChannelId',
          label: '采购渠道',
          type: 'remoteSelect',
          requestInfo: {
            url: '/{v}/PurchaseAgreement/GetPurchaseAgreementChannelListAsync',
            httpHead: 'P36007',
            method: 'GET',
            params: {},  // 初始化为空对象
            valueKey: 'ChannelId',
            labelKey: 'ChannelName'
          }
        },
        {
          key: 'AreaName',
          label: '地区',
          type: 'input',
          placeholder: '请输入'
        }
      ],
      columns: [
        { title: '客户', key: 'CustomerName', width: 180 },
        { title: '客户类型', key: 'CustomerType', width: 100 },
        { title: '地区', key: 'CustomerArea', width: 120 },
        { title: '商品名称', key: 'GoodsName', width: 200 },
        { title: '规格', key: 'PackingSpecification', width: 120 },
        { title: '单位', key: 'PackingUnit', width: 80 },
        { title: '生产厂商', key: 'ManufacturerName', width: 180 },
        { title: '商品编号', key: 'GoodsCode', width: 120 },
        { title: '采购单价', key: 'PriceInWarehousing', width: 100 },
        { title: '销售单价', key: 'SalePrice', width: 100 },
        { title: '销售数量', key: 'Count', width: 100 },
        { title: '时间', key: 'OrderTime', width: 160 },
        { title: '采购渠道', key: 'PurchaseSaleChannelName', width: 120 }
      ]
    }
  },
  methods: {
    show(record) {
      this.record = record
      this.visible = true
      // 在这里更新 searchFormFields 中的 params
      if (this.searchFormFields[3] && this.searchFormFields[3].requestInfo) {
        this.searchFormFields[3].requestInfo.params = {
          PurchaseAgreementId: record ? record.PurchaseAgreementId : ''
        }
      }
    },
    onReset() {
      this.searchFormData.CreateTimeBegin = null
      this.searchFormData.CreateTimeEnd = null
      this.$nextTick(() => {
        this.$refs.tableRef.getList(true)
      })
    },
    handleExport() {
      downFile(
        '/{v}/PurchaseAgreement/ExportFlowDirectionCardDetailAsync',
        {
          ...this.searchFormData,
          AgreementId: (this.record && this.record.PurchaseAgreementId) || '',
          FlowDirectionCardCode: this.record.FlowDirectionCardCode || '',
          PageIndex: 1,
          PageSize: 10000
        },
        'get',
        'P36007'
      ).then((data) => {
        if (!data) {
          message.warning('文件下载失败')
          return
        }

        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(new Blob([data]), '流向卡明细.xls')
        } else {
          // 生成a标签下载
          let url = window.URL.createObjectURL(new Blob([data]))
          let link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', '流向卡明细.xls')
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        }
      })
    },
  }
}
</script>
