<template>
  <a-modal :title="title" :width="1100" :visible="visible" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true" :footer="null">
    <div :style="{
            width: '100%',
            border: '1px solid #e9e9e9',
            padding: '10px 16px',
            background: '#fff',
        }">
      <!-- 通用协议 -->
      <div>
        <a-row :getter="24">
          <a-descriptions :column="3">
            <a-descriptions-item label="应收金额">
              {{
                                model1.Total.ReceivableAmount || model1.Total.ReceivableAmount == 0
                                    ? '¥' + model1.Total.ReceivableAmount
                                    : '--'
                            }}
            </a-descriptions-item>
            <a-descriptions-item label="已认款金额">
              {{
                                model1.Total.RecognizedAmount || model1.Total.RecognizedAmount == 0
                                    ? '¥' + model1.Total.RecognizedAmount
                                    : '--'
                            }}
            </a-descriptions-item>
            <a-descriptions-item label="额外收入金额">
              {{
                                model1.Total.AdditionalAmount || model1.Total.AdditionalAmount == 0
                                    ? '¥' + model1.Total.AdditionalAmount
                                    : '--'
                            }}
            </a-descriptions-item>
            <a-descriptions-item label="未认款金额">
              {{
                                model1.Total.RemainingAmount || model1.Total.RemainingAmount == 0
                                    ? '¥' + model1.Total.RemainingAmount
                                    : '--'
                            }}
            </a-descriptions-item>
            <a-descriptions-item label="取消返利金额">
              {{
                                model1.Total.CancelRebateAmount || model1.Total.CancelRebateAmount == 0
                                    ? '¥' + model1.Total.CancelRebateAmount
                                    : '--'
                            }}
            </a-descriptions-item>
          </a-descriptions>
        </a-row>

        <div v-if="record.IsLimitGoods">
          <div style="margin: 10px 0; font-weight: 600; color: #080808">品种认款情况：</div>
          <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns1" :dataSource="dataSource1" :pagination="false" :loading="loading" :scroll="{ x:'1000px', y: dataSource1.length === 0 ? false : '350px' }">
            <!-- 认款单号 -->
            <template slot="order" slot-scope="text, record">
              <a @click="goOrder(record)">{{ text }}</a>
            </template>
            <!-- 价格显示-->
            <span slot="price" slot-scope="text">
              <j-ellipsis :value="text || text == 0 ? (text != 0 ? '¥' + text : '0') : '--'" />
            </span>
            <!-- 字符串超长截取省略号显示-->
            <template slot="component" slot-scope="text">
              <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
              <j-ellipsis :value="text" v-else />
            </template>
          </a-table>
        </div>
        <div style="margin: 10px 0; font-weight: 600; color: #080808">认款记录：</div>
        <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns2" :dataSource="dataSource2" :rowClassName="rowClassNameFun" :pagination="false" :loading="loading" :scroll="{ x: '1000px', y: dataSource2.length === 0 ? false : '350px' }">
          <!-- 认款单号 -->
          <template slot="order" slot-scope="text, record">
            <a @click="goOrder(record)">{{ text }}</a>
          </template>
          <!-- 价格显示-->
          <span slot="price" slot-scope="text">
            <j-ellipsis :value="text || text == 0 ? (text != 0 ? '¥' + text : '0') : '--'" />
          </span>
          <!-- 字符串超长截取省略号显示-->
          <template slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </template>
        </a-table>

      </div>
    </div>
    <!-- 认款详情 -->
    <AcceptanceFunListInfoModal ref="AcceptanceFunListInfoModal"></AcceptanceFunListInfoModal>
    <!-- 特殊认款 返货抵钱现金抵返货和票折抵返货详情 -->
    <SpecialSubsScriptionInfoModal ref="SpecialSubsScriptionInfoModal"></SpecialSubsScriptionInfoModal>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'PurchaseCurrenceAcceptance',
  mixins: [ListMixin],
  components: {
    JEllipsis,
  },
  props: {
    type: {
      type: Number,
      default: 1, //1年度协议 2短期销售协议 4 通用协议
    },
  },
  data() {
    return {
      title: '查看协议认款情况',
      visible: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      model1: {
        RecognitionList: [],
        Total: {},
      },
      model2: {
        RebateGoodList: [],
        RecognitionGoodList: [],
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      queryCol: 8,
      queryParam: {},
      rangeDate: [],
      columns1: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收金额',
          dataIndex: 'TotalRebateAmount',
          width: 100,
          ellipsis: true,
          // fixed: 'right',
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '已认款金额',
          dataIndex: 'RecognitionAmount',
          width: 120,
          ellipsis: true,
          // fixed: 'right',
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 120,
          ellipsis: true,
          // fixed: 'right',
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '取消返利金额',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          // fixed: 'right',
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          // fixed: 'right',
          scopedSlots: { customRender: 'price' },
        },
      ],
      columns2: [
        {
          title: '认款单号',
          dataIndex: 'RecognitionOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'order' },
        },
        {
          title: '认款单类型',
          dataIndex: 'RecognitionOrderTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款金额',
          dataIndex: 'RecognitionAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 130,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '责任人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      tableTitleArray: [],
      dataSource1: [],
      dataSource2: [],
      record: {},
      source: null,
      isInitData: false,
      httpHead: 'P36012',
      url: {
        list: '/{v}/Recognition/GetGeneralAgreementRecognitionInfoAsync'
      },
    }
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record) {
      this.visible = true
      this.record = record
      this.init(record)
    },
    init() {
      // 通用协议
      this.model1 = {
        RecognitionList: [],
        Total: {},
      }
      this.model2 = {
        RebateGoodList: [],
        RecognitionGoodList: [],
      }

      this.queryParam.AgreementId = this.record.Id
      this.queryParam.AgreementRebateType = this.tabVal
      getAction(this.url.list, this.queryParam, this.httpHead).then(res => {
        if (res.IsSuccess) {
          this.model1 = res.Data
          this.dataSource1 = res.Data.GeneralAgreementGoodsRecognitionList || []
          this.dataSource2 = res.Data.RecognitionList || []
        }
      })
    },
    // table行的颜色
    rowClassNameFun(record, index) {
      if (record.RecognitionOrderType == 1 || record.RecognitionOrderType == 2 || record.RecognitionOrderType == 3) {
        return 'table-color-red-dust'
      }
    },
    goOrder(record) {
      // 详情
      // RecognitionOrderMode 返货抵钱 = 1
      // 现金抵返货 = 2
      // 票折抵返货 = 3
      // 商销认款 = 10
      // 返货认款 = 16
      // 采购退货认款 = 11
      // 短期采购协议认款 = 12
      // 短期销售协议认款 = 13
      // 年度协议认款 = 14
      // 过账 = 15
      // 通用协议款 = 19

      //  source //1 商销认款 2采购退货认款 3短期采购协议 4短期销售协议 5年度协议 6过账认款 9返货池已经其他页面处理  21 通用协议款
      switch (record.RecognitionOrderMode) {
        case 1:
          this.$refs.SpecialSubsScriptionInfoModal.show(record, false, 1)
          break //返货抵钱
        case 2:
          this.$refs.SpecialSubsScriptionInfoModal.show(record, false, 2)
          break //现金抵返货
        case 3:
          this.$refs.SpecialSubsScriptionInfoModal.show(record, false, 3)
          break //票折抵返货
        case 10:
          this.source = 1
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //商销认款
        case 11:
          this.source = 2
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //采购退货认款
        case 12:
          this.source = 3
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //短期采购协议认款
        case 13:
          this.source = 4
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //短期销售协议认款
        case 14:
          this.source = 5
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //年度协议认款
        case 15:
          this.source = 6
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //过账
        case 16:
          this.source = 9
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //返货认款
        case 19:
          this.source = 21
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
          break //通用协议认款
        default:
          if (this.source == null) {
            this.$message.warning('当前协议认款类型未知，暂无详情数据')
            return
          }
          this.$refs.AcceptanceFunListInfoModal.show(this.source, record)
      }
    },
    // 确定
    handleOk() {
      this.close()
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>