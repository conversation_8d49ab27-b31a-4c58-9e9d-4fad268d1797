<!--
 * @Description: 短期采购协议 - 补录协议 - 选择订单
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-04-01 09:59:35
-->
 
<template>
    <a-modal
      title="选择采购订单"
      :width="'80vw'"
      :visible="visible"
      :confirmLoading="confirmLoading"
      @cancel="handleCancel"
      @close="handleCancel"
      :maskClosable="false"
      cancelText="关闭"
      :destroyOnClose="true"
      :footer="null"
    >
      <a-spin :spinning="confirmLoading">
        <!-- 搜索 -->
        <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
        <SimpleTable
          ref="table"
          :tab="tab"
          :isTableInitData="isTableInitData"
          :queryParam="queryParam"
          :columns="columns"
          :linkHttpHead="linkHttpHead"
          :linkUrlType="linkUrlType"
          :linkUrl="linkUrl"
        >
          <span slot="PurchaseOrderNo" slot-scope="{ text, record }">
            <j-ellipsis :value="text" :length="20" />
            <p
              v-if="!record['CanSelect']"
              style="color: red; font-size: 13px; margin-bottom: 0"
            >
              {{ record['DisableReason'] || ' -- '}}
            </p>
          </span>
          <span slot="ColumnsAction" slot-scope="{ text, record }">
            <a :disabled="!record['CanSelect']" @click="handleOk(record)">选择</a>
          </span>
        </SimpleTable>
      </a-spin>
    </a-modal>
  </template>
  <script>
  import moment from 'moment'
  const JEllipsis = () => import('@/components/jeecg/JEllipsis')
  import { SimpleMixin } from '@/mixins/SimpleMixin'
  import { EditMixin } from '@/mixins/EditMixin'
  import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
  export default {
    name: 'SelectSupplementaryPurchaseShortAgreementOrderModal',
    components: { JEllipsis },
    mixins: [SimpleMixin, EditMixin],
    data() {
      return {
        confirmLoading: false,
        visible: false,
        tab: {
          rowKey: 'PurchaseOrderId',
          // rowSelection: {
          //   type: 'radio',
          //   getCheckboxProps: this.getCheckboxProps,
          // },
          bordered: true,
          sortArray: ['PurchaseOrderNo','ColumnsAction'],
        },
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '订单编号', type: 'input', value: '', key: 'PurchaseOrderNo', defaultVal: '', placeholder: '请输入订单编号' },
          {
            name: '订单类型',
            type: 'selectBasicLink',
            value: '',
            vModel: 'OrderType',
            dictCode: 'EnumSalesOrderType',
            defaultVal: '',
            placeholder: '请选择',
          },
          {
            name: '付款方式',
            type: 'selectBasicLink',
            value: '',
            vModel: 'PaymentMode',
            dictCode: 'EnumPurchasePaymentMode',
            defaultVal: '',
            placeholder: '请选择',
          },
          {
            name: '商品',
            type: 'input',
            value: '',
            vModel: 'GoodsKey',
            placeholder: '请输入商品名称/编号',
          },
        ],
        // 表头
        columns: [
          {
            title: '订单编号',
            dataIndex: 'PurchaseOrderNo',
            ellipsis: true,
            width: 200,
            scopedSlots: { customRender: 'PurchaseOrderNo' },
          },
          {
            title: '订单类型',
            dataIndex: 'OrderTypeStr',
            ellipsis: true,
            width: 120,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '付款方式',
            dataIndex: 'PaymentModeStr',
            ellipsis: true,
            width: 120,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '订单金额',
            dataIndex: 'OrderAmount',
            ellipsis: true,
            width: 100,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '创建人',
            dataIndex: 'CreateByName',
            ellipsis: true,
            width: 100,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建时间',
            dataIndex: 'CreateTime',
            ellipsis: true,
            width: 200,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            ellipsis: true,
            width: 100,
            scopedSlots: { customRender: 'ColumnsAction' },
          }
        ],
        queryParam: {},
        SupplierId:null,
        isTableInitData: false, //是否自动加载
        tableSelectedRows: [],
        linkHttpHead: 'P36007',
        linkUrlType: 'GET', //请求方式
        linkUrl: {
          list: '/{v}/SupplementaryPurchaseShortAgreement/GetPurchaseOrdersForAgreement',
        },
      }
    },
    computed: {},
    mounted() {},
    created() {},
    methods: {
      moment,
      getCheckboxProps(record) {
        let disabledBool = false
        if (!record['CanSelect']) { //是否可选（用于创建短期采购协议）
          disabledBool = true
        } 
        return {
          props: {
            disabled: disabledBool, 
            name: record.PurchaseOrderNo,
          },
        }
      },
      show(SupplierId,data = []) {
        // console.log(data)
        if(!SupplierId) {
          this.$message.error('请选择供应商')
          return
        }
        this.SupplierId = SupplierId
        this.tableSelectedRows = []
        this.visible = true
        this.queryParam = {
          SupplierId: SupplierId
        }
        this.$nextTick(() => {
          // this.$refs.table.selectedRowKeys = data.map(v => v.PurchaseOrderId)
          // this.$refs.table.selectedRows = data
          this.loadDataFun()
        })
      },
      loadDataFun() {
        if (this.$refs.table)
          this.$refs.table.loadDatas(1, this.queryParam)
      },
      searchQuery() {
        this.queryParam = this.$refs.SimpleSearchArea.queryParam
        this.queryParam.SupplierId = this.SupplierId
        this.$nextTick(() => {
          if (this.$refs.table) this.loadDataFun()
        })
      },
      handleCancel() {
        this.visible = false
        this.tableSelectedRows = []
      },
      handleSelectedRows(selectedRowKeys, selectedRows, key) {
        // console.log('selectedRowKeys   ', selectedRowKeys)
        // console.log('selectedRows   ', selectedRows)
        selectedRows.map((j) => {
          if (this.tableSelectedRows.findIndex((item) => item.PurchaseOrderId == j.PurchaseOrderId) == -1) {
            this.tableSelectedRows.push(j)
          }
        })
        this.tableSelectedRows = this.tableSelectedRows.filter(
          (item) => selectedRowKeys.findIndex((v) => v == item.PurchaseOrderId) > -1
        )
  
        // console.log('tableSelectedRows   ', this.tableSelectedRows)
      },
      handleOk(record) {
        // let tableSelectedRows = this.tableSelectedRows // (this.$refs.table && this.$refs.table.selectedRows) || []
        // console.log(tableSelectedRows.length)
        // console.log('table1SelectedRows   ', tableSelectedRows.length)
        if (!record) {
          this.$message.warning('请选择数据!')
          return
        }
        this.$emit('ok', [record])
        this.handleCancel()
      },
    },
  }
  </script>
  <style scoped></style>
  