<template>
  <div>
    <a-spin :spinning="confirmLoading">
      <div :style="{
          width: '100%',
          border: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          marginBottom: '10px',
        }">
        <div style="margin-bottom: 10px; color: #333333; font-weight: bold; font-size: 18px">{{ title }}</div>
        <a-row :getter="24">
          <a-descriptions>
            <a-descriptions-item label="协议编号">
              {{ model.AgreementNo || '--' }}
            </a-descriptions-item>
            <a-descriptions-item label="审核状态">
              {{ model.AuditStatusStr || '--' }}
            </a-descriptions-item>
            <a-descriptions-item label="协议状态">
              {{ model.AgreementStatusStr || '--' }}
            </a-descriptions-item>
            <a-descriptions-item label="认款状态">
              {{ model.RecognitionStatusStr || '--' }}
            </a-descriptions-item>
            <a-descriptions-item label="责任人">
              {{ model.ResponsiblePersonName || '--' }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ model.CreateTime || '--' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-row>
      </div>
      <div :style="{ width: '100%', border: '1px solid #e9e9e9', padding: '10px 16px', background: '#fff' }">
        <a-row :getter="24">
          <a-tabs default-active-key="1" @change="changeTabs">
            <!-- 基础信息 -->
            <a-tab-pane key="1" tab="基础信息">
              <a-row :getter="10">
                <a-col :md="24">
                  <a-descriptions>
                    <a-descriptions-item label="甲方">
                      {{ model.PartyFirstName || '--' }}
                    </a-descriptions-item>
                    <a-descriptions-item label="甲方类型">
                      {{ model.PartyFirstTypeStr || '--' }}
                    </a-descriptions-item>
                    <a-descriptions-item label="促销时间">
                      {{ model.AgreementStartTime ? model.AgreementStartTime.substring(0, 11) : '--' }} ~
                      {{ model.AgreementEndTime ? model.AgreementEndTime.substring(0, 11) : '' }}
                    </a-descriptions-item>
                    <a-descriptions-item label="核算方式" v-if="type == 1">
                      {{ model.AccountingMethodStr || '--' }}
                    </a-descriptions-item>
                    <a-descriptions-item label="指定采购渠道">
                      <span v-for="(item, index) in model.ChanneList" :key="index">
                        {{ item.ChannelName }}
                        <span v-if="index < model.ChanneList.length - 1">、</span>
                      </span>
                    </a-descriptions-item>
                  </a-descriptions>
                  <a-form-model-item label="促销政策：">
                    <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length === 0 ? false : '350px', x: '1300px' }">
                      <!-- 字符串超长截取省略号显示-->
                      <template slot="component" slot-scope="text">
                        <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                        <j-ellipsis :value="text" v-else />
                      </template>
                      <!-- 操作 -->
                      <span slot="action" slot-scope="text, record">
                        <a style="margin: 0 5px" @click="operate(record, 'copy')">详情</a>
                      </span>
                    </a-table>
                  </a-form-model-item>
                </a-col>
                <!-- <a-col :md="24">
                  <p style="color:rgba(0, 0, 0, 0.85);">返利形式：{{model.AgreementRebateTypeStr || '--'}}</p>
                </a-col> -->
                <a-col :md="24">
                  <a-col :md="8">
                    <p style="color: rgba(0, 0, 0, 0.85)">
                      返利时间：{{ model.RebateTime ? model.RebateTime.substring(0, 11) : '--' }}
                    </p>
                  </a-col>
                  <a-col :md="8" v-if="model.AccountingMethod == 2">
                    <p style="color: rgba(0, 0, 0, 0.85)">
                      流向来源：{{ model.FlowDataSourceStr || '--' }}
                    </p>
                  </a-col>
                  <a-col :md="8" v-if="model.AccountingMethod == 2">
                    <p style="color: rgba(0, 0, 0, 0.85)">
                      流向卡：
                      <template v-if="model.FlowDirectionCardList && model.FlowDirectionCardList.length">
                        <a-tag v-for="(item, index) in model.FlowDirectionCardList" 
                          :key="index" 
                          style="margin-right: 5px">
                          {{ item.FlowDirectionCardCode }}
                        </a-tag>
                      </template>
                      <template v-else>--</template>
                    </p>
                  </a-col>
                </a-col>
                <a-col :md="24">
                  <a-form-model-item label="原件照片">
                    <!-- <ImageControl
                      style="margin: 0 5px 5px 0"
                      v-for="(item, index) in model.FileList"
                      :key="index"
                      :src="item.FileUrl"
                      :width="150"
                      :height="150"
                    /> -->
                    <img style="width: 150px; height: 150px; cursor: pointer; margin: 0 10px 10px 0" v-for="(item, index) in model.FileList" :key="index" :src="item.FileUrl" @click="showImgModal(item, index)" alt="" />
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-tab-pane>
            <!-- 审核信息 -->
            <a-tab-pane key="2" tab="审核信息">
              <CusAuditInformation ref="CusAuditInformation" :approval="{}" v-if="model.AuditId && showAuditInfo" :AuditId="model.AuditId" />
            </a-tab-pane>
          </a-tabs>
        </a-row>
      </div>
    </a-spin>
    <a-affix :offset-bottom="10" class="affix pr16">
      <div style="margin-top: 10px;background-color: white; padding-right: 20px; padding-bottom: 10px">
        <a-button @click="goBack(true,type==1?'/pages/contractManagement/purchaseDealList':(type==2?'/pages/contractManagement/purchaseDealSaleShortList':''))">返回</a-button>
      </div>
    </a-affix>
    <!-- 详情 -->
    <PurchaseDealInfoModal ref="PurchaseDealInfoModal"></PurchaseDealInfoModal>
    <!-- 查看照片 -->
    <PurchaseDealImgModal ref="PurchaseDealImgModal"></PurchaseDealImgModal>
  </div>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import ImageControl from '@/components/yyl/components/ImageControl'
import { getAction, putAction, postAction } from '@/api/manage'

export default {
  name: 'PurchaseDealInfoBase',
  mixins: [ListMixin],
  components: {
    JEllipsis,
    ImageControl,
  },
  data() {
    return {
      title: '年度协议详情',
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      id: '',
      type: null, //1年度协议 2短期销售协议
      columns: [],
      columns1: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          // width: 300,
          ellipsis: false,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '返利形式',
          dataIndex: 'AgreementRebateType',
          width: 150,
          ellipsis: false,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          // fixed: "right",
          // scopedSlots: { customRender: 'action' }
          customRender: (text, record, index) => {
            let childrenVal
            childrenVal = (
              <div>
                <a onclick={() => this.operate(record, 'info', index)}>详情</a>
              </div>
            )
            const obj = {
              children: childrenVal,
              attrs: {
                rowSpan: null,
              },
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      columns2: [
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          // width: 350,
          ellipsis: false,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '返利形式',
          dataIndex: 'AgreementRebateType',
          width: 150,
          ellipsis: false,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          // fixed: "right",
          // scopedSlots: { customRender: 'action' }
          customRender: (text, record, index) => {
            let childrenVal
            childrenVal = (
              <div>
                <a onclick={() => this.operate(record, 'info', index)}>详情</a>
              </div>
            )
            const obj = {
              children: childrenVal,
              attrs: {
                rowSpan: null,
              },
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      showAuditInfo: false,
      dataSource: [],
      confirmLoading: false,
      httpHead: 'P36007',
      url: {
        info: '/{v}/PurchaseAgreement/GetInfoAsync',
      },
    }
  },
  mounted() { },
  created() {
    this.id = this.$route.query.id
    this.type = this.$route.query.type
    if (this.type == 1) {
      this.title = '年度协议详情'
    } else if (this.type == 2) {
      this.title = '短期销售协议详情'
    }
    this.$root.$emit('setPageTitle', null, this.title, this.$route.fullPath)
    this.columns = this.columns1
    this.showAuditInfo = true
    this.getInfo()
  },
  methods: {
    moment,
    getInfo() {
      let formData = {
        Id: this.id,
      }
      this.confirmLoading = true
      getAction(this.url.info, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.model = res.Data
            if (this.model.AccountingMethod == 3) {
              // 打款金额表格列
              // this.columns = this.columns2
              this.columns = this.columns1
            } else {
              // 购进和销售表格列
              this.columns = this.columns1
            }
            // 重组数据
            this.setCxTable()
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    // 还原促销政策表格
    setCxTable() {
      if (this.model.PolicyList && this.model.PolicyList.length > 0) {
        let array = []
        this.model.PolicyList.map(item => {
          if (JSON.stringify(item.GoodsList) == '{}' || item.GoodsList.length == 0) {
            item.AgreementRebateType = item.PolicyRebateMethod == 4 ? '返货' : '返钱'
            item.GoodsList = [{}]
            array.push(item)
          } else {
            item.GoodsList.map((rItem, rIndex) => {
              if (rIndex == 0) {
                rItem.rowSpan = item.GoodsList.length
              } else {
                rItem.rowSpan = 0
              }
              rItem.action = ''
              rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
              rItem.PolicyContent = item.PolicyContent
              rItem.AgreementRebateType = item.PolicyRebateMethod == 4 ? '返货' : '返钱'
              rItem.PurchaseAgreementPolicyConfigId = item.PurchaseAgreementPolicyConfigId
              array.push(rItem)
            })
          }
        })

        this.dataSource = array
      }

    },
    changeTabs(e) {
      if (e == 1) {
        this.getInfo()
        this.showAuditInfo = false
      } else {
        this.showAuditInfo = true
      }
    },
    showImgModal(item, index) {
      this.$refs.PurchaseDealImgModal.show(this.model.FileList, item, index)
    },
    // 确定
    handleOk() { },
    changeRole(val, name) {
      this.model.RoleId = val
      this.model.RoleName = name
    },
    operate(record, type) {
      // 选择品种
      if (type == 'info') {
        let clickPolicyObj = this.setClickPolicyObj(record)
        this.$refs.PurchaseDealInfoModal.show(record, clickPolicyObj, this.model)
      }
    },
    setClickPolicyObj(record, setGoodsArray) {
      // 重组政策的表格
      let goodsArray = []
      if (this.model.PolicyList && this.model.PolicyList.length > 0) {
        this.model.PolicyList.map((item, index) => {
          item.GoodsList.map((rItem) => {
            if (rItem.length === 0 || JSON.stringify(rItem) === '{}') {
              rItem = item
              rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
              rItem.index = index
              goodsArray.push(rItem)
            } else {
              rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
              rItem.index = index
              goodsArray.push(rItem)
            }

          })
        })
      }
      if (setGoodsArray) {
        this.model.dataSource = goodsArray
      }

      let PolicyIndex = null
      let clickPolicyObj = {}
      if (this.model.PolicyList && this.model.PolicyList.length > 0) {
        for (let i = 0; i < this.model.PolicyList.length; ++i) {
          if (record.PurchaseAgreementPolicyId == this.model.PolicyList[i].PurchaseAgreementPolicyId) {
            PolicyIndex = record.index
            break
          }
        }
        clickPolicyObj = this.model.PolicyList[PolicyIndex]
      }
      return clickPolicyObj
    },
    saveDraft() { },
  },
}
</script>
