<!--
 * @Description: 查看协议执行情况
 * @Version: 1.0
 * @Author: 继承
 * @Date: 2023-12-20
 * @LastEditors: wenjingGao
 * @LastEditTime: 2025-06-12 10:44:36
-->
<template>
  <a-modal
    :title="title"
    :width="1250"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :footer="null"
  >
    <a-table
      :bordered="true"
      ref="table"
      :rowKey="(record, index) => index"
      :key="'table' + tableKey"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="false"
      :loading="loading"
      @change="handleTableChange"
      :scroll="{ x: dataSource.length === 0 ? false : '900px', y: '350px' }"
    >
      <!-- 字符串超长截取省略号显示-->
      <template slot="component" slot-scope="text">
        <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
        <j-ellipsis :value="text" v-else />
      </template>
    </a-table>
    <!-- 详情 -->
    <PurchaseDealCarryOutInfo
      ref="PurchaseDealCarryOutInfo"
      :key="infoKey"
      @ok="modalOk"
      :type="type"
    ></PurchaseDealCarryOutInfo>
    <!-- 增加返利 -->
    <!-- <PurchaseDealAddRebateModal ref="PurchaseDealAddRebateModal"></PurchaseDealAddRebateModal> -->
  </a-modal>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
// import PurchaseDealAddRebateModal from './PurchaseDealAddRebateModal'
export default {
  name: 'PurchaseDealCarryOut',
  mixins: [ListMixin],
  components: {
    JEllipsis,
    // PurchaseDealAddRebateModal
  },
  props: {
    type: {
      type: Number,
      default: 1, //1年度协议 2短期销售协议
    },
  },
  data() {
    return {
      title: '查看协议执行情况',
      visible: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      infoKey: 0,
      queryParam: {},
      columns: [],
      columns1: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          width: 180,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '返利形式',
          dataIndex: 'RebateTypeStr',
          width: 90,
          ellipsis: false,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '完成状态',
          dataIndex: 'IsCompleted',
          width: 100,
          ellipsis: false,
          customRender: (text, record, index) => {
            const childrenVal = this.$createElement('span', {
              style: { color: text == true ? 'green' : 'red' },
              domProps: {
                innerHTML: text == true ? '已完成' : '未完成',
              },
            })
            const obj = {
              children: childrenVal,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '任务总量',
          dataIndex: 'TaskTotalValue',
          width: 100,
          ellipsis: false,
          customRender: (text, record, index) => {
            // console.log('任务总量', this.type, record)
            const obj = {
              children: (
                <j-ellipsis
                  value={
                    this.type == 1
                      ? (record.AccountingMethod == 2 && record.SaleCountStatisticType == 2) ||
                        (record.AccountingMethod == 3 && record.IsCasePaymentType)
                        ? ''
                        : String(text) || ''
                      : String(text) || ''
                  }
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '完成量',
          dataIndex: 'TaskCompletedValue',
          width: 100,
          ellipsis: false,
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <j-ellipsis
                  value={
                    this.type == 1
                      ? (record.AccountingMethod == 2 && record.SaleCountStatisticType == 2) ||
                        (record.AccountingMethod == 3 && record.IsCasePaymentType)
                        ? ''
                        : String(text) || ''
                      : String(text) || ''
                  }
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '完成百分比',
          dataIndex: 'TaskCompletedRatio',
          width: 120,
          ellipsis: false,
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <j-ellipsis
                  value={
                    this.type == 1
                      ? (record.AccountingMethod == 2 && record.SaleCountStatisticType == 2) ||
                        (record.AccountingMethod == 3 && record.IsCasePaymentType)
                        ? ''
                        : `${String(text) || ''}%`
                      : `${String(text) || ''}%`
                  }
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          // fixed: 'right',
          customRender: (text, record, index) => {
            let childrenVal
            childrenVal = (
              <div>
                <a onclick={() => this.operate(record, 'info', index)}>详情</a>
              </div>
            )
            const obj = {
              children: childrenVal,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      columns2: [
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          // width: 180,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis length={50} value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '完成状态',
          dataIndex: 'IsCompleted',
          width: 100,
          ellipsis: false,
          customRender: (text, record, index) => {
            const childrenVal = this.$createElement('span', {
              style: { color: text == true ? 'green' : 'red' },
              domProps: {
                innerHTML: text == true ? '已完成' : '未完成',
              },
            })
            const obj = {
              children: childrenVal,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '任务总量',
          dataIndex: 'TaskTotalValue',
          // width: 180,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <j-ellipsis
                  length={50}
                  value={
                    this.type == 1
                      ? (record.AccountingMethod == 2 && record.SaleCountStatisticType == 2) ||
                        (record.AccountingMethod == 3 && record.IsCasePaymentType)
                        ? ''
                        : String(text) || ''
                      : String(text) || ''
                  }
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '完成量',
          dataIndex: 'TaskCompletedValue',
          // width: 180,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <j-ellipsis
                  length={50}
                  value={
                    this.type == 1
                      ? (record.AccountingMethod == 2 && record.SaleCountStatisticType == 2) ||
                        (record.AccountingMethod == 3 && record.IsCasePaymentType)
                        ? ''
                        : String(text) || ''
                      : String(text) || ''
                  }
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '完成百分比',
          dataIndex: 'TaskCompletedRatio',
          // width: 180,
          ellipsis: true,
          customRender: (text, record, index) => {
            const obj = {
              children: (
                <j-ellipsis
                  length={50}
                  value={
                    this.type == 1
                      ? (record.AccountingMethod == 2 && record.SaleCountStatisticType == 2) ||
                        (record.AccountingMethod == 3 && record.IsCasePaymentType)
                        ? ''
                        : `${String(text) || ''}%`
                      : `${String(text) || ''}%`
                  }
                />
              ),
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          customRender: (text, record, index) => {
            let childrenVal
            childrenVal = (
              <div>
                <a onclick={() => this.operate(record, 'info', index)}>详情</a>
              </div>
            )
            const obj = {
              children: childrenVal,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      dataSource: [],
      infoData: {},
      listRecord: {},
      tableKey: 0,
      isInitData: false,
      httpHead: 'P36007',
      url: {
        list: '/{v}/ExecutionOrder/GetAgreementExecutionAsync',
        info: '/{v}/PurchaseAgreement/GetInfoAsync',
      },
    }
  },
  mounted() {},
  created() {},
  methods: {
    moment,
    show(record) {
      console.log(record)
      this.infoKey = 0
      this.tableKey = 0
      this.listRecord = record
      this.visible = true
      this.queryParam.purchaseAgreementId = record.Id
      if (record.AccountingMethod == 1 || record.AccountingMethod == 2) {
        // 购进销售
        this.columns = this.columns1
      } else if (record.AccountingMethod == 3) {
        // 金额
        this.columns = this.columns2
      }
      // 详情
      this.getInfo(record)
      this.getTableData(1)
    },
    getTableData(page) {
      this.loadData(page, (source) => {
        let array = []
        // 重组数据
        if (source.length > 0) {
          source.map((item, index) => {
            if (item.GoodsList && item.GoodsList.length > 0) {
              item.GoodsList.map((rItem, rIndex) => {
                if (rIndex === 0) {
                  rItem.rowSpan = item.GoodsList.length
                } else {
                  rItem.rowSpan = 0
                }
                array.push({
                  PurchaseAgreementPolicyId: item.PurchaseAgreementPolicyId,
                  ErpGoodsName: rItem.ErpGoodsName,
                  PackingSpecification: rItem.PackingSpecification,
                  PackageUnit: rItem.PackageUnit,
                  BrandManufacturer: rItem.BrandManufacturer,
                  ErpGoodsCode: rItem.ErpGoodsCode,
                  PolicyContent: item.PolicyContent,
                  RebateTypeStr: rItem.RebateTypeStr,
                  TaskTotalValue: item.TaskTotalValue,
                  TaskCompletedValue: item.TaskCompletedValue,
                  TaskCompletedRatio: item.TaskCompletedRatio,
                  IsCompleted: item.IsCompleted,
                  RebateType: rItem.RebateType,
                  index: index,
                  rowSpan: rItem.rowSpan,
                  AccountingMethod: item.AccountingMethod,
                  SaleCountStatisticType: item.SaleCountStatisticType,
                  IsCasePaymentType: item.IsCasePaymentType,
                })
              })
            } else {
              array.push(item)
            }
          })
        }
        this.dataSource = array
        setTimeout(() => {
          this.tableKey++
        })
      })
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        if (this.sort) {
          // 订单列表 排序方式
          this.queryParam[this.sort] = 'ascend' == sorter.order ? '1' : '2'
        }
      }
      this.ipagination = pagination
      this.getTableData()
    },
    getInfo(record) {
      let formData = {
        Id: record.Id,
      }
      this.confirmLoading = true
      getAction(this.url.info, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.infoData = res.Data
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    modalOk() {
      this.close()
    },
    operate(record, type, index) {
      if (type == 'info') {
        this.infoKey++
        this.$nextTick(() => {
          this.$refs.PurchaseDealCarryOutInfo.show(record, this.infoData, this.listRecord)
        })
      } else if (type == 'addRebate') {
        this.$refs.PurchaseDealAddRebateModal.show(record)
      }
    },
    // 确定
    handleOk() {
      this.close()
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .ant-table-tbody .blue {
  background-color: #e6f7ff !important;
  // height: 40px !important;
  border: none !important;
  padding: 0 !important;
}
</style>
