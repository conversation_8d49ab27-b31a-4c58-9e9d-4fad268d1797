<template>
  <div>
    <div style="background: #fbe6e9; padding: 10px 16px; color: red" v-if="model.AuditStatus == 3 && model.AuditOpinion" :md="24">
      <span>驳回原因：{{ model.AuditOpinion || '' }}</span>
    </div>
    <div :style="{ width: '100%', border: '1px solid #e9e9e9', padding: '10px 16px', background: '#fff' }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model">
          <a-row :gutter="10">
            <a-col :md="8">
              <a-form-model-item label="甲方：" prop="PartyFirstId">
                <div style="display: flex;">
                  <SingleInputSearchView
                    class="party-search"
                    placeholder="请选择供应商/生产厂家"
                    width="100%"
                    :httpParams="{
                      pageIndex: 1,
                      pageSize: 200,
                      AuthBusiness: 2,
                      IsValid: true,
                    }"
                    keyWord="KeyWord"
                    disabled
                    httpHead="P36003"
                    :Url="url.supplierList"
                    :noShowList="true"
                    :value="model.PartyFirstId"
                    :dataKey="{ name: 'Name', value: 'Id' }" 
                    :name="model.PartyFirstName" 
                    :isAbsolute="true" 
                    @clear="() => {
                      model.PartyFirstId = ''
                      model.PartyFirstName = ''
                    }
                    "
                    @change="changeSearchInput" />
                  <a @click="searchDataInput">选择</a>
                </div>
              </a-form-model-item>
            </a-col>
            <a-col :md="8">
              <a-form-model-item label="甲方类型：" prop="PartyFirstType">
                <a-select v-model="model.PartyFirstType" disabled>
                  <a-radio :value="1"> 供应商 </a-radio>
                  <a-radio :value="2"> 生产厂家 </a-radio>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :md="8">
              <a-form-model-item label="促销时间：" prop="rangeDate">
                <a-range-picker @change="onTimeChange" valueFormat="YYYY-MM-DD" v-model="rangeDate" style="width: 100%" />
              </a-form-model-item>
            </a-col>

            <a-col :md="8" v-if="type == 1">
              <a-form-model-item label="核算方式：" prop="AccountingMethod">
                <a-radio-group v-model="model.AccountingMethod" @change="(e) => onTypeChange(e, 'hsfs')">
                  <a-radio :value="1"> 购进 </a-radio>
                  <a-radio :value="2"> 销售 </a-radio>
                  <a-radio :value="3"> 打款金额 </a-radio>
                </a-radio-group>
              </a-form-model-item>
            </a-col>

            <a-col :md="24">
              <a-col :md="8" v-if="type == 1 || type == 2">
                <a-form-model-item label="指定采购渠道：(请选择发货的供应商)" prop="ChanneList">
                  <SelectMultipleInput
                    ref="SelectMultipleInput"
                    :httpParams="{
                      pageIndex: 1,
                      pageSize: 200,
                      IsValid: true,
                    }"
                    keyWord="KeyWord"
                    :value="model.ChanneList"
                    :dataKey="{ name: 'Name', value: 'Id' }"
                    placeholder="搜索名称/编号/拼音码,可多选"
                    httpHead="P36003"
                    :url="url.supplierList"
                    @change="changeSupplier" />
                </a-form-model-item>
              </a-col>
            </a-col>
            <a-col :md="24">
              <a-form-model-item label="促销政策：" prop="PolicyList">
                <div
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    border: 1px solid #e8e8e8;
                    border-bottom: none;
                    padding: 5px 10px;
                  ">
                  <div>已配政策</div>
                  <div>
                    <a-button type="primary" @click="operate(null, 'chooseVariety')">选择品种，并配置促销内容</a-button>
                  </div>
                </div>
                <a-table
                  :bordered="true"
                  ref="table"
                  :rowKey="(record, index) => index"
                  :columns="columns"
                  :dataSource="dataSource"
                  :pagination="false"
                  @change="handleTableChange"
                  :scroll="{ x: '1200px', y: dataSource.length === 0 ? false : '450px' }">
                  <!-- 字符串超长截取省略号显示-->
                  <template slot="component" slot-scope="text">
                    <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                    <j-ellipsis :value="text" v-else />
                  </template>
                  <!-- 操作 -->
                  <span slot="action" slot-scope="text, record, index">
                    <a style="margin: 0 5px" @click="operate(record, 'copy', index)">复制</a>
                    <a style="margin: 0 5px" @click="operate(record, 'edit', index)">编辑</a>
                    <a style="margin: 0 5px" @click="operate(record, 'del', index)">移除</a>
                  </span>
                </a-table>
              </a-form-model-item>
            </a-col>
            <a-col :md="24">
              <a-col :md="8">
                <a-form-model-item label="返利时间：" prop="RebateTime">
                  <a-date-picker style="width: 100%" valueFormat="YYYY-MM-DD" v-model="model.RebateTime" @change="onTimeChange2" />
                </a-form-model-item>
              </a-col>
              <a-col :md="8" v-if="type == 1">
                <a-form-model-item label="责任人(负责该协议收款的采购人员)：" prop="ResponsiblePersonId">
                  <SingleInputSearchView
                    placeholder="搜索姓名/手机号"
                    width="100%"
                    :httpParams="{
                      pageIndex: 1,
                      pageSize: 200,
                      Type: 1,
                      IsValid: true,
                    }"
                    keyWord="KeyWord"
                    httpHead="P36001"
                    :Url="url.personList"
                    :value="model.ResponsiblePersonId"
                    :dataKey="{ name: 'Name', value: 'Id' }"
                    :name="model.ResponsiblePersonName"
                    :isAbsolute="true"
                    @clear="() => {
                      model.ResponsiblePersonId = null
                      model.ResponsiblePersonName = null
                    }
                    "
                    @change="changeSearchPersonInput" />
                </a-form-model-item>
              </a-col>
              <a-col :md="8" v-if="model.AccountingMethod == 2">
                <a-form-model-item label="流向来源：" prop="FlowDataSource">
                  <EnumSingleChoiceView style="width: 100%" placeholder="请选择" dictCode="EnumFlowDataSource" v-model="model.FlowDataSource" @change="handleChooseFlowDataSource" />
                </a-form-model-item>
              </a-col>
              <a-col :md="8" v-if="model.AccountingMethod == 2 && model.FlowDataSource !== '2'">
                <a-form-model-item label="流向卡：">
                  <div class="flowCardCont">
                    <div class="cardCont">
                      <template v-if="model.FlowDataList && model.FlowDataList.length > 0">
                        <a-tag 
                          v-for="(item, index) in model.FlowDataList" 
                          :key="item.record_key"
                          closable
                          @close="delFlowCard(index)" >
                          {{ item.FlowDirectionCardCode }}
                        </a-tag>
                      </template>
                    </div>
                    <a @click="$refs.TableSelectFlowDataModal.show(model.FlowDataList)">选择</a>
                  </div>
                </a-form-model-item>
              </a-col>
            </a-col>
            <a-col :md="24">
              <a-form-model-item label="原件照片(请在甲方签字盖章后，拍照上传)：" prop="listImageUrl">
                <MultiUpload
                  :max="10"
                  :images="listImageUrl"
                  :uploadText="uploadText"
                  :fileType="4"
                  :bName="BName"
                  :dir="Dir"
                  :readOnly="false"
                  :fileSize="50"
                  @change="multiUploadChange" />
                <span>注：只支持png、jpg、jpeg、bmp格式的图片</span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-affix :offset-bottom="10" class="affix pr16">
      <div style="margin-top: 10px;background-color: white; padding-right: 20px; padding-bottom: 10px;">
        <a-button :disabled="confirmLoading" class="mr8" @click="handleOk(1)" type="primary">提交审核</a-button>
        <a-button :disabled="confirmLoading" class="mr8" @click="handleOk(2)">存为草稿</a-button>
        <a-button @click="goBack(true,type==1?'/pages/contractManagement/purchaseDealList':(type==2?'/pages/contractManagement/purchaseDealSaleShortList':''))">返回</a-button>
      </div>
    </a-affix>
    <!-- 新增促销政策 -->
    <PurchaseDealPromotionAddModal ref="PurchaseDealPromotionAddModal" :key="model.AccountingMethod" :purchaseType="type" @ok="modalOk">
    </PurchaseDealPromotionAddModal>
    <!-- 表格选择甲方弹窗 -->
    <TableSelectDataModal ref="TableSelectDataModal" type="radio" :selectTabData="selectTabData" @chooseData="chooseData">
    </TableSelectDataModal>
    <!-- 表格选择设置流向卡弹窗 -->
    <TableSelectDataModal ref="TableSelectFlowDataModal" :selectTableData="selectFlowData" @chooseData="chooseFlowData"></TableSelectDataModal>
  </div>
</template>

<script>
import Vue from 'vue'
import moment from 'moment'
import { USER_ID } from '@/store/mutation-types'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'

export default {
  title: '新增协议',
  name: 'PurchaseDealAddBase',
  mixins: [ListMixin],
  components: {
    JEllipsis,
  },
  data() {
    return {
      title: '新增协议',
      isEdit: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      model: {
        PartyFirstId: '',
        PartyFirstName: '',
        ResponsiblePersonId: null,
        FlowDataSource: null,
        ResponsiblePersonName: null,
        RebateTime: null,
        FileList: [],
        ChanneList: [],
        PolicyList: [],
        FlowDataList: [],
      },
      modalData: {
        rItemObj: {},
        dataSource: [],
        PolicyContent: '',
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      columns: [],
      columns1: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          width: 250,
          ellipsis: false,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis length={30} value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '返利形式',
          dataIndex: 'AgreementRebateType',
          width: 150,
          ellipsis: false,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 220,
          // fixed: "right",
          // scopedSlots: { customRender: 'action' },
          customRender: (text, record, index) => {
            let childrenVal
            childrenVal = (
              <div>
                <a onclick={() => this.operate(record, 'copy', index)}>复制</a>
                <a style="margin:0 5px;" onclick={() => this.operate(record, 'edit', index)}>
                  编辑
                </a>
                <a onclick={() => this.operate(record, 'del', index)}>移除</a>
              </div>
            )
            const obj = {
              children: childrenVal,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      columns2: [
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          // width: 350,
          ellipsis: false,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis length={30} value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '返利形式',
          dataIndex: 'AgreementRebateType',
          width: 150,
          ellipsis: false,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text) || ''} />,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 220,
          // fixed: "right",
          // scopedSlots: { customRender: 'action' },
          customRender: (text, record, index) => {
            let childrenVal
            childrenVal = (
              <div>
                <a onclick={() => this.operate(record, 'copy', index)}>复制</a>
                <a style="margin:0 5px;" onclick={() => this.operate(record, 'edit', index)}>
                  编辑
                </a>
                <a onclick={() => this.operate(record, 'del', index)}>移除</a>
              </div>
            )
            const obj = {
              children: childrenVal,
              attrs: {},
            }
            obj.attrs.rowSpan = record.rowSpan
            return obj
          },
        },
      ],
      selectTabData: [
        {
          tabTitle: '供应商',
          selectTableData: {
            searchInput: [
              //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
              {
                name: '供应商',
                type: 'input',
                value: '',
                key: 'KeyWord',
                defaultVal: '',
                placeholder: '名称/编号/拼音码',
              },
            ],
            title: '选择供应商',
            name: '供应商',
            recordKey: 'Id',
            httpHead: 'P36003',
            isInitData: false,
            queryParam: {
              AuthBusiness: 2,
            },
            url: {
              list: '/{v}/Supplier/GetSuppliersForSelect',
              listType: 'GET',
            },
            columns: [
              {
                title: '供应商名称',
                dataIndex: 'Name',
                width: 200,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '供应商编号',
                dataIndex: 'Code',
                width: 150,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '注册地址',
                dataIndex: 'RegAddress',
                width: 150,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '操作',
                dataIndex: 'action',
                align: 'center',
                width: 100,
                fixed: 'right',
                scopedSlots: { customRender: 'action' },
              },
            ],
          },
        },
        {
          tabTitle: '生产厂家',
          selectTableData: {
            searchInput: [
              //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
              {
                name: '生产厂家',
                type: 'input',
                value: '',
                key: 'KeyWord',
                defaultVal: '',
                placeholder: '名称/编号/拼音码',
              },
            ],
            title: '选择生产厂家',
            name: '生产厂家',
            recordKey: 'Id',
            httpHead: 'P36006',
            isInitData: false,
            url: {
              list: '/{v}/BrandManufacturer/QueryBrandManufacturerList',
              listType: 'GET',
            },
            columns: [
              {
                title: '生产厂家名称',
                dataIndex: 'Name',
                width: 200,
                ellipsis: true,
                scopedSlots: { customRender: 'component' },
              },
              {
                title: '操作',
                dataIndex: 'action',
                align: 'center',
                width: 100,
                fixed: 'right',
                scopedSlots: { customRender: 'action' },
              },
            ],
          },
        }
      ],
      uploadText: '上传图片',
      listImageUrl: [],
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
      id: '',
      isCopy: null,
      type: null, //1 年度协议 2 短期销售协议
      rangeDate: [],
      AccountingMethod: null,
      confirmLoading: false,
      httpHead: 'P36007',
      url: {
        list: '',
        add: '/{v}/PurchaseAgreement/SubmitAsync',
        info: '/{v}/PurchaseAgreement/GetInfoAsync',
        supplierList: '/{v}/Supplier/GetSuppliersForSelect',
        personList: '/{v}/UserPermission/User/List',
        createAudit: '/{v}/ApprovalWorkFlowInstance/CreateApproval',
      },
      selectFlowData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          {
            name: '流向卡编号',
            type: 'input',
            value: '',
            key: 'FlowDirectionCardCode',
            defaultVal: '',
            placeholder: '请输入编号',
          },
          {
            name: '流向名称',
            type: 'input',
            value: '',
            key: 'FlowDirectionCardName',
            defaultVal: '',
            placeholder: '请输入流向名称',
          },
          {
            name: '创建时间',
            type: 'timeInput',
            value: '',
            rangeDate: [],
            key: ['CreationTimeStart', 'CreationTimeEnd'],
            defaultVal: ['', ''],
            placeholder: ['开始日期', '结束日期'],
          },
        ],
        title: '配置流向',
        name: '流向',
        recordKey: 'FlowDirectionCardCode',
        httpHead: 'P36007',
        isInitData: false,
        url: {
          list: '/{v}/PurchaseAgreement/GetErpFlowDirectionCardPageListAsync',
          listType: 'GET',
        },
        columns: [
          {
            title: '流向卡编号',
            dataIndex: 'FlowDirectionCardCode',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '流向名称',
            dataIndex: 'FlowDirectionCardName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建时间',
            dataIndex: 'CreationTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '最后修改时间',
            dataIndex: 'LastModifyTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '说明',
            dataIndex: 'Description',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
    }
  },
  computed: {
    rules() {
      return {
        listImageUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.listImageUrl || this.listImageUrl.length == 0) {
                callback(new Error('请上传原件照片!'))
              } else {
                callback()
              }
            },
          },
        ],
        rangeDate: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.rangeDate || this.rangeDate.length == 0) {
                callback(new Error('请选择促销时间!'))
              } else {
                callback()
              }
            },
          },
        ],
        PolicyList: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.PolicyList || this.model.PolicyList.length == 0) {
                callback(new Error('请选择促销政策!'))
              } else {
                callback()
              }
            },
          },
        ],
        ChanneList: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.ChanneList || this.model.ChanneList.length == 0) {
                callback(new Error('请选择指定采购渠道!'))
              } else {
                callback()
              }
            },
          },
        ],
        RebateTime: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.model.RebateTime) {
                this.model.RebateTime = this.model.RebateTime.substring(0, 10)
              }
              if (this.model.AgreementStartTime) {
                this.model.AgreementStartTime = this.model.AgreementStartTime.substring(0, 10)
              }
              if (this.model.AgreementEndTime) {
                this.model.AgreementEndTime = this.model.AgreementEndTime.substring(0, 10)
              }
              if (!this.model.RebateTime) {
                callback(new Error('请选择返利时间!'))
              } else if (this.model.RebateTime < this.model.AgreementStartTime) {
                callback(new Error('返利时间必须大于促销开始时间!'))
              } else if (this.model.RebateTime < this.model.AgreementEndTime) {
                callback(new Error('返利时间必须大于促销结束时间!'))
              } else {
                callback()
              }
            },
          },
        ],
        PartyFirstId: [{ required: true, message: '请选择甲方!' }],
        ResponsiblePersonId: [{ required: true, message: '请选择责任人!' }],
        FlowDataSource: [{ required: true, message: '请选择流向来源!' }],
        AccountingMethod: [{ required: true, message: '请选择核算方式!' }],
        SettlementMethod: [{ required: true, message: '请选择结算方式!' }],
        SettlementPeriod: [{ required: true, message: '请选择结算账期!' }],
      }
    },
  },
  mounted() { },
  created() {
    this.id = this.$route.query.id
    this.type = Number(this.$route.query.type) //1年度协议 2短期销售协议
    switch (this.type) {
      case 1:
        this.title = '年度协议'
        break;
      case 2:
        this.title = '短期销售协议'
        break;
    }
    this.isCopy = this.$route.query.isCopy
    this.columns = this.columns1
    if (this.id) {
      this.edit(this.id)
    } else {
      this.add()
    }
  },
  methods: {
    moment,
    add() {
      this.title = '新增' + this.title
      this.setTitle(this.title)
      this.model = {
        PartyFirstId: '',
        PartyFirstName: '',
        ResponsiblePersonId: null,
        ResponsiblePersonName: null,
        RebateTime: null,
        FileList: [],
        ChanneList: [],
        PolicyList: [],
      }
      // 短期默认销售
      if (this.type == 2) {
        this.AccountingMethod = 2
        this.$set(this.model, 'AccountingMethod', 2)
      } else {
        this.AccountingMethod = null
        this.$set(this.model, 'AccountingMethod', null)
      }
      this.rangeDate = []
      this.listImageUrl = []
      this.isEdit = false
    },
    edit(id) {
      if (this.isCopy) {
        this.id = ''
        this.title = '新增' + this.title
      } else {
        this.title = '编辑' + this.title
      }
      setTimeout(() => {
        this.setTitle(this.title)
      });
      this.rangeDate = []
      this.getInfo(id)
      this.isEdit = true
    },
    getInfo(id) {
      let formData = {
        Id: id,
      }
      getAction(this.url.info, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            // 流向来源
            res.Data.FlowDataSource = (res.Data.FlowDataSource || res.Data.FlowDataSource == 0) ? String(res.Data.FlowDataSource) : null
            // 数据源
            this.model = Object.assign({}, res.Data)
            this.model.FlowDataList = res.Data.FlowDirectionCardList || []
            // 数据重组
            if (res.Data.AgreementStartTime || res.Data.AgreementEndTime) {
              this.rangeDate = [
                moment(res.Data.AgreementStartTime, 'YYYY-MM-DD'),
                moment(res.Data.AgreementEndTime, 'YYYY-MM-DD'),
              ]
            }
            if (this.model.FileList && this.model.FileList.length > 0) {
              this.listImageUrl = this.model.FileList.map((item) => {
                return item.FileUrl
              })
            }
            if (this.model.ChanneList && this.model.ChanneList.length > 0) {
              this.model.ChanneList = this.model.ChanneList.map((item) => {
                return {
                  key: item.ChannelId,
                  label: item.ChannelName,
                }
              })
            } else {
              this.model.ChanneList = []
            }
            if (this.model.AccountingMethod == 3) {
              // 打款金额表格列
              // this.columns = this.columns2
              this.columns = this.columns1
            } else {
              // 购进和销售表格列
              this.columns = this.columns1
            }
            if (this.model.AccountingMethod != 2) {
              this.model.FlowDataSource = undefined
            }
            this.AccountingMethod = this.model.AccountingMethod
            // 还原促销政策表格
            this.setCxTable()
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => { })
    },
    searchDataInput() {
      let data = [
        {
          Name: this.model.PartyFirstName,
          Id: this.model.PartyFirstId,
        }
      ]
      this.$refs.TableSelectDataModal.show(data)
    },
    chooseData(data) {
      if (data && data.length > 0) {
        if (data[0].tabActiveKey == 0) {
          this.model.PartyFirstType = 1
        } else if (data[0].tabActiveKey == 1) {
          this.model.PartyFirstType = 2
        }
        let val = data[0].Id
        let name = data[0].Name

        this.model.PartyFirstId = val
        this.model.PartyFirstName = name
      }
    },
    // 选择的流向卡数据
    chooseFlowData(data) {
      // 打印传入的数据，用于调试
      console.log('chooseFlowData', data)
      
      // 创建一个全新的数组用于存储最终结果
      let newList = [];
      
      // 如果已有数据，使用深拷贝复制现有数据，避免引用问题
      if (this.model.FlowDataList && this.model.FlowDataList.length > 0) {
        newList = JSON.parse(JSON.stringify(this.model.FlowDataList));
      }
      // 处理新数据，避免重复添加
      if (data && data.length > 0) {
        data.forEach(newItem => {
          // 使用some方法检查是否存在相同的FlowDirectionCardCode
          const exists = newList.some(item => 
            item.FlowDirectionCardCode === newItem.FlowDirectionCardCode
          );
          // 如果不存在，则添加新项（使用深拷贝避免引用问题）
          if (!exists) {
            newList.push(JSON.parse(JSON.stringify(newItem)));
          }
        });
      }
      
      // // Vue响应式更新处理
      // // 1. 先清空数组触发响应式
      // this.model.FlowDataList = [];
      
      // // 2. 在下一个tick中设置新值并强制更新
      // this.$nextTick(() => {
      //   this.$set(this.model, 'FlowDataList', newList);
      //   // 添加强制更新确保视图同步
      //   this.$forceUpdate();
      // })
      this.$set(this.model, 'FlowDataList', newList);
      this.$forceUpdate();
      // 打印更新后的数据，用于调试
      console.log('FlowDataList', this.model.FlowDataList)
    },
    // 删除已选择的流向卡
    delFlowCard(index) {
      this.model.FlowDataList.splice(index, 1)
    },
    modalOk(model, record, isAdd, zcData, isCopy) {
      let dataSourceArray = []
      if (model.dataSource.length > 0) {
        model.dataSource.forEach((item, index) => {
          if (index == 0) {
            item.rowSpan = model.dataSource.length
          } else {
            item.rowSpan = 0
          }
          item.action = ''
          item.PolicyContent = model.PolicyContent
          dataSourceArray.push(item)
        })
      }
      this.modalData = model
      if (this.model.AccountingMethod == 3) {
        // 打款金额表格列
        // this.columns = this.columns2
        this.columns = this.columns1
      } else {
        // 购进和销售表格列
        this.columns = this.columns1
      }
      if (isAdd) {
        this.dataSource = this.dataSource.concat(dataSourceArray)
        this.model.PolicyList.push(zcData)
      } else if (isCopy == true) {
        this.dataSource = this.dataSource.concat(dataSourceArray)
        this.model.PolicyList.push(zcData)
      } else {
        this.dataSource = dataSourceArray
        this.model.PolicyList[record.index] = zcData
      }
      // 还原促销政策表格
      this.setCxTable()
      this.$refs.form.validateField(['PolicyList'])
      console.log('PolicyList', this.model.PolicyList)
    },
    // 还原促销政策表格
    setCxTable() {
      if (this.model.PolicyList && this.model.PolicyList.length > 0) {
        let array = []
        this.model.PolicyList.map(item => {
          if ((item.GoodsList.length == 1 && JSON.stringify(item.GoodsList[0]) == '{}') || item.GoodsList.length == 0) {
            item.AgreementRebateType = item.PolicyRebateMethod == 4 ? '返货' : '返钱'
            item.GoodsList = [{}]
            array.push(item)
          } else {
            item.GoodsList.map((rItem, rIndex) => {
              if (rIndex == 0) {
                rItem.rowSpan = item.GoodsList.length
              } else {
                rItem.rowSpan = 0
              }
              rItem.action = ''
              rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
              rItem.PolicyContent = item.PolicyContent
              rItem.AgreementRebateType = item.PolicyRebateMethod == 4 ? '返货' : '返钱'
              rItem.PurchaseAgreementPolicyConfigId = item.PurchaseAgreementPolicyConfigId
              array.push(rItem)
            })
          }
        })

        this.dataSource = array
        this.modalData.dataSource = this.dataSource
      }

    },
    changeSupplier(key, label, e) {
      this.model.ChanneList = e
    },
    onTypeChange(e, type) {
      if (type == 'hsfs') {
        if (this.model.PolicyList.length > 0) {
          let AccountingMethodStr = ''
          switch (this.AccountingMethod) {
            case 1:
              AccountingMethodStr = '购进'
              this.columns = this.columns1
              break
            case 2:
              AccountingMethodStr = '销售'
              this.columns = this.columns1
              break
            case 3:
              AccountingMethodStr = '打款金额'
              // this.columns = this.columns2
              this.columns = this.columns1
              break
            default:
              AccountingMethodStr = '未知'
              this.columns = this.columns1
          }
          this.model.AccountingMethod = this.AccountingMethod
          this.$message.warning('当前核算方式已选择' + AccountingMethodStr + ',请删除促销政策后再切换核算方式')
          return
        } else {
          this.AccountingMethod = e.target.value
          if (this.AccountingMethod == 3) {
            // this.columns = this.columns2
            this.columns = this.columns1
          } else {
            this.columns = this.columns1
          }
        }
      }
    },
    // 上传回调
    multiUploadChange(images) {
      this.listImageUrl = images
    },
    onTimeChange(dates, dateStrings) {
      if (dateStrings.length == 2) {
        this.model.AgreementStartTime = dateStrings[0]
        this.model.AgreementEndTime = dateStrings[1]
      } else {
        this.model.AgreementStartTime = null
        this.model.AgreementEndTime = null
        this.rangeDate = []
      }
      if (this.model.RebateTime) {
        this.$refs.form.validateField(['RebateTime'])
      }
    },
    onTimeChange2(dates, dateStrings) {
      this.model.RebateTime = dateStrings
    },
    checkPolicyNum(type) {
      let num = 50
      if (type == 'next') {
        if (this.model.PolicyList.length >= num) {
          this.$message.warning('促销政策最多添加50条,请调整后保存')
          return false
        } else {
          return true
        }
      } else {
        if (this.model.PolicyList.length > num) {
          this.$message.warning('促销政策最多添加50条,请调整后保存')
          return false
        } else {
          return true
        }
      }
    },
    // 确定
    handleOk(type) {
      if (!this.isCopy && this.model.AuditStatus == 1) {
        this.$message.warning('当前采购协议审核中,暂时无法提交保存')
        return
      }
      if (!this.checkPolicyNum()) {
        return
      }
      const that = this
      // 触发表单验证
      // type 1 提交审核 2 存为草稿
      this.$refs.form.validate((err, values) => {
        if (err) {
          // 重组照片数据
          let listImageUrlData = []
          if (this.listImageUrl.length > 0) {
            listImageUrlData = this.listImageUrl.map((item) => {
              return {
                FileType: 1,
                FileUrl: item,
              }
            })
          }

          // 重组指定采购渠道
          let ChanneArray = []
          if (this.model.ChanneList.length > 0) {
            ChanneArray = this.model.ChanneList.map((item, index) => {
              return {
                ChannelId: item.key,
                ChannelName: item.label,
                IndexNum: index,
              }
            })
          }

          if (this.AccountingMethod == 3) {
            this.model.PolicyList.map(item => {
              if (item.IsLimitGoods == false) {
                item.GoodsList = []
              }
            })
          }

          // 重置政策和商品数据
          if (this.model.PolicyList.length > 0) {
            this.model.PolicyList.map((item) => {
              // 是否限制品种
              if (!item.IsLimitGoods && item.IsLimitGoods != false) {
                item.IsLimitGoods = true
              }
              // 打款金额设置 - 是否区分打款类型
              if (!item.IsCasePaymentType && item.IsCasePaymentType != false) {
                item.IsCasePaymentType = false
              }
              if (item.GoodsList.length == 1 && JSON.stringify(item.GoodsList[0]) == '{}') {
                item.GoodsList = []
              }
              // 不限制品种的时候强制设置goodsList为空数组
              if (item.IsLimitGoods === false) {
                item.goodsList = []
              }
              if (item.GoodsList.length > 0) {
                item.GoodsList.map((rItem) => {
                  if (rItem.IsRebate == null || rItem.IsRebate == undefined) {
                    rItem.IsRebate = true
                  }
                  if (!rItem.AccountingPrice) {
                    rItem.AccountingPrice = 0
                  }
                })
              }
            })
          }

          // AccountingConditions
          // 以我方采购金额为条件 = 1
          // 以供应商核算金额为条件 = 2
          // 以我方打款金额为条件 = 6
          // 以我方采购数量为条件 = 3
          // 以我方销售数量为条件 = 4
          // 以我方销售客户数为条件 = 5

          // PolicyRebateMethod
          // 返固定金额 = 1
          // 按每盒商品返金额 = 2
          // 按每家客户返金额 = 3

          // 返指定数量的本品或赠品 = 4

          // 按采购金额或进价的百分比 = 5
          // 按核算金额金额或核算价的百分比 = 6
          // 按打款金额的百分比 = 7

          // 指定数量的商品且每盒返单价的百分比 = 9

          let formData = {
            PurchaseAgreementType: this.type ? parseInt(this.type) : null, //1年度 2短期
            PurchaseAgreementId: this.id ? this.id : null,
            IsDraft: type == 1 ? false : true,
            PartyFirstId: this.model.PartyFirstId,
            PartyFirstName: this.model.PartyFirstName,
            PartyFirstType: this.model.PartyFirstType,
            ResponsiblePersonId: this.model.ResponsiblePersonId,
            FlowDataSource: (this.model.FlowDataSource || this.model.FlowDataSource == '0') ? Number(this.model.FlowDataSource) : null,
            FlowDirectionCards: this.model.FlowDataList || [],
            ResponsiblePersonName: this.model.ResponsiblePersonName,
            AgreementStartTime: this.model.AgreementStartTime,
            AgreementEndTime: this.model.AgreementEndTime,
            RebateTime: this.model.RebateTime,
            AccountingMethod: this.model.AccountingMethod,
            FileList: listImageUrlData,
            ChanneList: ChanneArray,
            PolicyList: this.model.PolicyList,
          }
          if (this.isEdit && !this.isCopy) {
            formData.PurchaseAgreementId = this.model.Id
          }
          console.log(formData)
          that.confirmLoading = true
          postAction(this.url.add, formData, this.httpHead)
            .then((res) => {
              if (res.IsSuccess) {
                let text = type == 1 ? '提交审核成功' : '保存草稿成功'
                if (type == 2) {
                  that.$message.success(text)
                }
                if (res.Data.PurchaseAgreementId) {
                  this.id = res.Data.PurchaseAgreementId
                }
                that.$emit('ok')
                // 提交审核
                if (type == 1) {
                  let params = {
                    BussinessNo: res.Data.AuditId,
                    MainBussinessNo: null,
                    Scenes: this.type == 1 ? 21 : 27,
                    OpratorId: Vue.ls.get(USER_ID),
                    Remark: '采购协议审核',
                  }
                  this.createApproval(params)
                } else {
                  // 保存草稿
                  setTimeout(() => {
                    this.backPage()
                  }, 500)
                }
              } else {
                that.confirmLoading = false
                that.$message.warning(res.Msg)
              }
            }).catch((err) => {
              that.confirmLoading = false
            }).finally(() => {
              if (type == 2) {
                that.confirmLoading = false
              }
            })
        }
      })
    },
    // 创建审核实例
    createApproval(params, callback) {
      this.confirmLoading = true
      postAction(this.url.createAudit, params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            this.$message.success('提交审核成功')
            callback && callback(true)
            this.backPage()
          } else {
            this.$message.error(res.Msg)
            callback && callback(false)
          }
        }).finally(() => {
          this.confirmLoading = false
        })
    },
    backPage() {
      if (this.type == 1) {
        this.$root.$emit('removeTabsAndBack', this.$route.fullPath, '/pages/contractManagement/purchaseDealList')
      } else if (this.type == 2) {
        this.$root.$emit(
          'removeTabsAndBack',
          this.$route.fullPath,
          '/pages/contractManagement/purchaseDealSaleShortList'
        )
      }
    },
    changeSearchInput(val, name) {
      this.model.PartyFirstId = val
      this.model.PartyFirstName = name
    },
    changeSearchPersonInput(val, name) {
      this.model.ResponsiblePersonId = val
      this.model.ResponsiblePersonName = name
    },
    subDel(record, type, index, clickPolicyObj) {
      if (type == 'del') {
        // 移除政策数据
        let policyIndex = record.index
        this.model.PolicyList.splice(policyIndex, 1)
        // 移除table数据
        let delArray = []
        this.dataSource.map((item, rIndex) => {
          if (record.index == item.index) {
            delArray.push(rIndex)
          }
        })
        let delStartIndex = delArray[0]
        let delStartLength = delArray.length
        this.dataSource.splice(delStartIndex, delStartLength)
      }
    },
    operate(record, type, index) {
      // 选择品种
      if (type == 'chooseVariety') {
        if (!this.model.AccountingMethod) {
          this.$message.warning('请先选择核算方式')
          return
        }
        if (!this.model.PartyFirstId) {
          this.$message.warning('请先选择甲方')
          return
        }
        if (!this.checkPolicyNum('next')) {
          return
        }
        this.model.dataSource = this.dataSource
        this.$refs.PurchaseDealPromotionAddModal.add(this.model)
      } else if (type == 'copy') {
        if (!this.checkPolicyNum('next')) {
          return
        }
        this.$message.success('复制成功,请编辑后保存')
        // 获取点击的政策
        let clickPolicyObj = this.setClickPolicyObj(record, 'setGoodsArray')
        this.model.rItemObj = clickPolicyObj
        console.log('点击的政策', this.model.rItemObj)
        this.$refs.PurchaseDealPromotionAddModal.edit(this.model, record, index, type)
      } else if (type == 'edit') {
        console.log('编辑的record', record)
        // 获取点击的政策
        let clickPolicyObj = this.setClickPolicyObj(record, 'setGoodsArray')
        this.model.rItemObj = clickPolicyObj
        this.$refs.PurchaseDealPromotionAddModal.edit(this.model, record, index)
      } else if (type == 'del') {
        let clickPolicyObj = this.setClickPolicyObj(record)
        clickPolicyObj.index = index
        this.model.rItemObj = clickPolicyObj
        let that = this
        this.$confirm({
          title: '您确定要移除该促销政策吗?',
          content: '',
          onOk() {
            that.subDel(record, type, index, clickPolicyObj)
          },
          onCancel() { },
        })
      }
    },
    setClickPolicyObj(record, setGoodsArray) {
      console.log('PolicyList是', this.model.PolicyList, record.PurchaseAgreementPolicyId)
      // 重组政策的表格
      let goodsArray = []
      if (this.model.PolicyList && this.model.PolicyList.length > 0) {
        this.model.PolicyList.map((item, index) => {
          item.GoodsList.map((rItem) => {
            if (rItem.length === 0 || JSON.stringify(rItem) === '{}') {
              rItem = item
              rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
              rItem.index = index
              goodsArray.push(rItem)
            } else {
              rItem.PurchaseAgreementPolicyId = item.PurchaseAgreementPolicyId
              rItem.index = index
              goodsArray.push(rItem)
            }

          })
        })
      }
      if (setGoodsArray) {
        this.model.dataSource = goodsArray
      }

      let PolicyIndex = null
      let clickPolicyObj = {}
      if (this.model.PolicyList && this.model.PolicyList.length > 0) {
        for (let i = 0; i < this.model.PolicyList.length; ++i) {
          if (record.PurchaseAgreementPolicyId == this.model.PolicyList[i].PurchaseAgreementPolicyId) {
            PolicyIndex = record.index
            break
          }
        }
        clickPolicyObj = this.model.PolicyList[PolicyIndex]
      }
      return clickPolicyObj
    },
    saveDraft() { },
    handleChooseFlowDataSource(val, txt, item) {
      if (val == 2) {
        this.$set(this.model, 'FlowDataList', []);
      }
      this.model.FlowDataSource = val
    }
  },
}
</script>
<style lang="scss" scoped>
.party-search {
  flex: 1;
  margin-right: 5px;
}

::v-deep .party-search .ant-input:disabled {
  color: rgba(0, 0, 0, 0.65);
  background-color: #ffffff;
}

.flowCardCont {
  display: flex;
  line-height: initial;
  align-items: center;
  margin-top: 4px;
  .cardCont {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    flex: 1;
    margin-right: 5px;
    padding: 4px;
    min-height: 32px;
    >span {
      margin-bottom: 2px;
    }
  }
}
</style>
