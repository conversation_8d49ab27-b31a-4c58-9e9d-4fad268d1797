<!--
 * @Description: 增加返利记录
 * @Version: 1.0
 * @Author: haolin.hu
 * @Date: 2025-02-11 14:17:02
 * @LastEditors: wenjingGao
 * @LastEditTime: 2025-03-31 11:29:27
-->
<template>
  <div>
    <a-modal
      title="增加返利记录"
      :width="800"
      :visible="visible"
      :confirmLoading="confirmLoading"
      @cancel="handleCancel"
      @close="handleCancel"
      :maskClosable="false"
      :destroyOnClose="true"
      :footer="null"
    >
    <a-spin :spinning="confirmLoading">
      <div style="text-align: right; margin-bottom: 10px">
        <a-button type="primary" @click="handleAdd">增加返利</a-button>
      </div>
      <a-table
        :bordered="true"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="false"
        :loading="loading"
        :scroll="{ y: '350px' }"
        rowKey="Id"
      >
        <!-- 字符串超长截取省略号显示-->
        <template slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else />
        </template>
      </a-table>
    </a-spin>
  </a-modal>

  <!-- 增加返利弹框 -->
  <a-modal
    title="增加返利"
    :width="500"
    :visible="addModalVisible"
    :confirmLoading="addModalLoading"
    @ok="handleAddOk"
    @cancel="handleAddCancel"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <a-form-model ref="addForm" :model="addModel" :rules="rules">
      <a-form-model-item label="增加返利金额/数量" prop="rebateAmount">
        <a-input-number v-model="addModel.rebateAmount" style="width: 100%" :precision="2" placeholder="请输入返利金额或数量" />
      </a-form-model-item>
      <a-form-model-item label="增加原因" prop="reason">
        <a-textarea v-model="addModel.reason" :rows="4" placeholder="请输入增加原因" />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
  </div>
</template>

<script>
import { getAction, putAction, postAction } from '@/api/manage'
// import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')

export default {
  name: 'PurchaseDealAddRebateModal',
  // mixins: [ListMixin],
  components: {
    JEllipsis
  },
  data() {
    return {
      visible: false,
      confirmLoading: false,
      loading: false,
      model: {},
      // 增加返利弹框
      addModalVisible: false,
      addModalLoading: false,
      addModel: {
        rebateAmount: undefined,
        reason: ''
      },
      rules: {
        rebateAmount: [{ required: true, message: '请输入返利金额或数量' }],
        reason: [{ required: true, message: '请输入增加原因' }]
      },
      columns: [
        {
          title: '操作时间',
          dataIndex: 'CreateTime',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '增加返利金额/数量',
          dataIndex: 'Qty',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '操作人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '增加原因',
          dataIndex: 'Desc',
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        }
      ],
      dataSource: [],
    }
  },
  methods: {
    show(record) {
      console.log('!1')
      this.model = Object.assign({}, record)
      this.visible = true
      this.loadData()
    },
    loadData() {
      this.loading = true
      getAction('/{v}/PurchaseAgreement/GetAddRebateListAsync', { purchaseAgreementPolicyId: this.model.PurchaseAgreementPolicyId }, 'P36007')
        .then(res => {
          if (res.IsSuccess) {
            console.log('获取返利记录列表', res)
            this.dataSource = res.Data
          } else {
            this.$message.error(res.Msg || '获取原件图片失败')
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleAdd() {
      this.addModalVisible = true
      this.addModel = {
        rebateAmount: undefined,
        reason: ''
      }
    },
    handleAddCancel() {
      this.addModalVisible = false
      this.$refs.addForm.resetFields()
    },
    handleAddOk() {
      this.$refs.addForm.validate(valid => {
        if (valid) {
          this.addModalLoading = true
          const params = {
            Qty: this.addModel.rebateAmount,
            Desc: this.addModel.reason,
            PurchaseAgreementPolicyId: this.model.PurchaseAgreementPolicyId // 假设需要传递采购交易ID
          }
          postAction('/{v}/PurchaseAgreement/AddRebateAsync', params, 'P36007')
            .then(res => {
              if (res.IsSuccess) {
                this.$message.success('增加返利成功')
                this.addModalVisible = false
                this.$refs.addForm.resetFields()
                this.loadData() // 刷新列表数据
              } else {
                this.$message.error(res.Msg || '操作失败')
              }
            })
            .catch(err => {
              this.$message.error('操作失败：' + (err.message || err))
            })
            .finally(() => {
              this.addModalLoading = false
            })
        }
      })
    },
    handleCancel() {
      this.visible = false
      if (this.$refs.addForm) {
        this.$refs.addForm.resetFields()
      }
    }
  }
}
</script>