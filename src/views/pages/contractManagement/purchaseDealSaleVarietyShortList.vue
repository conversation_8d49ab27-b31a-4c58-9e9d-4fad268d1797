<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea
      ref="SimpleSearchArea"
      :searchInput="searchInput"
      @searchQuery="searchQuery"
      @watchQueryParam="watchQueryParam"
    />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="onSetValid"
      @operate="operate"
      @changeTab="changeTab"
    >
      <!-- 促销时间 -->
      <span slot="ExpirationTimeVal" slot-scope="{ text, record }">
        <j-ellipsis
          :value="`${text ? text.substring(0, 11) : ''}~${
            record['AgreementEndTime'] ? record['AgreementEndTime'].substring(0, 11) : ''
          }`"
          :length="40"
        />
      </span>
      <!-- 返利时间 -->
      <span slot="RebateTime" slot-scope="{ text, record }">
        <span
          :style="{
            color:
              todayTime > new Date(text.substring(0, 10)).getTime() / 1000 &&
              (record.RemainingAmount > 0 || record.RemainingCount > 0)
                ? 'red'
                : '',
          }"
          >{{ text.substring(0, 11) }}</span
        >
      </span>
    </SimpleTable>
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'purchaseDealSaleVarietyShortList',
  title: '短期销售协议品种查询',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '协议编号', type: 'input', value: '', key: 'AgreementNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '甲方',
          type: 'input',
          value: '',
          key: 'PartyFirstKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码',
        },
        {
          name: '促销时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['AgreementStartTime', 'AgreementEndTime'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '返利时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['RebateStartTime', 'RebateEndTime'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '商品',
          type: 'input',
          value: '',
          key: 'GoodsNameKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码',
        },
        {
          name: '返利形式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AgreementRebateType',
          dictCode: 'EnumAgreementRebateType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '认款状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'RecognitionStatus',
          dictCode: 'EnumRecognitionStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '是否超期',
          type: 'select',
          value: '',
          key: 'IsOverdueBoole',
          defaultVal: [
            { title: '是', value: 1 },
            { title: '否', value: 2 },
          ],
          placeholder: '请选择',
        },
        {
          name: '负责人',
          type: 'input',
          value: '',
          key: 'ResponsiblePersonName',
          defaultVal: '',
          placeholder: '请输入',
        },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '导出', type: '', icon: 'download', key: '导出', id: '3f39815c-e27b-4c1d-8f40-75aa3659f46c' },
        ],
        hintArray: [],
        sortArray: ['ExpirationTimeVal', 'RebateTime'],
        tableTile: '超期未全部认款的行将红字显示返利时间',
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销时间',
          dataIndex: 'AgreementStartTime',
          width: 180,
          ellipsis: true,
          sorter: true,
          scopedSlots: { customRender: 'ExpirationTimeVal' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '促销内容',
          dataIndex: 'PolicyContent',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利形式',
          dataIndex: 'RebateTypeStr',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已完成销售数量',
          dataIndex: 'TotalSalesThresholdCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已完成销售客户数',
          dataIndex: 'TotalCustomerThresholdCount',
          width: 130,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收返利金额',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '额外收入金额',
          dataIndex: 'AdditionalAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收返货数量',
          dataIndex: 'TotalRebateCount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已认款数量',
          dataIndex: 'TotalRecognitionCount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '未认款数量',
          dataIndex: 'RemainingCount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'ResponsiblePersonName',
          width: 100,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 100,
          ellipsis: true,
          fixed: 'right',
          scopedSlots: { customRender: 'RebateTime' },
        },
      ],
      queryParam: {},
      todayTime: null,
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36007',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/PurchaseAgreement/GetPolicyGoodsList',
        exportUrl: '/{v}/PurchaseAgreement/ExportPolicyGoodsList',
      },
    }
  },
  created() {},
  // mounted() {
  //   let todayTimeVal = this.getTadayTime()
  //   this.todayTime = new Date(todayTimeVal).getTime() / 1000
  //   // console.log(this.todayTime, new Date('2023-08-31 23:59:59').getTime() / 1000)
  //   this.queryParam.PurchaseAgreementType = 2
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  activated() {
    let todayTimeVal = this.getTadayTime()
    this.todayTime = new Date(todayTimeVal).getTime() / 1000
    // console.log(this.todayTime, new Date('2023-08-31 23:59:59').getTime() / 1000)
    this.queryParam = this.$refs.SimpleSearchArea.queryParam
    this.queryParam.PurchaseAgreementType = 2
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    watchQueryParam(param) {
      let index = this.$refs.SimpleSearchArea.searchInput.findIndex((v) => v.name == '是否超期')
      if (param.RecognitionStatus == 3) {
        delete this.$refs.SimpleSearchArea.queryParam.IsOverdueBoole
        delete this.$refs.SimpleSearchArea.queryParam.IsOverdue
        this.$refs.SimpleSearchArea.searchInput[index].disabled = true
      } else {
        this.$refs.SimpleSearchArea.searchInput[index].disabled = false
      }
    },
    // 列表操作
    operate(record, type) {
      if (type == '导出') {
        this.handleExportXls(
          '短期销售协议品种查询',
          'Get',
          this.linkUrl.exportUrl,
          null,
          this.linkHttpHead,
          this.$refs.SimpleSearchArea.queryParam
        )
      }
    },
    // 获取当前时间
    getTadayTime() {
      var date = new Date()
      var seperator1 = '-'
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var strDate = date.getDate()
      if (month >= 1 && month <= 9) {
        month = '0' + month
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = '0' + strDate
      }
      var currentdate = year + seperator1 + month + seperator1 + strDate
      return currentdate
    },
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
      }
      queryParam.PurchaseAgreementType = 2
      if (queryParam.IsOverdueBoole == 1) {
        queryParam.IsOverdue = true
      } else if (queryParam.IsOverdueBoole == 2) {
        queryParam.IsOverdue = false
      }
      // delete queryParam.IsOverdueBoole
      this.$refs.table.loadDatas(1, queryParam)
    },
  },
}
</script>

<style></style>
