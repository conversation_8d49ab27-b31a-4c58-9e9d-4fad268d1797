<!--
 * @Description: 通用协议审核 列表
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: haolin.hu
 * @LastEditTime: 2025-05-14 09:20:21
-->

<template>
  <div>
    <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <TableNewView ref="tableView" :table="table" :columns="columns" :tabDataList="tabDataList" :dataList="dataSource"
      @operate="operate">
      <template slot="IsLimitGoods" slot-scope="{ text }">
        <span>{{ text ? '是' : '否' }}</span>
      </template>
    </TableNewView>
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { PublicMixin } from '@/mixins/PublicMixin'//非必要 公用的 有需要时候用其他mixin也行
import { getAction, postAction, putAction } from '@/api/manage'
export default {
  title: '通用协议审核',
  name: 'purchaseGeneralAuthList',
  mixins: [ListMixin, PublicMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '协议编号', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'AgreementNo', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '甲方', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'PartyFirstKey', //搜索key 必填
          placeholder: '名称/编号/拼音码',
        },
        {
          name: '甲方类型',
          type: this.$SEnum.SELECT,
          key: 'PartyFirstType',
          items: [{ label: '供应商', value: 1 }, { label: '生产厂家', value: 2 }],
        },
        {
          name: '责任人', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'ResponsiblePersonName', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '创建时间',
          type: this.$SEnum.DATE,
          // rangeDate: [],
          key: 'CreateTimeBegin',
          keyEnd: 'CreateTimeEnd',
          showTime: true,
          placeholder: ['开始日期', '结束日期'],
        },
        // {
        //   name: '审核状态',
        //   type: this.$SEnum.ENUM,
        //   key: 'AuditStatus',
        //   dictCode: 'EnumAuditStatus',
        // },
      ],
      queryParam: {

      },
      table: {
        curStatus: 1,
        tabStatusKey: 'ApproavalStatus',
        operateBtns: [

        ], //右上角按钮集合
        rowKey: 'Id',
        customSlot: ['IsLimitGoods'],
      },
      tabDataList: [
        {
          name: '待审核',
          value: 1,
          count: '',
          key: 'WaitAuditCount',
        },
        {
          name: '审核记录',
          value: 2,
          count: '',
          key: 'AuditRecordCount',
        },
      ],
      columns: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方类型',
          dataIndex: 'PartyFirstTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否按品种',
          dataIndex: 'IsLimitGoods',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'IsLimitGoods' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'CreateByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '审核',
              isShow: (record) => {
                return this.queryParam.ApproavalStatus == 1
              },
            },
            {
              name: '详情',
              isShow: (record) => {
                return this.queryParam.ApproavalStatus == 2
              },
            },
          ],
        },

      ],
      isInitData: true,
      httpHead: 'P36007',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/GeneralAgreement/GetAuditListAsync',//列表数据接口
        tabCount: '/{v}/GeneralAgreement/GetAuditCountAsync',//审核状态数量接口
      },
    }
  },
  computed: {},
  created() {

  },
  methods: {
    loadBefore() {

    },
    loadAfter() { },
    // 操作
    operate(record, key, index) {
      console.log(record, key, index)
      // type 4 通用协议详情
      if (key == '审核') {
        this.goPage('/pages/contractManagement/purchaseAuth/purchaseAgreementAudInfo', { type: 4, id: record.Id })
      }else if (key == '详情') {
        this.goPage('/pages/contractManagement/purchaseAuth/purchaseAgreementAudInfo', { type: 4, id: record.Id, isInfo: true })
      }
    },
  },
}
</script>
<style scoped>

</style>
