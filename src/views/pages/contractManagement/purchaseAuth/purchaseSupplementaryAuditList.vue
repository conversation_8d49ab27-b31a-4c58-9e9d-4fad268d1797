<template>
  <div>
      <!-- 搜索 -->
    <SimpleSearchArea
    ref="SimpleSearchArea"
    :searchInput="searchInput"
    @searchQuery="searchQuery"
  />
  <!-- 列表 -->
  <SimpleTable
    ref="table"
    :linkHttpHead="linkHttpHead"
    :linkUrl="linkUrl"
    :linkUrlType="linkUrlType"
    showTab
    cardBordered
    :tab="tab"
    :queryParam="queryParam"
    :columns="columns"
    @onSetValid="onSetValid"
    @operate="operate"
    @getListAmount="getListAmount"
    @changeTab="changeTab"
  />
  </div>
</template>
  
  <script>
  import { SimpleMixin } from '@/mixins/SimpleMixin'
  import { getAction, putAction, postAction } from '@/api/manage'
  export default {
    name: 'purchaseSupplementaryAuditList',
    mixins: [SimpleMixin],
    data() {
      return {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          {
            name: '供应商',
            type: 'input',
            value: '',
            key: 'PartyFirstName',
            defaultVal: '',
            placeholder: '请输入',
          },
          { name: '订单编号', type: 'input', value: '', key: 'PurchaseOrderNo', defaultVal: '', placeholder: '请输入' },
          {
            name: '创建人',
            type: 'input',
            value: '',
            key: 'CreateByName',
            defaultVal: '',
            placeholder: '请输入',
          },
          {
            name: '创建时间',
            type: 'timeInput',
            value: '',
            rangeDate:[],
            key: ['CreateTimeBegin', 'CreateTimeEnd'],
            defaultVal: ['', ''],
            placeholder: ['开始日期', '结束日期'],
          },
          // {
          //   name: '审核状态',
          //   type: 'selectBasicLink',
          //   value: '',
          //   vModel: 'AuditStatus',
          //   dictCode: 'EnumAuditStatus',
          //   defaultVal: '',
          //   placeholder: '请选择',
          // },
        ],

        tab: {
          bordered: false, //是否显示表格的边框
          operateBtn: [],
          hintArray: [],
          status: '1',
          statusKey: 'ApproavalStatus', //标签的切换关键字
          statusList: [
            {
              name: '待审核',
              value: '1',
              count: 0,
            },
            {
              name: '审核记录',
              value: '2',
              count: 0,
            },
          ],
        },
        columns: [
          {
            title: '供应商名称',
            dataIndex: 'PartyFirstName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '订单编号',
            dataIndex: 'PurchaseOrderNo',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建时间',
            dataIndex: 'CreateTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建人',
            dataIndex: 'CreateByName',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 120,
            fixed: 'right',
            actionBtn: [
              {
                name: '审核',
                icon: '',
                id: 'f5830d4a-ab06-4c4c-8448-7b1784a93069',
                specialShowFuc: (e) => {
                  let record = e || {}
                  return this.queryParam.ApproavalStatus == 1
                },
              },
              {
                name: '详情',
                icon: '',
                id: '479b2d76-f6dd-48a9-9b36-a4857e3aa63d',
                specialShowFuc: (e) => {
                  let record = e || {}
                  return this.queryParam.ApproavalStatus == 2
                },
              },
            ],
            scopedSlots: { customRender: 'action' },
          },
        ],
        queryParam: {},
        linkHttpHead: 'P36007',
        linkUrlType: '', //请求方式
        linkUrl: {
          list: '/{v}/SupplementaryPurchaseShortAgreement/GetSupplementaryPurchaseShortAgreementAuditList',
          listAmount: '/{v}/SupplementaryPurchaseShortAgreement/GetSupplementaryPurchaseShortAgreementAuditCount',
          delOccupy: '/{v}/PurchaseAgreement/DeleteAllAuditOccupyAsync',
        },
      }
    },
    created() {},
    mounted() {
      this.queryParam.ApproavalStatus = 1
    },
    methods: {
      // 搜索和重置搜索
      searchQuery(queryParam, type) {
        if (!this.$refs.table) {
          return
        }
        queryParam.ApproavalStatus = queryParam.ApproavalStatus || 1
        if (type == 'searchReset') {
          this.tab.status = '1'
          queryParam.ApproavalStatus = 1
        }
        this.queryParam.ApproavalStatus = queryParam.ApproavalStatus
        this.$refs.table.loadDatas(1, queryParam)
      },
      loadBefore() {
        this.queryParam.ApproavalStatus = this.queryParam.ApproavalStatus
      },
      getListAmount(data) {
        this.tab.statusList[0].count = (data && data.WaitAuditCount) || 0
        this.tab.statusList[1].count = (data && data.AuditRecordCount) || 0
      },
      // 删除占用
      // delOccupyFun() {
      //   postAction(this.linkUrl.delOccupy, {}, this.linkHttpHead).then((res) => {
      //     if (res.IsSuccess && res.Data) {
      //       this.$message.success('操作成功')
      //       this.$refs.table.loadDatas(1, this.queryParam)
      //     }
      //   })
      // },
      // 单个占用
      // singleOccupyFun(AuditId, callBack) {
      //   let url = '/{v}/PurchaseAgreement/AuditOccupyAsync?id=' + AuditId
      //   postAction(url, {}, this.linkHttpHead).then((res) => {
      //     if (!res.IsSuccess) {
      //       this.$message.warning(res.Msg)
      //       return
      //     } else {
      //       this.$message.success('占用成功')
      //       callBack && callBack()
      //     }
      //   })
      // },
      // 列表操作
      operate(record, type) {
        if (type == '审核') {
          // this.singleOccupyFun(record.Id, () => {
            this.goPage('/pages/contractManagement/PurchaseSupplementaryRecordInfo', { id: record.Id,isAudit:1 })
          // })
        } else if (type == '详情') {
          this.goPage('/pages/contractManagement/PurchaseSupplementaryRecordInfo', {id: record.Id,isAudit:2})
        } 
        // else if (type == '释放占用') {
        //   let that = this
        //   this.$confirm({
        //     title: '确定要释放占用吗?',
        //     content: '',
        //     onOk() {
        //       that.delOccupyFun()
        //     },
        //     onCancel() {},
        //   })
        // }
      },
    },
  }
  </script>
  
  <style>
  </style>