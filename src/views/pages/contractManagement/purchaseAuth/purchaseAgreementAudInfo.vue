<!--
 * @Description: 通用协议审核 审核页
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-03-31 10:13:19
-->
 
<template>
  <div>
    <div style="margin: 0 0 12px 0">
      <page-header>
        <template #Title>{{ description }}</template>
        <template #title-action>
          <a-button @click="handleGoBack">返回</a-button>
        </template>
      </page-header>
    </div>
    <!-- 内容 -->
    <a-spin :spinning="spinning">
      <a-row :gutter="12">
        <a-col :md="24">
          <a-card :bodyStyle="{ padding: '0 16px 16px 16px' }">
            <!-- 审核组件 -->
            <PurchaseAgreementAudInfoImg :row-data="rowData" @on-trigger-return="queryReturnLg" @onSwitch="handleSwitch" :isEdit="isEdit" viewType="lg" v-if="isAuditComponents" ref="iAuditComponents" @load="getDetailInfo" />
          </a-card>
        </a-col>
      </a-row>
    </a-spin>
    <!-- 全屏 -->
    <a-modal v-model="dialogVisibleAudit" width="100%" class="dialogVisibleAudit" @cancel="dialogVisibleAuditClose" :footer="null" :centered="true" :closable="false" :keyboard="false" :maskClosable="false" v-if="hasViewType == 'lg'">
      <!-- 审核组件 -->
      <PurchaseAgreementAudInfoImg @on-trigger-return="queryReturnLg" @back="handleGoBack" @onSwitch="handleSwitch" :row-data="rowData" :isEdit="isEdit" viewType="sm" v-if="dialogVisibleAudit && isAuditComponents" ref="iAuditComponents" @load="getDetailInfo" />
    </a-modal>
  </div>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const PageHeader = () => import('@/components/page/PageHeader')
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'purchaseAgreementAudInfo',
  mixins: [ListMixin],
  components: {
    PageHeader,
  },
  data() {
    return {
      title: '',
      description: '协议审核',
      detailId: null,
      // 编辑，查看组件需要的默认row对象
      rowData: {},
      // 初始化父级dialog
      dialogVisibleAudit: false,
      spinning: false,
      isAuditComponents: true,
      hasViewType: '',
      defaultHasViewType: null,
      type: null,//1年度协议 2短期销售 4 通用协议
      isEdit: false,//是否可编辑
      isInfo: false,//是否是详情
      httpHead: 'P36007',
      url: {
        detail: '/{v}/PurchaseAgreement/GetAuditInfoAsync',
        generalAgreementDetail: '/{v}/GeneralAgreement/GetAuditInfoAsync',
        delSingleOccupy: '/{v}/PurchaseAgreement/DeleteAuditOccupyAsync',
        singleOccupy: '/{v}/PurchaseAgreement/AuditOccupyAsync',
      }
    }
  },
  mounted() { },
  computed: {},
  created() {
    if (this.$route.query) {
      this.detailId = this.$route.query.id
      this.isInfo = this.$route.query.isInfo
      this.type = this.$route.query.type
      if (this.type == 1) {
        this.title = '年度协议审核'
      } else if (this.type == 4) {
        this.title = '通用协议审核'
        this.url.detail = this.url.generalAgreementDetail
      } else {
        this.title = '短期销售协议审核'
      }

      let title = this.isInfo ? this.title + '详情' : this.title
      this.description = title
      this.$root.$emit('setPageTitle', null, title, this.$route.fullPath)
      if (this.isInfo) {
        this.isEdit = false
      } else {
        this.isEdit = true
      }
      this.getDetailInfo()
    }
  },
  methods: {
    /**
     * @description: 获取详情
     * @param {*}
     * @return {*}
     */
    getDetailInfo(hasViewType, operateType) {
      if (!this.detailId) {
        return
      }
      this.spinning = true
      getAction(this.url.detail, { id: this.detailId }, this.httpHead)
        .then(res => {
          this.spinning = false
          if (!res.IsSuccess) {
            this.$message.warning(res.Msg)
            return
          }
          if (res.Data) {
            this.rowData = res.Data
            console.log('rowData', this.rowData, hasViewType)
            if ((hasViewType && hasViewType == 'close') && this.rowData.AllCount == 0) {
              let that = this
              this.$warning({
                title: '温馨提示',
                content: '当前所有数据已经审核完毕,请返回列表',
                okText: '返回列表',
                onOk() {
                  that.handleGoBack()
                }
              });
            }
            let tabList = []
            if (this.type == 4) {
              if (res.Data && res.Data.FileUrls.length > 0) {
                res.Data.FileUrls = res.Data.FileUrls.map(v => { return { FileUrl: v } })
                tabList.push({
                  Title: '图片',
                  Sum: res.Data.FileUrls.length,
                  Files: res.Data.FileUrls,
                  FileType: 1,
                })
              }
            } else {
              if (res.Data && res.Data.FileList.length > 0) {
                tabList.push({
                  Title: '图片',
                  Sum: res.Data.FileList.length,
                  Files: res.Data.FileList,
                  FileType: 1,
                })
              }
            }
            this.rowData['tabList'] = tabList

            this.isAuditComponents = false
            this.$nextTick(() => {
              this.isAuditComponents = true
            })
            // 单个占用
            if (operateType == 'switch' && this.type != 4) {
              this.singleOccupyFun()
            }
          }
        })
        .catch(() => {
          this.spinning = false
        })
    },
    /**
     * @description: 全屏
     * @param {*} queryParams
     * @return {*}
     */
    queryReturnLg(queryParams) {
      // 设置页面展示组件(类型)
      this.hasViewType = queryParams.hasViewType
      if (this.hasViewType != 'close') this.defaultHasViewType = this.hasViewType

      if (this.hasViewType == 'close') {
        // this.$router.go(-1);
        this.handleSwitch(2, this.rowData.NextId, this.hasViewType)
        // 全屏模式
        this.$nextTick(() => {
          if (this.defaultHasViewType == 'lg') {
            this.queryReturnLg({ hasViewType: 'lg' })
          }
        })
        return
      }
      if (this.hasViewType == 'sm') {
        this.dialogVisibleAudit = false

        this.isAuditComponents = false
        this.$nextTick(() => {
          this.isAuditComponents = true
        })
        return
      }
      if (this.hasViewType == 'lg') {
        this.dialogVisibleAudit = true
        this.isAuditComponents = false
        this.$nextTick(() => {
          this.isAuditComponents = true
        })
        return
      }
    },
    dialogVisibleAuditClose() {
      this.dialogVisibleAudit = false
    },
    // 单个占用
    singleOccupyFun() {
      if (!this.rowData.AuditId) {
        return
      }
      if (this.isInfo) {
        return
      }
      let url = '/{v}/PurchaseAgreement/AuditOccupyAsync?id=' + this.rowData.AuditId
      postAction(url, {}, this.httpHead).then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        } else {
          this.$message.success("占用成功");
        }
      })
    },
    // 删除单个占用
    delSingleOccupyFun(operateType, type, id, hasViewType) {
      if (!this.rowData.AuditId) {
        return
      }
      if (this.isInfo) {
        return
      }
      let url = '/{v}/PurchaseAgreement/DeleteAuditOccupyAsync?id=' + this.rowData.AuditId
      postAction(url, {}, this.httpHead).then(res => {
        if (res.IsSuccess && res.Data) {
          if (!res.IsSuccess) {
            this.$message.warning(res.Msg)
            return
          } else {
            this.$message.success("删除占用成功");
            if (operateType == 'switch') {
              this.detailId = id
              // 获取详情
              this.getDetailInfo(hasViewType, operateType)
            }
          }
        }
      })
    },
    handleGoBack() {
      // 第一个 true 是否关闭页面
      // 第二个 true 是否刷新返回的页面
      this.goBack(true, this.type == 1 ? '/pages/contractManagement/purchaseAuth/purchaseDealAuthList' : (this.type == 2 ? '/pages/contractManagement/purchaseAuth/purchaseDealSaleShortAuthList' : ''))
    },
    /*
     * 数据切换
     * type = 1 上一条，type = 2 下一条
     */
    handleSwitch(type, id, hasViewType) {
      if (id) {
        if (this.type == 4) { // 通用协议不需要删除占用
          this.detailId = id
          // 获取详情
          this.getDetailInfo(hasViewType)
          return
        }
        // 先删除占用再获取下一个
        this.delSingleOccupyFun('switch', type, id, hasViewType)
        // this.detailId = id
        // 获取详情
        // this.getDetailInfo(hasViewType)
      } else {
        this.handleGoBack()
      }
    },
  },
  beforeRouteLeave(to, from, next) {
    if (this.type != 4) this.delSingleOccupyFun()
    next()
  },
}
</script>
<style>
.dialogVisibleAudit {
  padding-bottom: 0;
  height: 100vh;
  width: 100%;
}
.dialogVisibleAudit .ant-modal-wrap {
  overflow: hidden;
}
.dialogVisibleAudit .ant-modal-body {
  height: 100vh;
  max-height: 100vh;
  overflow: auto;
}
</style>
