<template>
    <div>
      <a-spin :spinning="confirmLoading">
        <div
          :style="{
            width: '100%',
            border: '1px solid #e9e9e9',
            padding: '10px 16px',
            marginBottom: '10px',
          }"
        >
          <div style="margin-bottom: 10px; color: #333333; font-weight: bold; font-size: 18px">{{ title }}</div>
          <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }" :bordered="false">
            <a-descriptions>
              <a-descriptions-item>
                <div class="flc hcenter">
                    <a-icon
                        :type="getAuditIcon(model.AuditStatus)"
                        theme="twoTone"
                        :two-tone-color="getAuditColor(model.AuditStatus)"
                        style="font-size: 32px"
                    />
                    <span :style="{ color: getAuditColor(model.AuditStatus) }">{{ model.AuditStatusStr || '--' }}</span>
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="订单编号">
                {{ model.PurchaseOrderNo || '--'}}
              </a-descriptions-item>
              <a-descriptions-item label="创建时间">
                {{ model.CreateTime || '--' }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </div>
        <div :style="{ width: '100%', border: '1px solid #e9e9e9', padding: '10px 16px', background: '#fff' }">
          <a-row :getter="24">
            <a-tabs default-active-key="1" @change="changeTabs">
              <!-- 基础信息 -->
              <a-tab-pane key="1" tab="基础信息">
                <a-row :getter="10">
                  <a-col :md="24">
                    <a-descriptions>
                      <a-descriptions-item label="供应商名称：">
                        {{ model.PartyFirstName || '--' }}
                      </a-descriptions-item>
                    </a-descriptions>
                    <a-form-model-item label="采购订单：">
                      <a-table
                        :bordered="true"
                        ref="table"
                        :rowKey="(record, index) => index"
                        :columns="columns"
                        :dataSource="dataSource"
                        :pagination="false"
                        :loading="loading"
                        @change="handleTableChange"
                        :scroll="{ y: dataSource.length === 0 ? false : '450px', x: '1300px' }"
                      >
                        <!-- 字符串超长截取省略号显示-->
                        <template slot="component" slot-scope="text">
                          <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                          <j-ellipsis :value="text" v-else />
                        </template>
                        <!-- 价格 -->
                        <template slot="price" slot-scope="text">
                          <j-ellipsis :value="'￥' + (text || 0)"  />
                        </template>
                      </a-table>
                    </a-form-model-item>
                    <a-descriptions>
                        <a-descriptions-item label="补录原因：">
                            {{ model.Remark || '--' }}
                        </a-descriptions-item>
                    </a-descriptions>
                  </a-col>
                </a-row>
              </a-tab-pane>
              <!-- 审核信息 -->
              <a-tab-pane key="2" tab="审核信息">
                <CusAuditInformation
                  ref="CusAuditInformation"
                  :approval="{}"
                  v-if="model.AuditId && showAuditInfo"
                  :AuditId="model.AuditId"
                />
              </a-tab-pane>
            </a-tabs>
          </a-row>
        </div>
      </a-spin>
      <a-affix :offset-bottom="10" class="affix pr16">
        <div style="background-color: white; padding-right: 20px; padding-bottom: 10px;padding-top: 50px;">
          <a-button type="danger" @click="handleNoPass" v-if="isAudit == 1&&IsShowBtn" :loading="confirmLoading">审核驳回</a-button>
          <a-button type="primary" @click="handlePass" style="margin:0 10px;" :loading="confirmLoading" v-if="isAudit == 1&&IsShowBtn">审核通过</a-button>
          <a-button @click="goBack(true,true)" :loading="confirmLoading">返回</a-button>
        </div>
      </a-affix>

      <!-- 审核弹框 -->
      <AuditRemarkModal
        ref="iAuditRemarkModal"
        @ok="handleAuditModalOk"
        v-if="isAudit == 1"
      />
    </div>
  </template>
  
  <script>
  import Vue from 'vue'
  import { USER_ID } from '@/store/mutation-types'

  import moment from 'moment'
  import { ListMixin } from '@/mixins/ListMixin'
  const JEllipsis = () => import('@/components/jeecg/JEllipsis')
  import { getAction, putAction, postAction } from '@/api/manage'
  
  export default {
    name: 'PurchaseSupplementaryRecordInfo',
    mixins: [ListMixin],
    components: {
      JEllipsis
    },
    data() {
      return {
        title: '补录详情',
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 4 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 20 },
        },
        id: '',
        isAudit:null,
        columns: [
          {
            title: '商品名称',
            dataIndex: 'ErpGoodsName',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '商品编号',
            dataIndex: 'ErpGoodsCode',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '规格',
            dataIndex: 'PackingSpecification',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '生产厂家',
            dataIndex: 'BrandManufacturer',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '产地',
            dataIndex: 'Producer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '件装量',
            dataIndex: 'PackageCount',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '单位',
            dataIndex: 'PackageUnit',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '采购数量',
            dataIndex: 'PurchaseQuantity',
            width: 120,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '含税进价',
            dataIndex: 'TaxIncludedPurchasePrice',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '含税金额',
            dataIndex: 'TaxIncludedPurchasePriceTotal',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          {
            title: '每盒返利金额',
            dataIndex: 'BoxRebateAmount',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'price' },
          },
          
          {
            title: '返利到账天数',
            dataIndex: 'RebateDays',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
        ],
        showAuditInfo: false,
        dataSource: [],
        confirmLoading: false,
        IsShowBtn: false,
        httpHead: 'P36007',
        url: {
          info: '/{v}/SupplementaryPurchaseShortAgreement/GetSupplementaryAgreementDetail',
          auditInfo: '/{v}/SupplementaryPurchaseShortAgreement/GetSupplementaryAgreementAuditDetail',
          getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults'
        },
      }
    },
    mounted() {},
    created() {
      this.id = this.$route.query.id
      this.isAudit = this.$route.query.isAudit; //isAudit===1 审核 isAudit===2 审核详情 !isAudit  补录详情
      this.$root.$emit('setPageTitle', null, this.title, this.$route.fullPath)
      this.showAuditInfo = true
      this.getInfo()
    },
    methods: {
      moment,
      getInfo() {
        let formData = {
          Id: this.id,
        }
        this.confirmLoading = true
        //isAudit===1 审核 isAudit===2 审核详情 !isAudit  补录详情
        getAction(!this.isAudit?this.url.info : this.url.auditInfo, formData, this.httpHead)
          .then((res) => {
            if (res.IsSuccess) {
              this.model = res.Data
              
              if(this.isAudit) {
                if(this.model.AuditStatus > 1){
                  this.isAudit = 2
                }
                this.model.AuditId = this.id
                this.getApprovalResults(this.model.AuditId)
              }
              this.dataSource = this.model.Details || []
              
            } else {
              this.$message.warning(res.Msg)
            }
          })
          .finally(() => {
            this.confirmLoading = false
          })
      },
      changeTabs(e) {
        if (e == 1) {
          this.getInfo()
          this.showAuditInfo = false
        } else {
          this.showAuditInfo = true
        }
      },
       // IsHasSuperior
      getApprovalResults(bussinessNo) {
        if (!bussinessNo || this.isAudit == 2) return
        getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
          .then(res => {
            if (res.IsSuccess) {
              if (res.Data) {
                this.IsHasSuperior = res.Data['IsHasSuperior'] //是否有上级 true 有上级 false 没有上级
                this.ApprovalWorkFlowInstanceId = res.Data['ApprovalWorkFlowInstanceId']
                let userId = Vue.ls.get(USER_ID)
                if ((res.Data['ApprovalUserIds'] || []).length && this.isAudit == 1) {
                  this.IsShowBtn = res.Data['ApprovalUserIds'].some(v => v == userId)
                }
              }
            } else {
              this.$message.error(res.Msg)
            }
          })
          .finally(() => {})
      },
      /**
       * @description: 通过审核
       * @param {*}
       * @return {*}
       */
      handlePass() {
        this.$refs.iAuditRemarkModal.show(1, {
          ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId
        })
      },
      /**
       * @description: 驳回审核
       * @param {*}
       * @return {*}
       */
      handleNoPass() {
        this.$refs.iAuditRemarkModal.show(2, {
          ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
          IsHasSuperior: this.IsHasSuperior
        })
      },
      // 审核弹框回调
      handleAuditModalOk(AuditModalOkformData) {
        this.confirmLoading = true
        if (AuditModalOkformData) {
          // console.log('AuditModalOkformData             ', AuditModalOkformData)
          this.$refs.iAuditRemarkModal.postAuditInstance(AuditModalOkformData, (bool,code) => {
            setTimeout(() => {
              if (bool) {
                this.getInfo()
              }else{
                if(code === 6067){
                  this.getInfo()
                }else{
                  this.confirmLoading = false
                }
              }
            },1000);
          })
        } else {
          this.getInfo()
        }
      }
    },
  }
  </script>
  