<!--
 * @Description: 通用协议 列表
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: haolin.hu
 * @LastEditTime: 2025-05-09 14:02:10
-->

<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" :sortArray="['rebateType']" @searchQuery="searchQuery">
      <!-- 返利模式 -->
      <a-input-search slot="rebateType" v-model="queryParam.RebateTitle" placeholder="请选择" enter-button="选择" @search="onSearch" />
    </SimpleSearchArea>
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @changeTab="changeTab">
      <!-- 是否按品种 -->
      <span slot="IsLimitGoods" slot-scope="{ text }">
        {{ text ? '是' : '否' }}
      </span>
    </SimpleTable>
    <!-- 作废 -->
    <PurchaseCancellationModal ref="PurchaseCancellationModal" @ok="modalOk" :aUrl="'/{v}/GeneralAgreement/CancelGeneralAgreementAsync'" :pageN="'purchaseGeneralList'">
    </PurchaseCancellationModal>
    <!-- 作废原因 -->
    <PurchaseCancellationReasonModal ref="PurchaseCancellationReasonModal" aUrl="/{v}/GeneralAgreement/CancelGeneralAgreementDetailsAsync" :customColumn="customColumn" :pageN="'purchaseGeneralList'"></PurchaseCancellationReasonModal>
    <!-- 认款情况 -->
    <PurchaseDealAcceptance ref="PurchaseDealAcceptance" :type="type"></PurchaseDealAcceptance>
    <!-- 通用协议认款情况 -->
    <PurchaseCurrenceAcceptance ref="PurchaseCurrenceAcceptance" :type="4"></PurchaseCurrenceAcceptance>
    <!--返利模式弹窗 -->
    <RebateTypeModal ref="RebateTypeModal" :params="rebateParams" @ok="RebateTypeOk"></RebateTypeModal>
    <!-- 取消返利 -->
    <PurchaseCancelRebateModal ref="PurchaseCancelRebateModal" source="ty" @close="modalOk" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
import { number } from 'echarts'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'purchaseGeneralList',
  title: '通用协议',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  data() {
    return {
      type: 3,//1年度协议 2短期销售协议 3通用协议
      searchInput: [],
      searchInput1: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '协议编号', type: 'input', value: '', key: 'AgreementNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '甲方',
          type: 'input',
          value: '',
          key: 'PartyFirstKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码',
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '返利时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['RebateStartTime', 'RebateEndTime'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '协议状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AgreementStatus',
          dictCode: 'EnumAgreementStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '认款状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'RecognitionStatus',
          dictCode: 'EnumRecognitionStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '责任人',
          type: 'input',
          value: '',
          key: 'ResponsiblePersonName',
          defaultVal: '',
          placeholder: '请输入',
        },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '商品',
          type: 'input',
          value: '',
          key: 'GoodsKey',
          defaultVal: '',
          placeholder: '名称/编号',
        },
        { 
          name: '甲方类型',
          type: 'select',
          value: '',
          key: 'PartyFirstType',
          defaultVal: [{ title: '供应商', value: 1 }, { title: '生产厂家', value: 2 }],
          placeholder: '请选择'
        },
      ],
      searchInput2: [
        { name: '协议编号', type: 'input', value: '', key: 'AgreementNo', defaultVal: '', placeholder: '请输入' },
        {
          name: '甲方',
          type: 'input',
          value: '',
          key: 'PartyFirstKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码',
        },
        {
          name: '促销时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['AgreementStartTime', 'AgreementEndTime'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '返利时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['RebateStartTime', 'RebateEndTime'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '商品',
          type: 'input',
          value: '',
          key: 'GoodsNameKey',
          defaultVal: '',
          placeholder: '名称/编号/拼音码',
        },
        {
          name: '返利形式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AgreementRebateType',
          dictCode: 'EnumAgreementRebateType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '返利模式',
          type: 'rebateType',
          value: '',
          key: 'PurchaseAgreementPolicyConfigId',
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '协议状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AgreementStatus',
          dictCode: 'EnumAgreementStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '认款状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'RecognitionStatus',
          dictCode: 'EnumRecognitionStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '责任人',
          type: 'input',
          value: '',
          key: 'ResponsiblePersonName',
          defaultVal: '',
          placeholder: '请输入',
        },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '导出', type: 'primary', icon: '', key: '导出', id: '8d4e4967-3d48-4784-b7c7-8abdcee7868b' },
          { name: '新增', type: 'primary', icon: 'plus', key: 'add' }
        ],
        hintArray: [],
        sortArray: ['IsLimitGoods'],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方类型',
          dataIndex: 'PartyFirstTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否按品种',
          dataIndex: 'IsLimitGoods',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'IsLimitGoods' },
        },
        {
          title: '返利时间',
          dataIndex: 'RebateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '协议状态',
          dataIndex: 'AgreementStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款状态',
          dataIndex: 'RecognitionStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'CreateByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 260,
          fixed: 'right',
          actionBtn: [
            //AuditStatus 0 未提交 1审核中 2审核通过 3审核失败
            //AgreementStatus 1 未生效 2生效中 3已结束 4已作废
            //RecognitionStatus 1 未认款 2部分认款 3已认款
            {
              name: '认款情况',
              icon: '',
              id: '4095d25a-8d5f-4652-acbf-b3ac6e7a7229',
              specialShowFuc: (e) => {
                // 审核通过 已结束 部分认款/已认款 显示认款情况按钮
                let record = e || {}
                if (
                  [2].includes(record.AuditStatus) && // 审核通过
                  [2, 3].includes(record.AgreementStatus) && // 2生效中 3已结束
                  [2, 3].includes(record.RecognitionStatus) // 2部分认款 3已认款
                ) {
                  return true
                } else return false
              },
            },
            {
              name: '撤回',
              icon: '',
              id: '3aa43239-2180-4aa5-a262-79468bd48727',
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [1].includes(record.AuditStatus) && // 待审核
                  [1].includes(record.AgreementStatus) && // 未生效
                  [1].includes(record.RecognitionStatus) // 未认款
                ) {
                  return true
                } else return false
              },
            },

            {
              name: '编辑',
              icon: '',
              id: '1db0f752-b1b4-46f7-aa66-785782251373',
              specialShowFuc: (e) => {
                // 未提交 | 审核驳回   显示编辑按钮
                let record = e || {}
                if (
                  [0, 3].includes(record.AuditStatus)
                ) {
                  return true
                }
              },
            },
            {
              name: '删除',
              icon: '',
              id: '761859c5-c35f-48de-80f7-fbdcc0e824ca',
              specialShowFuc: (e) => {
                // 未提交 | 审核驳回   显示编辑按钮
                let record = e || {}
                if (
                  [0, 3].includes(record.AuditStatus)
                ) {
                  return true
                }
              },
            },
            {
              name: '详情',
              icon: '',
              id: '1bcc27ad-a6c5-4aa5-a49a-990737c03aa5',
              specialShowFuc: (e) => {
                // 待审核 | 审核通过   显示详情按钮
                let record = e || {}
                return [1, 2].includes(record.AuditStatus) ? true : false
              },
            },
            {
              name: '作废',
              icon: '',
              id: '856f50d0-9330-4481-8db9-b59f80f1d1e9',
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [2].includes(record.AuditStatus) && // 审核通过
                  [2, 3].includes(record.AgreementStatus) // 2生效中 3已结束
                ) {
                  return true
                } else return false
              },
            },
            {
              name: '复制',
              icon: '',
              id: 'd39f4942-d235-4b58-a690-86b8f55e6852',
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [2].includes(record.AuditStatus) && // 审核通过
                  [4].includes(record.AgreementStatus) && // 已作废
                  [1].includes(record.RecognitionStatus) // 1 未认款
                ) {
                  return true
                } else return false
              },
            },
            {
              name: '作废原因',
              icon: '',
              id: '7bcf95ff-80e9-46f8-bdc7-3667e550e263',
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [2].includes(record.AuditStatus) && // 审核通过
                  [4].includes(record.AgreementStatus) // 已作废
                ) {
                  return true
                } else return false
              },
            },
            {
              name: '取消返利',
              icon: '',
              id: '400c7139-a0c0-4f74-ace4-e4545f145fe3',
              specialShowFuc: (e) => {
                let record = e || {}
                if (
                  [2].includes(record.AuditStatus) && // 审核通过
                  [1, 2].includes(record.RecognitionStatus) && // 1未认款 2部分认款
                  record.AgreementStatus != 4 && // 非 作废
                  record.RemainingAmount > 0
                ) {
                  return true
                }
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {
        RebateTitle: '',
        PurchaseAgreementPolicyConfigId: null,
      },
      rebateParams: null,
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36007',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/GeneralAgreement/GetGeneralAgreementListAsync',//列表
        del: '/{v}/GeneralAgreement/DeleteGeneralAgreementAsync',//删除
        revokeAuditAsync: '/{v}/GeneralAgreement/RevokeGeneralAgreementAudit',//撤回
        exportUrl: '/{v}/GeneralAgreement/ExportGeneralAgreementAsync',//导出
      },
      customColumn: [
        {
          title: '操作时间',
          dataIndex: 'CancelTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作人',
          dataIndex: 'CancelByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '作废原因',
          dataIndex: 'CancelReason',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ]
    }
  },
  created() {
    this.initInfo()
  },
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  activated() {
    this.queryParam = this.$refs.SimpleSearchArea.queryParam
    this.initInfo()
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    initInfo() {
      if (this.type == 3) {
        this.searchInput = this.searchInput1
        this.rebateParams = {}
      }
    },
    onSearch(value) {
      this.$refs.RebateTypeModal.show()
    },
    RebateTypeOk(data) {
      this.$set(this.queryParam, 'RebateTitle', data.Title ? data.Title.replace(/<[^>]+>|&[^>]+;/g, '').trim() : '')
      this.$refs.SimpleSearchArea.queryParam.PurchaseAgreementPolicyConfigId = data.Id ? data.Id : null
    },
    modalOk() {
      this.$refs.table.loadDatas(null, this.$refs.SimpleSearchArea.queryParam)
    },
    // 确定删除
    subDelPurchaseDemand(record) {
      let params = {
        Id: record.Id
      }
      let url = this.linkUrl.del
      getAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('删除成功')
            this.$refs.table.loadDatas(1, this.queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => { })
    },
    searchQuery(queryParam, type, extendParams) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.tab.status = ''
        this.queryParam.RebateTitle = ''
      }
      this.$refs.table.loadDatas(1, queryParam, null, type, extendParams)
    },
    // 列表操作
    operate(record, type) {
      if (type == 'add') {
        let path = 'purchaseGeneralAdd'
        this.goPage(path, { type: this.type })
      } else if (type == '编辑') {
        let path = 'purchaseGeneralAdd'
        this.goPage(path, { type: this.type, id: record.Id })
      } else if (type == '详情') {
        let path = this.type == 1 ? 'purchaseDealInfo' : 'generalProtocolDetail'
        this.goPage(path, { id: record.Id, type: this.type })
      } else if (type == '复制') {
        this.$message.success('复制成功,请点击保存')
        let path = 'purchaseGeneralAdd'
        this.goPage(path, { id: record.Id, type: this.type, isCopy: true })
      } else if (type == '执行情况') {
        this.$refs.PurchaseDealCarryOut.show(record)
      } else if (type == '认款情况') {
        this.$refs.PurchaseCurrenceAcceptance.show(record)
      } else if (type == '删除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认删除本协议？删除后不能恢复',
          onOk() {
            that.subDelPurchaseDemand(record)
          },
          onCancel() { },
        })
      } else if (type == '作废') {
        this.$refs.PurchaseCancellationModal.show(record)
      } else if (type == '作废原因') {
        this.$refs.PurchaseCancellationReasonModal.show(record)
      } else if (type == '撤回') {
        let that = this
        this.$confirm({
          title: '您确定要撤回该协议吗?',
          content: '',
          onOk() {
            that.subRecall(record)
          },
          onCancel() { },
        });
      } else if (type == '取消返利') {
        this.$refs.PurchaseCancelRebateModal.show(record)
      } else if (type == '导出') {
        this.handleExportXls(
          '通用协议',
          'Get',
          this.linkUrl.exportUrl,
          null,
          this.linkHttpHead,
          this.$refs.SimpleSearchArea.queryParam
        )
      }
    },
    // 确认撤回
    subRecall(record) {
      let params = {
        Id: record.Id,
      }
      let url = this.linkUrl.revokeAuditAsync
      getAction(url, params, this.linkHttpHead)
        .then((res) => {
          if (res.Data) {
            this.$message.success('操作成功')
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => {
          this.$refs.table.loadDatas(null, this.queryParam)
        })
    },
  },
}
</script>

<style></style>
