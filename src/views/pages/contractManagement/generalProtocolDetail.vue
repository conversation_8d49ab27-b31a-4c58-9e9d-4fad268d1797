<!-- 通用协议详情 -->
<template>
  <div>
    <a-spin :spinning="confirmLoading">
      <div :style="{
          width: '100%',
          border: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          marginBottom: '10px',
        }">
        <div style="margin-bottom: 10px; color: #333333; font-weight: bold; font-size: 18px">{{ title }}</div>
        <a-row :getter="24">
          <a-descriptions>
            <a-descriptions-item label="协议编号">
              {{ model.AgreementNo || '--' }}
            </a-descriptions-item>
            <a-descriptions-item label="审核状态">
              {{ model.AuditStatusStr || '--' }}
            </a-descriptions-item>
            <a-descriptions-item label="协议状态">
              {{ model.AgreementStatusStr || '--' }}
            </a-descriptions-item>
            <a-descriptions-item label="认款状态">
              {{ model.RecognitionStatusStr || '--' }}
            </a-descriptions-item>
            <a-descriptions-item label="责任人">
              {{ model.OwnerByName || '--' }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ model.CreateTime || '--' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-row>
      </div>
      <div :style="{ width: '100%', border: '1px solid #e9e9e9', padding: '10px 16px', background: '#fff' }">
        <a-row :getter="24">
          <a-tabs default-active-key="1" @change="changeTabs">
            <!-- 基础信息 -->
            <a-tab-pane key="1" tab="基础信息">
              <a-row :getter="10">
                <a-col :md="24">
                  <a-descriptions>
                    <a-descriptions-item label="甲方">
                      {{ model.PartyFirstName || '--' }}
                    </a-descriptions-item>
                    <a-descriptions-item label="甲方类型">
                      {{ model.PartyFirstTypeStr || '--' }}
                    </a-descriptions-item>
                    <a-descriptions-item label="是否按照品种配置">
                      {{ model.IsLimitGoods?'是':'否' }}
                    </a-descriptions-item>
                  </a-descriptions>
                  <a-form-model-item label="促销政策：" v-if="model.IsLimitGoods">
                    <a-table :bordered="true" ref="table" :rowKey="(record, index) => index" :columns="columns" :dataSource="dataSource" :pagination="false" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length === 0 ? false : '350px', x: '1300px' }">
                      <!-- 字符串超长截取省略号显示-->
                      <template slot="component" slot-scope="text">
                        <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                        <j-ellipsis :value="text" v-else />
                      </template>
                      <template slot="price" slot-scope="text">
                        <j-ellipsis :value="'￥' + (text || 0)"  />
                      </template>
                    </a-table>
                  </a-form-model-item>
                </a-col>
                <a-col :md="24"  v-if="model.IsLimitGoods">
                  <p style="color:red;">合计返利金额： {{ total < 0 ? '-' : '' }}¥{{ total < 0 ? String(total).replace('-', '') : total }}</p>
                </a-col>
                <a-col :md="24">
                  <a-col :md="8"  v-if="!model.IsLimitGoods">
                    <p style="color: rgba(0, 0, 0, 0.85)">
                      促销内容：{{ model.PolicyContent || ' -- ' }}
                    </p>
                  </a-col>
                  <a-col :md="8"  v-if="!model.IsLimitGoods">
                    <p style="color: rgba(0, 0, 0, 0.85)">
                      返利金额：￥{{ model.RebateAmount || 0 }}
                    </p>
                  </a-col>
                  <a-col :md="8">
                    <p style="color: rgba(0, 0, 0, 0.85)">
                      返利时间：{{ model.RebateTime ? model.RebateTime.substring(0, 11) : '--' }}
                    </p>
                  </a-col>
                </a-col>
                <a-col :md="24">
                  <a-form-model-item label="原件照片">
                    <!-- <ImageControl
                      style="margin: 0 5px 5px 0"
                      v-for="(item, index) in model.FileUrls"
                      :key="index"
                      :src="item.FileUrl"
                      :width="150"
                      :height="150"
                    /> -->
                    <img style="width: 150px; height: 150px; cursor: pointer; margin: 0 10px 10px 0" v-for="(item, index) in model.FileUrls" :key="index" :src="item.FileUrl" @click="showImgModal(item, index)" alt="" />
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-tab-pane>
            <!-- 审核信息 -->
            <a-tab-pane key="2" tab="审核信息">
              <CusAuditInformation ref="CusAuditInformation" :approval="{}" v-if="model.AuditId && showAuditInfo" :AuditId="model.AuditId" />
            </a-tab-pane>
          </a-tabs>
        </a-row>
      </div>
    </a-spin>
    <a-affix :offset-bottom="10" class="affix pr16">
      <div style="background-color: white; padding: 20px 20px 10px 0;">
        <a-button @click="goBack(true,true)">返回</a-button>
      </div>
    </a-affix>
    <!-- 查看照片 -->
    <PurchaseDealImgModal ref="PurchaseDealImgModal"></PurchaseDealImgModal>
  </div>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import ImageControl from '@/components/yyl/components/ImageControl'
import { getAction, putAction, postAction } from '@/api/manage'

export default {
  name: 'generalProtocolDetail',
  mixins: [ListMixin],
  components: {
    JEllipsis,
    ImageControl,
  },
  data() {
    return {
      title: '通用协议详情',
      model: {},
      total: 0,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      id: '',
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '返利金额',
          dataIndex: 'RebateAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },

      ],
      showAuditInfo: false,
      dataSource: [],
      confirmLoading: false,
      httpHead: 'P36007',
      url: {
        info: '/{v}/GeneralAgreement/GeneralAgreementDetailsAsync',
      },
    }
  },
  mounted() { },
  created() {
    this.id = this.$route.query.id
    this.$root.$emit('setPageTitle', null, this.title, this.$route.fullPath)
    this.showAuditInfo = true
    this.getInfo()
  },
  methods: {
    moment,
    getInfo() {
      let formData = {
        Id: this.id,
      }
      this.confirmLoading = true
      getAction(this.url.info, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.model = res.Data
            if((this.model.FileUrls || []).length){
              this.model.FileUrls = this.model.FileUrls.map(v=>{return{FileUrl:v}})
            }
            if(res.Data&&(res.Data['RebateGoods'] || []).length){
              this.$set(this.model,'PolicyList',(res.Data['RebateGoods'] || []))
              this.$set(this,'dataSource',(res.Data['RebateGoods'] || []))
              this.getTotalAmount()
            }
            
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    /**
     * 获取总金额
     */
     getTotalAmount() {
        let sum = 0

        if ((this.model.PolicyList||[]).length) {
          this.model.PolicyList.forEach((x) => {
            if (x['RebateAmount']) {
              sum += (x['RebateAmount'] || 0)
            }
          })
        }

        this.total = this.getAmountOfMoney(sum,2)
    },
    changeTabs(e) {
      if (e == 1) {
        this.getInfo()
        this.showAuditInfo = false
      } else {
        this.showAuditInfo = true
      }
    },
    showImgModal(item, index) {
      this.$refs.PurchaseDealImgModal.show(this.model.FileUrls, item, index)
    },
  },
}
</script>
