<!--
 * @Author: LP
 * @Description: 采购订单列表
 * @Date: 2023/07/05
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
     <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
    />

    <!-- 调价 -->
    <TiaojiaModal ref="tiaojiaoModel" @ok="modalFormOk" />
    <!-- 票折 -->
    <PiaozheModal ref="piaozheModal" @ok="modalFormOk" />
    <!-- 退货 -->
    <TuihuoModal ref="tuihuoModal" @ok="modalFormOk" />
    <!-- 买赔 -->
    <MaipeiModal ref="maipeiModal" @ok="modalFormOk" />
  </div>
</template>
<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
import { postAction,putAction } from '@/api/manage'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PurchaseOrderList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierName',
        },

        {
          name: '企业类别',
          type: SEnum.ENUM,
          key: 'SupplierType',
          dictCode: 'EnumSupplierType',
        },
        {
          name: '订单编号',
          type: SEnum.INPUT,
          key: 'PurchaseOrderNo',
        },
        {
          name: '付款方式',
          type: SEnum.ENUM,
          key: 'PaymentMode',
          dictCode: 'EnumPurchasePaymentMode',
        },
        {
          name: '送货方式',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/Global/GetListDictItem',
          key: 'DeliveryTypeId',
          params: { groupPY: 'songhfs' },
          dataKey: { name: 'ItemValue', value: 'Id' },
        },
        {
          name: '审核状态',
          type: SEnum.ENUM,
          key: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
        },
        {
          name: '订单状态',
          type: SEnum.ENUM,
          key: 'Status',
          dictCode: 'EnumPurchaseOrderStatus',
        },
        {
          name: '付款状态',
          type: SEnum.ENUM,
          key: 'PaymentStatus',
          dictCode: 'EnumPurchasePaymentStatus',
        },
        {
          name: '发票状态',
          type: SEnum.ENUM,
          key: 'InvoiceStatus',
          dictCode: 'EnumPurchaseInvoiceStatus',
        },
        {
          name: '同步状态',
          type: SEnum.ENUM,
          key: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
        {
          name: '商品名称',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'GoodsName',
        },
        {
          name: '发票号',
          type: SEnum.INPUT,
          key: 'InvoiceNo',
        },
        {
          name: '订单类型',
          type: SEnum.ENUM,
          key: 'OrderType',
          dictCode: 'EnumPurchaseOrderType',
        },
        {
          name: '责任人',
          type: SEnum.INPUT,
          key: 'OwnerByName',
        },
        {
          name: '入库单号',
          type: SEnum.INPUT,
          key: 'WarehouseInNo',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        curStatus: '',
        tabStatusKey: '', //标签的切换关键字
        scrollY:'42vh',
        tabTitles: ['采购订单列表'],
        operateBtns: [
          {
            name: '导出',
            type: 'primary',
            icon: 'plus',
            key: '导出',
            id: '1df2c9e3-2e55-46e1-89f2-a99e99d5ac84',
          },
          {
            name: '新增含特订单',
            type: 'primary',
            icon: 'plus',
            key: '新增含特订单',
            id: '3e841171-6819-4fb2-bef8-ef69f53785cf',
          },
          {
            name: '新增订单',
            type: 'primary',
            icon: 'plus',
            key: '新增订单',
            id: '427f11fd-1fba-44b7-a961-1657ce7a1e35',
          },
        ],
        rowKey: 'Id',
      },
      httpHead: 'P36009',
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          fixed: 'left',
        },
        {
          title: '下单时间',
          dataIndex: 'CreateTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          fixed: 'left',
        },
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '订单类型',
          dataIndex: 'OrderTypeStr',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '企业类别',
          dataIndex: 'SupplierTypeStr',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '送货方式',
          dataIndex: 'DeliveryMethodStr',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '订单金额',
          dataIndex: 'OrderAmount',
          width: 100,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '入库金额',
          dataIndex: 'InventoryTotalAmount',
          width: 100,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '已打款金额',
          dataIndex: 'PaidAmount',
          width: 100,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '预到货日期',
          dataIndex: 'ExpectedArrivalDate',
          width: 120,
          scopedSlots: { customRender: 'date' },
          ellipsis: true,
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '订单状态',
          dataIndex: 'OrderStatusStr',
          width: 100,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '付款状态',
          dataIndex: 'PaymentStatusStr',
          width: 100,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '发票状态',
          dataIndex: 'InvoiceStatusStr',
          width: 100,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 280,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: 'e2134f5f-5627-4011-afd4-640efec49035',
              isShow: record => {
                return ![0, 3].includes(record.AuditStatus)
              }
            },
            {
              name: '编辑',
              id: '76fd91c4-8d9b-4e20-98f5-76fd7872e70a',
              isShow: (record) => {
                return (
                  [0, 3].includes(record.AuditStatus) &&
                  record.OrderStatus == 1 &&
                  record.PaymentStatus == 1 &&
                  record.InvoiceStatus == 1
                )
              },
            },
            {
              name: '删除',
              id: '388c49b0-f13a-49d8-91a6-1423667e0cb0',
              isShow: (record) => {
                /**
                 * AuditStatus  未提交 = 0, 审核中 = 1, 审核通过 = 2, 审核失败 = 3
                 * OrderStatus  1-待下发 2-待收货 3-已入库 4-已完成
                 * PaymentStatus  1.未付款 2.部分付款 3.已付款
                 * InvoiceStatus  1.未收票 2已收票
                 * PaymentMode  1预付款 2月结
                 */
                return (
                  (([0, 3].includes(record.AuditStatus) && record.OrderStatus == 1) ||
                    (record.AuditStatus == 2 && record.OrderStatus == 2)) &&
                  record.PaymentStatus == 1 &&
                  record.InvoiceStatus == 1
                )
              },
            },
            {
              name: '调价',
              id: '211f7f4e-f6c8-419f-8f1a-abb612e8d236',
              isShow: (record) => this.checkShow(record),
            },
            {
              name: '票折',
              id: 'b3d61da6-0c2d-4672-a469-e71177ce0735',
              isShow: (record) => this.checkShow(record),
            },
            {
              name: '退货',
              id: '5af628e2-4b34-40a5-a90a-2f5a8225347e',
              isShow: (record) => this.checkShow(record),
            },
            {
              name: '买赔',
              id: '89d02756-d85f-4c93-b2b9-6e5f81747b96',
              isShow: (record) => this.checkShow(record, true),
            },
            {
              name: '已完成',
              id: 'ea39fa2c-e31b-4f2d-b760-0d0a43812d3f',
              isShow: (record) => {
                // /**
                //  * AuditStatus  未提交 = 0, 审核中 = 1, 审核通过 = 2, 审核失败 = 3
                //  * OrderStatus  1-待下发 2-待收货 3-已入库 4-已完成
                //  * PaymentStatus  1.未付款 2.部分付款 3.已付款
                //  * InvoiceStatus  1.未收票 2已收票
                //  * PaymentMode  1预付款 2月结
                //  */
                if (record.PaymentMode == 1) { //1预付款
                  let payList = [1, 2, 3]
                  //审核通过 && 已入库 && (月结-未付款 or 月付款-未付款/部分付款/已付款) //&& 未收票
                  return (
                    record.AuditStatus == 2 && record.OrderStatus == 3 && payList.includes(record.PaymentStatus)
                    // && record.InvoiceStatus == 1
                  )
                }
                if (record.PaymentMode == 2) { //2月结
                  // 1.4需求 当订单状态=已入库时，付款金额小于等于入库金额时，显示已完成按钮，可以将订单变为已完成状态
                  return record.AuditStatus == 2 && record.OrderStatus == 3 && (record.PaidAmount || 0) <= (record.InventoryTotalAmount || 0)
                }

                
              },
            },
            {
              name: '作废',
              id: 'bed02d39-df7b-4a5a-8814-cdf45d03a514',
              isShow: (record) => {
                /**
                 * 预付款 && 待收货 && 已付款/部分付款 显示作废
                 * OrderStatus  1-待下发 2-待收货 3-已入库 4-已完成
                 * PaymentStatus  1.未付款 2.部分付款 3.已付款
                 * PaymentMode  1预付款 2月结
                 */
                return record.PaymentMode == 1 && record.OrderStatus == 2 && [2, 3].includes(record.PaymentStatus)
              },
            },
            {
              name: '撤回',
              id: '92e8bee9-c36e-46f8-9ed4-ce198e3925b6',
              isShow: (record) => {
                // 审核中的采购订单增加撤回
                return [1].includes(record.AuditStatus)
              },
            },
          ],
        },
      ],
      url: {
        list: '/{v}/PurchaseOrder/GetPageList', //'/{v}/Supplier/GetSearchListSupplierRecord',
        tabCount: '',
        delete: '/{v}/PurchaseOrder/DeletePurchaseOrder',
        complete: '/{v}/PurchaseOrder/PurchaseOrderComplete',
        zuofei: '/{v}/PurchaseOrder/SetInvalidAsync', //作废
        cancel: '/{v}/PurchaseOrder/RevokePurchaseOrderAudit', //撤回
        exportUrl: '/{v}/PurchaseOrder/ExportPurchaseOrderDetails', //导出
      },
    }
  },
  methods: {
    onOperateClick(key) {
      if(key === '导出') {
        let { CreateTimeBegin, CreateTimeEnd} = this.getQueryParams()
        if(!CreateTimeBegin || !CreateTimeEnd) {
          this.$message.error('请选择创建时间！')
          return
        } else if(moment(CreateTimeBegin).add(3, 'months').isBefore(CreateTimeEnd)) { // 计算时间间隔是否超过3个月
          this.$message.error('创建时间的间隔时间不能超过3个月！')
        } else {
          this.handleExportXls(
            '采购订单明细',
            'Get',
            this.url.exportUrl,
            null,
            this.httpHead,
            this.getQueryParams()
          )
        }
        
      }else if (key === '新增含特订单') {
        this.onDetailClick('AddPurchaseOrder', { orderType: 2 })
      } else if (key === '新增订单') {
        this.onDetailClick('AddPurchaseOrder', { orderType: 1 })
      }
    },
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('PurchaseOrderDetail', { id: record.Id })
      } else if (key === '编辑') {
        this.onDetailClick('AddPurchaseOrder', { id: record.Id, isAdd: 1 })
      } else if (key === '删除') {
        this.handleDeleteClick(record.Id)
      } else if (key === '调价') {
        this.$refs.tiaojiaoModel.show(record.Id)
      } else if (key === '票折') {
        this.$refs.piaozheModal.show(record.Id)
      } else if (key === '退货') {
        this.$refs.tuihuoModal.show(record.Id)
      } else if (key === '买赔') {
        this.$refs.maipeiModal.show(record.Id)
      } else if (key === '已完成') {
        let that = this
        that.$confirm({
          title: '提示',
          content: '是否确定已完成?',
          onOk: function () {
            that.loading = true
            postAction(that.url.complete, { id: record.Id }, that.httpHead).then((res) => {
              if (res.IsSuccess) {
                that.$message.success('操作成功')
                that.loadData()
              } else {
                that.$message.error(res.Msg)
              }
            }).catch((err) =>{
              that.loading = true
            }).finally(() =>{
              that.loading = false
            })
          },
        })
      } else if (key === '作废') {
        let that = this
        that.$confirm({
          title: '提示',
          content: '是否确定作废此订单?',
          onOk: function () {
            postAction(that.url.zuofei, { Id: record.Id }, that.httpHead).then((res) => {
              if (res.IsSuccess) {
                that.$message.success('操作成功')
                that.loadData()
              } else {
                that.$message.error(res.Msg)
              }
            })
          },
        })
      } else if (key === '撤回') {
        let that = this
        that.$confirm({
          title: '提示',
          content: '您确定要撤回该笔订单吗?',
          onOk: function () {
            that.loading = true
            putAction(that.url.cancel, { Id: record.Id }, that.httpHead).then((res) => {
              that.loading = false
              if (res.IsSuccess) {
                that.$message.success('操作成功')
              } else {
                that.$message.error(res.Msg)
              }
              that.loadData()

            })
          },
        })
      }
    },
    /**
     * AuditStatus  未提交 = 0, 审核中 = 1, 审核通过 = 2, 审核失败 = 3
     * OrderStatus  1-待下发 2-待收货 3-已入库 4-已完成
     * PaymentStatus  1.未付款 2.部分付款 3.已付款
     * InvoiceStatus  1.未收票 2已收票
     * @param {*} record 调价  票折 退货 买赔是否显示的验证方法
     */
    checkShow(record, isMaiPei) {
      let list = [1, 2, 3]
      // if (record.PaymentMode == 1) {
      //   //1预付款
      //   list = isMaiPei ? [1, 2, 3] : [3]
      // } else {
      //   //2月结
      //   list = [1, 2, 3]
      // }
      let oList = isMaiPei ? [2, 3] : [3, 4]

      return record.AuditStatus == 2 && oList.includes(record.OrderStatus) && list.includes(record.PaymentStatus)
    },
  },
}
</script>
<style scoped>

</style>
