<!--
 * @Author: LP
 * @Description: 预计到货详情
 * @Date: 2023/07/06
-->

<template>
  <a-spin :spinning="loading">
    <div style="min-height: 50vh">
      <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }">
        <a-descriptions :column="8">
          <a-descriptions-item>
            <span class="c999">供应商名称</span>
            <div>{{ model.SupplierName || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">送货方式</span>
            <div>{{ model.DeliveryMethodStr || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">订单编号</span>
            <div>{{ model.PurchaseOrderNo || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">预计到货日期</span>
            <div>{{ model.ExpectedArrivalDate || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">创建人</span>
            <div>{{ model.CreateByName || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">创建时间</span>
            <div>{{ model.CreateTime || '--' }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <a-card :bodyStyle="{ padding: '0 0 16px 0' }" class="mt20">
        <TableView
          :showCardBorder="false"
          :tab="tab"
          :columns="columns"
          :dataList="model.PurchaseOrderDetailInfos || []"
        />

        <!-- 底部区域 -->
        <a-affix :offset-bottom="10" class="affix pd1016">
          <a-button @click="goBack(true,true)">返回</a-button>
        </a-affix>
      </a-card>
    </div>
  </a-spin>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction } from '@/api/manage'
export default {
  name: 'ExpectedArrivalDetail',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      tab: {
        bordered: false, //是否显示表格的边框
        tabTitles: ['商品信息'],
        rowKey: 'Id',
      },
      id: '',
      httpHead: 'P36009',
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '采购数量',
          dataIndex: 'PurchaseQuantity',
          width: 150,
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },

        {
          title: '仓库',
          dataIndex: 'StorehouseStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '近效期',
          dataIndex: 'IsNearExpiry',
          width: 100,
          scopedSlots: { customRender: 'bool' },
          ellipsis: true,
        },
        {
          title: '备注',
          dataIndex: 'Remarks',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
      ],
      model: {},
      url: {
        detail: '/{v}/PurchaseOrder/GetInfo',
      },
    }
  },
  computed: {},
  created() {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.loadDetail()
    }
  },
  methods: {
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
  },
}
</script>
<style scoped>

.center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
