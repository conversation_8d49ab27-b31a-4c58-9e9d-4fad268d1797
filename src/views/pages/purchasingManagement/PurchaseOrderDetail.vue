<!--
 * @Author: LP
 * @Description: 采购订单详情
 * @Date: 2023/07/07
-->
<template>
  <a-spin :spinning="loading">
    <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }" v-if="model">
      <a-descriptions :column="7">
        <a-descriptions-item>
          <span class="c999">订单编号</span>
          <div>{{ model.PurchaseOrderNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">订单金额</span>
          <div>¥{{ model.OrderAmount }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">下单时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">审核状态</span>
          <div>{{ model.AuditStatusStr || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">订单状态</span>
          <div>{{ model.StatusStr || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">付款状态</span>
          <div>{{ model.PaymentStatusStr || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">发票状态</span>
          <div>{{ model.InvoiceStatusStr || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
    <a-card :bodyStyle="{ padding: '0 10px 10px 10px' }" class="mt20">
      <a-tabs v-model="activeTabKey">
        <a-tab-pane :key="1" tab="基本信息">
          <POrderBasicInformation :model="model" v-if="activeTabKey == 1 && model" />
        </a-tab-pane>
        <a-tab-pane :key="2" tab="入库信息">
          <POrdeRukuInformation :id="model.Id" v-if="activeTabKey == 2 && model" />
        </a-tab-pane>
        <a-tab-pane :key="3" tab="调价信息">
          <POrdeTiaojiaInformation :id="model.Id" v-if="activeTabKey == 3 && model" />
        </a-tab-pane>
        <a-tab-pane :key="4" tab="票折信息">
          <POrdePiaozheInformation :id="model.Id" v-if="activeTabKey == 4 && model" />
        </a-tab-pane>
        <a-tab-pane :key="5" tab="退货信息">
          <POrdeTuihuoInformation :id="model.Id" v-if="activeTabKey == 5 && model" />
        </a-tab-pane>
        <a-tab-pane :key="6" tab="买赔信息">
          <POrdeMaipeiInformation :id="model.Id" v-if="activeTabKey == 6 && model" />
        </a-tab-pane>
        <a-tab-pane :key="7" tab="发票信息">
          <POrdeFapiaoInformation :id="model.Id" v-if="activeTabKey == 7 && model" />
        </a-tab-pane>
        <a-tab-pane :key="8" tab="审核信息">
          <SupAuditInformation :bussinessNo="model.AuditId" v-if="activeTabKey == 8 && model" />
        </a-tab-pane>
      </a-tabs>

      <!-- 底部区域 -->
      <div>
        <div style="text-align: left; font-size: 20px; font-weight: bold;margin-left: 10px;padding-top: 20px;" v-if="activeTabKey == 1 && model">
          含税金额合计：<span style="color: red; margin-right: 50px">¥{{ getAmount() }}</span> 共<span style="color: red">{{ (model.PurchaseOrderDetailInfos || []).length }}</span>种商品
        </div>
        <a-divider />
        <div style="text-align: right;">
          <a-button @click="goBack(true,true)">返回</a-button>
        </div>
      </div>
      <!-- <a-affix :offset-bottom="10">
      
      </a-affix> -->
    </a-card>
  </a-spin>
</template>

<script>
import { getAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'PurchaseOrderDetail',
  mixins: [EditMixin],
  data() {
    return {
      model: null,
      loading: false,
      orderId: '', //采购订单id
      httpHead: 'P36009',
      activeTabKey: 1,
      url: {
        detail: '/{v}/PurchaseOrder/GetInfo',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.orderId = this.$route.query.id || ''
      this.activeTabKey = this.$route.query.activeTabKey ? Number(this.$route.query.activeTabKey) : 1
      this.loadDetail()
    }
  },
  created() { },
  methods: {
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.orderId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            that.orderType = that.model.OrderType
            if (that.model.PaymentMode) {
              that.model.PaymentMode = String(that.model.PaymentMode)
              if (that.model.PurchaseOrderDetailInfos) {
                that.dataSource = [].concat(that.model.PurchaseOrderDetailInfos)
                that.dataSource.forEach((x) => {
                  x.IsNearExpiry = String(x.IsNearExpiry)
                  x.IsPolicyReturn = String(x.IsPolicyReturn)
                })
              }
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    getAmount() {
      let num = 0
      if (this.model.PurchaseOrderDetailInfos) {
        this.model.PurchaseOrderDetailInfos.forEach((x) => {
          if (x.TaxIncludedPurchasePriceTotal * 1 >= 0) {
            num += x.TaxIncludedPurchasePriceTotal * 1
          }
        })
      }
      return this.getAmountOfMoney(num)
    },
  },
}
</script>

<style scoped>

</style>
