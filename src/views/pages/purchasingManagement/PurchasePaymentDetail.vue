<!--
 * @Author: LP
 * @Description: 采购付款详情
 * @Date: 2023/07/13
-->
<template>
  <a-card :bodyStyle="{ padding: '0 0' }">
    <a-tabs v-model="activeTabKey">
      <a-tab-pane :key="1" tab="详情信息">
        <a-spin :spinning="loading">
          <a-descriptions :column="7" v-if="model && activeTabKey == 1">
            <a-descriptions-item>
              <div class="flc hcenter" style="padding-left: 20px">
                <a-icon :type="model.AuditStatus == 2 ? 'check-circle' : 'clock-circle'" theme="twoTone" two-tone-color="#52c41a" style="font-size: 32px" />
                <span style="color: #52c41a">{{ model.AuditStatusStr || '--' }}</span>
              </div>
            </a-descriptions-item>
            <a-descriptions-item>
              <span class="c999">供应商名称</span>
              <div>{{ model.SupplierName || '--' }}</div>
            </a-descriptions-item>
            <a-descriptions-item>
              <span class="c999">付款单号</span>
              <div>{{ model.RemittedOrderNo || '--' }}</div>
            </a-descriptions-item>
            <a-descriptions-item>
              <span class="c999">付款金额</span>
              <div>¥{{ model.ActualPaymentAmount }}</div>
            </a-descriptions-item>
            <a-descriptions-item>
              <span class="c999">付款方式</span>
              <div>{{ model.PaymentModeStr || '--' }}</div>
            </a-descriptions-item>
            <a-descriptions-item>
              <span class="c999">创建人</span>
              <div>{{ model.CreateByName || '--' }}</div>
            </a-descriptions-item>
            <a-descriptions-item>
              <span class="c999">创建时间</span>
              <div>{{ model.CreateTime || '--' }}</div>
            </a-descriptions-item>
          </a-descriptions>

          <ApplyForPayment ref="ApplyForPayment" :opType="1" :isEdit="false" :id="id" v-if="activeTabKey == 1" @setAuditId="onSetAuditId" @setModel="setDetailModel" />
        </a-spin>
      </a-tab-pane>
      <a-tab-pane :key="2" tab="审核信息" style="padding-left: 16px">
        <SupAuditInformation :bussinessNo="auditId" v-if="activeTabKey == 2 && auditId" />
      </a-tab-pane>
    </a-tabs>
    <a-affix :offset-bottom="10" class="affix pr16">
      <div class="pd1016">
        <a-button @click="goBack(true)">返回</a-button>
      </div>
    </a-affix>
  </a-card>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'PurchasePaymentDetail',
  mixins: [EditMixin],
  data() {
    return {
      model: null,
      loading: false,
      id: '', //采购订单id
      httpHead: 'P36009',
      auditId: '',
      activeTabKey: 1,
      opType: 1, //1 详情 2编辑
      url: {},
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
    }
  },
  created() { },
  methods: {
    onSetAuditId(id) {
      this.auditId = id
    },
    setDetailModel(data) {
      this.model = data
      console.log('MBNJDs', this.model)
    },
  },
}
</script>

<style scoped>

</style>
