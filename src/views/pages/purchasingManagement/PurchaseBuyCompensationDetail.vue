<!--
 * @Author: LP
 * @Description: 采购买赔详情
 * @Date: 2023/07/06
-->

<template>
  <a-spin :spinning="loading">
    <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }">
      <a-descriptions :column="8">
        <a-descriptions-item>
          <div class="flc hcenter">
            <a-icon
              :type="getAuditIcon(model.BuyCompensationStatus)"
              theme="twoTone"
              :two-tone-color="getAuditColor(model.BuyCompensationStatus)"
              style="font-size: 32px"
            />
            <span :style="{ color: getAuditColor(model.BuyCompensationStatusStr) }">{{
              model.BuyCompensationStatusStr || '--'
            }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">供应商名称</span>
          <div>{{ model.SupplierName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">买赔单号</span>
          <div>{{ model.BuyCompensationNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">订单编号</span>
          <div>{{ model.PurchaseOrderNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">买赔金额</span>
          <div>¥{{ model.TotalBuyCompensationAmount }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建人</span>
          <div>{{ model.CreateByName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>

    <a-card :bodyStyle="{ padding: '0 0 16px 0' }" class="mt20">
      <TableView :showCardBorder="false" :tab="tab" :columns="columns" :dataList="model.GoodsItems || []" />

      <!-- 底部区域 -->
      <a-affix :offset-bottom="10" class="affix pr16">
        <a-button @click="goBack(true,true)">返回</a-button>
      </a-affix>
    </a-card>
  </a-spin>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction } from '@/api/manage'
export default {
  name: 'PurchaseBuyCompensationDetail',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      tab: {
        tabTitles: [],
        bordered: false, //是否显示表格的边框
        rowKey: 'Id',
      },
      httpHead: 'P36009',
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },

        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产日期',
          dataIndex: 'ManufacturingDate',
          width: 150,
          scopedSlots: { customRender: 'date' },
          ellipsis: true,
        },
        {
          title: '有效期',
          dataIndex: 'ExpirationDate',
          width: 150,
          scopedSlots: { customRender: 'date' },
          ellipsis: true,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '采购数量',
          dataIndex: 'PurchaseQuantity',
          width: 100,
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        {
          title: '已买赔数量',
          dataIndex: 'PurchasedCompensationQuantity',
          width: 120,
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        {
          title: '本次买赔数量',
          dataIndex: 'BuyCompensationQuantity',
          width: 120,
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
      ],
      loading: false,
      id: '',
      model: {},
      url: {
        detail: '/{v}/PurchaseBusiness/GetPurchaseBuyCompensationDetail',
      },
    }
  },
  computed: {},
  created() {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.loadDetail()
    }
  },
  methods: {
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
  },
}
</script>
<style scoped>

.center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
