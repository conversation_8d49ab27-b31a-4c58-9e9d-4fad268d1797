<!--
 * @Author: LP
 * @Description: 采购票折详情和编辑
 * @Date: 2023/07/10
-->
<template>
  <a-spin :spinning="loading || confirmLoading">
    <a-alert :message="'驳回原因：' + (model.AuditOpinion || '--')" type="warning" show-icon v-if="isEdit && model.AuditStatus == 3" class="mb10" />
    <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }">
      <a-descriptions :column="8">
        <a-descriptions-item>
          <div class="flc hcenter">
            <a-icon :type="getAuditIcon(model.AuditStatus)" theme="twoTone" :two-tone-color="getAuditColor(model.AuditStatus)" style="font-size: 32px" />
            <span :style="{ color: getAuditColor(model.AuditStatus) }">{{ model.AuditStatusStr || '--' }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">供应商名称</span>
          <div>{{ model.SupplierName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">票折单号</span>
          <div>{{ model.InvoiceDiscountNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">订单编号</span>
          <div>{{ model.PurchaseOrderNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">票折金额</span>
          <div>¥{{ model.TotalPriceInvoiceDiscount }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">结算状态</span>
          <div>{{ model.SettlementStatusStr || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建人</span>
          <div>{{ model.CreateByName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
    <div style="height: 15px; background-color: #f0f2f5"></div>
    <a-card :bodyStyle="{ padding: '0 0 16px 0' }">
      <POrderPiaozheView ref="POrderPiaozheView" :opType="isEdit ? 2 : 1" :model="model" />
    </a-card>
  </a-spin>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, postAction } from '@/api/manage'
import YYLButton from '@/components/yyl/business/YYLButton.vue'
export default {
  name: 'POrderPiaozheEditAndDetail',
  mixins: [ListMixin],
  components: { YYLButton },
  props: {
    /**
     * 订单id
     */
    id: {
      type: String,
      required: true,
      default: () => {
        return ''
      },
    },
    /**
     * 是否是编辑 默认false-详情
     */
    isEdit: {
      type: Boolean,
      default: () => {
        return false
      },
    },
  },
  data() {
    return {
      loading: false,
      confirmLoading: false,
      httpHead: 'P36009',
      model: {},
      url: {
        detail: '/{v}/PurchaseBusiness/GetPurchaseInvoiceDiscountDetail',
        save: '/{v}/PurchaseBusiness/EidtPurchaseInvoiceDiscount',
        validPurchaseInvoiceDiscountAudit: '/{v}/PurchaseBusiness/ValidPurchaseInvoiceDiscountAudit',
      },
    }
  },
  computed: {
    rules() {
      return {}
    },
  },
  mounted() {
    this.loadDetail()
  },
  created() { },
  methods: {
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id, isEdit: that.isEdit}, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    onSubmitAuditClick(IsDraft, callback) {
      // console.log('IsDraft  ',IsDraft)
      if (this.$refs.POrderPiaozheView) {
        this.$refs.POrderPiaozheView.getData((data) => {
          if (data) {
            data.IsDraft = IsDraft
            this.saveData(data, callback)
          }
        })
      }
    },
    saveData(data, callback) {
      let that = this
      that.confirmLoading = true

      callback && callback(that.confirmLoading)
      let params = {
        PurchaseOrderId: data.PurchaseOrderId,
        GoodsItems: data.GoodsItems,
        Remark: data.Remark,
        PurchaseInvoiceDiscountId: data.PurchaseInvoiceDiscountId,
        IsDraft: data.IsDraft,
        AgreementItems: data.AgreementItems
      }
      postAction(that.url.save, params, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (data.IsDraft) {
              that.confirmLoading = false
              that.$message.success('操作成功！')
              that.goBack(false,true)
              return
            }

            that.checkCanApproval(res.Data, callback, () => {
              that.createApproval(res.Data, callback)
            })
          } else {
            that.$message.error(res.Msg)
          }
          that.confirmLoading = false
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          callback && callback(that.confirmLoading)
        })
    },
    // 检测是否能审核
    checkCanApproval(bNo, callback, checkCallback) {
      let params = {
        businessRecordId: bNo
      }
      let that = this
      getAction(this.url.validPurchaseInvoiceDiscountAudit, params, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              checkCallback && checkCallback()
            } else {
              that.loading = false
              callback && callback(false)
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .catch((e) => {
          that.loading = false
          callback && callback(false)
        })
        .finally(() => {

        })
    },
    /**
     * 创建审核实例
     */
    createApproval(bNo, callback) {
      let that = this
      let params = {
        BussinessNo: bNo,
        Scenes: 7,
        OpratorId: that.getLoginUserId(),
      }
      if (!that.confirmLoading) {
        that.confirmLoading = true
      }
      postAction('/{v}/ApprovalWorkFlowInstance/CreateApproval', params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('提交审核成功！')
            that.goBack(false,true)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          that.confirmLoading = false
          callback && callback(that.confirmLoading)
        })
    },
  },
}
</script>

<style lang="less" scoped>

.mb20 {
  margin-bottom: 30px;
}
</style>
