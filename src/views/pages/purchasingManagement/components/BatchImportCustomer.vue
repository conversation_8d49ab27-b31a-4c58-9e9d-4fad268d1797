<!--
 * @Description: 批量导入模块，除了导入功能，其他的都是前端实现
 * @Version: 1.0
 * @Author: gang.peng
 * @Date: 2025-04-03 13:43:18
 * @LastEditors: wenji<PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-28 11:04:14
-->
<!-- 导入搜索置顶品种 modal-->
<template>
  <a-modal
    :title="modalTitle"
    :width="diyStyle.width"
    :visible="visible"
    @cancel="handleCancel"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <div>
      <template>
        <div v-if="isShowTopDownloadTempFile" style="font-size: 16px; font-weight: 600; margin-bottom: 15px">
          上传文件
          <a :href="downloadUrl" style="margin-left: 8px">
            <a-button type="primary">下载导入模板</a-button>
          </a>
        </div>
        <!-- 上面的区域 -->
        <slot style="margin-bottom: 5px" name="topView"></slot>
        <div style="margin-bottom: 5px"><span style="color: red">*</span> 导入文档：</div>
        <a-upload-dragger
          name="file"
          :multiple="false"
          :headers="tokenHeader"
          :action="importExcelUrl"
          @change="onUploadChange"
        >
          <p class="ant-upload-drag-icon">
            <a-icon type="plus" />
          </p>
          <p class="ant-upload-hint">{{ importConfigData.tips }}</p>
        </a-upload-dragger>
      </template>
      <div style="font-size: 16px; font-weight: 600; margin: 30px 0 15px 0">
        导入预览 <span style="color: red; font-size: 14px; font-weight: 400">{{ importConfigData.previewTips }}</span>
      </div>
      <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
      <SimpleSearchView ref="searchView" :searchParamsList="searchParamsList" @search="searchQuery" />
      <!-- 列表 -->
      <TableNewView
        ref="tableView"
        :table="table"
        :columns="tableColumns"
        :tabDataList="tabDataList"
        @onTabChange="onTabChange"
        :dataList="dataSource"
        @operate="operate"
        @onSelectChange="onSelectChange"
      >
      </TableNewView>
    </div>
    <template slot="footer">
      <a-button key="back" @click="handleCancel"> 取消 </a-button>
      <a-button key="submit" type="primary" @click="handleOk"> 保存 </a-button>
    </template>
  </a-modal>
</template>

<script>
import { apiHead } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')

export default {
  name: 'BatchImportCustomer',
  mixins: [ListMixin],
  components: { JEllipsis },
  props: {
    modalTitle: {
      type: String,
      default: '批量导入',
    },
    searchParamsList: {
      type: Array,
      default: () => [],
    },
    // 列
    tableColumns: {
      type: Array,
      default: () => [],
    },
    importHttpHead: {
      type: String,
      default: '',
    },
    importUrl: {
      type: String,
      default: '',
    },
    importConfig: {
      type: Object,
      default: () => {
        return this.importDefConfig
      },
    },
    diyStyle: {
      // 自定义样式
      type: Object,
      default: () => {
        return {
          width: 1100,
        }
      },
    },
    bordered: {
      type: Boolean,
      default: null,
    },
  },
  data() {
    return {
      visible: false,
      importDefConfig: {
        tips: '点击或将文档拖拽到这里上传',
        previewTips: '(重新导入将覆盖本次已导入数据，不可恢复)',
        tips: '点击或将文档拖拽到这里上传',
        templateFileName: '', //下载模板名字
        bottomBtn: [
          { name: '取消', type: '', funType: 'handleCancel' },
          { name: '保存', type: 'primary', funType: 'handleOk' },
        ],
      },
      tabDataList: [
        {
          name: '正常',
          value: 'Success',
          count: 0,
        },
        {
          name: '异常',
          value: 'Fail',
          count: 0,
        },
      ],
      table: {
        curStatus: 'Success',
        showPagination: false,
        rowKey: 'uniqueId',
        customSlot: ['commonContent'],
        selectType: 'checkbox',
        operateBtns: [
          {
            name: '批量移除',
            type: 'primary',
            icon: '',
            key: '批量移除',
            id: '390fe352-576b-4942-a548-d7e4fb885a5e', //按钮 id
          },
        ],
      },
      columns: [],
      dataSource: [],
      uploadResData: null,
      importFileName: '', //导入文件名称
    }
  },
  computed: {
    importExcelUrl: function () {
      return (
        apiHead(this.importHttpHead) +
        `${this.importUrl.indexOf('{v}') > -1 ? this.importUrl.replace(/{v}/, 'v1') : this.importUrl}`
      )
    },
    downloadUrl() {
      return apiHead('P36100') + `/${this.importConfigData.templateFileName}.xlsx`
    },
    importConfigData() {
      return Object.assign(this.importDefConfig, this.importConfig || {})
    },
    isShowTopDownloadTempFile() {
      let haveBottomDown = this.importConfigData.bottomBtn.find((item) => {
        return item.funType == 'downloadTempFile' || item.name == '模板下载'
      })
      if (haveBottomDown) {
        return false
      } else {
        return true
      }
    },
  },
  methods: {
    show() {
      this.init()
      this.visible = true
    },
    init() {
      this.dataSource = []
      this.table.curStatus = 'Success'
      this.tabDataList[0].count = 0
      this.tabDataList[1].count = 0
      if (this.bordered == true) {
        this.table.bordered = true
      } else {
        this.table.bordered = false
      }
    },
    searchQuery(params) {
      const Keyword = params ? params.Keyword : ''
      if (!Keyword || !this.uploadResData) {
        this.handleData(this.table.curStatus, true)
        return
      }
      const { SuccessList, FailList } = this.uploadResData
      const data = this.table.curStatus === 'Success' ? SuccessList : FailList
      this.dataSource = data.filter((item) => {
        const help = [item.ErpGoodsCode, item.ErpGoodsName, item.PinYinCode ? item.PinYinCode.toLowerCase() : null]
        return help.some((v) => v && v.includes(Keyword.toLowerCase()))
      })
      this.tabDataList = this.tabDataList.map((it) => ({
        ...it,
        count: it.value === this.table.curStatus ? this.dataSource.length : it.count,
      }))
    },
    onTabChange(type) {
      this.onClearSelected()
      this.$emit('tabChange', type)
      this.$refs.searchView.searchReset()
      this.handleData(type)
    },
    // 列表操作
    operate(record, type) {
      if (type == '移除') {
        console.log('移除', record)
        this.$confirm({
          title: '提示',
          content: '确认移除？',
          onOk: () => {
            this.onRemoveGoodsClick(record)
            this.tabDataList = this.tabDataList.map((it) => ({
              ...it,
              count: it.value === this.table.curStatus ? it.count - 1 : it.count,
            }))
            this.onClearSelected()
          },
          onCancel() {},
        })
      } else if (type == '批量移除') {
        if(!this.selectionRows.length) {
          this.$message.warning('请选择商品！')
          return
        }
        this.$confirm({
          title: '提示',
          content: '确认移除？',
          onOk: () => {
            this.selectionRows.forEach((item) => {
              this.onRemoveGoodsClick(item)
            })
            this.tabDataList = this.tabDataList.map((it) => ({
              ...it,
              count: it.value === this.table.curStatus ? it.count - this.selectionRows.length : it.count,
            }))
            this.onClearSelected()
          },
          onCancel() {},
        })
      }
    },
    // 确定
    handleOk() {
      if (this.table.curStatus !== 'Success') {
        this.$message.warning('请切换到导入正常的商品列表进行保存！')
        return
      }
      if (this.dataSource.length === 0) {
        this.$message.warning('暂无需要导入的商品！')
        return
      }
      this.$emit('ok', [...this.dataSource])
      this.close()
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.uploadResData = null
      this.visible = false
      this.importFileName = ''
    },
    onRemoveGoodsClick(record) {
      const idx = this.dataSource.findIndex((it) => (it.uniqueId === record.uniqueId))
      this.dataSource.splice(idx, 1)
      // 移除源数据
      const listField = this.table.curStatus
      const data = this.uploadResData[`${listField}List`]
      const index = data.findIndex((it) => it.uniqueId === record.uniqueId)
      this.uploadResData[`${listField}List`].splice(index, 1)
      this.uploadResData[`${listField}Count`] = this.uploadResData[`${listField}Count`] - 1
    },
    // 处理文件上传状态变化的方法
    onUploadChange(info) {
      this.uploadResData = null
      this.onClearSelected()
      this.$emit('tabChange', 'Success')
      const status = info.file.status
      // 当文件上传完成时
      if (status === 'done') {
        if (info.file.response.IsSuccess) {
          // 获取上传返回的数据
          let resData = info.file.response.Data
          this.uploadResData = resData || {}
          this.table.curStatus = 'Success'
          const { SuccessList, FailList } = this.uploadResData
          this.uploadResData.SuccessList = SuccessList.map((item, idx) => ({
            ...item,
            uniqueId: `${item.ErpGoodsCode}_success_${idx}`,
          }))
          this.uploadResData.FailList = FailList.map((item, idx) => ({
            ...item,
            uniqueId: `${item.ErpGoodsCode}_fail_${idx}`,
          }))
          // 加载数据列表
          this.handleData('Success', true)
        } else if (!info.file.response.IsSuccess && info.file.response.Msg) {
          // 上传失败时显示错误信息
          this.$message.warning(info.file.response.Msg)
          this.init()
        }
      }
    },
    handleData(type, isInit = false) {
      if (!this.uploadResData) return
      if (isInit) {
        // 初始化
        const { SuccessCount, FailCount } = this.uploadResData
        this.tabDataList[0].count = SuccessCount
        this.tabDataList[1].count = FailCount
      }
      if (type === 'Success') {
        this.dataSource = this.uploadResData.SuccessList.map((item) => ({ ...item }))
      } else if (type === 'Fail') {
        this.dataSource = this.uploadResData.FailList.map((item) => ({ ...item }))
      }
    },
  },
}
</script>
<style scoped>
.excel-div {
  position: relative;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background: #fafafa;
}
.icon-close {
  position: absolute;
  top: 10px;
  right: 10px;
}
.ant-result {
  padding: 5px 4px;
}
</style>
