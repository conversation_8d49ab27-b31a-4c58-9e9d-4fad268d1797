<!--
 * @Author: LP
 * @Description: 采购订单详情-发票信息
 * @Date: 2023/07/10
-->
<template>
  <a-spin :spinning="loading">
    <!-- 详情展示部分 -->

    <a-card v-if="dataList && dataList.length > 0" :bordered="false" :bodyStyle="{ padding: ' 0 16px' }" :key="index">
      <a-row>
        <a-col :span="12" v-for="(item, index) in dataList" :key="index">
          <a-descriptions :column="1" :title="'发票' + (index + 1)">
            <!-- <a-descriptions-item label="发票图片">
              <template v-if="model.ImageUrl && model.ImageUrl.length > 0">
                <ImageControl
                  v-for="item in model.ImageUrl"
                  :src="item"
                  :width="150"
                  :height="150"
                  :key="item"
                  style="margin-right: 10px;float: left;margin-bottom: 10px;"
                />
              </template>
              <span v-else>--</span>
            </a-descriptions-item> -->
            <a-descriptions-item label="发票代码">{{ item.InvoiceCode || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="发票号">{{ item.InvoiceNo || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="发票金额">{{ item.InvoiceAmount || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="发票地址">{{ item.InvoiceAddr || ' -- ' }}</a-descriptions-item>
          </a-descriptions>
        </a-col>
      </a-row>
    </a-card>
    <a-empty v-else />
  </a-spin>
</template>

<script>
const ImageControl = () => import('@/components/yyl/components/ImageControl')
import { getAction } from '@/api/manage'
export default {
  name: 'POrdeFapiaoInformation',
  mixins: [],
  components: { ImageControl },
  props: {
    /**
     * 买赔订单id
     */
    id: {
      type: String,
      required: true,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      loading: false,
      httpHead: 'P36009',
      dataList: [],
      url: {
        info: '/{v}/PurchaseOrder/GetPurchaseOrderInvoiceList'
      }
    }
  },
  computed: {
    rules() {
      return {}
    }
  },
  mounted() {
    this.loadInfo()
  },
  created() {},
  methods: {
    loadInfo() {
      let that = this
      that.loading = true
      getAction(that.url.info, { id: that.id }, that.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            that.dataList = res.Data || []
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    }
  }
}
</script>

<style lang="less" scoped>

.mb20 {
  margin-bottom: 30px;
}
</style>
