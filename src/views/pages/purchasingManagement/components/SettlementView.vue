<!--
 * @Author: LP
 * @Description: 预付款结算组件
 * @Date: 2023-12-22 10:33:04
-->
<template>
  <a-spin :spinning="loading || cLoading">
    <div>
      <a-alert
        :message="'驳回原因：' + (model.AuditOpinion || '--')"
        type="warning"
        show-icon
        v-if="isEdit && model.AuditOpinion"
        class="mb10"
      />
      <a-form-model ref="form" :rules="rules" :model="model">
        <a-card :title="isEdit ? '选择付款单据范围' : ''" :headStyle="{ padding: '10px 16px' }">
          <a-row :gutter="gutter" v-if="isEdit">
            <!-- 企业信息 -->
            <a-col :span="7">
              <a-form-model-item label="供应商名称" prop="SupplierId" :label-col="labelCol" :wrapper-col="wrapperCol">
                <SingleChoiceSearchView
                  style="width: 100%"
                  placeholder="请选择"
                  :httpParams="{ AuditStatus: 2, AuthBusiness: 2 }"
                  httpHead="P36003"
                  :keyWord="'KeyWord'"
                  Url="/{v}/Supplier/GetSuppliersForSelect"
                  :dataKey="{ name: 'Name', value: 'Id' }"
                  v-model="model.SupplierId"
                  :name="model.SupplierName"
                  @change="
                    (val, txt, item) => {
                      model.SupplierName = txt
                    }
                  "
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="7">
              <!--付款方式  1预付款 2月结 -->
              <a-form-model-item label="付款方式" prop="PaymentMode" :label-col="labelCol" :wrapper-col="wrapperCol">
                <EnumSingleChoiceView
                  style="width: 100%"
                  placeholder="请选择"
                  dictCode="EnumPurchasePaymentMode"
                  v-model="model.PaymentMode"
                  :disabled="true"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="7">
              <a-form-model-item label="日期" prop="BeginTime" :label-col="labelCol" :wrapper-col="wrapperCol">
                <a-range-picker
                  @change="(dates, dateStrings) => onTimeChange(dates, dateStrings, 'BeginTime', 'EndTime', true)"
                  v-model="rangeDate"
                  style="width: 100%"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="3" style="padding-top: 3px">
              <YYLButton
                menuId="819118d1-3c30-4758-ac09-bd64881da130"
                text="获取数据"
                type="primary"
                @click="onLoadDataClick"
              />
            </a-col>
          </a-row>
          <a-descriptions v-else>
            <a-descriptions-item label="供应商名称">{{ model.SupplierName || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="付款方式">{{ model.PurchasePaymentModeStr || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{
              moment(model.CreateTime).format('YYYY-MM-DD')
            }}</a-descriptions-item>
          </a-descriptions>
        </a-card>

        <TableView
          class="mt15"
          ref="tableView"
          showCardBorder
          :tab="tab"
          :columns="columns"
          :dataList="model.BusinessOrderList || []"
          @operateClick="onOperateClick"
          @actionClick="onActionClick"
        >
          <template slot="bottomView" v-if="(model.BusinessOrderList || []).length > 0">
            <a-row style="padding: 10px 10px">
              <a-col :span="12">
                <span
                  >本次结算金额合计：
                  <span style="color: red">{{ getShowPrice(totalAmount) }}</span>
                </span>
              </a-col>
              <a-col :span="12" style="text-align: right">
                <span
                  >剩余未结算金额合计：
                  <span style="color: red">{{ getShowPrice(unTotalAmount) }}</span>
                </span>
              </a-col>
            </a-row>
          </template>
        </TableView>

        <a-card
          title="发票信息"
          class="mt15"
          :headStyle="{ padding: '10px 16px' }"
          v-if="(model.BusinessOrderList || []).length > 0"
        >
          <a-form-model-item label="本次付款包含的发票号" prop="RemittedInvoiceNo" v-if="isEdit">
            <!-- <SelectMultipleInput
              ref="remittedInvoiceNoView"
              :httpParams="{
                pageIndex: 1,
                pageSize: 200,
                IsValid: true,
                DocumentOrderId: this.documentOrderIdList,
              }"
              keyWord="InvoiceNo"
              httpType="POST"
              :value="model.RemittedInvoiceNo || []"
              :dataKey="{ name: 'InvoiceNo', value: 'Id' }"
              placeholder="请选择"
              httpHead="P36009"
              url="/{v}/PurchaseRemittedOrder/GetPurchaseInvoiceByInvoiceNo"
              @change="changeInvoice"
            /> -->
            <MultipleChoiceSearchView
              ref="remittedInvoiceNoView"
              style="width: 100%"
              placeholder="请选择"
              :httpParams="{ pageIndex: 1, pageSize: 200, IsValid: true }"
              keyWord="InvoiceNo"
              httpType="POST"
              httpHead="P36009"
              :Url="url.invoiceNo"
              :dataKey="{ name: 'InvoiceNo', value: 'InvoiceNo' }"
              v-model="model.RemittedInvoiceNo"
              @change="changeInvoice"
            />
            <!-- @dataLoaded="onInvoiceNoLoaded" -->
          </a-form-model-item>
          <template v-else>
            <span>本次付款包含的发票号：</span><span>{{ model.RemittedInvoiceNoStr || '--' }}</span>
          </template>
        </a-card>
      </a-form-model>

      <!-- 商品明细 -->
      <SettlementGoodsDetailListModal ref="settlementGoodsDetailListModal" />
      <!-- 勾稽 -->
      <SettlementCheckingModal ref="settlementCheckingModal" @checkingOk="checkingCallback" />
    </div>
  </a-spin>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, postAction } from '@/api/manage'
import moment from 'moment'
export default {
  name: 'SettlementView',
  mixins: [ListMixin, EditMixin],
  components: {},
  props: {
    /**
     * 类型 暂未使用
     */
    opType: {
      type: Number,
      default: 1,
    },
    id: {
      type: String || Number,
      default: '',
    },
    cLoading: {
      type: Boolean,
      default: () => {
        return false
      },
    },
    /**
     * 是否能编辑
     */
    isEdit: {
      type: Boolean,
      default: () => {
        return false
      },
    },
  },
  data() {
    return {
      loading: false,
      labelCol: {
        xs: { span: 6 },
        sm: { span: 9 },
      },
      wrapperCol: {
        xs: { span: 18 },
        sm: { span: 15 },
      },
      isWGJ: true,
      tab: {
        tabTitles: ['付款单据列表'], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [
          {
            name: '结算勾稽',
            type: 'primary',
            icon: '',
            key: '结算勾稽',
            id: '414d2afe-4c44-443a-99bb-646f8d70b7cb',
            isShow: false,
          },
        ], //右上角按钮集合
        bordered: false, //是否显示表格的边框
        rowKey: 'BusinessOrderId',
      },
      model: {
        AuditOpinion: '',
        SupplierId: '',
        PaymentMode: '1', //1预付款 2月结
        BeginTime: null,
        EndTime: null,
        SettlementType: '',
        BusinessOrderList: [],
        RemittedInvoiceNo: [],
        RemittedInvoiceNoStr: '',
      },
      documentOrderIdList: [], //获取发票需要的参数 单据id集合
      gutter: 10,
      httpHead: 'P36009',
      baseColumns: [
        {
          title: '单据编号',
          dataIndex: 'BusinessOrderNo',
          scopedSlots: { customRender: 'component' },
          width: 200,
          ellipsis: true,
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessEventTypeStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '对应订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '发生日期',
          dataIndex: 'BusinessTime',
          width: 150,
          scopedSlots: { customRender: 'date' },
          ellipsis: true,
        },
        {
          title: '含税合计金额',
          dataIndex: 'TotalTaxIncludedPurchaseAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '剩余未勾稽发票金额',
          dataIndex: 'UnVerificationAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '未结算金额',
          dataIndex: 'UnSettlementAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '审核中未结算金额',
          dataIndex: 'UnTotalSettledAmountOnAudit',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '剩余未结算金额',
          dataIndex: 'UnTotalSettledAmountRemaining',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ],
      removeAction: {
        name: '移除',
        id: '40bbbcfb-06fd-413f-bb30-453e596b52a3',
        isShow: (record) => {
          return true
        },
      },
      detailAction: {
        name: '商品明细',
        id: 'ce190fae-c3dd-45fb-a943-78af3b96bf44',
        isShow: (record) => {
          return true
        },
      },
      actionColumns: [
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [],
        },
      ],
      columns: [],
      url: {
        detail: '/{v}/PurchaseSettlement/GetPurchasePrePaymentOrdersById',
        paymentOrderUrl: '/{v}/PurchaseSettlement/GetPurchasePrePaymentOrders',
        invoiceNo: '/{v}/PurchaseRemittedOrder/GetPurchaseInvoiceByInvoiceNo',
      },
    }
  },
  computed: {
    rules() {
      return {
        SupplierId: [{ required: true, message: '请选择!' }],
        BeginTime: [{ required: true, message: '请选择!' }],
        PaymentMode: [{ required: true, message: '请选择!' }],
        RemittedInvoiceNo: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.RemittedInvoiceNo) {
                callback(new Error('请选择'))
              } else {
                callback()
              }
            },
          },
        ],
      }
    },
    totalAmount() {
      return this.getAmountOfMoney(this.model.TotalSettlementAmount, 2)
    },
    unTotalAmount() {
      return this.getAmountOfMoney(this.model.UnTotalSettledAmount, 2)
    },
  },
  watch: {
    id(val) {
      if (val) {
        this.loadDetail()
      }
    },
  },
  created() {},
  mounted() {
    this.initColumns()
    if (this.id) {
      this.loadDetail()
    }
  },
  methods: {
    moment,
    initColumns() {
      this.isWGJ = true
      let list = [].concat(this.baseColumns)

      this.actionColumns[0].actionBtns = []
      if (this.isEdit) {
        this.actionColumns[0].actionBtns.push(this.removeAction)
      }
      this.actionColumns[0].actionBtns.push(this.detailAction)

      this.columns = list.concat(this.actionColumns)
    },
    /**
     * 改变table右上角发票勾稽显示内容和控制是否显示
     */
    checkOptionBtnIsShow() {
      if (this.isEdit) {
        this.tab.operateBtns[0].isShow = (this.model.BusinessOrderList || []).length > 0
        this.tab.operateBtns[0].name = '结算勾稽' + (this.isEdit ? (this.isWGJ == true ? '(未勾稽)' : '(已勾稽)') : '')
      } else {
        this.tab.operateBtns[0].name = '结算勾稽(已勾稽)'
        this.tab.operateBtns[0].isShow = true
      }
      // this.reloadFaPiaoData()
    },
    /**
     * 重新加载发票数据，供选择
     */
    reloadFaPiaoData() {
      // this.documentOrderIdList = []
      // this.model.BusinessOrderList.forEach((x) => {
      //   this.documentOrderIdList.push(x.BusinessOrderId)
      // })

      let that = this
      if (this.documentOrderIdList.length == 0) {
        that.model.RemittedInvoiceNo = []
        return
      }
      postAction(
        that.url.invoiceNo,
        {
          pageIndex: 1,
          pageSize: 200,
          IsValid: true,
          DocumentOrderId: this.documentOrderIdList,
        },
        'P36009'
      ).then((res) => {
        if (res.IsSuccess) {
          that.onInvoiceNoLoaded(res.Data || [])
        }
      })
    },
    onInvoiceNoLoaded(list) {
      if (this.isEdit == true && !this.id) {
        this.model.RemittedInvoiceNo = []
        list.forEach((x) => {
          this.model.RemittedInvoiceNo.push(x.InvoiceNo)
        })
        this.$forceUpdate()
      }
    },
    /**
     * 加载详情数据
     */
    loadDetail() {
      if (this.loading || !this.id) {
        return
      }
      this.isWGJ = false
      let that = this
      that.loading = true
      getAction(that.url.detail, { purchaseSettlementOrderId: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            that.$emit('setAuditId', that.model.AuditId)
            that.model.PaymentMode = '' + this.model.PurchasePaymentMode
            if (that.model.BeginTime && that.model.EndTime) {
              that.rangeDate = [moment(that.model.BeginTime), moment(that.model.EndTime)]
            } else {
              that.rangeDate = []
            }
            that.model.RemittedInvoiceNo = []
            if (that.model.RemittedInvoiceNoStr) {
              that.model.RemittedInvoiceNoStr.split(',').forEach((x) => {
                that.model.RemittedInvoiceNo.push(x)
              })
            }
            console.log('detail', that.model)
            that.checkOptionBtnIsShow()
            that.jisuanAmount()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },

    /**
     * 获取数据
     */
    onLoadDataClick() {
      console.log(this.model)
      let list = ['SupplierId', 'PaymentMode', 'BeginTime']
      let num = 0
      this.$refs.form.validateField(list, (errMsg) => {
        num++
        if (num == list.length) {
          if (!errMsg) {
            this.loadListData()
          }
        }
      })
    },
    /**
     * 获取单据数据
     */
    loadListData() {
      if (!this.url.paymentOrderUrl || !this.model.SupplierId || !this.model.BeginTime || !this.model.EndTime) {
        return
      }
      let that = this
      that.loading = true
      let params = {
        SupplierId: that.model.SupplierId,
        PaymentMode: that.model.PaymentMode * 1,
        BeginTime: that.model.BeginTime,
        EndTime: that.model.EndTime,
      }
      postAction(that.url.paymentOrderUrl, params, that.httpHead)
        .then((res) => {
          that.isWGJ = true
          if (res.IsSuccess) {
            that.model = Object.assign(res.Data || {}, params)
            that.model.PaymentMode = '' + params.PaymentMode
            console.log('res.Data ', that.model)
            that.checkOptionBtnIsShow()
            that.jisuanAmount()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },

    onOperateClick(type) {
      if (type == '结算勾稽') {
        if (this.isEdit) {
          let params = {
            SupplierId: this.model.SupplierId,
            PaymentMode: this.model.PaymentMode * 1,
            BeginTime: this.model.BeginTime,
            EndTime: this.model.EndTime,
            BusinessOrderIdList: [],
          }
          this.model.BusinessOrderList.forEach((x) => {
            params.BusinessOrderIdList.push(x.BusinessOrderId)
          })
          this.$refs.settlementCheckingModal.showOne(this.isEdit, params, this.isWGJ)
        } else {
          this.$refs.settlementCheckingModal.lookPay(this.id)
        }
      }
    },

    onActionClick(record, key, index) {
      if (key === '移除') {
        const no = record.BusinessOrderNo
        let dIndex = this.model.BusinessOrderList.findIndex((x) => x.BusinessOrderId === record.BusinessOrderId)
        if (dIndex > -1) {
          this.isWGJ = true
          this.model.BusinessOrderList.splice(dIndex, 1)
          this.model.TotalSettlementAmount = 0
          this.jisuanAmount()
          this.$message.success('单据 [' + no + '] 移除成功!')
        }
      } else if (key === '商品明细') {
        this.$refs.settlementGoodsDetailListModal.show(record.BusinessOrderId, record.BusinessEventType, false)
      }
    },
    /**
     * 检查form表填填写情况
     * @param {*返回提交结构的数据} callback
     */
    checkForm(callback) {
      let that = this
      this.$refs.form.validate((err, values) => {
        if (err) {
          if (!that.model.BusinessOrderList || that.model.BusinessOrderList.length == 0) {
            that.$message.warning('请先获取单据数据!')
            callback && callback()
            return
          }
          // 申请付款
          if (that.isWGJ) {
            that.$message.warning('请先勾稽！')
            callback && callback()
            return
          }
          callback && callback(that.getData())
        } else {
          callback && callback()
        }
      })
    },
    /**
     * 获取数据，把页面model数据转换为提交数据
     */
    getData() {
      let invoiceNoStr = ''
      if (this.model.RemittedInvoiceNo) {
        this.model.RemittedInvoiceNo.forEach((x, i, arr) => {
          invoiceNoStr += x + (i < arr.length - 1 ? ',' : '')
        })
      }
      let data = {
        RemittedInvoiceNoStr: invoiceNoStr,
        OrderList: this.model.BusinessOrderList,
        SupplierId: this.model.SupplierId,
        BeginTime: this.model.BeginTime,
        EndTime: this.model.EndTime,
      }
      if (this.id) {
        data['PurchaseSettlementOrderId'] = this.id
      }
      return data
    },
    /**
     * 计算金额
     */
    jisuanAmount() {
      let amount = 0
      this.model.BusinessOrderList.forEach((x) => {
        amount += x.UnTotalSettledAmountRemaining
      })
      this.model.UnTotalSettledAmount = amount
    },
    /**
     * 发票勾稽回调
     * @param {* data} data
     */
    checkingCallback(data, list) {
      if (data) {
        this.isWGJ = false

        this.tab.operateBtns[0].name = '结算勾稽' + (this.isEdit ? (this.isWGJ == true ? '(未勾稽)' : '(已勾稽)') : '')

        this.model.TotalSettlementAmount = data.TotalCurrentSettledAmount
        this.model.UnTotalSettledAmount = data.TotalUnSettlementAmount

        this.documentOrderIdList = list
        this.reloadFaPiaoData()

        // if (data.BusinessOrderList && data.BusinessOrderList.length > 0) {
        //   this.loading = true
        //   let oldList = [].concat(this.model.BusinessOrderList)
        //   let newList = []
        //   oldList.forEach((old) => {
        //     let bool = data.BusinessOrderList.some((x) => x.BusinessOrderId == old.BusinessOrderId)
        //     if (bool) {
        //       newList.push(old)
        //     }
        //   })
        //   this.model.BusinessOrderList = newList
        //   this.loading = false
        //   console.log('model', this.model)
        // }
      }
    },
    /**
     * 选了发票号过后刷新界面
     * @param {*} key
     * @param {*} list
     */
    changeInvoice(key, list) {
      if (this.model.RemittedInvoiceNo) {
        this.$refs.form.clearValidate('RemittedInvoiceNo')
      }
      this.$forceUpdate()
    },
  },
}
</script>
<style scoped lang="less">


// /deep/.ant-form-item-control {
//   height: 34px;
// }
/deep/.ant-card-head-title {
  padding: 0 0;
}
</style>
