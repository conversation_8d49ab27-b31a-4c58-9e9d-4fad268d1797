<!--
 * @Description: 采购订单/计划 详情 - 基本信息
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-06 14:17:02
 * @LastEditors: wenjing<PERSON>ao
 * @LastEditTime: 2025-05-13 11:02:15
-->
<template>
  <a-spin :spinning="loading">
    <!-- 详情展示部分 -->

    <a-card :bordered="false" :bodyStyle="{ padding: ' 0 16px' }">
      <a-descriptions>
        <a-descriptions-item label="供应商名称">{{ model.SupplierName || '--' }}</a-descriptions-item>
        <a-descriptions-item label="委托人">{{ model.DelegateScopeName || '--' }}</a-descriptions-item>
        <a-descriptions-item label="付款方式">{{ model.PaymentModeStr || '--' }}</a-descriptions-item>
        <a-descriptions-item v-if="isPlan" label="付款类型">{{ model.PaymentTypeStr || '--' }}</a-descriptions-item>
        <a-descriptions-item label="送货方式">{{ model.DeliveryMethodStr || '--' }}</a-descriptions-item>
        <a-descriptions-item label="预到货日期">{{ model.ExpectedArrivalDate || '--' }}</a-descriptions-item>
        <a-descriptions-item v-if="isPlan" label="政策说明">{{ model.PolicyStatement || '--' }}</a-descriptions-item>
        <a-descriptions-item label="备注">{{ model.Remarks || '--' }}</a-descriptions-item>
      </a-descriptions>

      <TableView :showCardBorder="true" :tab="tab" :columns="columns" :dataList="model.PurchaseOrderDetailInfos || []">
        <span slot="clickA" slot-scope="{ text, record,index, column}">
          <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
            <j-ellipsis :value="'' + text" :length="50" />
          </a>
          <span v-else>--</span>
        </span>
        <!-- 全部点击操作 -->
        <span slot="clickAl" slot-scope="{text, record, index, column}">
          <a v-if="record.ErpGoodsId" @click="clickA(text, record, index, column)">
            <j-ellipsis :value="(text || text == 0)?('' + text):'--'" :length="50" />
          </a>
          <span v-else>--</span>
        </span>
        <!-- 价格点击操作 -->
        <span slot="clickPriceA" slot-scope="{ text, record,index, column}">
          <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
            <j-ellipsis :value="text == 0?(''+text):('¥' + text)" :length="50" />
          </a>
          <span v-else>--</span>
        </span>
      </TableView>
    </a-card>
    <!-- 历史采购价 -->
    <HistoricalPurchasePriceModal ref="HistoricalPurchasePriceModal" />
    <!-- 平均参考成本 -->
    <AverageReferenceCostModal ref="AverageReferenceCostModal" />
    <!-- 政策底价 -->
    <PolicyBasePriceModal ref="PolicyBasePriceModal" />
    <!-- 库存明细 -->
    <InventoryDetailModal ref="InventoryDetailModal" />
    <!-- 本月销售明细 -->
    <SalesThisMonthModal ref="SalesThisMonthModal" />
    <!-- 在途库存 -->
    <PlanTotalInTransitCountModal ref="PlanTotalInTransitCountModal" />
  </a-spin>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'POrderBasicInformation',
  mixins: [ListMixin],
  components: { JEllipsis },
  props: {
    /**
     * 数据model
     */
    model: {
      type: Object,
      required: true,
      default: () => {
        return {}
      },
    },
    isPlan: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      tab: {
        tabTitles: ['商品信息'],
        customSlot: ['clickA', 'clickAl', 'clickPriceA'],
      },
      url: {},
    }
  },
  computed: {
    rules() {
      return {}
    },
    columns() {
      const allColumnsMap = {
        ErpGoodsName: {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          width: 150,
          fixed: 'left',
          ellipsis: true,
        },
        ErpGoodsCode: {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 100,
          ellipsis: true,
        },
        PackingSpecification: {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        BrandManufacturer: {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        GoodsOwnerBy: {
          title: '责任人',
          dataIndex: 'GoodsOwnerBy',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        Producer: {
          title: '产地',
          dataIndex: 'Producer',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        PackageCount: {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        PackageUnit: {
          title: '单位',
          dataIndex: 'PackageUnit',
          scopedSlots: { customRender: 'component' },
          width: 60,
          ellipsis: true,
        },
        PurchaseQuantity: {
          title: '采购数量',
          dataIndex: 'PurchaseQuantity',
          scopedSlots: { customRender: 'component' },
          width: 90,
          precision: 2,
          ellipsis: true,
        },
        TaxIncludedPurchasePrice: {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          scopedSlots: { customRender: 'component' },
          width: 90,
          precision: 6,
          ellipsis: true,
        },
        TaxIncludedPurchasePriceTotal: {
          title: '含税金额',
          dataIndex: 'TaxIncludedPurchasePriceTotal',
          scopedSlots: { customRender: 'price' },
          width: 100,
          precision: 2,
          ellipsis: true,
        },
        StorehouseStr: {
          title: '仓库',
          dataIndex: 'StorehouseStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          keyStr: 'StorehouseStr',
        },
        IsNearExpiry: {
          title: '近效期',
          dataIndex: 'IsNearExpiry',
          scopedSlots: { customRender: 'bool' },
          width: 80,
          ellipsis: true,
        },
        PurchaseRebateFlagStr: {
          title: '返利标记',
          dataIndex: 'PurchaseRebateFlagStr',
          scopedSlots: { customRender: 'component' },
          width: 80,
          ellipsis: true,
        },
        OriginalPrice: {
          title: '销售原价',
          dataIndex: 'OriginalPrice',
          scopedSlots: { customRender: 'price' },
          width: 110,
          precision: 6,
          ellipsis: true,
        },
        MinimumSellingPrice: {
          title: '商销底价',
          dataIndex: 'MinimumSellingPrice',
          width: 110,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        BoxRebateAmount: {
          title: '每盒返利金额',
          dataIndex: 'BoxRebateAmount',
          width: 100,
          scopedSlots: { customRender: 'price' },
          precision: 6,
          ellipsis: true,
        },
        RebateDays: {
          title: '返利到账天数',
          dataIndex: 'RebateDays',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        MarketPurchasePrice: { 
          title: '市场采购价', 
          dataIndex: 'MarketPurchasePrice', 
          width: 100, 
          scopedSlots: { customRender: 'component' },
        },
        GrossMarginRate: { 
          title: '毛利率', 
          dataIndex: 'GrossMarginRate', 
          width: 80, 
          scopedSlots: { customRender: 'component' },
        },
        PurchaseBatchNo: { 
          title: '购进批号', 
          dataIndex: 'PurchaseBatchNo', 
          width: 120, 
          scopedSlots: { customRender: 'component' },
        },
        EstimatedTimeOfSale: { 
          title: '预计销售时间', 
          dataIndex: 'EstimatedTimeOfSale', 
          width: 120, 
          scopedSlots: { customRender: 'component' },
        },
        IsForSale: {
          title: '是否商销',
          dataIndex: 'IsForSale',
          width: 100,
          scopedSlots: { customRender: 'bool' },
        },
        IsPackageHandled: {
          title: '是否包处理',
          dataIndex: 'IsPackageHandled',
          width: 100,
          scopedSlots: { customRender: 'bool' },
        },
        Remarks: {
          title: '备注',
          dataIndex: 'Remarks',
          scopedSlots: { customRender: 'component' },
          width: 120,
          maxLength: 50,
          maxLength: 100,
          ellipsis: true,
        },
        PurchaseTaxRate: {
          title: '税率',
          dataIndex: 'PurchaseTaxRate',
          width: 60,
          scopedSlots: { customRender: 'percent' },
          ellipsis: true,
        },
        AvailableInventory: {
          title: '可销总库存',
          dataIndex: 'AvailableInventory',
          scopedSlots: { customRender: 'clickA' },
          width: 100,
          ellipsis: true,
        },
        InboundQuantity: {
          title: '入库数量',
          dataIndex: 'InboundQuantity',
          scopedSlots: { customRender: 'component' },
          width: 120,
          ellipsis: true,
        },
        RemainingUnsoldCount: {
          title: '剩余未销售数量',
          dataIndex: 'RemainingUnsoldCount',
          scopedSlots: { customRender: 'component' },
          width: 120,
          ellipsis: true,
        },
        EstimatedSaleTime: {
          title: '预计可销时间',
          dataIndex: 'EstimatedSaleTime',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        InTransitInventory: {
          title: '在途库存',
          dataIndex: 'InTransitInventory',
          scopedSlots: { customRender: 'clickA' },
          width: 100,
          ellipsis: true,
        },
        LastMonthCount: {
          title: '上月销量',
          dataIndex: 'LastMonthCount',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        ThisMonthCount: {
          title: '本月销量',
          dataIndex: 'ThisMonthCount',
          width: 100,
          scopedSlots: { customRender: 'clickAl' },
          ellipsis: true,
        },
        LastPurchasePrice: {
          title: '末次采购价',
          dataIndex: 'LastPurchasePrice',
          width: 100,
          scopedSlots: { customRender: 'clickPriceA' },
          ellipsis: true,
        },
        NationalSupplyPrice: {
          title: '平台原价',
          dataIndex: 'NationalSupplyPrice',
          width: 100,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        ActivityType: {
          title: '活动类型',
          dataIndex: 'ActivityTypeName',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        ActivityPrice: {
          title: '活动价格',
          dataIndex: 'ActivityPrice',
          width: 100,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        MoveAverageCostPrice: {
          title: '含购进的移动平均成本',
          dataIndex: 'MoveAverageCostPrice',
          width: 160,
          scopedSlots: { customRender: 'clickPriceA' },
          ellipsis: true,
        }
      }
      const planColumns = [
        'ErpGoodsName',
        'ErpGoodsCode',
        'PackingSpecification',
        'BrandManufacturer',
        'Producer',
        'PackageCount',
        'PackageUnit',
        'PurchaseQuantity',
        'TaxIncludedPurchasePrice',
        'TaxIncludedPurchasePriceTotal',
        'StorehouseStr',
        'IsNearExpiry',
        'PurchaseRebateFlagStr',
        'OriginalPrice',
        'MinimumSellingPrice',
        'BoxRebateAmount',
        'RebateDays',
        'MarketPurchasePrice',
        'GrossMarginRate',
        'PurchaseBatchNo',
        'EstimatedTimeOfSale',
        'IsForSale',
        'IsPackageHandled',
        'Remarks',
        'PurchaseTaxRate',
        'AvailableInventory',
        'InboundQuantity',
        'RemainingUnsoldCount',
        'EstimatedSaleTime',
        'InTransitInventory',
        'LastMonthCount',
        'ThisMonthCount',
        'LastPurchasePrice',
        'NationalSupplyPrice',
        'ActivityType',
        'ActivityPrice',
        'MoveAverageCostPrice',
      ]
      const orderColumns = [
        'ErpGoodsName',
        'ErpGoodsCode',
        'PackingSpecification',
        'BrandManufacturer',
        'GoodsOwnerBy',
        'Producer',
        'PackageCount',
        'PackageUnit',
        'PurchaseQuantity',
        'TaxIncludedPurchasePrice',
        'TaxIncludedPurchasePriceTotal',
        'StorehouseStr',
        'IsNearExpiry',
        'PurchaseRebateFlagStr',
        'OriginalPrice',
        'MinimumSellingPrice',
        'BoxRebateAmount',
        'RebateDays',
        'Remarks',
        'PurchaseTaxRate',
        'AvailableInventory',
        'EstimatedSaleTime',
        'InTransitInventory',
        'LastMonthCount',
        'ThisMonthCount',
        'LastPurchasePrice',
        'NationalSupplyPrice',
        'ActivityType',
        'ActivityPrice',
        'MoveAverageCostPrice',
      ]
      return this.isPlan
        ? planColumns.map(key => allColumnsMap[key])
        : orderColumns.map(key => allColumnsMap[key])
    }
  },
  methods: {
    clickA(text, record, index, column) {
      let key = column.dataIndex
      // 可销总库存
      if (key == 'AvailableInventory') {
        this.$refs.InventoryDetailModal.show(record)
      } else if (key == 'ThisMonthCount') {
        // 本月销量
        this.$refs.SalesThisMonthModal.show(record)
      } else if (key == 'LastPurchasePrice') {
        // 末次进价
        this.$refs.HistoricalPurchasePriceModal.show(record)
      } else if (key == 'MoveAverageCostPrice') {
        // 含购进的移动平均成本
        this.$refs.PolicyBasePriceModal.show(record)
      } else if (key == 'InTransitInventory') {
        // 在途库存
        this.$refs.PlanTotalInTransitCountModal.show(record)
      }
    },
  },
}
</script>

<style lang="less" scoped>

</style>
