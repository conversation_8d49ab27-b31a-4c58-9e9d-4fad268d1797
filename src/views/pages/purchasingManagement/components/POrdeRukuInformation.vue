<!--
 * @Author: LP
 * @Description: 采购订单详情-入库信息
 * @Date: 2023/07/10
-->
<template>
  <a-spin :spinning="loading">
    <!-- 详情展示部分 -->
    <template v-if="dataList && dataList.length > 0">
      <a-card
        :bordered="false"
        :bodyStyle="{ padding: ' 0 16px' }"
        v-for="(item, index) in dataList"
        :key="index"
        class="mb20"
      >
        <a-descriptions>
          <a-descriptions-item label="入库单号">{{ item.WarehouseInNo || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="入库时间">{{ item.WarehouseInTime || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="入库金额">{{ item.WarehouseInTotalAmount || ' -- ' }}</a-descriptions-item>
        </a-descriptions>

        <TableView
          :showCardBorder="true"
          :tab="tab"
          :columns="columns"
          :dataList="item.PurchaseInventoryOrderDetailDtos || []"
        /> </a-card
    ></template>
    <a-empty v-else />
  </a-spin>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction } from '@/api/manage'
export default {
  name: 'POrdeRukuInformation',
  mixins: [ListMixin],
  components: {},
  props: {
    /**
     * 买赔订单id
     */
    id: {
      type: String,
      required: true,
      default: () => {
        return ''
      },
    },
  },
  data() {
    return {
      loading: false,
      tab: {
        tabTitles: [],
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '有效期',
          dataIndex: 'ValidityPeriod',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '入库数量',
          dataIndex: 'InboundQuantity',
          width: 150,
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        {
          title: '订单数量',
          dataIndex: 'PurchaseOrderQuantity',
          width: 150,
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        {
          title: '入库含税金额小计',
          dataIndex: 'TotalAmount',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '批次号',
          dataIndex: 'BatchNumber',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
      ],
      dataList: {},
      httpHead: 'P36009',
      url: {
        info: '/{v}/PurchaseOrder/GetInventoryOrderInfo',
      },
    }
  },
  computed: {
    rules() {
      return {}
    },
  },
  mounted() {
    this.loadInfo()
  },
  created() {},
  methods: {
    loadInfo() {
      let that = this
      that.loading = true
      getAction(that.url.info, { purchaseOrderId: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.dataList = res.Data || []
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
  },
}
</script>

<style lang="less" scoped>

.mb20 {
  margin-bottom: 30px;
}
</style>
