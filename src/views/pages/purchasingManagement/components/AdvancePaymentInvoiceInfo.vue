<!--
 * @Author: LP
 * @Description: 采购预付款审核和详情 组件
 * @Date: 2023/08/04
-->

<template>
  <a-spin :spinning="cLoading">
    <div>
      <a-alert
        :message="'驳回原因：' + (model.RejectReason || '--')"
        type="warning"
        show-icon
        v-if="isEdit && model.RejectReason"
        class="mb10"
      />

      <!-- 付款单据列表 -->
      <TableView
        class="mt15"
        ref="tableView"
        showCardBorder
        :tab="tab"
        :columns="columns"
        :dataList="dataSource"
        @actionClick="onActionClick"
      >
        <template slot="bottomView" v-if="opType == 2">
          <div style="text-align: right; margin: 10px 20px">
            合计应收发票金额：<span style="color: red"
              >{{ total < 0 ? '-' : '' }}¥{{ total < 0 ? String(total).replace('-', '') : total }}</span
            >
          </div>
        </template>
      </TableView>

      <!-- 发票信息 -->
      <a-card class="mt15" :headStyle="{ padding: '10px 16px' }" title="发票信息">
        <template v-if="model && model.RemittedInvoiceDtos">
          <a-form-model ref="form" :rules="rules" :model="model" v-if="isEdit">
            <a-row :gutter="gutter">
              <a-col :span="8" v-for="(item, index) in model.RemittedInvoiceDtos || []" :key="index">
                <a-col :span="24">
                  <span style="font-weight: bold; padding-right: 20px">发票{{ index + 1 }}</span>
                  <YYLButton
                    menuId="b860332c-9c79-4a59-a4b0-3bdf67c9c0f3"
                    :text="'查看发票勾稽'"
                    type="primary"
                    size="small"
                    @click="onFPGJClick(item, index)"
                  />
                  <!--  :disabled="!item.InvoiceCode || !item.InvoiceNo" -->
                  <a-button type="danger" size="small" @click="onRemoveInvoiceClick(index)" v-if="index > 0"
                    >删除</a-button
                  >
                </a-col>
                <a-col :span="24">
                  <a-form-model-item
                    label="发票类型"
                    prop="InvoiceType"
                    :key="'InvoiceType' + index"
                    :rules="{
                      required: true,
                      validator: (rule, value, callback) => {
                        if (!model.RemittedInvoiceDtos[index].InvoiceType) {
                          callback('请选择')
                        } else {
                          callback()
                        }
                      },
                    }"
                  >
                    <EnumSingleChoiceView
                      style="width: 100%"
                      placeholder="请选择"
                      dictCode="EnumSalesOrderInvoiceType"
                      v-model="item.InvoiceType"
                      tagType="radio"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="24">
                  <a-form-model-item
                    label="发票代码"
                    prop="InvoiceCode"
                    :key="'InvoiceCode' + index"
                    :rules="{
                      required: true,
                      validator: (rule, value, callback) => {
                        if (!model.RemittedInvoiceDtos[index].InvoiceCode) {
                          callback('请选择')
                        } else {
                          callback()
                        }
                      },
                    }"
                  >
                    <a-input placeholder="请输入" v-model="item.InvoiceCode" style="width: 100%" :maxLength="30" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="24">
                  <a-form-model-item
                    label="发票号"
                    prop="InvoiceNo"
                    :key="'InvoiceNo' + index"
                    :rules="{
                      required: true,
                      validator: (rule, value, callback) => {
                        if (!model.RemittedInvoiceDtos[index].InvoiceNo) {
                          callback('请选择')
                        } else {
                          callback()
                        }
                      },
                    }"
                  >
                    <a-input placeholder="请输入" v-model="item.InvoiceNo" style="width: 100%" :maxLength="100" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="24">
                  <a-form-model-item
                    label="税率"
                    prop="InvoiceTaxRate"
                    :key="'InvoiceTaxRate' + index"
                    :rules="{
                      required: false,
                      validator: (rule, value, callback) => {
                        // if (!model.RemittedInvoiceDtos[index].InvoiceTaxRate) {
                        //   callback('请选择')
                        // } else {}
                        callback()
                      },
                    }"
                  >
                    <span style="width: 100%">{{ item.InvoiceTaxRate }}</span>
                  </a-form-model-item>
                </a-col>
                <a-col :span="24">
                  <a-form-model-item
                    label="发票金额"
                    prop="InvoiceAmount"
                    :key="'InvoiceAmount' + index"
                    :rules="{
                      required: true,
                      validator: (rule, value, callback) => {
                        if (!model.RemittedInvoiceDtos[index].InvoiceAmount) {
                          callback('请选择')
                        } else {
                          callback()
                        }
                      },
                    }"
                  >
                    <a-input placeholder="请输入" v-model="item.InvoiceAmount" style="width: 100%" :maxLength="30" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="24">
                  <a-form-model-item
                    label="发票日期"
                    prop="InvoiceDate"
                    :key="'InvoiceDate' + index"
                    :rules="{
                      required: true,
                      validator: (rule, value, callback) => {
                        if (!model.RemittedInvoiceDtos[index].InvoiceDate) {
                          callback('请选择')
                        } else {
                          callback()
                        }
                      },
                    }"
                  >
                    <a-date-picker v-model="item.InvoiceDate" style="width: 100%" placeholder="请选择" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="24">
                  <a-form-model-item label="发票地址">
                    <a-input placeholder="请输入" v-model="item.InvoiceAddr" style="width: 100%" :maxLength="100" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="24">
                  <a-form-model-item label="本次勾稽金额">
                    <a-input
                      placeholder="请输入"
                      v-model="item.CurrentVerificationAmount"
                      style="width: 100%"
                      :maxLength="100"
                      :disabled="true"
                    />
                  </a-form-model-item>
                </a-col>
              </a-col>
            </a-row>
          </a-form-model>
          <a-row v-else>
            <a-col :span="8" v-for="(item, index) in model.RemittedInvoiceDtos || []" :key="index">
              <a-col style="margin-bottom: 10px" span="24">
                <span style="font-size: 16px; font-weight: bold; padding-right: 15px">上传发票</span>
                <span style="font-size: 16px; color: red; font-weight: bold">{{ faPiaoHint }}</span>
              </a-col>
              <a-col span="24" style="font-weight: bold"
                ><span class="pr16">发票{{ index + 1 }}</span>
                <YYLButton
                  menuId="b860332c-9c79-4a59-a4b0-3bdf67c9c0f3"
                  :text="'查看发票勾稽'"
                  type="primary"
                  size="small"
                  @click="onFPGJClick(item, index)"
                />
              </a-col>
              <a-col span="24" class="mt15">
                <span class="c999">发票类型：</span>{{ item.InvoiceTypeStr || '--' }}
              </a-col>
              <a-col span="24"> <span class="c999">发票代码：</span>{{ item.InvoiceCode || '--' }} </a-col>
              <a-col span="24"> <span class="c999">发票号：</span>{{ item.InvoiceNo || '--' }} </a-col>
              <a-col span="24">
                <span class="c999">税率：</span
                >{{ item.InvoiceTaxRate != undefined && item.InvoiceTaxRate >= 0 ? item.InvoiceTaxRate : '--' }}
              </a-col>
              <a-col span="24"> <span class="c999">发票金额：</span>¥{{ item.InvoiceAmount }} </a-col>
              <a-col span="24"> <span class="c999">发票日期：</span>{{ item.InvoiceDate || '--' }} </a-col>
              <a-col span="24"> <span class="c999">发票地址：</span>{{ item.InvoiceAddr || '--' }} </a-col>
              <a-col span="24"> <span class="c999">本次勾稽金额：</span>¥{{ item.CurrentVerificationAmount }} </a-col>
            </a-col>
          </a-row>
        </template>
      </a-card>

      <a-card class="mt15" :headStyle="{ padding: '10px 16px' }" title="审核信息">
        <!-- <a-col :span="24" class="mt10">
        <span style="font-size: 16px; font-weight: 600; padding-right: 20px">审核信息</span></a-col
      > -->
        <a-col :span="24" class="mt10">
          <SupAuditInformation v-if="model && model.Id" :bussinessNo="model.Id" />
        </a-col>
      </a-card>
      <!-- 商品明细 -->
      <GoodsDetailListModal ref="goodsDetailListModal" />
      <!-- 发票勾稽 -->
      <InvoiceCheckingModal ref="invoiceCheckingModal" :opType="opType" @ok="invoiceCheckingCallback" />
    </div>
  </a-spin>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'AdvancePaymentInvoiceInfo',
  mixins: [ListMixin, EditMixin],
  components: {},
  props: {
    model: {
      type: Object,
      default: () => {
        return {}
      },
    },
    cLoading: {
      type: Boolean,
      default: () => {
        return false
      },
    },
    /**
     * 是否能编辑
     */
    isEdit: {
      type: Boolean,
      default: () => {
        return false
      },
    },
  },
  data() {
    return {
      tab: {
        tabTitles: ['付款单据列表'], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        bordered: false, //是否显示表格的边框
        rowKey: '',
      },
      total: 0,
      opType: 2,
      // emptyInvoice: {
      //   InvoiceType: '',
      //   InvoiceCode: '',
      //   InvoiceNo: '',
      //   InvoiceAmount: '',
      //   InvoiceAddr: '',
      //   IsVerification: false,
      //   CurrentVerificationAmount: '',
      // },
      faPiaoHint: '',
      httpHead: 'P36009',
      columns: [
        {
          title: '单据编号',
          dataIndex: 'DocumentNo',
          scopedSlots: { customRender: 'component' },
          width: 200,
          ellipsis: true,
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessEventTypeStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '对应订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '发生日期',
          dataIndex: 'OccurrenceDate',
          width: 150,
          scopedSlots: { customRender: 'date' },
          ellipsis: true,
        },
        {
          title: '含税合计金额',
          dataIndex: 'TotalAmount', //'UnInvoiceVerificationAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '审核中未勾稽发票金额',
          dataIndex: 'InvoiceOccupiedAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '剩余未勾稽发票金额',
          dataIndex: 'UnInvoiceVerificationAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ],
      url: {},
    }
  },
  computed: {
    rules() {
      return {
        // SupplierId: [{ required: true, message: '请选择!' }],
        // PaymentBeginTime: [{ required: true, message: '请选择!' }],
        // PaymentMode: [{ required: true, message: '请选择!' }],
        // SettlementType: [{ required: true, message: '请选择!' }],
        // ActualPaymentAmount: [{ required: true, message: '请输入!' }],
      }
    },
  },
  watch: {
    model: {
      handler(newVal, oldVal) {
        this.initModel()
      },
      deep: true,
    },
  },
  created() {},
  mounted() {
    if (this.model.Id) {
      this.initModel()
    }
  },
  methods: {
    initModel() {
      if (!this.model || !this.model.Id) {
        return
      }
      this.dataSource = this.model.RemittedDetails || []
      //发票
      if (this.model.RemittedInvoiceDtos) {
        let IsNotErpSettlementArray = []
        this.model.RemittedInvoiceDtos.forEach((x) => {
          x.InvoiceType = '' + x.InvoiceType
          if (!x.IsErpSettlement) {
            IsNotErpSettlementArray.push(x)
          }
        })
        if (IsNotErpSettlementArray.length > 0) {
          this.faPiaoHint = '发票勾稽的所有商品，系统将仅做发票勾稽'
        } else {
          this.faPiaoHint = '发票勾稽的所有商品，系统将自动结算'
        }
      }

      this.getTotalAmount()

      console.log('detail', this.model)
    },
    /**
     * 增加发票
     */
    // onAddInvoiceClick() {
    //   this.model.RemittedInvoiceDtos.push(JSON.parse(JSON.stringify(this.emptyInvoice)))
    // },

    onActionClick(record, key, index) {
      if (key === '移除') {
        this.dataSource.splice(index, 1)
      } else if (key === '商品明细') {
        this.$refs.goodsDetailListModal.show(record.Id, record.BusinessEventType, false)
      }
    },
    checkForm(callback) {
      let that = this
      this.$refs.form.validate((err, values) => {
        if (err) {
          if (!that.dataSource || that.dataSource.length == 0) {
            that.$message.warning('请先获取单据数据!')
            callback && callback()
            return
          }
          if (that.model.PaymentMode == 2) {
            if (!that.model.RemittedInvoiceDtos || that.model.RemittedInvoiceDtos.length == 0) {
              that.$message.warning('请添加发票数据!')
              callback && callback()
              return
            } else {
              let errList = []
              that.model.RemittedInvoiceDtos.forEach((x, i, arr) => {
                if (!x.CurrentVerificationAmount) {
                  errList.push('发票' + (i + 1))
                }
              })
              if (errList.length > 0) {
                that.$message.warning(errList.toString() + '未勾稽')
                callback && callback()
                return
              }
            }
          }
          callback && callback(that.getData())
        } else {
          callback && callback()
        }
      })
    },
    getData() {
      let data = JSON.parse(JSON.stringify(this.model))

      data.SettlementType = Number(data.SettlementType)
      data.PaymentMode = Number(data.PaymentMode)
      if (data.ActualPaymentAmount) {
        data.ActualPaymentAmount = data.ActualPaymentAmount * 1
      }
      data.RemittedReconciliationDtos = []
      this.$delete(data, 'dzhImages')
      if (this.model.dzhImages) {
        //对账函
        this.model.dzhImages.forEach((x) => {
          data.RemittedReconciliationDtos.push({
            ImageUrl: x,
          })
        })
      }
      data.RemittedInvoiceDtos.forEach((item) => {
        item.InvoiceType = Number(item.InvoiceType)
      })

      data.SavePurchaseRemittedDetails = this.dataSource
      return data
    },
    /**
     * 删除发票
     * @param {*} index
     */
    onRemoveInvoiceClick(index) {
      this.model.RemittedInvoiceDtos.splice(index, 1)
    },
    /**
     * 发票勾稽回调
     * @param {* 当前发票的下标} index
     * @param {* 勾稽金额} amount
     * @param {* 销货对抵金额} offsetAmount
     */
    invoiceCheckingCallback(index, amount, offsetAmount) {
      if (index < 0) {
        return
      }
      this.model.RemittedInvoiceDtos[index].CurrentVerificationAmount = amount
      this.model.RemittedInvoiceDtos[index].IsVerification = true
      //实际付款金额=（发票勾稽金额-销货对抵金额）/实际付款金额=（发票勾稽金额-销货对抵金额）
      let num = amount - offsetAmount
      this.model.RemittedInvoiceDtos[index].SJFKAmount = num > 0 ? this.getAmountOfMoney(num, 6) : 0

      let newList = JSON.parse(JSON.stringify(this.model.RemittedInvoiceDtos))
      this.model.RemittedInvoiceDtos = []
      this.model.RemittedInvoiceDtos = newList
    },
    /**
     * 发票勾稽
     */
    onFPGJClick(item, index) {
      let goujiType = item.goujiType || item.InvoiceReconciliationType || 1
      let isAudit = true
      this.$refs.invoiceCheckingModal.look(item.Id, item.CurrentVerificationAmount, isAudit, goujiType)
    },
    /**
     * 合计应收发票金额
     */
    getTotalAmount() {
      let num = 0
      this.dataSource.forEach((x) => {
        if (x.UnInvoiceVerificationAmount >= 0 || x.UnInvoiceVerificationAmount < 0) {
          num += x.UnInvoiceVerificationAmount * 1
        }
      })
      this.total = this.getAmountOfMoney(num)
    },
  },
}
</script>
<style scoped lang="less">


// /deep/.ant-form-item-control {
//   height: 34px;
// }
/deep/.ant-card-head-title {
  padding: 0 0;
}
</style>
