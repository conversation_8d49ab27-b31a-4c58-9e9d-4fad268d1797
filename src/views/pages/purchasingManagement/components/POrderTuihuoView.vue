<!--
 * @Author: LP
 * @Description: 采购退货详情和编辑
 * @Date: 2023/07/10
-->
<template>
  <a-spin :spinning="loading || confirmLoading || Loading">
    <!-- <a-card :bodyStyle="{ padding: '0 0 16px 0' }"> -->
    <a-form-model ref="form" :rules="rules" :model="model">
      <a-row :gutter="10">
        <a-col span="24">
          <a-form-model-item label="退货原因" prop="ReturnReason" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <!-- <EnumSingleChoiceView
              v-if="isEdit"
              style="width: 100%;"
              placeholder="请选择"
              dictCode="EnumPurchasePaymentMode"
              v-model="model.ReturnReason"
            /> -->
            <!-- <SingleChoiceView -->
            <single-choice-view v-if="isEdit" style="width: 100%" placeholder="请选择" :httpParams="{
                groupPY: 'cgth',
              }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'Id' }" v-model="model.ReturnReason" :Url="'/{v}/Global/GetListDictItem'" @change="(val, txt, item) => (model.ReturnReasonStr = txt)" />
            <span v-else>{{ model.ReturnReasonStr || '--' }}</span>
          </a-form-model-item>
        </a-col>
        <a-col span="24">
          <TableView ref="tableView" :showCardBorder="false" :tab="tab" :columns="columns" :dataList="model.GoodsItems || []" @actionClick="onActionClick">
            <!-- <template slot="bottomView">
              <a-row :gutter="10" class="mt15">
                <a-col span="18">
                  <a-form-model-item label="备注" prop="Remark" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input
                      placeholder="请输入"
                      v-model="model.Remark"
                      @change="onReamrkChange"
                      style="width: 100%"
                      :maxLength="100"
                      v-if="isEdit"
                    />
                    <span v-else>{{ model.Remark || '--' }}</span>
                  </a-form-model-item>
                </a-col>
                <a-col span="6" style="text-align: right; font-weight: bold; padding-top: 8px; padding-right: 20px">
                  退款金额: {{ total < 0 ? '-' : '' }}¥{{ total < 0 ? String(total).replace('-', '') : total }}
                </a-col>
              </a-row>
            </template> -->
          </TableView>
        </a-col>
      </a-row>
      <a-row :gutter="10" class="mt15">
        <a-col span="18">
          <a-form-model-item label="物流方式" prop="LogisticsMethodCode" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <SingleChoiceView 
            style="width: 100%" 
            placeholder="请选择" 
            :httpParams="{ groupPY: 'wulfs'}" 
            httpHead="P36001" 
            :dataKey="{ name: 'ItemValue', value: 'ItemPym' }" 
            v-model="model.LogisticsMethodCode" 
            :Url="url.getListDictItem" 
            @change="(val, txt, item) => (model.LogisticsMethodCode = val,model.LogisticsMethodName = txt)"
            v-if="isEdit"  />
            <span v-else>{{ model.LogisticsMethodName || '--' }}</span>
          </a-form-model-item>
        </a-col>
        <a-col span="18">
          <a-form-model-item label="备注" prop="Remark" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input placeholder="请输入" v-model="model.Remark" @change="onReamrkChange" style="width: 100%" :maxLength="100" v-if="isEdit" />
            <span v-else>{{ model.Remark || '--' }}</span>
          </a-form-model-item>
        </a-col>
        <a-col span="6" style="text-align: right; font-weight: bold; padding-top: 8px; padding-right: 20px">
          退款金额: {{ total < 0 ? '-' : '' }}¥{{ total < 0 ? String(total).replace('-', '') : total }}
        </a-col>
      </a-row>
    </a-form-model>
    <!-- </a-card> -->
  </a-spin>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import SingleChoiceView from '@/components/yyl/components/SingleChoiceView.vue'
export default {
  name: 'POrderTuihuoView',
  mixins: [ListMixin, EditMixin],
  components: { SingleChoiceView },
  props: {
    /**
     * 商品数据
     */
    model: {
      type: Object,
      required: true,
      default: () => {
        return { ReturnReason: '', Remark: '', LogisticsMethodName:'',LogisticsMethodCode:'',TotalRefundAmount: '', GoodsItems: [] }
      },
    },
    /**
     * 类型 1 退货列表-详情 2 退货列表-编辑  3 采购列表新增
     */
    opType: {
      type: Number,
      default: () => {
        return 1
      },
    },
    Loading: {
      type: Boolean,
      default: () => {
        return false
      },
    },
  },
  watch: {
    model: {
      handler(newVal, oldVal) {
        this.getTotalAmount()
        this.parseListData()
      },
      deep: true,
    },
  },
  data() {
    return {
      loading: false,
      confirmLoading: false,
      labelCol: {
        xs: { span: 2 },
        sm: { span: 2 },
      },
      wrapperCol: {
        xs: { span: 22 },
        sm: { span: 22 },
      },
      tab: {
        tabTitles: [],
        // bordered: true,
        showPagination: false,
      },
      isEdit: false,
      total: 0,
      inputChangeing: false,
      refreshRemarks: true,
      httpHead: 'P36009',
      bcthslKey: 'RefundCount',
      thdjKey: 'LastRefundPrice',
      CaixiaoPCCostKey: 'LastCaixiaoPCCost',
      url: {
        getListDictItem: '/{v}/Global/GetListDictItem',//字典表接口
      },
    }
  },

  computed: {
    rules() {
      return {
        ReturnReason: [{ required: this.isEdit, message: '请选择!' }],
        Remark: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (value) {
                this.validStrMaxLength(rule, value, callback, 100)
              } else {
                callback()
              }
            },
          },
        ],
        TotalRefundAmount: [{ required: false, message: '请输入!' }],
        LogisticsMethodCode: [{ required: this.isEdit, message: '请选择!' }],
      }
    },

    columns() {
      const actionList = this.isEdit
        ? [
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 80,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
            actionBtns: [
              {
                name: '移除',
                id: '8538860b-dcac-4065-8e26-6f880287a4aa',
              },
            ],
          },
        ]
        : []

      return [
        // {
        //   title: 'mergeKey',
        //   dataIndex: 'mergeKey',
        //   ellipsis: true,
        //   fixed: this.isEdit ? 'left' : '',
        //   width: 300,
        //   scopedSlots: { customRender: 'component' },
        // },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          fixed: this.isEdit ? 'left' : '',
          width: 150,
          customRender: this.customRowAndColRender,
          // scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 100,
          ellipsis: true,
          customRender: this.customRowAndColRender,
          // scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 80,
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
          width: 200,
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
          width: 100,
        },
        {
          title: '批次号',
          dataIndex: 'BatchNumber',
          width: 150,
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
        },
        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 100,
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
        },
        {
          title: '有效期',
          dataIndex: this.opType == 3 ? 'ValidityPeriod' : 'ExpirationDate',
          width: 120,
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
        },
        // fixed: this.isEdit ? 'right' : '',
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          width: 120,
          precision: 6,
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => this.customRowAndColRender(text, record, index, 'price'),
          ellipsis: true,
        },
        // fixed: this.isEdit ? 'right' : '',
        {
          title: '入库数量',
          dataIndex: 'InboundQuantity',
          width: 100,
          // scopedSlots: { customRender: 'number' },
          customRender: (text, record, index) => this.customRowAndColRender(text, record, index, 'number'),
          ellipsis: true,
        },
        {
          title: '仓库名称',
          dataIndex: 'WarehouseName',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        // {
        //   title: '仓库编码',
        //   dataIndex: 'WarehouseCode',
        //   width: 120,
        //   scopedSlots: { customRender: 'component' },
        //   ellipsis: true,
        // },
        // {
        //   title: '类型',
        //   dataIndex: 'DataTypeStr',
        //   width: 100,
        //   scopedSlots: { customRender: 'component' },
        //   ellipsis: true,
        // },

        // {
        //   title: '采销批次在库成本',
        //   dataIndex: this.CaixiaoPCCostKey || 'LastCaixiaoPCCost',
        //   width: 150,
        //   precision: 6, //数值精度
        //   fixed: this.isEdit ? 'right' : '',
        //   scopedSlots: { customRender: 'price' },
        //   ellipsis: true,
        // },
        {
          title: 'erp批次在库成本',
          dataIndex: this.CaixiaoPCCostKey || 'LastErpPCCost',
          width: 150,
          precision: 6, //数值精度
          fixed: this.isEdit ? 'right' : '',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '已退货数量',
          dataIndex: 'GoodsExistsRefundCount',
          width: 100,
          fixed: this.isEdit ? 'right' : '',
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        {
          title: '可退货数量',
          dataIndex: 'CanRefundCount',
          width: 100,
          fixed: this.isEdit ? 'right' : '',
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        {
          title: '本次退货数量',
          dataIndex: this.bcthslKey || 'RefundCount',
          width: 150,
          precision: 0, //数值精度
          onInputChange: this.onInputChange,
          scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'number' },
          fixed: this.isEdit ? 'right' : '',
          ellipsis: true,
          isDisabled: this.IsDisabled,
        },
        {
          title: '退货单价',
          dataIndex: this.thdjKey || 'LastRefundPrice',
          width: 100,
          precision: 6, //数值精度
          onInputChange: this.onInputChange,
          scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'price' },
          fixed: this.isEdit ? 'right' : '',
          ellipsis: true,
          disabled: true,
        },
        ...actionList,
      ]
    },
  },
  mounted() {
    this.isEdit = this.opType == 2 || this.opType == 3
    this.thdjKey = this.isEdit ? 'LastRefundPrice' : 'RefundPrice'
    this.CaixiaoPCCostKey = this.isEdit ? 'LastErpPCCost' : 'ErpPCCost'
    console.log('isEdit = ' + this.isEdit)
  },
  created() { },
  methods: {
    IsDisabled(text, record, index, column) {
      return !record.WarehouseCode
    },
    getData(callback) {
      if (this.model.GoodsItems.length == 0) {
        this.$message.warning('无商品数据，无须提交')
        callback && callback()
        return
      }

      this.$refs.form.validate((err, values) => {
        if (err) {
          let inputedList = this.model.GoodsItems.filter((x) => {
            return x[this.bcthslKey]
          })
          if (inputedList && inputedList.length == 0) {
            this.$message.warning('请至少填写一个商品的本次退货数量！')
            callback && callback()
          } else {
            this.model.TotalRefundAmount = this.total
            let obj = JSON.parse(JSON.stringify(this.model))
            obj.GoodsItems = inputedList
            callback && callback(obj)
          }
        } else {
          callback && callback()
        }
      })
    },
    onActionClick(record, key, index) {
      if (key == '移除') {
        this.model.GoodsItems.splice(index, 1)
      }
    },
    /**
     * 获取总金额 = 含税进行*本次退货数量；
     */
    getTotalAmount() {
      let sum = 0
      if (this.model.GoodsItems) {
        this.model.GoodsItems.forEach((x) => {
          // 退货数量*退货单价计算
          if ((x[this.thdjKey] >= 0 || x[this.thdjKey] < 0) && x[this.bcthslKey] >= 0) {
            sum += x[this.thdjKey] * x[this.bcthslKey]
          }
          // if ((x.TaxIncludedPurchasePrice >= 0 || x.TaxIncludedPurchasePrice < 0) && x[this.bcthslKey] >= 0) {
          //   sum += x.TaxIncludedPurchasePrice * x[this.bcthslKey]
          // }
        })
      }
      this.total = this.getAmountOfMoney(sum)
    },
    /**
     * 退货金额=含税进行*本次退货数量
     * @param {*} val
     * @param {*} key
     * @param {*} record
     * @param {*} index
     */
    onInputChange(val, key, record, index) {
      if (val >= 0 && val > record.InboundQuantity) {
        //CanRefundCount
        if (this.inputChangeing) {
          record[this.bcthslKey] = record.CanRefundCount
          return
        }
        this.inputChangeing = true
        this.$message.warning('退货数量不能大于入库数量!')
        record[this.bcthslKey] = record.CanRefundCount
        this.$forceUpdate()
        return
      }
      // // 退货单价默认为采销批次在库成本，不能修改
      // record[this.thdjKey] = record.LastCaixiaoPCCost
      this.$forceUpdate()
      //计算调价金额
      this.getTotalAmount()
      this.inputChangeing = false
    },

    onReamrkChange() {
      // if (this.getStrMaxLength(this.model.Remark) > 100) {
      //   console.log('NBA', this.model.Remark.substring(0, 99))
      //   this.model.Remark = '' //
      //   this.$message.warning('备注最多100个字符')
      //   this.refreshRemarks = false
      //   this.$nextTick(() => {
      //     this.refreshRemarks = true
      //   })
      // }
      this.$forceUpdate()
    },
    /**
     * 自定义行和列渲染
     * @param {*} text
     * @param {*} record
     * @param {*} index
     * @param {* 数据类型 number price等，主要特殊处理price显示要加￥符号} dataType
     */
    customRowAndColRender(text, record, index, dataType) {
      let newText = text
      if (dataType == 'price') {
        newText =
          text == undefined || text == null
            ? '--'
            : (text < 0 ? '-' : '') + '￥' + Number(text < 0 ? String(text).replace('-', '') : text).toFixed(6)
      }
      const obj = {
        children: newText,
        attrs: {},
      }

      obj.attrs.rowSpan = record.rowSpan >= 0 ? record.rowSpan : 1
      obj.attrs.colSpan = 1
      return obj
    },
    /**
     * 解析列表数据目的设置正确的rowSpan
     */
    parseListData() {

      if (this.model.GoodsItems && this.model.GoodsItems.length > 1) {
        //把三个字段组成一个字段赋值，后面处理数据使用，作为key
        let list = this.model.GoodsItems
        list.forEach((x) => {
          x['mergeKey'] = x.ErpGoodsCode + '-' + x.BatchNumber + '-' + x.ProductionBatchNumber
          if (this.isEdit) {
            // 退货单价默认为采销批次在库成本，不能修改
            x['LastRefundPrice'] = x[this.CaixiaoPCCostKey]
            x['RefundPrice'] = x.LastRefundPrice
            x['ErpPCCost'] = x.LastErpPCCost
          }
        })
        let keyList = list.map((item) => {
          return item['mergeKey']
        })
        console.log('解析listData keyList', keyList)
        //找到相同的mergeKey的数据
        let sameKeyList = keyList.filter((val, i) => keyList.indexOf(val) !== i)
        console.log('解析listData sameKeyList', sameKeyList)

        //方案一 缺点需要唯一key作为判断依据
        // let sameList = []
        // sameKeyList.forEach((x) => {
        //   let filterList = list.filter((item) => item.mergeKey == x)
        //   if (filterList && filterList.length > 0) {
        //     sameList.push({
        //       key: x,
        //       list: filterList,
        //     })
        //   }
        // })
        // console.log('解析listData sameList', sameList)
        // list.forEach((old, oldIndex) => {
        //   sameList.forEach((same, sameIndex) => {
        //     if (same.key == old.mergeKey) {
        //       //注意这里需要不重复的属性去判断，如Id
        //       if (same.list[0].Id == old.Id) {
        //         old.rowSpan = same.list.length
        //       }
        //     }
        //   })
        // })

        //方案二 优点 不需要唯一key作为判断依据
        let sameList = []
        sameKeyList.forEach((x) => {
          sameList.push({
            key: x,
            length: keyList.filter((y) => y === x).length,
            index: keyList.indexOf(x), //相同元素首次出现的位置
          })
        })
        console.log('解析listData sameList', sameList)
        list.forEach((old, oldIndex) => {
          sameList.forEach((same, sameIndex) => {
            if (old.mergeKey == same.key) {
              if (oldIndex == same.index) {
                old.rowSpan = same.length
              } else {
                old.rowSpan = 0
              }
            }
          })
        })

        console.log('解析list ', list)
      } else {
        if ((this.model.GoodsItems || []).length && this.isEdit) {
          this.model.GoodsItems.map(record => {
            // 退货单价默认为采销批次在库成本，不能修改
            record['LastRefundPrice'] = record[this.CaixiaoPCCostKey]
            record['RefundPrice'] = record.LastRefundPrice
            record['ErpPCCost'] = record.LastErpPCCost
          })
        }
      }
    },
  },
}
</script>

<style lang="less" scoped>

.mb20 {
  margin-bottom: 30px;
}
</style>
