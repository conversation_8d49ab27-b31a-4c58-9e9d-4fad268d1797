<!--
 * @Author: LP
 * @Description: 申请付款和上传发票 组件
 * @Date: 2023/07/13
-->

<template>
  <a-spin :spinning="loading || cLoading">
    <div>
      <a-alert
        :message="'驳回原因：' + (model.RejectReason || '--')"
        type="warning"
        show-icon
        v-if="isEdit && model.RejectReason"
        class="mb10"
      />
      <a-form-model ref="form" :rules="rules" :model="model">
        <a-card title="选择付款单据范围" :headStyle="{ padding: '10px 16px' }" v-if="!(opType == 1 && !isEdit)">
          <a-row :gutter="gutter" v-if="isEdit">
            <!-- 企业信息 -->
            <a-col :span="7">
              <a-form-model-item label="供应商名称" prop="SupplierId" :label-col="labelCol" :wrapper-col="wrapperCol">
                <SingleChoiceSearchView
                  style="width: 100%"
                  placeholder="请选择"
                  :httpParams="{ AuditStatus: 2, AuthBusiness: 2 }"
                  httpHead="P36003"
                  :keyWord="'KeyWord'"
                  Url="/{v}/Supplier/GetSuppliersForSelect"
                  :dataKey="{ name: 'Name', value: 'Id' }"
                  v-model="model.SupplierId"
                  :name="model.SupplierName"
                  @change="(val, txt, item) => changeGys(val, txt, item)"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="7">
              <!--付款方式  1预付款 2月结 3货到付款 -->
              <a-form-model-item label="付款方式" prop="PaymentMode" :label-col="labelCol" :wrapper-col="wrapperCol">
                <EnumSingleChoiceView
                  style="width: 100%"
                  placeholder="请选择"
                  dictCode="EnumPurchasePaymentMode"
                  v-model="model.PaymentMode"
                  @change="onPaymentModeChange"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="7">
              <a-form-model-item label="日期" prop="PaymentBeginTime" :label-col="labelCol" :wrapper-col="wrapperCol">
                <a-range-picker
                  @change="
                    (dates, dateStrings) => onTimeChange(dates, dateStrings, 'PaymentBeginTime', 'PaymentEndTime', true)
                  "
                  v-model="rangeDate"
                  style="width: 100%"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="3" style="padding-top: 5px">
              <YYLButton
                menuId="6a5515f4-e516-40b0-9916-c7fca96f4b32"
                text="获取数据"
                type="primary"
                @click="onGetDataClick"
              />
            </a-col>
          </a-row>
          <a-descriptions v-else>
            <a-descriptions-item label="供应商名称">{{ model.SupplierName || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="付款方式">{{ model.PaymentModeStr || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="日期">{{
              moment(model.PaymentBeginTime).format('YYYY-MM-DD') +
              ' - ' +
              moment(model.PaymentEndTime).format('YYYY-MM-DD')
            }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
        <a-card :bordered="false" v-if="model.SupplierId">
          <TableView class="mt15" ref="tableViewA" :tab="tabA" :columns="columnsA" :dataList="dataSourceA" />
        </a-card>
        <a-form-model-item prop="TableView">
          <TableView
            class="mt15"
            ref="tableView"
            showCardBorder
            :tab="tab"
            :columns="columns"
            :dataList="dataSource"
            @operateClick="onOperateClick"
            @actionClick="onActionClick"
          >
            <template slot="bottomView" v-if="opType == 2">
              <div style="text-align: right; margin: 10px 20px">
                合计应收剩余发票金额：<span style="color: red"
                  >{{ total < 0 ? '-' : '' }}¥{{ total < 0 ? String(total).replace('-', '') : total }}</span
                >
              </div>
            </template>
            <div
              v-if="opType == 1"
              style="display: flex; align-items: center; justify-content: flex-end"
              slot="bottomView"
            >
              <div style="text-align: right; margin-top: 8px; margin-right: 15px">
                剩余未付款金额合计：<span style="color: red"
                  >¥{{ totalUnpaidAmounts ? Number(totalUnpaidAmounts).toFixed(2) : '0.00' }}</span
                >
              </div>
              <div v-if="[2, 3].includes(+model.PaymentMode)" style="text-align: right; margin-top: 8px">
                已勾稽未付款金额合计：<span style="color: red"
                  >¥{{ gojiTotalUnpaidAmounts ? Number(gojiTotalUnpaidAmounts).toFixed(2) : '0.00' }}</span
                >
              </div>
            </div>
          </TableView>
        </a-form-model-item>

        <!--  付款信息 采购付款展示  -->
        <a-card
          title="付款信息"
          class="mt15"
          :headStyle="{ padding: '10px 16px' }"
          v-if="opType == 1 && dataSource.length > 0"
        >
          <a-row :gutter="gutter">
            <template v-if="isEdit">
              <a-col :span="24" v-if="model.PaymentMode == 1">
                <a-col :span="8">
                  <a-form-model-item
                    label="总付款金额"
                    prop="TotalPaymentAmount"
                    :label-col="labelCol"
                    :wrapper-col="wrapperCol"
                  >
                    <a-input-number
                      placeholder="请输入"
                      v-model="model.TotalPaymentAmount"
                      @change="(e) => inputPayMoney(e, 1)"
                      style="width: 100%"
                      :min="0"
                      :max="totalUnpaidAmounts > 0 ? totalUnpaidAmounts : 0"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    label="预付余额抵扣"
                    prop="PrepayDeductAmount"
                    :label-col="labelCol"
                    :wrapper-col="wrapperCol"
                  >
                    <a-input-number
                      placeholder="请输入"
                      v-model="model.PrepayDeductAmount"
                      @change="(e) => inputPayMoney(e, 2)"
                      :min="0"
                      :max="
                        Number(totalUnpaidAmounts || 0) > Number(model.PrepayBalance || 0)
                          ? Number(model.PrepayBalance || 0)
                          : Number(totalUnpaidAmounts < 0 ? 0 : totalUnpaidAmounts) || 0
                      "
                      style="width: 100%"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    label="可用预付余额"
                    prop="PrepayBalance"
                    :label-col="labelCol"
                    :wrapper-col="wrapperCol"
                  >
                    <span>￥{{ model.PrepayBalance ? Number(model.PrepayBalance).toFixed(2) : '0.00' }}</span>
                  </a-form-model-item>
                </a-col>
              </a-col>
              <a-col :span="24">
                <a-col :span="8">
                  <a-form-model-item
                    label="实际付款金额"
                    prop="ActualPaymentAmount"
                    :label-col="labelCol"
                    :wrapper-col="wrapperCol"
                  >
                    <a-input
                      :placeholder="[2, 3].includes(+model.PaymentMode) ? '付款勾稽后显示' : '计算而来'"
                      v-model="model.ActualPaymentAmount"
                      :key="model.ActualPaymentAmount"
                      style="width: 100%"
                      :maxLength="30"
                      :disabled="true"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item
                    label="结算方式"
                    prop="SettlementType"
                    :label-col="labelCol"
                    :wrapper-col="wrapperCol"
                  >
                    <EnumSingleChoiceView
                      style="width: 100%"
                      placeholder="请选择"
                      dictCode="EnumPurchaseSettlementType"
                      v-model="model.SettlementType"
                      tagType="radio"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item label="备注" prop="Remarks" :label-col="labelCol" :wrapper-col="wrapperCol">
                    <a-input placeholder="请输入" v-model="model.Remarks" style="width: 100%" :maxLength="100" />
                  </a-form-model-item>
                </a-col>
              </a-col>
            </template>
            <div v-else>
              <a-descriptions>
                <a-descriptions-item label="总付款金额">¥{{ model.TotalPaymentAmount }}</a-descriptions-item>
                <a-descriptions-item label="预付余额抵扣">{{ model.PrepayDeductAmount || 0 }}</a-descriptions-item>
              </a-descriptions>
              <a-descriptions>
                <a-descriptions-item label="实际付款金额">¥{{ model.ActualPaymentAmount }}</a-descriptions-item>
                <a-descriptions-item label="结算方式">{{ model.SettlementTypeStr || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="备注">{{ model.Remarks || ' -- ' }}</a-descriptions-item>
              </a-descriptions>
            </div>
          </a-row>
          <a-row :gutter="gutter">
            <a-col :span="12">
              <a-form-model-item label="合同图片" prop="htImages" >
                <template v-if="isEdit">
                  <MultiUpload
                    v-if="model.htImages && model.htImages.length > 0"
                    :max="50"
                    :chooseFileType="model.htImages.length > 0 ? false : true"
                    :images="model.htImages"
                    :bName="BName"
                    :dir="Dir"
                    :add="false"
                    :fileType="4"
                    :readOnly="false"
                    @change="changeHetong"
                  />
                </template>
                <template v-else>
                  <ImageControl
                    v-for="(img, index) in model.htImages || []"
                    :key="index"
                    :src="img || ''"
                    :width="120"
                    :height="120"
                    style="float: left; margin-right: 5px"
                  />
                </template>

                <template v-if="isEdit">
                  <a-button
                    type="primary"
                    v-if="htDataList.length > 0"
                    style="margin-right: 10px"
                    @click="$refs.htListModal.show(htDataList)"
                    >选择合同</a-button
                  >

                  <a-button
                    type="primary"
                    @click="
                      $refs.purchaseContractAddModal.add(
                        model.SupplierId,
                        model.SupplierName,
                        model.PaymentMode * 1,
                        model.htImages
                      )
                    "
                    >去新增合同</a-button
                  >
                </template>
              </a-form-model-item>
            </a-col>
            <a-col :span="12" v-if="[2, 3].includes(+model.PaymentMode)">
              <a-form-model-item label="对账函图片" prop="dzhImages" :rules="{
                required: isEdit && model.PaymentMode !== '3' ? true : false,
                validator: (rule, value, callback) => {
                  if (model.PaymentMode !== '3' && (!model.dzhImages || model.dzhImages.length == 0)) {
                    callback('请上传!')
                  } else {
                    callback()
                  }
                }
              }">
                <MultiUpload
                  v-if="isEdit"
                  :max="10"
                  :images="model.dzhImages"
                  :bName="BName"
                  :dir="Dir"
                  :add="isEdit"
                  :fileType="4"
                  :readOnly="!isEdit"
                  @change="
                    (images) => {
                      model.dzhImages = images
                      $forceUpdate()
                    }
                  "
                />
                <template v-else>
                  <ImageControl
                    v-for="(img, index) in model.dzhImages || []"
                    :key="index"
                    :src="img || ''"
                    :width="120"
                    :height="120"
                    style="float: left; margin-right: 5px"
                  />
                </template>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-card>

        <a-card
          title="发票信息"
          class="mt15"
          :headStyle="{ padding: '10px 16px' }"
          v-if="opType == 1 && [2, 3].includes(+model.PaymentMode)"
        >
          <a-form-model-item label="本次付款包含的发票号" prop="RemittedInvoiceNoStr">
            <MultipleChoiceSearchView
              ref="remittedInvoiceNoView"
              style="width: 100%"
              :disabled="isEdit ? false : true"
              placeholder="请选择"
              :httpParams="{ pageIndex: 1, pageSize: 200, IsValid: true }"
              keyWord="InvoiceNo"
              httpType="POST"
              httpHead="P36009"
              :Url="url.invoiceNo"
              :dataKey="{ name: 'InvoiceNo', value: 'InvoiceNo' }"
              v-model="model.RemittedInvoiceNo"
              @change="changeInvoice"
            />
          </a-form-model-item>
          <!-- @dataLoaded="onInvoiceNoLoaded" -->
        </a-card>

        <!-- 发票信息 -->
        <a-card
          class="mt15"
          :headStyle="{ padding: '10px 16px' }"
          v-if="opType == 2 && dataSource && dataSource.length > 0"
        >
          <template slot="title">
            <span style="padding-right: 8px">发票信息</span>
            <YYLButton
              menuId="6fb513c8-e948-4a24-90de-970baaa30f16"
              text="增加发票"
              type="primary"
              @click="onAddInvoiceClick"
              v-if="isEdit"
            />
          </template>
          <a-row :gutter="gutter" v-if="isEdit">
            <a-col :span="8" v-for="(item, index) in model.RemittedInvoiceDtos || []" :key="index">
              <a-col :span="24">
                <span style="font-weight: bold; padding-right: 20px">发票{{ index + 1 }}</span>
                <YYLButton
                  menuId="7c745f0a-b151-426b-90db-ec8881adedac"
                  :text="isEdit ? '发票勾稽（' + (item.IsVerification ? '已' : '未') + '勾稽）' : '查看发票勾稽'"
                  type="primary"
                  size="small"
                  @click="onFPGJClick(item, index)"
                />
                <a-button type="danger" size="small" @click="onRemoveInvoiceClick(item, index)" v-if="index > 0"
                  >删除</a-button
                >
              </a-col>
              <a-col :span="24">
                <a-form-model-item
                  label="发票类型"
                  prop="InvoiceType"
                  :key="'InvoiceType' + index"
                  :rules="{
                    required: true,
                    validator: (rule, value, callback) => {
                      if (!model.RemittedInvoiceDtos[index].InvoiceType) {
                        callback('请选择')
                      } else {
                        callback()
                      }
                    },
                  }"
                >
                  <EnumSingleChoiceView
                    style="width: 100%"
                    placeholder="请选择"
                    dictCode="EnumSalesOrderInvoiceType"
                    v-model="item.InvoiceType"
                    tagType="radio"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item
                  label="发票代码"
                  prop="InvoiceCode"
                  :key="'InvoiceCode' + index"
                  :rules="{
                    required: true,
                    validator: (rule, value, callback) => {
                      if (!model.RemittedInvoiceDtos[index].InvoiceCode) {
                        callback('请输入')
                      } else {
                        callback()
                      }
                    },
                  }"
                >
                  <div style="display: flex">
                    <a-input
                      placeholder="请输入"
                      v-model="item.InvoiceCode"
                      style="width: 100%"
                      :disabled="item.IsVerification ? true : false"
                      @change="onInvoiceCodeChange($event, index, 'InvoiceCode')"
                      @blur="onInvoiceCodeBlur($event, index, item, 'InvoiceCode')"
                      :maxLength="30"
                    />
                    <a-button
                      style="margin-left: 3%"
                      v-if="item.IsVerification"
                      @click="updateInvoice('发票代码', 'InvoiceCode', index)"
                      >修改</a-button
                    >
                  </div>
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item
                  label="发票号"
                  prop="InvoiceNo"
                  :key="'InvoiceNo' + index"
                  :rules="{
                    required: true,
                    validator: (rule, value, callback) => {
                      if (!model.RemittedInvoiceDtos[index].InvoiceNo) {
                        callback('请输入')
                      } else if (
                        model.RemittedInvoiceDtos[index].InvoiceNo &&
                        !isValidLength(model.RemittedInvoiceDtos[index].InvoiceNo)
                      ) {
                        // 计算字符串的长度 发票号只能输入8位、9位、20位、21位
                        callback('发票号只能输入8位、9位、20位、21位')
                      } else {
                        callback()
                      }
                    },
                  }"
                >
                  <div style="display: flex">
                    <a-input
                      placeholder="请输入"
                      v-model="item.InvoiceNo"
                      style="width: 100%"
                      :disabled="item.IsVerification ? true : false"
                      @change="onInvoiceCodeChange($event, index, 'InvoiceNo')"
                      @blur="onInvoiceCodeBlur($event, index, item, 'InvoiceNo')"
                      :maxLength="100"
                    />
                    <a-button
                      style="margin-left: 3%"
                      v-if="item.IsVerification"
                      @click="updateInvoice('发票号', 'InvoiceNo', index)"
                      >修改</a-button
                    >
                  </div>
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item
                  label="税率"
                  prop="InvoiceTaxRate"
                  :key="'InvoiceTaxRate' + index"
                  :rules="{
                    required: true,
                    validator: (rule, value, callback) => {
                      if (
                        model.RemittedInvoiceDtos[index].InvoiceTaxRate == null ||
                        model.RemittedInvoiceDtos[index].InvoiceTaxRate == ''
                      ) {
                        callback('请选择')
                      } else {
                        callback()
                      }
                    },
                  }"
                >
                  <div style="display: flex">
                    <SingleChoiceView
                      style="width: 100%"
                      placeholder="请选择"
                      :httpParams="{
                        groupPY: 'cgshlv',
                      }"
                      httpHead="P36001"
                      :dataKey="{ name: 'ItemValue', value: 'ItemPym' }"
                      v-model="item.InvoiceTaxRate"
                      :Url="'/{v}/Global/GetListDictItem'"
                    />
                  </div>
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item
                  label="发票金额"
                  prop="InvoiceAmount"
                  :key="'InvoiceAmount' + index"
                  :rules="{
                    required: true,
                    validator: (rule, value, callback) => {
                      if (!model.RemittedInvoiceDtos[index].InvoiceAmount) {
                        callback('请输入')
                      } else {
                        callback()
                      }
                    },
                  }"
                >
                  <a-input-number
                    placeholder="请输入"
                    v-model="item.InvoiceAmount"
                    style="width: 100%"
                    :precision="6"
                    :maxLength="30"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item
                  label="发票日期"
                  prop="InvoiceDate"
                  :key="'InvoiceDate' + index"
                  :rules="{
                    required: true,
                    validator: (rule, value, callback) => {
                      if (!model.RemittedInvoiceDtos[index].InvoiceDate) {
                        callback('请选择')
                      } else {
                        callback()
                      }
                    },
                  }"
                >
                  <a-date-picker v-model="item.InvoiceDate" style="width: 100%" placeholder="请选择" />
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item
                  label="发票地址"
                  prop="InvoiceAddr"
                  :key="'InvoiceAddr' + index"
                  :rules="{
                    required: false,
                    validator: (rule, value, callback) => {
                      if (model.RemittedInvoiceDtos[index].InvoiceAddr) {
                        validStrMaxLength(rule, model.RemittedInvoiceDtos[index].InvoiceAddr, callback, 100)
                      } else {
                        callback()
                      }
                    },
                  }"
                >
                  <a-input placeholder="请输入" v-model="item.InvoiceAddr" style="width: 100%" :maxLength="100" />
                </a-form-model-item>
              </a-col>
              <a-col :span="24">
                <a-form-model-item label="本次勾稽金额">
                  <a-input
                    placeholder="请输入"
                    v-model="item.CurrentVerificationAmount"
                    style="width: 100%"
                    :maxLength="100"
                    :disabled="true"
                  />
                </a-form-model-item>
              </a-col>
            </a-col>
          </a-row>
          <a-row v-else>
            <a-col :span="8" v-for="(item, index) in model.RemittedInvoiceDtos || []" :key="index">
              <a-col span="24" style="font-weight: bold"
                >发票{{ index + 1 }}
                <YYLButton :text="'查看发票勾稽'" type="primary" size="small" @click="onFPGJClick(item, index)" />
              </a-col>
              <a-col span="24"> <span class="c999">发票类型：</span>{{ item.InvoiceTypeStr || '--' }} </a-col>
              <a-col span="24"> <span class="c999">发票代码：</span>{{ item.InvoiceCode || '--' }} </a-col>
              <a-col span="24"> <span class="c999">发票号：</span>{{ item.InvoiceNo || '--' }} </a-col>
              <a-col span="24">
                <span class="c999">税率：</span
                >{{ item.InvoiceTaxRate != undefined && item.InvoiceTaxRate >= 0 ? item.InvoiceTaxRate : '--' }}
              </a-col>
              <a-col span="24">
                <span class="c999">发票金额：</span>¥{{ item.InvoiceAmount ? item.InvoiceAmount.toFixed(2) : '--' }}
              </a-col>
              <a-col span="24"> <span class="c999">发票日期：</span>{{ item.InvoiceDate || '--' }} </a-col>
              <a-col span="24"> <span class="c999">发票地址：</span>{{ item.InvoiceAddr || '--' }} </a-col>
              <a-col span="24">
                <span class="c999">本次勾稽金额：</span>¥{{
                  item.CurrentVerificationAmount ? item.CurrentVerificationAmount.toFixed(2) : '--'
                }}
              </a-col>
            </a-col>
          </a-row>
        </a-card>
      </a-form-model>
      <!-- 修改发票的弹框 -->
      <a-modal
        title="发票信息修改"
        :width="450"
        :visible="invoicevisible"
        @ok="invoiceHandleOk"
        @cancel="invoicevisible = false"
      >
        <a-form-model ref="form1" :model="model" layout="inline">
          <a-row :gutter="gutter">
            <a-col :md="24">
              <a-form-model-item label="发票：" required>
                <a-input placeholder="请输入" v-model="invoiceVal" style="width: 300px" :maxLength="50" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-modal>
      <!-- 商品明细 -->
      <GoodsDetailListModal ref="goodsDetailListModal" />
      <!-- 发票勾稽 -->
      <InvoiceCheckingModal ref="invoiceCheckingModal" :opType="opType" @ok="invoiceCheckingCallback" />
      <!-- 新增合同 -->
      <PurchaseContractAddModal ref="purchaseContractAddModal" @ok="addHTCallback" />
      <!-- 合同列表弹窗 -->
      <HTListModal ref="htListModal" @ok="selectHTCallback" />
    </div>
  </a-spin>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, postAction } from '@/api/manage'
import moment from 'moment'
export default {
  name: 'ApplyForPayment',
  mixins: [ListMixin, EditMixin],
  components: {},
  props: {
    /**
     * 类型 1 申请付款 2 上传发票
     */
    opType: {
      type: Number,
      default: 1,
    },
    id: {
      type: String || Number,
      default: '',
    },
    orderNo: {
      type: String || Number,
      default: '',
    },
    cLoading: {
      type: Boolean,
      default: () => {
        return false
      },
    },
    /**
     * 是否能编辑
     */
    isEdit: {
      type: Boolean,
      default: () => {
        return false
      },
    },
  },
  data() {
    return {
      loading: false,
      BName: window._CONFIG.AliOssConfig['BaseBucketName'],
      Dir: window._CONFIG.AliOssConfig['CommonDir'],
      labelCol: {
        xs: { span: 6 },
        sm: { span: 9 },
      },
      wrapperCol: {
        xs: { span: 18 },
        sm: { span: 15 },
      },
      tabA: {
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        bordered: false, //是否显示表格的边框
        showPagination: false,
        rowKey: 'Id',
      },
      tab: {
        tabTitles: ['付款单据列表'], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        bordered: false, //是否显示表格的边框
        rowKey: 'DocumentId',
      },
      total: 0,
      model: {
        TemporaryId: '',
        RemittedInvoiceDtos: [],
        RemittedContractDtos: [],
        htImages: [],
        dzhImages: [],
        PaymentMode: '', //1预付款 2月结
        SettlementType: '',
        ActualPaymentAmount: '',
        TotalPaymentAmount: null, //总付款金额
        PrepayDeductAmount: null, //预付余额抵扣
        PrepayBalance: 0, //可用预付余额
      },
      emptyInvoice: {
        InvoiceType: '',
        InvoiceCode: '',
        InvoiceNo: '',
        InvoiceTaxRate: '',
        InvoiceAmount: '',
        InvoiceAddr: '',
        IsVerification: false,
        CurrentVerificationAmount: '',
      },
      gutter: 10,
      paytype: null, //1发票勾稽 2付款勾稽
      OriginalInvoiceInfo: null,
      isOnGetDataClick: false, //是否点击获取数据
      htDataList: [],
      httpHead: 'P36009',
      baseColumns: [
        {
          title: '单据编号',
          dataIndex: 'DocumentNo',
          scopedSlots: { customRender: 'component' },
          width: 200,
          ellipsis: true,
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessEventTypeStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '对应订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '发生日期',
          dataIndex: 'OccurrenceDate',
          width: 150,
          scopedSlots: { customRender: 'date' },
          ellipsis: true,
        },
        {
          title: '总金额',
          dataIndex: 'TotalAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '已付款金额',
          dataIndex: 'SettledAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ],
      removeAction: {
        name: '移除',
        id: 'bce901be-f52e-4d2b-9f3e-6bf1b2c889ae',
        isShow: (record) => {
          return true
        },
      },
      detailAction: {
        name: '商品明细',
        id: '1ac5f090-7201-47e7-b5e7-bf6ca0eb4f5a',
        isShow: (record) => {
          return true
        },
      },
      actionColumns: [
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [],
        },
      ],
      columns1: [
        {
          title: '未付款金额',
          dataIndex: 'UnPaidAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '审核中未付款金额',
          dataIndex: 'PaymentOccupiedAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '剩余未付款金额',
          dataIndex: 'UnsettledAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ],
      columns2: [
        {
          title: '含税合计金额',
          dataIndex: 'TotalAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '剩余未结算金额',
          dataIndex: 'ErpUnSettlementAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '未勾稽发票金额',
          dataIndex: 'UncheckedInvoiceAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '审核中未勾稽发票金额',
          dataIndex: 'InvoiceOccupiedAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '剩余未勾稽发票金额',
          dataIndex: 'UnInvoiceVerificationAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ],
      DocumentIdArray: [],
      gojiTotalUnpaidAmounts: 0, //已勾稽未付款金额合计
      invoicevisible: false,
      invoiceIndex: null,
      invoiceType: '',
      invoiceVal: '',
      columns: [],
      dataSourceA: [],
      columnsA: [
        {
          title: '应付余额',
          dataIndex: 'PayableBalance',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '预付余额',
          dataIndex: 'PrepaymentBalance',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '应收余额',
          dataIndex: 'ReceivableBalance',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '预收余额',
          dataIndex: 'AdvancePaymentBalance',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ],
      url: {
        detail1: '/{v}/PurchaseRemittedOrder/GetPurchaseRemittedInfo',
        detail2: '/{v}/PurchaseRemittedOrder/GetPurchaseSubsistInvoiceInfo',
        detailNo: '/{v}/PurchaseRemittedOrder/GetPurchaseRemittedInfoByNo',
        goodsList: '/{v}/PurchaseRemittedOrder/GetPurchaseDocumentList',
        fpgjList: '/{v}/PurchaseRemittedOrder/GetPurchaseInvoiceVerificationList', //发票勾稽列表
        payFpgjList: '/{v}/PurchaseRemittedOrder/GetPurchaseRemittedVerificationList', //付款勾稽列表
        delTemporary: '/{v}/PurchaseRemittedOrder/DeleteInvoiceVerification', //页面临时删除发票
        againRelevanceInvoice: '/{v}/PurchaseRemittedOrder/ReassociateInvoiceVerification', //重新关联勾稽的发票
        GetAvailableBalanceAsync: '/{v}/ReleasedBalance/GetAvailableBalanceAsync', //获取可用抵扣余额
        invoiceNo: '/{v}/PurchaseRemittedOrder/GetPurchaseInvoiceByInvoiceNo',
        getTotalBalanceAsync: '/{v}/Account/GetTotalBalanceAsync',
      },
    }
  },
  computed: {
    rules() {
      return {
        SupplierId: {
          required: true,
          validator: (rule, value, callback) => {
            if (!this.model.SupplierId) {
              callback(new Error('请选择!'))
            } else {
              callback()
            }
          },
        },
        PaymentBeginTime: [{ required: true, message: '请选择!' }],
        PaymentMode: [{ required: true, message: '请选择!' }],
        SettlementType: [{ required: true, message: '请选择!' }],
        ActualPaymentAmount: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (Number(this.model.ActualPaymentAmount) < 0) {
                callback(new Error('实际付款金额不能为负数!'))
              } else {
                callback()
              }
            },
          },
        ],
        // htImages: [{ required: this.isEdit, message: '请上传!' }],
        TableView: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.dataSource || this.dataSource.length === 0) {
                callback(new Error('单据列表不能为空，请获取数据!'))
              } else {
                callback()
              }
            },
          },
        ],
        TotalPaymentAmount: [
          {
            required: true,
            validator: (rule, value, callback) => {
              //单据是否有负数单据
              let haveFu = this.dataSource.some((x) => x.UnsettledAmount < 0)
              //有则总付款金额可填0
              if (haveFu ? !(this.model.TotalPaymentAmount >= 0) : !this.model.TotalPaymentAmount) {
                callback(new Error('请输入!'))
              } else if (
                Number(this.model.TotalPaymentAmount) > Number(this.totalUnpaidAmounts) &&
                Number(this.model.TotalPaymentAmount) != 0
              ) {
                callback(new Error('不能大于未付款金额合计' + this.totalUnpaidAmounts + '!'))
              } else {
                callback()
              }
            },
          },
        ],
        PrepayDeductAmount: [
          {
            required: false,
            validator: (rule, value, callback) => {
              let num =
                Number(this.totalUnpaidAmounts || 0) > Number(this.model.PrepayBalance || 0)
                  ? Number(this.model.PrepayBalance || 0)
                  : Number(this.totalUnpaidAmounts || 0)
              num = this.numFixed(num)
              if (this.model.PrepayDeductAmount > num && Number(this.model.PrepayDeductAmount) != 0) {
                callback(new Error('不能大于未付款金额合计和可用预付余额的最小值' + num + '!'))
              } else {
                callback()
              }
            },
          },
        ],
        htImages: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.model.htImages.length == 0) {
                callback(new Error('请选择合同或上传合同!'))
              } else {
                callback()
              }
            },
          },
        ],
        // dzhImages: [{ required: this.isEdit, message: '请上传!' }],
        // dzhImages: [
        //   {
        //     required: this.isEdit,
        //     validator: (rule, value, callback) => {
        //       if (!this.model.dzhImages || this.model.dzhImages.length == 0) {
        //         callback(new Error('请上传!'))
        //       } else {
        //         callback()
        //       }
        //     },
        //   },
        // ],
        Remarks: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (value) {
                this.validStrMaxLength(rule, value, callback, 100)
              } else {
                callback()
              }
            },
          },
        ],
        RemittedInvoiceNo: [
          {
            required: true,
            validator: (rule, value, callback) => {
              // if (this.RemittedInvoiceNoStrArray && this.RemittedInvoiceNoStrArray.length == 0) {
              //   callback(new Error('付款单据没有包含的发票号!'))
              // } else
              if (!this.model.RemittedInvoiceNo || this.model.RemittedInvoiceNo.length == 0) {
                callback(new Error('请选择本次付款包含的发票号!'))
              } else {
                callback()
              }
            },
          },
        ],
      }
    },
    /**
     * 未付款金额合计
     **/
    totalUnpaidAmounts: {
      get() {
        let total = 0
        this.dataSource.forEach((item) => {
          total += item.UnsettledAmount
        })
        return this.numFixed(total)
      },
      set(value) {
        this.totalUnpaidAmounts = value ? this.numFixed(value) : 0
      },
    },
  },
  watch: {
    opType(val) {
      console.log('opType = ' + this.opType + ' / ', val)
      if (this.opType == 2) {
        this.initColumns()
        if (this.isEdit) {
          this.model.PaymentMode = '1'
        }
        if (this.model.RemittedInvoiceDtos && this.model.RemittedInvoiceDtos.length == 0) {
          this.onAddInvoiceClick()
        }
      }
    },
    rangeDate(val) {
      if (this.rangeDate.length === 0) {
        this.initModel('init')
      }
    },
    id(val) {
      if (val) {
        this.loadDetail()
      }
    },
    orderNo(val) {
      if (val) {
        this.loadDetail()
      }
    },
    dataSource(val) {
      if (val) {
      }
    },
    totalUnpaidAmounts(val) {
      if (val || val == 0) {
        if (this.model.TotalPaymentAmount >= val) {
          this.$set(this.model, 'TotalPaymentAmount', val)
          this.setActualPaymentAmount()
        }
      }
    },
  },
  created() {},
  mounted() {
    if (this.id) {
      this.loadDetail()
    } else {
      this.initColumns()
      this.OriginalInvoiceInfo = null
      this.model.TemporaryId = this.GUID()
    }
  },
  methods: {
    moment,
    // 应收/应付信息
    getTotalBalanceAsync() {
      if (!this.model.SupplierId) return
      getAction(this.url.getTotalBalanceAsync, { SupplierId: this.model.SupplierId }, 'P36011').then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        // res.Data.TotalBalance = Number(
        //   (res.Data.ReceivableBalance + res.Data.PayableBalance + res.Data.PrepaymentBalance).toFixed(2)
        // )
        this.dataSourceA = [res.Data]
      })
    },
    initColumns() {
      let list = [].concat(this.baseColumns)
      //opType 1 申请付款 2 上传发票
      if (this.opType == 1) {
        list = list.concat(this.columns1)
        // if (this.model.PaymentMode == 1) {
        //   // 预付款
        //   list = list.concat(this.columns1)
        // } else {
        //   // 月结
        //   list = list.concat(this.columns1)
        // }
      } else if (this.opType == 2) {
        let size = list.length
        list.splice(size - 1, 1)
        list.splice(size - 2, 1)
        list = list.concat(this.columns2)
      }
      this.actionColumns[0].actionBtns = []
      if (this.isEdit) {
        this.actionColumns[0].actionBtns.push(this.removeAction)
      }
      this.actionColumns[0].actionBtns.push(this.detailAction)

      this.columns = list.concat(this.actionColumns)
    },
    // 金额输入事件
    inputPayMoney(e, type) {
      // 实际付款金额
      if (e) {
        this.$refs.form.clearValidate('ActualPaymentAmount')
      }
      this.setActualPaymentAmount()
    },
    // 实际付款金额
    setActualPaymentAmount() {
      this.model.ActualPaymentAmount = Number(
        (this.model.TotalPaymentAmount || 0) - (this.model.PrepayDeductAmount || 0)
      ).toFixed(2)
    },
    loadDetail() {
      if (!this.orderNo && (this.loading || !this.id)) {
        return
      }
      let that = this
      that.loading = true
      let url = this.opType == 1 ? that.url.detail1 : that.url.detail2
      let params = { id: that.id }
      if (this.orderNo) {
        url = that.url.detailNo
        params = { RemittedOrderNo: that.orderNo }
      }
      getAction(url, params, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            let data = res.Data || {}
            that.model = data
            that.$emit('setAuditId', that.model.AuditId)
            that.$emit('setModel', that.model)
            that.model.PaymentMode = '' + data.PaymentMode
            that.initColumns()
            // 付款单据列表还原
            that.dataSource = data.RemittedDetails || []
            if ((data.RemittedDetails || []).length > 0) {
              that.isOnGetDataClick = true
            }
            // 已勾稽未付款金额合计还原
            that.getGojiPayAmount()
            //合同数据
            if (that.model.RemittedInvoiceDtos) {
              that.model.RemittedInvoiceDtos.forEach((x) => {
                x.InvoiceType = '' + x.InvoiceType
              })
            }
            if (that.model.PaymentBeginTime && that.model.PaymentEndTime) {
              that.rangeDate = [moment(that.model.PaymentBeginTime), moment(that.model.PaymentEndTime)]
            } else {
              that.rangeDate = []
            }

            if (that.opType == 1) {
              if (that.isEdit) {
                that.loadHTListData()
              }
              // 还原月结发票信息
              that.model.RemittedInvoiceNo = []
              if (data.RemittedInvoiceNoStr) {
                that.model.RemittedInvoiceNoStr.split(',').forEach((x) => {
                  that.model.RemittedInvoiceNo.push(x)
                })
              }
              //对账函图片
              let dzhImages = []
              if (data.RemittedReconciliationDtos) {
                data.RemittedReconciliationDtos.forEach((x) => {
                  dzhImages.push(x.ImageUrl)
                })
              }
              that.model.dzhImages = dzhImages
              // 还原勾稽按钮
              that.setGoBtn()
              // 结算方式
              that.model.SettlementType = '' + data.SettlementType
              //合同图片
              that.inithtImages(data.RemittedContractDtos)
            } else if (that.opType == 2) {
              that.getTotalAmount()
            }
            console.log('this.model', that.model)
            this.getTotalBalanceAsync()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // 可用预付余额
    getPrepaidBalance() {
      let params = {
        merchantId: this.model.SupplierId,
      }
      getAction(this.url.GetAvailableBalanceAsync, params, 'P36011')
        .then((res) => {
          if (res.IsSuccess) {
            this.model.PrepayBalance = res.Data || 0
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {})
    },
    setGoBtn() {
      let that = this
      if (that.dataSource.length > 0) {
        if (that.model.ActualPaymentAmount) {
          if (that.opType == 1 && [2, 3].includes(+that.model.PaymentMode) && that.isEdit) {
            that.tab.operateBtns = [{ name: '付款勾稽（已勾稽）', type: 'primary', icon: '', key: '付款勾稽' }]
          } else {
            // 详情月结展示
            if (that.opType == 1 && [2, 3].includes(+that.model.PaymentMode)) {
              that.tab.operateBtns = [{ name: '付款勾稽', type: 'primary', icon: '', key: '付款勾稽' }]
            } else {
              that.tab.operateBtns = []
            }
          }
        } else {
          if (that.opType == 1 && [2, 3].includes(+that.model.PaymentMode)) {
            that.tab.operateBtns = [{ name: '付款勾稽（未勾稽）', type: 'primary', icon: '', key: '付款勾稽' }]
          } else {
            that.tab.operateBtns = []
          }
        }
      } else {
        that.tab.operateBtns = []
      }
    },
    /**
     * 发票号选择change事件
     * @param {*} key
     * @param {*} label
     * @param {*} e
     */
    changeInvoice(key, label, e) {
      // this.model.RemittedInvoiceNoStr = e

      if (this.model.RemittedInvoiceNo) {
        this.$refs.form.clearValidate('RemittedInvoiceNo')
      }
      this.$forceUpdate()
    },
    /**
     * 重新加载发票数据，供选择
     */
    reloadFaPiaoData() {
      //只有 申请付款 && 月结 情况下才加载发票数据
      if (!(this.opType == 1 && [2, 3].includes(+this.model.PaymentMode))) {
        return
      }
      // this.DocumentIdArray = []
      // this.dataSource.map((item) => {
      //   this.DocumentIdArray.push(item.DocumentId)
      // })

      let that = this
      if (this.DocumentIdArray.length == 0) {
        that.model.RemittedInvoiceNo = []
        return
      }
      postAction(
        that.url.invoiceNo,
        {
          pageIndex: 1,
          pageSize: 200,
          IsValid: true,
          DocumentOrderId: this.DocumentIdArray,
        },
        'P36009'
      ).then((res) => {
        if (res.IsSuccess) {
          that.onInvoiceNoLoaded(res.Data || [])
        }
      })
    },

    onInvoiceNoLoaded(list) {
      if (!this.dataSource || this.dataSource.length == 0) {
        return
      }
      if (this.opType == 1 && this.isEdit == true && !this.id) {
        this.model.RemittedInvoiceNo = []
        list.forEach((x) => {
          this.model.RemittedInvoiceNo.push(x.InvoiceNo)
        })
        this.$forceUpdate()
      }
    },
    updateInvoice(type, key, index) {
      if (!this.model.RemittedInvoiceDtos[index][key]) {
        this.$message.warning(type + '为空，请输入' + type)
        return
      }
      this.OriginalInvoiceInfo = JSON.parse(JSON.stringify(this.model.RemittedInvoiceDtos[index]))
      this.invoiceIndex = index
      this.invoiceType = type
      this.invoiceVal = this.model.RemittedInvoiceDtos[index][key]
      this.invoicevisible = true
    },
    invoiceHandleOk() {
      if (this.invoiceType == '发票代码') {
        if (this.invoiceVal === this.model.RemittedInvoiceDtos[this.invoiceIndex].InvoiceCode) {
          this.invoicevisible = false
          return
        }
        this.model.RemittedInvoiceDtos[this.invoiceIndex].InvoiceCode = this.invoiceVal
      } else if (this.invoiceType == '发票号') {
        this.$refs.form.clearValidate('InvoiceNo')
        if (this.invoiceVal && !this.isValidLength(this.invoiceVal)) {
          // 计算字符串的长度 发票号只能输入8位、9位、20位、21位
          this.$message.warning('发票号只能输入8位、9位、20位、21位')
          return
        }
        if (this.invoiceVal === this.model.RemittedInvoiceDtos[this.invoiceIndex].InvoiceNo) {
          this.invoicevisible = false
          return
        }
        this.model.RemittedInvoiceDtos[this.invoiceIndex].InvoiceNo = this.invoiceVal
      }
      this.subAgainRelevanceInvoice(this.invoiceIndex, this.model.RemittedInvoiceDtos[this.invoiceIndex])
      this.invoicevisible = false
    },
    inithtImages(list) {
      let htList = []
      if (list) {
        list.forEach((x) => {
          htList.push(x.ImageUrl)
        })
      }
      this.model.htImages = htList
      this.$forceUpdate()
    },
    /**
     * 获取数据
     */
    onGetDataClick() {
      this.isOnGetDataClick = false
      this.$refs.form.clearValidate()
      let list = ['SupplierId', 'PaymentMode', 'PaymentBeginTime']
      let num = 0
      let errArray = []
      this.$refs.form.validateField(list, (errMsg) => {
        num++
        errArray.push(errMsg)
        if (num == list.length) {
          let haveError = errArray.find((item) => {
            return item
          })
          if (!haveError) {
            this.loadListData()
          }
        }
      })
    },
    /**
     * 获取单据数据
     */
    loadListData(type) {
      if (!this.url.goodsList) {
        return
      }
      let that = this
      that.loading = true
      that.loadHTListData()
      getAction(
        that.url.goodsList,
        {
          TemporaryId: that.model.TemporaryId,
          SupplierId: that.model.SupplierId,
          PaymentMode: that.model.PaymentMode,
          PurchaseRemittedFunMode: that.opType,
          PaymentBeginTime: that.model.PaymentBeginTime,
          PaymentEndTime: that.model.PaymentEndTime,
        },
        that.httpHead
      )
        .then((res) => {
          if (res.IsSuccess) {
            //重置model部分数据
            if (type != 'info' && that.opType == 2) {
              that.initModel()
            }
            that.isOnGetDataClick = true
            that.dataSource = res.Data || []
            if (that.opType == 2) {
              that.getTotalAmount()
            }
            that.setGoBtn()
            if (that.isEdit && type != 'info') {
              that.initModel()
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    changeGys(val, txt, item) {
      this.model.SupplierName = txt
      this.model.SupplierId = val
      if (val) {
        this.initModel('init')
        this.getTotalBalanceAsync()
      }
    },
    /**
     * 重置model部分数据
     */
    initModel(type) {
      console.log('initModel')
      this.model.dzhImages = []
      this.model.htImages = []
      this.model.RemittedInvoiceDtos = []
      this.model.RemittedContractDtos = []
      this.model.ActualPaymentAmount = '' //实际付款金额
      this.model.SettlementType = ''
      this.model.TotalPaymentAmount = null //总付款金额
      this.model.PrepayDeductAmount = null //预付余额抵扣
      this.model.PrepayBalance = 0 //可用预付余额
      this.$set(this.model, 'Remarks', '')
      if (type == 'init') {
        this.dataSource = []
      } else {
        if (this.opType == 1) {
          this.getPrepaidBalance() //可用预付余额
          let array = ['TotalPaymentAmount', 'PrepayDeductAmount', 'ActualPaymentAmount']
          this.$refs.form.clearValidate(array)
        }
        if ([2, 3].includes(+this.model.PaymentMode) || (this.opType == 2 && this.model.PaymentMode == 1)) {
          this.onAddInvoiceClick()
        }
      }
    },
    /**
     * 获取计算实际付款金额
     */
    getActualPaymentAmount(ActualPaymentAmount) {
      if (this.paytype != 2) {
        let amount = 0
        //预付款 --- 实际付款金额=（采购订单+采购票折+采购退货+采购差价）的未结算金额
        if (this.model.PaymentMode == 1) {
          if (this.dataSource && this.dataSource.length > 0) {
            this.dataSource.forEach((x) => {
              amount += x.UnsettledAmount * 1
            })
          }
        } else if ([2, 3].includes(+this.model.PaymentMode)) {
          if (this.model.RemittedInvoiceDtos && this.model.RemittedInvoiceDtos.length > 0) {
            //月结 --- 实际付款金额=（发票勾稽金额-销货对抵金额）的合计  SJFKAmount就是（发票勾稽金额-销货对抵金额）
            this.model.RemittedInvoiceDtos.forEach((x) => {
              amount += x.SJFKAmount || 0
            })
          }
        }
        this.model.ActualPaymentAmount = this.getAmountOfMoney(amount, 6)
      } else if (this.paytype == 2) {
        // 付款勾稽
        if (this.model.PaymentMode == 1) {
          ActualPaymentAmount = 0
          this.dataSource.map((item) => {
            ActualPaymentAmount += item.UnsettledAmount
          })
        }
        this.model.ActualPaymentAmount = this.getAmountOfMoney(ActualPaymentAmount, 6)
      }

      console.log('ActualPaymentAmount = ' + this.model.ActualPaymentAmount)
    },
    /**
     * 获取合同数据
     */
    loadHTListData() {
      if (this.opType == 2) {
        return
      }
      let that = this
      // if (!that.loading) {
      //   that.loading = true
      // }
      that.htDataList = []
      getAction(
        '/{v}/PurchaseContract/GetListAsync',
        {
          SupplierId: that.model.SupplierId,
          SettlementMethod: that.model.PaymentMode,
          ValidStartTime: that.model.PaymentBeginTime,
          ValidEndTime: that.model.PaymentEndTime,
          HasFileList: true,
        },
        'P36007'
      )
        .then((res) => {
          if (res.IsSuccess) {
            that.htDataList = res.Data || []
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          // if (that.loading) {
          //   that.loading = false
          // }
        })
    },
    /**
     * 增加发票
     */
    onAddInvoiceClick() {
      this.model.RemittedInvoiceDtos.push(JSON.parse(JSON.stringify(this.emptyInvoice)))
    },
    onPaymentModeChange() {
      this.initColumns()
      if (this.opType == 1 && [2, 3].includes(+this.model.PaymentMode)) {
        if (this.dataSource.length > 0) {
          this.tab.operateBtns = [{ name: '付款勾稽（未勾稽）', type: 'primary', icon: '', key: '付款勾稽' }]
        }
        if (this.model.RemittedInvoiceDtos && this.model.RemittedInvoiceDtos.length == 0) {
          this.onAddInvoiceClick()
        }
      } else {
        this.model.RemittedInvoiceDtos = []
        this.tab.operateBtns = []
      }
      // 清空table数据
      this.tab.operateBtns = []
      this.model.ActualPaymentAmount = null
      this.initModel('init')
    },

    onActionClick(record, key, index) {
      if (key === '移除') {
        const documentNo = record.DocumentNo
        let dIndex = this.dataSource.findIndex((x) => x.DocumentId === record.DocumentId)
        if (dIndex > -1) {
          this.dataSource.splice(dIndex, 1)

          if (this.opType == 2) {
            this.getTotalAmount()
          } else {
            if ([2, 3].includes(+this.model.PaymentMode)) {
              this.getActualPaymentAmount()
            }
          }
          this.$message.success('单据 [' + documentNo + '] 移除成功!')
        }
      } else if (key === '商品明细') {
        this.$refs.goodsDetailListModal.show(record.DocumentId, record.BusinessEventType, false)
      }
    },
    checkSupplierId(callback) {
      let list = ['SupplierId', 'PaymentMode', 'PaymentBeginTime']
      let num = 0
      this.$refs.form.validateField(list, (errMsg) => {
        num++
        if (num == list.length) {
          if (!errMsg) {
            if (!this.isOnGetDataClick) {
              this.$message.warning('请先获取数据!')
              return
            }
            callback && callback(this.getData())
          }
        }
      })
    },
    checkForm(callback) {
      let that = this
      this.$refs.form.validate((err, values) => {
        if (err) {
          if (!that.dataSource || that.dataSource.length == 0) {
            that.$message.warning('请先获取单据数据!')
            callback && callback()
            return
          }
          // 上传发票
          if (that.opType == 2) {
            if (!that.model.RemittedInvoiceDtos || that.model.RemittedInvoiceDtos.length == 0) {
              that.$message.warning('请添加发票数据!')
              callback && callback()
              return
            } else {
              let errList = []
              that.model.RemittedInvoiceDtos.forEach((x, i, arr) => {
                if (!x.CurrentVerificationAmount) {
                  errList.push('发票' + (i + 1))
                }
              })
              if (errList.length > 0) {
                that.$message.warning(errList.toString() + '未勾稽')
                callback && callback()
                return
              } else {
                const noAmountList = that.model.RemittedInvoiceDtos.filter((invice) => !invice.InvoiceAmount)
                if (noAmountList && noAmountList.length > 0) {
                  that.$message.warning('请把发票的发票金额填写完整!')
                  callback && callback()
                  return
                }
              }
            }
          } else if (that.opType == 1) {
            // 申请付款
            if (!that.model.ActualPaymentAmount && that.dataSource.length == 0) {
              that.$message.warning('付款金额为空，请选择付款勾稽!')
              callback && callback()
              return
            }
            // 预付款
            if (that.model.PaymentMode == 1) {
              if (Number(that.model.TotalPaymentAmount) < 0) {
                that.$message.warning('未付款金额合计不能为负数!')
                callback && callback()
                return
              }
            } else if ([2, 3].includes(+that.model.PaymentMode)) {
              // 月结
              if (Number(that.model.ActualPaymentAmount) <= 0) {
                that.$message.warning('实际付款金额不能为负数和0!')
                callback && callback()
                return
              }
            }
          }
          callback && callback(that.getData())
        } else {
          callback && callback()
        }
      })
    },
    getData() {
      let data = JSON.parse(JSON.stringify(this.model))
      let SettlementType = null
      if (data.SettlementType == '0' || data.SettlementType) {
        SettlementType = Number(data.SettlementType)
      }
      data.SettlementType = SettlementType
      data.PaymentMode = Number(data.PaymentMode)
      if (data.ActualPaymentAmount) {
        data.ActualPaymentAmount = data.ActualPaymentAmount * 1
      }

      this.$delete(data, 'htImages')
      // data.RemittedContractDtos = []
      // if (this.model.htImages) {
      //   //合同
      //   this.model.htImages.forEach(x => {
      //     data.RemittedContractDtos.push({
      //       ImageUrl: x
      //     })
      //   })
      // }
      data.RemittedReconciliationDtos = []
      this.$delete(data, 'dzhImages')
      if (this.model.dzhImages) {
        //对账函
        this.model.dzhImages.forEach((x) => {
          data.RemittedReconciliationDtos.push({
            ImageUrl: x,
          })
        })
      }
      // 申请付款
      if (this.opType == 1) {
        data.RemittedInvoiceDtos = null
      } else {
        data.RemittedInvoiceDtos.forEach((item) => {
          item.InvoiceType = Number(item.InvoiceType)
        })
      }

      data.SavePurchaseRemittedDetails = this.dataSource
      // 采购付款并且是月结
      if (this.opType == 1 && [2, 3].includes(+this.model.PaymentMode)) {
        data.RemittedInvoiceNoStr = this.model.RemittedInvoiceNo ? this.model.RemittedInvoiceNo.join(',') : ''
      }
      // 设置金额数据不能为null
      if (!data.ActualPaymentAmount) {
        data.ActualPaymentAmount = 0
      }
      if (!data.TotalPaymentAmount) {
        data.TotalPaymentAmount = 0
      }
      if (!data.PrepayDeductAmount) {
        data.PrepayDeductAmount = 0
      }
      // 月结/货到付款时总金额等于实际付款金额
      if ([2, 3].includes(+this.model.PaymentMode)) {
        data.TotalPaymentAmount = data.ActualPaymentAmount
      }
      return data
    },
    /**
     * 删除发票
     * @param {*} index
     */
    onRemoveInvoiceClick(item, index) {
      if (item.IsVerification) {
        let that = this
        this.$confirm({
          title: '当前发票已勾稽，确定删除该发票?',
          content: '',
          onOk() {
            that.removeInvoiceClick(item, index)
          },
          onCancel() {},
        })
      } else {
        this.model.RemittedInvoiceDtos.splice(index, 1)
        this.$message.success('删除成功')
      }
    },
    // 确定删除发票
    removeInvoiceClick(item, index) {
      console.log(item, index)
      let params = {
        TemporaryId: this.model.TemporaryId,
        InvoiceCode: item.InvoiceCode,
        InvoiceNo: item.InvoiceNo,
      }
      postAction(this.url.delTemporary, params, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.$message.success('删除成功')
            this.model.RemittedInvoiceDtos.splice(index, 1)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    /**
     * 发票勾稽回调
     * @param {* 当前发票的下标} index
     * @param {* 勾稽金额} amount
     * @param {*  这个参数代表意义不一样  发票是销货对抵金额 付款勾稽 付款勾稽是勾稽的数据的DoctmentId集合} offsetAmount
     * @param {* } OriginalInvoiceInfo
     */
    invoiceCheckingCallback(index, amount, offsetAmount, OriginalInvoiceInfo) {
      console.log('OriginalInvoiceInfo', index, amount, offsetAmount, OriginalInvoiceInfo)
      // 已勾稽未付款金额合计
      this.gojiTotalUnpaidAmounts = amount
      if (this.paytype != 2) {
        if (index < 0) {
          return
        }
        this.OriginalInvoiceInfo = OriginalInvoiceInfo
        this.model.RemittedInvoiceDtos[index].CurrentVerificationAmount = amount
        this.model.RemittedInvoiceDtos[index].IsVerification = true
        this.model.RemittedInvoiceDtos[index].Index = OriginalInvoiceInfo.Index
        this.model.RemittedInvoiceDtos[index].goujiType = OriginalInvoiceInfo.goujiType
        this.model.RemittedInvoiceDtos[index].InvoiceReconciliationType = OriginalInvoiceInfo.goujiType
        //实际付款金额=（发票勾稽金额-销货对抵金额）/实际付款金额=（发票勾稽金额-销货对抵金额）
        let num = amount - offsetAmount
        this.model.RemittedInvoiceDtos[index].SJFKAmount = num > 0 ? this.getAmountOfMoney(num, 6) : 0

        let newList = JSON.parse(JSON.stringify(this.model.RemittedInvoiceDtos))
        this.model.RemittedInvoiceDtos = []
        this.model.RemittedInvoiceDtos = newList

        this.getActualPaymentAmount()
      } else if (this.paytype == 2) {
        // 付款勾稽
        if (this.opType == 1 && [2, 3].includes(+this.model.PaymentMode)) {
          this.tab.operateBtns = [{ name: '付款勾稽（已勾稽）', type: 'primary', icon: '', key: '付款勾稽' }]
        }
        this.DocumentIdArray = offsetAmount
        this.reloadFaPiaoData()
        this.getActualPaymentAmount(amount)
      }
    },
    onInvoiceCodeChange(e, index, type) {
      // if (this.model.RemittedInvoiceDtos && this.model.RemittedInvoiceDtos[index].IsVerification) {
      //   this.model.RemittedInvoiceDtos[index].IsVerification = false
      //   this.model.RemittedInvoiceDtos[index].CurrentVerificationAmount = null
      // }
    },
    onInvoiceCodeBlur(e, index, item, type) {
      console.log(e.target.value, index, item, type)
      if (item.IsVerification) {
        if (item[type] !== this.OriginalInvoiceInfo[type]) {
          this.subAgainRelevanceInvoice(index, item)
        }
      }
    },
    // 重新赋值勾稽的发票
    subAgainRelevanceInvoice(index, item) {
      let params = {
        TemporaryId: this.model.TemporaryId,
        InvoiceCode: item.InvoiceCode,
        InvoiceNo: item.InvoiceNo,
        InvoiceTaxRate: item.InvoiceTaxRate,
        OriginalInvoiceCode: this.OriginalInvoiceInfo.InvoiceCode,
        OriginalInvoiceNo: this.OriginalInvoiceInfo.InvoiceNo,
      }
      postAction(this.url.againRelevanceInvoice, params, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.$message.success('发票重新勾稽成功')
            this.OriginalInvoiceInfo.InvoiceCode = item.InvoiceCode
            this.OriginalInvoiceInfo.InvoiceNo = item.InvoiceNo
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    /**
     * 发票勾稽
     */
    onFPGJClick(item, index) {
      this.paytype = 1
      console.log('查看的item', item)
      if (this.isEdit) {
        if (!item.InvoiceCode || !item.InvoiceNo || !item.InvoiceTaxRate) {
          this.$message.warning('请先输入发票代码、发票号和税率！')
          return
        }
        let invoice = {
          TemporaryId: this.model.TemporaryId,
          InvoiceCode: item.InvoiceCode,
          InvoiceNo: item.InvoiceNo,
          InvoiceTaxRate: item.InvoiceTaxRate,
          Index: index,
        }
        //新增
        let that = this
        let params = {
          ...invoice,
          DocumentDtos: [],
        }
        this.$delete(params, 'Index')
        this.dataSource.forEach((x) => {
          params.DocumentDtos.push({
            DocumentId: x.DocumentId,
            BusinessEventType: x.BusinessEventType,
          })
        })
        let isNoVisible = false
        let goujiType = item.goujiType || item.InvoiceReconciliationType || 1
        this.$refs.invoiceCheckingModal.showOne(
          this.url.fpgjList,
          this.isEdit,
          invoice,
          item.IsVerification,
          params,
          isNoVisible,
          goujiType,
          'init',
          'showInit'
        )
        // that.loading = true
        // postAction(that.url.fpgjList, params, that.httpHead)
        //   .then((res) => {
        //     if (res.IsSuccess) {
        //       let isGouji = item.IsVerification
        //       that.$refs.invoiceCheckingModal.show(that.isEdit, res.Data || [], invoice, isGouji)
        //     } else {
        //       that.$message.error(res.Msg)
        //     }
        //   })
        //   .finally(() => {
        //     that.loading = false
        //   })
      } else {
        let goujiType = item.goujiType || item.InvoiceReconciliationType || 1
        let isAudit = false
        this.$refs.invoiceCheckingModal.look(item.Id, item.CurrentVerificationAmount, isAudit, goujiType)
      }
    },
    // 付款勾稽
    onPayFPGJClick() {
      //新增
      this.paytype = 2
      if (this.isEdit) {
        let that = this
        let params = {
          TemporaryId: this.model.TemporaryId,
          DocumentDtos: [],
        }
        this.dataSource.forEach((x) => {
          params.DocumentDtos.push({
            DocumentId: x.DocumentId,
            BusinessEventType: x.BusinessEventType,
          })
        })
        this.$refs.invoiceCheckingModal.showOne(
          this.url.payFpgjList,
          this.isEdit,
          {
            TemporaryId: this.model.TemporaryId,
          },
          this.model.ActualPaymentAmount ? true : false,
          params
        )
      } else {
        this.$refs.invoiceCheckingModal.lookPay(this.id)
      }
    },
    // 获取勾稽付款金额合计
    getGojiPayAmount() {
      let that = this
      let params = {
        TemporaryId: this.model.TemporaryId,
        DocumentDtos: [],
      }
      this.dataSource.forEach((x) => {
        params.DocumentDtos.push({
          DocumentId: x.DocumentId,
          BusinessEventType: x.BusinessEventType,
        })
      })
      postAction(this.url.payFpgjList, params, 'P36009')
        .then((res) => {
          if (res.IsSuccess) {
            let total = 0
            let resData = res.Data || []
            resData.forEach((item) => {
              if (item.IsSelect) {
                total += item.TaxIncludedAmount
              }
            })
            that.gojiTotalUnpaidAmounts = total
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {})
    },
    onOperateClick(type) {
      if (type == '付款勾稽') {
        this.onPayFPGJClick()
      }
    },
    /**
     * 新增合同的回调
     */
    addHTCallback(id) {
      let that = this
      that.loading = true
      getAction('/{v}/PurchaseContract/GetInfoAsync', { Id: id }, 'P36007')
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              if (res.Data.FileList) {
                let itemList = []
                res.Data.FileList.forEach((x, i, arr) => {
                  let item = {
                    isAdd: true,
                    PurchaseContractId: res.Data.Id,
                    PurchaseContractNo: res.Data.ContractNo,
                    ImageUrl: x.FileUrl,
                    id: i,
                  }
                  itemList.push(item)
                })
                that.model.RemittedContractDtos = [].concat(itemList)
              }

              that.inithtImages(that.model.RemittedContractDtos)
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    changeHetong(images) {
      // 删除重组数据
      let newArray = []
      let arr1 = this.model.RemittedContractDtos
      let arr2 = this.model.htImages
      newArray = arr1.filter((obj1) => arr2.some((obj2) => obj1.ImageUrl === obj2))
      this.model.RemittedContractDtos = newArray
      this.model.htImages = images
    },
    /**
     * 选择合同回调
     * @param {*} list
     */
    selectHTCallback(list) {
      console.log('selectHTCallback', list)
      if (list && list.length > 0) {
        // let addHTList = this.model.RemittedContractDtos.filter((x) => x.isAdd == true)
        let sleectedHTList = []
        list.forEach((x) => {
          if (x.FileList && x.FileList.length > 0) {
            // let item = this.model.RemittedContractDtos.find((y) => y.PurchaseContractId == x.Id)
            // if (!item) {
            x.FileList.forEach((img) => {
              if (img.FileUrl) {
                let newItem = {
                  isAdd: false,
                  PurchaseContractId: x.Id,
                  PurchaseContractNo: x.ContractNo,
                  ImageUrl: img.FileUrl,
                }
                sleectedHTList.push(newItem)
              }
            })
            // }
          }
        })

        // this.model.RemittedContractDtos = [].concat(addHTList).concat(sleectedHTList)
        this.model.RemittedContractDtos = [].concat(sleectedHTList)

        this.inithtImages(this.model.RemittedContractDtos)
        this.$refs.form.validateField(['htImages'])
        this.$forceUpdate()
      }
    },
    /**
     * 合计应收剩余发票金额
     */
    getTotalAmount() {
      let num = 0
      this.dataSource.forEach((x) => {
        if (x.UnInvoiceVerificationAmount >= 0 || x.UnInvoiceVerificationAmount < 0) {
          num += x.UnInvoiceVerificationAmount * 1
        }
      })
      this.total = this.getAmountOfMoney(num, 2)
    },
    // 计算字符串的长度 发票号只能输入8位、9位、20位、21位
    isValidLength(number) {
      const invoiceNumberLength = [8, 9, 20, 21]
      let len = number.length
      if (number.endsWith('.')) {
        len = len - 1
      }
      return invoiceNumberLength.some((v) => v == len)
    },
  },
}
</script>
<style scoped lang="less">
// /deep/.ant-form-item-control {
//   height: 34px;
// }
/deep/.ant-card-head-title {
  padding: 0 0;
}
</style>
