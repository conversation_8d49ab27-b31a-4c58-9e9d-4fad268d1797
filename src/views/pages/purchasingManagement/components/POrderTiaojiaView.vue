<!--
 * @Author: LP
 * @Description: 采购调价详情和编辑
 * @Date: 2023/07/10
-->
<template>
  <div>
    <a-spin :spinning="loading || confirmLoading || Loading">
      <a-form-model ref="form" :rules="rules" :model="model">
        <a-row :gutter="10">
          <a-col span="24">
            <TableView
              v-if="model"
              :showCardBorder="false"
              :tab="tab"
              :columns="columns"
              :dataList="model.GoodsItems || []"
              @actionClick="onActionClick"
            >
              <!-- <template slot="bottomView">
          <a-row style="padding: 20px 16px">
            <a-col span="18">
              备注:
              <a-input
                v-if="isEdit"
                placeholder="请输入"
                v-model="model.Remark"
                style="width: 90%"
                :maxLength="100"
                @change="onReamrkChange"
              />
              <span v-else>{{ model.Remark || '--' }}</span>
            </a-col>
            <a-col span="6" style="text-align: right; font-weight: bold"
              >调价金额： {{ total < 0 ? '-' : '' }}¥{{ total < 0 ? String(total).replace('-', '') : total }}</a-col
            >
          </a-row>
        </template> -->
            </TableView>
          </a-col>
        </a-row>
        <a-row :gutter="10" class="mt15">
          <a-col span="18">
            <a-form-model-item label="备注" prop="Remark" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-if="isEdit"
                placeholder="请输入"
                v-model="model.Remark"
                style="width: 100%"
                :maxLength="100"
                @change="onReamrkChange"
              />
              <span v-else>{{ model.Remark || '--' }}</span>
            </a-form-model-item>
          </a-col>
          <a-col span="6" style="text-align: right; font-weight: bold; padding-right: 20px"
            >调价金额： {{ total < 0 ? '-' : '' }}¥{{ total < 0 ? String(total).replace('-', '') : total }}</a-col
          >
        </a-row>
      </a-form-model>
    </a-spin>
  </div>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'POrderTiaojiaView',
  mixins: [ListMixin, EditMixin],
  components: {},
  props: {
    /**
     * 商品数据
     */
    model: {
      type: Object,
      required: true,
      default: () => {
        return { Remark: '', GoodsItems: [] }
      },
    },
    /**
     * 类型 1 调价列表-详情 2 调价列表-编辑  3 采购列表新增
     */
    opType: {
      type: Number,
      default: () => {
        return 1
      },
    },
    Loading: {
      type: Boolean,
      default: () => {
        return false
      },
    },
  },
  watch: {
    model: {
      handler(newVal, oldVal) {
        this.getTotalAmount()
      },
      deep: true,
    },
  },
  data() {
    return {
      loading: false,
      confirmLoading: false,
      tab: {
        tabTitles: [],
        showPagination: false,
      },
      labelCol: {
        xs: { span: 2 },
        sm: { span: 2 },
      },
      wrapperCol: {
        xs: { span: 22 },
        sm: { span: 22 },
      },
      total: 0,
      ErpPCCostKey: '',
      tjslKey: '',
      hscjxjKey: '',
      httpHead: 'P36009',
      url: {},
    }
  },
  computed: {
    rules() {
      return {
        Remark: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (this.model.Remark) {
                this.validStrMaxLength(rule, this.model.Remark, callback, 100)
              } else {
                callback()
              }
            },
          },
        ],
      }
    },
    isEdit() {
      return this.opType == 2 || this.opType == 3
    },
    columns() {
      const actionList = this.isEdit
        ? [
            {
              title: '操作',
              dataIndex: 'action',
              align: 'center',
              width: 80,
              fixed: 'right',
              scopedSlots: { customRender: 'action' },
              actionBtns: [
                {
                  name: '移除',
                  id: 'f2551512-daaf-40c8-ae07-f361328fc3b7',
                },
              ],
            },
          ]
        : []
      return [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          fixed: this.isEdit ? 'left' : '',
          width: 150,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 150,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 180,
        },
        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '有效期',
          dataIndex: 'ExpirationDate',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '批次号',
          dataIndex: 'BatchNumber',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: 'erp批次在库成本',
          dataIndex: this.ErpPCCostKey,
          width: 150,
          // fixed: this.isEdit ? 'right' : '',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '入库数量',
          dataIndex: 'InboundQuantity',
          width: 100,
          // fixed: this.isEdit ? 'right' : '',
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        {
          title: '已售数量',
          dataIndex: 'SoldQuantity',
          width: 100,
          // fixed: this.isEdit ? 'right' : '',
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        {
          title: '未售数量',
          dataIndex: 'UnsoldQuantity',
          width: 100,
          // fixed: this.isEdit ? 'right' : '',
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        // {
        //   title: '调价数量',
        //   dataIndex: this.tjslKey,
        //   width: 100,
        //   fixed: this.isEdit ? 'right' : '',
        //   precision: 0, //数值精度
        //   onInputChange: this.onInputChange,
        //   disabled: true,
        //   scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'number' },
        //   ellipsis: true,
        // },
        {
          title: '调价数量',
          dataIndex: this.tjslKey,
          width: 100,
          fixed: this.isEdit ? 'right' : '',
          precision: 0, //数值精度
          onBlur: this.onBlur,
          scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'number' },
          ellipsis: true,
        },

        // {
        //   title: '新含税进价',
        //   dataIndex: 'AdjustedTaxIncludedPurchasePrice',
        //   width: 150,
        //   fixed: this.isEdit ? 'right' : '',
        //   onInputChange: this.onInputChange,
        //   precision: 6,
        //   scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'price' },
        //   ellipsis: true,
        // },
        // 含税进价差价小计改名为调价小计金额
        {
          title: '调价小计金额',
          dataIndex: this.hscjxjKey,
          width: 150,
          fixed: this.isEdit ? 'right' : '',
          precision: 2, //数值精度
          min: -Infinity,
          onBlur: this.onBlur,
          scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'price' },
          ellipsis: true,
        },
        // 含税进价差价改名为差额
        {
          title: '差额',
          dataIndex: 'TaxIncludedPurchasePriceDifference',
          width: 120,
          fixed: this.isEdit ? 'right' : '',
          precision: 6, //数值精度
          min: -Infinity,
          disabled:true,
          scopedSlots:  { customRender: this.isEdit ? 'inputNumber' : 'price' },
          ellipsis: true,
        },
        
        ...actionList,
      ]
    },
  },
  mounted() {
    this.tjslKey =  'Quantity' //this.opType == 3 ? 'AdjustmentQuantity' : 'Quantity' //调价数量
    this.ErpPCCostKey = (this.opType == 3 || this.opType == 1) ? 'ErpPCCost' : 'LastErpPCCost' //erp批次在库成本
    // this.hscjxjKey =
    //   this.opType == 3 ? 'TaxIncludedPurchasePriceDifferenceTotal' : 'TotalTaxIncludedPurchasePriceDifference'
    this.hscjxjKey = 'TotalTaxIncludedPurchasePriceDifference'

    // setTimeout(() => {
    //   this.initDataList()
    // }, 500)
  },
  created() {},
  methods: {
    initDataList() {
      if (this.opType == 3) {
        this.model.GoodsItems.forEach((x) => {
          x[this.tjslKey] = x.UnsoldQuantity
        })
        this.$forceUpdate()
      }
    },
    getData(callback) {
      if (this.model.GoodsItems.length == 0) {
        this.$message.warning('无商品数据，无须提交')
        callback && callback()
        return
      }
      this.$refs.form.validate((err, values) => {
        if (err) {
          let inputList = this.model.GoodsItems.filter((x) => {
            return x[this.tjslKey] && x[this.hscjxjKey]
          })

          if (inputList && inputList.length == 0) {
            this.$message.warning('请至少完善一个商品数据')
            callback && callback()
          } else {
            let data = JSON.parse(JSON.stringify(this.model))
            data.GoodsItems = inputList
            callback && callback(data)
          }
        } else {
          callback && callback()
        }
      })
    },
    onActionClick(record, key, index) {
      if (key == '移除') {
        this.model.GoodsItems.splice(index, 1)
      }
    },
    /**
     * 获取总金额
     */
    getTotalAmount() {
      let sum = 0

      if (this.model.GoodsItems) {
        this.model.GoodsItems.forEach((x) => {
          if (x[this.hscjxjKey] >= 0 || x[this.hscjxjKey] < 0) {
            sum += x[this.hscjxjKey]
          }
        })
      }

      this.total = this.getAmountOfMoney(sum)
    },
    onReamrkChange() {
      // if (this.getStrMaxLength(this.model.Remark) > 100) {
      //   this.model.Remark = this.model.Remark.substring(0, 99)
      //   this.$message.warning('备注最多100个字符')
      // }

      this.$forceUpdate()
    },
    /**
     * @param {*} val
     * @param {*} key
     * @param {*} index
     */
     onBlur(val, key, record, index) {
      let num = val || 0

      if (key == this.tjslKey) {
        // .输入的调价数量只能大于等于未售数量，小于等于入库数量
        if(record[this.tjslKey] &&(record[this.tjslKey] < (record['UnsoldQuantity'] || 0) || record[this.tjslKey] > (record['InboundQuantity'] || 0))){
          this.$message.warning('调价数量只能大于等于未售数量，小于等于入库数量')
          record[this.tjslKey] = null
          return
        }
        // //输入调价数量则计算含税进价差价小计 公式（含税进价差价小计=含税进价差价*调价数量）
        // if (record.TaxIncludedPurchasePriceDifference != null && record.TaxIncludedPurchasePriceDifference != '') {
        //   let newCJTotal = record.TaxIncludedPurchasePriceDifference * num
        //   record[this.hscjxjKey] = newCJTotal
        // }
      }
      //  else if (key == 'AdjustedTaxIncludedPurchasePrice') {
      //   //输入新含税进价则计算含税差价 公式（含税进价差价=新含税进价-含税进价）
      //   if (record.TaxIncludedPurchasePrice != null && record.TaxIncludedPurchasePrice != '') {
      //     let newCJ = num > 0 ? num - record.TaxIncludedPurchasePrice : 0
      //     record.TaxIncludedPurchasePriceDifference = newCJ
      //   }
      //   if (record[this.tjslKey] >= 0) {
      //     let newCJTotal = record.TaxIncludedPurchasePriceDifference * record[this.tjslKey]
      //     record[this.hscjxjKey] = newCJTotal
      //   }
      // }
      // 差额=调价小计金额/调价数量
      // if (record[this.hscjxjKey] != null && record[this.tjslKey] != null) {
        // let newCJTotal = (record[this.hscjxjKey] || 0) / (record[this.tjslKey] || 0)
        let newCJTotal = this.numMath((record[this.hscjxjKey] || 0),(record[this.tjslKey] || 0),'/',6)
        record['TaxIncludedPurchasePriceDifference'] = newCJTotal
      // }
      this.$forceUpdate()
      // console.log('record val=' + num, record)
      //计算调价金额
      this.getTotalAmount()
    },
    /**
     * @param {*} val
     * @param {*} key
     * @param {*} index
     */
    //  onBlur(val, key, record, index) {
    //   let num = val || 0

    //   if (key == this.tjslKey) {
    //     // .输入的调价数量只能大于等于未售数量，小于等于入库数量
    //     if(record[this.tjslKey] &&(record[this.tjslKey] < (record['UnsoldQuantity'] || 0) || record[this.tjslKey] > (record['InboundQuantity'] || 0))){
    //         this.$message.warning('调价数量只能大于等于未售数量，小于等于入库数量')
    //         record[this.tjslKey] = null
    //         return
    //       }
    //   } 
    //   this.$forceUpdate()
    // },
  },
}
</script>

<style lang="less" scoped>

.mb20 {
  margin-bottom: 30px;
}
</style>
