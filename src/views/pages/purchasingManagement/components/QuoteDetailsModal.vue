<!-- 报价明细 -->
<template>
  <a-modal :title="title" :width="1100" :visible="visible" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :footer="null" cancelText="关闭" :destroyOnClose="true">
    <div>
      <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
      <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
      <!-- 列表 -->
      <TableNewView ref="tableView" :table="table" :columns="columns" :dataList="dataSource">
        <div :class="dataSource.length > 0?'time-text':'time-none-text'" slot="bottomView">更新时间：{{model.CreateTime || '--'}}</div>
      </TableNewView>
    </div>

  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "QuoteDetailsModal",
  components: {},
  mixins: [ListMixin],
  data() {
    return {
      title: "报价明细",
      visible: false,
      searchItems: [
        {
          name: '商品名称', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'GoodsKeyword', //搜索key 必填
          placeholder: '请输入商品名称/编号',
        },
        {
          name: '生产厂家', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'BrandManufacturerKeyword', //搜索key 必填
          placeholder: '请输入厂家名称',
        },
      ],
      queryParam: {

      },
      model: {},
      table: {
        operateBtns: [

        ], //右上角按钮集合
        rowKey: 'Id',
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批准文号',
          dataIndex: 'ApprovalNumber',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '求购数量',
          dataIndex: 'PurchaseQuantity',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单价',
          dataIndex: 'QuotationPrice',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供货数量',
          dataIndex: 'SupplyQuantity',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '效期优于',
          dataIndex: 'PreferredExpiryDate',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },

      ],
      isInitData: false,
      httpHead: 'P36009',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/PurchaseQuotation/GetQuotationOrderDetailPageListAsync',//列表数据接口
      },
    };
  },
  mounted() { },
  created() {

  },
  methods: {
    moment,
    show(record) {
      this.model = Object.assign({}, record);
      this.title = (this.model.SupplierName || '') + '报价明细'
      this.queryParam.PurchaseQuotationId = this.model.Id
      this.visible = true;
      this.loadData(1)
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
<style scoped>
.time-text {
  position: absolute;
  bottom: 15px;
}
.time-none-text {
  margin-top: 5px;
}
</style>
