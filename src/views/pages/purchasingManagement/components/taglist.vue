<template>
  <div>
    <a-tag
      style="cursor: pointer; padding: 5px 7px; font-size: 14px;margin-bottom: 5px"
      @click="toggleTag(item)"
      v-for="item in tagList"
      :key="item[sign]"
      :closable="tagList.length > 1"
      @close="closeTag(item)"
      :color="item[sign] === activeTag ? 'blue' : ''"
    >
      {{ item.Name }}
    </a-tag>
  </div>
</template>

<script>
export default {
  name: 'tagList',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    // 组件tag唯一标识
    sign: {
      type: String,
      default: 'Id',
    },
    // 当前高亮的组件sign值
    activeTag: {
      default: 1,
    },
  },
  data() {
    return {
      tagList: [],
    }
  },
  mounted() {
    this.tagList = JSON.parse(JSON.stringify(this.list))
  },
  created() {},
  watch: {
    list: {
      handler(val) {
        this.tagList = JSON.parse(JSON.stringify(this.list))
      },
      immediate: true,
      deep: true
    },
  },
  methods: {
    // 切换标签
    toggleTag(item) {
      const sign = this.sign
      this.$emit('update:activeTag', item[sign])
    },
    // 关闭公司标签的回显
    closeTag(item) {
      const sign = this.sign
      // const index = this.tagList.findIndex((f) => f[sign] === item[sign])
      // const preIndex = index - 1
      // if (preIndex < 0) {
      //   this.$emit('update:activeTag', null)
      // } else {
      //   if (this.activeTag == item[sign]) {
      //     this.$emit('update:activeTag', this.tagList[preIndex][sign])
      //   }
      // }
      // this.tagList.splice(index, 1)
      const tagList = JSON.parse(JSON.stringify(this.tagList))
      const activeTag = item[sign]
      const index =  tagList.findIndex(f => f[sign] === activeTag)
        tagList.splice(index, 1)
        this.$emit('update:list', tagList)
        const lg = tagList.length
        // 永远切换到删除后的最后一个供应商
        this.$emit('update:activeTag', tagList[lg - 1][sign])
    },
  },
}
</script>
