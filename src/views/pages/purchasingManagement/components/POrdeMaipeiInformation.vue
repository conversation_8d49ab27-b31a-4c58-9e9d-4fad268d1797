<!--
 * @Author: LP
 * @Description: 采购订单详情-买赔信息
 * @Date: 2023/07/10
-->
<template>
  <a-spin :spinning="loading">
    <!-- 详情展示部分 -->
    <template v-if="dataList && dataList.length > 0">
      <a-card
        :bordered="false"
        :bodyStyle="{ padding: ' 0 16px' }"
        v-for="(item, index) in dataList"
        :key="index"
        class="mb20"
      >
        <a-descriptions :column="4">
          <a-descriptions-item label="买赔单号">{{ item.BuyCompensationNo || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="买赔时间">{{ item.CreateTime || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="买赔金额">¥{{ item.TotalBuyCompensationAmount }}</a-descriptions-item>
          <a-descriptions-item label="买赔状态">{{ item.BuyCompensationStatusStr || ' -- ' }}</a-descriptions-item>
        </a-descriptions>

        <TableView :showCardBorder="true" :tab="tab" :columns="columns" :dataList="item.GoodsItems || []" />
      </a-card>
    </template>
    <a-empty v-else />
  </a-spin>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction } from '@/api/manage'
export default {
  name: 'POrdeMaipeiInformation',
  mixins: [ListMixin],
  components: {},
  props: {
    /**
     * 买赔订单id
     */
    id: {
      type: String,
      required: true,
      default: () => {
        return ''
      },
    },
  },
  data() {
    return {
      loading: false,
      tab: {
        tabTitles: [],
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 60,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '有效期',
          dataIndex: 'ExpirationDate',
          width: 180,
          scopedSlots: { customRender: 'date' },
          ellipsis: true,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          width: 100,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        // {
        //   title: '入库数量',
        //   dataIndex: 'InboundQuantity',
        //   width: 150,
        //   scopedSlots: { customRender: 'number' },
        //   ellipsis: true,
        // },
        {
          title: '含税进价小计',
          dataIndex: 'TotalTaxIncludedPurchasePrice',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '本次买赔数量',
          dataIndex: 'BuyCompensationQuantity',
          width: 150,
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
      ],
      dataList: {},
      httpHead: 'P36009',
      url: {
        info: '/{v}/PurchaseBusiness/QueryPurchaseBuyCompensationRecordList',
      },
    }
  },
  computed: {
    rules() {
      return {}
    },
  },
  mounted() {
    this.loadInfo()
  },
  created() {},
  methods: {
    loadInfo() {
      let that = this
      that.loading = true
      getAction(that.url.info, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.dataList = res.Data || []
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
  },
}
</script>

<style lang="less" scoped>

.mb20 {
  margin-bottom: 30px;
}
</style>
