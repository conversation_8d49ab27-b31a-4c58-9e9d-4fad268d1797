<!--
 * @Author: LP
 * @Description: 采购票折详情和编辑
 * @Date: 2023/07/12
-->
<template>
  <a-spin :spinning="loading || confirmLoading || Loading">
    <a-form-model v-if="model" ref="form" :rules="rules" :model="model">
      <a-row :gutter="10">
        <a-col span="24">
          <TableView
            :showCardBorder="false"
            :tab="tab"
            :columns="columns"
            :dataList="model.GoodsItems || []"
            @actionClick="onActionClick"
          >
            <!-- <template slot="bottomView">
              <a-row style="padding: 20px 16px">
                <a-col span="18">
                  备注:
                  <a-input
                    v-if="isEdit"
                    placeholder="请输入"
                    v-model="model.Remark"
                    style="width: 90%"
                    @change="onReamrkChange"
                    :maxLength="100"
                  />
                  <span v-else>{{ model.Remark }}</span>
                </a-col>
                <a-col span="6" style="text-align: right; font-weight: bold">
                  {{ opType == 3 ? '差价' : '票折' }}金额： {{ total < 0 ? '-' : '' }}¥{{
                    total < 0 ? String(total).replace('-', '') : total
                  }}
                </a-col>
              </a-row>
            </template> -->
          </TableView>
        </a-col>
      </a-row>
      <a-row :gutter="10" class="mt15">
        <a-col span="18">
          <a-form-model-item label="备注" prop="Remark" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input
              v-if="isEdit"
              placeholder="请输入"
              v-model="model.Remark"
              style="width: 100%"
              @change="onReamrkChange"
              :maxLength="100"
            />
            <span v-else>{{ model.Remark || '--' }}</span>
          </a-form-model-item>
        </a-col>
        <a-col span="6" style="text-align: right; font-weight: bold; padding-right: 20px">
          {{ opType == 3 ? '差价' : '票折' }}金额： {{ total < 0 ? '-' : '' }}¥{{
            total < 0 ? String(total).replace('-', '') : total
          }}
        </a-col>
      </a-row>
      <a-row :gutter="10">
        <a-col span="24">
          <a-form-model-item label="关联协议" prop="guanlianxieyi" :labelCol="labelCol" :wrapperCol="wrapperCol" labelAlign="left" style="text-align: right" >
            <a-button type="primary" v-if="opType !== 1" @click="chooseAgreement" :disabled="isEdit? total >= 0: true">选择协议</a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="10">
        <a-col span="24">
          <TableView
            :showCardBorder="false"
            :tab="tab"
            :columns="agreementColumns"
            :dataList="model.AgreementItems || []"
            @actionClick="onAgreementActionClick"
          >
          </TableView>
        </a-col>
      </a-row>
      <a-row :gutter="10" class="mt15">
        <a-col span="24">
          抵扣合计金额：<span class="cred">¥{{
            agreementTotal < 0 ? String(agreementTotal).replace('-', '') : agreementTotal
          }}</span>
        </a-col>
      </a-row>
    </a-form-model>
    <!-- 选择协议弹窗 -->
    <ChooseAgreementsModel ref="ChooseAgreementsModel" @ok="handleOk" />
  </a-spin>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'POrderPiaozheView',
  mixins: [ListMixin, EditMixin],
  components: {},
  props: {
    /**
     * 商品数据
     */
    model: {
      type: Object,
      required: true,
      default: () => {
        return { Remark: '', GoodsItems: [], AgreementItems: [] }
      },
    },
    /**
     * 类型 1 票折列表-详情 2 票折列表-编辑  3 采购列表新增
     */
    opType: {
      type: Number,
      default: () => {
        return 1
      },
    },
    Loading: {
      type: Boolean,
      default: () => {
        return false
      },
    },
  },
  watch: {
    model: {
      handler(newVal, oldVal) {
        if (!oldVal || (newVal && newVal.GoodsItems && newVal.GoodsItems.length > 0)) {
          this.initGoodsItems()
        }
        this.getTotalAmount()
        this.getAgreementTotalAmount()
      },
      deep: true,
    },
  },
  data() {
    return {
      loading: false,
      confirmLoading: false,
      tab: {
        tabTitles: [],
        showPagination: false,
      },
      inputChangeing: false,
      total: 0,
      agreementTotal: 0,
      dataList: {},
      httpHead: 'P36009',
      ErpPCCostKey: '',
      pzslKey: '',
      xhsjjKey: '',
      pzxjKey: '',//票折小计金额
      hsjjcjxjKey: '',
      labelCol: {
        xs: { span: 2 },
        sm: { span: 2 },
      },
      wrapperCol: {
        xs: { span: 22 },
        sm: { span: 22 },
      },
      url: {},
      
    }
  },
  computed: {
    rules() {
      return {
        Remark: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (this.model.Remark) {
                this.validStrMaxLength(rule, this.model.Remark, callback, 100)
              } else {
                callback()
              }
            },
          },
        ],
      }
    },
    isEdit() {
      return this.opType == 2 || this.opType == 3
    },
    columns() {
      const actionList = this.isEdit
        ? [
            {
              title: '操作',
              dataIndex: 'action',
              align: 'center',
              width: 80,
              fixed: 'right',
              scopedSlots: { customRender: 'action' },
              actionBtns: [
                {
                  name: '移除',
                  id: '5d529866-c114-4253-a893-555ff924c4cf',
                },
              ],
            },
          ]
        : []
      return [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          fixed: this.isEdit ? 'left' : '',
          width: 150,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 150,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 200,
        },
        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '有效期',
          dataIndex: 'ExpirationDate',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '批次号',
          dataIndex: 'BatchNumber',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '含税金额',
          dataIndex: this.opType == 3 ? 'TotalTaxIncludedPurchasePrice' : 'TotalAdjustedTaxIncludedPurchasePrice',
          width: 120,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: 'erp批次在库成本',
          dataIndex: this.ErpPCCostKey,
          width: 150,
          fixed: this.isEdit ? 'right' : '',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '入库数量',
          dataIndex: 'InboundQuantity',
          width: 100,
          fixed: this.isEdit ? 'right' : '',
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        {
          title: '票折数量',
          dataIndex: this.pzslKey,
          width: 100,
          fixed: this.isEdit ? 'right' : '',
          precision: 0, //数值精度
          // onInputChange: this.onInputChange,
          // scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'number' },
          scopedSlots: { customRender: this.isEdit ? 'number' : 'number' },
          ellipsis: true,
        },
        // {
        //   title: '新含税进价',
        //   dataIndex: this.xhsjjKey,
        //   width: 120,
        //   fixed: this.isEdit ? 'right' : '',
        //   onInputChange: this.onInputChange,
        //   precision: 6,
        //   scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'price' },
        //   ellipsis: true,
        // },
        // 含税进价差价小计改名为票折小计金额，保留小数点后两位
        {
          title: '票折小计金额',
          dataIndex: this.pzxjKey,
          width: 130,
          fixed: this.isEdit ? 'right' : '',
          precision: 2,
          min: -Infinity,
          // scopedSlots: { customRender: 'price' },
          onBlur: this.onBlur,
          scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'price' },
          ellipsis: true,
        },
        // 含税进价差价改名为差额，差额=票折小计金额/票折数量，差额保留小数点后六位
        {
          title: '差额',
          dataIndex: 'TaxIncludedPurchasePriceDifference',
          width: 110,
          precision: 6,
          min: -Infinity,
          scopedSlots:  { customRender: this.isEdit ? 'inputNumber' : 'price' },
          disabled:true,
          fixed: this.isEdit ? 'right' : '',
          ellipsis: true,
        },
        
        ...actionList,
      ]
    },
    agreementColumns() {
      const actionList = this.opType !== 1
        ? [
            {
              title: '操作',
              dataIndex: 'action',
              align: 'center',
              width: 80,
              fixed: 'right',
              scopedSlots: { customRender: 'action' },
              actionBtns: [
                {
                  name: '移除',
                  id: '5d529866-c114-4253-a893-555ff924c4cf',
                },
              ],
            }
          ]
        : []
      return [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '协议类型',
          dataIndex: 'AgreementTypeStr',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方类型',
          dataIndex: 'PartyFirstTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收返利',
          dataIndex: 'TotalRebateAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '取消返利',
          dataIndex: 'CancelRebateAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '增加返利',
          dataIndex: 'TotalAddRebateAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '已认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '抵扣金额',
          dataIndex: 'DeductionAmount',
          width: 130,
          precision: 2,
          min: 0,
          // scopedSlots: { customRender: 'price' },
          onBlur: this.handleOnBlur,
          scopedSlots:  { customRender: this.isEdit ? 'inputNumber' : 'price' },
          ellipsis: true,
        },
        ...actionList,
      ]
    } 
  },
  mounted() {
    this.pzslKey = 'Quantity' //票折数量
    this.ErpPCCostKey = (this.opType == 3 || this.opType == 1) ? 'ErpPCCost' : 'LastErpPCCost' //erp批次在库成本
    this.xhsjjKey = 'DiscountedTaxIncludedPurchasePrice' //this.opType == 3 ? 'DiscountedTaxIncludedPurchasePrice' : 'AdjustedTaxIncludedPurchasePrice'
    // this.pzxjKey = 'TotalTaxIncludedPurchasePriceDifference' 
    //   this.opType == 3 ? 'TaxIncludedPurchasePriceDifferenceTotal' : 'TotalTaxIncludedPurchasePriceDifference'
    this.pzxjKey = 'TotalTaxIncludedPurchasePriceDifference' //票折小计金额
  },
  created() {},
  methods: {
    initGoodsItems() {
      if (this.opType == 3) {
        if (this.model && this.model.GoodsItems) {
          this.model.GoodsItems.forEach((x) => {
            x[this.pzslKey] = x.InboundQuantity - x.ExistsRefundCount
          })
        }
      }
    },
    getData(callback) {
      if (this.model.GoodsItems.length == 0) {
        this.$message.warning('无商品数据，无须提交')
        callback && callback()
        return
      }
      this.$refs.form.validate((err, values) => {
        console.log('err', err)
        if (err) {
          let inputList = this.model.GoodsItems.filter((x) => {
            return x[this.pzslKey] && x[this.pzxjKey] 
          })
          let hasNoDeductionAmount = this.model.AgreementItems.filter(item => item.DeductionAmount===undefined || item.DeductionAmount==='')

          if (inputList && inputList.length == 0) {
            this.$message.warning('请至少完善一个商品的录入信息')
            callback && callback()
          } else if (hasNoDeductionAmount.length) {
            this.$message.warning('请填写协议抵扣金额！')
            callback && callback()
          } else if (this.agreementTotal > Math.abs(this.total)) {
            this.$message.warning('抵扣合计金额需小于等于差价金额！')
            callback && callback()
          } else {
            let data = JSON.parse(JSON.stringify(this.model))
            data.GoodsItems = inputList
            // if (this.opType == 2) {
            //   data.GoodsItems.forEach((x) => {
            //     x.DiscountedTaxIncludedPurchasePrice = x.AdjustedTaxIncludedPurchasePrice
            //   })
            // }
            callback && callback(data)
          }
        } else {
          callback && callback()
        }
      })
    },
    onActionClick(record, key, index) {
      if (key == '移除') {
        this.model.GoodsItems.splice(index, 1)
      }
    },
    onAgreementActionClick(record, key, index) {
      if (key == '移除') {
        let data = [...this.model.AgreementItems]
        data.splice(index, 1)
        this.$set(this.model, 'AgreementItems', data)
        this.$forceUpdate()
      }
    },
    /**
     * 获取总金额
     */
    getTotalAmount() {
      let sum = 0

      if (this.model.GoodsItems) {
        this.model.GoodsItems.forEach((x) => {
          // if (x[this.hsjjcjxjKey] >= 0 || x[this.hsjjcjxjKey] < 0) {
          //   sum += x[this.hsjjcjxjKey]
          // }
          if (x[this.pzxjKey] >= 0 || x[this.pzxjKey] < 0) {
            sum += x[this.pzxjKey]
          }
        })
      }
      this.total = this.getAmountOfMoney(sum)
    },
    onBlur(val, key, record, index) {
      let num = val || 0
      // console.log(key, val, record, index)
      if (key == this.pzslKey) {
        if (val && val > record.InboundQuantity) {
          if (this.inputChangeing) {
            return
          }
          this.inputChangeing = true
          this.$message.warning('[' + record.ErpGoodsName + ']的票折数量不能大于入库数量')
          record[this.pzslKey] = record.InboundQuantity
          return
        }
        //输入票折数量则计算含税进价差价小计 公式（含税进价差价小计=含税进价差价*调价数量）
        if (record.TaxIncludedPurchasePriceDifference != null && record.TaxIncludedPurchasePriceDifference != '') {
          // let newCJTotal = record.TaxIncludedPurchasePriceDifference * num
          let newCJTotal =  this.numMath(record.TaxIncludedPurchasePriceDifference,num,'*',6)
          record[this.hsjjcjxjKey] = newCJTotal
        }

        this.inputChangeing = false
      } else if (key == this.pzxjKey) {
        // 差额=票折小计金额/票折数量，差额保留小数点后六位
        // if (record[this.pzxjKey] >= 0) {
          // let newCJTotal = record[this.pzxjKey] / (record[this.pzslKey] || 0)
          let newCJTotal = this.numMath(record[this.pzxjKey],(record[this.pzslKey] || 0),'/',6)
          // console.log('差额      ',newCJTotal)
          record['TaxIncludedPurchasePriceDifference'] = newCJTotal
        // }
        // if (record['TaxIncludedPurchasePriceDifference'] >= 0) {
          let Total = record.TaxIncludedPurchasePriceDifference 
          record[this.hsjjcjxjKey] = Total
        // }
      }
      // } else if (key == this.xhsjjKey) {
      //   //输入新含税进价则计算含税差价 公式（含税进价差价=新含税进价-含税进价）
      //   if (record.TaxIncludedPurchasePrice != null && record.TaxIncludedPurchasePrice != '') {
      //     let newCJ = num > 0 ? num - record.TaxIncludedPurchasePrice : 0
      //     record.TaxIncludedPurchasePriceDifference = newCJ
      //   }
      //   if (record[this.pzslKey] >= 0) {
      //     let newCJTotal = record.TaxIncludedPurchasePriceDifference * record[this.pzslKey]
      //     record[this.hsjjcjxjKey] = newCJTotal
      //   }
      // }
      // console.log(record)
      this.$forceUpdate()
      //计算调价金额
      this.getTotalAmount()
    },

    onReamrkChange() {
      // if (this.getStrMaxLength(this.model.Remark) > 100) {
      //   this.model.Remark = this.model.Remark.substring(0, 99)
      //   this.$message.warning('备注最多100个字符')
      // }
      this.$forceUpdate()
    },
    chooseAgreement() {
      this.$refs.ChooseAgreementsModel.show()
    },
    handleOk(selectedRowDataKeys, selectedRowData) {
      // 协议去重
      let selectedData = this.model.AgreementItems.filter(item => !selectedRowDataKeys.includes(item.AgreementId)).concat(selectedRowData)
      this.$set(this.model, 'AgreementItems', selectedData)
      this.getAgreementTotalAmount()
      this.$forceUpdate()
    },
    handleOnBlur(val, key, record, index) {
      // console.log('handleOnBlur', val, key, record, index)
      if(record.AgreementType === 1 && record.DeductionAmount > record.RemainingAmount) {
        this.$message.warning('年度协议的抵扣金额只能小于等于未认款金额!')
        return
      }
      //计算折扣合计金额
      this.getAgreementTotalAmount()
    },
    getAgreementTotalAmount() {
      let sum = 0
      if (this.model.AgreementItems) {
        this.model.AgreementItems.forEach((x) => {
          if (x.DeductionAmount && x.DeductionAmount >= 0) {
            sum += x.DeductionAmount
          }
        })
      }
      this.agreementTotal = this.getAmountOfMoney(sum)
    }
  },
}
</script>

<style lang="less" scoped>

.mb20 {
  margin-bottom: 30px;
}
</style>
