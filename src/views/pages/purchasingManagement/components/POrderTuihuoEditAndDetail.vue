<!--
 * @Author: LP
 * @Description: 采购退货详情和编辑
 * @Date: 2023/07/10
-->
<template>
  <a-spin :spinning="loading || confirmLoading">
    <a-alert
      :message="'驳回原因：' + (model.AuditOpinion || '--')"
      type="warning"
      show-icon
      v-if="isEdit && model.AuditStatus == 3"
      class="mb10"
    />
    <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }">
      <a-descriptions :column="7">
        <a-descriptions-item>
          <div class="flc hcenter">
            <a-icon
              :type="getAuditIcon(model.AuditStatus)"
              theme="twoTone"
              :two-tone-color="getAuditColor(model.AuditStatus)"
              style="font-size: 32px"
            />
            <span :style="{ color: getAuditColor(model.AuditStatus) }">{{ model.AuditStatusStr || '--' }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">供应商名称</span>
          <div>{{ model.SupplierName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">退货单号</span>
          <div>{{ model.RefundOrderNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">订单编号</span>
          <div>{{ model.PurchaseOrderNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">退货金额</span>
          <div>¥{{ model.TotalRefundAmount }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建人</span>
          <div>{{ model.CreateByName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>

    <div style="height: 15px; background-color: #f0f2f5"></div>

    <a-card :bodyStyle="{ padding: '0 0 16px 0' }">
      <POrderTuihuoView ref="POrderTuihuoView" :opType="isEdit ? 2 : 1" :model="model" />
    </a-card>
  </a-spin>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, postAction } from '@/api/manage'
import YYLButton from '@/components/yyl/business/YYLButton.vue'
export default {
  name: 'POrderTuihuoEditAndDetail',
  mixins: [ListMixin],
  components: { YYLButton },
  props: {
    /**
     * 订单id
     */
    id: {
      type: String,
      required: true,
      default: () => {
        return ''
      },
    },
    /**
     * 是否是编辑 默认false-详情
     */
    isEdit: {
      type: Boolean,
      default: () => {
        return false
      },
    },
  },
  data() {
    return {
      loading: false,
      confirmLoading: false,
      labelCol: {
        xs: { span: 2 },
        sm: { span: 2 },
      },
      wrapperCol: {
        xs: { span: 22 },
        sm: { span: 22 },
      },
      tab: {
        tabTitles: [],
      },
      dataList: {},
      httpHead: 'P36009',
      model: {},
      url: {
        detail: '/{v}/PurchaseBusiness/GetPurchaseRefundDetail',
        save: '/{v}/PurchaseBusiness/EditPurchaseRefund',
      },
    }
  },
  computed: {},
  mounted() {
    this.loadDetail()
  },
  created() {},
  methods: {
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    onSubmitAuditClick(IsDraft,callback) {
      if (this.$refs.POrderTuihuoView) {
        this.$refs.POrderTuihuoView.getData((data) => {
          if (data) {
            data.IsDraft = IsDraft
            this.saveData(data, callback)
          }
        })
      }
    },
    saveData(data, callback) {
      let that = this
      that.confirmLoading = true
      callback && callback(that.confirmLoading)
      postAction(
        that.url.save,
        {
          ...data,
          PurchaseOrderId: data.PurchaseOrderId,
          PurchaseRefundId: data.PurchaseRefundId,
        },
        that.httpHead
      )
        .then((res) => {
          if (res.IsSuccess) {
            if(data.IsDraft){
              that.$message.success('操作成功！')
              that.goBack()
              that.confirmLoading = false
              return
            }
            that.createApproval(res.Data, callback)
          } else {
            that.$message.error(res.Msg)
            that.confirmLoading = false
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          callback && callback(that.confirmLoading)
        })
    },
    /**
     * 创建审核实例
     */
    createApproval(bNo, callback) {
      let that = this
      let params = {
        BussinessNo: bNo,
        Scenes: 11,
        OpratorId: that.getLoginUserId(),
      }
      if (!that.confirmLoading) {
        that.confirmLoading = true
      }
      postAction('/{v}/ApprovalWorkFlowInstance/CreateApproval', params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.goBack()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          that.confirmLoading = false
          callback && callback(that.confirmLoading)
        })
    },
  },
}
</script>

<style lang="less" scoped>

.mb20 {
  margin-bottom: 30px;
}
</style>
