<!--
 * @Author: LP
 * @Description: 预计到货列表
 * @Date: 2023/07/08
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
    />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'ExpectedArrivalList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierName',
        },
        {
          name: '订单编号',
          type: SEnum.INPUT,
          key: 'PurchaseOrderNo',
        },
        {
          name: '送货方式',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/Global/GetListDictItem',
          key: 'DeliveryTypeId',
          params: { groupPY: 'songhfs' },
          dataKey: { name: 'ItemValue', value: 'Id' },
        },
        {
          name: '预计到货日期',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'ArrivalTimeStart',
          keyEnd: 'ArrivalTimeEnd',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        curStatus: '',
        tabStatusKey: '', //标签的切换关键字
        tabTitles: ['预计到货列表'],
        operateBtns: [],
      },
      httpHead: 'P36009',
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '送货方式',
          dataIndex: 'DeliveryTypeStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '预期到货日期',
          dataIndex: 'ExpectedArrivalDate',
          width: 150,
          scopedSlots: { customRender: 'date' },
          ellipsis: true,
        },
        {
          title: '品种数量',
          dataIndex: 'DrugCount',
          width: 150,
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },

        {
          title: '总数量',
          dataIndex: 'TotalCount',
          width: 150,
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },

        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: '90149fc4-d496-499d-b783-22d8261e7621',
              isShow: (record) => {
                return true
              },
            },
          ],
        },
      ],
      url: {
        list: '/{v}/PurchaseOrder/GetPagePurchaseOrdeArrival',
        tabCount: '',
        setValidUrl: '',
      },
    }
  },
  computed: {},
  created() {},
  methods: {
    onOperateClick(key) {},
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('ExpectedArrivalDetail', { id: record.Id })
      }
    },
  },
}
</script>
<style scoped>

</style>
