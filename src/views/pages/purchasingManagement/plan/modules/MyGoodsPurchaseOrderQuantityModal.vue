<!-- 采购单数量 -->
<template>
  <a-modal
    :title="title"
    :width="1100"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :footer="null"
  >
    <a-spin :spinning="confirmLoading">
      <!-- 搜索 -->
      <SimpleTable
        ref="table"
        :tab="tab"
        :isTableInitData="isTableInitData"
        :queryParam="queryParam"
        :columns="columns"
        :linkHttpHead="linkHttpHead"
        :linkUrlType="linkUrlType"
        :linkUrl="linkUrl"
      />
    </a-spin>

  </a-modal>
</template>
<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'MyGoodsPurchaseOrderQuantityModal',
  components: { JEllipsis },
  mixins: [SimpleMixin, EditMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      title: '',
      tab: {},
      // 表头
      columns: [
        {
          title: '计划状态',
          dataIndex: 'AuditStatusStr',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '是否生成订单',
          dataIndex: 'IsGenerateOrder',
          ellipsis: true,
          width: 150,
          customRender: (t, r, i) => {
            return t ? '是' : '否'
          }
        },
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '计划编号',
          dataIndex: 'SchemeNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '采购计划数量',
          dataIndex: 'PurchaseQuantity',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '入库数量',
          dataIndex: 'InboundQuantity',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '备注',
          dataIndex: 'Remarks',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '计划创建时间',
          dataIndex: 'CreateTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '预到货日期',
          dataIndex: 'ExpectedArrivalDate',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        }
      ],
      queryParam: {},
      ErpGoodsId: null,
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36009',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/PurchaseScheme/GetPurchaseSchemeDetailsByErpGoodsId'
      }
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(record) {
      this.title = `${record['ErpGoodsName']}在途采购计划明细`
      // console.log(data)
      this.ErpGoodsId = record['ErpGoodsId']
      this.queryParam.ErpGoodsId = record['ErpGoodsId']
      this.visible = true
      this.$nextTick(() => {
        if (this.$refs.table) this.$refs.table.loadDatas(1, this.queryParam)
      })
    },

    searchQuery(queryParam, type) {
      queryParam['ErpGoodsId'] = this.ErpGoodsId
      this.$refs.table.loadDatas(1, queryParam)
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>
<style scoped></style>
