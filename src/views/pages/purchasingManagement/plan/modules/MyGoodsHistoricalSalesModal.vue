<!-- 历史销量 -->
<template>
  <a-modal
    :title="title"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :footer="null"
  >
    <a-spin :spinning="confirmLoading">
      <!-- <div
        class="echart"
        id="mychart"
        :style="myChartStyle"
      ></div> -->
      <div
        ref="verticalCharts"
        :style="{ height: contentHeight }"
      ></div>
    </a-spin>

  </a-modal>
</template>
<script>
import * as echarts from 'echarts'
import moment from 'moment'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'MyGoodsHistoricalSalesModal',
  components: { JEllipsis },
  mixins: [SimpleMixin, EditMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      title: '',
      ErpGoodsId: null,
      xData: [], // ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'], //横坐标
      yData: [], //[23, 24, 18, 25, 27, 28, 25], //数据
      myChartStyle: { float: 'left', width: '100%', height: '400px' }, //图表样式
      linkHttpHead: 'P36009',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        getGoodsHistorySalesList: '/{v}/PurchaseGoods/GetGoodsHistorySalesList',
      },
      verticalCharts: null,
      contentHeight: '',
      windowHeight: window.innerHeight,
      verticalScrollInterval: null,
    }
  },
  watch: {
    // 监听 屏幕高度
    windowHeight: {
      depp: true,
      immediate: true,
      handler(newVal) {
        if (newVal >= 1020) {
          this.contentHeight = Math.floor((newVal - 450) / 2) + 'px'
        } else if (newVal < 1020) {
          this.contentHeight = 350 + 'px'
        }
      },
    },
  },
  computed: {},
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  created() {},
  methods: {
    moment,
    show(record, title = '') {
      this.title = `${record['ErpGoodsName']}近30天销售明细`
      // console.log(data)
      this.ErpGoodsId = record['ErpGoodsId']
      this.queryParam.erpGoodsId = record['ErpGoodsId']
      this.visible = true
      this.getGoodsHistorySalesList()
    },
    handleResize() {
      this.windowHeight = window.innerHeight
    },
    getGoodsHistorySalesList() {
      this.confirmLoading = true
      getAction(this.linkUrl.getGoodsHistorySalesList, this.queryParam, this.linkHttpHead).then((res) => {
        if (res.IsSuccess) {
          if ((res.Data || []).length > 0) {
            this.xData = res.Data.map((v) => (v.Date ? moment(v.Date).format('YYYY-MM-DD') : ' - '))
            this.yData = res.Data.map((v) => v.SalesCount || 0)
          }
          this.$nextTick(() => {
            this.initEcharts()
          })
        } else {
          this.$message.warning(res.Msg)
        }
      }).finally(()=>{
         this.confirmLoading = false
      })
    },
    initEcharts() {
      const _this = this
      // 基本柱状图
      const option = {
        title: {
          top: '5%',
          left: '3%',
          text: '',
          textStyle: {
            fontWeight: 550,
            fontSize: 14, //主题文字字体大小，默认为18px
            color: '#000',
          },
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '6%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(0, 0, 0, 0.3)', // 设置阴影颜色
              opacity: 0.2, // 设置阴影透明度
            },
          },
          formatter: '' + '{b} <br> {c} ',
        },
        legend: {
          itemWidth: 8,
          itemHeight: 8,
          // itemGap: 50,
          top: -5,
          formatter: (name) => {
            return name
          },
          textStyle: {
            fontSize: 14,
            color: '#000',
            padding: 5,
          },
        },
        dataZoom: [
          {
            show: false, //是否显示滑动条，不影响使用
            type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
            startValue: 0, // 从头开始。
            endValue: 30, // 一次性展示30个。
          },
        ],
        xAxis: {
          data: _this.xData,
          axisTick: {
            alignWithLabel: true,
          },
          axisLabel: {
            rotate: 45, // 将标签文字以45度倾斜展示
          },
        },
        yAxis: { type: 'value' },
        series: [
          {
            type: 'bar', //形状为柱状图
            data: _this.yData,
            barWidth: 15, // 柱子宽度为20
            smooth: true,
            lineStyle: {
              normal: {
                color: '#409EFF',
              },
            },
            itemStyle: {
              normal: {
                color: '#409EFF',
              },
            },
            label: {
              // 设置label属性
              normal: {
                show: true, // 显示标签
                position: 'top', // 在右侧显示
              },
            },
          },
        ],
      }
      _this.verticalCharts = echarts.init(_this.$refs.verticalCharts)
      // myChart.setOption(option)
      // //随着屏幕大小调节图表
      // window.addEventListener('resize', () => {
      //   myChart.resize()
      // })
      // 设置定时滚动
      // _this.verticalEcharts(option, _this.xData)
      // 鼠标移入停止滚动
      // _this.verticalCharts.on('mouseover', function () {
      //   if (_this.verticalScrollInterval) {
      //     clearInterval(_this.verticalScrollInterval)
      //   }
      // })
      // 鼠标移出继续滚动
      // _this.verticalCharts.on('globalout', function () {
      //   _this.verticalEcharts(option, _this.xData)
      // })

      // 鼠标移出继续滚动

      _this.verticalCharts.setOption(option)
      // 根据缩放 改变宽度
      window.addEventListener('resize', () => {
        _this.verticalCharts.resize()
      })
    },
    // 垂直柱状图 滚动
    verticalEcharts(vertical, xData) {
      const _this = this
      if (this.verticalScrollInterval) {
        clearInterval(this.verticalScrollInterval)
      }
      this.verticalScrollInterval = setInterval(() => {
        if (vertical.dataZoom[0].endValue == xData.length - 1) {
          vertical.dataZoom[0].startValue = 0
          vertical.dataZoom[0].endValue = 29
        } else if (vertical.dataZoom[0].endValue < xData.length) {
          vertical.dataZoom[0].endValue = vertical.dataZoom[0].endValue + 1
          vertical.dataZoom[0].startValue = vertical.dataZoom[0].startValue + 1
        }
        _this.verticalCharts.setOption(vertical)
      }, 1500)
    },
    handleCancel() {
      this.visible = false
      this.queryParam = {}
      this.xData = []
      this.yData = []
      if (this.verticalScrollInterval) {
        clearInterval(this.verticalScrollInterval)
      }
    },
  },
}
</script>
<style scoped></style>
