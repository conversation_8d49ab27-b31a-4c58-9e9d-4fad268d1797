<!-- 历史入库 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true" :footer="null">
    <a-spin :spinning="confirmLoading">
      <!-- 数据列表 -->
      <a-table :bordered="true" ref="table" size="middle" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length === 0 ? false : '450px', x: '100%' }">

        <!-- 字符串超长截取省略号显示-->
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="'' + text" :length="50" v-if="typeof text == 'number'" />
          <j-ellipsis :value="text" v-else :length="50" />
        </span>

      </a-table>
      <!--/ 数据列表 -->
    </a-spin>

  </a-modal>
</template>
<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'MyGoodsHistoricalEntryModal',
  components: { JEllipsis },
  mixins: [ListMixin, EditMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      title: '',
      // 表头
      columns: [
        {
          title: '入库日期',
          dataIndex: 'WarehouseInTime',
          ellipsis: true,
          width: 150,
          customRender: (text, record, index) => {
            const obj = {
              children: (<j-ellipsis value={String(text) || ''} />),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '入库批号',
          dataIndex: 'ProductionBatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '入库批次号',
          dataIndex: 'BatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '入库数量',
          dataIndex: 'InboundQuantity',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        }
      ],
      queryParam: {},
      ErpGoodsId: null,
      isInitData: false, //是否自动加载
      httpHead: 'P36009',
      url: {
        list: '/{v}/PurchaseScheme/GetPageHistoryInventoryDateList'
      }
    }
  },
  computed: {},
  mounted() { },
  created() { },
  methods: {
    show(record) {
      this.title = `${record['ErpGoodsName']}历史入库明细`
      this.ErpGoodsId = record['ErpGoodsId']
      this.queryParam.ErpGoodsId = record['ErpGoodsId']
      this.visible = true
      this.$nextTick(() => {
        this.loadData(1)
      })
    },
    loadBefore() {
      this.queryParam.ErpGoodsId = this.ErpGoodsId
    },
    loadAfter(data) {
      let arr = []
      if (data.length) {
        arr = this.getListRowSpanData(data, '', 'WarehouseInTime', 'rowSpan') || []
      }
      this.dataSource = arr
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>
<style scoped></style>
