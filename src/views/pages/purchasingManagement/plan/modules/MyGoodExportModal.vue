<template>
  <a-modal v-model="visible" title="导出" @ok="handleOk">
    <a-checkbox-group v-model="exportList" @change="onChange">
      <a-row style="width: 280px;">
        <a-col :span="12">
          <a-checkbox value="1">
            我的商品
          </a-checkbox>
        </a-col>
        <a-col :span="12">
          <a-checkbox value="2">
            商品库存
          </a-checkbox>
        </a-col>
      </a-row>
    </a-checkbox-group>
  </a-modal>
</template>

<script>
// 我的商品导出弹窗
export default {
  name: 'MyGoodExportModal',
  data() {
    return {
      visible: false,
      exportList: [],
    }
  },
  methods: {
    show() {
      this.exportList = []
      this.visible = true
    },
    onChange(e) {
      this.exportList = e || []
    },
    handleOk() {
      if (this.exportList.length == 0) {
        this.$message.warning('请选择导出内容')
        return
      }
      this.$emit('ok', this.exportList)
      this.visible = false
    }
  },

}
</script>

<style>
</style>