<!-- 在途库存明细 -->
<template>
    <a-modal :title="title" :width="1100" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :footer="null" cancelText="关闭" :destroyOnClose="true">
      <div>
        <a-spin :spinning="confirmLoading">
          <SimpleSearchArea ref="SimpleSearchArea" :queryCol="8" :searchInput="searchInput" @searchQuery="searchQuery" />
          <SimpleTable ref="table" :linkHttpHead="linkHttpHead" @getListAmount="getAmount" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab :cardBordered="false" :tab="tab" :queryParam="queryParam" :columns="columns" isHandleTableChange :isTableInitData="isTableInitData" @operate="operate" @handleTableChange="handleTableChange" >
            <div style="margin-top:5px;" slot="bottomBtn">
                <span style="margin-right: 15px;">合计在途库存：{{getTotalGoodsInTransitInventory|| 0}}</span>
              </div>
            </SimpleTable>
        </a-spin>
      </div>
  
    </a-modal>
  </template>
  
  <script>
  import moment from "moment";
  import { EditMixin } from '@/mixins/EditMixin'
  const JEllipsis = () => import('@/components/jeecg/JEllipsis')
  import { getAction, putAction, postAction } from "@/api/manage";
  export default {
    name: "PlanTotalInTransitCountModal",
    components: {
      JEllipsis
    },
    mixins: [EditMixin],
    data() {
      return {
        title: "在途库存",
        visible: false,
        confirmLoading: false,
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          {
            name: '订单编号',
            type: 'input',
            value: '',
            key: 'PurchaseOrderNo',
            defaultVal: '',
            placeholder: '请输入采购订单编号',
          },
          {
            name: '创建时间',
            type: 'timeInput',
            value: '',
            key: ['CreateTimeBegin', 'CreateTimeEnd'],
            rangeDate: [],
            placeholder: ['开始日期', '结束日期'],
            }
        ],
        tab: {
          bordered: true, //是否显示表格的边框
          operateBtn: [
  
          ],
          hintArray: [],
          status: '',
          statusKey: '', //标签的切换关键字
          statusList: [],
        },
        columns: [
          {
            title: '订单编号',
            dataIndex: 'PurchaseOrderNo',
            width: 180,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建人',
            dataIndex: 'CreateByName',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '订单数量',
            dataIndex: 'OrderQuantity',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '入库数量',
            dataIndex: 'InboundQuantity',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '创建时间',
            dataIndex: 'CreateTime',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '在途库存',
            dataIndex: 'InTransitInventory',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
            sorter: true,
          },
        ],
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 },
        },
        model: {},
        getTotalGoodsInTransitInventory:0,
        queryCol: 6,
        queryParam: {},
        isTableInitData: false, //是否自动加载
        linkHttpHead: 'P36009',
        linkUrlType: 'POST', //请求方式
        linkUrl: {
          list: '/{v}/PurchaseGoods/QueryGoodsInTransitInventory',
          listAmountType: 'POST',
          listAmount: '/{v}/PurchaseGoods/GetTotalGoodsInTransitInventory',
        },
  
      };
    },
    mounted() { },
    created() { },
    methods: {
      moment,
      show(record) {
        this.model = record || {}
        this.visible = true;
        this.queryParam = {
          ErpGoodsId: record.ErpGoodsId,
        }
        this.$nextTick(() => {
            // console.log(this.queryParam,this.$refs.SimpleSearchArea.queryParam)
          this.$refs.table.loadDatas(1,this.queryParam)
          // if(this.$refs.SimpleSearchArea) this.$refs.SimpleSearchArea.resetQueryParam()
        })
      },
      // 获取合计在途库存
      getAmount(data){
        // console.log(data)
        this.getTotalGoodsInTransitInventory = data || 0
      },
      searchQuery() {
        this.queryParam = {
          ErpGoodsId: this.queryParam.ErpGoodsId,
          ...this.$refs.SimpleSearchArea.queryParam,
        }
        this.$refs.table.loadDatas(1,this.queryParam)
      },
      // 列表操作
      operate(record, type) {
  
      },
      // 分页切换事件
      handleTableChange() {
        this.$refs.table.loadDatas(null,this.queryParam)
      },
      // 确定
      handleOk() {
  
      },
      // 关闭
      handleCancel() {
        this.close();
      },
      close() {
        this.$emit("close");
        this.visible = false;
      },
    },
  };
  </script>
  <style scoped>
  .info-box span {
    font-size: 14px;
    margin-right: 10px;
  }
  </style>
  