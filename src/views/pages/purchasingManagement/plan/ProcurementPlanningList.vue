<!--
 * @Author: LP
 * @Description: 采购计划
 * @Date: 2023/07/25
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
    />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, postAction,putAction } from '@/api/manage'
import moment from 'moment'

export default {
  name: 'ProcurementPlanningList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierName',
        },
        {
          name: '企业类别',
          type: SEnum.ENUM,
          key: 'SupplierType',
          dictCode: 'EnumSupplierType',
        },
        {
          name: '计划编号',
          type: SEnum.INPUT,
          key: 'SchemeNo',
        },
        {
          name: '付款方式',
          type: SEnum.ENUM,
          key: 'PaymentMode',
          dictCode: 'EnumPurchasePaymentMode',
        },
        {
          name: '送货方式',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/Global/GetListDictItem',
          key: 'DeliveryTypeId',
          params: { groupPY: 'songhfs' },
          dataKey: { name: 'ItemValue', value: 'Id' },
        },
        {
          name: '审核状态',
          type: SEnum.ENUM,
          key: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
        },
        {
          name: '是否生成订单',
          type: SEnum.SELECT,
          key: 'IsOrderGenerated',
          items: [
            { label: '是', value: true },
            { label: '否', value: false },
          ],
        },

        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '商品名称',
          type: SEnum.INPUT,
          key: 'GoodsName',
          placeholder: '名称/拼音码/编号',
        },
        {
          name: '计划类型',
          type: SEnum.ENUM,
          key: 'OrderType',
          dictCode: 'EnumPurchasePlanType',
        },
        {
          name: '责任人',
          type: SEnum.INPUT,
          key: 'OwnerByName',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        curStatus: '',
        tabStatusKey: '', //标签的切换关键字
        tabTitles: ['采购计划列表'],
        operateBtns: [
          {
            name: '导出',
            type: 'primary',
            key: '导出',
            loading: false,
            id: 'da79d392-f88d-40c8-aa83-7aab4e0fe99b',
          },
          {
            name: '新增含特计划',
            type: 'primary',
            icon: 'plus',
            key: '新增含特计划',
            id: 'e04b9ef8-d9e4-4b6a-8978-de854a9b0fc6',
          },
          {
            name: '新增计划',
            type: 'primary',
            icon: 'plus',
            key: '新增计划',
            id: 'df4fb6d4-9ce6-4776-bfa8-5f4db13f283e',
          },
        ],
        rowKey:'Id',
        scrollX: 2150
      },
      httpHead: 'P36009',
      url: {
        list: '/{v}/PurchaseScheme/GetPagePurchaseSchemeAsync',
        delete: '/{v}/PurchaseScheme/Delete',
        createOrder: '/{v}/PurchaseScheme/GeneratePurchaseOrder',
        cancelOrder: '/{v}/PurchaseScheme/RevokeAudit',//撤回
      },
    }
  },
  computed: {
    columns() {
      return [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 180,
          ellipsis: true,
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 100,
          fixed: 'left',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 180,
          fixed: 'left',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '计划编号',
          dataIndex: 'SchemeNo',
          // width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '计划类型',
          dataIndex: 'OrderTypeStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '企业类别',
          dataIndex: 'SupplierTypeStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '送货方式',
          dataIndex: 'DeliveryMethodStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '计划金额',
          dataIndex: 'OrderAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '预到货日期',
          dataIndex: 'ExpectedArrivalDate',
          width: 150,
          scopedSlots: { customRender: 'date' },
          ellipsis: true,
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 150,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '是否生成订单',
          dataIndex: 'IsGenerateOrder',
          width: 150,
          scopedSlots: { customRender: 'bool' },
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: 'fcebbb28-977d-4092-a4f1-677fef6fbd7d',
              isShow: (record) => {
                return record.AuditStatus == 1 || record.AuditStatus == 2
              },
            },
            {
              name: '编辑',
              id: '05e234af-9b7c-4bdb-ac77-0f075b7f0096',
              isShow: (record) => {
                return record.AuditStatus == 0 || record.AuditStatus == 3
              },
            },
            {
              name: '生成订单',
              id: 'fce44274-6963-4914-a360-27c658c174b1',
              isShow: (record) => {
                return record.AuditStatus == 2 && !record.IsGenerateOrder
              },
            },
            {
              name: '打印计划',
              icon: (record) => {
                return record.printLoading ? 'loading' : ''
              },
              id: '07920ebd-1fd8-4b3c-a5a4-9f7c732df5a2',
              isShow: (record) => {
                return record.AuditStatus == 1 || record.AuditStatus == 2
              },
            },
            {
              name: '删除',
              id: '25b27238-2319-4383-a274-9109ba1d468b',
              isShow: (record) => {
                //未提交 = 0, 审核中 = 1, 审核通过 = 2, 审核失败 = 3,
                return record.AuditStatus != 1 && !record.IsGenerateOrder
              },
            },
            {
              name: '撤回',
              id: '62996b35-a623-4c01-a50c-ef5f79150000',
              isShow: (record) => {
                //未提交 = 0, 审核中 = 1, 审核通过 = 2, 审核失败 = 3,
                return record.AuditStatus == 1
              },
            },
          ],
        },
      ]
    }
  },
  methods: {
    onOperateClick(key) {
      if (key == '新增含特计划') {
        this.onDetailClick('../AddPurchaseOrder', { orderType: 2, opType: 2 })
      } else if (key == '新增计划') {
        this.onDetailClick('../AddPurchaseOrder', { orderType: 1, opType: 2 })
      } else if (key == '导出') {
        const queryParam = this.$refs.searchView.getQueryParam()

        if (!Object.keys(queryParam).length) {
          this.$message.warning('请您先选择创建时间，再导出（导出时间最大跨度为一年）')
          return
        }

        const { CreateTimeBegin, CreateTimeEnd } = queryParam
        if (Object.keys(queryParam).length === 2 && CreateTimeBegin) { // 筛选条件只选了时间，则需要限制在一年内
          const years = moment(CreateTimeEnd).diff(moment(CreateTimeBegin), 'year', true)
          if (years > 1) {
            this.$message.warning('导出时间最大跨度为一年')
            return
          }
        }

        this.tab.operateBtns[0].loading = true
        this.handleExportXls(
          '采购计划记录表',
          'Get',
          '/{v}/PurchaseScheme/ExportPurchasePlan',
          null,
          'P36009',
          queryParam,
          null,
          () => {
            this.tab.operateBtns[0].loading = false
          }
        )
      }
    },
    onActionClick(record, key, index) {
      if (key == '详情') {
        this.onDetailClick('ProcurementPlanningDetail', { id: record.Id })
      } else if (key == '编辑') {
        this.onDetailClick('../AddPurchaseOrder', { id: record.Id, opType: 2, isAdd: 1 })
      } else if (key == '生成订单') {
        this.createOrder(record.Id)
      } else if (key == '打印计划') {
        this.$set(record, 'printLoading', true)
        getAction('/{v}/PurchaseScheme/PrintPurchasePlan', { id: record.Id }, 'P36009').then(({ IsSuccess, Data, Msg}) => {
          record.printLoading = false
          if (!IsSuccess) {
            this.$message.warning(Msg)
            return
          }

          window.open(Data)
        })
      } else if (key == '删除') {
        this.handleDeleteClick(record.Id)
      } else if (key == '撤回') {
        const that = this
        this.$confirm({
          title: '撤回采购计划',
          content: '您确定要撤回该采购计划吗？',
          onOk() {
            that.cancelOrder(record.Id)
          },
          onCancel() { },
        })
      }
    },
     // 撤回采购计划
     cancelOrder(Id) {
      if (!Id) return
      this.loading = true
      putAction(this.url.cancelOrder, { Id: Id }, this.httpHead).then((res) => {
        this.loading = false
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          this.loadData()
          return
        }
        this.$message.success('操作成功!')
        this.loadData()
      })
    },
    createOrder(id) {
      let that = this
      that.loading = true
      postAction(that.url.createOrder, { Id: id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功!')
            that.loadData()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
  },
}
</script>
<style scoped>

</style>
