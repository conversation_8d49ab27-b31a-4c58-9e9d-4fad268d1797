<!--
 * @Description: 采购计划审核 列表
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-03-31 14:41:45
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
    />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'ProPlanAuditList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierName',
        },
        {
          name: '企业类别',
          type: SEnum.ENUM,
          key: 'SupplierType',
          dictCode: 'EnumSupplierType',
        },
        {
          name: '计划编号',
          type: SEnum.INPUT,
          key: 'SchemeNo',
        },
        {
          name: '付款方式',
          type: SEnum.ENUM,
          key: 'PaymentMode',
          dictCode: 'EnumPurchasePaymentMode',
        },

        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
      ],
      tabDataList: [
        {
          name: '待审核',
          value: '1',
          count: '',
          key: 'WaitAuditCount',
        },
        {
          name: '审核记录',
          value: '2',
          count: '',
          key: 'AuditRecordCount',
        },
      ],
      tab: {
        curStatus: '1',
        tabStatusKey: 'ApproavalStatus',
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        // rowKey: 'Id'
      },

      httpHead: 'P36009',
      url: {
        list: '/{v}/PurchaseScheme/GetPurchasePlanAuditList',
        tabCount: '/{v}/PurchaseScheme/GetPurchasePlanAuditCount',
        tabIsList: false,
        setValidUrl: '',
      },
    }
  },
  computed: {
    columns() {
      let auditStatusList =
        this.tab.curStatus == 2
          ? [
              {
                title: '审核状态',
                dataIndex: 'AuditStatusStr',
                width: 100,
                scopedSlots: { customRender: 'state' },
                ellipsis: true,
              },
            ]
          : []
      return [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '计划编号',
          dataIndex: 'SchemeNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '企业类别',
          dataIndex: 'SupplierTypeStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '计划金额',
          dataIndex: 'OrderAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        ...auditStatusList,
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: '33945063-5ba2-43ea-b134-8d07d99378ff',
              isShow: (record) => {
                return this.tab.curStatus == '2'
              },
            },
            {
              name: '审核',
              id: 'ee46b075-ddd7-4066-856c-26d323774fde',
              isShow: (record) => {
                return this.tab.curStatus == '1'
              },
            },
          ],
        },
      ]
    },
  },
  created() {},

  methods: {
    onOperateClick(key) {},
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('ProPlanAuditPage', { id: record.Id, type: 1 })
      } else if (key === '审核') {
        this.onDetailClick('ProPlanAuditPage', { id: record.Id, type: 2 })
      }
    },
  },
}
</script>
<style scoped>

</style>
