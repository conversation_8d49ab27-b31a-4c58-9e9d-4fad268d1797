<!--
 * @Description: 采购计划审核 详情
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-03-31 17:02:29
-->
<template>
  <a-spin :spinning="loading">
    <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }" v-if="model">
      <a-descriptions :column="4">
        <a-descriptions-item>
          <div class="flc hcenter">
            <a-icon :type="getAuditIcon(model.AuditStatus)" theme="twoTone" :two-tone-color="getAuditColor(model.AuditStatus)" style="font-size: 32px" />
            <span :style="{ color: getAuditColor(model.AuditStatus) }">{{ model.AuditStatusStr || '--' }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">计划金额</span>
          <div>¥{{ model.OrderAmount }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">计划编号</span>
          <div>{{ model.SchemeNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
    <a-card :bodyStyle="{ padding: '16px 16px' }" class="mt15">
      <a-row>
        <a-descriptions :column="3">
          <a-descriptions-item>
            <span class="c999">供应商名称</span>
            <div>{{ model.SupplierName || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">委托人</span>
            <div>{{ model.DelegateScopeName || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">付款方式</span>
            <div>{{ model.PaymentModeStr || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
              <span class="c999">付款类型</span>
              <div>{{ model.PaymentTypeStr || '--' }}</div>
            </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">送货方式</span>
            <div>{{ model.DeliveryMethodStr || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">预到货日期</span>
            <div>{{ model.ExpectedArrivalDate || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
              <span class="c999">政策说明</span>
              <div>{{ model.PolicyStatement || '--' }}</div>
            </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">备注</span>
            <div>{{ model.Remarks || '--' }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </a-row>
      <a-row class="mt20">
        <a-col span="24"><span style="font-size: 16px; font-weight: 600">商品信息</span></a-col>
        <a-col span="24">
          <TableView ref="tableView" :showCardBorder="false" :tab="tab" :columns="columns" :dataList="model.PurchaseOrderDetailInfos || []" @actionClick="onActionClick">
            <span slot="clickA" slot-scope="{ text, record,index, column}">
              <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
                <j-ellipsis :value="'' + text" :length="50" />
              </a>
              <span v-else>--</span>
            </span>
            <!-- 全部点击操作 -->
            <span slot="clickAl" slot-scope="{text, record, index, column}">
              <a v-if="record.ErpGoodsId" @click="clickA(text, record, index, column)">
                <j-ellipsis :value="(text || text == 0)?('' + text):'--'" :length="50" />
              </a>
              <span v-else>--</span>
            </span>
            <!-- 价格点击操作 -->
            <span slot="clickPriceA" slot-scope="{ text, record,index, column}">
              <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
                <j-ellipsis :value="text == 0?(''+text):('¥' + text)" :length="50" />
              </a>
              <span v-else>--</span>
            </span>
            <span slot="TaxIncludedPurchasePrice" slot-scope="{ text, record,index, column}">
              <span v-if="text || text == 0" :style="{color:text>record['LastPurchasePrice']?'#ff0000':'#00000'}">
                <j-ellipsis :value="text == 0?(''+text):('¥' + text)" :length="50" />
              </span>
              <span v-else>--</span>
            </span>
          </TableView>
        </a-col>
      </a-row>
      <a-row v-if="model.Id">
        <a-col span="24"><span style="font-size: 16px; font-weight: 600">审核信息</span></a-col>
        <a-col span="24" class="mt10">
          <SupAuditInformation :bussinessNo="model.Id" />
        </a-col>
      </a-row>

      <!-- 底部区域 -->
      <a-affix :offset-bottom="10" class="affix">
        <a-divider />
        <div>
          <a-button @click="goBack(true,true)" class="mr8">返回</a-button>
          <template v-if="opType == 2 && showBtn">
            <YYLButton menuId="0d37a3c5-9ab4-4434-b3fc-c694590e49b4" text="审核通过" type="primary" :loading="confirmLoading" @click="onPassClick" />
            <YYLButton menuId="4ea90d00-4391-49cd-b846-a540db7eb763" text="审核驳回" type="danger" :loading="confirmLoading" @click="onRejectClick" />
          </template>
        </div>
      </a-affix>
    </a-card>
    <!-- 审核弹框 -->
    <AuditRemarkModal ref="auditRemarkModal" @ok="onAuditModalOk" />
    <!-- 历史采购价 -->
    <HistoricalPurchasePriceModal ref="HistoricalPurchasePriceModal" />
    <!-- 平均参考成本 -->
    <AverageReferenceCostModal ref="AverageReferenceCostModal" />
    <!-- 政策底价 -->
    <PolicyBasePriceModal ref="PolicyBasePriceModal" />
    <!-- 库存明细 -->
    <InventoryDetailModal ref="InventoryDetailModal" />
    <!-- 本月销售明细 -->
    <SalesThisMonthModal ref="SalesThisMonthModal" />
  </a-spin>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'ProPlanAuditPage',
  mixins: [EditMixin, ListMixin],
  components: { JEllipsis },
  data() {
    return {
      model: {},
      loading: false,
      confirmLoading: false,
      orderId: '', //采购订单id
      opType: 1, //1详情  2审核
      authType: null,
      httpHead: 'P36009',
      tab: {
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        rowKey: 'Id',
        customSlot: ['clickA', 'clickAl', 'clickPriceA','TaxIncludedPurchasePrice'],
      },

      showBtn: false,
      approvalModel: null,
      url: {
        detail: '/{v}/PurchaseScheme/GetPurchaseSchemeRecordInfo',
        save: '/{v}/PurchaseScheme/SavePurchaseSchemeRecord',
      },
    }
  },
  computed: {
    columns() {
      const actionList =
        this.opType == 2
          ? [
            {
              title: '操作',
              dataIndex: 'action',
              align: 'center',
              width: 80,
              fixed: 'right',
              scopedSlots: { customRender: 'action' },
              actionBtns: [
                {
                  name: '移除',
                  id: '76609c0e-4c24-42d2-8b22-d09da42e0ba4',
                },
              ],
            },
          ]
          : []
      return [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'name' },
          width: 150,
          fixed: 'left',
          ellipsis: true,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 100,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '产地',
          dataIndex: 'Producer',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          scopedSlots: { customRender: 'component' },
          width: 60,
          ellipsis: true,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          scopedSlots: { customRender: 'TaxIncludedPurchasePrice' },
          width: 80,
          precision: 6,
          ellipsis: true,
        },
        {
          title: '含税金额',
          dataIndex: 'TaxIncludedPurchasePriceTotal',
          scopedSlots: { customRender: 'price' },
          width: 80,
          precision: 2,
          ellipsis: true,
        },
        {
          title: '仓库',
          dataIndex: 'StorehouseStr',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          keyStr: 'StorehouseStr',
        },
        {
          title: '近效期',
          dataIndex: 'IsNearExpiry',
          scopedSlots: { customRender: 'bool' },
          width: 80,
          ellipsis: true,
        },
        {
          title: '返利标记',
          dataIndex: 'PurchaseRebateFlagStr',
          scopedSlots: { customRender: 'component' },
          width: 80,
          ellipsis: true,
        },
        {
          title: '销售原价',
          dataIndex: 'OriginalPrice',
          scopedSlots: { customRender: 'component' },
          width: 110,
          precision: 6,
          ellipsis: true,
        },
        {
          title: '商销底价',
          dataIndex: 'MinimumSellingPrice',
          width: 110,
          precision: 6,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '每盒返利金额',
          dataIndex: 'BoxRebateAmount',
          width: 100,
          scopedSlots: { customRender: 'component' },
          precision: 6,
          ellipsis: true,
        },
        {
          title: '返利到账天数',
          dataIndex: 'RebateDays',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        { 
          title: '市场采购价', 
          dataIndex: 'MarketPurchasePrice', 
          width: 100, 
          scopedSlots: { customRender: 'component' },
        },
        { 
          title: '毛利率', 
          dataIndex: 'GrossMarginRate', 
          width: 80, 
          scopedSlots: { customRender: 'component' },
        },
        { 
          title: '购进批号', 
          dataIndex: 'PurchaseBatchNo', 
          width: 120, 
          scopedSlots: { customRender: 'component' },
        },
        { 
          title: '预计销售时间', 
          dataIndex: 'EstimatedTimeOfSale', 
          width: 120, 
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否商销',
          dataIndex: 'IsForSale',
          width: 100,
          scopedSlots: { customRender: 'bool' },
        },
        {
          title: '是否包处理',
          dataIndex: 'IsPackageHandled',
          width: 100,
          scopedSlots: { customRender: 'bool' },
        },
        {
          title: '备注',
          dataIndex: 'Remarks',
          scopedSlots: { customRender: 'component' },
          width: 120,
          maxLength: 50,
          maxLength: 100,
          ellipsis: true,
        },
        {
          title: '税率',
          dataIndex: 'PurchaseTaxRate',
          width: 60,
          scopedSlots: { customRender: 'percent' },
          ellipsis: true,
        },
        {
          title: '可销总库存',
          dataIndex: 'AvailableInventory',
          scopedSlots: { customRender: 'clickA' },
          width: 90,
          ellipsis: true,
        },
        {
          title: '预计可销时间',
          dataIndex: 'EstimatedSaleTime',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '在途库存',
          dataIndex: 'InTransitInventory',
          scopedSlots: { customRender: 'component' },
          width: 80,
          ellipsis: true,
        },
        {
          title: '上月销量',
          dataIndex: 'LastMonthCount',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '本月销量',
          dataIndex: 'ThisMonthCount',
          width: 80,
          scopedSlots: { customRender: 'clickAl' },
          ellipsis: true,
        },
        {
          title: '末次进价',
          dataIndex: 'LastPurchasePrice',
          width: 80,
          scopedSlots: { customRender: 'clickPriceA' },
          ellipsis: true,
        },
        {
          title: '平台原价',
          dataIndex: 'LatestOriginalPrice',
          width: 80,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '活动类型',
          dataIndex: 'ActivityType',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '活动价格',
          dataIndex: 'ActivityPrice',
          width: 100,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '含购进的移动平均成本',
          dataIndex: 'MoveAverageCostPrice',
          width: 160,
          scopedSlots: { customRender: 'clickPriceA' },
          ellipsis: true,
        },
        {
          title: '采购数量',
          dataIndex: 'PurchaseQuantity',
          precision: 2, //数值精度
          onInputChange: this.onInputChange,
          scopedSlots: { customRender: this.opType == 2 ? 'inputNumber' : 'number' },
          width: this.opType == 2 ? 150 : 100,
          fixed: 'right',
          ellipsis: true,
        },
        ...actionList,
      ]
    },
  },
  mounted() {
    if (this.$route.query) {
      this.authType = null
      this.orderId = this.$route.query.id || ''
      this.opType = Number(this.$route.query.type) || 1
      this.setTitle('采购计划审核' + (this.opType == 1 ? '详情' : ''))
      this.loadDetail()
      if (this.opType == 2) {
        this.getApprovalResults()
      }
    }
  },
  created() { },
  methods: {
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.orderId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 获取审批记录
     */
    getApprovalResults() {
      if (!this.model) {
        return
      }
      this.loading = true
      getAction('/{v}/ApprovalWorkFlowInstance/GetApprovalResults', { bussinessNo: this.orderId }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            this.approvalModel = res.Data || {}

            this.checkLoginUserIsHasBtnPerssion(this.approvalModel, (bool) => {
              this.showBtn = bool
            })
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },

    /**
     * 通过
     */
    onPassClick() {
      this.showAuditModel(1)
    },
    /**
     * 驳回
     */
    onRejectClick() {
      this.showAuditModel(2)
    },

    /**
     * 显示审核弹窗
     * @param {*} type 1通过  2驳回
     */
    showAuditModel(type) {
      let that = this
      this.authType = type
      if (this.$refs.auditRemarkModal) {
        let param = {
          ApprovalWorkFlowInstanceId: that.approvalModel ? that.approvalModel.ApprovalWorkFlowInstanceId : '',
          IsHasSuperior: that.approvalModel ? that.approvalModel.IsHasSuperior : false,
        }
        console.log('param', param)
        this.$refs.auditRemarkModal.show(type, param)
      }
    },
    /**
     * 审核弹窗回调
     */
    onAuditModalOk(formData) {
      if (formData) {
        let that = this
        that.loading = true
        that.confirmLoading = true
        let AuditStatus = null
        if (this.authType == 1) {
          AuditStatus = 2
        } else if (this.authType == 2) {
          AuditStatus = 3
        }
        postAction(
          that.url.save,
          {
            Id: that.model.Id,
            PurchaseOrderDetails: that.model.PurchaseOrderDetailInfos || [],
            AuditStatus: AuditStatus,
          },
          that.httpHead
        )
          .then((res) => {
            if (res.IsSuccess) {
              that.tongGuo(formData)
            } else {
              that.$message.error(res.Msg)
            }
          })
          .catch((e) => {
            that.loading = false
            that.confirmLoading = false
          })
          .finally(() => {
            that.loading = false
            that.confirmLoading = false
          })
      } else {
        this.goBack(true,true)
      }
    },
    tongGuo(formData) {
      if (this.$refs.auditRemarkModal) {
        this.$refs.auditRemarkModal.postAuditInstance(formData, (bool) => {
          if (bool) {
            this.goBack(true,true)
          }
        })
      }
    },
    /**
     * 计算TaxIncludedPurchasePriceTotal
     */
    onInputChange(val, key, record, index) {
      let count = 0
      if (record.PurchaseQuantity > 0 && record.TaxIncludedPurchasePrice > 0) {
        count = record.PurchaseQuantity * record.TaxIncludedPurchasePrice
      }
      this.model.PurchaseOrderDetailInfos[index].TaxIncludedPurchasePriceTotal = count
    },
    clickA(text, record, index, column) {
      let key = column.dataIndex
      // 可销总库存
      if (key == 'AvailableInventory') {
        this.$refs.InventoryDetailModal.show(record)
      } else if (key == 'ThisMonthCount') {
        // 本月销量
        this.$refs.SalesThisMonthModal.show(record)
      } else if (key == 'LastPurchasePrice') {
        // 末次进价
        this.$refs.HistoricalPurchasePriceModal.show(record)
      } else if (key == 'MoveAverageCostPrice' || key == 'LatestAverageCost') {
        // 含购进的移动平均成本
        this.$refs.PolicyBasePriceModal.show(record)
      }
    },
    onActionClick(record, key, index) {
      if (key == '移除') {
        this.model.PurchaseOrderDetailInfos.splice(index, 1)
      }
    },
  },
}
</script>

<style scoped>

</style>
