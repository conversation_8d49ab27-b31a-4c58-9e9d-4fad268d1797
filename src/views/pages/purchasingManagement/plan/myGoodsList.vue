<!--
 * @Description: 采购计划 我的商品 列表
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: wenjing<PERSON>ao
 * @LastEditTime: 2025-06-12 17:34:49
-->

<template>
  <!-- 内容 -->
  <a-row :gutter="12">
    <a-col :md="24">
      <a-card :bodyStyle="{ padding: '10px' }" :headStyle="{ padding: '0 10px ' }">
        <!-- 搜索 -->
        <a-card :bodyStyle="{ padding: '10px 10px 0 10px' }" style="margin-bottom: 10px">
          <div class="table-page-search-wrapper">
            <a-form layout="inline">
              <a-row :gutter="10">
                <a-col :md="queryCol">
                  <a-form-item label="商品" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input placeholder="名称/拼音码/编号" v-model="queryParam.KeyWord"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :md="queryCol">
                  <a-form-item label="生产厂家" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input placeholder="请输入" v-model="queryParam.BrandManufacturer"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :md="queryCol">
                  <a-form-item label="商品类别" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <SingleChoiceSearchView style="width: 100%" :httpParams="{
                        Name: '',
                        Level: 1,
                      }" showSearch :dataKey="{ name: 'Name', value: 'Id' }" :Url="'/{v}/GoodsManage/QueryGoodsManageClassifyList'" keyWord="Name" httpHead="P36006" placeholder="请选择" httpType="POST" v-model="queryParam.GoodsManageClassify1" />
                  </a-form-item>
                </a-col>
                <a-col :md="queryCol">
                  <a-form-item label="是否活动" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select v-model="queryParam.IsValid" placeholder="请选择" @change="
                        (value) => {
                          queryParam.IsValid = value
                        }
                      " >
                      <a-select-option value="true">是</a-select-option>
                      <a-select-option value="false">否</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="queryCol">
                  <a-form-item label="是否暂不经营"  :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select placeholder="请选择" @change="
                        (value) => {
                          queryParam.NotAllowedPurchase = value
                        }
                      " v-model="queryParam.NotAllowedPurchase">
                      <a-select-option value="true">是</a-select-option>
                      <a-select-option value="false">否</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="queryCol">
                  <a-form-item label="是否有库存" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select placeholder="请选择" @change="
                        (value) => {
                          queryParam.HasStock = value
                        }
                      " v-model="queryParam.HasStock">
                      <a-select-option value="true">是</a-select-option>
                      <a-select-option value="false">否</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="queryCol">
                  <a-form-item label="采购人" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input placeholder="请输入" v-model="queryParam.PurchaserName"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :md="queryCol" class="custom-tailwind">
                  <a-form-item label="可销天数" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input-group compact style="padding-top: 1px; display: flex; align-items: center">
                      <a-input-number v-model="queryParam.DaysOfInventoryStart" class="grow-1" :precision="2" :min="0" placeholder="请输入"/>
                      <span class="px-8 shrink-0">-</span>
                      <a-input-number v-model="queryParam.DaysOfInventoryEnd" class="grow-1" :precision="2" :min="queryParam.HarmoniTestStart" placeholder="请输入"/>
                    </a-input-group>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <span style="float: right; overflow: hidden; margin-bottom: 10px" class="table-page-search-submitButtons">
                    <a-button @click="searchReset" icon="reload" style="margin-right: 8px">重置</a-button>
                    <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                  </span>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </a-card>
        <!--/ 搜索 -->
        <a-card :bodyStyle="{ padding: '10px' }" :headStyle="{ padding: '0 10px ' }" title="查询结果">
          <!-- 操作按钮 -->
          <div slot="extra">
            <a-button :disabled="dataSource.length < 1 ? true : false" icon="export" style="margin-right: 10px" @click="exportData()">导出</a-button>
          </div>
          <a-row>
            <a-col :span="24">
              <!-- 数据列表 -->
              <a-table :bordered="true" ref="table" rowKey="Id" size="middle" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length === 0 ? false : '37vh', x: '100%' }">
                <!-- 列头 -->
                <template slot="titleDate1">
                  <span v-html="returnReplace('本月时间段')"></span>
                </template>
                <template slot="titleDate2">
                  <span v-html="returnReplace('上个月')"></span>
                </template>
                <template slot="titleDate3">
                  <span v-html="returnReplace('上上个月')"></span>
                </template>
                <template slot="titleDate4">
                  <span v-html="returnReplace('上上上个月')"></span>
                </template>
                <template slot="titleDateSale">
                  <div class="centerBox">
                    <div>近90天</div>
                    <div>日均销量</div>
                  </div>
                </template>
                <template slot="isOperation">
                  <div class="centerBox">
                    <div>是否暂不</div>
                    <div>经营</div>
                  </div>
                </template>
                <template slot="referenceCost">
                  <div class="centerBox">
                    <div>含购进的</div>
                    <div>移动平均成本</div>
                  </div>
                </template>
                <!-- 字符串超长截取省略号显示-->
                <span slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" :length="50" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else :length="50" />
                </span>
                <!-- 四舍五入-->
                <span slot="componentInt" slot-scope="text">
                  <j-ellipsis :value="text ? '' + Math.round(text) : text == 0 || text == '0' ? text : '--'" :length="50" />
                </span>
                <!-- 点击操作 -->
                <span slot="clickA" slot-scope="text, record, index, column">
                  <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
                    <j-ellipsis :value="'' + text" :length="50" />
                  </a>
                  <span v-else>--</span>
                </span>
                <!-- 价格点击操作 -->
                <span slot="clickPriceA" slot-scope="text, record, index, column">
                  <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
                    <j-ellipsis :value="text == 0?('¥'+text):('¥' + text)" :length="50" />
                  </a>
                  <span v-else>--</span>
                </span>
                <!-- 计划单数量 -->
                <span slot="PlanOrderCount" slot-scope="text, record">
                  <a @click="text ? $refs.iMyGoodsPurchaseOrderQuantityModal.show(record) : null">
                    <j-ellipsis :value="'' + text" :length="50" />
                  </a>
                </span>
                <span slot="Price" slot-scope="text">
                  <j-ellipsis :value="(text == 0 || text) ? '¥' + text : ''" :length="50" />
                </span>
                <!-- 操作 -->
                <span slot="action" slot-scope="text, record, index, column">
                  <a @click="handleAction(text, record, index, column)">查看</a>
                </span>
              </a-table>
              <!--/ 数据列表 -->
            </a-col>
          </a-row>
        </a-card>
      </a-card>
    </a-col>
    <!-- 采购单数量 -->
    <MyGoodsPurchaseOrderQuantityModal ref="iMyGoodsPurchaseOrderQuantityModal" />
    <!-- 历史销量 -->
    <MyGoodsHistoricalSalesModal ref="iMyGoodsHistoricalSalesModal" />
    <!-- 历史入库 -->
    <MyGoodsHistoricalEntryModal ref="iMyGoodsHistoricalEntryModal" />
    <!-- 历史采购价 -->
    <HistoricalPurchasePriceModal ref="HistoricalPurchasePriceModal" />
    <!-- 平均参考成本 -->
    <AverageReferenceCostModal ref="AverageReferenceCostModal" />
    <!-- 政策底价 -->
    <PolicyBasePriceModal ref="PolicyBasePriceModal" />
    <!-- 库存明细 -->
    <InventoryDetailModal ref="InventoryDetailModal" />
    <!-- 我的商品导出弹窗 -->
    <MyGoodExportModal ref="MyGoodExportModal" @ok="chooseExportType" />
    <!-- 在途库存 -->
    <PlanTotalInTransitCountModal ref="PlanTotalInTransitCountModal" />
  </a-row>
</template>

<script>
import { filterObj } from '@/utils/util'
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')

export default {
  description: '我的商品',
  name: 'myGoodsList',
  mixins: [ListMixin],
  components: { JEllipsis },
  data() {
    return {
      dateFormat: 'YYYY-MM-DD',
      dateFormatPoint: 'YYYY.MM.DD',
      // 表头
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          key: 'ErpGoodsName',
          width: 180,
          fixed: 'left',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          key: 'ErpGoodsCode',
          width: 90,
          fixed: 'left',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: 'SPZ码',
          dataIndex: 'ErpGoodsId',
          key: 'ErpGoodsId',
          width: 120,
          fixed: 'left',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品规格',
          dataIndex: 'PackingSpecification',
          width: 120,
          fixed: 'left',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          key: 'BrandManufacturer',
          width: 180,
          fixed: 'left',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购人',
          dataIndex: 'PurchaserName',
          key: 'PurchaserName',
          width: 90,
          fixed: 'left',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品类别',
          dataIndex: 'ManageClassifyStr1',
          key: 'ManageClassifyStr1',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否活动',
          dataIndex: 'IsValid',
          key: 'IsValid',
          width: 80,
          customRender: (t, r, i) => {
            return t ? '是' : '否'
          },
        },
        {
          // title: '是否暂不经营',
          dataIndex: 'NotAllowedPurchase',
          key: 'NotAllowedPurchase',
          width: 80,
          scopedSlots: { title: 'isOperation' },
          customRender: (t, r, i) => {
            return t ? '是' : '否'
          },
        },
        {
          title: '批准文号',
          dataIndex: 'ApprovalNumber',
          key: 'ApprovalNumber',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '末次采购价',
          dataIndex: 'LastPurchasePriceIncludingPriceAdjustment',
          key: 'LastPurchasePriceIncludingPriceAdjustment',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'clickPriceA' },
        },
        {
          title: '平台原价',
          dataIndex: 'OriginalPrice',
          key: 'OriginalPrice',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '活动类型',
          dataIndex: 'ActivityTypeName',
          key: 'ActivityTypeName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '活动价格',
          dataIndex: 'ActivityPrice',
          key: 'ActivityPrice',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'Price' },
        },
        {
          // title: '含购进的移动平均成本',
          dataIndex: 'MoveAverageCostPrice',
          key: 'MoveAverageCostPrice',
          width: 100,
          ellipsis: true,
          scopedSlots: { title: 'referenceCost', customRender: 'clickPriceA' },
        },
        {
          title: '竞品活动价',
          align: 'center',
          children: [
            {
              title: '合纵药店',
              dataIndex: 'PharmacyHZSellsPrice',
              width: 90,
              scopedSlots: { customRender: 'Price' },
              ellipsis: true,
            },
            {
              title: '合纵诊所',
              dataIndex: 'ClinicHZSellsPrice',
              width: 90,
              scopedSlots: { customRender: 'Price' },
              ellipsis: true,
            },
            {
              title: '聚创药店',
              dataIndex: 'PharmacyJCSellsPrice',
              width: 90,
              scopedSlots: { customRender: 'Price' },
              ellipsis: true,
            },
            {
              title: '聚创诊所',
              dataIndex: 'ClinicJCSellsPrice',
              width: 90,
              scopedSlots: { customRender: 'Price' },
              ellipsis: true,
            },
          ],
        },
        {
          title: '库存情况',
          align: 'center',
          children: [
            {
              title: '总入库数',
              dataIndex: 'TotalInboundCount',
              width: 90,
              scopedSlots: { customRender: 'component' },
              ellipsis: true,
            },
            {
              title: '可销总库存',
              dataIndex: 'CanSaleInventory',
              key: 'CanSaleInventory',
              width: 90,
              scopedSlots: { customRender: 'clickA' },
              ellipsis: true,
            },
            {
              title: '在途库存',
              dataIndex: 'InTransitInventory',
              key: 'InTransitInventory',
              width: 90,
              scopedSlots: { customRender: 'clickA' },
              ellipsis: true,
            },
          ],
        },
        {
          title: '销售情况',
          align: 'center',
          children: [
            {
              title: '累计销售数量',
              dataIndex: 'TotalSalesCount',
              width: 120,
              scopedSlots: { customRender: 'component' },
              ellipsis: true,
            },
            {
              // title: '本月时间段',
              dataIndex: 'Month0SalesCount',
              width: 110,
              scopedSlots: { customRender: 'component', title: 'titleDate1' },
              ellipsis: true,
            },
            {
              // title: '上个月',
              dataIndex: 'Month1SalesCount',
              width: 90,
              scopedSlots: { customRender: 'component', title: 'titleDate2' },
              ellipsis: true,
            },
            {
              // title: '上上个月',
              dataIndex: 'Month2SalesCount',
              width: 90,
              scopedSlots: { customRender: 'component', title: 'titleDate3' },
              ellipsis: true,
            },
            {
              // title: '上上上个月',
              dataIndex: 'Month3SalesCount',
              width: 90,
              scopedSlots: { customRender: 'component', title: 'titleDate4' },
              ellipsis: true,
            },
            {
              // title: '近90天日均销量',
              dataIndex: 'DailyAverageSalesCount_90',
              width: 90,
              scopedSlots: { customRender: 'component', title: 'titleDateSale' },
              ellipsis: true,
            },
          ],
        },
        {
          title: '可销天数',
          align: 'center',
          children: [
            {
              title: '可销天数',
              dataIndex: 'DaysOfInventoryExcludePlan',
              width: 160,
              scopedSlots: { customRender: 'component' },
              ellipsis: true,
              sorter: true,
            },
            {
              title: '建议采购数量',
              dataIndex: 'RecommendedPurchaseQuantity',
              width: 160,
              scopedSlots: { customRender: 'componentInt' },
              ellipsis: true,
              sorter: true,
            },
          ],
        },
        {
          title: '正在执行的采购计划情况',
          align: 'center',
          children: [
            {
              title: '计划单数量',
              dataIndex: 'PlanOrderCount',
              width: 100,
              scopedSlots: { customRender: 'PlanOrderCount' },
              ellipsis: true,
            },
            {
              title: '计划商品数量',
              dataIndex: 'PlanOrderGoodsCount',
              width: 110,
              scopedSlots: { customRender: 'component' },
              ellipsis: true,
            },
            {
              title: '已入库数量',
              dataIndex: 'PlanTotalInboundCount',
              width: 100,
              scopedSlots: { customRender: 'component' },
              ellipsis: true,
            },
            {
              title: '待入库数量',
              dataIndex: 'PlanTotalInTransitCount',
              width: 100,
              scopedSlots: { customRender: 'component' },
              ellipsis: true,
            },
          ],
        },
        {
          title: '历史数据',
          align: 'center',
          children: [
            {
              title: '历史销量',
              dataIndex: '历史销量',
              width: 100,
              scopedSlots: { customRender: 'action' },
              ellipsis: true,
            },
            {
              title: '历史入库',
              dataIndex: '历史入库',
              width: 100,
              scopedSlots: { customRender: 'action' },
              ellipsis: true,
            },
          ],
        },
      ],
      queryParam: {
        IsValid: undefined,
        HasStock: undefined,
        NotAllowedPurchase: undefined,
      },
      labelCol: {
        xs: { span: 8 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 16 },
        sm: { span: 19 },
      },
      date: {
        上上上个月: '',
        上上个月: '',
        上个月: '',
        本月时间段: '',
      },
      queryCol: 6,
      httpHead: 'P36009',
      /* 排序参数 */
      isorter: {
        column: '',
        order: '',
      },
      isInitData: false,
      url: {
        listType: 'POST',
        list: '/{v}/PurchaseGoods/QueryMyGoodsList',
        exportXlsUrl: '/{v}/PurchaseGoods/ExportMyGoods',//导出商品url
        exportMyGoodsStock: '/{v}/PurchaseGoods/ExportMyGoodsStock',//导出库存url
      },
    }
  },
  activated() {
    this.loadData(1)
    this.computedDate()
  },
  methods: {
    moment,
    getQueryParams() {
      //获取查询条件
      let sup = {}
      if (this.superQueryParams) {
        sup['superQueryParams'] = encodeURI(this.superQueryParams)
      }
      let searchPar = {}
      if (this.$refs.searchView) {
        searchPar = this.$refs.searchView.queryParam || {}
      }
      var param = Object.assign({ ...sup, ...searchPar }, this.queryParam, this.isorter, this.filters)
      param.field = this.getQueryField()
      param.PageIndex = this.ipagination.current
      param.PageSize = this.ipagination.pageSize
      if (this.disableSort) {
        param.column = '' //禁用排序字段
      }
      let filterObjParams = filterObj(param)
      filterObjParams['IsValid'] =
        filterObjParams['IsValid'] == 'true' ? true : filterObjParams['IsValid'] == 'false' ? false : null
      filterObjParams['HasStock'] =
        filterObjParams['HasStock'] == 'true' ? true : filterObjParams['HasStock'] == 'false' ? false : null
      filterObjParams['NotAllowedPurchase'] =
        filterObjParams['NotAllowedPurchase'] == 'true' ? true : filterObjParams['NotAllowedPurchase'] == 'false' ? false : null
      return filterObjParams
    },
    searchQuery() {
      this.loadData(1)
    },
    searchReset() {
      this.queryParam = { IsValid: undefined, HasStock: undefined, NotAllowedPurchase: undefined }
      this.loadData(1)
    },
    // 返回时间段
    retrunDate(start, end) {
      // return start.substring(5, start.length) + ' ~ ' + end.substring(5, end.length)
      return start + ' ~ ' + end
    },
    computedDate() {
      let howWeek = moment().day() // 获取当前是星期几；0为星期日、6为星期6
      // console.log('今天周几    ',howWeek)
      // console.log('今天日期    ',moment().format(this.dateFormat))

      // 计算本月时间段
      let firstDayOfMonthStr = moment().startOf('month').format(this.dateFormatPoint) //本月第一天
      let yesterday = moment().subtract(1, 'days').format(this.dateFormatPoint) //昨天日期
      let howMonthDay = this.retrunDate(firstDayOfMonthStr, yesterday) //本月第一天到今天
      // 计算上个月时间
      var currentDate = new Date();
      currentDate.setMonth(currentDate.getMonth() - 1);
      var oneMonthsAgoDate = currentDate.getFullYear() + '.' +
        (currentDate.getMonth() + 1).toString().padStart(2, '0') + '月'
      // 计算前上上个月时间
      var currentDate2 = new Date();
      currentDate2.setMonth(currentDate2.getMonth() - 2);
      var twoMonthsAgoDate = currentDate2.getFullYear() + '.' +
        (currentDate2.getMonth() + 1).toString().padStart(2, '0') + '月'
      // 计算前上上上个月时间
      var currentDate3 = new Date();
      currentDate3.setMonth(currentDate3.getMonth() - 3);
      var threeMonthsAgoDate = currentDate3.getFullYear() + '.' +
        (currentDate3.getMonth() + 1).toString().padStart(2, '0') + '月'
      console.log(howMonthDay, oneMonthsAgoDate, twoMonthsAgoDate, threeMonthsAgoDate)

      this.$set(this, 'date', {
        上上上个月: threeMonthsAgoDate,
        上上个月: twoMonthsAgoDate,
        上个月: oneMonthsAgoDate,
        本月时间段: howMonthDay,
      })
      // console.log(this['date'])
    },
    returnReplace(key) {
      if (!this['date'][key]) {
        return ''
      }
      return this['date'][key].replace('~', '<br/>~<br/>')
    },
    exportData() {
      this.$refs.MyGoodExportModal.show()
    },
    chooseExportType(data) {
      if (data.length > 0) {
        if (data.indexOf('1') >= 0 && data.length == 1) {
          this.handleExportXls(`我的商品列表_商品_${this.moment().format('YYYY-MM-DD')}`, 'post', this.url.exportXlsUrl, '', this.httpHead)
        } else if (data.indexOf('2') >= 0 && data.length == 1) {
          this.handleExportXls(`我的商品列表_库存_${this.moment().format('YYYY-MM-DD')}`, 'post', this.url.exportMyGoodsStock, '', this.httpHead)
        } else if (data.indexOf('1') >= 0 && data.indexOf('2') >= 0 && data.length == 2) {
          this.handleExportXls(`我的商品列表_商品_${this.moment().format('YYYY-MM-DD')}`, 'post', this.url.exportXlsUrl, '', this.httpHead)
          setTimeout(() => {
            this.handleExportXls(`我的商品列表_库存_${this.moment().format('YYYY-MM-DD')}`, 'post', this.url.exportMyGoodsStock, '', this.httpHead)
          }, 500)
        }
      }
    },
    clickA(text, record, index, column) {
      // console.log(text, record, index, column)
      let key = column.dataIndex
      // 末次采购价(历史采购价)
      if (key == 'LastPurchasePriceIncludingPriceAdjustment') {
        this.$refs.HistoricalPurchasePriceModal.show(record)
      } else if (key == 'MoveAverageCostPrice') {
        // 含购进的移动平均成本
        this.$refs.PolicyBasePriceModal.show(record)
        // this.$refs.AverageReferenceCostModal.show(record)
      } else if (key == 'CanSaleInventory') {
        // 可销总库存
        this.$refs.InventoryDetailModal.show(record)
      } else if (key == 'InTransitInventory') {
        // 在途库存
        this.$refs.PlanTotalInTransitCountModal.show(record)
      }
    },
    // 操作按钮
    handleAction(text, record, index, column) {
      if (column && column.dataIndex == '历史销量') {
        // this.$message.warning('此功能开发中')
        this.$refs.iMyGoodsHistoricalSalesModal.show(record)
        return
      }
      if (column && column.dataIndex == '历史入库') {
        this.$refs.iMyGoodsHistoricalEntryModal.show(record)
        return
      }
    },
  },
}
</script>

<style lang="less" scoped>

/deep/.ant-table-middle {
  border: none;
}
.centerBox {
  text-align: center;
}
/deep/ .ant-table-fixed-header .ant-table-scroll .ant-table-header {
  margin-bottom: 0px;
  padding-bottom: 0px;
  overflow: hidden;  
}
</style>
