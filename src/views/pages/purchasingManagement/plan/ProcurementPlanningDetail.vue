<!--
 * @Description: 采购计划 详情
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-06 14:17:02
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-03-27 09:35:35
-->

<template>
  <a-spin :spinning="loading" class="custom-tailwind">
    <a-card class="-mb-2" :bodyStyle="{ padding: '16px 16px 0 16px' }">
      <a-descriptions v-if="model" :column="5">
        <a-descriptions-item>
          <div class="flc hcenter">
            <a-icon
              :type="getAuditIcon(model.AuditStatus)"
              theme="twoTone"
              :two-tone-color="getAuditColor(model.AuditStatus)"
              style="font-size: 32px"
            />
            <span :style="{ color: getAuditColor(model.AuditStatus) }">{{
              model.AuditStatusStr || '--'
            }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">计划编号</span>
          <div>{{ model.SchemeNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">计划金额</span>
          <div>¥{{ model.OrderAmount }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建人</span>
          <div>{{ model.CreateByName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>

    <a-card>
      <a-tabs v-model="activeTabKey">
        <a-tab-pane :key="1" tab="基本信息">
          <POrderBasicInformation v-if="model" :model="model" :isPlan="true"  />
          <!-- 底部区域 -->
          <a-affix :offset-bottom="10" class="affix">
            <div style="text-align: left; font-size: 20px; font-weight: bold; padding-top: 20px" v-if="model">
              含税金额合计：<span style="color: red; margin-right: 50px">¥{{ getAmount() }}</span> 共<span style="color: red">{{ (model.PurchaseOrderDetailInfos || []).length }}</span>种商品
            </div>
            <a-divider />
            <div>
              <a-button @click="goBack(true,true)">返回</a-button>
            </div>
          </a-affix>
        </a-tab-pane>
        <a-tab-pane :key="2" tab="审核信息">
          <SupAuditInformation :bussinessNo="model.AuditId" v-if="activeTabKey == 2 && model" />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </a-spin>
</template>

<script>
import { getAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'ProcurementPlanningDetail',
  mixins: [EditMixin],
  data() {
    return {
      model: null,
      loading: false,
      activeTabKey: 1,
      orderId: '', //采购订单id
      httpHead: 'P36009',
      url: {
        detail: '/{v}/PurchaseScheme/GetPurchaseSchemeInfo',
      },
    }
  },
  mounted() {
    if (this.$route.query) {
      this.orderId = this.$route.query.id || ''
      this.loadDetail()
    }
  },
  methods: {
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.orderId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            that.orderType = that.model.OrderType
            if (that.model.PaymentMode) {
              that.model.PaymentMode = String(that.model.PaymentMode)
              that.model.PaymentType = String(that.model.PaymentType)
              if (that.model.PurchaseOrderDetailInfos) {
                that.dataSource = [].concat(that.model.PurchaseOrderDetailInfos)
                that.dataSource.forEach((x) => {
                  x.IsNearExpiry = String(x.IsNearExpiry)
                  x.IsForSale = String(x.IsForSale)
                  x.IsPackageHandled = String(x.IsPackageHandled)
                  x.IsPolicyReturn = String(x.IsPolicyReturn)
                })
              }
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    getAmount() {
      let num = 0
      if (this.model.PurchaseOrderDetailInfos) {
        this.model.PurchaseOrderDetailInfos.forEach((x) => {
          if (x.TaxIncludedPurchasePriceTotal * 1 >= 0) {
            num += x.TaxIncludedPurchasePriceTotal * 1
          }
        })
      }
      return this.getAmountOfMoney(num, 6)
    },
  },
}
</script>
