<!--
 * @Author: LP
 * @Description: 采购调价详情
 * @Date: 2023/07/12
-->
<template>
  <a-card :bodyStyle="{ padding: '0 0 16px  0' }">
    <a-tabs v-model="activeTabKey" v-if="opType == 1">
      <a-tab-pane :key="1" tab="详情信息">
        <POrderTiaojiaEditAndDetail :id="orderId" v-if="activeTabKey == 1 && orderId" />
      </a-tab-pane>
      <a-tab-pane :key="2" tab="审核信息" style="padding-left: 16px">
        <SupAuditInformation
          :bussinessNo="recordList && recordList.length > 0 ? recordList[0].RecordId : ''"
          v-if="activeTabKey == 2"
        />
      </a-tab-pane>
    </a-tabs>
    <template v-else-if="opType == 2">
      <POrderTiaojiaEditAndDetail ref="pOrderTiaojiaEditAndDetail" :id="orderId" isEdit v-if="orderId" />
    </template>
    <!-- 底部区域 -->
    <a-affix :offset-bottom="10" class="affix mr16 mt20">
      <a-button @click="goBack(true,true)">返回</a-button>
      <template v-if="opType == 2">
        <YYLButton
          menuId="dda1ca41-701c-436a-9a78-982204b57183"
          text="保存"
          type="primary"
          :loading="confirmLoading"
          @click="onSubClick(true)"
          class="ml8"
        />
        <YYLButton
          menuId="c55ab85e-e55d-449e-8fd0-892341ae0e08"
          text="提交审核"
          type="primary"
          :loading="confirmLoading"
          @click="onSubClick(false)"
          class="ml8"
        />
      </template>
    </a-affix>
  </a-card>
</template>

<script>
import { getAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'PurchaSepriceAdjustmentDetail',
  mixins: [EditMixin],
  data() {
    return {
      model: null,
      loading: false,
      confirmLoading: false,
      orderId: '', //采购订单id
      httpHead: 'P36009',
      activeTabKey: 1,
      opType: 1, //1 详情 2编辑
      recordList: [],
      url: {
        record: '/{v}/PurchaseBusiness/GetPurchasePriceAdjustmentAuditRecordListById',
      },
    }
  },
  mounted() {
    if (this.$route.query) {
      this.orderId = this.$route.query.id || ''
      this.opType = this.$route.query.type || 1

      this.loadRecordList()
    }
  },
  methods: {
    onSubClick(IsDraft) {
      if (this.$refs.pOrderTiaojiaEditAndDetail) {
        this.$refs.pOrderTiaojiaEditAndDetail.onSubmitAuditClick(IsDraft,(bool) => {
          this.confirmLoading = bool
        })
      }
    },
    loadRecordList() {
      let that = this
      getAction(that.url.record, { id: this.orderId }, that.httpHead).then((res) => {
        if (res.IsSuccess) {
          that.recordList = res.Data || []
        } else {
          that.$message.error(res.Msg)
        }
      })
    },
  },
}
</script>

<style scoped>

</style>
