<!--
 * @Author: LP
 * @Description: 采购退货列表
 * @Date: 2023/07/08
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView ref="tableView" showCardBorder :tabDataList="tabDataList" :tab="tab" :columns="columns" :dataList="dataSource" @operateClick="onOperateClick" @actionClick="onActionClick" />
    <!-- 退货 -->
    <TuihuoModal ref="tuihuoModal" @ok="modalFormOk" />
  </div>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PurchaReturnGoodsList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierKeyWords',
        },
        {
          name: '退货单号',
          type: SEnum.INPUT,
          key: 'RefundOrderNo',
        },
        {
          name: '订单编号',
          type: SEnum.INPUT,
          key: 'PurchaseOrderNo',
        },
        {
          name: '审核状态',
          type: SEnum.ENUM,
          key: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
        },
        {
          name: '结算状态',
          type: SEnum.ENUM,
          key: 'SettlementStatus',
          dictCode: 'EnumSettlementStatus',
        },
        {
          name: '同步状态',
          type: SEnum.ENUM,
          key: 'SyncStatus',
          dictCode: 'EnumPushERPStatus',
        },
        {
          name: '退货单状态',
          type: SEnum.ENUM,
          key: 'RefundStatus',
          dictCode: 'EnumPurchaseOrderRefundStatus',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '责任人',
          type: SEnum.INPUT,
          key: 'OwnerByName',
        },
        {
          name: '物流方式',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/Global/GetListDictItem',
          key: 'LogisticsMethodCode',
          params: { groupPY: 'wulfs' },
          dataKey: { name: 'ItemValue', value: 'ItemPym' }
        },
        {
          name: '商品',
          type: SEnum.INPUT,
          key: 'ErpGoodsName',
        },
        {
          name: '出库单号',
          type: SEnum.INPUT,
          key: 'OutOrderNo',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        curStatus: '',
        tabStatusKey: '', //标签的切换关键字
        tabTitles: ['采购退货列表'],
        operateBtns: [],
      },
      httpHead: 'P36009',
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '退货单号',
          dataIndex: 'RefundOrderNo',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '退货金额',
          dataIndex: 'TotalRefundAmount',
          width: 100,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '结算状态',
          dataIndex: 'SettlementStatusStr',
          width: 100,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '退货单状态',
          dataIndex: 'RefundStatusStr',
          width: 120,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '同步状态',
          dataIndex: 'SyncStatusStr',
          width: 100,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '物流方式',
          dataIndex: 'LogisticsMethodName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: '76c2a7c9-46de-4f21-9819-f391d29e5da8',
              isShow: (record) => {
                return ![0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '编辑',
              id: 'a4e437db-1d0e-4527-acfe-ec1c78184a00',
              isShow: (record) => {
                return [0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '删除',
              id: 'cbddc58d-8d6d-46d7-a543-bb82c4439caf',
              isShow: (record) => {
                return [0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '撤回',
              id: '553e2d04-30f7-4de6-82d3-6dc5811feda1',
              isShow: (record) => {
                return [1].includes(record.AuditStatus)
              },
            },
            // 采购退货审核通过后，只要还没有下发波次，都能进行作废
            {
              name: '作废',
              id: '59d1aeec-d3f7-4e6d-bc44-c30954f0add0',
              isShow: (record) => {
                return [2].includes(record.AuditStatus) && record.RefundStatus < 3 && record.SyncStatus == 5
              },
            },
            // 已作废
            {
              name: '复制',
              id: '4cc817a8-d52f-49a9-8a1f-630ba8575e3b',
              isShow: (record) => {
                return record.RefundStatus == 99
              },
            },
            //  当出库金额＜申请退货金额时，为已出库，增加完成按钮，释放占用库存
            {
              name: '完成',
              id: '571069f8-026a-4fa2-b2c3-0d786f287aed',
              isShow: (record) => {
                return record.RefundStatus == 3 && record.TotalStockOutAmount < record.TotalRefundAmount
              },
            },
          ],
        },
      ],
      url: {
        list: '/{v}/PurchaseBusiness/QueryPurchaseRefundList',
        tabCount: '',
        delete: '/{v}/PurchaseBusiness/RemovePurchaseRefund',
        cancelOrder: '/{v}/PurchaseBusiness/RevokePurchaseRefundOrder',//撤回
        voidOrder: '/{v}/PurchaseBusiness/BlankOutPurchaseRefund',//作废
        completedOrder: '/{v}/PurchaseBusiness/PurchaseRefundCompleted',//完成
      },
    }
  },
  methods: {
    onOperateClick(key) {
      this.onDetailClick('PurchaReturnGoodsDetail', { id: '', type: 1 })
    },
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('PurchaReturnGoodsDetail', { id: record.Id, type: 1 })
      } else if (key === '编辑') {
        this.onDetailClick('PurchaReturnGoodsDetail', { id: record.Id, type: 2 })
      } else if (key === '删除') {
        this.handleDeleteClick(record.Id, 'PUT')
      } else if (key === '撤回') {
        const that = this
        this.$confirm({
          title: '撤回退货单',
          content: '您确定要撤回该退货单吗？',
          onOk() {
            that.cancelOrder(record.Id)
          },
          onCancel() { },
        })
      } else if (key === '作废') {
        const that = this
        this.$confirm({
          title: '作废退货单',
          content: '您确定要作废该退货单吗？',
          onOk() {
            that.voidOrder(record.Id)
          },
          onCancel() { },
        })
      } else if (key === '复制') {
        // Id isCopy true
        this.$refs.tuihuoModal.show(record.Id, true)
      } else if (key === '完成') {
        const that = this
        this.$confirm({
          title: '完成退货单',
          content: '您确定要完成该退货单吗？',
          onOk() {
            that.completedOrder(record.Id)
          },
          onCancel() { },
        })
      }
    },
    // 完成退货单
    completedOrder(Id) {
      if (!Id) return
      this.loading = true
      getAction(this.url.completedOrder, { purchaseRefundId: Id }, this.httpHead).then((res) => {
        this.loading = false
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          this.loadData()
          return
        }
        this.$message.success('操作成功!')
        this.loadData()
      })
    },
    // 作废退货单
    voidOrder(Id) {
      if (!Id) return
      this.loading = true
      getAction(this.url.voidOrder, { purchaseRefundId: Id }, this.httpHead).then((res) => {
        this.loading = false
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          this.loadData()
          return
        }
        this.$message.success('操作成功!')
        this.loadData()
      })
    },
    // 撤回退货单
    cancelOrder(Id) {
      if (!Id) return
      this.loading = true
      postAction(this.url.cancelOrder + '?Id=' + Id, {}, this.httpHead).then((res) => {
        this.loading = false
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          this.loadData()
          return
        }
        this.$message.success('操作成功!')
        this.loadData()
      })
    },
  },
}
</script>
<style scoped>

</style>
