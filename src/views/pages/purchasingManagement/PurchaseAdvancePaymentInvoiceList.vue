<!--
 * @Author: LP
 * @Description: 采购发票列表
 * @Date: 2023/07/08
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
    />
  </div>
</template>
<script>
import { postAction} from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PurchaseAdvancePaymentInvoiceList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierName',
        },
        {
          name: '上传发票单号',
          type: SEnum.INPUT,
          key: 'RemittedOrderNo',
        },
        {
          name: '发票号',
          type: SEnum.INPUT,
          key: 'InvoiceCode',
        },
        {
          name: '订单编号',
          type: SEnum.INPUT,
          key: 'PurchaseOrderNo',
        },
        {
          name: '审核状态',
          type: SEnum.ENUM,
          key: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
        },
        {
          name: '执行状态',
          type: SEnum.ENUM,
          key: 'ExecuteStatus',
          dictCode: 'EnumExecuteStatus',
        },
        {
          name: '同步状态',
          type: SEnum.ENUM,
          key: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
        },
        {
          name: '发票单状态',
          type: SEnum.ENUM,
          key: 'InvoiceOrderStatus',
          dictCode: 'EnumPurchaseRedOffSetStatus',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '单据编号',
          type: SEnum.INPUT,
          key: 'DocumentNo',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
        {
          name: '审核时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'AuditTime',
        },
        {
          name: '责任人',
          type: SEnum.INPUT,
          key: 'OwnerByName',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        curStatus: '',
        tabStatusKey: '', //标签的切换关键字
        tabTitles: ['采购发票列表'],
        rowKey: 'Id',
        selectType: null,
        rowSelectionCheckbox: 'checkbox',
        operateBtns: [
          {
            name: '上传发票',
            type: 'primary',
            icon: 'plus',
            key: '上传发票',
            id: '87413bc4-e281-40b7-92b3-2639d5a4c788',
          },
          { name: '取消导出', icon: '', isHide: true, key: '取消导出' },
          { name: '确定导出', type: 'primary', icon: '', isHide: true, key: '确定导出' },
          {
            name: '导出发票信息',
            type: 'primary',
            icon: 'download',
            key: '导出发票信息',
            id: '2df143b9-9fab-4f18-aafb-eb3849be6e87',
          },
          {
            name: '导出全部发票',
            type: 'primary',
            icon: 'download',
            key: '导出全部发票',
            id: 'fb8c1c22-59f3-4a91-86a6-80f1b395b1a7',
          },
        ],
      },
      httpHead: 'P36009',
      selectedRowKeys: [],
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '上传发票单号',
          dataIndex: 'RemittedOrderNo',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },

        {
          title: '执行状态',
          dataIndex: 'ExecuteStatusStr',
          width: 100,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票单状态',
          dataIndex: 'InvoiceOrderStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '审核时间',
          dataIndex: 'AuditTime',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: 'e67be337-d04a-4186-869a-68452feb58fb',
              isShow: (record) => {
                //未提交 = 0, 审核中 = 1, 审核通过 = 2, 审核失败 = 3
                return ![0,3].includes(record.AuditStatus)
              },
            },
            {
              name: '编辑',
              id: '1a0a4c40-cb34-4d43-b537-ac380afab273',
              isShow: (record) => {
                // 冲红后不能编辑和删除
                return [0,3].includes(record.AuditStatus) && record.InvoiceOrderStatus != 4101
              },
            },
            {
              name: '删除',
              id: 'e5158354-8205-46d7-a848-538f5807882c',
              isShow: (record) => {
                // 冲红后不能编辑和删除
                return [0,3].includes(record.AuditStatus) && record.InvoiceOrderStatus != 4101
              },
            },
            {
              name: '撤回',
              id: '5b35908c-23c1-4962-84d1-4d8ce7a2f755',
              isShow: (record) => {
                return [1].includes(record.AuditStatus)
              },
            },
          ],
        },
      ],
      url: {
        list: '/{v}/PurchaseRemittedOrder/QueryPurchaseSubsistInvoice',
        delete: '/{v}/PurchaseRemittedOrder/DeletePurchaseRemittedOrder',
        exportAllUrl: '/{v}/PurchaseRemittedOrder/ExportAllAsync',
        exportUrl: '/{v}/PurchaseRemittedOrder/ExportAsync',
        cancelOrder: '/{v}/PurchaseRemittedOrder/RevokePurchaseRemittedOrder',//撤回
      },
    }
  },
  watch: {
    selectedRowKeys(val) {
      if (val && val.length > 0 && this.tab.operateBtns[1].isHide == false) {
        this.tab.operateBtns[2].isHide = false
      } else {
        this.tab.operateBtns[2].isHide = true
      }
    },
  },
  methods: {
    onOperateClick(key) {
      if (key == '上传发票') {
        this.onDetailClick('ApplyForPaymentPage', { type: 2 })
      } else if (key == '导出全部发票') {
        this.closeImport()
        let queryParam = {
          ...this.$refs.searchView.queryParam,
          ...this.queryParam,
        }
        let array = ['AuditStatus', 'ExecuteStatus', 'PushERPStatus']
        array.map((item) => {
          if (queryParam[item]) {
            queryParam[item] = Number(queryParam[item])
          } else {
            delete queryParam[item]
          }
        })
        this.handleExportXls('采购发票列表', 'post', this.url.exportAllUrl, null, this.httpHead, queryParam)
      } else if (key == '导出发票信息') {
        this.tab.selectType = this.tab.rowSelectionCheckbox
        this.tab.operateBtns[1].isHide = false
      } else if (key == '取消导出') {
        this.closeImport()
      } else if (key == '确定导出') {
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning('请选择要导出的数据')
          return
        }
        let params = {
          PurchaseInvoiceIds: this.selectedRowKeys,
        }
        const now = new Date()
        const year = now.getFullYear()
        const month = now.getMonth() + 1
        const day = now.getDate()
        const hour = now.getHours()
        const minute = now.getMinutes()
        const second = now.getSeconds()
        const nowTime = `${year}${month < 10 ? '0' + month : month}${day < 10 ? '0' + day : day}${hour}${
          minute < 10 ? '0' + minute : minute
        }${second < 10 ? '0' + second : second}`
        let exportTitle = '采购发票列表' + nowTime.toString() + '(共' + this.selectedRowKeys.length + '条记录)'
        this.handleExportXls(exportTitle, 'post', this.url.exportUrl, null, this.httpHead, params, null, () => {
          // this.$message.success('导出成功')
          this.closeImport()
          this.loadData(1)
        })
      }
    },
    closeImport() {
      this.tab.operateBtns[1].isHide = true
      this.tab.operateBtns[2].isHide = true
      this.tab.selectType = null
      this.selectedRowKeys = []
    },
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('PurchaseAdvancePaymentInvoiceDetail', { id: record.Id })
      } else if (key === '编辑') {
        this.onDetailClick('ApplyForPaymentPage', { type: 2, id: record.Id })
      } else if (key === '删除') {
        this.handleDeleteClick(record.Id)
      } else if (key === '撤回') {
        const that = this
        this.$confirm({
          title: '撤回发票单',
          content: '您确定要撤回该发票单吗？',
          onOk() {
            that.cancelOrder(record.Id)
          },
          onCancel() { },
        })
      }
    },
     // 撤回发票单
     cancelOrder(Id) {
      if (!Id) return
      this.loading = true
      postAction(this.url.cancelOrder, { Id: Id }, this.httpHead).then((res) => {
        this.loading = false
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          this.loadData()
          return
        }
        this.$message.success('操作成功!')
        this.loadData()
      })
    },
  },
}
</script>
<style scoped>

</style>
