<!--
 * @Author: LP
 * @Description: 采购退货详情
 * @Date: 2023/07/12
-->
<template>
  <a-card :bodyStyle="{ padding: '0 0 16px  0' }">
    <a-tabs v-model="activeTabKey" v-if="opType == 1">
      <a-tab-pane :key="1" tab="详情信息">
        <POrderTuihuoEditAndDetail :id="orderId" v-if="activeTabKey == 1 && orderId" :isEdit="false" />
      </a-tab-pane>
      <a-tab-pane :key="2" tab="出库记录">
        <POrderOutboundRecords :id="orderId" v-if="activeTabKey == 2" />
      </a-tab-pane>
      <a-tab-pane :key="3" tab="审核信息" style="padding-left: 16px">
        <SupAuditInformation
          :bussinessNo="recordList && recordList.length > 0 ? recordList[0].RecordId : ''"
          v-if="activeTabKey == 3"
        />
      </a-tab-pane>
    </a-tabs>
    <template v-else-if="opType == 2">
      <POrderTuihuoEditAndDetail ref="pOrderTuihuoEditAndDetail" :id="orderId" isEdit v-if="orderId" />
    </template>
    <!-- 底部区域 -->
    <a-affix :offset-bottom="10" class="affix mr16 mt20">
      <a-button @click="goBack(true,true)">返回</a-button>
      <template v-if="opType == 2">
        <YYLButton
          menuId="7bfeb546-cebe-48ab-9658-403f55f4d96a"
          text="保存"
          type="primary"
          :loading="confirmLoading"
          @click="onSubClick(true)"
          class="ml8"
        />
        <YYLButton
          menuId="9a20955d-7529-4fbc-9999-fe72de81e635"
          text="提交审核"
          type="primary"
          :loading="confirmLoading"
          @click="onSubClick(false)"
          class="ml8"
        />
      </template>
    </a-affix>
  </a-card>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { getAction } from '@/api/manage'
export default {
  name: 'PurchaReturnGoodsDetail',
  mixins: [EditMixin],
  data() {
    return {
      model: null,
      loading: false,
      orderId: '', //采购订单id
      httpHead: 'P36009',
      activeTabKey: 1,
      opType: 1, //1 详情 2编辑
      recordList: [],
      confirmLoading: false,
      url: {
        record: '/{v}/PurchaseBusiness/GetPurchaseRefundAuditRecordListById',
      },
    }
  },
  mounted() {
    if (this.$route.query) {
      this.orderId = this.$route.query.id || ''
      this.opType = this.$route.query.type || 1

      this.loadRecordList()
    }
  },
  methods: {
    onSubClick(IsDraft) {
      if (this.$refs.pOrderTuihuoEditAndDetail) {
        this.$refs.pOrderTuihuoEditAndDetail.onSubmitAuditClick(IsDraft,(bool) => {
          this.confirmLoading = bool
        })
      }
    },
    loadRecordList() {
      let that = this
      getAction(that.url.record, { id: this.orderId }, that.httpHead).then((res) => {
        if (res.IsSuccess) {
          that.recordList = res.Data || []
        } else {
          that.$message.error(res.Msg)
        }
      })
    },
  },
}
</script>

<style scoped>

</style>
