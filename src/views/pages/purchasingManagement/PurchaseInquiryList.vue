<template>
  <div>
    <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <TableNewView
      ref="tableView"
      :table="table"
      :columns="columns"
      :tabDataList="tabDataList"
      :dataList="dataSource"
      @operate="operate"
    >
    </TableNewView>
    <!-- 批量导入 导入报价 -->
    <BatchImportSimpleModal
      :diyStyle="{ width: '80vw' }"
      ref="iBatchImportGoodsModal"
      modalTitle="导入报价"
      :tableColumns="goodsImportColumns"
      :topViewData="model"
      :searchParamsList="searchImportGoodsInput"
      :importConfig="goodsImportConfig"
      :importUrl="importUrl"
      importHttpHead="P36009"
      @ok="(e) => handleBatchImportModalOk(e, 'goods')"
      @downloadTempFileDiy="downExprt"
      :okBefore="beforeImport"
    >
      <div slot="topView">
        <a-form-model ref="form" layout="inline" :rules="rules" :model="model">
          <a-form-model-item label="供应商是否建档：" prop="IsSupplierRegistered">
            <a-radio-group name="radioGroup" v-model="model.IsSupplierRegistered" @change="IsSupplierRegisteredChange">
              <a-radio :value="true"> 已建档 </a-radio>
              <a-radio :value="false"> 未建档 </a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item v-if="model.IsSupplierRegistered" label="供应商名称：" prop="SupplierId">
            <SingleInputSearchView
              placeholder="请选择"
              width="100%"
              :httpParams="{
                pageIndex: 1,
                pageSize: 200,
                AuditStatus: 2,
                IsValid: true,
              }"
              keyWord="KeyWord"
              httpHead="P36003"
              :Url="url.supplierList"
              :value="model.SupplierId"
              :dataKey="{ name: 'Name', value: 'Id' }"
              :name="model.SupplierName"
              :isAbsolute="true"
              @clear="IsSupplierRegisteredChange"
              @change="changeSearchInput"
            />
          </a-form-model-item>
          <a-form-model-item v-else label="供应商名称：" prop="SupplierName">
            <a-input type="text" :maxLength="100" placeholder="请输入" v-model.trim="model.SupplierName"> </a-input>
          </a-form-model-item>
        </a-form-model>
      </div>
    </BatchImportSimpleModal>

    <!-- 邀请报价 -->
    <InvitationForQuotation ref="InvitationForQuotation" />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { PublicMixin } from '@/mixins/PublicMixin' //非必要 公用的 有需要时候用其他mixin也行
import { getAction, postAction, putAction } from '@/api/manage'
const goodsImportColumns = [
  {
    title: '商品编号',
    dataIndex: 'ErpGoodsCode',
    ellipsis: true,
    width: 150,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '商品名称',
    ellipsis: true,
    dataIndex: 'ErpGoodsName',
    width: 200,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '规格',
    width: 120,
    dataIndex: 'PackingSpecification',
  },
  {
    title: '生产厂家',
    dataIndex: 'BrandManufacturer',
    ellipsis: true,
    width: 150,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '批准文号',
    dataIndex: 'ApprovalNumber',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '求购数量',
    dataIndex: 'PurchaseQuantity',
    width: 150,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '单价',
    dataIndex: 'QuotationPrice',
    scopedSlots: { customRender: 'price' },
    width: 100,
    ellipsis: true,
  },
  {
    title: '供货数量',
    dataIndex: 'SupplyQuantity',
    width: 100,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '效期优于',
    dataIndex: 'PreferredExpiryDate',
    width: 100,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '异常原因',
    dataIndex: 'ErrorInfo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 80,
    actionBtns: [{ name: '移除', icon: '' }],
    scopedSlots: { customRender: 'action' },
  },
]

export default {
  title: '采购询价',
  name: 'PurchaseInquiryList',
  mixins: [ListMixin, PublicMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '询价名称', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'InquiryName', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '创建人',
          type: this.$SEnum.INPUT,
          key: 'CreateByName',
          placeholder: '请输入',
        },
        {
          name: '创建时间',
          type: this.$SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTimeBegin',
          keyEnd: 'CreateTimeEnd',
          showTime: true,
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '商品',
          type: this.$SEnum.INPUT,
          key: 'GoodsKey',
          placeholder: '请输入',
        },
      ],
      queryParam: {},
      table: {
        curStatus: 1,
        tabStatusKey: 'ApproavalStatus',
        operateBtns: [
          { name: '新增询价', type: 'primary', icon: 'plus', key: 'add', id: 'ac75a9ec-444f-4e68-bcd0-04074daa47d4' },
        ], //右上角按钮集合
        rowKey: 'Id',
      },
      tabDataList: [],
      columns: [
        {
          title: '询价名称',
          dataIndex: 'InquiryName',
          align: 'center',
          ellipsis: true,
          width: 250,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '询价商品数',
          width: 150,
          align: 'center',
          dataIndex: 'InquiryGoodsCount',
        },
        {
          title: '报价次数',
          align: 'center',
          width: 150,
          dataIndex: 'QuotationCount',
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          align: 'center',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          align: 'center',
          width: 160,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 250,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              isShow: (record) => {
                return true
              },
            },
            {
              name: '导入报价',
              icon: '',
              specialShowFuc: (e) => {
                return true
              },
            },

            {
              name: '邀请报价',
              icon: '',
              specialShowFuc: (e) => {
                return true
              },
            },
            {
              name: '删除',
              icon: '',
              isShow: (e) => {
                let record = e || {}
                return record.QuotationCount <= 0
              },
            },
          ],
        },
      ],

      model: {
        SupplierId: '',
        SupplierName: '',
        IsSupplierRegistered: true,
      },
      // 导入的参数
      searchImportGoodsInput: [
        {
          name: '商品', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'GoodsKeyword', //搜索key 必填
          placeholder: '请输入',
        },
        {
          name: '生产厂家', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'BrandManufacturerKeyword', //搜索key 必填
          placeholder: '请输入',
        },
      ],
      importUrl: '',
      goodsImportConfig: {
        templateFileName: '四川欣佳能达医药有限公司询价表', //下载模板名字
        ErrorShowKey: 'ErrorInfo', //异常信息字段名称
        saveKey: 'BatchId',
        saveUrlType: 'POST', //保存接口请求方式
        saveUrl: '/{v}/PurchaseQuotation/ConfirmImportQuotationAsync', //保存接口
        listQueryKey: 'BatchId', //列表的时候需要用到的字段
        batchRemoveId: '', //批量移除id
        listRemoveType: 'DELETE', //列表删除接口请求方式
        listRemoveUrl: '/{v}/PurchaseQuotation/DeleteImportQuotationDetailAsync', //列表删除 接口
        listRemoveKey: '', //列表删除 参数 key
        listHttpHead: 'P36009',
        listUrl: '/{v}/PurchaseQuotation/GetQuotationImportDetailPageListAsync', //列表的请求接口
        listUrlType: 'GET', //列表接口请求方式
        queryParamStatusKey: 'Status', //列表查询 异常 正常 key
        noLoadData: true, //是否默认弹出时候不加载数据
        importResKey: 'BatchId', //导出完成后返回的key 列表的时候用到
        dataSourceListKey: 'ImportDetailList',
        clearUrlType: 'DELETE',
        clearUrl: '/{v}/PurchaseQuotation/DeleteImportQuotationAsync',
        clearSaveKey: 'BatchId',
        haveDataSourceKey: 'ErpGoodsCode',
        onlySaveOneData: null,
        bottomBtn: [
          { name: '取消', type: '', funType: 'handleCancel' },
          {
            name: '模板下载',
            type: 'primary',
            funType: 'downloadTempFile',
            httpHead: 'P36009',
            urlType: 'GET',
            url: '/{v}/PurchaseQuotation/GetToQuotationOrderFileAsync',
            params: { purchaseInquiryOrderId: '' },
          },
          { name: '确认', type: 'primary', funType: 'handleOk' },
        ],
      },
      goodsImportColumns: goodsImportColumns,
      httpHead: 'P36009',
      isInitData: false,
      url: {
        supplierList: '/{v}/Supplier/GetSuppliersForSelect', //获取供应商接口
        listType: 'POST', //列表接口请求类型
        list: '/{v}/PurchaseInquiryOrder/List', //列表数据接口
        delete: '/{v}/PurchaseInquiryOrder/DeletePurchaseInquiry',
      },
    }
  },
  computed: {
    rules() {
      return {
        SupplierName: [{ required: true, message: '供应商不能为空', trigger: 'blur' }],
        SupplierId: [{ required: true, message: '供应商不能为空', trigger: 'change' }],
      }
    },
  },
  created() {},
  methods: {
    loadBefore() {},
    loadAfter() {},
    changeSearchInput(val, txt) {
      this.$set(this.model, 'SupplierId', val)
      this.$set(this.model, 'SupplierName', txt)
      this.$refs.form.clearValidate()
    },
    IsSupplierRegisteredChange() {
      this.$set(this.model, 'SupplierId', '')
      this.$set(this.model, 'SupplierName', '')
    },
    // 操作
    operate(record, key, index) {
      console.log(record, key, index)
      if (key == '详情') {
        this.goPage('/pages/purchasingManagement/PurchaseInquiryDetail', {
          Id: record.Id,
          InquiryName: encodeURIComponent(record.InquiryName),
        })
      } else if (key == '导入报价') {
        // 重置表格
        // this.model = this.$options.data().model
        this.model = {IsSupplierRegistered: true}
        let queryParam = {
          purchaseInquiryOrderId: record.Id,
        }
        this.importUrl = '/v1/PurchaseQuotation/ImportQuotationAsync?purchaseInquiryOrderId=' + record.Id
        let mbDataIndex = this.goodsImportConfig.bottomBtn.findIndex((item) => {
          return item.name == '模板下载'
        })
        if (mbDataIndex > -1) {
          this.goodsImportConfig.bottomBtn[mbDataIndex].params = {
            purchaseInquiryOrderId: record.Id,
          }
        }
        this.$refs.iBatchImportGoodsModal.show([], queryParam)
      } else if (key == '邀请报价') {
        this.$refs.InvitationForQuotation.show(record)
      } else if (key == '删除') {
        this.handleDeleteClick(record.Id, 'DELETE')
      } else if (key == 'add') {
        // this.goPage('/pages/purchasingManagement/AddInquiry')
        this.goPage('/pages/purchasingManagement/AddInquiry')
      }
    },
    // 导入保存
    handleBatchImportModalOk(data, type) {
      // const ids =
      //   this.dataSource.map((f) => {
      //     return f.GoodsSpuId
      //   }) || []
      // let newData = data.filter((f) => !ids.includes(f))
      // if (data.length > 0) {
      //   if (type == 'goods') {
      //     this.dataSource = this.dataSource.concat(newData)
      //   }
      // }
      this.loadData()
    },
    // 自定义下载模板事件
    downExprt(item) {
      const newParams = item.params
      const url = item.url
      this.handleExportXls('四川欣佳能达医药有限公司询价表', 'Get', url, null, this.httpHead, newParams)
    },
    // 导入报价确认的前置事件
    beforeImport() {
      let result = null
      // 触发表单验证
      this.$refs.form.validate((val) => {
        if (val) {
          // 服务端这儿需要特殊处理
          this.model.SupplierId = this.model.SupplierId ? this.model.SupplierId : null
          result = true
        } else {
          result = false
        }
      })
      return result
    },
  },
}
</script>
<style scoped lang="scss"></style>
