<!--
 * @Author: LP
 * @Description: 采购预付款结算详情
 * @Date: 2023-12-25 14:51:21
-->
<template>
  <a-card :bodyStyle="{ padding: '0 0' }">
    <a-tabs v-model="activeTabKey">
      <a-tab-pane :key="1" tab="详情信息">
        <a-spin :spinning="loading">
          <SettlementView
            v-if="activeTabKey == 1"
            ref="settlementView"
            :isEdit="false"
            :id="id"
            @setAuditId="onSetAuditId"
          />
        </a-spin>
      </a-tab-pane>
      <a-tab-pane :key="2" tab="审核信息" style="padding-left: 16px">
        <SupAuditInformation :bussinessNo="auditId" v-if="activeTabKey == 2 && auditId" />
      </a-tab-pane>
    </a-tabs>
    <a-affix :offset-bottom="10" class="affix pr16">
      <div class="pd1016">
        <a-button @click="goBack(true,true)">返回</a-button>
      </div>
    </a-affix>
  </a-card>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'PurPrepaymentSettlementDetail',
  mixins: [EditMixin],
  data() {
    return {
      loading: false,
      id: '', //id
      httpHead: 'P36009',
      auditId: '',
      activeTabKey: 1,
      url: {},
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
    }
  },
  created() {},
  methods: {
    onSetAuditId(id) {
      this.auditId = id
    },
  },
}
</script>

<style scoped>

</style>
