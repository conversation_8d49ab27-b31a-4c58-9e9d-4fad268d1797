<template>
  <div>
    <a-card style="margin-bottom: 20px">
      <div class="head-title">
        <h1>询价详情</h1>
        <a-button @click="goBack(true,true)">返回</a-button>
      </div>
    </a-card>
    <a-tabs default-active-key="1" @change="tabsChange">
      <a-tab-pane key="1" tab="比价结果" force-render>
        <ComparisonResults  v-if="activeTab == 1"/>
      </a-tab-pane>
      <a-tab-pane key="2" tab="询价信息" force-render>
        <InquiryInformation v-if="activeTab == 2"/>
      </a-tab-pane>
      <a-tab-pane key="3" tab="报价记录" force-render>
        <QuotationRecord v-if="activeTab == 3"/>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { PublicMixin } from '@/mixins/PublicMixin' //非必要 公用的 有需要时候用其他mixin也行
import { getAction, postAction, putAction } from '@/api/manage'
export default {
  title: '询价详情',
  name: 'PurchaseInquiryDetail',
  mixins: [ListMixin, PublicMixin],
  components: {},
  data() {
    return {
      activeTab: 1,
    }
  },
  computed: {},
  created() {},

  methods: {
    // tabs切换事件
    tabsChange(e) {
     this.activeTab = e
    },
  },
}
</script>
<style scoped lang="scss">
.head-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  & > h1 {
    font-weight: bold;
  }
}
</style>
