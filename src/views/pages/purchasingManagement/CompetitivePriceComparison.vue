<template>
  <a-card>
    <a-row>
      <a-col span="8"></a-col>
      <a-col span="8" style="text-align: center"><a :href="webUrl" target="_blank">去比价系统查看》</a></a-col>
      <a-col span="8"></a-col>
    </a-row>
  </a-card>
</template>

<script>
import { apiHead } from '@/api/manage'
import Vue from 'vue'
import { ACCESS_TOKEN, USER_ID, USER_NAME } from '@/store/mutation-types'
export default {
  name: 'CompetitivePriceComparison',
  data() {
    return {
      webUrl: 'http://ih.hysyyl.com:35200',
    }
  },
  created() {
    this.initWebUrl()
    console.log('path', this.webUrl)
    window.open(this.webUrl, '_blank')
  },
  mounted() { },
  methods: {
    initWebUrl() {
      let appToken = '&appToken=' + this.getToken() + '&appUserId=' + this.getUserId() + '&appUserName=' + this.getUserName()
      this.webUrl = apiHead('SRM_SITE') + appToken
      // console.log('webUrl', this.webUrl)
    },
    getToken() {
      const token = Vue.ls.get(ACCESS_TOKEN)
      return token
    },
    getUserId() {
      return apiHead('SRM_SITE_USERID') //Vue.ls.get(USER_ID)
    },
    getUserName() {
      return apiHead('SRM_SITE_USERNAME') //Vue.ls.get(USER_NAME)
    },
  },
}
</script>

<style></style>
