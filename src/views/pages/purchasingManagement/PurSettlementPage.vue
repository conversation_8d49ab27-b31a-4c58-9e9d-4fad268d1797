<!--
 * @Author: LP
 * @Description: 预付款结算界面
 * @Date: 2023-12-22 10:32:23
-->
<template>
  <a-card :bodyStyle="{ padding: '0 0 10px 0' }" :bordered="false">
    <SettlementView ref="settlementView" isEdit :id="id" :cLoading="confirmLoading" />
    <a-affix :offset-bottom="10" class="affix">
      <div class="pd1016">
        <YYLButton
          menuId="872eede2-281e-4ade-abfa-9ee233df841f"
          text="提交审核"
          type="primary"
          :loading="confirmLoading"
          @click="onSaveAndAuditClick"
        />
        <a-button @click="goBack(true,true)">取消</a-button>
      </div>
    </a-affix>
  </a-card>
</template>
<script>
import { postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'PurSettlementPage',
  mixins: [EditMixin, ListMixin],
  components: {},
  data() {
    return {
      confirmLoading: false,
      id: '',
      httpHead: 'P36009',
      url: {
        create: '/{v}/PurchaseSettlement/CreatePurchaseSettlementOrder',
        update: '/{v}/PurchaseSettlement/EditPurchaseSettlementOrder',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.setTitle('申请结算')
    }
  },
  methods: {
    onSaveAndAuditClick() {
      this.$refs.settlementView.checkForm((data) => {
        if (data) {
          console.log('data', data)
          this.saveAndAudit(data)
        }
      })
    },
    saveAndAudit(data) {
      let that = this
      that.confirmLoading = true
      postAction(that.id ? that.url.update : that.url.create, data, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.createApproval(
              {
                BussinessNo: res.Data,
                Scenes: 31,
                OpratorId: that.getLoginUserId(),
              },
              (bool) => {
                if (bool) {
                  that.goBack()
                }
              }
            )
          } else {
            that.$message.error(res.Msg)
            that.confirmLoading = false
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          // that.confirmLoading = false
        })
    },
    /**
     * 创建审核实例
     * @param {*} callback
     */
    createApproval(params, callback) {
      let that = this
      if (!that.confirmLoading) {
        that.confirmLoading = true
      }
      postAction('/{v}/ApprovalWorkFlowInstance/CreateApproval', params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            callback && callback(true)
          } else {
            that.$message.error(res.Msg)
            callback && callback(false)
            that.confirmLoading = false
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
  },
}
</script>
<style scoped>

</style>
