<!--
 * @Description: 安全库存 列表
 * @Version: 1.0
 * @Author: Harmon<PERSON>
 * @Date: 2025-03-27 13:48:32
 * @LastEditors: haolin.hu
 * @LastEditTime: 2025-04-30 00:55:41
-->

<template>
  <div class="custom-tailwind">
    <!-- 搜索 -->
    <YYLForm
      v-model="searchFormData"
      class="pt-16 px-16"
      :formFields="searchFormFields"
      :isSearchMode="true"
      :col="{ md: 6, sm: 24 }"
      @reset="onReset()"
      @search="$refs.tableRef.getList(true)"
    />

    <!-- 导出 -->
    <div class="mb-12 px-16 flex justify-end">
      <a-button 
        type="primary" 
        v-has="'8c00a69c-1739-4489-9850-7c57d5497780'"
        :loading="exportLoading"
        @click="onExport()"
      >
        导出
      </a-button>
    </div>

    <!-- 数据列表 -->
    <YYLTable
      ref="tableRef"
      class="px-16"
      url="/{v}/PurchaseGoods/GetGoodsSafetyInventoryList"
      httpHead="P36009"
      method="GET"
      rowKey="Id"
      :queryParams="searchFormData"
      :columns="columns"
      :scrollX="2250"
    />

    <!-- 历史记录 弹窗 -->
    <SafeStockRecordModal ref="recordModalRef" />
    <!-- 在途库存 -->
    <PlanTotalInTransitCountModal ref="PlanTotalInTransitCountModal" />
  </div>
</template>

<script>
import { exportXLS } from '@/utils/util'

export default {
  name: 'SafeStockList',
  data() {
    return {
      exportLoading: false,
      searchFormData: {
        KeyWord: null,
        BrandManufacturer: null,
        PurchaseBy: null,
        CreateTime: [],
        CreateTimeBegin: null,
        CreateTimeEnd: null
      },
      searchFormFields: [
        {
          key: 'KeyWord',
          label: '商品',
          type: 'input',
          placeholder: '名称/SPZ码/编号'
        },
        {
          key: 'BrandManufacturer',
          label: '生产厂家',
          type: 'input',
          placeholder: '请输入'
        },
        {
          key: 'PurchaseBy',
          label: '采购人',
          type: 'input',
          placeholder: '请输入'
        },
        {
          key: 'CreateTime',
          label: '生成时间',
          type: 'dateRange',
          placeholder: ['开始日期', '结束日期'],
          valueFormat: 'YYYY-MM-DD',
          onChange: (val) => {
            console.log('val', val)
            if (val && val.length) {
              this.searchFormData.CreateTimeBegin = val[0]
              this.searchFormData.CreateTimeEnd = val[1]
            } else {
              this.searchFormData.CreateTimeBegin = null
              this.searchFormData.CreateTimeEnd = null
            }
          }
        },
      ],
      columns: [
        { title: '商品名称', key: 'ErpGoodsName', width: 200},
        { title: '商品编号', key: 'ErpGoodsCode', width: 100},
        { title: 'SPZ码', key: 'ErpGoodsId', width: 120},
        { title: '商品规格', key: 'PackingSpecification', width: 200},
        { title: '生产厂家', key: 'BrandManufacturer', width: 500},
        { title: '采购人', key: 'PurchaseByName', width: 90, align: 'center'},
        { title: '30天销量', key: 'TotalSalesCount_30', width: 120, align: 'center'},
        { title: '近30天日均销量', key: 'DailyAverageSalesCount_30', width: 120, align: 'right'},
        { title: '可销总库存', key: 'CanSaleInventory', width: 120, align: 'right'},
        { title: '在途库存', key: 'InTransitInventory', width: 120, align: 'right', isLink: true, onClick: (record) => {
          this.$refs.PlanTotalInTransitCountModal.show(record)
        }},
        { title: '采销可售天数', key: 'SellableDays', width: 120, align: 'right'},
        { title: '建议采购数量', key: 'RecommendedPurchaseQuantity', width: 120, align: 'right'},
        { title: '生成时间', key: 'CreateTime', align: 'center'},
        { 
          title: '操作',
          key: 'action',
          width: 120,
          actionBtns: [
            { 
              key: 'record',
              permissionId: 'bdb687a4-303f-4908-913d-180b41add630',
              text: '历史记录',
              onClick: (record) => {
                this.$refs.recordModalRef.show(record.GoodsSpuId)
              }
            }
          ]
        },
      ],
    }
  },
  methods: {
    onExport() {
      this.exportLoading = true
      exportXLS({
        fileName: '安全库存记录表.xls',
        url: '/{v}/PurchaseGoods/ExportGoodsSafetyInventory',
        method: 'POST',
        httpHead: 'P36009',
        params: this.searchFormData,
        callBack: () => {
          this.exportLoading = false
        }
      })
    },
    onReset() {
      this.searchFormData.CreateTimeBegin = null
      this.searchFormData.CreateTimeEnd = null
      this.$refs.tableRef.getList(true)
    }
  }
}
</script>