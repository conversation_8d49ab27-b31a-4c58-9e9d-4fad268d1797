<template>
  <div style="padding-top: 30px">
    <div style="padding-left: 16px">
      <a-form-model :form="form" ref="ruleForm" :model="form" :rules="rules">
        <a-row :gutter="10">
          <a-col :md="queryCol">
            <a-form-model-item label="询价名称" prop="InquiryName">
              <a-input style="width: 100%" placeholder="请输入" v-model="form.InquiryName" :maxLength="50"></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <a-card class="noborderBottom">
      <h1>询价商品</h1>
    </a-card>
    <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <TableNewView
      ref="tableView"
      :table="table"
      :columns="columns"
      :tabDataList="tabDataList"
      :dataList="dataSource"
      @operate="operate"
    >
      <span slot="PurchaseQuantity" slot-scope="{ text, record }">
        <a-input-number
          style="width: 100%"
          v-model="record['PurchaseQuantity']"
          @blur="inputChange($event, record)"
          placeholder="请输入"
          :maxLength="20"
          :min="0"
          step="1"
          :precision="0"
        />
      </span>
    </TableNewView>
    <a-affix :offset-bottom="0" style="float: right; width: 100%; text-align: right">
      <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
        <a-button style="margin-right: 8px" @click="goBack(true,true)">返回</a-button>
        <a-button type="primary" @click="addInquiry" :loading="confirmLoading">保存</a-button>
      </a-card>
    </a-affix>
    <!-- 表格选择商品数据弹窗 -->
    <TableSelectDataModal
      ref="TableSelectDataModal"
      :type="'checkbox'"
      :selectTableData="selectTableData"
      showSelectedData
      showChooseAll
      @chooseData="chooseData"
    ></TableSelectDataModal>
    <!-- 批量导入 导入商品 -->
    <BatchImportSimpleModal
      ref="iBatchImportGoodsModal"
      modalTitle="导入询价商品"
      :tableColumns="goodsImportColumns"
      :searchParamsList="searchImportGoodsInput"
      :importConfig="goodsImportConfig"
      :importUrl="`/{v}/PurchaseInquiryOrder/Import`"
      importHttpHead="P36009"
      @ok="(e) => handleBatchImportModalOk(e, 'goods')"
    />
    <!-- 邀请报价 -->
    <InvitationForQuotation ref="InvitationForQuotation" @close="closeInvite"/>
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { PublicMixin } from '@/mixins/PublicMixin' //非必要 公用的 有需要时候用其他mixin也行
import { getAction, postAction, putAction } from '@/api/manage'
import { apiHead } from '@/api/manage'
import { re } from 'mathjs'
const goodsImportColumns = [
  {
    title: '商品编号',
    dataIndex: 'ErpGoodsCode',
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '商品名称',
    ellipsis: true,
    dataIndex: 'ErpGoodsName',
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '规格',
    width: 150,
    dataIndex: 'PackingSpecification',
  },
  {
    title: '生产厂家',
    dataIndex: 'BrandManufacturer',
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '批准文号',
    dataIndex: 'ApprovalNumber',
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '求购数量',
    dataIndex: 'PurchaseQuantity',
    width: 200,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '异常原因',
    dataIndex: 'ErrorShow',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 80,
    actionBtns: [{ name: '移除', icon: '' }],
    scopedSlots: { customRender: 'action' },
  },
]

export default {
  title: '新增询价',
  name: 'AddInquiry',
  mixins: [ListMixin, PublicMixin],
  components: {},
  data() {
    return {
      confirmLoading: false,
      spinning: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      queryCol: 10,
      searchItems: [
        {
          name: '商品', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'ErpGoodsName', //搜索key 必填
          placeholder: '请输入商品名称/编号', //ErpGoodsName/ErpGoodsCode
        },
        {
          name: '生产厂家',
          type: this.$SEnum.INPUT,
          key: 'BrandManufacturer',
          placeholder: '请输入生产厂家',
        },
        {
          name: '供应商名称',
          type: this.$SEnum.INPUT,
          key: 'SupplierName',
          placeholder: '请输入供应商名称',
        },
      ],
      form: {},
      storedataSource: [], // 实际导入的/选择的/删除后的总数据
      rules: {
        InquiryName: [{ required: true, message: '请输入询价名称', trigger: 'blur' }],
      },
      queryParam: {},
      table: {
        curStatus: 1,
        tabStatusKey: 'ApproavalStatus',
        operateBtns: [
          { name: '选择商品', type: 'primary', icon: '', key: 'choose', id: '29d7e349-6546-4907-831d-68ce53e81e49' },
          { name: '导入', type: '', icon: '', key: '导入', id: '4a5ad274-7a36-4990-be08-7bb0b2a0f323' },
          { name: '批量移除', type: '', icon: '', key: 'batchRemoval', id: 'c2eac0dc-23b9-4eae-8185-e5682fd91bb0' },
        ], //右上角按钮集合
        rowKey: 'GoodsSpuId',
        selectType: 'checkbox',
        isSelectCurPage: false,
        customSlot: ['PurchaseQuantity'],
        scrollY: this.$root.tableHeight - 120,
      },
      tabDataList: [],
      columns: [
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          ellipsis: true,
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          width: 150,
          dataIndex: 'PackingSpecification',
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批准文号',
          dataIndex: 'ApprovalNumber',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '求购数量',
          dataIndex: 'PurchaseQuantity',
          width: 200,
          scopedSlots: { customRender: 'PurchaseQuantity' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '移除',
              icon: '',
              specialShowFuc: (e) => {
                return true
              },
            },
          ],
        },
      ],
      // 导入的参数
      searchImportGoodsInput: [
        {
          name: '商品', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'GoodsKey', //搜索key 必填
          placeholder: '名称/编码',
        },
        {
          name: '生产厂家', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'BrandManufacturer', //搜索key 必填
          placeholder: '请输入',
        },
      ],
      searchImportCusInput: [
        {
          name: '客户名称', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'Name', //搜索key 必填
          placeholder: '请输入客户名称',
        },
      ],
      goodsImportConfig: {
        importTitle:'商品',
        templateFileName: '导入询价商品模板', //下载模板名字
        ErrorShowKey: 'ErrorShow', //异常信息字段名称
        saveKey: 'BatchId',
        saveUrlType: 'POST', //保存接口请求方式
        saveUrl: '/{v}/PurchaseInquiryOrder/ImportGoodsConfirm', //保存接口
        listQueryKey: 'BatchId', //列表的时候需要用到的字段
        batchRemoveId: '', //批量移除id
        listRemoveType: 'POST', //列表删除接口请求方式
        listRemoveUrl: '/{v}/PurchaseInquiryOrder/ImportRemove', //列表删除 接口
        listRemoveKey: '', //列表删除 参数 key
        listHttpHead: 'P36009',
        listUrl: '/{v}/PurchaseInquiryOrder/ImportPageList', //列表的请求接口
        listUrlType: 'POST', //列表接口请求方式
        queryParamStatusKey: 'Status', //列表查询 异常 正常 key
        noLoadData: true, //是否默认弹出时候不加载数据
        importResKey: 'BatchId', //导出完成后返回的key 列表的时候用到
        dataSourceListKey: 'ImportDetailList',
        clearUrlType: 'DELETE',
        clearUrl: '/{v}/PurchaseInquiryOrder/ImportDeleteBatchData',
        clearSaveKey: 'BatchId',
        haveDataSourceKey: 'ErpGoodsCode',
        onlySaveOneData: null,
        bottomBtn: [
          { name: '取消', type: '', funType: 'handleCancel' },
          { name: '模板下载', type: 'primary', funType: 'downloadTempFile' },
          { name: '确认', type: 'primary', funType: 'handleOk' },
        ],
      },
      goodsImportColumns: goodsImportColumns,
      httpHead: 'P36009',
      url: {
        // listType: 'GET', //列表接口请求类型
        // list: '/{v}/PurchaseOrder/GetPageList', //列表数据接口
        add: '/{v}/PurchaseInquiryOrder/SaveInquiryOrder',
      },
      selectTableData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          {
            name: '商品名称',
            type: 'input',
            value: '',
            key: 'GoodsKey',
            defaultVal: '',
            placeholder: '请输入商品名称/编号',
          },
          // {
          //   name: '商品类别',
          //   type: 'selectLink',
          //   vModel: 'GoodsManageClassify1',
          //   dataKey: { name: 'Name', value: 'Id' },
          //   httpParams: { Name: '', Level: 1 },
          //   keyWord: 'Name',
          //   defaultVal: '',
          //   placeholder: '请选择',
          //   httpType: 'POST',
          //   url: '/{v}/GoodsManage/QueryGoodsManageClassifyList',
          //   httpHead: 'P36006',
          // },
          {
            name: '生产厂家',
            type: 'input',
            value: '',
            key: 'BrandManufacturer',
            defaultVal: '',
            placeholder: '请输入厂家名称',
          },
        ],
        title: '选择商品',
        name: '商品',
        recordKey: 'GoodsSpuId',
        httpHead: 'P36009',
        isInitData: false,
        url: {
          list: '/{v}/PurchaseInquiryOrder/GetInquiryGoodsList',
          listType: 'POST',
        },
        columns: [
          {
            title: '商品名称',
            dataIndex: 'ErpGoodsName',
            width: 180,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '商品编号',
            dataIndex: 'ErpGoodsCode',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '规格',
            dataIndex: 'PackingSpecification',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '生产厂商',
            dataIndex: 'BrandManufacturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '批准文号',
            dataIndex: 'ApprovalNumber',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '可销库存',
            dataIndex: 'AvailableInventory',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '在途库存',
            dataIndex: 'InTransitInventory',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '采购人员',
            dataIndex: 'PurchaserName',
            width: 100,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
    }
  },
  computed: {},
  created() {},

  methods: {
    loadBefore() {},
    loadAfter() {},
    // 关闭邀请报价弹窗回调
    closeInvite() {
      this.goBack(true,true)
    },
    //  求购数量输入框失去焦点事件
    inputChange(e, record) {
      const val = e.target.value
      // 同步真实本地数据
      this.storedataSource.map(item => {
        if (item.ErpGoodsCode === record.ErpGoodsCode) {
          item.PurchaseQuantity = val
        }
      })
    },
    // 导入保存
    handleBatchImportModalOk(data, type) {
      console.log(data)
      const ids =
        this.storedataSource.map((f) => {
          return f.GoodsSpuId
        }) || []
      let newData = data.filter((f) => !ids.includes(f))
      if (data.length > 0) {
        if (type == 'goods') {
          this.storedataSource = this.storedataSource.concat(newData)
        }
      }
      this.showFilterdataSource()
    },
    importGoods() {
      this.$refs.iBatchImportGoodsModal.show(this.storedataSource)
    },
    operate(record, key, index) {
      console.log(key)
      if (key == '导入') {
        this.importGoods()
      } else if (key === 'choose') {
        this.chooseGoods()
      } else if (key === '移除') {
        this.deleteGoods([record.GoodsSpuId])
      } else if (key === 'batchRemoval') {
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning('请选择数据!')
          return
        }
        this.deleteGoods(this.selectedRowKeys)
      }
    },
    // 移除和批量移除
    deleteGoods(ids) {
      const selectedRowKeys = this.selectedRowKeys
      this.storedataSource =
        this.storedataSource.filter((f) => {
          return !ids.includes(f.GoodsSpuId)
        }) || []

      this.selectedRowKeys =
        this.selectedRowKeys.filter((f) => {
          return !ids.includes(f)
        }) || []
      this.showFilterdataSource()
    },
    // 选择商品选择数据回调
    chooseData(data, type) {
      if (data && data.length > 0) {
        if (type == 'radio') {
          this.storedataSource = data
        } else {
          this.storedataSource = this.storedataSource.concat(data)
        }
        this.showFilterdataSource()
      }
    },
    chooseGoods() {
      let queryParam = {
        IsAll: true
      }
      // this.$refs.TableSelectDataModal.show(dataSource, queryParam)
      this.$refs.TableSelectDataModal.show(this.storedataSource, queryParam)
    },
    // 本地筛选数据
    searchQuery(queryParam, extendParams, searchType) {
      let storedataSource = JSON.parse(JSON.stringify(this.storedataSource))
      if (searchType === 1) {
        if (queryParam) {
          Object.keys(queryParam).forEach((key) => {
            const val = queryParam[key]
            storedataSource =
              storedataSource.filter((f) => {
                if (key === 'ErpGoodsName') {
                  return f[key] &&  (f[key].indexOf(val) !== -1 || f['ErpGoodsCode'].indexOf(val) !== -1)
                } else {
                  return f[key] && f[key].indexOf(val) !== -1
                }
              }) || []
          })
        }
      } else if (searchType === 2) {
        console.log('重置')
        // 重置则回退数据到取未查询前的本地存储数据
        // storedataSource = JSON.parse(JSON.stringify(this.storedataSource))
        this.ipagination.current = 1
      }
      this.dataSource = storedataSource
      this.onClearSelected()
    },
    // 显示筛选后的数据
    showFilterdataSource() {
      this.$refs.searchView.searchQuery(null, 1)
    },
    // 新增询价
    addInquiry() {
      const that = this
      // 触发表单验证
      this.$refs.ruleForm.validate((err, values) => {
        if (err) {
          const storedataSource = JSON.parse(JSON.stringify(that.storedataSource))
          if (storedataSource.length < 1) {
            that.$message.warning('商品数量不能为空')
            return false
          }
          that.confirmLoading = true
          const prchaseInquiryOrderDetails = storedataSource.map((item) => {
            const { GoodsSpuId, PurchaseQuantity } = item
            return {
              GoodsSpuId,
              PurchaseQuantity: PurchaseQuantity ? PurchaseQuantity : null,
            }
          })
          let url = that.url.add
          let formData = {
            InquiryName: that.form.InquiryName,
            prchaseInquiryOrderDetails: prchaseInquiryOrderDetails,
          }
          postAction(url, formData, that.httpHead)
            .then((res) => {
              if (res.IsSuccess) {
                that.$message.success('操作成功')
                let code = res.Data
                this.$confirm({
                  title: '提示',
                  content: '是否立即邀请供应商进行报价？',
                  onOk() {
                    // window.open(`${apiHead('EnquiryUrl')}?code=${code}`, '_blank')
                    that.$refs.InvitationForQuotation.show({ Id: code })
                  },
                  onCancel() {
                    that.goBack(true,true)
                  },
                })
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
  },
}
</script>
<style scoped lang="scss">
.noborderBottom {
  border-bottom: none;
}
</style>
