<!--
 * @Description: 采购订单/计划 - 新增订单 / 新增含特订单
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-06 14:17:02
 * @LastEditors: wenjingGao
 * @LastEditTime: 2025-06-12 17:26:52
-->

<template>
  <a-spin :spinning="pLoading || confirmLoading">
    <a-alert
      v-if="orderId && model && model.RejectionReason"
      type="warning"
      :message="'驳回原因：' + (model.RejectionReason || '--')"
      banner
      :showIcon="false"
      style="margin-bottom: 5px"
    />

    <a-card :bordered="false" :bodyStyle="{ padding: '0px' }">
      <a-card :bordered="false">
        <a-form-model ref="form" :rules="rules" :model="model">
          <a-row :gutter="gutter">
            <!-- 企业信息 -->
            <a-col :span="span">
              <a-form-model-item label="供应商名称" prop="SupplierId">
                <SingleChoiceSearchView
                  ref="SupplierSingleChoiceSearchView"
                  :disabled="!!ifQiuckOrder"
                  :httpParams="{ KeyWord: ifQiuckOrder ? QiuckSuppliersName : '' }"
                  @dataLoaded="SupplierLoad"
                  style="width: 100%"
                  placeholder="请选择"
                  httpHead="P36003"
                  :keyWord="'KeyWord'"
                  Url="/{v}/Supplier/GetSelectPurchaseSupplier"
                  :dataKey="{ name: 'SupplierName', value: 'SupplierId' }"
                  v-model="model.SupplierId"
                  :name="model.SupplierName"
                  @change="
                    (val, txt, item) => {
                      if (val) {
                        $refs.singleChoiceViewTWR.getData('SupplierId', val)
                        model.SupplierId = val
                        model.SupplierName = txt
                      }
                      model.DelegateScopeId = ''
                      if (item && item.PurchaseSupplierErrors && item.PurchaseSupplierErrors.length > 0) {
                        model.SupplierErr = item.PurchaseSupplierErrors[0].ErrorMessage
                      } else {
                        model.SupplierErr = ''
                      }
                    }
                  "
                />
                <span slot="help" class="cred" v-if="model.SupplierErr">{{ model.SupplierErr }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :span="span">
              <a-form-model-item label="委托人" prop="DelegateScopeId">
                <SingleChoiceView
                  ref="singleChoiceViewTWR"
                  style="width: 100%"
                  placeholder="请选择"
                  :httpParams="{ SupplierId: model.SupplierId, OrderType: orderType }"
                  httpHead="P36003"
                  :dataKey="{ name: 'DelegateName', value: 'DelegateId' }"
                  v-model="model.DelegateScopeId"
                  :disabled="!model.SupplierId"
                  Url="/{v}/Supplier/GetSupplierDelegateScope"
                  @dataLoaded="onLoadSuccess"
                  @change="
                    (val, txt, item) => {
                      if (item) {
                        model.DelegateErr = item.ErrorMessage || ''
                        model.DelegateCode = item.DelegateCode || ''
                      } else {
                        model.DelegateErr = ''
                        model.DelegateCode = ''
                      }
                    }
                  "
                />
                <span slot="help" class="cred" v-if="model.DelegateErr">{{ model.DelegateErr }}</span>
              </a-form-model-item>
            </a-col>
            <a-col :span="span">
              <a-form-model-item label="付款方式" prop="PaymentMode">
                <EnumSingleChoiceView
                  style="width: 100%"
                  placeholder="请选择"
                  dictCode="EnumPurchasePaymentMode"
                  v-model="model.PaymentMode"
                />
              </a-form-model-item>
            </a-col>
            <a-col v-if="opType == 2" :span="span">
              <a-form-model-item label="付款类型" prop="PaymentType">
                <EnumSingleChoiceView
                  style="width: 100%"
                  placeholder="请选择"
                  dictCode="EnumPurchasePaymentType"
                  v-model="model.PaymentType"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="span">
              <a-form-model-item label="送货方式" prop="DeliveryMethodId">
                <SingleChoiceView
                  style="width: 100%"
                  placeholder="请选择"
                  :httpParams="{
                    groupPY: 'songhfs',
                  }"
                  :dataKey="{ name: 'ItemValue', value: 'Id' }"
                  @change="
                    (val, txt, item) => {
                      model.DeliveryMethodStr = txt
                    }
                  "
                  httpHead="P36001"
                  v-model="model.DeliveryMethodId"
                  :Url="'/{v}/Global/GetListDictItem'"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="span">
              <a-form-model-item label="预到货日期" prop="ExpectedArrivalDate">
                <!-- @change="(dates, dateStrings) => onEndTimeChange(dates, dateStrings)" -->
                <a-date-picker
                  v-model="model.ExpectedArrivalDate"
                  style="width: 100%"
                  placeholder="请选择"
                  :disabled-date="(current) => disabledDate(current)"
                />
              </a-form-model-item>
            </a-col>
            <a-col v-if="opType == 2" :span="span">
              <a-form-model-item label="政策说明" prop="PolicyStatement">
                <a-input placeholder="请输入" v-model="model.PolicyStatement" style="width: 100%" :maxLength="100" />
              </a-form-model-item>
            </a-col>
            <a-col :span="span">
              <a-form-model-item label="备注" prop="Remarks">
                <a-input placeholder="请输入" v-model="model.Remarks" style="width: 100%" :maxLength="150" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-card>

      <a-card
        :bordered="false"
        title="商品信息"
        :bodyStyle="{ padding: '0 0 20px 0' }"
        :headStyle="{ padding: '0 16px' }"
      >
        <div slot="extra">
          <a-button v-if="opType == 1" type="primary" @click="importGoods">导入商品</a-button>
          <a-button style="margin-left: 10px;" @click="onMulDeleteClick" v-if="selectedRowKeys && selectedRowKeys.length > 0">批量移除</a-button>
        </div>

        <a-table
          :bordered="false"
          ref="table"
          rowKey="ErpGoodsIdAndIndex"
          size="middle"
          :columns="columns"
          :dataSource="dataSource"
          :pagination="false"
          :loading="loading"
          :rowSelection="{
            selectedRowKeys: selectedRowKeys,
            onChange: (sRowKeys, sRows) => {
              onSelectChange(sRowKeys, sRows, 'ErpGoodsId')
            },
            type: 'checkbox',
          }"
          @change="handleTableChange"
          :scroll="{ y: 450, x: '100%' }"
        >
          <span slot="component" slot-scope="text">
            <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
            <j-ellipsis :value="text" v-else />
          </span>
          <span slot="name" slot-scope="text, record, index">
            <a-input-search
              v-if="!record.GoodsSpuId"
              :ref="'inputSearch' + index"
              v-model="searchKey"
              placeholder="名称/拼音码/编号"
              :disabled="!model.SupplierId || !model.DelegateScopeId"
              :loading="searchLoading"
              @search="onSearch"
              @pressEnter="onSearch"
            />
            <template v-else>
              <j-ellipsis :value="text" />
              <p v-if="record['ErrorInfo']" class="redName">
                <j-ellipsis :value="record['ErrorInfo']" />
              </p>
            </template>
          </span>
          <!-- 点击操作 -->
          <span slot="clickA" slot-scope="text, record, index, column">
            <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
              <j-ellipsis :value="'' + text" :length="50" />
            </a>
            <span v-else>--</span>
          </span>
          <!-- 全部点击操作 -->
          <span slot="clickAl" slot-scope="text, record, index, column">
            <a v-if="record.ErpGoodsId" @click="clickA(text, record, index, column)">
              <j-ellipsis :value="text || text == 0 ? '' + text : '--'" :length="50" />
            </a>
            <span v-else>--</span>
          </span>
          <!-- 价格点击操作 -->
          <span slot="clickPriceA" slot-scope="text, record, index, column">
            <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
              <j-ellipsis :value="text == 0 ? '' + text : '¥' + text" :length="50" />
            </a>
            <span v-else>--</span>
          </span>
          <!-- 价格 -->
          <span slot="price" slot-scope="text, record, index, column">
            <j-ellipsis
              v-if="text || text == 0"
              :value="text == 0 ? '' + text : '¥' + Number(text || 0).toFixed(column.precision || 2)"
            />
            <span v-else>--</span>
          </span>

          <span slot="input" slot-scope="text, record, index, column">
            <a-input
              placeholder="请输入"
              v-model="record[column.dataIndex]"
              @pressEnter="onEnterClick(record, index)"
              :disabled="!record.GoodsSpuId"
              :maxLength="column.maxLength || 10"
              @change="(e) => recordRemarkChange(record, column.dataIndex, index)"
            />
          </span>
          <span slot="inputNumber" slot-scope="text, record, index, column">
            <a-input-number
              style="width: 100%"
              v-model="record[column.dataIndex]"
              placeholder="请输入"
              :disabled="
                !record.GoodsSpuId ||
                (column.dataIndex === 'RebateDays' &&
                  record.GoodsSpuId &&
                  ['2', '3'].includes(record.PurchaseRebateFlag))
              "
              :min="
                ['2', '3'].includes(record.PurchaseRebateFlag) && column.dataIndex === 'BoxRebateAmount'
                  ? -*********
                  : column.min || 0
              "
              :max="
                ['2', '3'].includes(record.PurchaseRebateFlag) && column.dataIndex === 'BoxRebateAmount'
                  ? 0
                  : column.max || *********
              "
              :precision="column.precision >= 0 ? column.precision : 2"
              @blur="
                ;['PurchaseQuantity', 'TaxIncludedPurchasePrice'].includes(column.dataIndex)
                  ? jsTaxIncludedPurchasePriceTotal(record, index)
                  : null
              "
              @change="(value) => handleInputChange(value, record, column.dataIndex)"
            />
          </span>
          <span slot="select" slot-scope="text, record, index, column">
            <template v-if="['StorehouseId', 'PurchaseRebateFlag'].includes(column.dataIndex)">
              <template v-if="column.dataIndex == 'StorehouseId'">
                <single-choice-view
                  style="width: 100%"
                  placeholder="请选择"
                  v-model="record[column.dataIndex]"
                  :disabled="!record.GoodsSpuId || column.dataIndex == 'StorehouseId'"
                  :Url="'/{v}/Global/GetListDictItem'"
                  :httpParams="{
                    groupPY: 'cangk',
                  }"
                  :httpHead="'P36001'"
                  :dataKey="{ name: 'ItemValue', value: 'Id' }"
                  @change="
                    (val, txt, item) => {
                      record[column.keyStr] = txt
                    }
                  "
                />
              </template>
              <template v-if="column.dataIndex == 'PurchaseRebateFlag'">
                <EnumSingleChoiceView
                  style="width: 100%"
                  placeholder="请选择"
                  dictCode="EnumPurchaseRebateFlag"
                  v-model="record[column.dataIndex]"
                  @change="(e) => changePurchaseRebateFlag(e, record)"
                />
              </template>
            </template>
            <a-select
              v-else
              :key="index"
              style="width: 100%"
              v-model="record[column.dataIndex]"
              placeholder="请选择"
              :disabled="!record.GoodsSpuId"
              @change="(e) => onSFSelectChange(e, column.dataIndex, index)"
            >
              <a-select-option value="true">是</a-select-option>
              <a-select-option value="false">否</a-select-option>
            </a-select>
          </span>
          <span slot="date" slot-scope="text, record, index, column">
            <a-date-picker :disabled="!record.GoodsSpuId" valueFormat="YYYY-MM-DD" v-model="record[column.dataIndex]"/>
          </span>
          <span slot="action" slot-scope="text, record, index">
            <a @click="onDeleteClick(record, index)" v-if="record.GoodsSpuId">移除</a>
          </span>
        </a-table>
        <!-- 底部区域 -->
        <a-row class="mt10">
          <a-col :span="8" style="text-align: left">
            <span style="font-size: 20px; font-weight: bold">
              含税金额合计：<span style="color: red; margin-right: 50px">¥{{ amountMoney }}</span> 共<span
                style="color: red"
                >{{ dataSource && dataSource.length > 1 ? dataSource.length - 1 : 0 }}</span
              >种商品
            </span>
          </a-col>
          <a-col :span="8"></a-col>
          <a-col :span="8" style="text-align: right">
            <YYLButton
              menuId="40df0341-121d-408a-b2e4-413c3034ccc8"
              text="保存"
              type="primary"
              :loading="confirmLoading"
              @click="onSaveClick"
            /><!-- v-if="!orderId"  -->
            <YYLButton
              menuId="6a71d16c-e213-43e8-b9fc-351584dd152f"
              text="保存并提交审核"
              type="primary"
              :loading="confirmLoading"
              :disabled="(model.DelegateErr || []).length > 0"
              @click="onSaveAndAuditClick"
            />
            <a-button @click="cancel">取消</a-button>
          </a-col>
        </a-row>
      </a-card>
    </a-card>
    <!-- 选择商品 -->
    <SelectGoodsBySearchListModal ref="selectGoodsBySearchListModal" @ok="addGoods" />
    <!-- 历史采购价 -->
    <HistoricalPurchasePriceModal ref="HistoricalPurchasePriceModal" />
    <!-- 平均参考成本 -->
    <AverageReferenceCostModal ref="AverageReferenceCostModal" />
    <!-- 政策底价 -->
    <PolicyBasePriceModal ref="PolicyBasePriceModal" />
    <!-- 库存明细 -->
    <InventoryDetailModal ref="InventoryDetailModal" />
    <!-- 本月销售明细 -->
    <SalesThisMonthModal ref="SalesThisMonthModal" />
    <!-- 在途库存 -->
    <PlanTotalInTransitCountModal ref="PlanTotalInTransitCountModal" />
    <!--  导入商品 -->
    <BatchImportCustomer
      ref="iBatchImportGoodsModal"
      modalTitle="导入商品"
      :tableColumns="tableColumns"
      :searchParamsList="searchImportInput"
      :importConfig="goodsImportConfig"
      :bordered="true"
      :importUrl="`/{v}/GoodsPurchase/ImportPurchaseGoodsTemp?SupplierId=${model.SupplierId}&DelegateScopeId=${model.DelegateScopeId}&PurchaseOrderType=${orderType}&DelegateCode=${model.DelegateCode}`"
      importHttpHead="P36006"
      @tabChange="handleTabChange"
      @ok="handleBatchImportModalOk"
    />
  </a-spin>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
import YYLButton from '@/components/yyl/business/YYLButton.vue'
import { getAction, postAction, apiHead } from '@/api/manage'
import moment from 'moment'
import { uniqBy } from 'lodash'
import { forEach } from 'mathjs'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  components: { YYLButton, JEllipsis },
  name: 'AddPurchaseOrder',
  mixins: [EditMixin, ListMixin],
  props: {
    quickOrderSupplierList: {
      // 快捷下单供应商清单
      type: Array,
      default: () => [],
    },
    suppliersIdAndType: {
      // 当前快捷下单当前供应商Id + '+'orderType
      type: String,
      default: '',
    },
    ComparisonBatchNumber: {
      // 比价批次号
      type: String,
      default: '',
    },
    QiuckSuppliersName: {
      // 快捷下单当前供应商名称
      type: String,
      default: '',
    },
  },
  data() {
    return {
      quickOrderSuppliersId: null, // 当前快捷下单供应商Id
      model: {},
      confirmLoading: false,
      pLoading: false,
      loading: false,
      httpHead: 'P36009',
      opType: 1, //操作类型 1 采购订单  2 采购计划
      orderType: 1, //订单类型 1.普通订单 2.含特订单
      orderId: '',
      span: 8,
      emptyItem: {
        GoodsSpuId: '',
        PurchaseQuantity: null,
        TaxIncludedPurchasePrice: '',
        StorehouseId: '',
        StorehouseStr: '',
        IsNearExpiry: 'false',
        IsPolicyReturn: 'false',
        IsForSale: '',
        IsPackageHandled: '',
        OriginalPrice: '',
        MinimumSellingPrice: '',
        Remarks: '',
      },
      goodsImportConfig: {
        templateFileName: '采购订单导入商品模板', //下载模板名字
      },
      searchImportInput: [
        {
          name: '商品', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'Keyword',
          width: '160',
          placeholder: '商品编号、商品名称、拼音码',
        },
      ],
      tableColumns: [
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购数量',
          dataIndex: 'PurchaseQuantity',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          width: 200,
          ellipsis: true,
          precision: 6,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '近效期',
          dataIndex: 'IsNearExpiry',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'bool' },
        },
        {
          title: '返利标记',
          dataIndex: 'PurchaseRebateFlagStr',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '销售原价',
          dataIndex: 'OriginalPrice',
          width: 200,
          ellipsis: true,
          precision: 6,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '商销底价',
          dataIndex: 'MinimumSellingPrice',
          width: 200,
          ellipsis: true,
          precision: 6,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '每盒返利金额',
          dataIndex: 'BoxRebateAmount',
          width: 200,
          ellipsis: true,
          precision: 6,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '返利到账天数',
          dataIndex: 'RebateDays',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Remarks',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtns: [{ name: '移除', icon: '', id: '390fe352-576b-4942-a548-d7e4fb885a5e' }],
          scopedSlots: { customRender: 'action' },
        },
      ],
      searchLoading: false, //商品搜索loading
      searchKey: '', //商品搜索关键字
      amountMoney: 0,
      isEdit: false,
      hgpkId: '', //合格品库id
      url: {
        searchGoodsUrl: '/{v}/GoodsPurchase/GetPurchaseGoodsList',
        checkGoods: '/{v}/PurchaseAgreement/GetPurchaseAgreementSupplierPrompt',

        detail: '',
        save: '',
        edit: '',
        saveAudit: '',

        detail1: '/{v}/PurchaseOrder/GetInfo',
        save1: '/{v}/PurchaseOrder/CreatePurchaseOrder',
        edit1: '/{v}/PurchaseOrder/EditPurchaseOrder',
        saveAudit1: '/{v}/PurchaseOrder/SubmitPurchaseOrder',

        detail2: '/{v}/PurchaseScheme/GetPurchaseSchemeInfo',
        save2: '/{v}/PurchaseScheme/Create',
        edit2: '/{v}/PurchaseScheme/Update',
        saveAudit2: '/{v}/PurchaseScheme/Submit',

        importUrl: '', //导入
        comparisonGoodsInfo: '/{v}/PurchaseComparison/CreatePurchaseOrderByComparison', //快捷下单对应供应商的商品信息
      },
      SupplierList: [], // 所有供应商列表
    }
  },
  computed: {
    // 是否快捷下单进入该组件
    ifQiuckOrder() {
      return this.suppliersIdAndType
    },
    columns() {
      const allColumnsMap = {
        ErpGoodsName: {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'name' },
          width: 150,
          fixed: 'left',
          ellipsis: true,
        },
        ErpGoodsCode: {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 100,
          ellipsis: true,
        },
        PackingSpecification: {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        BrandManufacturer: {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        GoodsOwnerBy: {
          title: '责任人',
          dataIndex: 'GoodsOwnerBy',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        Producer: {
          title: '产地',
          dataIndex: 'Producer',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        PackageCount: {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        PackageUnit: {
          title: '单位',
          dataIndex: 'PackageUnit',
          scopedSlots: { customRender: 'component' },
          width: 60,
          ellipsis: true,
        },
        PurchaseQuantity: {
          title: '采购数量',
          dataIndex: 'PurchaseQuantity',
          scopedSlots: { customRender: 'inputNumber' },
          width: 90,
          ellipsis: true,
        },
        TaxIncludedPurchasePrice: {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          scopedSlots: { customRender: 'inputNumber' },
          width: 100,
          precision: 6,
          ellipsis: true,
        },
        TaxIncludedPurchasePriceTotal: {
          title: '含税金额',
          dataIndex: 'TaxIncludedPurchasePriceTotal',
          scopedSlots: { customRender: 'price' },
          width: 100,
          precision: 2,
          ellipsis: true,
        },
        StorehouseId: {
          title: '仓库',
          dataIndex: 'StorehouseId',
          width: 150,
          scopedSlots: { customRender: 'select' },
          ellipsis: true,
          keyStr: 'StorehouseStr',
        },
        IsNearExpiry: {
          title: '近效期',
          dataIndex: 'IsNearExpiry',
          scopedSlots: { customRender: 'select' },
          width: 80,
          ellipsis: true,
        },
        PurchaseRebateFlag: {
          title: '返利标记',
          dataIndex: 'PurchaseRebateFlag',
          scopedSlots: { customRender: 'select' },
          width: 150,
          ellipsis: true,
        },
        OriginalPrice: {
          title: '销售原价',
          dataIndex: 'OriginalPrice',
          scopedSlots: { customRender: 'inputNumber' },
          width: 100,
          precision: 6,
          ellipsis: true,
        },
        MinimumSellingPrice: {
          title: '商销底价',
          dataIndex: 'MinimumSellingPrice',
          width: 100,
          precision: 6,
          scopedSlots: { customRender: 'inputNumber' },
          ellipsis: true,
        },
        BoxRebateAmount: {
          title: '每盒返利金额',
          dataIndex: 'BoxRebateAmount',
          width: 100,
          scopedSlots: { customRender: 'inputNumber' },
          precision: 6,
          ellipsis: true,
        },
        RebateDays: {
          title: '返利到账天数',
          dataIndex: 'RebateDays',
          width: 100,
          scopedSlots: { customRender: 'inputNumber' },
          ellipsis: true,
        },
        MarketPurchasePrice: {
          title: '市场采购价',
          dataIndex: 'MarketPurchasePrice',
          width: 100,
          maxLength: 100,
          scopedSlots: { customRender: 'input' },
        },
        GrossMarginRate: {
          title: '毛利率',
          dataIndex: 'GrossMarginRate',
          width: 80,
          maxLength: 100,
          scopedSlots: { customRender: 'input' },
        },
        PurchaseBatchNo: {
          title: '购进批号',
          dataIndex: 'PurchaseBatchNo',
          width: 120,
          maxLength: 100,
          scopedSlots: { customRender: 'input' },
        },
        EstimatedTimeOfSale: {
          title: '预计销售时间',
          dataIndex: 'EstimatedTimeOfSale',
          width: 140,
          maxLength: 100,
          scopedSlots: { customRender: 'date' },
        },
        IsForSale: {
          title: '是否商销',
          dataIndex: 'IsForSale',
          width: 100,
          scopedSlots: { customRender: 'select' },
        },
        IsPackageHandled: {
          title: '是否包处理',
          dataIndex: 'IsPackageHandled',
          width: 100,
          scopedSlots: { customRender: 'select' },
        },
        Remarks: {
          title: '备注',
          dataIndex: 'Remarks',
          scopedSlots: { customRender: 'input' },
          width: 120,
          maxLength: 50,
          maxLength: 100,
          ellipsis: true,
        },
        PurchaseTaxRate: {
          title: '税率',
          dataIndex: 'PurchaseTaxRate',
          width: 60,
          scopedSlots: { customRender: 'percent' },
          ellipsis: true,
        },
        AvailableInventory: {
          title: '可销总库存',
          dataIndex: 'AvailableInventory',
          scopedSlots: { customRender: 'clickA' },
          width: 100,
          ellipsis: true,
        },
        EstimatedSaleTime: {
          title: '预计可销时间',
          dataIndex: 'EstimatedSaleTime',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        InTransitInventory: {
          title: '在途库存',
          dataIndex: 'InTransitInventory',
          scopedSlots: { customRender: 'clickA' },
          width: 100,
          ellipsis: true,
        },
        LastMonthCount: {
          title: '上月销量',
          dataIndex: 'LastMonthCount',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        ThisMonthCount: {
          title: '本月销量',
          dataIndex: 'ThisMonthCount',
          width: 100,
          scopedSlots: { customRender: 'clickAl' },
          ellipsis: true,
        },
        LastPurchasePrice: {
          title: '末次采购价',
          dataIndex: 'LastPurchasePrice',
          width: 100,
          scopedSlots: { customRender: 'clickPriceA' },
          ellipsis: true,
        },
        NationalSupplyPrice: {
          title: '平台原价',
          dataIndex: 'NationalSupplyPrice',
          width: 100,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        ActivityTypeName: {
          title: '活动类型',
          dataIndex: 'ActivityTypeName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        ActivityPrice: {
          title: '活动价格',
          dataIndex: 'ActivityPrice',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        MoveAverageCostPrice: {
          title: '含购进的移动平均成本',
          dataIndex: 'MoveAverageCostPrice',
          width: 160,
          scopedSlots: { customRender: 'clickPriceA' },
          ellipsis: true,
        },
        action: {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          scopedSlots: { customRender: 'action' },
        },
      }

      const planColumns = [
        'ErpGoodsName',
        'ErpGoodsCode',
        'PackingSpecification',
        'BrandManufacturer',
        'Producer',
        'PackageCount',
        'PackageUnit',
        'PurchaseQuantity',
        'TaxIncludedPurchasePrice',
        'TaxIncludedPurchasePriceTotal',
        'StorehouseId',
        'IsNearExpiry',
        'PurchaseRebateFlag',
        'OriginalPrice',
        'MinimumSellingPrice',
        'BoxRebateAmount',
        'RebateDays',
        'MarketPurchasePrice',
        'GrossMarginRate',
        'PurchaseBatchNo',
        'EstimatedTimeOfSale',
        'IsForSale',
        'IsPackageHandled',
        'Remarks',
        'PurchaseTaxRate',
        'AvailableInventory',
        'EstimatedSaleTime',
        'InTransitInventory',
        'LastMonthCount',
        'ThisMonthCount',
        'LastPurchasePrice',
        'NationalSupplyPrice',
        'ActivityTypeName',
        'ActivityPrice',
        'MoveAverageCostPrice',
        'action',
      ]
      const orderColumns = [
        'ErpGoodsName',
        'ErpGoodsCode',
        'PackingSpecification',
        'BrandManufacturer',
        'GoodsOwnerBy',
        'Producer',
        'PackageCount',
        'PackageUnit',
        'PurchaseQuantity',
        'TaxIncludedPurchasePrice',
        'TaxIncludedPurchasePriceTotal',
        'StorehouseId',
        'IsNearExpiry',
        'PurchaseRebateFlag',
        'OriginalPrice',
        'MinimumSellingPrice',
        'BoxRebateAmount',
        'RebateDays',
        'Remarks',
        'PurchaseTaxRate',
        'AvailableInventory',
        'EstimatedSaleTime',
        'InTransitInventory',
        'LastMonthCount',
        'ThisMonthCount',
        'LastPurchasePrice',
        'NationalSupplyPrice',
        'ActivityTypeName',
        'ActivityPrice',
        'MoveAverageCostPrice',
        'action',
      ]
      return this.opType == 1
        ? orderColumns.map((key) => allColumnsMap[key])
        : planColumns.map((key) => allColumnsMap[key])
    },
    rules() {
      return {
        SupplierId: [{ required: true, message: '请选择!' }],
        DelegateScopeId: [{ required: true, message: '请选择!' }],
        PaymentMode: [{ required: true, message: '请选择!' }],
        PaymentType: [{ required: this.opType == 2, message: '请选择!' }],
        DeliveryMethodId: [{ required: true, message: '请选择!' }],
        ExpectedArrivalDate: [{ required: true, message: '请输入!' }],
        Remarks: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (value) {
                this.validStrMaxLength(rule, value, callback, 100)
              } else {
                callback()
              }
            },
          },
        ],
      }
    },
    /**
     * 未使用
     */
    importExcelUrl: function () {
      return apiHead(this.httpHead) + `${this.url.importUrl}`
    },
  },
  watch: {
    dataSource: {
      handler(newVal, oldVal) {
        this.calculateAmount()
      },
      deep: true,
    },
    suppliersIdAndType: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.$refs.SupplierSingleChoiceSearchView.getData(this.QiuckSuppliersName)
        })
      },
    },
    // QiuckSuppliersName: { // 快捷下单切换商家后自动查询
    //   handler(newVal, oldVal) {
    //     this.$refs.SupplierSingleChoiceSearchView.getData(newVal)
    //   }
    // },
  },
  mounted() {
    if (this.ifQiuckOrder) {
      this.initQiuckOrder()
    } else {
      this.init()
    }
  },
  methods: {
    moment,
    handleTabChange(type) {
      const len = this.tableColumns.length
      if (type === 'Success') {
        this.tableColumns = this.tableColumns.filter(item => item.title !== '异常原因')
      } else if (type === 'Fail') {
        if(!this.tableColumns.filter(item => item.title === '异常原因').length) {
          this.tableColumns.splice(len - 1, 0,
            {
              title: '异常原因',
              dataIndex: 'ErrorShow',
              align: 'center',
              width: 200,
              ellipsis: true,
              scopedSlots: { customRender: 'component' },
            }
          )
        }
      }

    },
    // 取消操作
    cancel() {
      // 快捷下单直接关闭
      if (this.ifQiuckOrder) {
        this.$emit('goNext', 'close')
      } else {
        this.goBack(true, true)
      }
    },
    async handleBatchImportModalOk(goodsList) {
      const existGoods = this.dataSource
      const unExistGoods = goodsList.filter((x) => {
        const { Id, TaxIncludedPurchasePrice } = x
        // 过滤出已存在的商品
        const help = existGoods.filter((it) => it.GoodsSpuId && it.GoodsSpuId === Id) || []
        // 映射已存在商品的价格
        const prices = help.map((x) => x.TaxIncludedPurchasePrice)
        return !prices.length || !prices.includes(TaxIncludedPurchasePrice)
      })
      const GoodsSpuIds = unExistGoods.map((x) => x.Id)
      if (GoodsSpuIds.length === 0) return
      const res = await postAction('/{v}/GoodsPurchase/GetImportPurchaseGoodsInfos', { GoodsSpuIds }, 'P36006')
      const data = res.Data
      const appendGoods = unExistGoods.map((item) => {
        if (!item.PurchaseQuantity) delete item.PurchaseQuantity
        if (!item.TaxIncludedPurchasePrice) delete item.TaxIncludedPurchasePrice
        if (!item.OriginalPrice) delete item.OriginalPrice
        if (!item.MinimumSellingPrice) delete item.MinimumSellingPrice
        if (item.PurchaseRebateFlag) {
          item.PurchaseRebateFlag = String(item.PurchaseRebateFlag)
        } else {
          item.PurchaseRebateFlag = '1'
        }
        item.StorehouseId = this.hgpkId || 'ec82de27-bb01-2f26-eff5-6060619ba959'
        delete item.ErpGoodsCode
        delete item.ErpGoodsName
        delete item.PackingSpecification
        delete item.BrandManufacturer
        if (item.PurchaseQuantity && item.TaxIncludedPurchasePrice ) {
          item.TaxIncludedPurchasePriceTotal = this.numMath(item.PurchaseQuantity ,item.TaxIncludedPurchasePrice, '*', 2)
        }
        item.IsNearExpiry = typeof item.IsNearExpiry === 'boolean' ? '' + item.IsNearExpiry : 'false'
        const target = data.find((x) => x.GoodsSpuId === item.Id)
        return { ...target, ...item, ErpGoodsIdAndIndex: `${item.ErpGoodsId}${item.TaxIncludedPurchasePrice}` }
      })
      console.log('导入商品', appendGoods) 
      this.dataSource = [...appendGoods, ...existGoods]
    },
    importGoods() {
      if (!this.model.SupplierId || !this.model.DelegateScopeId) {
        this.$message.warning('请先选择供应商和委托人！')
        return
      }
      this.$refs.iBatchImportGoodsModal.show()
    },
    // 供应商数据加载完成的回调
    SupplierLoad(data) {
      if (this.ifQiuckOrder) {
        this.SupplierList = data
        this.$nextTick(() => {
          const list = this.suppliersIdAndType ? this.suppliersIdAndType.split('&') : []
          this.quickOrderSuppliersId = list.length > 0 ? list[0] : null
          this.SetNewSuppliers(this.quickOrderSuppliersId, this.suppliersIdAndType)
        })
      }
    },
    // 设置新的供应商
    SetNewSuppliers(val, suppliersIdAndType) {
      // 只保留底部的编辑商品
      this.dataSource = this.dataSource.slice(-1)
      const quickOrderSupplierList = this.quickOrderSupplierList
      const SupplierList = JSON.parse(JSON.stringify(this.SupplierList))
      console.log(SupplierList)
      const item = SupplierList.find((f) => f.SupplierId === val)
      const txt = item.SupplierName
      const info = quickOrderSupplierList.find((f) => f.createId === suppliersIdAndType)
      const IsSpecialOrder = Boolean(info.IsSpecialOrder)
      this.orderType = IsSpecialOrder ? 2 : 1
      if (val) {
        this.$refs.singleChoiceViewTWR.getData('SupplierId', val)
        this.model.SupplierId = val
        this.model.SupplierName = txt
      }
      this.model.DelegateScopeId = ''
      if (item && item.PurchaseSupplierErrors && item.PurchaseSupplierErrors.length > 0) {
        this.model.SupplierErr = item.PurchaseSupplierErrors[0].ErrorMessage
      } else {
        this.model.SupplierErr = ''
      }
      this.loadGoodsData(IsSpecialOrder)
    },
    // 根据供应商生成采购订单商品信息
    loadGoodsData(IsSpecialOrder) {
      const that = this
      let params = {
        ComparisonBatchNumber: this.ComparisonBatchNumber,
        SupplierId: this.model.SupplierId,
        IsSpecialOrder: IsSpecialOrder,
      }
      let requestObj = postAction(this.url.comparisonGoodsInfo, params, this.httpHead)
      this.loading = true
      requestObj
        .then((res) => {
          this.loading = false
          if (res.IsSuccess) {
            const PurchaseOrderDetailInfos = res.Data.PurchaseOrderDetailInfos
            PurchaseOrderDetailInfos.map((item) => {
              this.addGoodsTrue(item)
            })
            that.dataSource.forEach((x, index) => {
              x.IsNearExpiry = '' + x.IsNearExpiry
              if (this.opType == 2) {
                x.IsForSale = '' + x.IsForSale
                x.IsPackageHandled = '' + x.IsPackageHandled
              }

              x.IsPolicyReturn = '' + x.IsPolicyReturn
              x.PurchaseRebateFlag = '' + (x.PurchaseRebateFlag || '')
              x.ErpGoodsIdAndIndex = x.ErpGoodsId + index
            })
            this.onClearSelected() // 每次切换清空表格勾选数据
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 快捷下单初始化
    initQiuckOrder() {
      this.opType = 1
      this.isEdit = false
      this.initUrl()
      this.getHGPKId()
      this.initModel()
    },
    init() {
      if (this.$route.query) {
        this.orderId = this.$route.query.id || ''
        this.opType = this.$route.query.opType ? Number(this.$route.query.opType) : 1
        this.isEdit = this.$route.query.isAdd && Number(this.$route.query.isAdd) == 1 ? true : false
        this.initUrl()
        this.getHGPKId()
        if (!this.orderId) {
          this.orderType = this.$route.query.orderType ? Number(this.$route.query.orderType) : 1
          this.initModel()
          this.setPageTitle(false)
        } else {
          this.loadDetail()
        }
      }
    },
    /**
     * 获取合格品库id
     */
    getHGPKId() {
      let that = this
      getAction('/{v}/Global/GetListDictItem', { groupPY: 'cangk', PageIndex: 1, PageSize: 100 }, 'P36001').then(
        (res) => {
          if (res.IsSuccess && res.Data) {
            res.Data.forEach((x) => {
              if (x.ItemPym == 'CKZ00000001') {
                that.hgpkId = x.Id
              }
            })
          }
        }
      )
    },
    disabledDate(current) {
      return current && current < moment().startOf('day')
    },
    initUrl() {
      if (this.opType) {
        this.url.detail = this.url['detail' + this.opType]
        this.url.save = this.url['save' + this.opType]
        this.url.edit = this.url['edit' + this.opType]
        this.url.saveAudit = this.url['saveAudit' + this.opType]
      }
    },
    initModel() {
      this.model = {
        SupplierId: undefined,
        DelegateScopeId: undefined,
        OrderType: this.orderType,
      }
      this.dataSource = []
      this.addEmptyGoods()
    },
    loadDetail() {
      let that = this
      that.pLoading = true
      getAction(that.url.detail, { id: that.orderId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            that.$refs.singleChoiceViewTWR.resetHttpParams({
              SupplierId: res.Data.SupplierId,
              OrderType: res.Data.OrderType,
            })
            setTimeout(() => {
              that.orderType = that.model.OrderType
              that.model.PaymentMode = String(that.model.PaymentMode)
              typeof that.model.PaymentType === 'number' && (that.model.PaymentType = String(that.model.PaymentType))
              if (that.model.PurchaseOrderDetailInfos) {
                that.dataSource = JSON.parse(JSON.stringify(that.model.PurchaseOrderDetailInfos))
                that.dataSource.forEach((x, index) => {
                  x.IsNearExpiry = '' + x.IsNearExpiry
                  if (that.opType == 2) {
                    x.IsForSale = '' + x.IsForSale
                    x.IsPackageHandled = '' + x.IsPackageHandled
                  }

                  x.IsPolicyReturn = '' + x.IsPolicyReturn
                  x.PurchaseRebateFlag = '' + (x.PurchaseRebateFlag || '')
                  x.ErpGoodsIdAndIndex = x.ErpGoodsId + index
                })
                that.addEmptyGoods()
              }
              // console.log('info', this.dataSource)
            }, 500)

            this.setPageTitle(true)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.pLoading = false
        })
    },
    setPageTitle(isEdit) {
      if (this.ifQiuckOrder) {
        return false
      }
      let beforeStr = isEdit ? '编辑' : '新增'
      let isSpecStr = this.orderType == 2 ? '含特' : ''
      let endStr = this.opType == 1 ? '订单' : '计划'
      this.setTitle(beforeStr + isSpecStr + endStr)
    },
    /**
     * 搜索商品
     */
    onSearch() {
      let that = this
      if (that.searchKey && that.model.SupplierId && that.model.DelegateScopeId) {
        that.searchLoading = true
        let param = {
          SupplierId: that.model.SupplierId,
          DelegateId: that.model.DelegateScopeId,
          OrderType: that.orderType,
          KeyWord: that.searchKey,
          DelegateCode: that.model.DelegateCode,
        }
        getAction(that.url.searchGoodsUrl, param, 'P36006')
          .then((res) => {
            if (res.IsSuccess && res.Data) {
              if (res.Data.length > 0) {
                if (res.Data.length == 1 && res.Data[0].IsCanSelect) {
                  that.addGoods(res.Data[0])
                } else {
                  that.showGoodsListDialog(param)
                }
              }
            } else {
              if (res.Msg) {
                that.$message.error(res.Msg)
              }
            }
          })
          .finally(() => {
            that.searchLoading = false
          })
      }
    },
    showGoodsListDialog(param) {
      this.searchKey = ''
      this.$refs.selectGoodsBySearchListModal.show(param)
    },
    /**
     * 输入框回车事件
     * @param {*} record
     * @param {*} index
     */
    onEnterClick(record, index) {
      this.addEmptyGoods()
    },
    addEmptyGoods(focus) {
      if (this.dataSource.length > 0 && !this.dataSource[this.dataSource.length - 1].GoodsSpuId) {
        //已存在空占位商品，不在加入
        return
      }
      this.dataSource.push(JSON.parse(JSON.stringify(this.emptyItem)))
      this.searchKey = ''
      if (focus) {
        this.requestFocusToLastItem()
      }
    },
    // 批量移除
    onMulDeleteClick() {
      this.selectedRowKeys.forEach((x, i, arr) => {
        let index = this.dataSource.findIndex((item) => {
          // let key = item.ErpGoodsId + '' + i
          return item.ErpGoodsIdAndIndex == x
        })
        if (index > -1) {
          this.dataSource.splice(index, 1)
        }
        if (i == this.selectedRowKeys.length - 1) {
          this.onClearSelected()
        }
      })
    },
    /**
     * 移除
     * @param {*} record
     * @param {*} index
     */
    onDeleteClick(record, index) {
      if (!record.ItemType) {
        this.dataSource.splice(index, 1)
      }
    },
    /**
     * 验证商品信息是否录入完整
     */
    checkDataSource(callback, checkGoodsIsOK) {
      let errList = []
      this.dataSource.forEach((x) => {
        if (checkGoodsIsOK) {
          if (x.GoodsSpuId && !this.checkGoodsIsOK(x)) {
            errList.push(x.ErpGoodsName)
          }
        }
      })
      if (errList.length > 0) {
        this.$message.warning('商品[' + errList + ']未填写完整，请填完整后提交！')
        callback && callback()
      } else {
        let list = this.getDataSourceList(checkGoodsIsOK)
        // 商销底价和销售原价有不一致的商品名称
        let rejectList = []
        if (list && list.length > 0) {
          if (checkGoodsIsOK) {
            // 按商品名称去重
            let uniqList = uniqBy(list, 'ErpGoodsCode')
            list.forEach((item) => {
              uniqList.forEach((iv) => {
                if (
                  item.ErpGoodsCode == iv.ErpGoodsCode &&
                  (item.MinimumSellingPrice !== iv.MinimumSellingPrice || item.OriginalPrice !== iv.OriginalPrice)
                ) {
                  rejectList.push(item.ErpGoodsName)
                }
              })
            })
          }
          let remarkErrs = []
          this.dataSource.forEach((y) => {
            if (this.getStrMaxLength(y.Remarks) > 100) {
              remarkErrs.push(y.ErpGoodsName)
            }
          })
          if (remarkErrs.length > 0) {
            this.$message.warning('商品[' + remarkErrs + ']的备注不能超过00个字符！')
            callback && callback()
          } else if (rejectList.length > 0) {
            this.$message.warning(`商品名称${rejectList.join()}，商销底价不一致或者销售原价不一致，请重新输入后再提交`)
          } else {
            callback && callback(list)
          }
        } else {
          this.$message.warning('请添加商品')
          callback && callback()
        }
      }
    },
    /**
     * 检查商品输入内容是否完整
     * @param {*} goods
     *
     * PurchaseQuantity: '',
        TaxIncludedPurchasePrice: '',
        StorehouseId: '',
        StorehouseStr: '',
        IsNearExpiry: false,
        IsPolicyReturn: false,
        OriginalPrice: '',
        MinimumSellingPrice: '',
        Remarks: ''
     */
    checkGoodsIsOK(goods) {
      let bool = false
      if (goods && goods.GoodsSpuId) {
        // 正常商品： 每盒返利金额和返利到账天数，要求都录或者都不录，都录后表示有短期采购协议；
        // 返货： 必须录入每盒返利金额，要求填写负数，返利到账天数禁止录入；
        // 赠品： 必须录入每盒返利金额，要求填写负数，返利到账天数禁止录入；
        let twoOk =
          goods.PurchaseRebateFlag == 1 &&
          ((goods.BoxRebateAmount && goods.RebateDays) || (!goods.BoxRebateAmount && !goods.RebateDays))
        let twoOkA = ['2', '3'].includes(goods.PurchaseRebateFlag) && goods.BoxRebateAmount
        const commonRequiredKeys = ['PurchaseQuantity', 'TaxIncludedPurchasePrice', 'StorehouseId']
        const planRequiredKeys = [
          'MarketPurchasePrice',
          'GrossMarginRate',
          // 'PurchaseBatchNo',
          'EstimatedTimeOfSale',
          'IsForSale',
          'IsPackageHandled',
        ]
        const requiredKeys = this.opType == 2 ? [...planRequiredKeys, ...commonRequiredKeys] : commonRequiredKeys
        const requiredFalse = requiredKeys.some((key) => !goods[key])
        bool = !requiredFalse && (twoOk || twoOkA)
      }
      return bool
    },
    /**
     * 获取商品数据，过滤掉最后一个空站位数据和未填写完整的数据
     */
    getDataSourceList(checkGoodsIsOK) {
      let list = []
      this.dataSource.forEach((goods) => {
        let bool = checkGoodsIsOK ? goods.GoodsSpuId && this.checkGoodsIsOK(goods) : goods.GoodsSpuId
        if (bool) {
          let x = JSON.parse(JSON.stringify(goods))
          x.IsNearExpiry = x.IsNearExpiry == 'true'
          if (this.opType == 2) {
            x.IsForSale = x.IsForSale == 'true'
            x.IsPackageHandled = x.IsPackageHandled == 'true'
          }

          x.IsPolicyReturn = x.IsPolicyReturn == 'true'
          list.push(x)
        }
      })
      return list
    },
    /***
     * 保存按钮
     */
    onSaveClick() {
      let that = this

      let list = ['SupplierId', 'DelegateScopeId']
      let num = 0
      this.$refs.form.validateField(list, (errMsg) => {
        num++
        if (num == list.length) {
          if (!errMsg) {
            let subModel = JSON.parse(JSON.stringify(that.model))
            subModel.PaymentMode = Number(subModel.PaymentMode)
            subModel.PaymentType && (subModel.PaymentType = Number(subModel.PaymentType))

            if (!subModel.DeliveryMethodId) {
              subModel.DeliveryMethodId = null
            }

            that.checkDataSource((list) => {
              if (list) {
                subModel.PurchaseOrderDetails = list
                that.changeListItemDataNullValue(subModel.PurchaseOrderDetails)
                // 快捷下单控制提交参数
                if (this.ifQiuckOrder) {
                  subModel.ComparisonBatchNumber = this.ComparisonBatchNumber
                }
                //保存数据
                that.saveData(subModel)
              }
            }, false)
          }
        }
      })
    },
    /**
     * 改变列表数据部分属性值 空改为null
     * */
    changeListItemDataNullValue(list) {
      if (!list) {
        return
      }
      list.forEach((x) => {
        if (!x.BoxRebateAmount) {
          x.BoxRebateAmount = null
        }
        if (!x.RebateDays) {
          x.RebateDays = null
        }
        if (!x.OriginalPrice) {
          x.OriginalPrice = null
        }
        if (!x.MinimumSellingPrice) {
          x.MinimumSellingPrice = null
        }
        if (!x.PurchaseRebateFlag) {
          x.PurchaseRebateFlag = null
        }
        if (x.PurchaseRebateFlag) {
          x.PurchaseRebateFlag = Number(x.PurchaseRebateFlag)
        }
      })
    },
    /**
     * 保存并提交审核
     */
    onSaveAndAuditClick() {
      let that = this
      this.$refs.form.validate((err, values) => {
        if (err) {
          let newModel = Object.assign(that.model, values)
          let subModel = JSON.parse(JSON.stringify(newModel))
          subModel.PaymentMode = Number(subModel.PaymentMode)
          subModel.PaymentType && (subModel.PaymentType = Number(subModel.PaymentType))
          that.checkDataSource((list) => {
            if (list) {
              that.$delete(subModel, 'PurchaseOrderDetailInfos')
              subModel.PurchaseOrderDetails = list
              // 快捷下单控制提交参数
              if (this.ifQiuckOrder) {
                subModel.ComparisonBatchNumber = this.ComparisonBatchNumber
                // 校验保存并提交审核时是否有无法下单的商品未移除
                let errorInfoList = this.dataSource.filter((f) => f.ErrorInfo) || []
                if (errorInfoList.length > 0) {
                  this.$message.warning('请先移除无法采购商品，再提交')
                  return false
                }
              }
              //保存数据
              that.saveDataAndSubmitAudit(subModel)
            }
          }, true)
        }
      })
    },
    /**
     * 保存数据
     */
    saveData(subModel) {
      let amountMoney = '' + this.amountMoney
      if (amountMoney.length > 18) {
        this.$message.warning('金额过大、无法提交')
        return
      }
      // console.log('saveData', subModel)
      let that = this
      that.confirmLoading = true
      let url = that.model.Id ? that.url.edit : that.url.save
      postAction(url, subModel, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.$emit('ok')
            // 快捷下单需要切换下一个供应商
            if (this.ifQiuckOrder) {
              this.$emit('goNext')
            } else {
              that.goBack(true, true)
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    /**
     * 保存数据并提交审核
     */
    saveDataAndSubmitAudit(subModel) {
      let amountMoney = '' + this.amountMoney
      if (amountMoney.length > 18) {
        this.$message.warning('金额过大、无法提交')
        return
      }
      this.changeListItemDataNullValue(subModel.PurchaseOrderDetails)

      let that = this
      that.confirmLoading = true
      postAction(that.url.saveAudit, subModel, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            // opType: 1, //操作类型 1 采购订单  2 采购计划
            // orderType: 1, //订单类型 1.普通订单 2.含特订单
            // 采购订单-提交订单的时候，创建审核。现在不用调用了，直接提交。
            if (this.opType == 1) {
              that.$message.success('操作成功！')
              that.$emit('ok')
              // 快捷下单需要切换下一个供应商
              if (this.ifQiuckOrder) {
                this.$emit('goNext')
              } else {
                that.goBack(true, true)
              }
              return
            }
            // 采购计划
            that.createApproval(
              {
                BussinessNo: res.Data,
                Scenes: that.opType == 1 ? (that.orderType == 1 ? 4 : 28) : that.orderType == 1 ? 25 : 29,
                OpratorId: that.getLoginUserId(),
              },
              (bool) => {
                if (bool) {
                  that.$emit('ok')
                  that.goBack(true, true)
                }
              }
            )
          } else {
            that.$message.error(res.Msg)
            that.confirmLoading = false
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    /**
     * 创建审核实例
     * @param {*} callback
     */
    createApproval(params, callback) {
      let that = this
      if (!that.confirmLoading) {
        that.confirmLoading = true
      }
      postAction('/{v}/ApprovalWorkFlowInstance/CreateApproval', params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            callback && callback(true)
          } else {
            that.$message.error(res.Msg)
            callback && callback(false)
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    /**
     * 添加商品
     * @param {*} goods
     */
    addGoods(goods) {
      
      if (!goods) {
        return
      }

      // let item = this.dataSource.find((x) => {
      //   return x.GoodsSpuId == goods.GoodsSpuId
      // })
      // if (item) {
      //   this.$message.warning('此商品已添加，无须重复添加！')
      //   return
      // }
      let that = this
      that.loading = true
      getAction(
        that.url.checkGoods,
        {
          SupplierId: that.model.SupplierId,
          GoodsSpuId: goods.GoodsSpuId,
        },
        'P36007'
      )
        .then((res) => {
          if (res.IsSuccess && res.Data) {
            if (res.Data.IsPrompt) {
              that.$confirm({
                title: '提示',
                content: res.Data.TipsMessage,
                onOk: function () {
                  that.addGoodsTrue(goods)
                },
              })
            } else {
              //直接加商品
              that.addGoodsTrue(goods)
            }
          } else if (res.Msg) {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    addGoodsTrue(goods) {
      console.log('addGoodsTrue', goods)
      if (this.opType == 2) {
        if (goods.ActivityPrice && goods.ActivityPrice > 0) {
          goods.GrossMarginRate = this.numMath(goods.ActivityPrice - goods.TaxIncludedPurchasePrice, goods.ActivityPrice, '/', 6).toString()
        } else if (goods.OriginalPrice && goods.OriginalPrice > 0) {
          goods.GrossMarginRate = this.numMath(goods.OriginalPrice - goods.TaxIncludedPurchasePrice, goods.OriginalPrice, '/', 6).toString()
        }
      }
      if (goods.IsNearExpiry != false && goods.IsNearExpiry != true) {
        goods.IsNearExpiry = 'false'
      }
      // if (this.opType == 2 && goods.IsForSale != false && goods.IsForSale != true) {
      //   goods.IsForSale = 'false'
      // }
      // if (this.opType == 2 && goods.IsPackageHandled != false && goods.IsPackageHandled != true) {
      //   goods.IsPackageHandled = 'false'
      // }
      if (goods.IsPolicyReturn != false && goods.IsPolicyReturn != true) {
        goods.IsPolicyReturn = 'false'
      }
      // if (this.orderType == 2) {
      goods.StorehouseId = this.hgpkId || 'ec82de27-bb01-2f26-eff5-6060619ba959'
      // }

      this.searchKey = ''
      goods.ErpGoodsIdAndIndex = goods.ErpGoodsId + '' + this.dataSource.length
      console.log(goods)
      goods.PurchaseRebateFlag = goods.PurchaseRebateFlag || '1'
      this.dataSource.splice(this.dataSource.length - 1, 0, goods)
      // console.log('dataSource', this.dataSource)
    },
    requestFocusToLastItem() {
      this.$nextTick(() => {
        let num = this.dataSource.length - 1
        if (this.$refs['inputSearch' + num]) {
          this.$refs['inputSearch' + num].focus()
        }
      })
    },
    /**
     * 计算TaxIncludedPurchasePriceTotal
     */
    jsTaxIncludedPurchasePriceTotal(record, index) {
      let count = 0
      if (record.PurchaseQuantity > 0 && record.TaxIncludedPurchasePrice > 0) {
        count = record.PurchaseQuantity * record.TaxIncludedPurchasePrice
      }
      this.dataSource[index].TaxIncludedPurchasePriceTotal = this.getAmountOfMoney(count, 2)
      this.calculateAmount()
    },
    /**
     * 计算金额
     */
    calculateAmount() {
      let num = 0
      this.dataSource.forEach((x) => {
        if (x.TaxIncludedPurchasePriceTotal >= 0) {
          num += x.TaxIncludedPurchasePriceTotal * 1
        }
      })
      this.amountMoney = this.getAmountOfMoney(num, 2)
    },
    // 返利标记
    changePurchaseRebateFlag(e, item) {
      // console.log(e)
      item.PurchaseRebateFlag = e
      // item['BoxRebateAmount'] = null
      item['RebateDays'] = null
      if (!item.PurchaseRebateFlag) {
        item['BoxRebateAmount'] = null
        item['RebateDays'] = null
        // return
      }
      if (['2', '3'].includes(item.PurchaseRebateFlag)) {
        item['RebateDays'] = null
        // return
      }
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    onSFSelectChange(val, key, index) {
      this.dataSource[index][key] = val
      this.$forceUpdate()
    },
    onLoadSuccess(list) {
      if (this.isEdit) {
        if (this.model.DelegateScopeId && list.length > 0) {
          let obj = list.find((x) => {
            return x.ItemValue == this.model.DelegateScopeId
          })
          if (obj && obj.Item) {
            this.model.DelegateCode = obj.Item.DelegateCode || ''
            this.model.DelegateErr = obj.Item.ErrorMessage || ''
            this.$refs.form.clearValidate('DelegateScopeId')
            this.$forceUpdate()
          }
        }
      } else {
        let obj = list && list.length == 1 ? list[0] : null
        if (obj && obj.Item) {
          this.model.DelegateScopeId = obj.ItemValue
          this.model.DelegateCode = obj.Item.DelegateCode || ''
          this.model.DelegateErr = obj.Item.ErrorMessage || ''
          this.$refs.form.clearValidate('DelegateScopeId')
          this.$forceUpdate()
        }
      }
    },
    clickA(text, record, index, column) {
      let key = column.dataIndex
      // 可销总库存
      if (key == 'AvailableInventory') {
        this.$refs.InventoryDetailModal.show(record)
      } else if (key == 'ThisMonthCount') {
        // 本月销量
        this.$refs.SalesThisMonthModal.show(record)
      } else if (key == 'LastPurchasePrice') {
        // 末次采购价
        this.$refs.HistoricalPurchasePriceModal.show(record)
      } else if (key == 'AverageCost' || key == 'MoveAverageCostPrice') {
        // 含购进的移动平均成本
        this.$refs.PolicyBasePriceModal.show(record)
      } else if (key == 'InTransitInventory') {
        // 在途库存
        this.$refs.PlanTotalInTransitCountModal.show(record)
      }
    },
    showColumn(text, record, index, column) {
      if (index == 0) {
        // console.log('text', text)
        // console.log('index', index)
        // console.log('record', record)
        // console.log('column', column)
      }
    },
    // onReamrkChange() {
    //   this.$forceUpdate()
    //   if (this.getStrMaxLength(this.model.Remarks) > 100) {
    //     this.$message.warning('备注最多100个字符')
    //   }
    // },
    recordRemarkChange(record, key, index) {
      console.log('recordRemarkChange', record, key, index)
      if (key == 'Remarks') {
        let value = record[key]
        if (value.length > 50) {
          if (this.getStrMaxLength(value) > 100) {
            this.$message.warning('备注最多100个字符')
          }
        }
        this.$forceUpdate()
      }
      if(key == 'GrossMarginRate') {
        let value = record[key]
        record[key] = value
        this.$forceUpdate()
      }
    },
    handleInputChange(value, record, key) {
      if (value == null) return
      if (
        key == 'BoxRebateAmount' &&
        ['2', '3'].includes(record.PurchaseRebateFlag) &&
        ((typeof value == 'number' && value >= 0) || (typeof value != 'number' && String(value).length))
      ) {
        record['BoxRebateAmount'] = null
        this.$message.warning('返利标记为返货/赠品时，每盒返利金额只能填写负数')
      }
      if (this.opType == 2) {
        if (key == 'OriginalPrice') {
          record.GrossMarginRate = this.numMath(value - record.TaxIncludedPurchasePrice, value, '/', 6).toString()
        }
        if (key == 'TaxIncludedPurchasePrice') {
          if (record.ActivityPrice && record.ActivityPrice > 0) {
            record.GrossMarginRate = this.numMath(record.ActivityPrice - record.TaxIncludedPurchasePrice, record.ActivityPrice, '/', 6).toString()
          } else if (record.OriginalPrice && record.OriginalPrice > 0) {
            record.GrossMarginRate = this.numMath(record.OriginalPrice - record.TaxIncludedPurchasePrice, record.OriginalPrice, '/', 6).toString()
          }
        }
        this.$forceUpdate()
      }
    },
  },
}
</script>

<style lang="less" scoped>
/deep/.ant-form-item-control {
  height: 34px;
}
/deep/ .ant-table-tbody .ant-table-row td {
  position: relative;
}
.redName {
  color: red;
  font-size: 12px;
  position: absolute;
  transform: scale(0.8);
  left: -4px;
  top: 38px;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
// /deep/.ant-alert-message {
//   color: red;
// }
</style>
