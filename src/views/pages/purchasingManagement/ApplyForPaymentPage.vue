<!--
 * @Author: LP
 * @Description: 申请付款
 * @Date: 2023/07/13
-->
<template>
  <a-card :bodyStyle="{ padding: '0 0 10px 0' }" :bordered="false">
    <ApplyForPayment ref="ApplyForPayment" :opType="opType" isEdit :id="id" :cLoading="confirmLoading" />
    <a-affix :offset-bottom="10" class="affix">
      <div class="pd1016">
        <!-- && !id -->
        <!-- v-if="opType == 1" -->
        <YYLButton menuId="e3c41de1-88ed-4e59-b451-01896cf75f16" text="保存" type="primary" :loading="confirmLoading" @click="onSaveClick"  />
        <YYLButton menuId="d7dc8619-cb45-4779-babd-f5ac7c85c2ec" text="保存并提交审核" type="primary" :loading="confirmLoading" @click="onSaveAndAuditClick" />
        <a-button :loading="confirmLoading" @click="goBack(true,true)">取消</a-button>
      </div>
    </a-affix>
  </a-card>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'ApplyForPaymentPage',
  mixins: [EditMixin, ListMixin],
  components: {},
  data() {
    return {
      confirmLoading: false,
      opType: 1, //类型 1 申请付款 2 上传发票
      id: '',
      httpHead: 'P36009',
      url: {
        save: '/{v}/PurchaseRemittedOrder/CreatePurchaseRemitted',
        editPurchaseRemitted: '/{v}/PurchaseRemittedOrder/EditPurchaseRemitted',
        submitAudit1: '/{v}/PurchaseRemittedOrder/SubmitPurchaseRemitted',
        submitAudit2: '/{v}/PurchaseRemittedOrder/SubmitSubsistInvoice',
        createSubsistInvoice: '/{v}/PurchaseRemittedOrder/CreateSubsistInvoice',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.opType = this.$route.query.type * 1 || 1
      this.id = this.$route.query.id || ''
      this.setTitle(this.opType == 1 ? '申请付款' : '上传发票')
    }
  },
  methods: {
    onSaveClick() {
      this.$refs.ApplyForPayment.checkSupplierId((data) => {
        if (data) {
          console.log('data', data)
          this.saveData(data)
        }
      })
    },
    saveData(data) {
      let that = this
      that.confirmLoading = true
      let url;
      if(that.opType == 2){
        url = that.url.createSubsistInvoice
      }else{
        url = that.id ? that.url.editPurchaseRemitted : that.url.save
      }
      postAction(url, data, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.goBack(true,true)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    onSaveAndAuditClick() {
      let that = this
      this.$refs.ApplyForPayment.checkForm((data) => {
        if (data) {
          //上传发票 && 预付款
          if (that.opType == 2 && data.PaymentMode == 1) {
            that.saveAndAudit(data)
          } else if (that.opType === 1 && data.PaymentMode == 1) {
            //申请付款 && 预付款
            that.saveAndAudit(data)
          } else {
            that.saveAndAudit(data)
          }
        }
      })
    },
    checkSaveAudit(data) {
      let params = {
        TemporaryId: data.TemporaryId,
      }
      this.confirmLoading = true
      getAction('/{v}/PurchaseRemittedOrder/VerifyInvoiceAutoSettlement', params, 'P36009')
        .then((res) => {
          if (res.IsSuccess) {
            let IsVerifySuccess = res.Data.IsVerifySuccess
            let content = IsVerifySuccess
              ? '发票勾稽的所有商品，系统将自动结算'
              : '发票勾稽的所有商品，系统将仅做发票勾稽'
            let that = this
            this.$confirm({
              title: '提示',
              content: content,
              okText: '确定提交审核',
              onOk() {
                that.saveAndAudit(data)
              },
              onCancel() {
                that.confirmLoading = false
              },
            })
          } else {
            that.$message.error(res.Msg)
            that.confirmLoading = false
          }
        })
        .catch((e) => {
          this.confirmLoading = false
        })
        .finally(() => {
          // that.confirmLoading = false
        })
    },
    /**
     * 申请付款 && 预付款 的情况下、校验能否自动结算先
     * @param {*} data
     */
    checkSaveAuditOpType1(data) {
      let that = this
      let params = {
        ...data,
      }
      console.log('params', params)
      this.confirmLoading = true
      postAction('/{v}/PurchaseRemittedOrder/VerifyAutoSettlementByPurchaseRemitted', params, 'P36009')
        .then((res) => {
          if (res.IsSuccess) {
            let IsVerifySuccess = res.Data.IsVerifySuccess
            let content = IsVerifySuccess ? '本次付款的所有商品，系统将自动结算' : '本次付款的所有商品，系统将仅做付款'

            this.$confirm({
              title: '提示',
              content: content,
              okText: '确定提交审核',
              onOk() {
                that.saveAndAudit(data)
              },
              onCancel() {
                that.confirmLoading = false
              },
            })
          } else {
            that.$message.error(res.Msg)
            that.confirmLoading = false
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => { })
    },
    saveAndAudit(data) {
      let that = this
      that.confirmLoading = true
      let url = that.opType == 1 ? that.url.submitAudit1 : that.url.submitAudit2
      postAction(url, data, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            //opType: 1, //类型 1 申请付款 2 上传发票
            //PaymentMode=1 预付款 PaymentMode=2 月结 PaymentMode=3 货到付款
            that.createApproval(
              {
                BussinessNo: res.Data,
                Scenes:
                  that.opType == 2
                    ? data.PaymentMode == 1
                      ? 10
                      : data.PaymentMode == 2
                        ? 30
                        : data.PaymentMode == 3
                          ? 13
                          : ''
                    : data.PaymentMode == 1
                      ? 8
                      : data.PaymentMode == 2
                        ? 9
                        : data.PaymentMode == 3
                          ? 12
                          : '',
                OpratorId: that.getLoginUserId(),
              },
              (bool) => {
                if (bool) {
                  that.goBack(true,true)
                }
              }
            )
          } else {
            that.$message.error(res.Msg)
            that.confirmLoading = false
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          // that.confirmLoading = false
        })
    },
    /**
     * 创建审核实例
     * @param {*} callback
     */
    createApproval(params, callback) {
      let that = this
      if (!that.confirmLoading) {
        that.confirmLoading = true
      }
      postAction('/{v}/ApprovalWorkFlowInstance/CreateApproval', params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            callback && callback(true)
          } else {
            that.$message.error(res.Msg)
            callback && callback(false)
            that.confirmLoading = false
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
  },
}
</script>
<style lang="less" scoped>

</style>
