<!--
 * @Author: LP
 * @Description: 采购买赔列表
 * @Date: 2023/07/06
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView ref="tableView" showCardBorder :tabDataList="tabDataList" :tab="tab" :columns="columns" :dataList="dataSource" @operateClick="onOperateClick" @actionClick="onActionClick" @onTabChange="onTabChange" />
    <!-- 付款 -->
    <PaymentModal ref="paymentModal" @ok="onPayCallBack" />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PurchaseBuyCompensationList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierKeyWords',
        },
        {
          name: '买赔单号',
          type: SEnum.INPUT,
          key: 'BuyCompensationNo',
        },
        {
          name: '订单编号',
          type: SEnum.INPUT,
          key: 'PurchaseOrderNo',
        },
        {
          name: '出库单号',
          type: SEnum.INPUT,
          key: 'OutOrderNo',
        },
        {
          name: '执行状态',
          type: SEnum.ENUM,
          key: 'ExecuteStatus',
          dictCode: 'EnumExecuteStatus',
        },
        {
          name: '同步状态',
          type: SEnum.ENUM,
          key: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '责任人',
          type: SEnum.INPUT,
          key: 'OwnerByName',
        },
      ],
      tabDataList: [
        // {
        //   name: '待买赔',
        //   value: '1',
        //   count: '',
        //   key: ''
        // },
        // {
        //   name: '已买赔',
        //   value: '2',
        //   count: '',
        //   key: ''
        // }
      ],
      tab: {
        curStatus: 1,
        tabValueKey: 'BuyCompensationStatus',
        tabStatusKey: 'BuyCompensationStatus',
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [
          {
            name: '批量付款',
            type: 'primary',
            icon: 'plus',
            key: '批量付款',
            id: '71f68b9f-604e-4404-9a84-86f776a66861',
          },
        ], //右上角按钮集合
        bordered: false, //是否显示表格的边框
        rowKey: 'Id',
        selectType: 'checkbox',
      },

      httpHead: 'P36009',
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '买赔单号',
          dataIndex: 'BuyCompensationNo',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        // {
        //   title: '出库单号',
        //   dataIndex: 'OutboundNo',
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        //   ellipsis: true,
        // },
        {
          title: '买赔金额',
          dataIndex: 'TotalBuyCompensationAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '执行状态',
          dataIndex: 'ExecuteStatusStr',
          width: 150,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: 'bcef387f-43cd-464f-8e75-c0636b627b8c',
            },
            {
              name: '付款',
              id: '3d582cf2-db4c-4b52-ab68-ed9ae2889323',
              isShow: (record) => {
                return this.tab.curStatus == 1
              },
            },
          ],
        },
      ],
      url: {
        list: '/{v}/PurchaseBusiness/QueryBuyCompensationList',
        tabCount: '/{v}/PurchaseBusiness/QueryBuyCompensationListCount',
        tabIsList: true,
        setValidUrl: '',
      },
    }
  },
  methods: {
    onTabChange() {
      // console.log(this.tab.curStatus)
      this.tab.operateBtns =
        this.tab.curStatus == 1
          ? [
            {
              name: '批量付款',
              type: 'primary',
              icon: 'plus',
              key: '批量付款',
              id: '71f68b9f-604e-4404-9a84-86f776a66861',
            },
          ]
          : []
      this.tab.selectType = this.tab.curStatus == 1 ? 'checkbox' : ''
    },
    onOperateClick(key) {
      //批量付款
      if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
        this.$refs.paymentModal.show(this.selectedRowKeys)
      } else {
        this.$message.warning('请先选择买赔单!')
      }
    },
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('PurchaseBuyCompensationDetail', { id: record.Id })
      } else if (key === '付款') {
        this.$refs.paymentModal.show([record.Id])
      }
    },
    onPayCallBack() {
      this.modalFormOk()
      this.onClearSelected()
    },
  },
}
</script>
<style scoped>

</style>
