<!--
 * @Author: LP
 * @Description: 采购付款列表
 * @Date: 2023/07/06
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView ref="tableView" showCardBorder :tabDataList="tabDataList" :tab="tab" :columns="columns" :dataList="dataSource" @operateClick="onOperateClick" @actionClick="onActionClick" />
    <ImageListModal ref="imageListModal" title="打款凭证" />
  </div>
</template>
<script>
import moment from 'moment'
import { getAction ,postAction} from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PurchasePaymentList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierName',
        },
        {
          name: '付款单号',
          type: SEnum.INPUT,
          key: 'RemittedOrderNo',
        },
        {
          name: '单据编号',
          type: SEnum.INPUT,
          key: 'DocumentNo',
        },
        {
          name: '付款方式',
          type: SEnum.ENUM,
          key: 'PaymentMode',
          dictCode: 'EnumPurchasePaymentMode',
        },
        {
          name: '审核状态',
          type: SEnum.ENUM,
          key: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
        },
        {
          name: '打款状态',
          type: SEnum.ENUM,
          key: 'RemittanceStatus',
          dictCode: 'EnumRemittanceStatus',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '责任人',
          type: SEnum.INPUT,
          key: 'OwnerByName',
        },
        {
          name: '审核结束时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'AuditTime',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate1,
          key: 'CreateTime',
        },
      ],
      tab: {
        tabTitles: ['采购付款列表'], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [
          {
            name: '导出',
            type: 'primary',
            key: '导出',
            id: '928e9324-efa7-44ad-a2c1-e9ad1eb71ba3',
          },
          {
            name: '申请付款',
            type: 'primary',
            icon: 'plus',
            key: '申请付款',
            id: '697e3315-8aee-4e70-8f94-e4f992100212',
          },
        ], //右上角按钮集合
        bordered: false, //是否显示表格的边框
        rowKey: 'Id',
      },

      columns: [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '付款单号',
          dataIndex: 'RemittedOrderNo',
          width: 250,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 120,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '实际付款金额',
          dataIndex: 'ActualPaymentAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 120,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '打款状态',
          dataIndex: 'RemittanceStatusStr',
          width: 120,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },

        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '审核结束时间',
          dataIndex: 'AuditCompleteTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 300,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: '0b79bc0f-c260-49ed-8034-3208b2fe0294',
              isShow: (record) => {
                //未提交 = 0, 审核中 = 1, 审核通过 = 2, 审核失败 = 3
                return record.AuditStatus == 1 || record.AuditStatus == 2
              },
            },
            {
              name: '编辑',
              id: 'a16c8cfb-d1ff-4ba8-bd4a-8129080e4fcb',
              isShow: (record) => {
                // 已冲红的付款申请，只能查看详情，不能编辑和删除
                return (record.AuditStatus == 0 || record.AuditStatus == 3) && record.RemittanceStatus != 4101
              },
            },
            {
              name: '删除',
              id: '65104f5f-5245-4d74-b960-aebef41d5d72',
              isShow: (record) => {
                // 已冲红的付款申请，只能查看详情，不能编辑和删除
                return (record.AuditStatus == 0 || record.AuditStatus == 3) && record.RemittanceStatus != 4101
              },
            },
            {
              name: '打款凭证',
              id: '55c2bf07-f454-421b-a576-128d5b5078de',
              isShow: (record) => {
                return record.AuditStatus == 2  && record.RemittanceStatus != 4101
              },
            },
            {
              name: '撤回',
              id: '7c720cdc-3f65-4a8b-8e1b-77dcc2436c7d',
              isShow: (record) => {
                // 审核中的采购订单增加撤回
                return [1].includes(record.AuditStatus)  && record.RemittanceStatus != 4101
              },
            },
          ],
        },
      ],
      isInitData: false,
      httpHead: 'P36009',
      url: {
        list: '/{v}/PurchaseRemittedOrder/GetPageList',
        tabCount: '',
        exportUrl: '/{v}/PurchaseRemittedOrder/ExportPurchaseRemittedOrder',
        delete: '/{v}/PurchaseRemittedOrder/DeletePurchaseRemittedOrder',
        cancel: '/{v}/PurchaseRemittedOrder/RevokePurchaseRemittedOrder', //撤回
      },
    }
  },
  methods: {
    onOperateClick(key) {
      if (key === '申请付款') {
        this.onDetailClick('ApplyForPaymentPage', { type: 1 })
      } else if (key === '导出') {
        const params = this.getQueryParams()
        const noConDition = params ? Object.values(params).filter(it => it).length <= 5 : true
        if (noConDition) {
          this.$message.warning('请您先选择创建时间/审核结束时间再导出(时间跨度最大不超过一年)')
          return
        }
        const { CreateTimeBegin, CreateTimeEnd, AuditTimeBegin, AuditTimeEnd } = params
        const hasTimeFilter = CreateTimeBegin && CreateTimeEnd && moment(CreateTimeEnd).diff(moment(CreateTimeBegin), 'year', true) > 1
        const hasAuditTimeFilter = AuditTimeBegin && AuditTimeEnd && moment(AuditTimeEnd).diff(moment(AuditTimeBegin), 'year', true) > 1
        if (hasTimeFilter || hasAuditTimeFilter) {
          this.$message.warning('请您先选择创建时间/审核结束时间再导出(时间跨度最大不超过一年)')
          return
        }
        this.handleExportXls(
          '采购付款列表',
          'Get',
          this.url.exportUrl,
          null,
          this.httpHead,
          params
        )
      }
    },
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('PurchasePaymentDetail', { id: record.Id })
      } else if (key === '编辑') {
        this.onDetailClick('ApplyForPaymentPage', { type: 1, id: record.Id })
      } else if (key === '删除') {
        this.handleDeleteClick(record.Id)
      } else if (key === '打款凭证') {
        this.getFiles(record.Id)
      } else if (key === '撤回') {
        let that = this
        that.$confirm({
          title: '提示',
          content: '您确定要撤回该笔订单吗?',
          onOk: function () {
            that.loading = true
            //PurchaseRemittedFunMode  采购付款单=1，采购预付款发票单=2
            postAction(that.url.cancel, { Id: record.Id,PurchaseRemittedFunMode:1 }, that.httpHead).then((res) => {
              that.loading = false
              if (res.IsSuccess) {
                that.$message.success('操作成功')
              } else {
                that.$message.error(res.Msg)
              }
              that.loadData()

            })
          },
        })
      }
    },
    getFiles(id) {
      let that = this
      that.loading = true
      getAction('/{v}/PurchaseRemittedOrder/GetRemittedOrderFileList', { id: id }, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data && res.Data.length) {
              let fileList = []
              if (res.Data && res.Data.length > 0) {
                res.Data.map(item => {
                  fileList.push(item.File || '')
                })
              }
              that.$refs.imageListModal.show(fileList)
            } else {
              that.$message.warning('暂无打款凭证')
            }
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .catch((e) => {
          that.loading = false
        })
        .finally(() => {
          that.loading = false
        })
    },
  },
}
</script>
<style scoped>

</style>
