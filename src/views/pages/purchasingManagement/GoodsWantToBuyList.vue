<!--
 * @Author: LP
 * @Description: 商品求购列表
 * @Date: 2023/07/05
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
    >
      <!--<template slot="operateBtnBefore">-->
      <!-- <a-upload
          name="file"
          :showUploadList="false"
          :multiple="false"
          :headers="tokenHeader"
          :action="importExcelUrl"
          @change="handleImportExcel"
        > -->
      <!-- <a-button type="primary" icon="import" @click="onOperateClick('导入')">导入</a-button> -->
      <!-- </a-upload> -->
      <!--  </template>-->
      <span slot="images" slot-scope="{ text, record }">
        <template v-if="record.ImageUrls && record.ImageUrls.length > 0">
          <ImageControl
            v-for="(img, index) in getImageUrls(record.ImageUrls)"
            :key="index"
            :src="img || ''"
            :width="30"
            :height="15"
            style="float: left; margin-right: 5px"
          />
        </template>
        <span v-else>--</span>
      </span>
      <span slot="remark" slot-scope="{ text, record }">
        <j-ellipsis
          :value="
            record.FeedbackRemark && record.FeedbackRemark.length > 0
              ? record.FeedbackRemark.map((v) => v.Remark + ' ' + v.CreateByName + ' ' + v.CreateTime + ',').toString()
              : '--'
          "
        />
      </span>
    </TableView>
    <!-- 添加备注 -->
    <AddRemarksToWantToBuyModal ref="addRemarksToWantToBuyModal" @ok="modalFormOk" />
    <!-- 查看和回复 -->
    <GoodsWantToBuyModal ref="goodsWantToBuyModal" @ok="modalFormOk" />
    <!-- 导入弹窗 -->
    <!-- <GoodsWantToBuyImportModal ref="goodsWantToBuyImportModal" @ok="modalFormOk" /> -->
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
const ImageControl = () => import('@/components/yyl/components/ImageControl')
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'GoodsWantToBuyList',
  mixins: [ListMixin],
  components: { ImageControl, JEllipsis },
  data() {
    return {
      searchItems: [
        {
          name: '反馈编号',
          type: SEnum.INPUT,
          key: 'Code',
        },
        {
          name: '客户名称',
          type: SEnum.INPUT,
          key: 'CustomerName',
        },

        {
          name: '联系电话',
          type: SEnum.INPUT,
          key: 'LinkePhone',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
        {
          name: '回复时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate1,
          key: 'ReplyTime',
        },
      ],
      tabDataList: [
        {
          name: '未回复',
          value: 'false',
          count: '',
          key: 'NoReplyNum',
        },
        {
          name: '已回复',
          value: 'true',
          count: '',
          key: 'RepliedNum',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        curStatus: 'false',
        tabStatusKey: 'IsReply', //标签的切换关键字
        tabTitles: ['采购订单列表'],
        operateBtns: [
          // {
          //     name: '导入',
          //     icon: 'import',
          //     key: '导入',
          //     id: 'a7acc46d-1689-4e5c-98ca-b9027acfaf26'
          //   },
          //   {
          //     name: '导出',
          //     icon: 'export',
          //     key: '导出',
          //     id: 'a7acc46d-1689-4e5c-98ca-b9027acfaf26'
          //   }
        ],
        rowKey: 'Id',
        customSlot: ['remark', 'images'],
      },
      httpHead: 'P31121',
      columns: [
        {
          title: '反馈编号',
          dataIndex: 'Code',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '反馈描述',
          dataIndex: 'Description',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '反馈图片',
          dataIndex: 'ImageUrls',
          width: 200,
          scopedSlots: { customRender: 'images' },
          ellipsis: true,
        },
        {
          title: '备注',
          dataIndex: 'FeedbackRemark',
          width: 200,
          scopedSlots: { customRender: 'remark' },
          ellipsis: true,
        },
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '联系电话',
          dataIndex: 'LinkPhone',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '回复时间',
          dataIndex: 'PeplyTime',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '添加备注',
              id: '60b5aa56-b529-42c4-9770-be2d19616d16',
              isShow: (record) => {
                return this.tab.curStatus == 'false'
              },
            },
            {
              name: '回复',
              id: '6592ce60-8af1-4ecf-b789-13d7a418168a',
              isShow: (record) => {
                return this.tab.curStatus == 'false'
              },
            },
            {
              name: '查看',
              id: '87db293e-428d-4279-b846-2937e19d0083',
              isShow: (record) => {
                return this.tab.curStatus == 'true'
              },
            },
          ],
        },
      ],
      dcLoading: false,
      drLoading: false,
      url: {
        list: '/Feedback/list',
        tabCount: '/Feedback/FeedbackNum',
        exportUrl: '', //导出
        // importExcelUrl: '' //导入
      },
    }
  },
  computed: {
    // importExcelUrl: function() {
    //   return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    // }
  },
  created() {},
  methods: {
    loadBefore() {
      this.queryParam['Type'] = 4
    },
    getImageUrls(list) {
      let newList = JSON.parse(list[0])
      return newList
    },
    onOperateClick(key) {
      if (key == '导出') {
        this.handleExportXls(
          '商品求购',
          'Get',
          this.url.exportUrl,
          null,
          this.httpHead,
          this.$refs.searchView.queryParam
        )
      } else if (key == '导入') {
        this.$refs.goodsWantToBuyImportModal.show()
      }
    },
    onActionClick(record, key, index) {
      if (key == '添加备注') {
        this.$refs.addRemarksToWantToBuyModal && this.$refs.addRemarksToWantToBuyModal.add(record.Id)
      } else {
        this.$refs.goodsWantToBuyModal && this.$refs.goodsWantToBuyModal.edit({ ...record, isReply: key == '查看' })
      }
    },
  },
}
</script>
<style scoped>

</style>
