<!--
 * @Author: LP
 * @Description: 预付款结算审核列表
 * @Date: 2023-12-21 17:29:00
-->
<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
    />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PurPrepaymentSettlementAuditList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'KeyWord',
        },
        {
          name: '结算单号',
          type: SEnum.INPUT,
          key: 'SettlementOrderNo',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
      ],
      tabDataList: [],
      tab: {
        curStatus: 1,
        tabStatusKey: 'AuditStatus',
        tabValueKey: 'AuditStatus',
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        rowKey: 'Id',
      },

      httpHead: 'P36009',
      url: {
        list: '/{v}/PurchaseSettlement/GetPurchaseSettlementOrderAuditList',
        tabCount: '/{v}/PurchaseSettlement/GetPurchaseSettlementOrderAuditListCount',
        listType: 'POST',
        tabIsList: true,
        setValidUrl: '',
      },
    }
  },
  computed: {
    columns() {
      let auditStatusList =
        this.tab.curStatus == 0
          ? [
              {
                title: '审核状态',
                dataIndex: 'AuditStatusStr',
                width: 100,
                scopedSlots: { customRender: 'state' },
                ellipsis: true,
              },
            ]
          : []
      let centerList = [
        {
          title: '结算单号',
          dataIndex: 'SettlementOrderNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '结算金额',
          dataIndex: 'TotalCurrentSettledAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ]

      return [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        ...centerList,
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        ...auditStatusList,
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: 'bebfabd2-6979-42af-957d-650c347736bd',
              isShow: (record) => {
                return this.tab.curStatus == 0
              },
            },
            {
              name: '审核',
              id: '6b5e3cd4-8fcd-4266-bb12-6097c9ee460c',
              isShow: (record) => {
                return this.tab.curStatus == 1
              },
            },
          ],
        },
      ]
    },
  },
  created() {},

  methods: {
    onOperateClick(key) {},
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('PurPrepaymentSettlementAuditPage', { id: record.PurchaseSettlementOrderRecordId, type: 1 })
      } else if (key === '审核') {
        this.onDetailClick('PurPrepaymentSettlementAuditPage', { id: record.PurchaseSettlementOrderRecordId, type: 2 })
      }
    },
  },
}
</script>
<style scoped>

</style>
