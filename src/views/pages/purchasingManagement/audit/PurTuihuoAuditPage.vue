<!--
 * @Author: LP
 * @Description: 采购退货审核详情
 * @Date: 2023/07/11
-->
<template>
  <a-spin :spinning="loading">
    <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }" v-if="model">
      <a-descriptions :column="7">
        <a-descriptions-item>
          <div class="flc hcenter">
            <a-icon :type="getAuditIcon(model.AuditStatus)" theme="twoTone" :two-tone-color="getAuditColor(model.AuditStatus)" style="font-size: 32px" />
            <span :style="{ color: getAuditColor(model.AuditStatus) }">{{ model.AuditStatusStr || '--' }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">供应商名称</span>
          <div>{{ model.SupplierName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">退货单号</span>
          <div>{{ model.RefundOrderNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">订单编号</span>
          <div>{{ model.PurchaseOrderNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">退货金额</span>
          <div>¥{{ originModel.TotalRefundAmount }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建人</span>
          <div>{{ model.CreateByName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
    <a-card :bodyStyle="{ padding: '16px 16px' }" class="mt15">
      <a-row>
        <a-col span="24" style="margin-bottom: 5px">
          <span class="c999">退货原因：</span><span>{{ model.ReturnReasonStr || '--' }}</span>
        </a-col>
        <a-col span="24">
          <TableView ref="tableView" :showCardBorder="false" :tab="tab" :columns="columns" :dataList="model.GoodsItems || []" />
        </a-col>
        <a-col span="24">
          <span class="c999">退款金额：</span><span>{{ model.TotalRefundAmount || '--' }}</span>
        </a-col>
        <a-col span="24">
          <span class="c999">物流方式：</span><span>{{ model.LogisticsMethodName || '--' }}</span>
        </a-col>
        <a-col span="24">
          <span class="c999">备注：</span><span>{{ model.Remark || '--' }}</span>
        </a-col>
      </a-row>
      <a-row class="mt20">
        <a-col span="24"><span style="font-size: 16px; font-weight: 600">审核信息</span></a-col>
        <a-col span="24" class="mt10" v-if="opType == 2 ? approvalModel : orderId">
          <SupAuditInformation :aList="approvalModel ? approvalModel.ApprovalWorkFlowInstanceResults : []" :bussinessNo="orderId" />
        </a-col>
      </a-row>

      <!-- 底部区域 -->
      <a-affix :offset-bottom="10" class="affix">
        <a-divider />
        <div>
          <a-button @click="goBack(true,true)" class="mr8">返回</a-button>
          <template v-if="opType == 2 && showBtn">
            <YYLButton menuId="78ce340e-2498-4f31-a1ef-23bf25cc1aad" text="审核通过" type="primary" :loading="confirmLoading" @click="onPassClick" />
            <YYLButton menuId="ff198386-7858-4f20-a833-a223c01e6591" text="审核驳回" type="danger" :loading="confirmLoading" @click="onRejectClick" />
          </template>
        </div>
      </a-affix>
    </a-card>
    <!-- 审核弹框 -->
    <AuditRemarkModal ref="auditRemarkModal" @ok="onAuditModalOk" />
  </a-spin>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'PurTuihuoAuditPage',
  mixins: [EditMixin, ListMixin],
  data() {
    return {
      model: {},
      loading: false,
      confirmLoading: false,
      orderId: '', //采购订单id
      refundOrderNo: '',
      opType: 1, //1详情  2审核
      httpHead: 'P36009',
      tab: {
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        rowKey: '',
      },

      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          width: 150,
          ellipsis: true,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          width: 100,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          width: 100,
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 200,
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 100,
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
        },
        {
          title: '批次号',
          dataIndex: 'BatchNumber',
          width: 150,
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
        },
        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 150,
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
        },
        {
          title: '有效期',
          dataIndex: 'ExpirationDate',
          width: 100,
          // scopedSlots: { customRender: 'component' },
          customRender: this.customRowAndColRender,
          ellipsis: true,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          width: 150,
          precision: 6,
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => this.customRowAndColRender(text, record, index, 'price'),
          ellipsis: true,
        },
        // {
        //   title: '采销批次在库成本',
        //   dataIndex: 'CaixiaoPCCost',
        //   width: 150,
        //   precision: 6,
        //   // scopedSlots: { customRender: 'price' },
        //   customRender: (text, record, index) => this.customRowAndColRender(text, record, index, 'price'),
        //   ellipsis: true,
        // },
        {
          title: 'erp批次在库成本',
          dataIndex: 'ErpPCCost',
          width: 150,
          precision: 6,
          // scopedSlots: { customRender: 'price' },
          customRender: (text, record, index) => this.customRowAndColRender(text, record, index, 'price'),
          ellipsis: true,
        },
        {
          title: '入库数量',
          dataIndex: 'InboundQuantity',
          // scopedSlots: { customRender: 'number' },
          customRender: (text, record, index) => this.customRowAndColRender(text, record, index, 'number'),
          width: 100,
          ellipsis: true,
        },
        {
          title: '已退货数量',
          dataIndex: 'GoodsExistsRefundCount',
          // scopedSlots: { customRender: 'number' },
          customRender: (text, record, index) => this.customRowAndColRender(text, record, index, 'number'),
          width: 100,
          ellipsis: true,
        },
        {
          title: '可退货数量',
          dataIndex: 'CanRefundCount',
          // scopedSlots: { customRender: 'number' },
          customRender: (text, record, index) => this.customRowAndColRender(text, record, index, 'number'),
          width: 100,
          ellipsis: true,
        },
        {
          title: '仓库名称',
          dataIndex: 'WarehouseName',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '本次退货数量',
          dataIndex: 'RefundCount',
          width: 150,
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        {
          title: '退货单价',
          dataIndex: 'RefundPrice',
          width: 150,
          precision: 6, //数值精度
          min: 0.01,
          disabled: false,
          scopedSlots: { customRender: 'inputNumber' },
          ellipsis: true,
          fixed: 'right',
        },
      ],
      showBtn: false,
      isCanUpdate: false,
      approvalModel: null,
      isAllPriceSame: null,
      allPrice1: 0,
      allPrice2: 0,
      originModel: [],//源model
      url: {
        approvalUrl: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
        detail: '/{v}/PurchaseBusiness/GetPurchaseRefundAuditDetail',
        save: '/{v}/PurchaseBusiness/AuditEditPurchaseRefund',
      },
    }
  },
  watch: {
    model: {
      handler(newVal, oldVal) {
        this.parseListData()
      },
      deep: true,
    },
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.orderId = this.$route.query.id || ''
      this.refundOrderNo = this.$route.query.refundOrderNo || ''
      this.opType = Number(this.$route.query.type) || 1
      this.setTitle('采购退货审核' + (this.opType == 1 ? '详情' : ''))
      this.checkCanUpdateTuiPrice()
      let RefundPriceObj = this.columns.find(item => {
        return item.dataIndex == 'RefundPrice'
      })
      if (RefundPriceObj) {
        RefundPriceObj.disabled = this.isCanUpdate ? false : true
        RefundPriceObj.scopedSlots = { customRender: this.opType == 1 ? 'price' : 'inputNumber' }
      }
      this.loadDetail()
      if (this.opType == 2) {
        this.getApprovalResults()
      }
    }
  },
  created() { },
  methods: {
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.orderId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            that.originModel = JSON.parse(JSON.stringify(res.Data || {}))
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    checkCanUpdateTuiPrice() {
      let menuId = '9ef132b4-9dc5-4a86-8499-68621fabdb9a'
      let isCanUpdate = this.checkBtnPermissions(menuId) ? true : false
      this.isCanUpdate = isCanUpdate
    },
    compareArraysByProperty(arr1, arr2, property) {
      if (arr1.length !== arr2.length) return false;

      for (let i = 0; i < arr1.length; i++) {
        if (arr1[i][property] !== arr2[i][property]) {
          return false;
        }
      }
      return true;
    },
    /**
     * 获取审批记录
     */
    getApprovalResults() {
      if (!this.model) {
        return
      }
      this.loading = true
      getAction(this.url.approvalUrl, { bussinessNo: this.orderId }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            this.approvalModel = res.Data || {}

            this.checkLoginUserIsHasBtnPerssion(this.approvalModel, (bool) => {
              this.showBtn = bool
            })
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },

    /**
     * 通过
     */
    onPassClick() {
      this.checkReturnInventory(this.goPassClick)
    },
    goPassClick() {
      const array1 = this.originModel.GoodsItems
      const array2 = this.model.GoodsItems
      this.isAllPriceSame = this.compareArraysByProperty(array1, array2, 'RefundPrice')
      // 源退货单价
      let allPrice1 = 0
      let allPrice2 = 0
      array1.map(item => {
        allPrice1 += item.RefundPrice * item.RefundCount
      })
      array2.map(item => {
        allPrice2 += item.RefundPrice * item.RefundCount
      })
      this.allPrice1 = Number(allPrice1.toFixed(2))
      this.allPrice2 = Number(allPrice2.toFixed(2))
      this.showAuditModel(1)
    },
    // 校验采购退货单库存是否足够
    checkReturnInventory(callback) {
      if (!this.refundOrderNo) {
        return
      }
      let formData = {
        refundOrderNo: this.refundOrderNo,
      }
      this.confirmLoading = true
      getAction('/{v}/PurchaseBusiness/ValidateGoodsStockIsEnough', formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            callback && callback()
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((e) => {
          this.confirmLoading = false
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    /**
     * 驳回
     */
    onRejectClick() {
      this.showAuditModel(2)
    },
    saveData(callback, callData) {
      let that = this
      let data = this.model
      let formData = {
        ...data,
        PurchaseOrderId: '00000000-0000-0000-0000-000000000000',
        PurchaseRefundId: '00000000-0000-0000-0000-000000000000',
        PurchaseRefundOrderRecordId: this.orderId,
      }
      that.confirmLoading = true
      postAction(that.url.save, formData, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            callback && callback(callData)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .catch((e) => {
          that.confirmLoading = false
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },

    /**
     * 显示审核弹窗
     * @param {*} type 1通过  2驳回
     */
    showAuditModel(type) {
      let that = this
      if (this.$refs.auditRemarkModal) {
        let param = {
          ApprovalWorkFlowInstanceId: that.approvalModel ? that.approvalModel.ApprovalWorkFlowInstanceId : '',
          IsHasSuperior: that.approvalModel ? that.approvalModel.IsHasSuperior : false,
        }
        let remark = ''
        if (this.isAllPriceSame == false && type == 1) {
          remark = '修改退货单价,退货总价从【' + (this.allPrice1) + '】修改为【' + this.allPrice2 + '】'
        } else {
          remark = ''
        }
        this.$refs.auditRemarkModal.show(type, param, remark)
      }
    },
    /**
     * 审核弹窗回调
     */
    onAuditModalOk(formData) {
      if (formData) {
        // 审核通过
        if (this.isAllPriceSame) {
          this.onAuditModalOkSub(formData)
        } else {
          this.saveData(this.onAuditModalOkSub, formData)
        }
      } else {
        // 审核失败
        this.onAuditModalOkSub()
      }
    },
    onAuditModalOkSub(formData) {
      if (formData) {
        this.$refs.auditRemarkModal.postAuditInstance(formData, (bool) => {
          if (bool) {
            this.goBack(true,true)
          }
        })
      } else {
        this.goBack(true,true)
      }
    },
    /**
     * 自定义行和列渲染
     * @param {*} text
     * @param {*} record
     * @param {*} index
     * @param {* 数据类型 number price等，主要特殊处理price显示要加￥符号} dataType
     */
    customRowAndColRender(text, record, index, dataType) {
      let newText = text
      if (dataType == 'price') {
        newText =
          text == undefined || text == null
            ? '--'
            : (text < 0 ? '-' : '') + '￥' + Number(text < 0 ? String(text).replace('-', '') : text).toFixed(6)
      }
      const obj = {
        children: newText,
        attrs: {},
      }

      obj.attrs.rowSpan = record.rowSpan >= 0 ? record.rowSpan : 1
      obj.attrs.colSpan = 1
      return obj
    },
    /**
     * 解析列表数据目的设置正确的rowSpan
     */
    parseListData() {
      // 修改退款金额
      if (this.model.GoodsItems && this.model.GoodsItems.length > 0) {
        let TotalRefundAmount = 0
        this.model.GoodsItems.forEach(x => {
          TotalRefundAmount += x.RefundPrice * x.RefundCount
        })
        this.model.TotalRefundAmount = Number(TotalRefundAmount.toFixed(2))
      }
      if (this.model.GoodsItems && this.model.GoodsItems.length > 1) {
        //把三个字段组成一个字段赋值，后面处理数据使用，作为key
        let list = this.model.GoodsItems
        list.forEach((x) => {
          x['mergeKey'] = x.ErpGoodsCode + '-' + x.BatchNumber + '-' + x.ProductionBatchNumber
        })
        let keyList = list.map((item) => {
          return item['mergeKey']
        })
        console.log('解析listData keyList', keyList)
        //找到相同的mergeKey的数据
        let sameKeyList = keyList.filter((val, i) => keyList.indexOf(val) !== i)
        console.log('解析listData sameKeyList', sameKeyList)

        //方案一 缺点需要唯一key作为判断依据
        // let sameList = []
        // sameKeyList.forEach((x) => {
        //   let filterList = list.filter((item) => item.mergeKey == x)
        //   if (filterList && filterList.length > 0) {
        //     sameList.push({
        //       key: x,
        //       list: filterList,
        //     })
        //   }
        // })
        // console.log('解析listData sameList', sameList)
        // list.forEach((old, oldIndex) => {
        //   sameList.forEach((same, sameIndex) => {
        //     if (same.key == old.mergeKey) {
        //       //注意这里需要不重复的属性去判断，如Id
        //       if (same.list[0].Id == old.Id) {
        //         old.rowSpan = same.list.length
        //       }
        //     }
        //   })
        // })

        //方案二 优点 不需要唯一key作为判断依据
        let sameList = []
        sameKeyList.forEach((x) => {
          sameList.push({
            key: x,
            length: keyList.filter((y) => y === x).length,
            index: keyList.indexOf(x), //相同元素首次出现的位置
          })
        })
        console.log('解析listData sameList', sameList)
        list.forEach((old, oldIndex) => {
          sameList.forEach((same, sameIndex) => {
            if (old.mergeKey == same.key) {
              if (oldIndex == same.index) {
                old.rowSpan = same.length
              } else {
                old.rowSpan = 0
              }
            }
          })
        })

        console.log('解析list ', list)
      }
    },
  },
}
</script>

<style scoped>

</style>
