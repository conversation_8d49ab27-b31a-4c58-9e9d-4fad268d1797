<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="onSetValid" @operate="operate" @getListAmount="getListAmount" @changeTab="changeTab" />
    <!-- 新增 -->
    <ReceivableAndPayableAddModal ref="ReceivableAndPayableAddModal" @ok="modalOk"></ReceivableAndPayableAddModal>
  </a-row>
</template>

<script>

import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: "ReceivableAndPayableAuditList",
  title: '应收/应付调整单审核',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [ //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '调整单号', type: 'input', value: '', key: 'Code', defaultVal: '', placeholder: '请输入' },
        { name: '企业名称', type: 'input', value: '', key: 'MerchantKey', defaultVal: '', placeholder: '请输入' },
        {
          name: '调整类型',
          type: 'select',
          value: '',
          key: 'AccountType',
          defaultVal: [
            { title: '应收', value: 1 },
            { title: '应付', value: 3 },
          ],
          placeholder: '请选择'
        },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [

        ],
        hintArray: [],
        status: 1,
        statusKey: 'AuditStatus',//标签的切换关键字
        statusList: [
          {
            name: '待审核',
            value: 1,
            count: 0,
          },
          {
            name: '审核记录',
            value: null,
            count: 0,
          },
        ],
      },
      columns: [
        {
          title: '调整单号',
          dataIndex: 'Code',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '企业名称',
          dataIndex: 'MerchantName',
          width: 220,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调整类型',
          dataIndex: 'AccountTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调整金额',
          dataIndex: 'AdjustmentAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调整后余额',
          dataIndex: 'NewAdjustmentAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: "right",
          actionBtn: [
            {
              name: '审核', icon: '', id: '1f32e20d-e6ca-431f-b417-bd4327feb008', specialShowFuc: (record) => {
                //未提交 = 0, 审核中 = 1, 审核通过 = 2, 审核失败 = 3
                return record.AuditStatus == 1
              },
            },
            {
              name: '详情', icon: '', id: 'f4b18dee-18df-4558-9a3a-941bb66d42ea', specialShowFuc: (record) => {
                return record.AuditStatus !== 1
              },
            },
          ],
          scopedSlots: { customRender: 'action' }
        }
      ],
      queryParam: {

      },
      isTableInitData: true,//是否自动加载
      linkHttpHead: 'P36011',
      linkUrlType: 'GET',//请求方式
      linkUrl: {
        list: '/{v}/Adjustment/GetAuditListAsync',
        listAmount: '/{v}/Adjustment/GetAuditCountAsync',
      },
    }
  },
  created() {

  },
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    // 搜索和重置搜索
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        queryParam[this.tab.statusKey] = this.tab.status
      }
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 状态切换
    changeTab(value, statusKey) {
      let columnsItem = {
        title: '审核状态',
        dataIndex: 'AuditStatusStr',
        width: 150,
        ellipsis: true,
        scopedSlots: { customRender: 'component' },
      }
      let index = this.columns.findIndex(item => {
        return item.dataIndex == 'AuditStatusStr'
      })
      if (this.tab.status == 1) {
        if (index >= 0) {
          this.columns.splice(index, 1)
        }
      } else {
        if (index < 0) {
          this.columns.splice(index, 0, columnsItem)
        }
      }
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      if (statusKey) {
        this.queryParam[statusKey] = value
      }
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    getListAmount(data) {
      if ((data || []).length) {
        data.map((item, index) => {
          this.tab.statusList[index].count = item.Count || 0
        })
      }
    },
    modalOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '审核') {
        this.onDetailClick('ReceivableAndPayableAudit', { id: record.Id, isAudit: true })
      } else if (type == '详情') {
        this.onDetailClick('/pages/financialManagement/accountManagement/ReceivableAndPayableInfo', { id: record.Id, isAuditInfo: true })
      }
    },

  }

}
</script>

<style>
</style>