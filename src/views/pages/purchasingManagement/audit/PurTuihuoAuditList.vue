<!--
 * @Author: LP
 * @Description: 采购退货审核列表
 * @Date: 2023/07/11
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView ref="tableView" showCardBorder :tabDataList="tabDataList" :tab="tab" :columns="columns" :dataList="dataSource" @operateClick="onOperateClick" @actionClick="onActionClick" />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PurTuihuoAuditList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierKeyWords',
        },
        {
          name: '退货单号',
          type: SEnum.INPUT,
          key: 'RefundOrderNo',
        },
        {
          name: '订单编号',
          type: SEnum.INPUT,
          key: 'PurchaseOrderNo',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
      ],
      tabDataList: [],
      tab: {
        curStatus: 1,
        tabStatusKey: 'AuditStatus',
        tabValueKey: 'AuditStatus',
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        rowKey: 'Id',
      },

      httpHead: 'P36009',
      url: {
        list: '/{v}/PurchaseBusiness/QueryPurchaseRefundAuditList',
        tabCount: '/{v}/PurchaseBusiness/QueryPurchaseRefundAuditListCount',
        tabIsList: true,
        setValidUrl: '',
      },
    }
  },
  computed: {
    columns() {
      let auditStatusList =
        this.tab.curStatus == 0
          ? [
            {
              title: '审核状态',
              dataIndex: 'AuditStatusStr',
              width: 100,
              scopedSlots: { customRender: 'state' },
              ellipsis: true,
            },
          ]
          : []
      return [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '退货单号',
          dataIndex: 'RefundOrderNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '退货金额',
          dataIndex: 'TotalRefundAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '下单时间',
          dataIndex: 'CreateTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        ...auditStatusList,
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: '6a352183-dd22-4374-9f53-21b489c6acc6',
              isShow: (record) => {
                return this.tab.curStatus == 0
              },
            },
            {
              name: '审核',
              id: 'f2a46774-0970-4574-8c85-78f77abdc287',
              isShow: (record) => {
                return this.tab.curStatus == 1
              },
            },
          ],
        },
      ]
    },
  },
  created() { },

  methods: {
    onOperateClick(key) { },
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('PurTuihuoAuditPage', { id: record.Id, type: 1 })
      } else if (key === '审核') {
        this.onDetailClick('PurTuihuoAuditPage', { id: record.Id, refundOrderNo: record.RefundOrderNo, type: 2 })
      }
    },
  },
}
</script>
<style scoped>

</style>
