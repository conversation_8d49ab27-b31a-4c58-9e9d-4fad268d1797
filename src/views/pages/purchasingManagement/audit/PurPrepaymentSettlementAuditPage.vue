<!--
 * @Author: LP
 * @Description: 预付款结算审核
 * @Date: 2023-12-25 16:13:29
-->
<template>
  <a-spin :spinning="loading">
    <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }" v-if="model">
      <a-descriptions :column="7">
        <a-descriptions-item>
          <div class="flc hcenter">
            <a-icon
              :type="getAuditIcon(model.AuditStatus)"
              theme="twoTone"
              :two-tone-color="getAuditColor(model.AuditStatus)"
              style="font-size: 32px"
            />
            <span :style="{ color: getAuditColor(model.AuditStatus) }">{{ model.AuditStatusStr || '--' }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">供应商名称</span>
          <div>{{ model.SupplierName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">付款方式</span>
          <div>{{ model.PurchasePaymentModeStr || '预付款' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建人</span>
          <div>{{ model.CreateByName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
    <a-card :bodyStyle="{ padding: '0 16ox 16px 16px' }" class="mt15">
      <a-row>
        <a-col span="24">
          <TableView
            ref="tableView"
            :showCardBorder="false"
            :tab="tab"
            :columns="columns"
            :dataList="model.BusinessOrderList || []"
            @operateClick="onOperateClick"
            @actionClick="onActionClick"
          />
        </a-col>
        <template v-if="(model.BusinessOrderList || []).length > 0">
          <a-col span="24">
            <span class="c999">本次结算金额合计：</span><span>{{ getShowPrice(model.TotalSettlementAmount) }}</span>
          </a-col>
          <a-col span="24" class="mt15">
            <div style="font-weight: bold; padding-bottom: 10px; font-size: 15px">发票信息</div>
            <div>本次结算包含的发票号：{{ model.RemittedInvoiceNoStr || '--' }}</div>
          </a-col>
        </template>
      </a-row>
      <a-row class="mt20">
        <a-col span="24"><span style="font-size: 16px; font-weight: 600">审核信息</span></a-col>
        <a-col span="24" class="mt10" v-if="opType == 2 ? approvalModel : orderId">
          <SupAuditInformation
            :aList="approvalModel ? approvalModel.ApprovalWorkFlowInstanceResults : []"
            :bussinessNo="orderId"
          />
        </a-col>
      </a-row>

      <!-- 底部区域 -->
      <a-affix :offset-bottom="10" class="affix">
        <a-divider />
        <div>
          <a-button @click="goBack(true,true)" class="mr8">返回</a-button>
          <template v-if="opType == 2 && showBtn">
            <YYLButton
              menuId="5ff79d0f-114b-4cbd-9c77-d7db7cfbacb8"
              text="审核通过"
              type="primary"
              :loading="confirmLoading"
              @click="onPassClick"
            />
            <YYLButton
              menuId="30cbe8dc-df11-4b29-961d-065eb3af6a14"
              text="审核驳回"
              type="danger"
              :loading="confirmLoading"
              @click="onRejectClick"
            />
          </template>
        </div>
      </a-affix>
    </a-card>
    <!-- 审核弹框 -->
    <AuditRemarkModal ref="auditRemarkModal" @ok="onAuditModalOk" />

    <!-- 商品明细 -->
    <SettlementGoodsDetailListModal ref="settlementGoodsDetailListModal" />
    <!-- 勾稽 -->
    <SettlementCheckingModal ref="settlementCheckingModal" />
  </a-spin>
</template>

<script>
import { getAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'PurPrepaymentSettlementAuditPage',
  mixins: [EditMixin, ListMixin],
  data() {
    return {
      model: {},
      loading: false,
      confirmLoading: false,
      orderId: '', //采购订单id
      opType: 1, //1详情  2审核
      httpHead: 'P36009',
      tab: {
        tabTitles: ['结算单据列表'], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [
          {
            name: '查看结算勾稽',
            type: 'primary',
            key: '查看结算勾稽',
          },
        ], //右上角按钮集合
      },
      showBtn: false,
      approvalModel: null,
      url: {
        approvalUrl: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
        detail: '/{v}/PurchaseSettlement/GetPurchaseSettlementBusinessOrderAuditList',
      },
    }
  },
  computed: {
    columns() {
      return [
        {
          title: '单据编号',
          dataIndex: 'BusinessOrderNo',
          scopedSlots: { customRender: 'component' },
          width: 200,
          ellipsis: true,
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessEventTypeStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '对应订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '发生日期',
          dataIndex: 'BusinessTime',
          width: 150,
          scopedSlots: { customRender: 'date' },
          ellipsis: true,
        },
        {
          title: '含税合计金额',
          dataIndex: 'TotalTaxIncludedPurchaseAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '未勾稽发票金额',
          dataIndex: 'UnVerificationAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '未结算金额',
          dataIndex: 'UnSettlementAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '审核中未结算金额',
          dataIndex: 'UnTotalSettledAmountOnAudit',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '剩余未结算金额',
          dataIndex: 'UnTotalSettledAmountRemaining',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '商品明细',
              id: 'ec5349ed-df4f-4273-94aa-0708fcf8281d',
            },
          ],
        },
      ]
    },
  },
  mounted() {
    if (this.$route.query) {
      this.orderId = this.$route.query.id || ''
      this.opType = Number(this.$route.query.type) || 1
      this.setTitle('采购预付款审核' + (this.opType == 1 ? '详情' : ''))
      // if (this.opType == 1) {
      //   this.tab.operateBtns.push()
      // }
      this.loadDetail()
      if (this.opType == 2) {
        this.getApprovalResults()
      }
    }
  },
  created() {},
  methods: {
    /**
     * 加载详情
     **/
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { purchaseSettlementOrderRecordId: that.orderId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 获取审批记录
     */
    getApprovalResults() {
      if (!this.model) {
        return
      }
      this.loading = true
      getAction(this.url.approvalUrl, { bussinessNo: this.orderId }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            this.approvalModel = res.Data || {}

            this.checkLoginUserIsHasBtnPerssion(this.approvalModel, (bool) => {
              this.showBtn = bool
            })
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },

    /**
     * 通过
     */
    onPassClick() {
      this.showAuditModel(1)
    },
    /**
     * 驳回
     */
    onRejectClick() {
      this.showAuditModel(2)
    },

    /**
     * 显示审核弹窗
     * @param {*} type 1通过  2驳回
     */
    showAuditModel(type) {
      let that = this
      if (this.$refs.auditRemarkModal) {
        let param = {
          ApprovalWorkFlowInstanceId: that.approvalModel ? that.approvalModel.ApprovalWorkFlowInstanceId : '',
          IsHasSuperior: that.approvalModel ? that.approvalModel.IsHasSuperior : false,
        }
        console.log('param', param)
        this.$refs.auditRemarkModal.show(type, param)
      }
    },
    /**
     * 审核弹窗回调
     */
    onAuditModalOk(formData) {
      if (formData) {
        this.$refs.auditRemarkModal.postAuditInstance(formData, (bool) => {
          if (bool) {
            this.goBack(true,true)
          }
        })
      } else {
        this.goBack(true,true)
      }
    },

    onOperateClick(key) {
      if (key == '查看结算勾稽') {
        this.$refs.settlementCheckingModal.lookPay(this.orderId, true)
      }
    },
    onActionClick(record, key, index) {
      if (key === '商品明细') {
        this.$refs.settlementGoodsDetailListModal.show(record.BusinessOrderId, record.BusinessEventType, true)
      }
    },
  },
}
</script>

<style scoped>

</style>
