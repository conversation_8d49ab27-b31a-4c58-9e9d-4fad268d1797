<!--
 * @Author: LP
 * @Description: 采购付款审核
 * @Date: 2023/07/15
-->
<template>
  <div>

  <!-- :bodyStyle="{ padding: '10px 16px 5px 16px' }" -->
  <a-card>
    <a-spin :spinning="loading" style="width: 100%">
      <template v-if="!model">
        <a-empty v-if="!loading" />
      </template>
      <div v-else>
        <a-row :gutter="[16, 6]">
          <a-col :span="5"></a-col>
          <a-col :span="14" style="text-align: center">
            <a-icon type="left-circle" @click="handleSwitch(1, model.LastId)" v-if="model.LastId && isAudit" />
            <span style="font-weight: bold; font-size: 18px; padding: 0px 20px">{{ model.SupplierName || '--' }}</span>
            <a-icon type="right-circle" @click="handleSwitch(2, model.NextId)" v-if="model.NextId && isAudit"
          /></a-col>
          <a-col :span="5" style="text-align: right">
            <a-button @click="goBack(true,true)" style="margin-left: 16px">返回</a-button>
          </a-col>
        </a-row>
        <a-divider />
        <a-tabs type="card" tabPosition="left">
          <a-tab-pane key="1" tab="付款信息">
            <PaymentInfoView
              ref="paymentInfoView"
              :model="model"
              v-if="isAudit ? model && approvalModel && isSwitch : model&&isSwitch"
              :isEdit="isAudit"
              :auditList="approvalModel ? approvalModel.ApprovalWorkFlowInstanceResults || [] : []"
            />
          </a-tab-pane>
          <a-tab-pane key="2" tab="证照信息">
            <QualificationsListAudit
              ref="qualificationsListAudit"
              :groupList="groupList"
              :businessId="model.Id"
              :isEdit="isAudit"
            />
          </a-tab-pane>
        </a-tabs>
        <!-- <a-row :gutter="[16, 6]">
          <a-col :span="24">
          </a-col>
          <a-col :span="24">
          </a-col>
        </a-row> -->
        
      </div>
      <!-- 审核弹框 -->
      <AuditRemarkModal ref="auditRemarkModal" @ok="onAuditModalOk" />
    </a-spin>
  </a-card>
    <a-affix :offset-bottom="10" class="affix" v-if="isAudit && showBtn">
      <div style="padding: 10px 0;">
        <YYLButton
          menuId="dd9b53fc-98fb-4ce2-be34-0a47d94099e9"
          text="通过审核"
          type="primary"
          :loading="confirnLoading"
          @click="onPassClick"
        />
        <YYLButton
          menuId="54228bbf-74e2-4ef8-a2b6-a3c0bb931c2d"
          text="驳回审核"
          type="danger"
          :loading="confirnLoading"
          @click="onRejectClick"
        />
      </div>
    </a-affix>
  </div>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { getAction } from '@/api/manage'
import { PublicMixin } from '@/mixins/PublicMixin'
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'PurPaymentAuditPage',
  mixins: [EditMixin, PublicMixin, ListMixin],
  components: {},
  data() {
    return {
      description: '采购付款审核',
      id: '',
      model: {},
      loading: false,
      isAudit: false, //是否是审核 false则表示是详情
      groupList: [],
      confirnLoading: false,
      httpHead: 'P36009',
      approvalModel: null,
      opType: 1, //1 采购付款审核  2 采购预付款发票审核
      showBtn: false,
      isSwitch: true,
      url: {
        detail: '/{v}/PurchaseRemittedOrder/GetPurchaseRemittedInfoRecord',
        approval: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
      },
    }
  },
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id
      this.opType = this.$route.query.opType || 1
      if (this.$route.query.isAudit) {
        this.isAudit = this.$route.query.isAudit == 2
      }
      this.setTitle('采购付款审核' + (this.isAudit ? '' : '详情'))
      this.getDetailInfo()
    }
  },
  computed: {},
  created() {},
  methods: {
    moment,
    /**
     * @description: 获取详情
     * @param {*}
     * @return {*}
     */
    getDetailInfo() {
      if (!this.id) {
        return
      }
      if (this.model) {
        this.model = {}
      }
      let that = this
      this.loading = true
      getAction(this.url.detail, { id: this.id }, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            if (that.isAudit) {
              that.getApprovalResults()
            }
            that.getGroupList()
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    /**
     * 获取审批记录
     */
    getApprovalResults() {
      if (!this.model) {
        return
      }
      this.loading = true
      getAction(this.url.approval, { bussinessNo: this.model.Id }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            this.approvalModel = res.Data || {}

            this.checkLoginUserIsHasBtnPerssion(this.approvalModel, (bool) => {
              this.showBtn = bool
            })
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    toggleSwitch(){
      this.isSwitch = false
      this.$nextTick(()=>{
        this.isSwitch = true
      })
    },

    /*
     * 数据切换
     * type = 1 上一条，type = 2 下一条
     */
    handleSwitch(type, id) {
      if (id) {
        this.id = id
        // 获取详情
        this.getDetailInfo()
        this.toggleSwitch()
      } else {
        this.goBack(true,true)
      }
    },
    getGroupList() {
      if (!this.model) {
        return
      }
      this.groupList = []
      if (this.model.RemittedContractDtos && this.model.RemittedContractDtos.length > 0) {
        let group = {
          Title: '合同',
          GroupName: '合同',
          GroupCode: '合同',
          IsChanged: false,
          Qualifications: [
            {
              QualificationType: 1,
              Files: [],
            },
          ],
        }
        this.model.RemittedContractDtos.forEach((x) => {
          group.Qualifications[0].Files.push({ FileUrl: x.ImageUrl })
        })
        this.groupList.push(group)
      }
      if (this.model.RemittedReconciliationDtos && this.model.RemittedReconciliationDtos.length > 0) {
        let group = {
          Title: '对账函',
          GroupName: '对账函',
          GroupCode: '对账函',
          IsChanged: false,
          Qualifications: [
            {
              QualificationType: 1,
              Files: [],
            },
          ],
        }
        this.model.RemittedReconciliationDtos.forEach((x) => {
          group.Qualifications[0].Files.push({ FileUrl: x.ImageUrl })
        })
        this.groupList.push(group)
      }
    },
    /**
     * 通过
     */
    onPassClick() {
      this.showAuditModel(1)
    },
    /**
     * 驳回
     */
    onRejectClick() {
      this.showAuditModel(2)
    },

    /**
     * 显示审核弹窗
     * @param {*} type 1通过  2驳回
     */
    showAuditModel(type) {
      let that = this
      if (this.$refs.auditRemarkModal) {
        let param = {
          ApprovalWorkFlowInstanceId: that.approvalModel ? that.approvalModel.ApprovalWorkFlowInstanceId : '',
          IsHasSuperior: that.approvalModel ? that.approvalModel.IsHasSuperior : false,
        }
        console.log('param', param)
        this.$refs.auditRemarkModal.show(type, param)
      }
    },
    /**
     * 审核弹窗回调
     */
    onAuditModalOk(formData) {
      if (formData) {
        this.$refs.auditRemarkModal.postAuditInstance(formData, (bool,code) => {
          setTimeout(() => {
            if(code === 6067){
              this.goBack(true,true)
              return
            }
            if (bool) {
              this.auditOK()
            }
          },1000);
        })
      } else {
        this.auditOK()
      }
    },
    auditOK() {
      if (this.model.NextId) {
        this.handleSwitch(2, this.model.NextId)
      } else {
        if (this.model.LastId) {
          this.handleSwitch(1, this.model.LastId)
        } else {
          this.goBack(true,true)
        }
      }
    },
  },
}
</script>
<style></style>
