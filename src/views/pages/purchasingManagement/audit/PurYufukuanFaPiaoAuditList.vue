<!--
 * @Author: LP
 * @Description: 采购预付款发票审核列表
 * @Date: 2023/07/11
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView ref="tableView" showCardBorder :tabDataList="tabDataList" :tab="tab" :columns="columns" :dataList="dataSource" @operateClick="onOperateClick" @actionClick="onActionClick">
      <span slot="InvoiceRecordsJson" slot-scope="{ text }">
        <div v-if="text">
          <div v-for="(item,index) in JSON.parse(text)" :key="index">
            <j-ellipsis :value="'发票号：'+(item.InvoiceNo || '--')+' '+'发票类型：'+(item.InvoiceTypeStr || '--')" :length="40" />
          </div>
        </div>
      </span>
      <div style="margin-top:5px;" slot="bottomView">
        <span style="margin-right: 15px;">合计发票金额（不含税）：¥{{countObj.TotalInvoiceAmount || 0}}</span>
        <span style="margin-right: 15px;">合计税额：¥{{countObj.TotalTaxAmount || 0}}</span>
        <span style="margin-right: 15px;">含税金额：¥{{countObj.TotalTaxIncludedAmount || 0}}</span>
      </div>
    </TableView>
  </div>
</template>
<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import { getAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'PurYufukuanFaPiaoAuditList',
  mixins: [ListMixin],
  components: { JEllipsis },
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierName',
        },
        {
          name: '上传发票单号',
          type: SEnum.INPUT,
          key: 'RemittedOrderNo',
        },
        {
          name: '发票号',
          type: SEnum.INPUT,
          key: 'InvoiceNo',
        },
        {
          name: '订单编号',
          type: SEnum.INPUT,
          key: 'OrderNo',
        },

        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
        // {
        //   name: '审核时间',
        //   type: SEnum.DATE,
        //   rangeDate: this.rangeDate,
        //   key: 'AuditorTime',
        // }
      ],
      tabDataList: [
        {
          name: '待审核',
          value: '1',
          count: '0',
          key: 'WaitAuditCount',
        },
        {
          name: '审核记录',
          value: '2',
          count: '0',
          key: 'AuditRecordCount',
        },
      ],
      tab: {
        curStatus: '1',
        tabStatusKey: 'ApproavalStatus',
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        rowKey: 'Id',
        customSlot: ['InvoiceRecordsJson'],
      },
      countObj: {},
      httpHead: 'P36009',
      url: {
        list: '/{v}/PurchaseRemittedOrder/GetPurchaseSubsistInvoiceAuditList',
        tabCount: '/{v}/PurchaseRemittedOrder/GetPurchaseSubsistInvoiceAuditCount',
        tabIsList: false,
        setValidUrl: '',
        // delSingleOccupy: '/{v}/PurchaseRemittedOrder/AuditOccupyAsync',
        // singleOccupy: '/{v}/PurchaseRemittedOrder/AuditOccupyAsync',
      },
    }
  },
  computed: {
    columns() {
      let auditStatusList = this.tab.curStatus == '2'
          ? [
            {
              title: '审核状态',
              dataIndex: 'AuditStatusStr',
              width: 100,
              scopedSlots: { customRender: 'state' },
              ellipsis: true,
            },
            {
              title: '审核时间',
              dataIndex: 'AuditTime',
              width: 150,
              scopedSlots: { customRender: 'component' },
              ellipsis: true,
            },
          ]
          : []
      return [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 190,
        },
        {
          title: '上传发票单号',
          dataIndex: 'RemittedOrderNo',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 190,
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 120,
        },
        {
          title: '发票金额（不含税）',
          dataIndex: 'InvoiceAmount',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
          width: 150,
        },
        // {
        //   title: '发票总金额',
        //   dataIndex: 'TaxIncludedAmount',
        //   scopedSlots: { customRender: 'component' },
        //   ellipsis: true,
        //   width: 120,
        // },
        {
          title: '税额',
          dataIndex: 'TaxAmount',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
          width: 120,
        },
        {
          title: '含税金额',
          dataIndex: 'TaxIncludedAmount',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
          width: 120,
        },
        {
          title: '发票信息',
          dataIndex: 'InvoiceRecordsJson',
          scopedSlots: { customRender: 'InvoiceRecordsJson' },
          ellipsis: true,
          width: 380,
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
           width: 150,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
           width: 150,
        },
        ...auditStatusList,
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: 'c18856d5-f15e-4312-bf33-b32a44df9f8f',
              isShow: (record) => {
                return this.tab.curStatus == 2
              },
            },
            {
              name: '审核',
              id: 'e53e2ef2-9a51-4f6f-83bb-49e38ee5b538',
              disabled: (record) => {
                // return record.NowAuditBy?true:false
                return (record.NowAuditBy&&Vue.ls.get(USER_ID)&&record.NowAuditBy==Vue.ls.get(USER_ID))||!record.NowAuditBy ? false:true
              },
              isShow: (record) => {
                return this.tab.curStatus == 1
              },
            },
          ],
        },
      ]
    },
  },
  mounted() {
    this.setTitle('预付款发票审核列表')
  },
  created() { },

  methods: {
    onOperateClick(key) { },
 
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('PurYufukuanFaPiaoAuditPage', { id: record.Id, isAudit: 1 })
      } else if (key === '审核') {
        this.onDetailClick('PurYufukuanFaPiaoAuditPage', { id: record.Id, isAudit: 2 })
      }
    },

    loadTabDataAfter(data){
      this.countObj = data || {}
    }
  },
}
</script>
<style scoped>

</style>
