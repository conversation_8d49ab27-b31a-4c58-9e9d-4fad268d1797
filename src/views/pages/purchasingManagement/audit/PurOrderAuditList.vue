<!--
 * @Author: LP
 * @Description: 采购订单审核列表
 * @Date: 2023/07/11
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="curSearchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
      @onTabChange="onTabChange"
    />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PurOrderAuditList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierName',
        },
        {
          name: '企业类别',
          type: SEnum.ENUM,
          key: 'SupplierType',
          dictCode: 'EnumSupplierType',
        },
        {
          name: '订单编号',
          type: SEnum.INPUT,
          key: 'PurchaseOrderNo',
        },
        {
          name: '付款方式',
          type: SEnum.ENUM,
          key: 'PaymentMode',
          dictCode: 'EnumPurchasePaymentMode',
        },

        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '下单时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
      ],
      curSearchItems: [],
      tabDataList: [
        {
          name: '待审核',
          value: '1',
          count: '',
          key: 'WaitAuditCount',
        },
        {
          name: '审核记录',
          value: '2',
          count: '',
          key: 'AuditRecordCount',
        },
      ],
      tab: {
        curStatus: '1',
        tabStatusKey: 'ApproavalStatus',
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        // rowKey: 'Id'
      },
      httpHead: 'P36009',
      url: {
        list: '/{v}/PurchaseOrder/GetPurchaseOrderAuditList',
        tabCount: '/{v}/PurchaseOrder/GetPurchaseOrderAuditCount',
        tabIsList: false,
        setValidUrl: '',
      },
    }
  },
  computed: {
    columns() {
      let auditStatusList =
        this.tab.curStatus == '2'
          ? [
              {
                title: '审核状态',
                dataIndex: 'AuditStatusStr',
                width: 100,
                scopedSlots: { customRender: 'state' },
                ellipsis: true,
              },
              {
                title: '审核时间',
                dataIndex: 'ApprovalTime',
                width: 180,
                scopedSlots: { customRender: 'component' },
                ellipsis: true,
              },
            ]
          : []
      return [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '企业类别',
          dataIndex: 'SupplierTypeStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '订单金额',
          dataIndex: 'OrderAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '下单时间',
          dataIndex: 'CreateTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        ...auditStatusList,
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: '33f1c1f5-1cd7-4ab0-ac4c-f39c5419dae2',
              isShow: (record) => {
                return this.tab.curStatus == '2'
              },
            },
            {
              name: '审核',
              id: 'ee2fd8ca-a00f-4fd5-97e6-16b19108ad72',
              isShow: (record) => {
                return this.tab.curStatus == '1'
              },
            },
          ],
        },
      ]
    },
  },
  created() {
    this.curSearchItems = this.searchItems
  },
  methods: {
    onOperateClick(key) {},
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('PurOrderAuditPage', { id: record.Id, type: 1 })
      } else if (key === '审核') {
        this.onDetailClick('PurOrderAuditPage', { id: record.Id, type: 2 })
      }
    },
    onTabChange(curStatus) {
      if (curStatus == '2') {
        this.curSearchItems = [
          ...this.searchItems,
          {
            name: '审核时间',
            type: SEnum.DATE,
            rangeDate: this.rangeDate1,
            key: 'ApprovalTime',
          },
        ]
      } else {
        this.curSearchItems = this.searchItems
        this.$refs.searchView.queryParam.ApprovalTimeBegin = null
        this.$refs.searchView.queryParam.ApprovalTimeEnd = null
      }
    },
  },
}
</script>
<style scoped></style>
