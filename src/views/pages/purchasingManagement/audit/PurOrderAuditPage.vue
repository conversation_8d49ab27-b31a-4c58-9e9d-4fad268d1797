<!--
 * @Author: LP
 * @Description: 采购订单审核详情
 * @Date: 2023/07/07
-->
<template>
  <a-spin :spinning="loading">
    <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }" v-if="model">
      <a-descriptions :column="4">
        <a-descriptions-item>
          <div class="flc hcenter">
            <a-icon :type="getAuditIcon(model.AuditStatus)" theme="twoTone" :two-tone-color="getAuditColor(model.AuditStatus)" style="font-size: 32px" />
            <span :style="{ color: getAuditColor(model.AuditStatus) }">{{ model.AuditStatusStr || '--' }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">订单金额</span>
          <div>¥{{ model.OrderAmount }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">订单编号</span>
          <div>{{ model.PurchaseOrderNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">下单时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
    <a-card :bodyStyle="{ padding: '16px 16px' }" class="mt15">
      <a-row>
        <a-descriptions :column="3">
          <a-descriptions-item>
            <span class="c999">供应商名称</span>
            <div>{{ model.SupplierName || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">委托人</span>
            <div>{{ model.DelegateScopeName || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">付款方式</span>
            <div>{{ model.PaymentModeStr || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">送货方式</span>
            <div>{{ model.DeliveryMethodStr || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">预到货日期</span>
            <div>{{ model.ExpectedArrivalDate || '--' }}</div>
          </a-descriptions-item>
          <a-descriptions-item>
            <span class="c999">备注</span>
            <div>{{ model.Remarks || '--' }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </a-row>
      <a-row class="mt20">
        <a-col span="24"><span style="font-size: 16px; font-weight: 600">商品信息</span></a-col>
        <a-col span="24" class="mt5">
          <TableView ref="tableView" :showCardBorder="false" :tab="tab" :columns="columns" @actionClick="actionClick" :dataList="model.PurchaseOrderDetailInfos || []">
            <span slot="clickA" slot-scope="{ text, record,index, column}">
              <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
                <j-ellipsis :value="'' + text" :length="50" />
              </a>
              <span v-else>--</span>
            </span>
            <!-- 全部点击操作 -->
            <span slot="clickAl" slot-scope="{text, record, index, column}">
              <a v-if="record.ErpGoodsId" @click="clickA(text, record, index, column)">
                <j-ellipsis :value="(text || text == 0)?('' + text):'--'" :length="50" />
              </a>
              <span v-else>--</span>
            </span>
            <!-- 价格点击操作 -->
            <span slot="clickPriceA" slot-scope="{ text, record,index, column}">
              <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
                <j-ellipsis :value="text == 0?(''+text):('¥' + text)" :length="50" />
              </a>
              <span v-else>--</span>
            </span>
            <span slot="TaxIncludedPurchasePrice" slot-scope="{ text, record }">
              <span v-if="text || text == 0" :style="{color:text>record['LastPurchasePrice']?'#ff0000':'#00000'}">
                <j-ellipsis :value="text == 0?(''+text):('¥' + text)" :length="50" />
              </span>
              <span v-else>--</span>
            </span>
          </TableView>
        </a-col>
      </a-row>
      <!-- 比价表格 -->
      <ComparisonTable v-if="bjdataList.length > 0" :tab="bjTab" :isInfo="true" :dataList="bjdataList" :columns="bjColumns" :supplierQuotationChooseList.sync="supplierQuotationChooseList" :sign="'PurchaseQuotationDetailId'" :listKeyName="'GoodsPurchaseQuotationList'" :tableChooseList="tableChooseList"></ComparisonTable>

      <a-row v-if="model.Id">
        <a-col span="24"><span style="font-size: 16px; font-weight: 600">审核信息</span></a-col>
        <a-col span="24" class="mt5">
          <SupAuditInformation :bussinessNo="model.Id" />
        </a-col>
      </a-row>

      <!-- 底部区域 -->
      <a-affix :offset-bottom="10" class="affix">
        <a-divider />
        <div style="padding-bottom: 10px;">
          <a-button @click="goBack(true,true)" class="mr8">返回</a-button>
          <template v-if="opType == 2 && showBtn">
            <YYLButton menuId="897e8686-22e6-45b1-b584-dafed8cc4c53" text="审核通过" type="primary" :loading="confirmLoading" @click="onPassClick" />
            <YYLButton menuId="fe0b3fef-cb81-4d45-9829-18ad4420f524" text="审核驳回" type="danger" :loading="confirmLoading" @click="onRejectClick" />
          </template>
        </div>
      </a-affix>
    </a-card>
    <!-- 审核弹框 -->
    <AuditRemarkModal ref="auditRemarkModal" @ok="onAuditModalOk" />
    <!-- 历史采购价 -->
    <HistoricalPurchasePriceModal ref="HistoricalPurchasePriceModal" />
    <!-- 平均参考成本 -->
    <AverageReferenceCostModal ref="AverageReferenceCostModal" />
    <!-- 政策底价 -->
    <PolicyBasePriceModal ref="PolicyBasePriceModal" />
    <!-- 库存明细 -->
    <InventoryDetailModal ref="InventoryDetailModal" />
    <!-- 本月销售明细 -->
    <SalesThisMonthModal ref="SalesThisMonthModal" />
    <!-- 在途库存 -->
    <PlanTotalInTransitCountModal ref="PlanTotalInTransitCountModal" />

  </a-spin>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'PurOrderAuditPage',
  mixins: [EditMixin, ListMixin],
  components: { JEllipsis },
  data() {
    return {
      model: {},
      loading: false,
      confirmLoading: false,
      orderId: '', //采购订单id
      opType: 1, //1详情  2审核
      httpHead: 'P36009',
      // 报价信息
      bjTab: {
        rowKey: 'Id',
      },
      // 比价表格列名
      bjColumns: [
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          align: 'center',
          width: 120,
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          align: 'center',
          scopedSlots: { customRender: 'component' },
          width: 230,
        },
        {
          title: '规格',
          ellipsis: true,
          width: 120,
          align: 'center',
          dataIndex: 'PackingSpecification',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          ellipsis: true,
          align: 'center',
          width: 200,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批准文号',
          ellipsis: true,
          width: 120,
          align: 'center',
          dataIndex: 'ApprovalNumber',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '平均参考成本',
          width: 120,
          align: 'center',
          dataIndex: 'AverageCost',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '末次采购价',
          width: 120,
          align: 'center',
          dataIndex: 'LastPurchasePrice',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '供应商报价',
          width: 200,
          align: 'center',
          dataIndex: 'list',
          scopedSlots: { customRender: 'priceList' },
        },
      ],
      bjdataList: [

      ],
      bjdataListData: [],
      supplierQuotationChooseList: [], // 供应商报价勾选集合ids
      tableChooseList: [], // 表格勾选需导出集合ids

      tab: {
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        rowKey: 'Id',
        customSlot: ['clickA', 'clickAl', 'clickPriceA', 'TaxIncludedPurchasePrice'],
        filterColumns: true,//是否筛选columns 加这属性是为了提高性能 有再筛选
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'name' },
          width: 150,
          fixed: 'left',
          ellipsis: true,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 100,
          ellipsis: true,
        },
        {
          title: '商品ABC',
          dataIndex: 'GoodsABCLabel',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'GoodsOwnerBy',
          ellipsis: true,
          align: 'center',
          width: 200,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          scopedSlots: { customRender: 'component' },
          width: 60,
          ellipsis: true,
        },
        {
          title: '采购数量',
          dataIndex: 'PurchaseQuantity',
          scopedSlots: { customRender: 'component' },
          width: 80,
          ellipsis: true,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          scopedSlots: { customRender: 'TaxIncludedPurchasePrice' },
          width: 90,
          precision: 6,
          ellipsis: true,
        },
        {
          title: '含税金额',
          dataIndex: 'TaxIncludedPurchasePriceTotal',
          scopedSlots: { customRender: 'price' },
          width: 80,
          precision: 2,
          ellipsis: true,
        },
        {
          title: '末次采购价',
          dataIndex: 'LastPurchasePrice',
          width: 100,
          scopedSlots: { customRender: 'clickPriceA' },
          ellipsis: true,
        },
        {
          title: '平台原价',
          dataIndex: 'NationalSupplyPrice',
          width: 80,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '活动类型',
          dataIndex: 'ActivityType',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '活动价格',
          dataIndex: 'ActivityPrice',
          width: 80,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '可销总库存',
          dataIndex: 'AvailableInventory',
          scopedSlots: { customRender: 'clickA' },
          width: 90,
          ellipsis: true,
        },
        {
          title: '预计可销时间',
          dataIndex: 'EstimatedSaleTime',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '上月销量',
          dataIndex: 'LastMonthCount',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '本月销量',
          dataIndex: 'ThisMonthCount',
          width: 80,
          scopedSlots: { customRender: 'clickAl' },
          ellipsis: true,
        },
        {
          title: '在途库存',
          dataIndex: 'InTransitInventory',
          scopedSlots: { customRender: 'clickA' },
          width: 80,
          ellipsis: true,
        },
        {
          title: '备注',
          dataIndex: 'Remarks',
          scopedSlots: { customRender: 'component' },
          width: 80,
          maxLength: 50,
          maxLength: 100,
          // fixed: 'right',
          ellipsis: true,
        },
        {
          title: '近效期',
          dataIndex: 'IsNearExpiry',
          scopedSlots: { customRender: 'bool' },
          width: 80,
          // fixed: 'right',
          ellipsis: true,
        },
        {
          title: '返利标记',
          dataIndex: 'PurchaseRebateFlagStr',
          scopedSlots: { customRender: 'component' },
          width: 80,
          // fixed: 'right',
          ellipsis: true,
        },
        {
          title: '每盒返利金额',
          dataIndex: 'BoxRebateAmount',
          width: 100,
          scopedSlots: { customRender: 'component' },
          // fixed: 'right',
          precision: 6,
          ellipsis: true,
        },
        {
          title: '返利到账天数',
          dataIndex: 'RebateDays',
          width: 100,
          scopedSlots: { customRender: 'component' },
          // fixed: 'right',
          ellipsis: true,
        },
        {
          title: '含购进的移动平均成本', // TODO: 接口字段
          dataIndex: 'AverageCost',
          width: 160,
          scopedSlots: { customRender: 'clickPriceA' },
          ellipsis: true,
        },
        {
          title: '销售原价',
          dataIndex: 'OriginalPrice',
          scopedSlots: { customRender: 'component' },
          width: 80,
          precision: 6,
          // fixed: 'right',
          ellipsis: true,
        },
        {
          title: '商销底价',
          dataIndex: 'MinimumSellingPrice',
          width: 80,
          precision: 6,
          scopedSlots: { customRender: 'component' },
          // fixed: 'right',
          ellipsis: true,
        },
        {
          title: '仓库',
          dataIndex: 'StorehouseStr',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          // fixed: 'right',
          keyStr: 'StorehouseStr',
        },
        {
          title: '税率',
          dataIndex: 'PurchaseTaxRate',
          width: 60,
          scopedSlots: { customRender: 'percent' },
          ellipsis: true,
        },
        {
          title: '产地',
          dataIndex: 'Producer',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        // {
        //   title: '政策返货',
        //   dataIndex: 'IsPolicyReturn',
        //   width: 80,
        //   // fixed: 'right',
        //   scopedSlots: { customRender: 'bool' },
        //   ellipsis: true,
        // },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          isShow: (item) => {
            if (this.model.OrderSource && this.model.OrderSource == 2) {
              return true
            } else {
              return false
            }
          },
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '查看比价',
              id: 'f7d60f01-9d65-4334-9abf-439538bcbd00',
              isShow: (record) => {
                return !record.isCancelBj
              },
            },
            {
              name: '取消查看比价',
              id: 'c47f7a69-9f22-47cf-9da3-659f0ecbf843',
              isShow: (record) => {
                return record.isCancelBj
              },
            },
          ],
        },

      ],
      showBtn: false,
      approvalModel: null,
      url: {
        approvalUrl: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
        detail: '/{v}/PurchaseOrder/GetPurchaseOrderRecordInfo',
        check: '/{v}/PurchaseOrder/VerifyPurchaseOrderSubmit',
        lookComparePrice: '/{v}/PurchaseInquiryOrder/LookComparePrice',//查看比价详情
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.orderId = this.$route.query.id || ''
      this.opType = Number(this.$route.query.type) || 1
      this.setTitle('采购订单审核' + (this.opType == 1 ? '详情' : ''))
      this.loadDetail()
      if (this.opType == 2) {
        this.getApprovalResults()
      }
    }
  },
  created() { },
  methods: {
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.orderId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    actionClick(record, key, index) {
      if (key == '查看比价') {
        this.$set(record, 'isCancelBj', true)
        this.getBjList(record)
      } else if (key == '取消查看比价') {
        let haveDataIndex = this.bjdataListData.findIndex(item => {
          return item.recordId == record.GoodsSpuId
        })
        if (haveDataIndex > -1) {
          this.bjdataListData.splice(haveDataIndex, 1)
        }
        this.bjdataList = this.setBjdataList(record) || []
        console.log(this.bjdataListData, this.bjdataList)
        this.$set(record, 'isCancelBj', false)
      }
    },
    getBjList(record) {
      let params = {
        GoodsSpuId: record.GoodsSpuId,
        PurchaseOrderNo: this.model.PurchaseOrderNo,
      }
      this.loading = true
      postAction(this.url.lookComparePrice, params, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (!res.Data || res.Data.length === 0) {
              res.Data = [{
                GoodsPurchaseQuotationList: []
              }]
            }
            let haveData = this.bjdataListData.find(item => {
              return item.id == record.GoodsSpuId
            })
            if (!haveData) {
              this.bjdataListData.push({
                recordId: record.GoodsSpuId,
                data: res.Data || []
              })
            }
            this.bjdataList = this.setBjdataList(record) || []
            console.log(this.bjdataListData, this.bjdataList)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    setBjdataList(record) {
      let array = []
      this.bjdataListData.map(item => {
        item.data.map(rItem => {
          array.push({
            recordId: record.GoodsSpuId,
            ...rItem
          })
        })
      })
      return array
    },
    /**
     * 获取审批记录
     */
    getApprovalResults() {
      if (!this.model) {
        return
      }
      this.loading = true
      getAction(this.url.approvalUrl, { bussinessNo: this.orderId }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            this.approvalModel = res.Data || {}

            this.checkLoginUserIsHasBtnPerssion(this.approvalModel, (bool) => {
              this.showBtn = bool
            })
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },

    /**
     * 通过
     */
    onPassClick() {
      let that = this
      that.loading = true
      postAction(that.url.check, { Id: that.orderId, AuditStatus: 2 }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.showAuditModel(1)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 驳回
     */
    onRejectClick() {
      this.showAuditModel(2)
    },

    /**
     * 显示审核弹窗
     * @param {*} type 1通过  2驳回
     */
    showAuditModel(type) {
      let that = this
      if (this.$refs.auditRemarkModal) {
        let param = {
          ApprovalWorkFlowInstanceId: that.approvalModel ? that.approvalModel.ApprovalWorkFlowInstanceId : '',
          IsHasSuperior: that.approvalModel ? that.approvalModel.IsHasSuperior : false,
        }
        console.log('param', param)
        this.$refs.auditRemarkModal.show(type, param)
      }
    },
    /**
     * 审核弹窗回调
     */
    onAuditModalOk(formData) {
      if (formData) {
        this.$refs.auditRemarkModal.postAuditInstance(formData, (bool, code) => {
          setTimeout(() => {
            if (code === 6067) {
              this.goBack(true,true)
              return
            }
            if (bool) {
              this.goBack(true,true)
            }
          }, 1000);

        })
      } else {
        this.goBack(true,true)
      }
    },
    clickA(text, record, index, column) {
      let key = column.dataIndex
      // 可销总库存
      if (key == 'AvailableInventory') {
        this.$refs.InventoryDetailModal.show(record)
      } else if (key == 'ThisMonthCount') {
        // 本月销量
        this.$refs.SalesThisMonthModal.show(record)
      } else if (key == 'LastPurchasePrice') {
        // 末次进价
        this.$refs.HistoricalPurchasePriceModal.show(record)
      } else if (key == 'AverageCost' || key == 'LatestAverageCost') {
        // 含购进的移动平均成本
        this.$refs.PolicyBasePriceModal.show(record)
        // this.$refs.AverageReferenceCostModal.show(record)
      } else if (key == 'InTransitInventory') {
        // 在途库存
        this.$refs.PlanTotalInTransitCountModal.show(record)
      }
    },
  },
}
</script>

<style scoped>

</style>
