<!--
 * @Author: LP
 * @Description: 采购预付款审核和详情界面
 * @Date: 2023/08/04
-->

<template>
  <div>
    <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }" v-if="model">
      <a-descriptions :column="4">
        <a-descriptions-item>
          <div class="flc hcenter">
            <a-icon
              :type="getAuditIcon(model.AuditStatus)"
              theme="twoTone"
              :two-tone-color="getAuditColor(model.AuditStatus)"
              style="font-size: 32px"
            />
            <span :style="{ color: getAuditColor(model.AuditStatus) }">{{ model.AuditStatusStr || '--' }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">供应商名称</span>
          <div>{{ model.SupplierName }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建人</span>
          <div>{{ model.CreateByName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
    <a-card :bodyStyle="{ padding: '0 0 10px 0' }" :bordered="false" class="mt15">
      <AdvancePaymentInvoiceInfo
        ref="advancePaymentInvoiceInfo"
        :isEdit="false"
        :model="model"
        :cLoading="confirmLoading"
      />
      <a-affix :offset-bottom="10" class="affix">
        <div class="pd1016">
          <template v-if="isAudit && showBtn">
            <YYLButton
              menuId="20d7e830-1a3d-4c98-94d1-94cd2d43bf13"
              text="通过审核"
              type="primary"
              :loading="confirnLoading"
              @click="onPassClick"
            />
            <YYLButton
              menuId="cfd508b2-3151-40e3-a834-792d9df91dec"
              text="驳回审核"
              type="danger"
              :loading="confirnLoading"
              @click="onRejectClick"
            />
          </template>
          <a-button @click="auditOK(true)">返回</a-button>
        </div>
      </a-affix>
      <!-- 审核弹框 -->
      <AuditRemarkModal ref="auditRemarkModal" @ok="onAuditModalOk" />
    </a-card>
  </div>
</template>
<script>
import { getAction ,postAction} from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'PurYufukuanFaPiaoAuditPage',
  mixins: [EditMixin, ListMixin],
  components: {},
  data() {
    return {
      confirmLoading: false,
      id: '',
      loading: false,
      isAudit: false, //是否是审核 false则表示是详情
      model: {},
      showBtn: false,
      confirnLoading: false,
      approvalModel: null,
      httpHead: 'P36009',
      url: {
        detail: '/{v}/PurchaseRemittedOrder/GetPurchaseSubsistInvoiceInfoRecord',
        approval: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
        delSingleOccupy: '/{v}/PurchaseRemittedOrder/DeleteAuditOccupyAsync',// 删除单个占用
        singleOccupy: '/{v}/PurchaseRemittedOrder/AuditOccupyAsync',//单个占用
      },
    }
  },
  computed: {},
  created() {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      if (this.$route.query.isAudit) {
        this.isAudit = this.$route.query.isAudit == 2
      }
      this.setTitle('采购预付款发票审核' + (this.isAudit ? '' : '详情'))
      this.getDetailInfo()
    }
  },

  methods: {
    /**
     * @description: 获取详情
     * @param {*}
     * @return {*}
     */
    getDetailInfo() {
      if (!this.id) {
        return
      }
      if (this.model) {
        this.model = null
      }
      let that = this
      this.loading = true
      getAction(this.url.detail, { id: this.id }, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            if (that.isAudit) {
              if (that.model.NowAuditBy && that.model.NowAuditBy != that.getLoginUserId()) {
                that.$message.warning('该笔发票正在由' + (that.model.NowAuditByName || '') + '审核')
                that.auditOK()
                return
              }
              that.getApprovalResults()
              // 单个占用
              that.singleOccupyFun()
            }
            that.getGroupList()
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 单个占用
    singleOccupyFun() {
      if (!this.model.Id) {
        return
      }
      postAction(this.url.singleOccupy + '?id=' + this.model.Id, {}, this.httpHead).then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        } else {
          // this.$message.success("占用成功");
        }
      })
    },
    /**
     * 获取审批记录
     */
    getApprovalResults() {
      if (!this.model) {
        return
      }
      this.loading = true
      getAction(this.url.approval, { bussinessNo: this.model.Id }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            this.approvalModel = res.Data || {}

            this.checkLoginUserIsHasBtnPerssion(this.approvalModel, (bool) => {
              this.showBtn = bool
            })
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },
    /**
     * 通过
     */
    onPassClick() {
      this.showAuditModel(1)
    },
    /**
     * 驳回
     */
    onRejectClick() {
      this.showAuditModel(2)
    },

    /**
     * 显示审核弹窗
     * @param {*} type 1通过  2驳回
     */
    showAuditModel(type) {
      let that = this
      if (this.$refs.auditRemarkModal) {
        let param = {
          ApprovalWorkFlowInstanceId: that.approvalModel ? that.approvalModel.ApprovalWorkFlowInstanceId : '',
          IsHasSuperior: that.approvalModel ? that.approvalModel.IsHasSuperior : false,
        }
        console.log('param', param)
        this.$refs.auditRemarkModal.show(type, param)
      }
    },
    /**
     * 审核弹窗回调
     */
    onAuditModalOk(formData) {
      if (formData) {
        this.$refs.auditRemarkModal.postAuditInstance(formData, (bool) => {
          if (bool) {
            this.auditOK()
          }
        })
      } else {
        this.auditOK()
      }
    },
    auditOK(isBool) {
      // this.delSingleOccupyFun()
      let that = this
      // 返回按钮 提示
      if(isBool && this.isAudit){
        that.$confirm({
          title: '提示',
          content: '是否释放占用审核?',
          okText: '是',
          cancelText: '否',
          onOk: function () {
            that.delSingleOccupyFun() // 删除单个占用
            that.goBack(true,true)
          },
          onCancel:function(){
            that.goBack(true,true)
          }
        })
        return
      }
      that.goBack(true,true)
    },
    // 删除单个占用
    delSingleOccupyFun() {
      if (!this.model.Id) {
        return
      }
      let url = this.url.delSingleOccupy+'?id=' + this.model.Id
      postAction(url, {}, this.httpHead).then(res => {
        if (res.IsSuccess && res.Data) {
          if (!res.IsSuccess) {
            this.$message.warning(res.Msg)
            return
          } else {
            // this.$message.success("删除占用成功");
          }
        }
      })
    },
  },
}
</script>
<style scoped>

</style>
