<!--
 * @Author: LP
 * @Description: 采购付款审核列表
 * @Date: 2023/07/11
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
    />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PurPaymentAuditList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierName',
        },
        {
          name: '付款单号',
          type: SEnum.INPUT,
          key: 'RemittedOrderNo',
        },
        {
          name: '付款方式',
          type: SEnum.ENUM,
          key: 'PaymentMode',
          dictCode: 'EnumPurchasePaymentMode',
        },
        {
          name: '结算方式',
          type: SEnum.ENUM,
          key: 'SettlementType',
          dictCode: 'EnumPurchaseSettlementType',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
        {
          name: '审核时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'AuditTime',
        },
      ],
      tabDataList: [
        {
          name: '待审核',
          value: '1',
          count: '',
          key: 'WaitAuditCount',
        },
        {
          name: '审核记录',
          value: '2',
          count: '',
          key: 'AuditRecordCount',
        },
      ],
      tab: {
        curStatus: '1',
        tabStatusKey: 'ApproavalStatus',
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        rowKey: 'Id',
      },
      httpHead: 'P36009',
      url: {
        list: '/{v}/PurchaseRemittedOrder/GetPurchaseRemittedAuditList',
        tabCount: '/{v}/PurchaseRemittedOrder/GetPurchaseRemittedAuditCount',
        tabIsList: false,
        setValidUrl: '',
      },
    }
  },
  computed: {
    columns() {
      let auditStatusList =
        this.tab.curStatus == 2
          ? [
              {
                title: '审核状态',
                dataIndex: 'AuditStatusStr',
                width: 100,
                scopedSlots: { customRender: 'state' },
                ellipsis: true,
              },
              {
                title: '审核时间',
                dataIndex: 'AuditTime',
                width: 150,
                scopedSlots: { customRender: 'component' },
                ellipsis: true,
              },
              {
                title: '审核意见',
                dataIndex: 'AuditOpinion',
                width: 200,
                scopedSlots: { customRender: 'component' },
                ellipsis: true,
              },
            ]
          : []
      return [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '付款单号',
          dataIndex: 'RemittedOrderNo',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '付款金额',
          dataIndex: 'ActualPaymentAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '结算方式',
          dataIndex: 'SettlementTypeStr',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        ...auditStatusList,
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: '3a060bd1-e0d2-4c6c-9db8-c55ddf01af1f',
              isShow: (record) => {
                return this.tab.curStatus == 2
              },
            },
            {
              name: '审核',
              id: 'd33bb470-6fc6-453c-a6a8-8875099bf4e6',
              isShow: (record) => {
                return this.tab.curStatus == 1
              },
            },
          ],
        },
      ]
    },
  },
  created() {},

  methods: {
    onOperateClick(key) {},
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('PurPaymentAuditPage', { id: record.Id, opType: 1, isAudit: 1 })
      } else if (key === '审核') {
        this.onDetailClick('PurPaymentAuditPage', { id: record.Id, opType: 1, isAudit: 2 })
      }
    },
  },
}
</script>
<style scoped>

</style>
