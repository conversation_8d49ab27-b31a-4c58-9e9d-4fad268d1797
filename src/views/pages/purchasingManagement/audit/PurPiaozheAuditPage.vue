<!--
 * @Author: LP
 * @Description: 采购票折审核详情
 * @Date: 2023/07/07
-->
<template>
  <a-spin :spinning="loading">
    <a-card :bodyStyle="{ padding: '16px 16px 0 16px' }" v-if="model">
      <a-descriptions :column="7">
        <a-descriptions-item>
          <div class="flc hcenter">
            <a-icon
              :type="getAuditIcon(model.AuditStatus)"
              theme="twoTone"
              :two-tone-color="getAuditColor(model.AuditStatus)"
              style="font-size: 32px"
            />
            <span :style="{ color: getAuditColor(model.AuditStatus) }">{{ model.AuditStatusStr || '--' }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">供应商名称</span>
          <div>{{ model.SupplierName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">票折单号</span>
          <div>{{ model.InvoiceDiscountNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">订单编号</span>
          <div>{{ model.PurchaseOrderNo || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">票折金额</span>
          <div>¥{{ model.TotalPriceAdjustment }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建人</span>
          <div>{{ model.CreateByName || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>
    <a-card :bodyStyle="{ padding: '16px 16px' }" class="mt15">
      <a-row>
        <!-- <a-col span="24"><span style="font-size: 16px; font-weight: 600;">商品信息</span></a-col> -->
        <a-col span="24">
          <TableView
            ref="tableView"
            :showCardBorder="false"
            :tab="tab"
            :columns="columns"
            :dataList="model.GoodsItems || []"
          />
        </a-col>
        <a-col span="24">
          <span class="c999">备注：</span><span>{{ model.Remark || '--' }}</span>
        </a-col>
      </a-row>
      <a-row class="mt10">
        <a-col span="24">
          <span>相关协议</span>
        </a-col>
        <a-col span="24">
          <TableView
            :showCardBorder="false"
            :tab="tab"
            :columns="agreementColumns"
            :dataList="model.AgreementItems || []"
          >
          </TableView>
        </a-col>
      </a-row>
      <a-row class="mt20">
        <a-col span="24"><span style="font-size: 16px; font-weight: 600">审核信息</span></a-col>
        <a-col span="24" class="mt10" v-if="opType == 2 ? approvalModel : orderId">
          <SupAuditInformation
            :aList="approvalModel ? approvalModel.ApprovalWorkFlowInstanceResults : []"
            :bussinessNo="orderId"
          />
        </a-col>
      </a-row>

      <!-- 底部区域 -->
      <a-affix :offset-bottom="10" class="affix">
        <a-divider />
        <div>
          <a-button @click="goBack(true,true)" class="mr8">返回</a-button>
          <template v-if="opType == 2 && showBtn">
            <YYLButton
              menuId="50f774c8-2466-4b1c-81b7-155f513a1188"
              text="审核通过"
              type="primary"
              :loading="confirmLoading"
              @click="onPassClick"
            />
            <YYLButton
              menuId="97ffa4b1-65c9-4bc6-a429-386f5e4d9edd"
              text="审核驳回"
              type="danger"
              :loading="confirmLoading"
              @click="onRejectClick"
            />
          </template>
        </div>
      </a-affix>
    </a-card>
    <!-- 审核弹框 -->
    <AuditRemarkModal ref="auditRemarkModal" @ok="onAuditModalOk" />
  </a-spin>
</template>

<script>
import { getAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'PurPiaozheAuditPage',
  mixins: [EditMixin, ListMixin],
  data() {
    return {
      model: {},
      loading: false,
      confirmLoading: false,
      orderId: '', //采购订单id
      opType: 1, //1详情  2审核
      httpHead: 'P36009',
      tab: {
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        rowKey: 'GoodsSpuId',
      },
      showBtn: false,
      approvalModel: null,
      url: {
        approvalUrl: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
        detail: '/{v}/PurchaseBusiness/QueryPurchaseInvoiceDiscountAuditDetail',
      },
    }
  },
  computed: {
    columns() {
      const list1 =
        this.opType == 2
          ? [
              {
                title: '票折数量',
                dataIndex: 'InvoiceDiscount',
                scopedSlots: { customRender: 'number' },
                width: 100,
                ellipsis: true,
              },
            ]
          : [
              {
                title: '已售数量',
                dataIndex: 'SoldQuantity',
                scopedSlots: { customRender: 'number' },
                width: 100,

                ellipsis: true,
              },
              {
                title: '未售数量',
                dataIndex: 'UnsoldQuantity',
                scopedSlots: { customRender: 'number' },
                width: 100,
                ellipsis: true,
              },
              {
                title: '票折数量',
                dataIndex: 'InvoiceDiscount',
                scopedSlots: { customRender: 'number' },
                width: 100,
                ellipsis: true,
              },
            ]
      return [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          width: 150,
          ellipsis: true,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 150,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          scopedSlots: { customRender: 'component' },
          width: 100,
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '有效期',
          dataIndex: 'ExpirationDate',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '含税金额',
          dataIndex: 'TotalAdjustedTaxIncludedPurchasePrice',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '入库数量',
          dataIndex: 'InboundQuantity',
          scopedSlots: { customRender: 'number' },
          width: 100,
          ellipsis: true,
        },
        ...list1,
        // {
        //   title: '新含税进价',
        //   dataIndex: 'DiscountedTaxIncludedPurchasePrice',
        //   scopedSlots: { customRender: 'price' },
        //   width: 150,
        //   precision: 6,
        //   ellipsis: true,
        // },
        
        {
          title: '票折小计金额',//(this.opType == 2 ? '含税进价' : '') + '差价小计',
          dataIndex: 'TotalTaxIncludedPurchasePriceDifference',
          scopedSlots: { customRender: 'price' },
          width: 180,
          ellipsis: true,
        },
        {
          title: '差额',
          dataIndex: 'TaxIncludedPurchasePriceDifference',
          scopedSlots: { customRender: 'price' },
          width: 120,
          ellipsis: true,
        }
      ]
    },
    agreementColumns() {
      return [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方类型',
          dataIndex: 'PartyFirstTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收返利',
          dataIndex: 'TotalRebateAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '取消返利',
          dataIndex: 'CancelRebateAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '增加返利',
          dataIndex: 'TotalAddRebateAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '已认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '抵扣金额',
          dataIndex: 'DeductionAmount',
          width: 130,
          precision: 2,
          min: 0,
          // scopedSlots: { customRender: 'price' },
          onBlur: this.handleOnBlur,
          scopedSlots:  { customRender: this.isEdit ? 'inputNumber' : 'price' },
          ellipsis: true,
        },
      ]
    } 
  },
  mounted() {
    if (this.$route.query) {
      this.orderId = this.$route.query.id || ''
      this.opType = Number(this.$route.query.type) || 1
      this.setTitle('采购票折审核' + (this.opType == 1 ? '详情' : ''))
      this.loadDetail()
      if (this.opType == 2) {
        this.getApprovalResults()
      }
    }
  },
  created() {},
  methods: {
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.orderId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 获取审批记录
     */
    getApprovalResults() {
      if (!this.model) {
        return
      }
      this.loading = true
      getAction(this.url.approvalUrl, { bussinessNo: this.orderId }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            this.approvalModel = res.Data || {}

            this.checkLoginUserIsHasBtnPerssion(this.approvalModel, (bool) => {
              this.showBtn = bool
            })
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },

    /**
     * 通过
     */
    onPassClick() {
      this.showAuditModel(1)
    },
    /**
     * 驳回
     */
    onRejectClick() {
      this.showAuditModel(2)
    },

    /**
     * 显示审核弹窗
     * @param {*} type 1通过  2驳回
     */
    showAuditModel(type) {
      let that = this
      if (this.$refs.auditRemarkModal) {
        let param = {
          ApprovalWorkFlowInstanceId: that.approvalModel ? that.approvalModel.ApprovalWorkFlowInstanceId : '',
          IsHasSuperior: that.approvalModel ? that.approvalModel.IsHasSuperior : false,
        }
        console.log('param', param)
        this.$refs.auditRemarkModal.show(type, param)
      }
    },
    /**
     * 审核弹窗回调
     */
    onAuditModalOk(formData) {
      if (formData) {
        this.$refs.auditRemarkModal.postAuditInstance(formData, (bool) => {
          if (bool) {
            this.goBack(true,true)
          }
        })
      } else {
        this.goBack(true,true)
      }
    },
  },
}
</script>

<style scoped>

</style>
