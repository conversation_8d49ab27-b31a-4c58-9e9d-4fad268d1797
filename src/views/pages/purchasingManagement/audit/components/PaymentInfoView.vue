<!--
 * @Author: LP
 * @Description: 审核-基础信息和开票信息
 * @Date: 2023/06/12
-->
<template>
  <a-spin :spinning="loading">
    <a-row :gutter="10" class="auditH">
      <a-col :span="24">
        <span style="font-size: 16px; font-weight: 600">基础信息</span>
      </a-col>
      <a-col :span="24">
        <a-descriptions :column="1" class="mt10">
          <a-descriptions-item label="供应商名称">{{ model.SupplierName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="总付款金额" v-if="model.PaymentMode == 1"
            >¥{{ model.TotalPaymentAmount || 0 }}</a-descriptions-item
          >
          <a-descriptions-item label="预付余款抵扣" v-if="model.PaymentMode == 1"
            >¥{{ model.PrepayDeductAmount || 0 }}</a-descriptions-item
          >
          <a-descriptions-item label="实际付款金额">¥{{ model.ActualPaymentAmount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="付款方式">{{ model.PaymentModeStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="结算方式">{{ model.SettlementTypeStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="创建人">{{ model.CreateByName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ model.CreateTime || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="备注">{{ model.Remarks || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item v-if="model.PaymentMode == 2 || model.PaymentMode == 3" label="本次付款包含的发票号">{{
            model.RemittedInvoiceNoStr || ' -- '
          }}</a-descriptions-item>
        </a-descriptions>
      </a-col>
      <a-col :span="24" class="mt10">
        <span style="font-size: 16px; font-weight: 600">账户信息</span>
      </a-col>
      <a-col :span="24">
        <TableView class="mt15" ref="tableViewA" :tab="tabA" :columns="columnsA" :dataList="dataSourceA" />
      </a-col>
      <a-col
        :span="24"
        class="mt10"
        style="padding-left: 8px; padding-right: 8px; display: flex; justify-content: space-between"
      >
        <!-- PaymentMode 1预付款 2月结 -->
        <span style="font-size: 16px; font-weight: 600; padding-right: 20px"
          >单据信息<span
            v-if="model.PaymentMode == 1 && model.IsAutoSettlement == true"
            style="color: red; padding-left: 10px"
            >(本次付款的所有商品，系统将自动结算)</span
          ></span
        >
        <!-- 0820 1.4需求注释 -->
        <!-- <a-button type="primary" size="small" v-if="model.PaymentMode == 2" @click="payArticulation">付款勾稽</a-button> -->
      </a-col>
      <a-col :span="24" style="margin-top: 10px">
        <TableView
          :showCardBorder="false"
          :tab="tab"
          :columns="model.PaymentMode == 2 || model.PaymentMode == 3 ? columns : columnsYFK"
          :dataList="model.PaymentMode == 1 ? model.RemittedDetails || [] : dataSource"
          @actionClick="onActionClick"
        >
          <!-- 价格点击操作 -->
          <span slot="clickPriceA" slot-scope="{ text, record, index, column }">
            <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
              <j-ellipsis :value="text == 0 ? '' + text : '¥' + text" :length="50" />
            </a>
            <span v-else>--</span>
          </span>
          <!-- 全部点击操作 -->
          <span slot="clickAl" slot-scope="{ text, record, index, column }">
            <a v-if="record.ErpGoodsId" @click="clickA(text, record, index, column)">
              <j-ellipsis :value="text || text == 0 ? '' + text : '--'" :length="50" />
            </a>
            <span v-else>--</span>
          </span>
        </TableView>
      </a-col>
      <template v-if="(model.PaymentMode == 2  || model.PaymentMode == 3) && (model.RemittedInvoiceDtos || []).length">
        <a-col :span="24" class="mt10">
          <span style="font-size: 16px; font-weight: 600; padding-right: 20px">发票信息</span>
        </a-col>
        <a-col :span="24">
          <a-descriptions :column="1" v-for="(item, index) in model.RemittedInvoiceDtos || []" :key="index">
            <template slot="title">
              <span style="padding-right: 15px">{{ '发票' + (index + 1) }}</span>
              <YYLButton
                v-if="model.PaymentMode == 2"
                menuId="cc367f62-d1bb-4c29-be03-38051acbcd96"
                text="查看发票勾稽"
                type="primary"
                @click="onLookFPGJClick(item)"
              />
            </template>
            <a-descriptions-item label="发票类型">{{ item.InvoiceTypeStr || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="发票代码">{{ item.InvoiceCode || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="发票号">{{ item.InvoiceNo || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="发票金额">¥{{ item.InvoiceAmount }}</a-descriptions-item>
            <a-descriptions-item label="发票日期">{{ item.InvoiceDate || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="发票地址">{{ item.InvoiceAddr || ' -- ' }}</a-descriptions-item>
            <a-descriptions-item label="本次勾稽金额">¥{{ item.CurrentVerificationAmount }}</a-descriptions-item>
          </a-descriptions>
        </a-col>
      </template>
      <a-col :span="24" class="mt10">
        <span style="font-size: 16px; font-weight: 600; padding-right: 20px">审核信息</span>
      </a-col>
      <a-col :span="24" class="mt10">
        <!-- :aList="auditList" -->
        <SupAuditInformation v-if="model.Id" :bussinessNo="model.Id" />
      </a-col>
    </a-row>
    <!-- 发票勾稽 -->
    <InvoiceCheckingModal ref="invoiceCheckingModal" :opType="1" />
    <!-- 商品明细 -->
    <GoodsDetailListModal
      ref="goodsDetailListModal"
      :urlA="url.getQueryPrePurchaseRemittedInfoDetails"
      :columnsA="columnsGoods"
    />
    <!-- 历史采购价 -->
    <HistoricalPurchasePriceModal ref="HistoricalPurchasePriceModal" />
    <!-- 库存明细 -->
    <InventoryDetailModal ref="InventoryDetailModal" />
    <!-- 本月销售明细 -->
    <SalesThisMonthModal ref="SalesThisMonthModal" />
  </a-spin>
</template>

<script>
import { getAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'PaymentInfoView',
  mixins: [ListMixin],
  components: { JEllipsis },
  props: {
    /**
     * 是否能编辑
     */
    isEdit: {
      type: Boolean,
      required: true,
      default: () => {
        return false
      },
    },
    /**
     * 数据model
     */
    model: {
      type: Object,
      required: true,
      default: () => {
        return {
          Remarks: '',
          RegAreaCode: '',
          SupplierInvoiceRecords: [],
        }
      },
    },
    /**
     * 审核记录信息
     */
    auditList: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      loading: false,
      tab: {
        tabTitles: [],
        showPagination: this.model.PaymentMode == 1 ? false : true,
        customSlot: ['clickPriceA', 'clickAl'],
      },
      httpHead: 'P36009',
      tabA: {
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [], //右上角按钮集合
        bordered: false, //是否显示表格的边框
        showPagination: false,
        rowKey: '',
      },
      dataSourceA: [],
      columnsA: [
        {
          title: '应收余额',
          dataIndex: 'ReceivableBalance',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '预收余额',
          dataIndex: 'AdvancePaymentBalance',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '应付余额',
          dataIndex: 'PayableBalance',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '预付余额',
          dataIndex: 'PrepaymentBalance',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ],
      // 预付款-商品明细表头
      columnsGoods: [
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '商品规格',
          dataIndex: 'PackingSpecification',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '末次进价',
          dataIndex: 'LastPurchasePrice',
          scopedSlots: { customRender: 'clickPriceA' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '采购人员',
          dataIndex: 'PurchaserName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '采购订单数量',
          dataIndex: 'PurchaseQuantity',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '可销总库存',
          dataIndex: 'AvailableInventory',
          width: 100,
          scopedSlots: { customRender: 'clickAl' },
          ellipsis: true,
        },
        {
          title: '本月销量',
          dataIndex: 'ThisMonthCount',
          width: 100,
          scopedSlots: { customRender: 'clickAl' },
          ellipsis: true,
        },
        {
          title: '上月销量',
          dataIndex: 'LastMonthCount',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '近90天销量',
          dataIndex: 'TotalSalesCount_90',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '预计可销天数',
          dataIndex: 'EstimatedSaleTime',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
      ],
      // 月结表头
      columns: [
        {
          title: '单据编号',
          dataIndex: 'DocumentNo',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 150,
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessEventTypeStr',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '所属订单',
          dataIndex: 'PurchaseOrderNo',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '商品规格',
          dataIndex: 'PackingSpecification',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '末次进价',
          dataIndex: 'LastPurchasePrice',
          scopedSlots: { customRender: 'clickPriceA' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '采购人员',
          dataIndex: 'PurchaserName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '原单入库数量',
          dataIndex: 'OriginInventoryCount',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '原单库存数量',
          dataIndex: 'RealInventory',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '原单总金额',
          dataIndex: 'OriginAmount',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '本次付款金额',
          dataIndex: 'CurrentPayAmount',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '审核中未付款金额',
          dataIndex: 'PaidFrozenAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '剩余未付款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '可销总库存',
          dataIndex: 'AvailableInventory',
          width: 100,
          scopedSlots: { customRender: 'clickAl' },
          ellipsis: true,
        },
        {
          title: '本月销量',
          dataIndex: 'ThisMonthCount',
          width: 100,
          scopedSlots: { customRender: 'clickAl' },
          ellipsis: true,
        },
        {
          title: '上月销量',
          dataIndex: 'LastMonthCount',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '近90天销量',
          dataIndex: 'TotalSalesCount_90',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '预计可销天数',
          dataIndex: 'EstimatedSaleTime',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
      ],
      // 预付款表头
      columnsYFK: [
        {
          title: '单据编号',
          dataIndex: 'DocumentNo',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 150,
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessEventTypeStr',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '总金额',
          dataIndex: 'TotalAmount',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '未结算金额',
          dataIndex: 'UnPaidAmount',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
          width: 130,
        },
        {
          title: '审核中未付款金额',
          dataIndex: 'PaymentOccupiedAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '剩余未付款金额',
          dataIndex: 'UnsettledAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '商品明细',
              id: '3e582ce0-e721-4cf3-aed0-2f820fccace6',
            },
          ],
        },
      ],
      isInitData: false,
      url: {
        getTotalBalanceAsync: '/{v}/Account/GetTotalBalanceAsync',
        list: '/{v}/PurchaseRemittedOrder/GetQueryMonthPurchaseRemittedInfoDetails', //月结单据信息接口
        getQueryPrePurchaseRemittedInfoDetails: '/{v}/PurchaseRemittedOrder/GetQueryPrePurchaseRemittedInfoDetails', //预付款商品明细接口
      },
    }
  },
  computed: {
    rules() {
      return {}
    },
  },
  watch: {
    model(val) {
      let isAudit = this.$route.query.isAudit
      // console.log(3333,isAudit,val,val.PaymentMode)

      if (val && val.SupplierId) {
        this.getTotalBalanceAsync()
      }
      // if(isAudit == 1 && val && val.PaymentMode == 2) this.loadData(1) //2月结
      if (val && (val.PaymentMode == 2 || val.PaymentMode == 3)) this.loadData(1) //2月结
      // if(isAudit == 2 && val && val.PaymentMode == 2) this.loadData(1) //2月结
    },
  },
  mounted() {
    let isAudit = this.$route.query.isAudit
    // console.log(2222,isAudit,this.model)

    if (isAudit == 2) {
      this.$nextTick(() => {
        this.getTotalBalanceAsync()
        if (this.model && (this.model.PaymentMode == 2 || this.model.PaymentMode == 3)) this.loadData(1) //2月结
      })
    }
  },
  created() {
    // console.log(1111)
  },
  methods: {
    clickA(text, record, index, column) {
      let key = column.dataIndex
      // 可销总库存
      if (key == 'AvailableInventory') {
        this.$refs.InventoryDetailModal.show(record)
      } else if (key == 'ThisMonthCount') {
        // 本月销量
        this.$refs.SalesThisMonthModal.show(record)
      } else if (key == 'LastPurchasePrice') {
        // 末次进价
        this.$refs.HistoricalPurchasePriceModal.show(record)
      }
    },
    loadBefore() {
      this.queryParam.PurchaseRemittedOrderRecordId = this.model.Id
    },
    onLookFPGJClick(item) {
      this.$refs.invoiceCheckingModal.look(item.Id, item.CurrentVerificationAmount, true)
    },
    onActionClick(record, key, index) {
      if (key === '商品明细') {
        this.$refs.goodsDetailListModal.show(record.DocumentId, record.BusinessEventType, true)
      }
    },
    // 付款勾稽
    payArticulation() {
      let id = this.$route.query.id
      this.$refs.invoiceCheckingModal.lookPay(id, true)
    },
    // 应收/应付信息
    getTotalBalanceAsync() {
      if (!this.model.SupplierId) return
      getAction(this.url.getTotalBalanceAsync, { SupplierId: this.model.SupplierId }, 'P36011').then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        // res.Data.TotalBalance = Number(
        //   (res.Data.ReceivableBalance + res.Data.PayableBalance + res.Data.PrepaymentBalance).toFixed(2)
        // )
        this.dataSourceA = [res.Data]
      })
    },
  },
}
</script>

<style lang="less" scoped>
.auditH {
  height: calc(100vh - 300px);
  overflow: auto;
}
</style>
