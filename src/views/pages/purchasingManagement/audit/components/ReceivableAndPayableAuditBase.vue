<!--
 * @Author: LP
 * @Description: 应收/应付调整审核和详情
 * @Date: 2023/07/13
-->
<template>
  <a-card :bodyStyle="{ padding: '0 0' }">
    <div style="margin: 0 0 15px 0">
      <page-header>
        <a-button slot="title-action" @click="goBack(true,true)">返回</a-button>
      </page-header>
    </div>
    <a-spin :spinning="loading">
      <a-descriptions :column="4">
        <a-descriptions-item>
          <div class="flc hcenter" style="padding-left: 20px">
            <a-icon :type="model.AuditStatus == 2 ? 'check-circle' : 'clock-circle'" theme="twoTone" two-tone-color="#52c41a" style="font-size: 32px" />
            <span style="color: #52c41a">{{ model.AuditStatusStr || '--' }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">调整金额</span>
          <div>{{ model.AdjustmentAmount? '¥'+model.AdjustmentAmount : '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">调整编号</span>
          <div>{{ model.Code || '--' }}</div>
        </a-descriptions-item>
        <a-descriptions-item>
          <span class="c999">创建时间</span>
          <div>{{ model.CreateTime || '--' }}</div>
        </a-descriptions-item>
      </a-descriptions>
      <a-tabs v-model="activeTabKey">
        <a-tab-pane :key="1" tab="基本信息">
          <ReceivableAndPayableAddBase ref="ReceivableAndPayableAddBase" :infoData="model" :isEdit="false" />
        </a-tab-pane>
        <a-tab-pane :key="2" tab="审核信息">
          <div style="margin: 5px 15px">
            <SupAuditInformation :bussinessNo="model.AuditId" v-if="activeTabKey == 2 && model.AuditId" />
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-spin>
    <a-affix :offset-bottom="15" class="affix" v-if="isAudit">
      <div style="margin-right: 15px">
        <YYLButton menuId="890f64db-9ee1-4df0-b943-5860b980552f" v-if="IsShowBtn" text="审核通过" type="primary" :loading="confirnLoading" @click="onPassClick" />
        <YYLButton menuId="9365a227-a96e-4d85-b9cf-11b0962c658f" v-if="IsShowBtn" text="审核驳回" type="danger" :loading="confirnLoading" @click="onRejectClick" />
        <span style="margin-right: 15px" v-if="!IsShowBtn">无审核权限</span>
        <a-button @click="goBack(true,true)">取消</a-button>
      </div>
    </a-affix>

    <!-- 审核弹框 -->
    <AuditRemarkModal ref="auditRemarkModal" @ok="onAuditModalOk" />
  </a-card>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction } from '@/api/manage'
const PageHeader = () => import('@/components/page/PageHeader')
export default {
  name: 'ReceivableAndPayableAuditBase',
  mixins: [EditMixin],
  components: { PageHeader },
  data() {
    return {
      model: {},
      loading: false,
      confirnLoading: false,
      IsShowBtn: false,
      id: '', //采购订单id
      isEdit: null,
      isAudit: null,
      isAuditInfo: null,
      ApprovalWorkFlowInstanceId: null,
      IsHasSuperior: false,
      httpHead: 'P36011',
      activeTabKey: 1,
      asdasd: 1,
      opType: 1, //1 详情 2编辑
      url: {
        info: '/{v}/Adjustment/GetInfoAsync',//详情
        authInfo: '/{v}/Adjustment/GetAuditInfoAsync',//审核详情
        getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.isEdit = this.$route.query.isEdit || null
      this.isAudit = this.$route.query.isAudit || null
      this.isAuditInfo = this.$route.query.isAuditInfo || null
      if (this.isAudit || this.isAuditInfo) {
        this.getAuthInfo()
      } else {
        this.getInfo()
      }
    }
  },
  created() { },
  methods: {
    getInfo() {
      let params = {
        id: this.id,
      }
      getAction(this.url.info, params, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.model = res.Data || {}
      })
    },
    getAuthInfo() {
      let params = {
        id: this.id,
      }
      getAction(this.url.authInfo, params, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.model = res.Data || {}
        this.model.AuditId = res.Data.Id
        if (this.isAudit) {
          this.getApprovalResults(this.model.AuditId)
        }
      })
    },
    // 获取审核权限和信息
    getApprovalResults(bussinessNo) {
      if (!bussinessNo) return
      getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.IsHasSuperior = res.Data['IsHasSuperior'] //是否有上级 true 有上级 false 没有上级
              this.ApprovalWorkFlowInstanceId = res.Data['ApprovalWorkFlowInstanceId']
              let userId = Vue.ls.get(USER_ID)
              if ((res.Data['ApprovalUserIds'] || []).length && this.isAudit) {
                this.IsShowBtn = res.Data['ApprovalUserIds'].some((v) => v == userId)
              }
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => { })
    },
    // 通过
    onPassClick() {
      this.showAuditModel(1)
    },
    // 驳回
    onRejectClick() {
      this.showAuditModel(2)
    },
    // 显示审核弹窗 type 1通过 2驳回
    showAuditModel(type) {
      let that = this
      if (this.$refs.auditRemarkModal) {
        let param = {
          ApprovalWorkFlowInstanceId: that.ApprovalWorkFlowInstanceId,
          IsHasSuperior: that.IsHasSuperior,
        }
        this.$refs.auditRemarkModal.show(type, param)
      }
    },
    // 审核弹窗回调
    onAuditModalOk(formData) {
      if (formData) {
        this.$refs.auditRemarkModal.postAuditInstance(formData, (bool) => {
          if (bool) {
            this.auditOK()
          }
        })
      } else {
        this.auditOK()
      }
    },
    auditOK() {
      setTimeout(() => {
        this.goBack(true,true)
      }, 500)
    },
  },
}
</script>

<style scoped>

</style>
