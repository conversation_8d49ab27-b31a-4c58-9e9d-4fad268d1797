<!--
 * @Author: LP
 * @Description: 调价modal
 * @Date: 2023/07/11
-->
<template>
  <a-modal :title="title" :width="'90vw'" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel2" @ok="hanleOK" :maskClosable="false" :destroyOnClose="true" okText="提交审核">
    <div>
      <!-- <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" /> -->
      <a-row style="margin-bottom: 10px">
        <a-col :span="20">
          入库单：
          <SingleChoiceView style="width: 200px" :placeholder="'请选择'" :Url="'/{v}/PurchaseOrder/GetPurchaseInventorySelect'" :httpHead="'P36009'" v-model="queryParam.purchaseInventoryOrderId" :dataKey="{ name: 'WarehouseInNo', value: 'PurchaseInventoryOrderId' }" :httpParams="{ pageindex: 1, pagesize: 1000, purchaseOrderId: orderId }" @dataLoaded="radioDatLoadAfter" />
        </a-col>
        <a-col :span="4">
          <a-button @click="searchReset" class="mr8" icon="reload">重置</a-button>
          <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
        </a-col>
      </a-row>

      <POrderTiaojiaView ref="POrderTiaojiaView" :opType="3" :model="{ GoodsItems: dataSource, Remark: '' }" :Loading="loading" />
    </div>
    <template slot="footer">
      <a-button @click="handleCancel" :disabled="confirmLoading">取消</a-button>
      <YYLButton menuId="" text="保存" type="primary" :loading="confirmLoading" @click="hanleOK(true)" class="ml8" />
      <YYLButton menuId="" text="提交审核" type="primary" :loading="confirmLoading" @click="hanleOK(false)" class="ml8" />
    </template>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { postAction } from '@/api/manage'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'TiaojiaModal',
  mixins: [ListMixin],
  components: {},
  props: {},
  data() {
    return {
      title: '申请采购调价',
      visible: false,
      confirmLoading: false,
      // searchItems: [
      //   {
      //     name: '入库单',
      //     type: SEnum.RADIO,
      //     httpHead: 'P36009',
      //     url: '/{v}/PurchaseOrder/GetPurchaseInventorySelect',
      //     key: 'purchaseInventoryOrderId',
      //     params: { purchaseOrderId: '' },
      //     dataKey: { name: 'WarehouseInNo', value: 'PurchaseInventoryOrderId' },
      //     loadAfter: this.radioDatLoadAfter,
      //   },
      // ],
      orderId: '',
      queryParam: {
        purchaseInventoryOrderId: '',
        purchaseOrderId: '',
      },
      isInitData: false,
      httpHead: 'P36009',
      refresh: true,
      url: {
        list: '',
        submit: '/{v}/PurchaseBusiness/CreatePurchasePriceAdjustment',
      },
    }
  },
  computed: {},
  mounted() { },
  created() { },
  methods: {
    loadAfter() {
      this.dataSource.forEach((x) => {
        // x.Quantity = x.UnsoldQuantity
        this.$set(x, 'Quantity', x.UnsoldQuantity)
      })

      // this.$refs.POrderTiaojiaView.initDataList()
    },
    show(orderId) {
      if (!orderId) {
        return
      }

      this.orderId = orderId
      // this.searchItems[0].params.purchaseOrderId = orderId
      this.queryParam.purchaseOrderId = orderId
      this.url.list = '/{v}/PurchaseOrder/GetAdjustInventoryOrderDetailList?purchaseOrderId=' + orderId

      this.visible = true
      // this.loadData(1)
    },
    handleCancel2() {
      if(this.confirmLoading) {
        this.$message.warning('处理中，请稍后再试')
        return false
      }
      this.close()
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
    hanleOK(IsDraft) {
      if (this.$refs.POrderTiaojiaView) {
        this.$refs.POrderTiaojiaView.getData((data) => {
          if (data) {
            data.IsDraft = IsDraft
            this.saveData(data)
          }
        })
      }
    },
    radioDatLoadAfter(list) {
      if (list && list.length > 0) {
        this.queryParam.purchaseInventoryOrderId = list[0].ItemValue

        this.loadData(1)
      }
    },
    saveData(data) {
      let that = this
      that.loading = true
      that.confirmLoading = true
      data.PurchaseOrderId = that.orderId
      postAction(that.url.submit, data, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {

            if (data.IsDraft) {
              that.confirmLoading = false
              that.loading = false
              that.$message.success('操作成功！')
              that.$emit('ok')
              that.handleCancel()
            } else {
              that.createApproval(res.Data)
            }

          } else {
            that.$message.error(res.Msg)

            that.confirmLoading = false
            that.loading = false
          }
        })
        .catch((e) => {
          that.confirmLoading = false
          that.loading = false
        })
      // .finally(() => {
      //   that.confirmLoading = false
      //   that.loading = false
      // })
    },
    /**
     * 创建审核实例
     */
    createApproval(bNo) {
      let that = this
      let params = {
        BussinessNo: bNo,
        Scenes: 6,
        OpratorId: that.getLoginUserId(),
      }
      if (!that.confirmLoading) {
        that.confirmLoading = true
        that.loading = true
      }
      postAction('/{v}/ApprovalWorkFlowInstance/CreateApproval', params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.$emit('ok')
            that.handleCancel()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .catch((e) => {
          that.confirmLoading = false
          that.loading = false
        })
        .finally(() => {
          that.confirmLoading = false
          that.loading = false
        })
    },
  },
}
</script>
<style lang="less">

</style>
