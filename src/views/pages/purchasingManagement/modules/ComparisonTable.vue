<!-- 比价表格 -->
<template>
  <div>
    <a-table
      style="margin-top: 15px"
      :bordered="true"
      ref="table"
      :columns="columnsNew"
      :dataSource="dataList"
      :pagination="ipagination"
      :loading="loading"
      @change="handleTableChange"
      :rowSelection="rowSelectionInfo"
      :scroll="{
        y: dataList.length === 0 ?false :(tab.scrollY ? tab.scrollY : this.$root.tableHeight),
        x: '900px',
      }"
      :rowKey="tab.rowKey ? tab.rowKey : (record, index) => index"
    >
      <!-- 字符串超长截取省略号显示-->
      <template slot="component" slot-scope="text">
        <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
        <j-ellipsis :value="text" v-else />
      </template>
      <div slot="priceList" class="price-box" slot-scope="text, record">
        <div class="position" :class="[`position${priceTdWiathStep}`]">
          <div
            :class="{ active: isInfo ? item.IsChecked : supplierQuotationChooseList.includes(item[sign]) }"
            v-for="(item, index) in record[listKeyName]"
            :key="index"
            @click="chooseSupplierQuotation(item)"
          >
            <div class="name">
              <j-ellipsis :value="item.SupplierName" />
            </div>
            <div class="offer-difference">
              <div class="offer">报价：￥{{ item.QuotationPrice }}</div>
              <div class="difference">
                <template v-if="item.EnumComparisonResult === 3">
                  <a-icon type="fall" class="green"/>
                  <span class="green" style="margin-left: 3px;">{{ item.ComparisonResult }}</span>
                </template>
                <template v-else-if="item.EnumComparisonResult === 1">
                  <a-icon type="rise" class="red" />
                  <span class="red" style="margin-left: 3px;">{{ item.ComparisonResult }}</span>
                </template>
                <template v-else>
                  <span>--</span>
                </template>
              </div>
            </div>
            <div class="count-date">
              <div class="count">供货数量：<j-ellipsis :value="item.SupplyQuantity" /></div>
              <div class="date">效期优于：<j-ellipsis :value="item.PreferredExpiryDate" /></div>
            </div>
          </div>
          <!-- 留白填充空格 -->
          <template v-if="record[listKeyName].length && record[listKeyName].length % priceTdWiathStep">
            <div
              class="space"
              v-for="whiteSpace in priceTdWiathStep - (record[listKeyName].length % priceTdWiathStep)"
              :key="`fill${whiteSpace}`"
            ></div>
          </template>
          <template v-else-if="record[listKeyName].length === 0">
            <div class="space" v-for="whiteSpace in priceTdWiathStep" :key="`fill${whiteSpace}`"></div>
          </template>
        </div>
      </div>
    </a-table>
  </div>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from '@/api/manage'
import { sign } from 'core-js/core/number'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'ComparisonTable',
  components: { JEllipsis },
  mixins: [ListMixin],
  props: {
    // 表格基础配置
    tab: {
      type: Object,
      default: () => {
        return {}
      },
    },
    // 供应商列表渲染字段key名
    listKeyName: {
      type: String,
      default: 'GoodsPurchaseQuotationList',
    },
    // 供应商报价唯一ID的key值
    sign: {
      type: String,
      default: 'Id',
    },
    /**
     * table数据
     */
    dataList: {
      type: Array,
      required: true,
      default: () => {
        return []
      },
    },
    // 供应商报价勾选集合ids
    supplierQuotationChooseList: {
      type: Array,
      default: () => {
        return []
      },
    },
    // 表格基础列名
    columns: {
      type: Array,
      required: true,
      default: () => {
        return []
      },
    },
    // 表格勾选行ids集合
    tableChooseList: {
      type: Array,
      default: () => {
        return []
      },
    },
    // 是否勾选全部只勾选当前页
    onlyAllChooseNowPage: {
      type: Boolean,
      default: true,
    },
    // 是否是详情模式
    isInfo: {
      type: Boolean,
      default: null,
    },
  },
  data() {
    return {
      priceColumnWidth: 240, // 供应商报价列厂家默认一单元格的宽度（单位px）
      basicMaxLength: 5, // 供应商报价列厂家个数超出多少就纵向滚动的阈值(目前最长可设置到5),修改改参数需要微调position类名的border-top值
      selectedRowKeys: [], //表格选中项的key
      selectedRows: [], //选中项的信息
    }
  },
  computed: {
    // 设置表格是否单选多选多选
    rowSelectionInfo() {
      if (this.tab.rowSelection && this.tab.rowSelection.type && !this.tab.rowSelection.getCheckboxProps) {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange,
          onSelect: this.onSelect,
          onSelectAll: this.onSelectAll,
          type: this.tab.rowSelection.type || 'checkbox',
        }
      } else if (this.tab.rowSelection && this.tab.rowSelection.getCheckboxProps) {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange,
          onSelect: this.onSelect,
          onSelectAll: this.onSelectAll,
          type: this.tab.rowSelection.type || 'checkbox',
          getCheckboxProps: this.tab.rowSelection.getCheckboxProps,
        }
      } else {
        return null
      }
    },
    columnsNew() {
      const columns = JSON.parse(JSON.stringify(this.columns))
      const priceTdWiathStep = this.priceTdWiathStep
      const priceColumnWidth = this.priceColumnWidth
      columns.map((item) => {
        if (item.title && item.title === '供应商报价') {
          item.width = priceColumnWidth * priceTdWiathStep
        }
      })
      return columns
    },
    // 供应商报价当前页最长的一列数据量
    maxPriceLenght() {
      const listKeyName = this.listKeyName
      let dataList = JSON.parse(JSON.stringify(this.dataList)) || []
      const maxLength = dataList.length
      const ipagination = this.ipagination
      const pageSize = ipagination.pageSize
      const current = ipagination.current
      let start = pageSize * (current - 1)
      let end =  pageSize * (current)
      end = end > dataList.length ? maxLength : end
      dataList = dataList.slice(start, end)
      dataList.sort((a, b) => {
        return b[listKeyName].length - a[listKeyName].length
      })
      return dataList[0] ? dataList[0][listKeyName].length || 1 : 1
    },
    // 供应商报价列长基数
    priceTdWiathStep() {
      const maxPriceLenght = this.maxPriceLenght
      const basicMaxLength = this.basicMaxLength
      // return maxPriceLenght <= basicMaxLength ? maxPriceLenght : basicMaxLength
      return maxPriceLenght
    },
  },
  mounted() {},
  watch: {
    tableChooseList: {
      handler(val) {
        console.log(111)
        this.selectedRowKeys = val
      },
      immediate: true,
    },
    dataList: {
      // 表格总数据变化，默认就是重置所有数据
      handler(val) {
        // 重置分页
        this.ipagination.current = 1
        // 重置供应商勾选集合
        this.$emit('update:supplierQuotationChooseList', [])
        // 重置左侧勾选集合
        this.$emit('update:tableChooseList', [])
      },
    }
  },
  methods: {
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        if (this.sort) {
          // 订单列表 排序方式
          this.queryParam[this.sort] = 'ascend' == sorter.order ? '1' : '2'
        }
      }
      this.ipagination = pagination
      // this.loadData(null, (source) => {
      //   this.dataSource2 = source
      // })
    },
    // 表格中供应商报价列点击事件
    chooseSupplierQuotation(item) {
      if (this.isInfo) {
        return
      }
      const sign = this.sign
      const supplierQuotationChooseList = JSON.parse(JSON.stringify(this.supplierQuotationChooseList))
      const index = supplierQuotationChooseList.findIndex((f) => f === item[sign])
      if (index === -1) {
        supplierQuotationChooseList.push(item[sign])
      } else {
        supplierQuotationChooseList.splice(index, 1)
      }
      this.$emit('update:supplierQuotationChooseList', supplierQuotationChooseList)
    },
    // tab选中项发生变化时的回调
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      this.$emit('selectedRows', selectedRowKeys, selectedRows, this.onlyAllChooseNowPage)
    },
    // 用户手动选择/取消选择某列的回调
    onSelect(record, selected, selectedRows) {
      this.$emit('onSelect', record, selected, selectedRows)
    },
    // 用户手动选择/取消选择所有列的回调
    onSelectAll(selected, selectedRows, changeRows) {
      this.$emit('onSelectAll', selected, selectedRows, changeRows)
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .ant-table-row-cell-break-word {
  position: relative;
}
.price-box {
  width: 100%;
  height: 40px;
  .position {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    // overflow-y: scroll;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-wrap: wrap;
    & > div {
      flex: 1;
      box-sizing: border-box;
      height: calc(40px + 30px);
      box-sizing: border-box;
      border-right: 1px solid #e8e8e8;
      // border-top: 1px solid #e8e8e8;
      padding: 10px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      .name {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        flex-shrink: 0;
        text-align: left;
      }
      .offer-difference {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 12px;
        .offer {
        }
        .difference {
          margin-left: 10px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .red {
            color: #e00064;
            background: transparent;
          }
          .green {
            color: #269454;
            background: transparent;
          }
        }
      }
      .count-date {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        .count {
          font-size: 12px;
          position: relative;
          max-width: 120px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .date {
          width: 120px;
          box-sizing: border-box;
          padding-left: 5px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          font-size: 12px;
          text-align: left;
        }
      }
    }
    & > .space {
      cursor: inherit;
    }
    & > .active {
      background: #ee909c;
    }
    & > div:nth-child(1) {
      border-top: none;
    }
    & > div:nth-child(2) {
      border-top: none;
    }
    & > div:nth-child(3) {
      border-top: none;
    }
    & > div:nth-child(4) {
      border-top: none;
    }
    & > div:nth-child(5) {
      border-top: none;
    }
    & > div:last-child {
      border-right: none;
    }
  }
  // .position1 {
  //   & > div {
  //     width: 100%;
  //   }
  // }
  // .position2 {
  //   & > div {
  //     width: 50%;
  //   }
  // }
  // .position3 {
  //   & > div {
  //     width: 33.3%;
  //   }
  // }
  // .position4 {
  //   & > div {
  //     width: 25%;
  //   }
  // }
  // .position5 {
  //   & > div {
  //     width: 20%;
  //   }
  // }
}
</style>
