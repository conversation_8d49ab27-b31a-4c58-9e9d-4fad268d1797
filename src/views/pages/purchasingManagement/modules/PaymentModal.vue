<!--
 * @Author: LP
 * @Description: 支付弹窗 modal
 * @Date: 2023/07/25
-->
<template>
  <a-modal
    :title="title"
    :width="500"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    @ok="hanleOK"
    :maskClosable="false"
    :destroyOnClose="true"
    :footer="null"
  >
    <div class="flc hcenter">
      <a-radio-group v-model="payType" @change="onRadioGroupChange">
        <a-radio :value="WXPAY"><img src="pay/wx.png" /> <span>微信</span></a-radio>
        <a-radio :value="ALIPAY"><img src="pay/ali.png" /> <span>支付宝</span></a-radio>
      </a-radio-group>
      <div class="mt20">
        <template v-if="codeUrl">
          <img :src="codeUrl" alt="" v-if="isImage" style="width: 200px; height: 200px" />

          <QRCodeView ref="QRCodeView" :contentUrl="codeUrl" v-else :contentH="200" :contentW="200" />
        </template>
        <template v-else>
          <a-spin :spinning="loading" v-if="loading">
            <div style="width: 200px; height: 200px"></div>
          </a-spin>
          <a-empty v-else />
        </template>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
const WXPAY = 33,
  ALIPAY = 34
export default {
  name: 'PaymentModal',
  mixins: [],
  components: {},
  props: {},
  data() {
    return {
      WXPAY,
      ALIPAY,
      title: '付款',
      loading: false,
      visible: false,
      confirmLoading: false,
      resultLoading: false,

      payType: WXPAY, //支付类型 33 微信 34 支付宝
      isImage: false,
      codeUrl: '', //二维码url
      paymentBusinessOrderId: '',
      interval: null,
      ids: [],
      httpHead: 'P36009',
      url: {
        createTwoCode: '/{v}/PurchaseBusiness/CreatePurchaseBuyCompensationPayCode', //创建买赔付款二维码
        result: '/{v}/PurchaseBusiness/IsPaySuccess', //查询是否支付成功
      },
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(list) {
      this.init()
      this.ids = list
      this.visible = true

      this.createPayTwoCode()
    },
    onRadioGroupChange(e) {
      if (this.interval) {
        clearInterval(this.interval)
      }
      this.createPayTwoCode()
    },
    init() {
      this.isImage = false
      this.interval = null
      this.codeUrl = ''
      this.paymentBusinessOrderId = ''
    },
    /**
     * 创建买赔付款二维码
     */
    createPayTwoCode() {
      let that = this
      that.loading = true
      that.init()
      let params = {
        PurchaseBuyCompensationIdList: this.ids,
        RechargeChannel: that.payType,
      }
      postAction(that.url.createTwoCode, params, that.httpHead)
        .then((res) => {
          if (res.IsSuccess && res.Data) {
            that.isImage = res.Data.IsCUMSPay
            if (res.Data && res.Data.PayParamJson) {
              that.paymentBusinessOrderId = res.Data.PaymentBusinessOrderId || ''
              let obj = JSON.parse(res.Data.PayParamJson)
              that.codeUrl = obj ? obj.codeUrl || '' : ''

              that.checkPayResult()
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    checkPayResult() {
      if (this.interval) {
        return
      }
      this.interval = setInterval(() => {
        this.getPayResult()
      }, 2000)
    },
    /**
     * 获取查询结果
     */
    getPayResult() {
      let that = this
      if (!that.paymentBusinessOrderId || that.resultLoading) {
        return
      }
      that.resultLoading = true
      getAction(that.url.result, { paymentBusinessOrderId: that.paymentBusinessOrderId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data == 3) {
              that.$message.success('支付成功!')
              that.handleCancel()
            } else if (res.Data != 1) {
              let msg = '失败!'
              switch (res.Data) {
                case 4:
                  msg = '已取消'
                  break
                case 5:
                  msg = '已超时'
                  break
              }
              that.$message.success('支付' + msg)
              that.close()
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.resultLoading = false
        })
    },
    handleCancel() {
      this.$emit('ok')
      this.close()
    },
    close() {
      if (this.interval) {
        clearInterval(this.interval)
      }
      this.visible = false
    },
    hanleOK() {
      let that = this
      that.confirmLoading = true
      postAction(
        that.url.submit,
        {
          PurchaseOrderId: that.orderId,
          GoodsItems: that.dataSource,
        },
        that.httpHead
      )
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.$emit('ok')
            that.handleCancel()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
  },
}
</script>
<style lang="less">

</style>
