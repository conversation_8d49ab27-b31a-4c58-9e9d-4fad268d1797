<!--
 * 快捷下单
-->
<template>
  <a-modal
    :title="'快捷下单'"
    :width="'90vw'"
    :visible="visible"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :footer="null"
  >
    <!--邀请报价弹窗 -->
    <div
      :style="{
        width: '100%',
        background: '#fff',
      }"
    >
      <a-card :bordered="false" :bodyStyle="{ padding: '0px' }">
        <div class="scrollCompany">
          <tagList v-if="visible" :list.sync="tagList" :sign="'createId'" :activeTag.sync="activeTag"></tagList>
        </div>
        <div class="divide"></div>
        <!-- 新增订单编辑页面 -->
        <AddPurchaseOrder
          @goNext="changeNextOrder"
          :ComparisonBatchNumber="ComparisonBatchNumber"
          :quickOrderSupplierList="tagList"
          :suppliersIdAndType="suppliersIdAndType"
          :QiuckSuppliersName="QiuckSuppliersName"
          ref="AddPurchaseOrder"
        />
      </a-card>
    </div>
  </a-modal>
</template>

<script>
import { getAction, postAction, apiHead } from '@/api/manage'
export default {
  name: 'QuickOrdering',
  data() {
    return {
      activeTag: null,
      tagList: [],
      ComparisonBatchNumber: null, // 比价批次号
      visible: false,
    }
  },
  computed: {
    suppliersIdAndType() {
      return this.activeTag
    },
    QiuckSuppliersName() {
      const tagList = this.tagList
      if (tagList.length > 0) {
        const list = this.activeTag ? this.activeTag.split('&') : []
        let quickOrderSuppliersId = list.length > 0 ? list[0] : null
        const info = tagList.find((f) => f.Id === quickOrderSuppliersId)
        return info.Name
      } else {
        return ''
      }
    },
  },
  watch: {
    activeTag: {
      handler(newVal, oldVal) {
        console.log(newVal)
      },
    },
  },
  mounted() {},
  // activated() {
  //   this.init()
  // },
  created() {},
  methods: {
    show(SuppliersList, ComparisonBatchNumber) {
      this.ComparisonBatchNumber = ComparisonBatchNumber
      this.tagList = SuppliersList
      this.activeTag = SuppliersList[0].createId
      this.visible = true
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
      //   this.queryParam = {}
    },
    // 切换下个订单
    changeNextOrder(type) {
      if (type === 'close') {
        this.close()
        return false
      }
      const tagList = JSON.parse(JSON.stringify(this.tagList))
      const activeTag = this.activeTag
      if (tagList.length === 1) {
        this.close()
      } else {
        const index = tagList.findIndex((f) => f.createId === activeTag)
        tagList.splice(index, 1)
        this.tagList = tagList
        const lg = this.tagList.length
        // 永远切换到删除后的最后一个供应商
        this.activeTag = this.tagList[lg - 1].createId
      }
    },
  },
}
</script>

<style lang="less" scoped>
/deep/.ant-modal-title {
  font-weight: bold;
  font-size: 18px;
}

.scrollCompany {
  box-sizing: border-box;
  overflow-y: auto;
  max-height: 200px;
}
.divide {
  box-sizing: border-box;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}
</style>
