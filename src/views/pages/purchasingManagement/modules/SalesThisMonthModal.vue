<template>
  <a-modal :title="title" :width="1000" v-model="visible" :footer="null" @cancel="close" @ok="handleOk">
    <a-spin :spinning="loading">
      <div v-if="visible" id="main" style="width: 100%; height: 400px"></div>
    </a-spin>
  </a-modal>
</template>

<script>
import * as echarts from 'echarts'
import { getAction, postAction, apiHead } from '@/api/manage'
export default {
  name: 'SalesThisMonthModal',
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      last12Months: [],
      yearInfoData: [],
      yearInfoMonthsData: [],
      httpHead: 'P36009',
      url: '/{v}/PurchaseGoods/GetGoodsHistoryMonthSalesList',
    }
  },
  methods: {
    show(record) {
      let goodsTitle = record.ErpGoodsName + '近一年销售明细'
      this.title = goodsTitle
      this.visible = true
      this.$nextTick(() => {
        this.getInfo(record.ErpGoodsId)
      })
    },
    getInfo(Id) {
      let that = this
      let params = {
        erpGoodsId: Id,
      }
      let array1 = []
      let array2 = []
      this.loading = true
      getAction(that.url, params, that.httpHead).then((res) => {
        if (res.IsSuccess) {
          if (res.Data && res.Data.length > 0) {
            res.Data.map((item) => {
              array1.push(item.DateStr)
              array2.push(item.SalesCount || 0)
            })
          }
          this.yearInfoMonthsData = array1
          this.yearInfoData = array2
          this.initEcharts()
          // console.log(this.yearInfoMonthsData, this.yearInfoData)
        } else {
          that.$message.error(res.Msg)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    getYearMonth() {
      // 获取当前日期和月份
      var currentDate = new Date();
      var currentMonth = currentDate.getMonth();

      // 创建一个数组来保存所有月份
      var last12Months = [];
      for (var i = 0; i < 12; i++) {
        // 创建一个新的日期对象表示指定年份和月份的第一天
        var dateOfPreviousMonth = new Date(currentDate.getFullYear(), currentMonth - i, 1);
        // 提取月份（JavaScript中月份是从0开始计数的，需要加1）
        var monthNameOrNumber = dateOfPreviousMonth.getFullYear() + "." + (dateOfPreviousMonth.getMonth() + 1).toString().padStart(2, '0');
        last12Months.push(monthNameOrNumber);
      }
      this.last12Months = last12Months
    },
    initEcharts() {
      let myChart = echarts.getInstanceByDom(document.getElementById("main"))
      if (myChart == null) {
        myChart = echarts.init(document.getElementById("main"));
      }
      // let myChart = echarts.init(document.getElementById("main"));
      // 指定图表的配置项和数据
      let option = {
        title: {
          text: "",
        },
        label: {
          show: true,
          position: 'top'
        },
        tooltip: {},
        legend: {
          data: [],
        },
        xAxis: {
          data: this.yearInfoMonthsData,
        },
        yAxis: {},
        series: [
          {
            name: "",
            type: "bar",
            data: this.yearInfoData,
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
    },
    close() {
      this.visible = false
    },
    handleOk() {

    },
  }
}
</script>

<style>
</style>