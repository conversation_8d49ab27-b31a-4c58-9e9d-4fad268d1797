<!--
 * @Description: wenjingGao 政策底价历史记录弹窗
 * @Version: 1.0
 * @Author: wenjingGao
 * @Date: 2025-05-13 09:37:19
 * @LastEditors: wenjing<PERSON>ao
 * @LastEditTime: 2025-06-12 14:30:09
-->
<template>
  <a-modal
    title="历史记录"
    :width="800"
    :visible="visible"
    :maskClosable="false"
    :destroyOnClose="true"
    :footer="null"
    @cancel="onClose"
  >
    <!-- 数据列表 -->
    <YYLTable
      ref="tableRef"
      url="/{v}/GoodsManage/GetGoodsPolicyPriceHisList"
      httpHead="P36006"
      method="GET"
      rowKey="GoodsSpuId"
      :queryParams="queryParams"
      :columns="columns"
      :isShowPagination="false"
      :pageSize="99999"
    />
  </a-modal>
</template>

<script>

export default {
  name: 'PolicyBasePriceHistoricalRecordsModal',
  data() {
    return {
      visible: false,
      queryParams: {},
      PureSalesColumns: [
        { title: '变更时间', key: 'ChangeTime', align: 'center'},
        { title: '变更前纯销政策底价', key: 'BeforePrice', align: 'center'},
        { title: '变更后纯销政策底价', key: 'AfterPrice', align: 'center'},
        { title: '变更原因', key: 'Reason', align: 'center', width: 250},
      ],
      CommercialSalesColumns: [
        { title: '变更时间', key: 'ChangeTime', align: 'center'},
        { title: '变更前商销政策底价', key: 'BeforePrice', align: 'center'},
        { title: '变更后商销政策底价', key: 'AfterPrice', align: 'center'},
        { title: '变更原因', key: 'Reason', align: 'center', width: 250},
      ],
      columns: [],
      httpHead: 'P36006',
      url: {
        list: '/{v}/GoodsManage/GetGoodsPolicyPriceHisList',
      },
    }
  },
  methods: {
    show(record, isPureSales, isFromOrder) {
      this.queryParams = {
        PriceChangeType: isPureSales ? 1:2,
        GoodsSpuId: record.GoodsSpuId,
        BatchNumber: isFromOrder ? record.BatchNo : record.BatchNumber,
        PageIndex: 1,
        PageSize: 99999 
      }
      if(isPureSales) {
        this.columns = this.PureSalesColumns
      } else {
        this.columns = this.CommercialSalesColumns
      }
      this.visible = true
    },
    onClose() {
      this.visible = false
    },
  },
}
</script>
