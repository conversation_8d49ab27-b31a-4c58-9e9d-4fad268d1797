<template>
  <div>
    <div style="padding-left: 16px">
      <a-form-model
        :form="form"
        ref="ruleForm"
        :model="form"
        :rules="rules"
      >
        <a-row :gutter="10">
          <a-col :md="queryCol">
            <a-form-model-item label="询价名称" prop="InquiryName">
              <a-input
                :title="form.InquiryName"
                disabled
                style="width: 100%"
                placeholder="请输入"
                v-model="form.InquiryName"
                :maxLength="50"
              ></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <a-card class="noborderBottom">
      <h1>询价商品</h1>
    </a-card>
    <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <TableNewView ref="tableView" :table="table" :columns="columns" :tabDataList="tabDataList" :dataList="dataSource">
    </TableNewView>
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { PublicMixin } from '@/mixins/PublicMixin' //非必要 公用的 有需要时候用其他mixin也行
import { getAction, postAction, putAction } from '@/api/manage'
export default {
  title: '询价信息',
  name: 'InquiryInformation',
  mixins: [ListMixin, PublicMixin],
  components: {},
  data() {
    return {
      loading: false,
      spinning: false,
      routeQueryParams: null, // 页面路由参数
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      queryCol: 10,
      searchItems: [
        {
          name: '商品', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'GoodsKey', //搜索key 必填
          placeholder: '请输入商品名称/编码',
        },
        {
          name: '生产厂家',
          type: this.$SEnum.INPUT,
          key: 'BrandManufacturer',
          placeholder: '请输入生产厂家',
        },
        {
          name: '供应商名称',
          type: this.$SEnum.INPUT,
          key: 'SupplierName',
          placeholder: '请输入供应商名称',
        },
      ],
      form: {},
      rules: {
        InquiryName: [{ required: true, message: '请输入询价名称', trigger: 'blur' }],
      },
      table: {
        curStatus: 1,
        tabStatusKey: 'ApproavalStatus',
        operateBtns: [], //右上角按钮集合
        rowKey: 'ErpGoodsCode',
        scrollY: this.$root.tableHeight - 120,
        size: 'small',
      },
      tabDataList: [],
      columns: [
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          ellipsis: true,
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          width: 200,
          dataIndex: 'PackingSpecification',
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批准文号',
          dataIndex: 'ApprovalNumber',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '求购数量',
          dataIndex: 'PurchaseQuantity',
          width: 200,
          disabled: true,
          scopedSlots: { customRender: 'input' },
        },
      ],
      httpHead: 'P36009',
      url: {
        listType: 'POST', //列表接口请求类型
        list: '/{v}/PurchaseInquiryOrder/InquiryOrderDetail', //列表数据接口
      },
    }
  },
  computed: {},
  created() {
    this.routeQueryParams = {
      Id: this.$route.query.Id,
      InquiryName: decodeURIComponent(this.$route.query.InquiryName),
    }
  },

  methods: {
    loadBefore() {
      const routeQueryParams = this.routeQueryParams
      this.queryParam['Id'] = routeQueryParams.Id
      this.form.InquiryName = routeQueryParams.InquiryName
    },
    loadAfter() {},
  },
}
</script>
<style scoped lang="scss">
.noborderBottom {
  border-bottom: none;
}
</style>
