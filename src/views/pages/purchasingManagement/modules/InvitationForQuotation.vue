<template>
  <a-modal
    :title="'邀请报价'"
    :width="'80vw'"
    :visible="visible"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :footer="null"
  >
    <!--邀请报价弹窗 -->
    <div
      :style="{
        width: '100%',
        background: '#fff',
      }"
    >
      <a-spin :spinning="confirmLoading">
        <div>
          <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
          <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
          <a-row :gutter="10">
            <a-col :md="24">
              <!-- 数据列表 -->
              <!-- 列表 -->
              <TableNewView
                ref="tableView"
                :table="table"
                :columns="columns"
                :tabDataList="tabDataList"
                :dataList="dataSource"
                @operate="operate"
              >
                <span slot="IsSupplierRegistered" slot-scope="{ text, record }">
                  <span>{{record.IsSupplierRegistered?'是':'否'}}</span>
                </span>
                <div slot="copyLink" slot-scope="{ text, record }">
                  <span @click="copyLinkNow(record)" :title="'点击复制链接'" class="copyLinkBox">复制链接</span>
                </div>
              </TableNewView>
            </a-col>
          </a-row>
        </div>
      </a-spin>
      <!-- 供应商编辑弹窗 -->
      <supplierAddModal ref="supplierAddModal" @update="updateSupplier" />
    </div>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { PublicMixin } from '@/mixins/PublicMixin' //非必要 公用的 有需要时候用其他mixin也行
import { getAction, postAction, putAction } from '@/api/manage'
export default {
  title: '邀请报价',
  name: 'InvitationForQuotation',
  mixins: [ListMixin, PublicMixin],
  components: {},
  data() {
    return {
      visible: false,
      confirmLoading: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      queryCol: 6,
      searchItems: [
        {
          name: '供应商名称', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'SupplierNameKeyword', //搜索key 必填
          placeholder: '请输入供应商名称',
        },
        {
          name: '创建时间',
          type: this.$SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTimeBegin',
          keyEnd: 'CreateTimeEnd',
          showTime: true,
          placeholder: ['开始日期', '结束日期'],
        },
      ],
      table: {
        curStatus: 1,
        tabStatusKey: 'ApproavalStatus',
        operateBtns: [
          { name: '下载报价单', type: 'primary', icon: '', key: 'down', id: '87888071-0458-454f-a739-2b67c4e80958' },
          { name: '新增链接', type: 'primary', icon: '', key: 'add', id: '57309f27-bf2f-4bc0-b016-f5310a3277b0' },
        ], //右上角按钮集合
        rowKey: 'Id',
        customSlot: ['IsSupplierRegistered', 'copyLink'],
      },
      tabDataList: [],
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          ellipsis: true,
          width: 230,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否建档',
          width:100,
          dataIndex: 'IsSupplierRegistered',
          scopedSlots: { customRender: 'IsSupplierRegistered' },
        },
        {
          title: '链接地址',
          ellipsis: true,
          dataIndex: 'LinkUrl',
          width: 350,
          length: 50,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          fixed: 'right',
          scopedSlots: { customRender: 'copyLink' },
        },
      ],
      httpHead: 'P36009',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/PurchaseQuotation/GetQuotationInvitationPageListAsync', //列表数据接口,
        exportUrl: '/{v}/PurchaseQuotation/GetToQuotationOrderFileAsync' // 导出报价单
      },
    }
  },
  computed: {},
  created() {},

  methods: {
    loadBefore() {},
    loadAfter() {},
    // searchQuery(param) {
    //   this.loadData()
    // },
    // 操作
    operate(record, key, index) {
      console.log(record, key, index)
      if (key == 'down') {
        const PurchaseInquiryOrderId = this.PurchaseInquiryOrderId
        const newParams = Object.assign({}, this.$refs.searchView.queryParam, {PurchaseInquiryOrderId})
        this.handleExportXls(
          '四川欣佳能达医药有限公司询价表',
          'Get',
          this.url.exportUrl,
          null,
          this.httpHead,
          newParams
        )
      } else if (key == 'add') {
        // this.goPage('/pages/purchasingManagement/AddInquiry')
        this.$refs.supplierAddModal.add(record, this.PurchaseInquiryOrderId)
      }
    },
    show(record) {
      const PurchaseInquiryOrderId = record.Id
      this.queryParam.PurchaseInquiryOrderId = PurchaseInquiryOrderId
      this.PurchaseInquiryOrderId =  PurchaseInquiryOrderId
      this.visible = true
      this.loadData()
    },
    // 复制链接
    copyLinkNow(record) {
      var input = document.createElement('input') // 创建input标签
      input.value = record.LinkUrl // 将input的值设置为需要复制的内容
      document.body.appendChild(input) // 添加input标签
      input.select() // 选中input标签
      document.execCommand('copy') // 执行复制
      this.$message.success({
        content: '已复制到粘贴板',
        duration: 1,
      })
      document.body.removeChild(input) // 移除input标签
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
      this.queryParam = {}
      this.$emit('close')
    },
    // 供应商编辑弹窗回调
    updateSupplier(type) {
      if (type === 1) {
        this.loadData()
      }
    },
  },
}
</script>

<style scoped lang="scss">
.copyLinkBox {
  cursor: pointer;
  color: #1890ff;
}
</style>
