<!--
 * @Description: 平均参考成本 弹窗
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-03-27 10:38:55
-->

<template>
  <a-modal :title="title" :width="800" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :footer="null" cancelText="关闭" :destroyOnClose="true">
    <div>
      <a-spin :spinning="confirmLoading">
        <!-- 平均参考成本变动记录列表 -->
        <div class="title-text">平均参考成本变动记录</div>
        <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab :cardBordered="false" :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate" />
        <!-- 批次参考成本列表 -->
        <a-row style="display: flex;align-items: center;margin-top: 10px;">
          <span class="title-text">批次参考成本</span>
          <a-col :md="queryCol">
            <a-form-item label="批次号" style="margin-bottom: 0!important" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input style="width: 100%" placeholder="请输入" v-model="queryParam.BatchNo"></a-input>
            </a-form-item>
          </a-col>
          <a-col style="margin-left:10px;margin-bottom: 1px;" :md="queryCol">
            <span class="table-page-search-submit-btns">
              <a-button @click="searchReset" icon="reload" style="margin-right: 8px">重置</a-button>
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            </span>
          </a-col>
        </a-row>
        <SimpleTable ref="table2" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl2" :linkUrlType="linkUrlType" showTab :cardBordered="false" :tab="tab" :queryParam="queryParam" :columns="columns2" :isTableInitData="isTableInitData" @operate="operate" />
      </a-spin>
    </div>

  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "AverageReferenceCostModal",
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      title: "平均参考成本",
      visible: false,
      confirmLoading: false,
      tab: {
        bordered: true, //是否显示表格的边框
        operateBtn: [

        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '发生时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生事件',
          dataIndex: 'BusinessEventTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单编号',
          dataIndex: 'BusinessOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '变动后平均参考成本',
          dataIndex: 'AfterAverageCost',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columns2: [
        {
          title: '批次号',
          dataIndex: 'BatchNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'BatchNumber',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '入库时间',
          dataIndex: 'WarehouseInTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '在库库存',
          dataIndex: 'RealInventory',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批次参考成本',
          dataIndex: 'PCCost',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      model: {},
      queryCol: 9,
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36009',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '/{v}/PurchaseGoods/GetGoodsAverageCostChangeRecordList',
      },
      linkUrl2: {
        list: '/{v}/PurchaseGoods/GetGoodsPCCostList',
      },

    };
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record) {
      this.model = record || {}
      this.isEdit = false;
      this.visible = true;
      this.queryParam = {
        ErpGoodsId: record.ErpGoodsId,
      }
      this.$nextTick(() => {
        this.$refs.table.loadDatas(1, this.queryParam)
        this.$refs.table2.loadDatas(1, this.queryParam)
      })
    },
    searchQuery() {
      this.$refs.table2.loadDatas(1, this.queryParam)
    },
    searchReset() {
      this.queryParam = {
        ErpGoodsId: this.queryParam.ErpGoodsId,
      }
      this.$refs.table2.loadDatas(1, this.queryParam)
    },
    // 列表操作
    operate(record, type) {
      
    },
    // 确定
    handleOk() {

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
<style scoped>
.title-text {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
  font-size: 15px;
}
</style>
