<!--
 * @Author: LP
 * @Description: 票折modal
 * @Date: 2023/07/11
-->
<template>
  <a-modal
    :title="title"
    :width="'90vw'"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel2"
    @ok="hanleOK"
    :maskClosable="false"
    :destroyOnClose="true"
    okText="提交审核"
  >
    <div>
      <!-- <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" /> -->
      <a-row style="margin-bottom: 10px">
        <a-col :span="20">
          入库单：
          <SingleChoiceView
            style="width: 200px"
            :placeholder="'请选择'"
            :Url="'/{v}/PurchaseOrder/GetPurchaseInventorySelect'"
            :httpHead="'P36009'"
            v-model="queryParam.purchaseInventoryOrderId"
            :dataKey="{ name: 'WarehouseInNo', value: 'PurchaseInventoryOrderId' }"
            :httpParams="{ pageindex: 1, pagesize: 1000, purchaseOrderId: orderId }"
            @dataLoaded="radioDatLoadAfter"
          />
        </a-col>
        <a-col :span="4">
          <a-button @click="searchReset" class="mr8" icon="reload">重置</a-button>
          <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
        </a-col>
      </a-row>
      <POrderPiaozheView
        ref="POrderPiaozheView"
        :opType="3"
        :model="{ GoodsItems: this.dataSource, Remark: remark, AgreementItems: this.AgreementItems }"
        :Loading="loading"
      />
    </div>
    <div slot="footer">
      <YYLButton  text="保存" type="primary" @click="hanleOK(true)" />
      <YYLButton  text="提交审核" type="primary" @click="hanleOK(false)" />
      <a-button @click="handleCancel" :disabled="confirmLoading">取消</a-button>
    </div>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { postAction } from '@/api/manage'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PiaozheModal',
  mixins: [ListMixin],
  components: {},
  props: {},
  data() {
    return {
      title: '申请票折',
      visible: false,
      // searchItems: [
      //   {
      //     name: '入库单',
      //     type: SEnum.RADIO,
      //     httpHead: 'P36009',
      //     url: '/{v}/PurchaseOrder/GetPurchaseInventorySelect',
      //     key: 'purchaseInventoryOrderId',
      //     params: { purchaseOrderId: '' },
      //     dataKey: { name: 'WarehouseInNo', value: 'PurchaseInventoryOrderId' },
      //   },
      // ],
      orderId: '',
      httpHead: 'P36009',
      confirmLoading: false,
      isInitData: false,
      queryParam: {
        purchaseInventoryOrderId: '',
        purchaseOrderId: '',
      },
      remark: '',
      url: {
        list: '',
        submit: '/{v}/PurchaseBusiness/CreatePurchaseInvoiceDiscount',
      },
      AgreementItems: []
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(orderId) {
      if (!orderId) {
        return
      }
      this.orderId = orderId
      // this.searchItems[0].params.purchaseOrderId = orderId
      this.queryParam.purchaseOrderId = orderId
      this.url.list = '/{v}/PurchaseOrder/GetInvoiceDiscountInventoryList?purchaseOrderId=' + orderId
      this.remark = ''
      this.AgreementItems = []
      this.visible = true
      // this.loadData(1)
    },
    loadAfter() {
      this.dataSource.forEach((x) => {
        x['Quantity'] = ''
      })
    },
    radioDatLoadAfter(list) {
      if (list && list.length > 0) {
        this.queryParam.purchaseInventoryOrderId = list[0].ItemValue

        this.loadData(1)
      }
    },
    handleCancel2() {
      if(this.confirmLoading) {
        this.$message.warning('处理中，请稍后再试')
        return false
      }
      this.dataSource = []
      this.close()
    },
    handleCancel() {
      this.dataSource = []
      this.close()
    },
    close() {
      this.visible = false
    },
    hanleOK(IsDraft) {
      if (this.$refs.POrderPiaozheView) {
        this.$refs.POrderPiaozheView.getData((data) => {
          if (data) {
            data.IsDraft = IsDraft
            this.remark = data.Remark
            this.AgreementItems = data.AgreementItems
            this.saveData(data)
          }
        })
      }
    },
    saveData(data) {
      let that = this
      that.loading = true
      that.confirmLoading = true
      data.PurchaseOrderId = that.orderId
      postAction(that.url.submit, data, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            
            if(data.IsDraft){
              that.$message.success('操作成功！')
              that.$emit('ok')
              that.handleCancel()
              that.confirmLoading = false
              that.loading = false
              return
            }
            that.createApproval(res.Data)
          } else {
            that.$message.error(res.Msg)
            that.confirmLoading = false
            that.loading = false
          }
        })
        .catch((e) => {
          that.confirmLoading = false
          that.loading = false
        })
      // .finally(() => {
      //   that.loading = false
      //   that.confirmLoading = false
      // })
    },
    /**
     * 创建审核实例
     */
    createApproval(bNo) {
      let that = this
      let params = {
        BussinessNo: bNo,
        Scenes: 7,
        OpratorId: that.getLoginUserId(),
      }
      if (!that.confirmLoading) {
        that.confirmLoading = true
        that.loading = true
      }
      postAction('/{v}/ApprovalWorkFlowInstance/CreateApproval', params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.$emit('ok')
            that.handleCancel()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .catch((e) => {
          that.confirmLoading = false
          that.loading = false
        })
        .finally(() => {
          that.confirmLoading = false
          that.loading = false
        })
    },
  },
}
</script>
<style lang="less">

</style>
