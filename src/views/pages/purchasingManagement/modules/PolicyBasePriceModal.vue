<!--
 * @Description: 纯销政策底价 弹窗
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: wenjing<PERSON>ao
 * @LastEditTime: 2025-06-12 14:44:35
-->

<template>
  <a-modal
    title="政策底价"
    :width="1200"
    :visible="visible"
    :maskClosable="false"
    :destroyOnClose="true"
    @cancel="onClose"
  >
    <!-- 搜索 -->
    <YYLForm
      v-model="searchFormData"
      :formFields="searchFormFields"
      :isSearchMode="true"
      :col="{ md: 8, sm: 24 }"
      @reset="$refs.tableRef.getList(true)"
      @search="$refs.tableRef.getList(true)"
    />

    <!-- 数据列表 -->
    <YYLTable
      ref="tableRef"
      url="/{v}/GoodsManage/GetGoodsPolicyPriceList"
      httpHead="P36006"
      method="GET"
      rowKey="Id"
      :queryParams="{ ...searchFormData, GoodsSpuId }"
      :columns="columns"
      :scrollX="1750"
    />

    <!-- 历史记录 弹窗 -->
    <PolicyBasePriceHistoricalRecordsModal ref="PolicyBasePriceHistoricalRecordsModal" />
  </a-modal>
</template>

<script>
export default {
  name: 'PolicyBasePriceModal',
  data() {
    return {
      visible: false,
      GoodsSpuId: '',
      searchFormData: {
        BatchNumber: '',
      },
      searchFormFields: [
        {
          key: 'BatchNumber',
          label: '批次号',
          type: 'input',
          placeholder: '请输入',
        },
        {
          key: 'PurchaseOrderNo',
          label: '订单编号',
          type: 'input',
          placeholder: '请输入',
        },
      ],
      columns: [
        { title: '订单编号', key: 'PurchaseOrderNo', width: 200 },
        { title: '批次号', key: 'BatchNumber', width: 200 },
        { title: '批号', key: 'ProductionBatchNumber', width: 120 },
        { title: '入库时间', key: 'WarehouseInTime', width: 180, align: 'center' },
        { title: '入库数量', key: 'InboundQuantity', width: 120, align: 'right' },
        { title: '在库库存', key: 'StockQuantity', width: 120, align: 'right' },
        { title: '入库单价', key: 'InventoryPrice', width: 120, type: 'amount' },
        { title: '采购调价', key: 'PurchasePriceAdjustmentPrice', width: 120, type: 'amount' },
        { title: '购进返利', key: 'PurchaseRebatePrice', width: 120, type: 'amount' },
        { title: '纯销销售返利', key: 'PureSalesRebatePrice', width: 120, type: 'amount' },
        {
          title: '纯销政策底价',
          key: 'PureSalesPolicyPrice',
          width: 120,
          type: 'amount',
          isLink: true,
          onClick: (record) => {
            this.$refs.PolicyBasePriceHistoricalRecordsModal.show(record, true, false)
          },
        },
        { title: '商销销售返利', key: 'CommercialSalesRebatePrice', width: 120, type: 'amount' },
        {
          title: '商销政策底价',
          key: 'CommercialSalesPolicyPrice',
          width: 120,
          type: 'amount',
          isLink: true,
          onClick: (record) => {
            this.$refs.PolicyBasePriceHistoricalRecordsModal.show(record, false, false)
          },
        },
      ],
    }
  },
  methods: {
    show(record) {
      this.GoodsSpuId = record.GoodsSpuId || record.Id
      this.visible = true
    },
    onClose() {
      this.visible = false
      this.searchFormData.BatchNumber = ''
    },
  },
}
</script>
