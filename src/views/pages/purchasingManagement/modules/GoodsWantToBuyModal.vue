<!--
 * @Author: LP
 * @Description: 商品求购回复和查看弹窗
 * @Date: 2023/07/05
-->
<template>
  <a-modal
    :title="isReply ? '详情' : '回复'"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <div
      :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff'
      }"
    >
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-divider orientation="left" style="font-size: 16px; font-weight: 600;">
                反馈信息
              </a-divider>
            </a-col>
            <a-col :md="24">
              <a-descriptions>
                <a-descriptions-item label="反馈编号">{{ model.Code || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="创建时间">{{ model.CreateTime || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="客户">{{ model.CustomerName || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="联系电话">{{ model.LinkPhone || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="反馈描述">{{ model.Description || ' -- ' }}</a-descriptions-item>
              </a-descriptions>
            </a-col>
            <a-col :md="24">
              <a-form-model-item label="反馈图片：">
                <template v-if="model.ImageUrl && model.ImageUrl.length > 0">
                  <ImageControl
                    v-for="item in model.ImageUrl"
                    :src="item"
                    :width="150"
                    :height="150"
                    :key="item"
                    style="margin-right: 10px;float: left;margin-bottom: 10px;"
                  />
                </template>
                <span v-else>--</span>
              </a-form-model-item>
            </a-col>
            <a-col :md="24">
              <a-form-model-item label="反馈视频：">
                <video height="252px" width="100%" :src="model.VideoUrl" controls v-if="model.VideoUrl"></video>
                <span v-else>--</span>
              </a-form-model-item>
            </a-col>
            <a-col :md="24">
              <a-divider orientation="left" style="font-size: 16px; font-weight: 600;">
                备注信息
              </a-divider>
            </a-col>
            <a-col :md="24">
              <a-table
                :bordered="true"
                ref="table"
                rowKey="Id"
                size="middle"
                :columns="columns"
                :dataSource="model.FeedbackRemark || []"
                :pagination="false"
                :scroll="{ y: '200' }"
              >
                <!-- 字符串超长截取省略号显示-->
                <span slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </span>
              </a-table>
            </a-col>
            <a-col :md="24">
              <a-divider orientation="left" style="font-size: 16px; font-weight: 600;">
                回复信息
              </a-divider>
            </a-col>
            <a-col :md="24" v-if="!model.IsReply">
              <a-form-model-item label="回复内容：" prop="ReplyContent">
                <JEditor v-model="model.ReplyContent" :bName="BName" :dir="Dir"> </JEditor>
              </a-form-model-item>
            </a-col>
            <a-col :md="24" v-if="model.IsReply">
              <a-form-model-item label="回复内容：">
                <div v-html="model.ReplyContent"></div>
              </a-form-model-item>
            </a-col>
            <a-col :md="24" v-if="model.IsReply">
              <a-form-model-item label="回复时间：">
                {{ model.ReplyTime || ' -- ' }}
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel">
        关闭
      </a-button>
      <a-button :disabled="disableSubmit" @click="handleOk" type="primary">确定</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
const ImageControl = () => import('@/components/yyl/components/ImageControl')
const JEditor = () => import('@/components/jeecg/JEditor')
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'GoodsWantToBuyModal',
  mixins: [EditMixin],
  components: {
    ImageControl,
    JEllipsis,
    JEditor
  },
  data() {
    return {
      title: '反馈',
      visible: false,
      BName: window._CONFIG['AliOssConfig'].BaseBucketName,
      Dir: window._CONFIG['AliOssConfig'].CommonDir,
      disableSubmit: false,
      isReply: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 }
      },
      columns: [
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '备注内容',
          dataIndex: 'Remark',
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        }
      ],
      confirmLoading: false,
      url: {
        detail: '/Feedback/Detail',
        update: '/Feedback/Reply'
      }
    }
  },
  computed: {
    rules() {
      return {
        ReplyContent: [{ required: true, message: '请输入!' }]
      }
    }
  },
  mounted() {},
  created() {},
  methods: {
    edit(record) {
      this.isReply = record.isReply // true --- 回复  false -- 详情
      this.getDetail(record.Id)
      this.visible = true
    },
    /**
     * @description: 详情
     * @param {*}
     * @return {*}
     */

    getDetail(id) {
      if (!id) return
      getAction(this.url.detail, { id: id }, 'P31121').then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (!res.Data) return
        this.model = Object.assign({}, res.Data)

        if (res.Data.ImageUrl) {
          this.model.ImageUrl = JSON.parse(res.Data.ImageUrl)
        }
        console.log(this.model)

        if (!res.Data.IsReply) {
          this.model.ReplyContent = ''
        }
      })
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let formData = Object.assign(this.model, values)
          let params = {
            FeedbackId: formData.Id,
            ReplyContent: formData.ReplyContent
          }
          let obj
          obj = postAction(this.url.update, params, 'P31121')
          obj
            .then(res => {
              if (res.IsSuccess) {
                that.$message.success('操作成功')
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    }
  }
}
</script>
