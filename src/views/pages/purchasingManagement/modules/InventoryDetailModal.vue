<!-- 库存明细 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :footer="null" cancelText="关闭" :destroyOnClose="true">
    <div>
      <a-spin :spinning="confirmLoading">
        <div class="info-box" style="margin-bottom: 10px;">
          <span><strong>商品名称：</strong>{{model.ErpGoodsName || '--'}}</span>
          <span><strong>商品编号：</strong>{{model.ErpGoodsCode || '--'}}</span>
          <span><strong>规格：</strong>{{model.PackageUnit || '--'}}</span>
          <span><strong>生产厂商：</strong>{{model.BrandManufacturer || '--'}}</span>
          <span><strong>总库存：</strong>{{model.AvailableInventory || model.CanSaleInventory || '--'}}</span>
        </div>
        <SimpleSearchArea ref="SimpleSearchArea" :queryCol="8" :searchInput="searchInput" @searchQuery="searchQuery" />
        <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab :cardBordered="false" :tab="tab" :queryParam="queryParam" :columns="columns" isHandleTableChange :isTableInitData="isTableInitData" @operate="operate" @handleTableChange="handleTableChange" />
      </a-spin>
    </div>

  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "InventoryDetailModal",
  components: {
    JEllipsis
  },
  mixins: [EditMixin],
  data() {
    return {
      title: "库存明细",
      visible: false,
      confirmLoading: false,
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '批号',
          type: 'input',
          value: '',
          key: 'BatchNumber',
          defaultVal: '',
          placeholder: '请输入批号',
        },
        {
          name: '仓库',
          type: 'selectBaseLink',
          httpHead: 'P36001',
          url: '/{v}/Global/GetListDictItem',
          vModel: 'WarehouseCode',
          httpParams: { groupPY: 'cangk' },
          dataKey: { name: 'ItemValue', value: 'ItemPym' },
        },
      ],
      tab: {
        bordered: true, //是否显示表格的边框
        operateBtn: [

        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '批号',
          dataIndex: 'BatchNumber',
          width: 150,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: (<j-ellipsis value={String(text) || ''} />),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '批号可销总库存',
          dataIndex: 'CanSaleInventory',
          width: 150,
          ellipsis: true,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: (<j-ellipsis value={String(text) || ''} />),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '仓库',
          dataIndex: 'WarehouseName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '可销库存',
          dataIndex: 'RealInventory',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '有效期',
          dataIndex: 'Period',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      model: {},
      queryCol: 6,
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36009',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '/{v}/PurchaseGoods/GetGoodsStockList',
      },

    };
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(record) {
      this.model = record || {}
      this.isEdit = false;
      this.visible = true;
      this.queryParam = {
        ErpGoodsId: record.ErpGoodsId,
      }
      this.$nextTick(() => {
        this.loadDataFun(1)
      })
    },
    loadDataFun(page) {
      this.$refs.table.loadDatas(page, this.queryParam, (data) => {
        if (!data) {
          data = []
        }
        if (data) {
          // 列合并处理数据
          // 先排序
          data.sort((a, b) => (a.BatchNumber > b.BatchNumber) ? 1 : ((b.BatchNumber > a.BatchNumber) ? -1 : 0));
          const arr = data
          // 找相同的项出现次数
          const countMap = {};
          arr.forEach(obj => {
            const BatchNumber = obj.BatchNumber;
            countMap[BatchNumber] = (countMap[BatchNumber] || 0) + 1;
          });
          // 赋值rowSpan
          const uniqueNames = new Set();
          arr.forEach(obj => {
            if (!uniqueNames.has(obj.BatchNumber)) {
              obj.rowSpan = countMap[obj.BatchNumber];
              uniqueNames.add(obj.BatchNumber);
            } else {
              obj.rowSpan = 0;
            }
          });
          // console.log(countMap, arr);
          this.$refs.table.dataSource = arr || []
        }
      })
    },
    searchQuery() {
      this.queryParam = {
        ErpGoodsId: this.queryParam.ErpGoodsId,
        ...this.$refs.SimpleSearchArea.queryParam,
      }
      this.loadDataFun(1)
    },
    // 列表操作
    operate(record, type) {

    },
    // 分页切换事件
    handleTableChange() {
      this.loadDataFun()
    },
    // 确定
    handleOk() {

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
<style scoped>
.info-box span {
  font-size: 14px;
  margin-right: 10px;
}
</style>
