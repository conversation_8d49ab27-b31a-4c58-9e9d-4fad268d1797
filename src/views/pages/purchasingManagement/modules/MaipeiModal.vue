<!--
 * @Author: LP
 * @Description: 买赔modal
 * @Date: 2023/07/11
-->
<template>
  <a-modal :title="title" :width="'90vw'" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" @ok="hanleOK" :maskClosable="false" :destroyOnClose="true" okText="提交">
    <a-form-model ref="form" :rules="rules" :model="model">
      <a-row :gutter="10">
        <a-col span="24">
          <!-- 列表 -->
          <TableView ref="tableView" :showCardBorder="false" :tab="tab" :columns="columns" :dataList="dataSource" @actionClick="onActionClick">
            <template slot="sDate" slot-scope="{ text, record, index, column }">
              <a-date-picker v-model="record[column.dataIndex]" style="width: 100%" placeholder="请选择" :disabled-date="(current) => disabledDate(current, column.dataIndex, record)" />
            </template>
            <!-- <template slot="bottomView">
              <a-row style="padding-top: 20px">
                <a-col span="12" style="font-weight: bold">
                  买赔金额： {{ total < 0 ? '-' : '' }}¥{{ total < 0 ? String(total).replace('-', '') : total }}
                </a-col>
                <a-col span="12">
                  <span style="color: red">*</span>验收人<a-input
                    placeholder="请输入"
                    v-model="acceptedBy"
                    :maxLength="20"
                    style="width: 90%; margin-left: 5px"
                  />
                </a-col>
              </a-row>
            </template> -->
          </TableView>
        </a-col>
      </a-row>
      <a-row :gutter="10" class="mt15">
        <a-col span="12" style="font-weight: bold">
          买赔金额： {{ total < 0 ? '-' : '' }}¥{{ total < 0 ? String(total).replace('-', '') : total }}
        </a-col>
        <a-col span="12">
          <a-form-model-item label="验收人" prop="AcceptedBy" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input placeholder="请输入" v-model="model.AcceptedBy" :maxLength="20" style="width: 100%" />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { postAction } from '@/api/manage'
import moment from 'moment'
export default {
  name: 'MaipeiModal',
  mixins: [ListMixin, EditMixin],
  components: {},
  props: {},
  watch: {
    model: {
      handler(newVal, oldVal) {
        this.getTotalAmount()
      },
      deep: true,
    },
  },
  data() {
    return {
      title: '申请买赔',
      visible: false,
      confirmLoading: false,
      tab: {
        bordered: false, //是否显示表格的边框
        rowKey: 'Id',
        customSlot: ['sDate'],
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          // fixed: 'left',
          width: 150,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 150,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 200,
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
        {
          title: '含税进价',
          dataIndex: 'TaxIncludedPurchasePrice',
          width: 150,
          scopedSlots: { customRender: 'price' },
          precision: 6,
          ellipsis: true,
        },
        {
          title: '采购数量',
          dataIndex: 'PurchaseQuantity',
          width: 100,
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        {
          title: '已买赔数量',
          dataIndex: 'PurchasedCompensationQuantity',
          width: 120,
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
        },
        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 150,
          scopedSlots: { customRender: 'input' },
          fixed: 'right',
          ellipsis: true,
        },
        {
          title: '生产日期',
          dataIndex: 'ManufacturingDate',
          width: 150,
          scopedSlots: { customRender: 'sDate' },
          fixed: 'right',
          ellipsis: true,
        },
        {
          title: '失效日期',
          dataIndex: 'ExpirationDate',
          width: 150,
          scopedSlots: { customRender: 'sDate' },
          fixed: 'right',
          ellipsis: true,
        },
        {
          title: '本次买赔数量',
          dataIndex: 'BuyCompensationQuantity',
          width: 150,
          precision: 0, //数值精度
          min: 0,
          onInputChange: this.onInputChange,
          scopedSlots: { customRender: 'inputNumber' },
          fixed: 'right',
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '移除',
              id: '21afdaa4-b21f-43dc-bb11-70e4200ad1f4',
            },
          ],
        },
      ],
      model: { AcceptedBy: '' },
      total: 0,
      orderId: '',
      httpHead: 'P36009',
      labelCol: {
        xs: { span: 2 },
        sm: { span: 2 },
      },
      wrapperCol: {
        xs: { span: 22 },
        sm: { span: 22 },
      },
      url: {
        list: '',
        submit: '/{v}/PurchaseBusiness/CreatePurchaseBuyCompensation',
      },
    }
  },
  computed: {
    rules() {
      return {
        AcceptedBy: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (value) {
                this.validStrMaxLength(rule, value, callback, 20)
              } else {
                callback()
              }
            },
          },
        ],
      }
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    disabledDate(current, key, record) {
      if (key == 'ManufacturingDate') {
        return current && current > moment().startOf('day')
      } else if (key == 'ExpirationDate') {
        return current && current <= moment().startOf('day')
        // // if (record.ManufacturingDate) {
        // //   return current && current < moment().startOf(record.ManufacturingDate)
        // // } else {
        // return false
        // // }
      }
    },
    show(id) {
      this.orderId = id
      this.model.AcceptedBy = ''
      this.visible = true

      this.url.list =
        '/{v}/PurchaseOrder/GetPurchaseBuyCompensationInventoryOrderDetail?purchaseOrderId=' + this.orderId
      this.loadData(1)
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
    onActionClick(record, key, index) {
      if (key == '移除') {
        this.dataSource.splice(index, 1)
      }
    },
    hanleOK() {
      if (this.dataSource.length == 0) {
        this.$message.warning('无商品数据，无须提交')
        return
      }
      this.$refs.form.validate((err, values) => {
        if (err) {
          let list = this.dataSource.filter((x) => {
            return x.BuyCompensationQuantity
          })
          if (list && list.length == 0) {
            this.$message.warning('请至少完善一个商品的本次买赔数量！')
            return
          }
          if (!this.model.AcceptedBy) {
            this.$message.warning('请输入验收人！')
            return
          }
          let that = this
          that.confirmLoading = true
          that.loading = true
          postAction(
            that.url.submit,
            {
              PurchaseOrderId: that.orderId,
              GoodsItems: list,
              AcceptedBy: that.model.AcceptedBy,
            },
            that.httpHead
          )
            .then((res) => {
              if (res.IsSuccess) {
                that.$message.success('操作成功！')
                that.$emit('ok')
                that.handleCancel()
              } else {
                that.$message.error(res.Msg)
                that.confirmLoading = false
                that.loading = false
              }
            })
            .finally(() => {
              that.confirmLoading = false
              that.loading = false
            })
        }
      })
    },
    /**
     * 获取总金额
     */
    getTotalAmount() {
      let sum = 0
      this.dataSource.forEach((x) => {
        if ((x.TaxIncludedPurchasePrice >= 0 || x.TaxIncludedPurchasePrice < 0) && x.BuyCompensationQuantity >= 0) {
          sum += Number((x.TaxIncludedPurchasePrice * x.BuyCompensationQuantity).toFixed(2))
        }
      })
      this.total = this.getAmountOfMoney(sum)
    },
    /**
     * 买赔金额=含税进行*本次买赔数量；
     * @param {*} val
     * @param {*} key
     * @param {*} record
     * @param {*} index
     */
    onInputChange(val, key, record, index) {
      if (record.PurchaseQuantity >= 0 && record.PurchasedCompensationQuantity >= 0) {
        let num = record.PurchaseQuantity - record.PurchasedCompensationQuantity
        if (val && val > num) {
          if (this.inputChangeing) {
            return
          }
          this.inputChangeing = true
          this.$message.warning('本次买赔数量不能超过采购数量减去已买赔数量！')
          record.BuyCompensationQuantity = 0
        }
      }

      //计算调价金额
      this.getTotalAmount()
      this.inputChangeing = false
    },
  },
}
</script>
<style lang="less">

</style>
