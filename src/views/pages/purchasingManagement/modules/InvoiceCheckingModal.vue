<!--
 * @Author: LP
 * @Description: 发票勾稽 modal
 * @Date: 2023/07/13
-->
<template>
  <drag-modal
    :title="opType == 1 ? '付款勾稽' : '发票勾稽'"
    :width="opType == 1 ? '70vw' : '80vw'"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    @ok="hanleOK"
    :maskClosable="false"
    :destroyOnClose="true"
    okText="保存"
  >
    <a-radio-group
      v-if="opType == 2"
      style="margin-bottom: 10px"
      v-model="goujiType"
      :disabled="isEdit ? false : true"
      name="goujiType"
      @change="changeGoujiType"
    >
      <a-radio :value="1"> 按金额勾稽 </a-radio>
      <a-radio :value="2"> 按商品勾稽 </a-radio>
    </a-radio-group>
    <div v-if="isEdit">
      <div
        v-if="goujiType == 1 || (goujiType != 1 && opType == 1)"
        style="display: flex; flex-direction: row; align-items: center; margin-bottom: 15px"
      >
        <div>
          按金额勾稽:<a-input-number
            placeholder="请输入"
            v-model="invoiceAmount"
            style="width: 200px; margin-left: 10px; margin-right: 10px"
            :max="*********.99"
          />
          <a-button type="primary" @click="onSureGJClick" :disabled="!invoiceAmount" :loading="loading"
            >确认勾稽</a-button
          >
        </div>
        <div style="margin-left: 10px" v-if="opType == 2">
          <span>勾稽范围：</span>
          <a-radio-group v-model="goujiNumType" name="goujiNumType"  @change="changeGoujiType('勾稽范围')">
            <a-radio :value="0"> 全部 </a-radio>
            <a-radio :value="1"> 仅勾正数 </a-radio>
            <a-radio :value="2"> 仅勾负数 </a-radio>
          </a-radio-group>
        </div>
        <div style="margin-left: 10px" v-if="opType == 2" @change="changeProductType('商品范围')">
          <span>商品范围：</span>
          <a-radio-group v-model="GoodsScaleType" name="GoodsScaleType">
            <a-radio :value="1"> 全部 </a-radio>
            <a-radio :value="2"> 正常商品 </a-radio>
            <a-radio :value="3"> 促销品 </a-radio>
          </a-radio-group>
        </div>
      </div>
      <div v-if="goujiType == 2">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="24">
              <a-col :md="8" :sm="8">
                <a-form-item label="商品">
                  <a-input placeholder="名称/拼音码/编码" v-model="queryParam.GoodsKey"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="8">
                <a-form-item label="单据编号">
                  <a-input placeholder="请输入" v-model="queryParam.DocumentNo"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="8">
                <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                  <a-button @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>
    </div>
    <TableView
      ref="tableView"
      v-if="refresh"
      :showCardBorder="false"
      :tab="tab"
      :columns="opType == 1 ? columns2 : columns"
      :dataList="dataSource"
    >
      <template v-if="goujiType == 1 || (goujiType != 1 && opType == 1) || !isEdit" slot="bottomView">
        <a-row style="padding-top: 20px" v-if="opType == 2 && total">
          <a-col span="6" style="font-weight: bold">
            金额合计： {{ total.one < 0 ? '-' : '' }}¥{{
              total.one < 0 ? String(total.one).replace('-', '') : total.one
            }}
          </a-col>
          <a-col span="6" style="font-weight: bold">
            税额合计： {{ total.two < 0 ? '-' : '' }}¥{{
              total.two < 0 ? String(total.two).replace('-', '') : total.two
            }}
          </a-col>
          <a-col span="6" style="font-weight: bold">
            含税金额合计： {{ total.three < 0 ? '-' : '' }}¥{{
              total.three < 0 ? String(total.three).replace('-', '') : total.three
            }}
          </a-col>
        </a-row>
        <a-row style="padding-top: 10px" v-if="opType == 1">
          <a-col span="6" style="font-weight: bold">
            付款金额合计： {{ payMoneyTotal < 0 ? '-' : '' }}¥{{
              payMoneyTotal < 0 ? String(payMoneyTotal.toFixed(2)).replace('-', '') : payMoneyTotal.toFixed(2)
            }}
          </a-col>
        </a-row>
      </template>
    </TableView>
    <div v-if="goujiType == 2 && isEdit">
      <div style="color: rgba(0, 0, 0, 0.85); font-weight: 500; font-size: 16px; margin: 10px 0">已选商品</div>
      <!-- 已选商品 -->
      <TableView
        ref="tableView"
        v-if="refresh"
        :showCardBorder="false"
        :tab="tab2"
        :columns="opType == 1 ? columns2 : columnsOperate"
        :dataList="dataSource2"
        @actionClick="actionClick"
      >
        <template slot="bottomView">
          <a-row style="padding-top: 20px" v-if="opType == 2 && total">
            <a-col span="6" style="font-weight: bold">
              金额合计： {{ total.one < 0 ? '-' : '' }}¥{{
                total.one < 0 ? String(total.one).replace('-', '') : total.one
              }}
            </a-col>
            <a-col span="6" style="font-weight: bold">
              税额合计： {{ total.two < 0 ? '-' : '' }}¥{{
                total.two < 0 ? String(total.two).replace('-', '') : total.two
              }}
            </a-col>
            <a-col span="6" style="font-weight: bold">
              含税金额合计： {{ total.three < 0 ? '-' : '' }}¥{{
                total.three < 0 ? String(total.three).replace('-', '') : total.three
              }}
            </a-col>
          </a-row>
        </template>
      </TableView>
    </div>

    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel">取消</a-button>
      <a-button :disabled="confirmLoading" @click="hanleOK" type="primary" v-if="isEdit">保存</a-button>
    </a-row>
  </drag-modal>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { ListMixin } from '@/mixins/ListMixin'
import { postAction } from '@/api/manage'
import DragModal from '@/components/DragModal'
export default {
  name: 'InvoiceCheckingModal',
  mixins: [ListMixin],
  components: {DragModal,JEllipsis},
  props: {
    /**
     * 类型 1 申请付款 2 上传发票
     */
    opType: {
      type: Number,
      default: 2,
    },
  },
  data() {
    return {
      title: '',
      visible: false,
      confirmLoading: false,
      tab: {
        bordered: true, //是否显示表格的边框
        rowKey: 'DocumentDetailId',
        selectType: 'checkbox',
        // showPagination: false,
      },
      tab2: {
        bordered: true, //是否显示表格的边框
        rowKey: 'DocumentDetailId',
        showPagination: false,
      },
      invoice: null,
      isEdit: false,
      httpHead: 'P36009',
      refresh: true,
      isHanleOK: false,
      isChangeOk: false,
      invoiceAmount: '',
      oldList: [], //原始数据
      old2List: [], //下面的原始数据
      total: {
        one: 0,
        two: 0,
        three: 0,
      },
      showOneData: {},
      dataSource: [],
      dataSource2: [],
      dataSourceOld: [],
      payMoneyTotal: 0, //付款金额合计
      goujiType: 1, //1按金额勾稽 2按商品勾稽
      goujiNumType: 0,
      GoodsScaleType: 2,
      queryParam: {},
      id: '',
      url: {
        list: '',
        sureUrl: '/{v}/PurchaseRemittedOrder/SaveAmountInvoiceVerification', //发票确认勾稽
        sureUrlPay: '/{v}/PurchaseRemittedOrder/SaveAmountPurchaseRemittedVerification', //付款确认勾稽
        saveUrl: '/{v}/PurchaseRemittedOrder/SaveInvoiceVerification', //保存
        saveUrlPay: '/{v}/PurchaseRemittedOrder/SaveTempPurchaseRemittedVerification', //付款保存
      },
    }
  },
  computed: {
    //发票
    columnsBase() {
      return [
        {
          title: '序号',
          dataIndex: 'Order',
          scopedSlots: { customRender: 'Order' },
          ellipsis: true,
          fixed: 'left',
          width: 60,
        },
        {
          title: '单据编号',
          dataIndex: 'DocumentNo',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          fixed: 'left',
          width: 150,
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessEventTypeStr',
          scopedSlots: { customRender: 'component' },
          width: 120,
          ellipsis: true,
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 150,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 120,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 80,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 150,
        },
        // {
        //   title: '销货对抵金额',
        //   dataIndex: 'OffsetAmount',
        //   scopedSlots: { customRender: 'price' },
        //   ellipsis: true,
        //   width: 150,
        // },
        {
          title: '未开票数量',
          dataIndex: 'UninvoicedQuantity',
          scopedSlots: { customRender: 'number' },
          ellipsis: true,
          width: 120,
        },
        {
          title: '单价',
          dataIndex: 'Price',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '金额',
          dataIndex: 'Amount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '税率',
          dataIndex: 'PurchaseTaxRate',
          width: 80,
          scopedSlots: { customRender: 'percent' },
          ellipsis: true,
        },

        // fixed: this.isEdit ? 'right' : '',
        {
          title: '税额',
          dataIndex: 'TaxAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '含税单价',
          dataIndex: 'TaxIncludedPrice',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '含税金额',
          dataIndex: 'TaxIncludedAmount',
          width: 150,
          precision: 2,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ]
    },
    columnsJisuan() {
      return [
        {
          title: '本次开票数量',
          dataIndex: 'CurrentInvoiceQuantity',
          fixed: this.isEdit ? 'right' : '',
          min: -*********,
          scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'number' },
          precision: 3,
          onBlur: (val, dataIndex, record, index) => this.onCurrentInvoiceQuantityChange(val, dataIndex, record, index),
          onEnter: (val, dataIndex, record, index) =>
            this.onCurrentInvoiceQuantityChange(val, dataIndex, record, index),
          ellipsis: true,
          width: 120,
        },
        {
          title: '税差',
          dataIndex: 'TaxDifference',
          width: 100,
          precision: 2,
          fixed: this.isEdit ? 'right' : '',
          min: -*********,
          scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'component' },
          onBlur: (val, dataIndex, record, index) => this.onTaxDifferenceChange(val, dataIndex, record, index),
          onEnter: (val, dataIndex, record, index) => this.onTaxDifferenceChange(val, dataIndex, record, index),
          ellipsis: true,
        },
        {
          title: '尾差',
          dataIndex: 'TailAdjustment',
          width: 100,
          fixed: this.isEdit ? 'right' : '',
          precision: 2,
          min: -*********,
          scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'component' },
          onBlur: (val, dataIndex, record, index) => this.onTailAdjustmentChange(val, dataIndex, record, index),
          onEnter: (val, dataIndex, record, index) => this.onTailAdjustmentChange(val, dataIndex, record, index),
          ellipsis: true,
        },
      ]
    },
    columnsJisuan2() {
      return [
        {
          title: '本次开票数量',
          dataIndex: 'CurrentInvoiceQuantity',
          fixed: this.isEdit ? 'right' : '',
          min: -*********,
          scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'number' },
          precision: 3,
          onBlur: (val, dataIndex, record, index) =>
            this.onCurrentInvoiceQuantityChange(val, dataIndex, record, index, 'choose'),
          onEnter: (val, dataIndex, record, index) =>
            this.onCurrentInvoiceQuantityChange(val, dataIndex, record, index, 'choose'),
          ellipsis: true,
          width: 120,
        },
        {
          title: '税差',
          dataIndex: 'TaxDifference',
          width: 100,
          precision: 2,
          fixed: this.isEdit ? 'right' : '',
          min: -*********,
          scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'component' },
          onBlur: (val, dataIndex, record, index) =>
            this.onTaxDifferenceChange(val, dataIndex, record, index, 'choose'),
          onEnter: (val, dataIndex, record, index) =>
            this.onTaxDifferenceChange(val, dataIndex, record, index, 'choose'),
          ellipsis: true,
        },
        {
          title: '尾差',
          dataIndex: 'TailAdjustment',
          width: 100,
          fixed: this.isEdit ? 'right' : '',
          precision: 2,
          min: -*********,
          scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'component' },
          onBlur: (val, dataIndex, record, index) =>
            this.onTailAdjustmentChange(val, dataIndex, record, index, 'choose'),
          onEnter: (val, dataIndex, record, index) =>
            this.onTailAdjustmentChange(val, dataIndex, record, index, 'choose'),
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtns: [
            {
              name: '移除',
              icon: '',
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ]
    },
    columns() {
      let array = [...this.columnsBase, ...this.columnsJisuan]
      return array
    },
    columnsOperate() {
      let array = [...this.columnsBase, ...this.columnsJisuan2]
      return array
    },
    //付款
    columns2() {
      return [
        {
          title: '单据编号',
          dataIndex: 'DocumentNo',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          fixed: 'left',
          width: 150,
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessEventTypeStr',
          scopedSlots: { customRender: 'component' },
          width: 150,
          ellipsis: true,
        },
        {
          title: '采购人',
          dataIndex: 'PurchasingUserName',
          scopedSlots: { customRender: 'component' },
          width: 120,
          ellipsis: true,
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 150,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 150,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 200,
        },
        // 单据类型为采购入款的显示【原单入库数量】、【原单剩余可销库存】，其他单据都用“-”显示
        {
          title: '原单入库数量',
          dataIndex: 'OriginalStockQuantity',
          // scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 120,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text || ' -- ') } />,
              attrs: {},
            }
            return record.BusinessEventType == 2 ? obj : ' -- '
          },
        },
        {
          title: '原单剩余可销库存',
          dataIndex: 'RemainingSalesStock',
          // scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 150,
          customRender: (text, record, index) => {
            const obj = {
              children:  <j-ellipsis value={String(text || ' -- ') } />,
              attrs: {},
            }
            return record.BusinessEventType == 2 ? obj : ' -- '
          },
        },
        // 单据类型为采购入库的显示【近30天销量】，近30天的销量为当前时间的前30天的销量
        {
          title: '近30天销量',
          dataIndex: 'SalesInLast30Days',
          // scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 120,
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={String(text || ' -- ') } />,
              attrs: {},
            }
            return record.BusinessEventType == 2 ? obj : ' -- '
          },
        },
        {
          title: '剩余未付款金额',
          dataIndex: 'UnSettlementAmount',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
          width: 150,
        },
        {
          title: '付款金额',
          dataIndex: 'TaxIncludedAmount',
          width: 100,
          precision: 2,
          min: -Infinity,
          fixed: this.isEdit ? 'right' : '',
          scopedSlots: { customRender: this.isEdit ? 'inputNumber' : 'price' },
          onBlur: this.onPayMoneyChange,
          onEnter: this.onPayMoneyChange,
          ellipsis: true,
        },
      ]
    },
  },
  watch: {
    dataSource2(val) {
      if (val) {
        this.calculateTotalAmount()
      }
    },
  },
  mounted() {},
  created() {},
  methods: {
    loadBefore() {
      this.queryParam.id = this.id
    },
    showOne(url, edit, invoice, isGouji, params, isNoVisible, goujiType, type, showInit,key) {
      if (!url) {
        this.$message.warning('参数错误!')
        return
      }
      this.goujiNumType = key === '勾稽范围'? this.goujiNumType : 0
      if(key === '勾稽范围'){
        params.VerificationScope = this.goujiNumType
      }
      params.GoodsScaleType = this.GoodsScaleType
      this.goujiType = goujiType
      this.showOneData = {
        url: url,
        edit: edit,
        invoice: invoice,
        isGouji: isGouji,
        params: params,
      }
      if (!isNoVisible) {
        this.visible = true
      }
      this.invoiceAmount = ''
      this.onClearSelected()
      this.isEdit = edit
      if (invoice) {
        this.invoice = invoice
      }
      if (showInit == 'showInit') {
        this.initInvoiceData()
      }
      console.log(type, showInit)
      params.InvoiceReconciliationType = this.goujiType ? this.goujiType : 1
      this.loadInvoiceData(url, params, isGouji, type, showInit)
    },
    loadInvoiceData(url, params, isGouji, type, showInit) {
      let that = this
      that.loading = true
      postAction(url, params, 'P36009')
        .then((res) => {
          if (res.IsSuccess) {
            if (type == 'init') {
              this.dataSourceOld = res.Data || []
            }
            that.loadInvoiceDataSuccess(res.Data || [], isGouji, showInit)
          } else {
            that.$message.error(res.Msg)
            that.close()
          }
        })
        .finally(() => {
          setTimeout(() => {
            that.loading = false
          }, 1000)
        })
    },
    loadInvoiceDataSuccess(list, isGouji, showInit) {
      if (list) {
        setTimeout(() => {
          this.setDataSource(list)
          if (isGouji) {
            this.setSelected(false, showInit)
          } else if (this.goujiType == 2) {
            this.setSelected()
          } else {
            this.setSelected(true)
          }
        }, 500)
      }
    },
    initInvoiceData() {
      this.queryParam = {}
      this.dataSource2 = []
    },
    /**
     *
     * @param {* 是否能编辑} edit
     * @param {* 商品数据} list
     * @param {* 发票信息} invoice = {
     *            TemporaryId:'',//临时Id
     *            InvoiceCode:'',//发票代码
     *            InvoiceNo:'',  //发票号
     * }
     */
    show(edit, list, invoice, isGouji) {
      this.visible = true

      this.invoiceAmount = ''
      this.onClearSelected()
      this.isEdit = edit

      if (list) {
        this.$nextTick(() => {
          this.setDataSource(list)
          if (isGouji) {
            this.setSelected()
          } else {
            this.setSelected(true)
          }
        })
      }
      if (invoice) {
        this.invoice = invoice
      }
    },
    look(id, amount, isAudit, goujiType) {
      this.goujiType = goujiType
      this.onClearSelected()
      this.invoiceAmount = amount || ''
      this.id = id
      this.isEdit = false
      this.tab.selectType = ''
      this.url.list = isAudit
        ? '/{v}/PurchaseRemittedOrder/GetPurchaseRemittedInvoiceVerificationRecordList' //审核的
        : '/{v}/PurchaseRemittedOrder/GetPurchaseRemittedInvoiceVerificationList' //详情的
      this.queryParam.InvoiceReconciliationType = this.goujiType ? this.goujiType : 1
      this.loadData(1)
      this.visible = true
    },
    // 付款勾稽详情查看
    lookPay(id, isAudit) {
      this.onClearSelected()
      this.isEdit = false
      this.tab.selectType = ''
      this.id = id
      this.url.list = isAudit
        ? '/{v}/PurchaseRemittedOrder/GetPurchaseRemittedInfoVerificationRecordList'
        : '/{v}/PurchaseRemittedOrder/GetPurchaseRemittedInfoVerificationList'
      this.loadData(1)
      this.visible = true
    },
    loadAfter() {
      this.setSelected(true)
    },
    /**
     * 计算总额
     */
    calculateTotalAmount() {
      let one = 0
      let two = 0
      let three = 0
      let array = this.goujiType == 1 ? this.selectionRows : this.dataSource2
      if (!this.isEdit) {
        array = this.selectionRows
      }
      array.forEach((x) => {
        if (x.Amount * 1 >= 0 || x.Amount * 1 < 0) {
          one += x.Amount * 1
        }
        if (x.TaxAmount * 1 >= 0 || x.TaxAmount * 1 < 0) {
          two += x.TaxAmount * 1
        }
        // console.log('X.TaxIncludedAmount', x.TaxIncludedAmount * 1)
        if (x.TaxIncludedAmount * 1 >= 0 || x.TaxIncludedAmount * 1 < 0) {
          three += x.TaxIncludedAmount * 1
        }
      })
      this.total = {
        one: this.getAmountOfMoney(one),
        two: this.getAmountOfMoney(two),
        three: this.getAmountOfMoney(three),
      }
      if (this.opType == 1) {
        this.jiSuanPayMoneyTotal()
      }
    },
    changeGoujiType(key) {
      if (this.goujiType == 1) {
        console.log('showOneData', this.showOneData)
        let url = this.showOneData.url
        let edit = this.showOneData.edit
        let invoice = this.showOneData.invoice
        let isGouji = this.showOneData.isGouji
        let params = this.showOneData.params
        let isNoVisible = true
        delete params.DocumentNo
        delete params.GoodsKey
        this.showOne(url, edit, invoice, isGouji, params, isNoVisible, 1, 'init', 'showInit',key)
      } else if (this.goujiType == 2) {
        let url = this.showOneData.url
        let edit = this.showOneData.edit
        let invoice = this.showOneData.invoice
        let isGouji = this.showOneData.isGouji
        let params = this.showOneData.params
        let isNoVisible = true
        delete params.DocumentNo
        delete params.GoodsKey
        this.showOne(url, edit, invoice, isGouji, params, isNoVisible, 2, 'init', 'showInit')
      }
    },
    changeProductType(key) {
        let url = this.showOneData.url
        let edit = this.showOneData.edit
        let invoice = this.showOneData.invoice
        let isGouji = this.showOneData.isGouji
        let params = this.showOneData.params
        let isNoVisible = true
        delete params.DocumentNo
        delete params.GoodsKey
        this.showOne(url, edit, invoice, isGouji, params, isNoVisible, 1, 'init', 'showInit')
    },
    onSureGJClick() {
      let that = this
      that.loading = true
      let url = that.opType == 1 ? that.url.sureUrlPay : that.url.sureUrl
      postAction(
        url,
        {
          Amount: that.invoiceAmount * 1,
          InvoiceVerificationDtos: that.dataSource,
          VerificationScope: this.goujiNumType,
          GoodsScaleType: this.GoodsScaleType
        },
        that.httpHead
      )
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.setDataSource(res.Data || [])
            that.onClearSelected()
            that.setSelected()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    setDataSource(list) {
      this.oldList = JSON.parse(JSON.stringify(list))
      this.dataSource = list
    },
    setSelected(all, showInit) {
      this.onClearSelected()
      this.dataSource.forEach((x) => {
        let isTrue = all ? true : x.IsSelect
        if (isTrue) {
          this.selectedRowKeys.push(x.DocumentDetailId)
          this.selectionRows.push(x)
        }
      })
      if (showInit == 'showInit') {
        this.dataSource2 = this.selectionRows
        this.old2List = JSON.parse(JSON.stringify(this.dataSource2))
      }
      this.calculateTotalAmount()
    },
    // 发票勾稽  - 计算金额合计
    onSelectChange(sRowKeys, sRows) {
      this.selectedRowKeys = sRowKeys
      this.selectionRows = sRows
      if (this.goujiType == 1) {
        // 重组数据
        this.dataSource.map((item) => {
          item.IsSelect = sRowKeys.includes(item.DocumentDetailId)
        })
      }
      this.calculateTotalAmount()
    },
    // table单选事件
    onOneSelectChange(record, selected, selectedRows) {
      if (this.goujiType == 2 && selected) {
        let index = this.dataSource2.findIndex((item) => {
          return item.DocumentDetailId == record.DocumentDetailId
        })
        if (index <= -1) {
          this.dataSource2.push(record)
          this.old2List = JSON.parse(JSON.stringify(this.dataSource2))
        }
      }
    },
    /**
     * 用户手动选择/取消选择所有列的回调
     * @param {*} selected
     * @param {*} sRowKeys
     * @param {*} sRows
     * @param {*} rowKey
     */
    onSelectAllChange(selected, sRowKeys, sRows, rowKey) {
      console.log('onSelectAllChange', selected)
      this.onClearSelected()
      if (selected == true) {
        this.selectedRowKeys = this.dataSource.map((x) => x.DocumentDetailId)
        this.selectionRows = [].concat(this.dataSource)
      }
      // 重组数据
      this.dataSource.map((item) => {
        item.IsSelect = sRowKeys.includes(item.DocumentDetailId)
      })
      if (this.goujiType == 2) {
        if (this.selectionRows.length == this.dataSourceOld.length) {
          this.dataSource2 = this.selectionRows
        } else if (this.selectionRows.length === 0) {
        } else if (this.selectionRows.length && this.selectionRows.length < this.dataSourceOld.length) {
          var uniqueArr = this.removeDuplicates(this.dataSource2.concat(sRows), 'DocumentDetailId')
          this.dataSource2 = uniqueArr
        }
        this.old2List = JSON.parse(JSON.stringify(this.dataSource2))
      }
      this.calculateTotalAmount()
    },
    removeDuplicates(arr, prop) {
      return arr.filter((obj, index, self) => index === self.findIndex((t) => t[prop] === obj[prop]))
    },
    subDel(record, key, index) {
      this.dataSource2.splice(index, 1)
      this.selectionRows = this.selectionRows.filter((item) => {
        return item.DocumentDetailId != record.DocumentDetailId
      })
      this.selectedRowKeys = this.selectedRowKeys.filter((item) => {
        return item != record.DocumentDetailId
      })
    },
    actionClick(record, key, index) {
      if (key == '移除') {
        let that = this
        this.$confirm({
          title: '你确定要移除当前数据吗?',
          content: '',
          onOk() {
            that.subDel(record, key, index)
          },
          onCancel() {},
        })
      }
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.total = null
      this.oldList = []
      this.old2List = []
      this.dataSource = []
      this.visible = false
    },
    searchQuery() {
      let url = this.showOneData.url
      let edit = this.showOneData.edit
      let invoice = this.showOneData.invoice
      let isGouji = this.showOneData.isGouji
      let type = ''
      let params = {
        ...this.queryParam,
        ...this.showOneData.params,
      }
      if (!this.queryParam.GoodsKey) {
        delete params.GoodsKey
      }
      if (!this.queryParam.DocumentNo) {
        delete params.DocumentNo
      }
      if (!this.queryParam.GoodsKey && !this.queryParam.DocumentNo) {
        type = 'init'
      } else {
        type = ''
      }
      let isNoVisible = true
      this.showOne(url, edit, invoice, isGouji, params, isNoVisible, 2, type)
    },
    searchReset() {
      this.queryParam = {}
      let url = this.showOneData.url
      let edit = this.showOneData.edit
      let invoice = this.showOneData.invoice
      let isGouji = this.showOneData.isGouji
      let params = this.showOneData.params
      let isNoVisible = true
      delete params.GoodsKey
      delete params.DocumentNo
      this.showOne(url, edit, invoice, isGouji, params, isNoVisible, 2, 'init')
    },
    hanleOK() {
      this.isHanleOK = true
      setTimeout(() => {
        this.isHanleOK = false
        if (this.goujiType && this.goujiType == 1) {
          this.jisuan(this.hanleOKSub)
        } else {
          this.hanleOKSub()
        }
      }, 200)
    },
    hanleOKSub() {
      if (this.opType == 2 && (!this.url.saveUrl || this.dataSource.length == 0)) {
        return
      }
      if (this.opType == 1 && (!this.url.saveUrlPay || this.dataSource.length == 0)) {
        return
      }
      let dataList = []
      if (this.selectedRowKeys.length > 0) {
        let dataSourceArray = this.dataSource
        dataSourceArray.forEach((x) => {
          let bool = this.selectedRowKeys.find((key) => x.DocumentDetailId == key)
          if (bool) {
            dataList.push(x)
          }
        })
      }
      if (this.goujiType == 2) {
        dataList = this.dataSource2.length == 0 ? [] : this.dataSource2
      }
      if (dataList.length == 0) {
        this.$message.warning('您未勾选任何数据！')
        return
      }
      let list = []
      if (this.opType == 2) {
        list = dataList.filter((x) => {
          return !x.CurrentInvoiceQuantity // || !x.TaxDifference || x.TailAdjustment
        })
      } else if (this.opType == 1) {
        list = dataList.filter((x) => {
          return !x.TaxIncludedAmount // || !x.TaxDifference || x.TailAdjustment
        })
      }
      if (list && list.length > 0) {
        this.$message.warning('勾选的数据填写不完整！')
        return
      }
      let that = this
      let params = {}
      if (this.opType == 2) {
        params = {
          ...that.invoice,
          InvoiceReconciliationType: this.goujiType,
          PurchaseRemittedInvoiceVerifications: dataList,
        }
      } else if (this.opType == 1) {
        params = {
          TemporaryId: that.invoice.TemporaryId,
          InvoiceReconciliationType: this.goujiType,
          PurchaseRemittedInvoiceVerifications: dataList,
        }
      }
      that.invoice.goujiType = that.goujiType ? that.goujiType : 1
      that.$delete(params, 'Index')
      console.log(params)
      that.confirmLoading = true
      let url = that.opType == 1 ? that.url.saveUrlPay : that.url.saveUrl
      postAction(url, params, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            let num = 0 //勾稽金额
            let num1 = 0 //销货对抵金额
            dataList.forEach((x) => {
              if (x.TaxIncludedAmount >= 0 || x.TaxIncludedAmount < 0) {
                num += x.TaxIncludedAmount * 1
              }
              if (x.OffsetAmount >= 0 || x.OffsetAmount < 0) {
                num1 += x.OffsetAmount * 1
              }
            })
            if (that.opType == 2) {
              that.$emit(
                'ok',
                that.invoice ? that.invoice.Index : -1,
                that.getAmountOfMoney(num, 6),
                that.getAmountOfMoney(num1, 6),
                that.invoice
              )
            } else if (that.opType == 1) {
              // console.log('付款勾稽', this.selectedRowKeys)
              let documentIds = []
              this.selectedRowKeys.forEach((x) => {
                let sItem = this.dataSource.find((item) => item.DocumentDetailId == x)
                if (sItem) {
                  let index = documentIds.findIndex((y) => y == sItem.DocumentId)
                  if (index == -1) {
                    documentIds.push(sItem.DocumentId)
                  }
                }
              })
              // console.log('付款勾稽1', documentIds)
              that.$emit('ok', null, that.getAmountOfMoney(that.payMoneyTotal, 6), documentIds)
            }
            that.handleCancel()
          } else {
            that.$message.error(res.Msg)
            if (res.Data && res.Data.length > 0) {
              that.setDataSource(res.Data)
              that.setSelected()
              that.$message.warning('保存失败，已自动更正数据，请重新提交')
              if (this.goujiType == 2) {
                this.dataSource2 = res.Data
              }
            }
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
    /**
     * 本次开票数量 change事件  修改开票数量要根据初始化公式重新计算如下几个值
     * @param {*} e
     * @param {*} dataIndex
     * @param {*} record
     * @param {*} index
     */
    onCurrentInvoiceQuantityChange(val, dataIndex, record, index, type) {
      let old = type == 'choose' ? this.old2List[index] : this.oldList[index]
      let key = 'CurrentInvoiceQuantity'
      if (!old) return
      let returnBool = false
      if (!record[key] && record[key] != 0) {
        return
      }
      const newVal = record[key] * 1
      //验证输入内容是否符合要求
      switch (record.BusinessEventType) {
        case 4: //退货为负数
          if (newVal >= 0 || newVal < old.UninvoicedQuantity * 1) {
            this.$message.error('采购退货的本次开票数量必须大于等于未开票数量并小于0')
            returnBool = true
          }
          break
        default: //其余情况都是正数
          if (newVal <= 0 || newVal > old.UninvoicedQuantity * 1) {
            this.$message.error('本次开票数量必须大于0并小于等于未开票数量')
            returnBool = true
          }
          break
      }
      if (returnBool) {
        record[key] = old[key]
        return
      }
      if (record[key] == old[key]) {
        return
      }

      if (type == 'choose') {
        this.old2List[index] = record
      } else {
        this.oldList[index] = record
      }
      this.jisuan()
    },
    /**
     * 税差 change事件
     * @param {*} e
     * @param {*} dataIndex
     * @param {*} record
     * @param {*} index
     */
    onTaxDifferenceChange(val, dataIndex, record, index, type) {
      let old = type == 'choose' ? this.old2List[index] : this.oldList[index]
      let key = 'TaxDifference'
      if (!old) return
      if (!record[key] && record[key] != 0) {
        return
      }
      if (record[key] == old[key]) {
        return
      }
      console.log(old[key], record[key])
      if (type == 'choose') {
        this.old2List[index] = record
      } else {
        this.oldList[index] = record
      }
      this.jisuan()
    },
    /**
     * 尾差 change事件
     * @param {*} e
     * @param {*} dataIndex
     * @param {*} record
     * @param {*} index
     */
    onTailAdjustmentChange(val, dataIndex, record, index, type) {
      let old = type == 'choose' ? this.old2List[index] : this.oldList[index]
      let key = 'TailAdjustment'
      if (!old) return
      if (!record[key] && record[key] != 0) {
        return
      }
      if (record[key] == old[key] && old[key] != 0) {
        return
      }
      console.log(old[key], record[key])
      if (type == 'choose') {
        this.old2List[index] = record
      } else {
        this.oldList[index] = record
      }
      this.jisuan()
    },
    // 付款金额 change事件
    onPayMoneyChange(val, dataIndex, record, index) {
      if (!record) {
        return
      }
      if (record.TaxIncludedAmount >= 0) {
        if (record.TaxIncludedAmount > record.UnSettlementAmount) {
          record.TaxIncludedAmount = record.UnSettlementAmount
        }
      } else {
        if (record.UnSettlementAmount < 0 && record.TaxIncludedAmount < record.UnSettlementAmount) {
          record.TaxIncludedAmount = record.UnSettlementAmount
        }
      }
      this.jiSuanPayMoneyTotal()
      this.refreshTable()
    },
    jiSuanPayMoneyTotal() {
      this.payMoneyTotal = 0
      let array = this.goujiType == 2 ? this.dataSource2 : this.selectionRows
      array.map((item) => {
        this.payMoneyTotal += item.TaxIncludedAmount
      })
    },
    /**
     * 统一计算
     * @param {*} record
     * @param {*} index
     */
    jisuan(callback) {
      setTimeout(() => {
        if (this.isHanleOK) {
          return
        }
        if (this.loading) {
          return
        }
        let that = this
        that.loading = true
        // 重组数据
        if (this.goujiType == 1) {
          that.dataSource.map((item) => {
            that.selectedRowKeys.indexOf(item.DocumentDetailId) > -1 ? (item.IsSelect = true) : (item.IsSelect = false)
          })
        } else {
          that.dataSource.map((item) => {
            that.dataSource2.map((rItem) => {
              item.IsSelect = item.DocumentDetailId == rItem.DocumentDetailId ? true : false
            })
          })
        }
        postAction('/{v}/PurchaseRemittedOrder/CalculateTotalTaxIncludedAmount', that.dataSource, that.httpHead)
          .then((res) => {
            if (res.IsSuccess) {
              that.dataSource = res.Data || []
              if (this.goujiType == 2) {
                const arr1 = that.dataSource
                const arr2 = that.dataSource2
                let commonElements = this.findCommonElements(arr1, arr2)
                that.dataSource2.map((item, index) => {
                  commonElements.map((rItem, rIndex) => {
                    if (item.DocumentDetailId == rItem.DocumentDetailId) {
                      that.dataSource2[index] = rItem
                    }
                  })
                })
              }
              that.setSelected()
              that.refreshTable()
              setTimeout(() => {
                callback && callback()
              }, 500)
            } else {
              that.$message.error(res.Message || res.Msg)
            }
          })
          .finally(() => {
            that.loading = false
          })
      }, 120)
    },
    findCommonElements(arr1, arr2) {
      return arr1.filter((item) => arr2.some((item2) => item.DocumentDetailId === item2.DocumentDetailId))
    },
    refreshTable() {
      // this.refresh = false
      // this.$nextTick(() => {
      //   this.refresh = true
      // })
      this.$forceUpdate()
    },
  },
}
</script>
<style lang="less">

</style>
