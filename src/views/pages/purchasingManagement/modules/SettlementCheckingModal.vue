<!--
 * @Author: LP
 * @Description: 预付款-结算勾稽
 * @Date: 2023-12-22 16:59:26
-->
<template>
  <a-modal title="结算勾稽" :width="'75vw'" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" @ok="handleOK" :maskClosable="false" :destroyOnClose="true" okText="保存">
    <div v-if="isEdit">
      <div style="display: flex; flex-direction: row; align-items: center; margin-bottom: 15px">
        <div>
          按金额结算:<a-input-number placeholder="请输入" v-model="inputAmount" style="width: 200px; margin-left: 10px; margin-right: 10px" :max="canUseAmount >= 0 ? canUseAmount : 999999999.99" />
          <a-button type="primary" @click="onSureGJClick" :disabled="!inputAmount" :loading="loading">确认</a-button>
          <span style="padding-left: 50px">可用预付款结算金额：<span style="font-weight: bold">￥{{ canUseAmount }}</span></span>
        </div>
      </div>
    </div>
    <TableView ref="tableView" v-if="refresh" :showCardBorder="false" :tab="tab" :columns="columns" :dataList="dataSource">
      <span slot="currentSettledAmount" slot-scope="{ text, record, index, column }">
        <a-input-number style="width: 100%" v-model="record[column.dataIndex]" placeholder="请输入" :min="
            record['UnTotalSettledAmountRemaining'] == 0
              ? -Infinity
              : record['UnTotalSettledAmountRemaining'] > 0
              ? 0
              : record['UnTotalSettledAmountRemaining']
          " :max="
            record['UnTotalSettledAmountRemaining'] == 0
              ? 999999999
              : record['UnTotalSettledAmountRemaining'] > 0
              ? record['UnTotalSettledAmountRemaining']
              : 0
          " :precision="2" @blur="() => (column.onBlur ? column.onBlur(null, column.dataIndex, record, index) : {})" @pressEnter="(e) => (column.onEnter ? column.onEnter(e, column.dataIndex, record, index) : {})" />
      </span>
      <template slot="bottomView" v-if="dataSource.length > 0">
        <div style="font-weight: bold; padding-top: 20px">结算金额合计： {{ getShowPrice(payMoneyTotal) }}</div>
      </template>
    </TableView>

    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel">取消</a-button>
      <a-button :disabled="dataSource.length == 0" :loading="confirmLoading" @click="handleOK" type="primary" v-if="isEdit">保存</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import { getAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import { postAction } from '@/api/manage'
export default {
  name: 'SettlementCheckingModal',
  mixins: [ListMixin],
  components: {},
  props: {},
  data() {
    return {
      title: '',
      visible: false,
      confirmLoading: false,
      tab: {
        scrollY: '400px',
        customSlot: ['currentSettledAmount'],
        bordered: true, //是否显示表格的边框
        rowKey: 'BusinessOrderDetailId',
        selectType: 'checkbox',
        // showPagination: false,
      },
      invoice: null,
      isEdit: false,
      httpHead: 'P36009',
      refresh: true,
      oldList: [], //原始数据
      canUseAmount: 0, //可用预付款结算金额
      payMoneyTotal: 0, //付款金额合计
      inputAmount: '', //输入的金额
      supplierId: '', //供应商id
      id: '',
      url: {
        list: '',
        getlist: '/{v}/PurchaseSettlement/GetPurchaseSettlementBusinessGoodsDetails', //结算勾稽单据接口
        // getListFalse: '/{v}/PurchaseSettlement/GetUnSavePurchaseSettlementBusinessGoodsDetailsBySupplierId',
        amountUrl: '/{v}/PurchaseSettlement/CreatePurchaseSettlementBusinessGoodsDetailByTotalAmount', // 按金额勾稽获取数据
        save: '/{v}/PurchaseSettlement/SavePurchaseSettlementBusinessGoodsDetail', //保存
        lookAudit: '/{v}/PurchaseSettlement/GetPurchaseSettlementBusinessGoodsDetailAuditList', //审核 查看勾稽
        look: '/{v}/PurchaseSettlement/GetPurchaseSettlementBusinessGoodsDetailsById', //详情 查看勾稽
      },
    }
  },
  computed: {
    columns() {
      return [
        {
          title: '单据编号',
          dataIndex: 'BusinessOrderNo',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          fixed: 'left',
          width: 150,
        },
        {
          title: '对应订单编号',
          dataIndex: 'PurchaseOrderNo',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          fixed: 'left',
          width: 150,
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessEventTypeStr',
          scopedSlots: { customRender: 'component' },
          width: 150,
          ellipsis: true,
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 200,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 150,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 200,
        },
        {
          title: '剩余未结算金额',
          dataIndex: 'UnTotalSettledAmountRemaining',
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
          width: 150,
        },
        {
          title: '结算金额',
          dataIndex: 'CurrentSettledAmount',
          width: 200,
          precision: 2,
          min: -Infinity,
          fixed: this.isEdit ? 'right' : '',
          scopedSlots: { customRender: this.isEdit ? 'currentSettledAmount' : 'price' },
          onEnter: this.onPayMoneyChange,
          onBlur: this.onPayMoneyChange,
          ellipsis: true,
        },
      ]
    },
  },
  mounted() { },
  created() { },
  methods: {
    /**
     * 显示弹窗
     * @param {*是否编辑状态} edit
     * @param {*接口请求参数} params
     * @param {*是否未勾稽} isWGJ
     */
    showOne(edit, params, isWGJ) {
      this.visible = true
      this.supplierId = params ? params.SupplierId : ''
      this.inputAmount = ''
      this.onClearSelected()
      this.isEdit = edit

      this.loadInvoiceData(params, isWGJ)
    },
    /**
     * 加载数据
     * @param {*} url
     * @param {*} params
     */
    loadInvoiceData(params, isWGJ) {
      let that = this
      that.loading = true
      // let request = null
      // if (isWGJ) {
      //   request = postAction(that.url.getlist, params, 'P36009')
      // } else {
      //   request = getAction(that.url.getListFalse, { supplierId: params.SupplierId }, 'P36009')
      // }

      postAction(that.url.getlist, params, 'P36009')
        .then((res) => {
          if (res.IsSuccess) {
            if (res['Data']) {
              that.canUseAmount = res['Data'].TotalPayAmount || 0
              let dataList = res['Data'].GoodsList || []
              that.setDataSource(dataList)
              that.setSelected()
              if (dataList.length > 0) {
                this.calculateTotalAmount()
              }
            } else {
              that.$message.error('无数据')
              that.close()
            }
          } else {
            that.$message.error(res.Msg)
            that.close()
          }
        })
        .finally(() => {
          setTimeout(
            () => {
              that.loading = false
            },
            that.dataSource.length > 200 ? 1000 : 100
          )
        })
    },

    /**
     * 查看勾稽
     * @param {*} id
     * @param {*是否审核} isAudit
     */
    lookPay(id, isAudit) {
      this.onClearSelected()
      this.isEdit = false
      this.tab.selectType = ''
      this.visible = true
      this.loadLookDetail(id, isAudit)
    },
    /**
     * 加载查看勾稽的详细数据
     * @param {*} id
     * @param {*} isAudit
     */
    loadLookDetail(id, isAudit) {
      let that = this
      that.loading = true
      getAction(
        isAudit ? that.url.lookAudit : that.url.look,
        isAudit ? { purchaseSettlementOrderRecordId: id } : { purchaseSettlementOrderId: id },
        that.httpHead
      )
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              that.dataSource = res.Data.GoodsList || []
              that.payMoneyTotal = res.Data.TotalSettlementAmount || 0
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 计算总额
     */
    calculateTotalAmount() {
      let total = 0
      this.selectionRows.map((item) => {
        total += item.CurrentSettledAmount
      })
      console.log('计算金额', total)
      this.payMoneyTotal = total
    },
    /**
     * 按金额结算 确定按钮事件
     */
    onSureGJClick() {
      let that = this
      that.loading = true
      postAction(
        that.url.amountUrl,
        {
          TotalAmount: that.inputAmount * 1,
          GoodsList: that.dataSource,
        },
        that.httpHead
      )
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.setDataSource(res.Data || [])
            that.onClearSelected()
            that.setSelected()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    setDataSource(list) {
      // this.oldList = JSON.parse(JSON.stringify(list))
      this.dataSource = list
    },
    setSelected(all) {
      this.onClearSelected()
      this.dataSource.forEach((x) => {
        let isTrue = all ? true : x.CurrentSettledAmount != 0
        if (isTrue) {
          this.selectedRowKeys.push(x.BusinessOrderDetailId)
          this.selectionRows.push(x)
        }
      })
      this.calculateTotalAmount()
    },
    /**
     * 发票勾稽  - 计算金额合计
     **/
    onSelectChange(sRowKeys, sRows) {
      this.selectedRowKeys = sRowKeys
      this.selectionRows = sRows
      // console.log('selectedRowKeys', this.selectedRowKeys)
      // 重组数据
      this.dataSource.map((item) => {
        item.IsSelect = sRowKeys.includes(item.BusinessOrderDetailId)
      })

      // console.log('NBA', this.dataSource)
      this.calculateTotalAmount()
    },
    /**
     * 用户手动选择/取消选择所有列的回调
     * @param {*} selected
     * @param {*} sRowKeys
     * @param {*} sRows
     * @param {*} rowKey
     */
    onSelectAllChange(selected, sRowKeys, sRows, rowKey) {
      console.log('onSelectAllChange', selected)
      this.onClearSelected()
      if (selected == true) {
        this.selectedRowKeys = this.dataSource.map((x) => x.BusinessOrderDetailId)
        this.selectionRows = [].concat(this.dataSource)
      }
      // 重组数据
      this.dataSource.map((item) => {
        item.IsSelect = sRowKeys.includes(item.BusinessOrderDetailId)
      })
      this.calculateTotalAmount()
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.oldList = []
      this.inputAmount = ''
      this.confirmLoading = false
      this.canUseAmount = 0
      this.dataSource = []
      this.visible = false
    },
    /**
     * 保存数据
     */
    handleOK() {
      let dataList = []
      if (this.selectedRowKeys.length > 0) {
        this.dataSource.forEach((x) => {
          let bool = this.selectedRowKeys.find((key) => x.BusinessOrderDetailId == key)
          if (bool) {
            dataList.push(x)
          }
        })
      }
      if (dataList.length == 0) {
        this.$message.warning('您未勾选任何数据！')
        return
      }
      let list = dataList.filter((x) => {
        return !x.CurrentSettledAmount
      })

      if (list && list.length > 0) {
        this.$message.warning('勾选的数据填写不完整！')
        return
      }

      let that = this
      that.confirmLoading = true
      let params = {
        SupplierId: this.supplierId,
        GoodsList: dataList,
        TotalAmount: this.payMoneyTotal,
      }

      postAction(that.url.save, params, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            let documentIds = []
            this.selectedRowKeys.forEach((x) => {
              let sItem = this.dataSource.find((item) => item.BusinessOrderDetailId == x)
              if (sItem) {
                let index = documentIds.findIndex((y) => y == sItem.BusinessOrderId)
                if (index == -1) {
                  documentIds.push(sItem.BusinessOrderId)
                }
              }
            })
            that.$emit('checkingOk', res['Data'] || null, documentIds)
            that.handleCancel()
          } else {
            that.$message.error(res.Msg)
            // if (res.Data && res.Data.length > 0) {
            //   that.setDataSource(res.Data)
            //   that.setSelected()
            //   that.$message.warning('保存失败，已自动更正数据，请重新提交')
            // }
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },

    // 付款金额 change事件
    onPayMoneyChange(val, dataIndex, record, index) {
      this.calculateTotalAmount()
    },

    refreshTable() {
      // this.refresh = false
      // this.$nextTick(() => {
      //   this.refresh = true
      // })
      this.$forceUpdate()
    },
  },
}
</script>
<style lang="less">

</style>
