<!--
 * @Author: LP
 * @Description: 新增，编辑备注
 * @Date: 2023/07/05
-->
<template>
  <a-modal
    :title="(isEdit ? '编辑' : '添加') + '备注'"
    :width="600"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <div
      :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff'
      }"
    >
      <a-spin :spinning="confirmLoading">
        <a-row>
          <a-col :md="24">
            <a-table
              :bordered="true"
              ref="table"
              rowKey="Id"
              size="middle"
              :columns="columns"
              :dataSource="Remarks"
              :pagination="false"
              :scroll="{ y: '200' }"
            >
              <!-- 字符串超长截取省略号显示-->
              <span slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </span>
            </a-table>
          </a-col>
          <a-col :md="24">
            <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
              <a-row :gutter="10">
                <a-col :md="24">
                  <a-form-model-item label="备注内容：" prop="Remark">
                    <a-textarea
                      placeholder="请输入"
                      v-model="model.Remark"
                      :maxLength="200"
                      :auto-size="{ minRows: 3, maxRows: 5 }"
                    />
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-form-model>
          </a-col>
        </a-row>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel">取消</a-button>
      <a-button :disabled="confirmLoading" @click="handleOk" type="primary">确认提交</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'AddRemarksToWantToBuyModal',
  components: { JEllipsis },
  mixins: [ListMixin],
  data() {
    return {
      current: 0,
      visible: false,
      model: {
        FeedbackId: '',
        Remark: ''
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 }
      },
      confirmLoading: false,
      Remarks: [],
      httpHead: 'P31121',
      columns: [
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        },
        {
          title: '备注内容',
          dataIndex: 'Remark',
          scopedSlots: { customRender: 'component' },
          ellipsis: true
        }
      ],
      isEdit: false,
      // 表头
      url: {
        detail: '/Feedback/Detail',
        add: '/Feedback/AddRemark',
        update: ''
      }
    }
  },
  props: {},
  computed: {
    rules() {
      return {
        Remark: [{ required: true, message: '请输入!' }]
      }
    }
  },
  mounted() {},
  created() {},
  methods: {
    /**
     * 新增备注
     * @param {提交数据的数据结构，主要是用于传递此界面不展示的字段，如客户审核备注：{CustomerId：'XXXXX'}} mod
     */
    add(id) {
      this.model = {
        FeedbackId: id,
        Remark: ''
      }
      this.Remarks = []
      this.getDetail(id)
      this.isEdit = false
      this.visible = true
    },

    getDetail(id) {
      if (!id) return
      getAction(this.url.detail, { id: id }, this.httpHead).then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (!res.Data) return
        this.Remarks = res.Data.FeedbackRemark || []
      })
    },
    /**
     * 确定
     */
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          let formData = Object.assign(this.model, values)
          that.confirmLoading = true
          let obj = null
          if (that.isEdit) {
            obj = putAction(that.url.update, formData, this.httpHead)
          } else {
            obj = postAction(that.url.add, formData, this.httpHead)
          }
          obj.then(res => {
            that.confirmLoading = false
            if (res.IsSuccess) {
              that.$emit('ok')
              that.$message.success('操作成功！')
              that.close()
            } else {
              that.$message.warning(res.Msg)
            }
          })
        }
      })
    },

    /**
     * 关闭
     * */
    handleCancel() {
      this.close()
    },
    close() {
      this.isEdit = false
      this.confirmLoading = false
      this.visible = false
      this.model = {
        Content: '',
        CustomerId: ''
      }
    }
  }
}
</script>
<style>
.radio-ant-form-item-children .ant-form-item-children {
  display: inline-block;
  width: 100%;
}
.radio-ant-form-item-children .ant-form-item-children .ant-radio-group {
  width: 100%;
}
.not-ant-card-head-title .ant-card-head-title {
  padding: 6px 0;
  color: #1890ff;
}
.not-ant-card-head-title .ant-card-head {
  min-height: inherit;
}
</style>
