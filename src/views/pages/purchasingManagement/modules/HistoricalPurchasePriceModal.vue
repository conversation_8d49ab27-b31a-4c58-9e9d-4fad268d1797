<!--
 * @Description: 查看历史采购价 弹窗
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-11 17:26:17
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-03-31 14:10:24
-->
<template>
  <a-modal :title="title" :width="1000" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :footer="null" cancelText="关闭" :destroyOnClose="true">
    <div>
      <a-spin :spinning="confirmLoading">
        <SimpleSearchArea ref="SimpleSearchArea" :queryCol="8" :searchInput="searchInput" @searchQuery="searchQuery" />
        <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab :cardBordered="false" :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate" />
      </a-spin>
    </div>
  </a-modal>
</template>

<script>
import moment from "moment";
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "HistoricalPurchasePriceModal",
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      title: "查看历史采购价",
      visible: false,
      confirmLoading: false,
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '供应商名称',
          type: 'input',
          value: '',
          key: 'SupplierName',
          placeholder: '请输入供应商名称',
        },
        { name: '批号', type: 'input', value: '', key: 'ProductionBatchNumber', placeholder: '请输入批号' },
      ],
      tab: {
        bordered: true, //是否显示表格的边框
        operateBtn: [

        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        initSortColumn: 'WarehouseInTime',
      },
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购人',
          dataIndex: 'OwnerByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '入库时间',
          dataIndex: 'WarehouseInTime',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 90,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批次号',
          dataIndex: 'BatchNumber',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '有效期',
          dataIndex: 'ValidityPeriod',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 90,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '入库数量',
          dataIndex: 'InboundQuantity',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购价格',
          dataIndex: 'LastPurchasePrice',
          width: 140,
          ellipsis: true,
          sorter: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '每盒返利金额',
          dataIndex: 'RebatePrice',
          width: 140,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      model: {},
      queryCol: 6,
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36009',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '/{v}/PurchaseGoods/QueryGoodsPurchasePriceHistoryList',
      },

    };
  },
  methods: {
    moment,
    show(record) {
      this.model = record || {}
      this.isEdit = false;
      this.visible = true;
      this.queryParam = {
        ErpGoodsId: record.ErpGoodsId,
      }
      this.$nextTick(() => {
        /* 排序参数 */
        this.$refs.table.isorter = {
          column: 'WarehouseInTime',
          order: 'desc',
        }
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    searchQuery() {
      this.queryParam = {
        ErpGoodsId: this.queryParam.ErpGoodsId,
        ...this.$refs.SimpleSearchArea.queryParam,
      }
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 列表操作
    operate(record, type) {

    },
    // 确定
    handleOk() {

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
<style scoped>
.title-text {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
  font-size: 15px;
}
</style>
