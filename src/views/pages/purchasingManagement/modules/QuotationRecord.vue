<template>
  <div>
    <!-- 列表 -->
    <TableNewView
      ref="tableView"
      :table="table"
      :columns="columns"
      :tabDataList="tabDataList"
      :dataList="dataSource"
      @operate="operate"
    >
      <!-- 字符串超长截取省略号显示-->
      <div slot="quotationQuantity" slot-scope="{ text, record }">
        <span @click="goDetail(record)" :title="'点击查看报价明细'" class="quotationQuantityBox">{{ text }}</span>
      </div>
    </TableNewView>

    <!-- 供应商编辑弹窗 -->
    <supplierAddModal ref="supplierAddModal" @update="updateSupplier" />
    <!-- 报价明细 -->
    <QuoteDetailsModal ref="QuoteDetailsModal" />
    <!-- 批量导入 导入报价 -->
    <BatchImportSimpleModal
      :diyStyle="{ width: '80vw' }"
      ref="iBatchImportGoodsModal"
      modalTitle="导入报价"
      :tableColumns="goodsImportColumns"
      :topViewData="model"
      :searchParamsList="searchImportGoodsInput"
      :importConfig="goodsImportConfig"
      :importUrl="importUrl"
      importHttpHead="P36009"
      @ok="(e) => handleBatchImportModalOk(e, 'goods')"
      @downloadTempFileDiy="downExprt"
      :okBefore="beforeImport"
    >
      <div slot="topView">
        <a-form-model ref="form" layout="inline" :rules="rules" :model="model">
          <a-form-model-item label="供应商是否建档：" prop="IsSupplierRegistered">
            <a-radio-group name="radioGroup" v-model="model.IsSupplierRegistered" @change="IsSupplierRegisteredChange">
              <a-radio :value="true"> 已建档 </a-radio>
              <a-radio :value="false"> 未建档 </a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item v-if="model.IsSupplierRegistered" label="供应商名称：" prop="SupplierId">
            <SingleInputSearchView
              placeholder="请选择"
              width="100%"
              :httpParams="{
                pageIndex: 1,
                pageSize: 200,
                AuditStatus: 2,
                IsValid: true,
              }"
              keyWord="KeyWord"
              httpHead="P36003"
              :Url="url.supplierList"
              :value="model.SupplierId"
              :dataKey="{ name: 'Name', value: 'Id' }"
              :name="model.SupplierName"
              :isAbsolute="true"
              @clear="IsSupplierRegisteredChange"
              @change="changeSearchInput"
            />
          </a-form-model-item>
          <a-form-model-item v-else label="供应商名称：" prop="SupplierName">
            <a-input type="text" :maxLength="100" placeholder="请输入" v-model.trim="model.SupplierName"> </a-input>
          </a-form-model-item>
        </a-form-model>
      </div>
    </BatchImportSimpleModal>
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import { PublicMixin } from '@/mixins/PublicMixin' //非必要 公用的 有需要时候用其他mixin也行
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'
const goodsImportColumns = [
  {
    title: '商品编号',
    dataIndex: 'ErpGoodsCode',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '商品名称',
    ellipsis: true,
    width: 200,
    dataIndex: 'ErpGoodsName',
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '规格',
    width: 120,
    dataIndex: 'PackingSpecification',
  },
  {
    title: '生产厂家',
    dataIndex: 'BrandManufacturer',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '批准文号',
    width: 150,
    dataIndex: 'ApprovalNumber',
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '求购数量',
    dataIndex: 'PurchaseQuantity',
    width: 150,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '单价',
    dataIndex: 'QuotationPrice',
    scopedSlots: { customRender: 'price' },
    width: 100,
    ellipsis: true,
  },
  {
    title: '供货数量',
    dataIndex: 'SupplyQuantity',
    width: 100,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '效期优于',
    dataIndex: 'PreferredExpiryDate',
    width: 100,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '异常原因',
    dataIndex: 'ErrorInfo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 80,
    actionBtns: [{ name: '移除', icon: '' }],
    scopedSlots: { customRender: 'action' },
  },
]

export default {
  title: '报价记录',
  name: 'QuotationRecord',
  mixins: [ListMixin, PublicMixin],
  components: {},
  data() {
    return {
      routeQueryParams: null, // 页面路由参数
      table: {
        curStatus: 1,
        tabStatusKey: 'ApproavalStatus',
        operateBtns: [{ name: '导入报价', type: 'primary', icon: '', key: 'importInquiry', id: '' }], //右上角按钮集合
        rowKey: 'Id',
        customSlot: ['quotationQuantity'],
      },
      tabDataList: [],
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '报价数量',
          dataIndex: 'TotalSupplyQuantity',
          scopedSlots: { customRender: 'quotationQuantity' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 200,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '编辑供应商',
              isShow: (record) => {
                return true
              },
            },
            {
              name: '移除',
              icon: '',
              specialShowFuc: (e) => {
                return true
              },
            },
          ],
        },
      ],
      model: {
        SupplierId: '',
        SupplierName: '',
        IsSupplierRegistered: true,
      },
      // 导入的参数
      searchImportGoodsInput: [
        {
          name: '商品', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'GoodsKeyword', //搜索key 必填
          placeholder: '名称/编码',
        },
        {
          name: '生产厂家', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'BrandManufacturerKeyword', //搜索key 必填
          placeholder: '请输入',
        },
      ],
      importUrl: '',
      goodsImportConfig: {
        templateFileName: '四川欣佳能达医药有限公司询价表', //下载模板名字
        ErrorShowKey: 'ErrorShow', //异常信息字段名称
        saveKey: 'BatchId',
        saveUrlType: 'POST', //保存接口请求方式
        saveUrl: '/{v}/PurchaseQuotation/ConfirmImportQuotationAsync', //保存接口
        listQueryKey: 'BatchId', //列表的时候需要用到的字段
        batchRemoveId: '', //批量移除id
        listRemoveType: 'DELETE', //列表删除接口请求方式
        listRemoveUrl: '/{v}/PurchaseQuotation/DeleteImportQuotationDetailAsync', //列表删除 接口
        listRemoveKey: '', //列表删除 参数 key
        listHttpHead: 'P36009',
        listUrl: '/{v}/PurchaseQuotation/GetQuotationImportDetailPageListAsync', //列表的请求接口
        listUrlType: 'GET', //列表接口请求方式
        queryParamStatusKey: 'Status', //列表查询 异常 正常 key
        noLoadData: true, //是否默认弹出时候不加载数据
        importResKey: 'BatchId', //导出完成后返回的key 列表的时候用到
        dataSourceListKey: 'ImportDetailList',
        clearUrlType: 'DELETE',
        clearUrl: '/{v}/PurchaseQuotation/DeleteImportQuotationAsync',
        clearSaveKey: 'BatchId',
        haveDataSourceKey: 'ErpGoodsCode',
        onlySaveOneData: null,
        bottomBtn: [
          { name: '取消', type: '', funType: 'handleCancel' },
          {
            name: '模板下载',
            type: 'primary',
            funType: 'downloadTempFile',
            httpHead: 'P36009',
            urlType: 'GET',
            url: '/{v}/PurchaseQuotation/GetToQuotationOrderFileAsync',
            params: { purchaseInquiryOrderId: '' },
          },
          { name: '确认', type: 'primary', funType: 'handleOk' },
        ],
      },
      goodsImportColumns: goodsImportColumns,
      httpHead: 'P36009',
      url: {
        supplierList: '/{v}/Supplier/GetSuppliersForSelect', //获取供应商接口
        listType: 'GET', //列表接口请求类型
        list: '/{v}/PurchaseQuotation/GetQuotationOrderPageListAsync', //列表数据接口，
        delete: '/{v}/PurchaseQuotation/DeleteQuotationAsync', // 删除列表数据
      },
    }
  },
  computed: {
    rules() {
      return {
        SupplierName: [{ required: true, message: '供应商不能为空', trigger: 'blur' }],
        SupplierId: [{ required: true, message: '供应商不能为空', trigger: 'change' }],
      }
    },
  },
  created() {
    this.routeQueryParams = {
      Id: this.$route.query.Id,
    }
  },

  methods: {
    loadBefore() {
      const routeQueryParams = this.routeQueryParams
      this.queryParam['PurchaseInquiryOrderId'] = routeQueryParams.Id
    },
    loadAfter() {},
    // 供应商选择框改变
    changeSearchInput(val, txt) {
      this.$set(this.model, 'SupplierId', val)
      this.$set(this.model, 'SupplierName', txt)
      this.$refs.form.clearValidate()
    },
    IsSupplierRegisteredChange() {
      this.$set(this.model, 'SupplierId', '')
      this.$set(this.model, 'SupplierName', '')
    },
    // 操作
    operate(record, key, index) {
      console.log(record, key, index)
      if (key == '编辑供应商') {
        this.$refs.supplierAddModal.edit(record)
      } else if (key === 'importInquiry') {
        // 重置表格
        // this.model = this.$options.data().model
        this.model = { IsSupplierRegistered: true }
        const routeQueryParams = this.routeQueryParams
        let queryParam = {
          purchaseInquiryOrderId: routeQueryParams.Id,
        }
        this.importUrl = '/v1/PurchaseQuotation/ImportQuotationAsync?purchaseInquiryOrderId=' + routeQueryParams.Id
        let mbDataIndex = this.goodsImportConfig.bottomBtn.findIndex((item) => {
          return item.name == '模板下载'
        })
        if (mbDataIndex > -1) {
          this.goodsImportConfig.bottomBtn[mbDataIndex].params = {
            purchaseInquiryOrderId: routeQueryParams.Id,
          }
        }
        this.$refs.iBatchImportGoodsModal.show(this.dataSource, queryParam)
      } else if (key == '移除') {
        this.remove(record.Id)
      }
    },
    // 表格单项移除
    remove(Id) {
      var that = this
      this.$confirm({
        title: '提示',
        content: '是否确定要删除该数据？',
        onOk: function () {
          let delObj = null
          delObj = deleteAction(that.url.delete + `?purchaseQuotationId=${Id}`, {}, that.httpHead)
          delObj.then((res) => {
            if (res.IsSuccess) {
              that.$message.success(res.Msg ? res.Msg : '操作成功')
              that.loadData()
            } else {
              that.$message.warning(res.Msg)
            }
          })
        },
      })
    },
    // 报价数量点击跳转报价明细
    goDetail(record) {
      this.$refs.QuoteDetailsModal.show(record)
    },
    // 导入保存
    handleBatchImportModalOk(data, type) {
      console.log(data)
      this.loadData()
    },
    // 自定义下载模板事件
    downExprt(item) {
      const newParams = item.params
      const url = item.url
      this.handleExportXls('四川欣佳能达医药有限公司询价表', 'Get', url, null, this.httpHead, newParams)
    },
    // 导入报价确认的前置事件
    beforeImport() {
      let result = null
      // 触发表单验证
      this.$refs.form.validate((val) => {
        if (val) {
          // 服务端这儿需要特殊处理
          this.model.SupplierId = this.model.SupplierId ? this.model.SupplierId : null
          result = true
        } else {
          result = false
        }
      })
      return result
    },
    // 供应商编辑弹窗回调
    updateSupplier(type) {
      this.loadData()
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep .quotationQuantityBox {
  cursor: pointer;
  color: #1890ff;
}
</style>
