<!--
 * @Description: 安全库存 历史记录 弹窗
 * @Version: 1.0
 * @Author: Harmoni
 * @Date: 2025-03-27 13:48:32
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-03-31 16:44:07
-->

<template>
  <a-modal
    title="历史记录" 
    :width="800" 
    :visible="visible" 
    :maskClosable="false" 
    :destroyOnClose="true"
    @cancel="onClose" 
  >
    <!-- 数据列表 -->
    <YYLTable
      ref="tableRef"
      url="/{v}/PurchaseGoods/GetGoodsSafetyInventoryHistoryAsync"
      httpHead="P36009"
      method="POST"
      rowKey="Id"
      :queryParams="{ goodsSpuId }"
      :columns="columns"
    />
  </a-modal>
</template>

<script>
export default {
  name: 'SafeStockRecordModal',
  data() {
    return {
      visible: false,
      goodsSpuId: null,
      columns: [
        { title: '生成时间', key: 'CreateTime', align: 'center'},
        { title: '可售天数', key: 'SellableDays', align: 'right'},
        { title: '采购人', key: 'PurchaseByName', align: 'center'},
      ]
    }
  },
  methods: {
    show(id) {
      this.goodsSpuId = id
      this.visible = true
    },
    onClose() {
      this.visible = false
    }
  }
}
</script>