<!--
 * @Author: LP
 * @Description: 合同集合 modal
 * @Date: 2023/07/31
-->
<template>
  <a-modal
    :title="title"
    :width="1100"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    @ok="hanleOK"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <!-- 列表 -->
    <TableView :showCardBorder="false" :tab="tab" :columns="columns" :dataList="dataSource" />
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'HTListModal',
  mixins: [ListMixin],
  components: {},
  props: {},
  data() {
    return {
      title: '商品明细',
      visible: false,
      confirmLoading: false,
      tab: {
        bordered: false, //是否显示表格的边框
        rowKey: 'Id',
        selectType: 'radio',
      },
      columns: [
        {
          title: '合同编号',
          dataIndex: 'ContractNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '合同金额',
          dataIndex: 'ContractAmount',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '供应商',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '合同类型',
          dataIndex: 'ContractTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '结算方式',
          dataIndex: 'SettlementMethodStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '合同状态',
          dataIndex: 'ContractStatusStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      url: {
        list: '',
      },
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    /**
     * 显示
     */
    show(list) {
      this.visible = true
      this.dataSource = list || []
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
    hanleOK() {
      if (!this.selectionRows || this.selectionRows.length == 0) {
        this.$message.warning('请选择合同!')
        return
      }
      this.$emit('ok', this.selectionRows)
      this.close()
    },
  },
}
</script>
<style lang="less">

</style>
