<!--
 * @Author: LP
 * @Description: 图片查看弹窗，可指定标题
 * @Date: 2024-01-10 13:47:48
-->
<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :footer="null"
  >
    <ImageViewer :imgList="imgList" title="照片" urlKey="File" :imgIndex="imgIndex" />
  </a-modal>
</template>

<script>
export default {
  name: 'ImageListModal',
  components: {},
  props: {
    title: {
      type: String,
      default: '查看图片',
    },
  },
  data() {
    return {
      visible: false,
      imgList: [],
      imgIndex: 0,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      confirmLoading: false,
      url: {},
    }
  },
  mounted() {},
  created() {},
  methods: {
    show(list, index) {
      this.imgList = list || []
      this.imgIndex = index || 0
      console.log('imag', list)
      this.visible = true
    },
    // 确定
    handleOk() {},
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
  },
}
</script>
