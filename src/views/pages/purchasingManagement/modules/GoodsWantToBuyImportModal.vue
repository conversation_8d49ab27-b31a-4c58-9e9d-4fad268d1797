<!--
 * @Author: LP
 * @Description: 票折modal
 * @Date: 2023/07/11
-->
<template>
  <a-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    @ok="hanleOK"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <div>
      <template>
        <a-divider orientation="left" style="font-size: 16px; font-weight: 600">上传文件</a-divider>
        <a-upload-dragger
          name="file"
          :multiple="false"
          :headers="tokenHeader"
          :action="importExcelUrl"
          @change="onUploadChange"
        >
          <p class="ant-upload-drag-icon">
            <a-icon type="plus" />
          </p>
          <!-- <p class="ant-upload-text">
            点击上传
          </p>-->
          <p class="ant-upload-hint">点击或将导入文档拖拽到这里上传</p>
        </a-upload-dragger>
      </template>

      <a-divider orientation="left" style="font-size: 16px; font-weight: 600">导入预览</a-divider>

      <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

      <!-- 列表 -->
      <TableView
        ref="tableView"
        showCardBorder
        :tabDataList="tabDataList"
        :tab="tab"
        :columns="columns"
        :dataList="dataSource"
        @actionClick="onActionClick"
      >
        <!-- <span slot="images" slot-scope="{ text, record }">
          <template v-if="record.ImageUrls && record.ImageUrls.length > 0">
            <ImageControl
              v-for="(img, index) in getImageUrls(record.ImageUrls)"
              :key="index"
              :src="img || ''"
              :width="30"
              :height="15"
              style="float:left;margin-right: 5px;"
            />
          </template>
          <span v-else>--</span>
        </span>
        <span slot="remark" slot-scope="{ text, record }">
          <j-ellipsis
            :value="
              record.FeedbackRemark && record.FeedbackRemark.length > 0
                ? record.FeedbackRemark.map(v => v.Remark + ' ' + v.CreateByName + ' ' + v.CreateTime + ',').toString()
                : '--'
            "
          />
        </span> -->
      </TableView>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel">取消</a-button>
      <a :href="downloadWDUrl" download="商品求购导入模板" style="margin-right: 10px">
        <a-button :style="{ marginRight: '8px' }" type="primary">下载模板</a-button>
      </a>
      <a-button :disabled="confirmLoading" @click="hanleOK" type="primary">确认</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { ListMixin } from '@/mixins/ListMixin'
import { postAction, apiHead } from '@/api/manage'
export default {
  name: 'GoodsWantToBuyImportModal',
  mixins: [ListMixin],
  components: {},
  props: {},
  data() {
    return {
      title: '导入求购商品',
      visible: false,
      confirmLoading: false,
      searchItems: [
        {
          name: '反馈编号',
          type: SEnum.INPUT,
          key: 'Code',
        },
        {
          name: '客户名称',
          type: SEnum.INPUT,
          key: 'CustomerName',
        },

        {
          name: '联系电话',
          type: SEnum.INPUT,
          key: 'LinkePhone',
        },
      ],
      tabDataList: [
        {
          name: '正常',
          value: 'true',
          count: '',
          key: 'NoReplyNum',
        },
        {
          name: '异常',
          value: 'false',
          count: '',
          key: 'RepliedNum',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        curStatus: 'true',
        tabStatusKey: 'IsReply', //标签的切换关键字
        tabTitles: [],
        operateBtns: [],
        rowKey: 'Id',
      },
      httpHead: 'P31121',
      columns: [
        {
          title: '反馈编号',
          dataIndex: 'Code',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '反馈描述',
          dataIndex: 'Description',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        // {
        //   title: '反馈图片',
        //   dataIndex: 'ImageUrls',
        //   width: 200,
        //   scopedSlots: { customRender: 'images' },
        //   ellipsis: true
        // },
        // {
        //   title: '备注',
        //   dataIndex: 'FeedbackRemark',
        //   width: 200,
        //   scopedSlots: { customRender: 'remark' },
        //   ellipsis: true
        // },
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '联系电话',
          dataIndex: 'LinkPhone',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '回复时间',
          dataIndex: 'PeplyTime',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '移除',
              id: '',
            },
          ],
        },
      ],
      url: {
        list: '',
        submit: '',
        importUrl: '',
      },
    }
  },
  computed: {
    importExcelUrl: function () {
      return apiHead(this.httpHead) + `${this.url.importUrl}`
    },
    downloadWDUrl: function () {
      return apiHead('WEB_URL') + '/商品求购导入模板.xlsx'
    },
  },
  mounted() {},
  created() {},
  methods: {
    show() {
      this.visible = true
    },
    onActionClick(record, key, index) {
      if (key == '移除') {
        this.dataSource.splice(index, 1)
      }
    },
    onUploadChange(info) {
      const status = info.file.status
      if (status === 'done') {
        if (info.file.response.IsSuccess) {
          this.loadData(1)
        } else if (!info.file.response.IsSuccess && info.file.response.Msg) {
          this.$message.warning(info.file.response.Msg)
        }
      }
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
    hanleOK() {
      this.close()
    },
    saveData(data) {
      let that = this
      that.confirmLoading = true
      data.PurchaseOrderId = that.orderId
      postAction(that.url.submit, data, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.$emit('ok')
            that.handleCancel()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },
  },
}
</script>
<style lang="less">

</style>
