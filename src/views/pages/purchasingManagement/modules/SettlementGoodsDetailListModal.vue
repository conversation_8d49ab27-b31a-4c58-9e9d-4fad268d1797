<!--
 * @Author: LP
 * @Description: 商品明细
 * @Date: 2023-12-25 15:42:15
-->
<template>
  <a-modal
    :title="title"
    :width="'90vw'"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    @ok="hanleOK"
    :maskClosable="false"
    :destroyOnClose="true"
    :footer="null"
  >
    <!-- 列表 -->
    <TableView ref="tableView" :showCardBorder="false" :tab="tab" :columns="columns" :dataList="dataSource" />
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'SettlementGoodsDetailListModal',
  mixins: [ListMixin],
  components: {},
  props: {},
  data() {
    return {
      title: '商品明细',
      visible: false,
      confirmLoading: false,
      tab: {
        bordered: false, //是否显示表格的边框
        rowKey: 'Id',
      },
      id: '',
      httpHead: 'P36009',
      isAduit: false,
      type: 1, //1采购订单  2采购入库 4采购退货  6采购调价 9采购票折
      url: {
        list: '',
        list1: '/{v}/PurchaseOrder/GetPurchaseOrderGoodsList',
        list2: '/{v}/PurchaseOrder/GetPurchaseInventoryGoodsList',
        list4: '/{v}/PurchaseBusiness/GetPurchaseRefundGoodsList',
        list6: '/{v}/PurchaseBusiness/GetPurchasePriceAdjustmentGoodsList',
        list9: '/{v}/PurchaseBusiness/GetPurchaseInvoiceDiscountGoodsList',
      },
    }
  },
  computed: {
    columns() {
      const basicList = [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          fixed: 'left',
          width: 200,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 150,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 200,
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
      ]
      const hsjjList = [
        {
          title: '含税进价',
          dataIndex: this.type == 2 ? 'TaxIncludedPrice' : 'TaxIncludedPurchasePrice',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ]
      const pyhList = [
        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '有效期',
          dataIndex: this.type == 2 ? 'ValidityPeriod' : 'ExpirationDate',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        ...hsjjList,
      ]
      const hsList = [
        {
          title: '新含税进价',
          dataIndex:
            this.type == 6 || this.type == 9 ? 'NewTaxIncludedPurchasePrice' : 'AdjustedTaxIncludedPurchasePrice',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '含税进价差价',
          dataIndex: 'TaxIncludedPurchasePriceDifference',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '差价小计',
          dataIndex: 'TaxIncludedPurchasePriceDifferenceTotal',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ]
      const hsjjxjList = [
        {
          title: '含税金额',
          dataIndex: this.type == 2 ? 'TaxIncludedAmount' : 'TaxIncludedPurchasePriceTotal',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ]

      const auditList = this.isAduit
        ? [
            {
              title: '历史最低采购进价',
              dataIndex: 'HistoryLowestPurchasePrice',
              width: 200,
              scopedSlots: { customRender: 'component' },
              ellipsis: true,
            },
            {
              title: '可销库存',
              dataIndex: 'AvailableStock',
              width: 150,
              scopedSlots: { customRender: 'component' },
              ellipsis: true,
            },
          ]
        : []

      if (this.type == 1) {
        //采购订单
        return [
          ...basicList,
          ...hsjjList,
          ,
          {
            title: '采购数量',
            dataIndex: 'PurchaseQuantity',
            width: 100,
            scopedSlots: { customRender: 'number' },
            ellipsis: true,
          },
          ...hsjjxjList,
          ...auditList,
        ]
      } else if (this.type == 2) {
        //采购入库
        return [
          ...basicList,
          ...pyhList,
          {
            title: '入库数量',
            dataIndex: 'InboundQuantity',
            width: 100,
            scopedSlots: { customRender: 'number' },
            ellipsis: true,
          },
          ...hsjjxjList,
          ...auditList,
        ]
      } else if (this.type == 9) {
        //采购票折
        return [
          ...basicList,
          ...pyhList,
          {
            title: '票折数量',
            dataIndex: 'InvoiceDiscountQuantity',
            width: 100,
            scopedSlots: { customRender: 'number' },
            ellipsis: true,
          },
          ...hsList,
        ]
      } else if (this.type == 6) {
        //采购调价
        return [
          ...basicList,
          ...pyhList,
          {
            title: '差价调整数量',
            dataIndex: 'AdjustmentQuantity',
            width: 100,
            scopedSlots: { customRender: 'number' },
            ellipsis: true,
          },
          ...hsList,
        ]
      } else if (this.type == 4) {
        //采购退货
        return [
          ...basicList,
          ...hsjjList,
          {
            title: '退货数量',
            dataIndex: 'RefundQuantity',
            width: 150,
            scopedSlots: { customRender: 'component' },
            ellipsis: true,
          },
          // {
          //   title: '退货出库数量',
          //   dataIndex: 'StockOutQuantity',
          //   width: 150,
          //   scopedSlots: { customRender: 'component' },
          //   ellipsis: true,
          // },
          {
            title: '退货含税小计',
            dataIndex: this.type == 4 ? 'TaxIncludedPurchaseAmount' : 'stockOutAmount',
            width: 200,
            precision: 6,
            scopedSlots: { customRender: 'price' },
            ellipsis: true,
          },
        ]
      }
    },
  },
  mounted() {},
  created() {},
  methods: {
    /**
     * 显示
     * @param {* 查询数据id} id
     * @param {* 类型 1采购订单  2采购入库 6采购退货  8采购调价 9采购票折} type
     */
    show(id, type, audit) {
      this.type = type
      this.isAduit = audit
      this.id = id
      this.url.list = this.url['list' + type]
      this.loadData(1)
      this.visible = true
    },
    loadBefore() {
      this.queryParam['id'] = this.id
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
    hanleOK() {
      this.close()
    },
  },
}
</script>
<style lang="less">

</style>
