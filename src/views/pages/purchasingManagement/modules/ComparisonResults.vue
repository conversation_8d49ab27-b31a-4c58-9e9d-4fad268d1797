<!-- 比价结果 -->
<template>
  <div>
    <a-form>
      <a-row :gutter="10">
        <a-col :md="queryCol">
          <a-form-item label="商品" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input style="width: 100%" :maxLength="100" placeholder="请输入商品名称/编号" v-model.trim="queryParam.GoodsKey"></a-input>
          </a-form-item>
        </a-col>
        <a-col :md="queryCol">
          <a-form-item label="生产厂家" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-input
              style="width: 100%"
              :maxLength="100"
              placeholder="请输入生产厂家"
              v-model.trim="queryParam.BrandManufacturer"
            ></a-input>
          </a-form-item>
        </a-col>
        <a-col style="text-align: right" :md="12">
          <span class="table-page-search-submit-btns">
            <a-button type="primary" icon="search" @click="search">查询</a-button>
            <a-button @click="reseteSearch">重置</a-button>
            <a-button icon="download" @click="exportPriceComparisonResults">导出</a-button>
            <a-button @click="refresh">刷新</a-button>
            <YYLButton
              menuId="d691c1d8-c8d3-4bb4-9b93-3ad33422a735"
              text="快捷下单"
              type="primary"
              @click="quickOrdering"
            />
          </span>
        </a-col>
      </a-row>
    </a-form>
    <a-spin :spinning="confirmLoading">
      <!-- 比价表格 -->
      <ComparisonTable
        :tab="tab"
        :dataList="dataList"
        :columns="columns"
        :supplierQuotationChooseList.sync="supplierQuotationChooseList"
        :tableChooseList.sync="tableChooseList"
        :sign="'PurchaseQuotationDetailId'"
        :listKeyName="'GoodsPurchaseQuotationList'"
        @selectedRows="
          (selectedRowKeys, selectedRows, onlyAllChooseNowPage) =>
            handleSelectedRows(selectedRowKeys, selectedRows, onlyAllChooseNowPage)
        "
      ></ComparisonTable>
    </a-spin>
    <!-- 快捷下单 -->
    <QuickOrdering ref="QuickOrdering" />
  </div>
</template>

<script>
import { getAction, putAction, postAction } from '@/api/manage'
import { filterObj } from '@/utils/util'
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'ComparisonResults',
  components: {},
  mixins: [ListMixin],
  props: {},
  data() {
    return {
      queryParam: {},
      confirmLoading: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      queryCol: 6,
      httpHead: 'P36009',
      // 比价表格列表基础数据
      dataList: [],
      // 比价表格列名
      columns: [
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          align: 'center',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          align: 'center',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          ellipsis: true,
          align: 'center',
          width: 150,
          dataIndex: 'PackingSpecification',
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 150,
          align: 'center',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批准文号',
          ellipsis: true,
          align: 'center',
          width: 150,
          dataIndex: 'ApprovalNumber',
        },
        {
          title: '平均参考成本',
          width: 150,
          align: 'center',
          dataIndex: 'AverageCost',
        },
        {
          title: '末次进价',
          width: 150,
          align: 'center',
          dataIndex: 'LastPurchasePrice',
        },
        {
          title: '供应商报价',
          width: 200,
          align: 'center',
          dataIndex: 'GoodsPurchaseQuotationList',
          scopedSlots: { customRender: 'priceList' },
        },
      ],
      supplierQuotationChooseList: [], // 供应商报价勾选集合ids
      tableChooseList: [], // 表格勾选需导出集合ids
      url: {
        listType: 'POST',
        list: '/{v}/PurchaseInquiryOrder/ComparePrice', // 获取比价结果
        exportUrl: '/{v}/PurchaseInquiryOrder/ComparePriceExport', // 导出比价结果
        checkOrder: '/{v}/PurchaseComparison/CreatePurchaseOrderQuickComparison' // 快捷下单
      },
      tab: {
        rowKey: 'Id',
        rowSelection: {
          type: 'checkbox',
        },
        scrollY: this.$root.tableHeight - 120
      },
      routeQueryParams: null, // 页面路由参数
    }
  },
  computed: {},
  mounted() {
    // this.loadData()
  },
  created() {
    this.routeQueryParams = {
      Id: this.$route.query.Id,
    }
  },
  methods: {
    // 表格勾选回调
    handleSelectedRows(selectedRowKeys, selectedRows, onlyAllChooseNowPage) {
      // console.log('selectedRowKeys   ', selectedRowKeys)
      let dataList = JSON.parse(JSON.stringify(this.dataList))
      let tableChooseList = JSON.parse(JSON.stringify(this.tableChooseList))
      let newIdList = []
      newIdList = dataList.map((item) => {
        return item.Id
      })
      // 先把勾选dis集合中去掉所有当前页的id数据，再添加当前页勾选的数据
      tableChooseList =
        tableChooseList.filter((f) => {
          return !newIdList.includes(f)
        }) || []
      tableChooseList.push(...selectedRowKeys)
      this.tableChooseList = Array.from(new Set(tableChooseList))
      console.log(this.tableChooseList)
    },
    // 导出比价结果
    exportPriceComparisonResults() {
      const { Id } = this.routeQueryParams
      const tableChooseList = JSON.parse(JSON.stringify(this.tableChooseList))
      const dataList = JSON.parse(JSON.stringify(this.dataList))
      const allIdList = dataList.map((item) => {
        return item.Id
      })
      const newParams = {
        PurchaseInquiryOrderId: Id,
        PurchaseInquiryOrderDetailIds: tableChooseList.length > 0 ? [...tableChooseList] : [...allIdList]
      }
      // console.log(newParams)
      this.handleExportXls('比价结果', 'POST', this.url.exportUrl, null, this.httpHead, newParams)
    },
    // 快捷下单按钮点击事件
    quickOrdering() {
      const that = this
      const supplierQuotationChooseList = this.supplierQuotationChooseList
      if(supplierQuotationChooseList.length < 1) {
        that.$message.warning('请先选择供应商报价')
        return false
      }
      let params = {
        PurchaseQuotationDetailIdList: supplierQuotationChooseList
      }
      let requestObj = postAction(this.url.checkOrder, params, this.httpHead)
      this.confirmLoading = true
      requestObj
        .then((res) => {
          this.confirmLoading = false
          if (res.IsSuccess) {
            let {ComparisonBatchNumber, FailureSupplierNames, Suppliers, SpecialSuppliers} = res.Data
            if (FailureSupplierNames.length > 0) {
              const FailureSupplierNamesString = FailureSupplierNames.join('/')
              that.$message.warning(`${FailureSupplierNamesString}供应商还未建档，请先创建供应商再创建订单`)
            }else {
              SpecialSuppliers.map( f => {
                f.IsSpecialOrder= true
              })
              let allList = Suppliers.concat(SpecialSuppliers)
              allList.map(f => {
                const orderType = f.IsSpecialOrder ? 2 : 1
                f.orderType = orderType
                // 因为有相同ID数据，createId用于区分相同ID数据下含特和不含特
                f.createId = f.Id + '&' + (orderType)
              })
              this.$refs.QuickOrdering.show(allList, ComparisonBatchNumber)
            }
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    // 重置查询
    reseteSearch() {
      this.queryParam = {}
      this.search()
    },
    // 刷新查询
    refresh() {
      this.search()
    },
    // 查询表格数据
    search() {
      this.loadData()
    },
    getQueryField() {
      var str = 'id,'
      if (this.columns) {
        this.columns.forEach(function (value) {
          str += ',' + value.dataIndex
        })
      }
      return str
    },
    getQueryParams() {
      const { Id } = this.routeQueryParams
      //获取查询条件
      const defaultParam = {
        PurchaseInquiryOrderId: Id,
        // 这儿写死PageSize无限大默认一次性获取所有商家的数据
        PageIndex: 1,
        PageSize: 99999,
      }
      var param = Object.assign(defaultParam, this.queryParam)
      param.field = this.getQueryField()
      // param.PageIndex = this.ipagination.current
      // param.PageSize = this.ipagination.pageSize
      if (this.disableSort) {
        param.column = '' //禁用排序字段
      }
      let filterObjParams = filterObj(param)

      return filterObjParams
    },
    // 加载表格数据
    loadData(arg, callback, loadTab, searchType) {
      // this.ipagination.current = 1
      this.confirmLoading = true
      var params = this.getQueryParams() //查询条件
      let requestObj = postAction(this.url.list, params, this.httpHead)
      requestObj
        .then((res) => {
          this.confirmLoading = false
          if (res.IsSuccess) {
            this.dataList = res.Data
          } else {
            that.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
  },
}
</script>
<style scoped lang="scss">
.table-page-search-submit-btns {
  & > button {
    margin-right: 8px;
  }
}
</style>
