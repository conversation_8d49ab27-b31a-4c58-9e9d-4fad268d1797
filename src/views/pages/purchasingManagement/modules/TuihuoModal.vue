<!--
 * @Author: LP
 * @Description: 退货modal
 * @Date: 2023/07/11
-->
<template>
  <a-modal :title="title" :width="'90vw'" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel2" @ok="hanleOK" :maskClosable="false" :destroyOnClose="true" okText="提交审核">
    <POrderTuihuoView ref="POrderTuihuoView" :opType="3" :model="model" :Loading="loading" />
    <template slot="footer">
      <a-button @click="handleCancel" :disabled="confirmLoading">取消</a-button>
      <YYLButton menuId="" text="保存" type="primary" :loading="confirmLoading" @click="hanleOK(true)" class="ml8" />
      <YYLButton menuId="" text="提交审核" type="primary" :loading="confirmLoading" @click="hanleOK(false)" class="ml8" />
    </template>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'TuihuoModal',
  mixins: [ListMixin],
  components: {},
  props: {},
  data() {
    return {
      title: '申请退货',
      visible: false,
      confirmLoading: false,
      labelCol: {
        xs: { span: 2 },
        sm: { span: 2 },
      },
      wrapperCol: {
        xs: { span: 22 },
        sm: { span: 22 },
      },
      id: '',
      isCopy: false,
      model: {
        ReturnReason: '',
        Remark: '',
        TotalRefundAmount: '',
        GoodsItems: [],
      },
      httpHead: 'P36009',
      url: {
        list: '',
        submit: '/{v}/PurchaseBusiness/CreatePurchaseRefund',
        detail: '/{v}/PurchaseBusiness/GetPurchaseRefundDetail',
      },
    }
  },
  computed: {},
  mounted() { },
  created() { },
  methods: {
    init() {
      this.id = ''
      this.model = {
        ReturnReason: '',
        Remark: '',
        TotalRefundAmount: '',
        GoodsItems: [],
      }
    },
    show(id, isCopy) {
      this.init()

      this.visible = true

      this.id = id
      this.isCopy = isCopy
      if (isCopy) {
        this.loadDetail()
      } else {
        this.url.list = '/{v}/PurchaseOrder/GetPurchaseRefundInventoryOrderDetail?purchaseOrderId=' + this.id
        this.loadData(1)
      }

    },
    loadDetail() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            console.log(res.Data || {})
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    loadAfter() {
      this.model.GoodsItems = this.dataSource
    },
    handleCancel2() {
      if(this.confirmLoading) {
        this.$message.warning('处理中，请稍后再试')
        return false
      }
      this.close()
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
    hanleOK(IsDraft) {
      if (this.$refs.POrderTuihuoView) {
        this.$refs.POrderTuihuoView.getData((data) => {
          if (data) {
            data.IsDraft = IsDraft
            this.saveData(data)
          }
        })
      }
    },
    saveData(data) {
      let that = this
      that.loading = true
      that.confirmLoading = true
      // console.log(that.model.PurchaseOrderId)
      data.PurchaseOrderId = that.orderId
      postAction(that.url.submit, { ...data, PurchaseOrderId: this.isCopy ? that.model.PurchaseOrderId : this.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (data.IsDraft) {
              that.$message.success('操作成功！')
              that.$emit('ok')
              that.handleCancel()
              that.confirmLoading = false
              that.loading = false
              return
            }

            that.createApproval(res.Data)
          } else {
            that.$message.error(res.Msg)
            that.confirmLoading = false
            that.loading = false
          }
        })
        .catch((e) => {
          that.confirmLoading = false
          that.loading = false
        })
      // .finally(() => {
      //   that.loading = false
      //   that.confirmLoading = false
      // })
    },
    /**
     * 创建审核实例
     */
    createApproval(bNo) {
      let that = this
      let params = {
        BussinessNo: bNo,
        Scenes: 11,
        OpratorId: that.getLoginUserId(),
      }
      if (!that.confirmLoading) {
        that.confirmLoading = true
        that.loading = true
      }
      postAction('/{v}/ApprovalWorkFlowInstance/CreateApproval', params, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            that.$message.success('操作成功！')
            that.$emit('ok')
            that.handleCancel()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .catch((e) => {
          that.confirmLoading = false
          that.loading = false
        })
        .finally(() => {
          that.confirmLoading = false
          that.loading = false
        })
    },
  },
}
</script>
<style lang="less">

</style>
