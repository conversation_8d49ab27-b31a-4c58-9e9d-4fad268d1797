<!--
 * @Author: LP
 * @Description: 选择查询商品
 * @Date: 2023/07/05
-->
<template>
  <a-modal
    :title="title"
    :width="1100"
    :visible="visible"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    :destroyOnClose="true"
    :footer="null"
  >
    <a-table
      :bordered="true"
      ref="goodsTable"
      rowKey="ErpGoodsId"
      size="middle"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      @change="handleTableChange"
      :row-class-name="
        (record, index) => (record.IsCanSelect && index == curIndex ? 'green' : !record.IsCanSelect ? 'gray' : null)
      "
      :scroll="{ y: 400, x: '100%' }"
      :customRow="onCustomRow"
    >
      <!-- 字符串超长截取省略号显示-->
      <span slot="component" slot-scope="text">
        <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
        <j-ellipsis :value="text" v-else />
      </span>
      <span slot="action" slot-scope="text, record, index, column">
        <div class="rflex" style="height: 30px; width: 90px; overflow: hidden">
          <a-button type="link" :disabled="!record.IsCanSelect" @click="onChoiceClick(record)">选择</a-button>
          <a-input :ref="'input' + index" style="width: 1px; height: 1px; opacity: 0" />
        </div>
      </span>
    </a-table>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'SelectGoodsBySearchListModal',
  mixins: [ListMixin],
  components: { JEllipsis },
  props: {},
  data() {
    return {
      title: '选择商品',
      visible: false,
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 200,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 150,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 200,
        },
        {
          title: '提示',
          dataIndex: 'Tips',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
        },
      ],
      curIndex: 0,
      httpHead: 'P36006',
      loadParams: null,
      btnText: '选择',
      url: {
        list: '',
      },
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    loadBefore() {
      if (this.loadParams) {
        this.queryParam = { ...this.loadParams }
      }
    },
    loadAfter() {
      if (this.dataSource && this.dataSource.length > 0) {
      }
      this.curIndex = this.dataSource.findIndex((x) => {
        return x.IsCanSelect
      })

      console.log('show', this.curIndex)
    },
    show(param) {
      if (param) {
        this.url.list = '/{v}/GoodsPurchase/GetPurchaseGoodsList'
        this.loadParams = param

        document.onkeydown = (e) => {
          this.onKeyDownClick(e)
        }
        this.loadData(1)
        this.visible = true
      }
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.url.list = ''
      this.visible = false
    },
    onCustomRow(record, index) {
      return {
        onClick: (e) => {
          console.log(e)
        },
      }
    },
    onKeyDownClick(e) {
      // console.log('curIndex', this.curIndex)
      if (e.key == 'ArrowDown') {
        if (this.curIndex < this.dataSource.length - 1) {
          this.curIndex++
          // if (this.curIndex > 7) {
          this.$nextTick(() => {
            this.$refs['input' + this.curIndex].focus()
          })
          // }

          console.log('down = ' + this.curIndex, this.ipagination)
        } else if (this.curIndex == this.dataSource.length - 1) {
          let pageNum = this.ipagination.total / this.ipagination.pageSize
          if (this.curIndex == this.ipagination.pageSize - 1 && this.ipagination.current < pageNum) {
            this.ipagination.current++
            console.log('curIndex = ' + this.curIndex + ' 下一页')
            this.loadData()
          } else {
            console.log('curIndex = ' + this.curIndex + ' 不满足下一页条件', this.ipagination)
          }
        }
      } else if (e.key == 'ArrowUp') {
        if (this.curIndex > 0) {
          this.curIndex--
          // if (this.curIndex < 7) {
          this.$nextTick(() => {
            this.$refs['input' + this.curIndex].focus()
          })
          // }
          console.log('up = ' + this.curIndex)
        } else if (this.curIndex == 0) {
          let pageNum = this.ipagination.total / this.ipagination.pageSize
          if (this.ipagination.current > 1) {
            this.ipagination.current--
            console.log('curIndex = ' + this.curIndex + ' 上一页')
            this.loadData()
          } else {
            console.log('curIndex = ' + this.curIndex + ' 不满足上一页条件', this.ipagination)
          }
        }
      } else if (e.key == 'Enter') {
        if (this.visible) {
          if (this.curIndex > -1 && this.curIndex <= this.dataSource.length - 1) {
            let goods = this.dataSource[this.curIndex]
            // if (goods.IsCanSelect) {
            this.$emit('ok', goods)
            this.close()
            // }
          }
        }
      }
    },
    addCurIndex(index) {
      if (index <= this.dataSource.length - 1) {
        if (this.dataSource[index].IsCanSelect) {
          this.curIndex = index
        } else {
          if (index == this.dataSource.length - 1) {
            this.curIndex = -1
          } else {
            this.addCurIndex(index + 1)
          }
        }
      }
    },
    reduceCurIndex(index) {
      if (index >= 0) {
        if (this.dataSource[index].IsCanSelect) {
          this.curIndex = index
        } else {
          if (index == 0) {
            this.curIndex = -1
          } else {
            this.reduceCurIndex(index - 1)
          }
        }
      }
    },
    onChoiceClick(record) {
      this.$emit('ok', record)
      this.close()
    },
  },
}
</script>
<style lang="less">

.gray {
  background-color: rgba(242, 242, 242);
}
.green {
  background-color: rgba(202, 249, 130, 0.5);
}
// /deep/.ant-input {
//   box-sizing: border-box;
//   margin: 0;
//   padding: 0;
//   font-variant: tabular-nums;
//   list-style: none;
//   font-feature-settings: 'tnum';
//   position: relative;
//   display: inline-block;
//   width: 10px;
//   height: 10px;
//   padding: 0px 0px;
//   color: rgba(0, 0, 0, 0.65);
//   font-size: 1px;
//   // line-height: 1.5;
//   background-color: transparent;
//   background-image: none;
//   border: 1px solid red;
//   border-radius: 4px;
//   transition: all 0.3s;
// }
// /deep/.ant-input:focus {
//   border-color: #40a9ff;
//   border-right-width: 1px !important;
//   outline: 0;
//   box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
// }
</style>
