<!--
 * @Description: wenji<PERSON>G<PERSON> 选择协议弹窗
 * @Version: 1.0
 * @Author: wenjingGao
 * @Date: 2025-05-13 14:45:30
 * @LastEditors: wenjingGao
 * @LastEditTime: 2025-05-26 16:10:21
-->
<template>
  <a-modal
    title="选择协议"
    :width="1400"
    :visible="visible"
    :maskClosable="false"
    :destroyOnClose="true"
    @cancel="handleCancel"
    @close="handleCancel"
  >
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <a-tabs v-model="tabActiveKey" @change="changeActiveTab">
      <a-tab-pane v-for="item in tabDataList" :key="item.activeKey" :tab="item.tabTitle"> </a-tab-pane>
    </a-tabs>
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSelect="handleSelectedRows"
      @onSelectAll="onSelectAll"
    />
    <div slot="footer">
      <div>
        <a-button
          type="primary"
          class="mr5"
          @click="handleOk"
          >保存</a-button
        >
        <a-button @click="handleCancel">取消</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { ListMixin } from '@/mixins/ListMixin'
export default {
  name: 'ChooseAgreementsModel',
  mixins: [SimpleMixin, ListMixin],
  data() {
    return {
      visible: false,
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '协议编号',
          type: 'input',
          value: '',
          key: 'AgreementNo',
          defaultVal: '',
          placeholder: '请输入协议编号',
        },
        { name: '甲方', type: 'input', value: '', key: 'PartyFirstKey', defaultVal: '', placeholder: '请输入甲方名称' },
      ],
      tabActiveKey: 3,
      tabDataList: [
        { tabTitle: '短期采购协议', activeKey: 3 },
        { tabTitle: '短期销售协议', activeKey: 2 },
        { tabTitle: '通用协议', activeKey: 4 },
        { tabTitle: '年度协议', activeKey: 1 },
      ],
      linkHttpHead: 'P36007',
      linkUrl: {
        list: '/{v}/PurchaseAgreement/GetInvoiceDiscountAgreementList',
      },
      linkUrlType: 'GET',
      queryParam: {},
      isTableInitData: false, //是否自动加载
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        sortArray: ['IsLimitGoods'],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        rowSelection: {
          type: 'checkbox',
        },
        rowKey: 'AgreementId',
      },
      columns: [
        {
          title: '协议编号',
          dataIndex: 'AgreementNo',
          width: 250,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方',
          dataIndex: 'PartyFirstName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '甲方类型',
          dataIndex: 'PartyFirstTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '应收返利',
          dataIndex: 'TotalRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '取消返利',
          dataIndex: 'CancelRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '增加返利',
          dataIndex: 'TotalAddRebateAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '已认款金额',
          dataIndex: 'TotalRecognitionAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '未认款金额',
          dataIndex: 'RemainingAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
      ],
      selectedRowData: [], //选择的项数据
      selectedRowDataKeys: [], //选择的项数据key
      isInitData: false,
    }
  },
  methods: {
    show() {
      this.visible = true
      this.queryParam = { PurchaseAgreementType: this.tabActiveKey }
      this.$nextTick(() => {
        this.loadDataFun()
      })
    },
    loadDataFun() {
      if (this.$refs.table)
        this.$refs.table.loadDatas(1, this.queryParam)
    },
    searchQuery() {
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      this.queryParam.PurchaseAgreementType = this.tabActiveKey
      this.$nextTick(() => {
        if (this.$refs.table) this.loadDataFun()
      })
    },
    changeActiveTab() {
      // todo 切换tab时清空搜索条件，多选数据不清空
      this.$refs.SimpleSearchArea.resetQueryParam()
      this.queryParam = { PurchaseAgreementType: this.tabActiveKey }
      this.$nextTick(() => {
        this.loadDataFun()
      })
    },
    handleCancel() {
      this.selectedRowData = []
      this.selectedRowDataKeys = []
      this.visible = false
    },
    handleOk() {
      if (!this.selectedRowDataKeys.length) {
        this.$message.warning('请选择数据!')
        return
      }
      this.$emit('ok', this.selectedRowDataKeys, this.selectedRowData)
      this.handleCancel()
    },
    onSelectAll(selected, selectedRows, changeRows) {
      this.selectedRowDataKeys = this.$refs.table.selectedRowKeys
      if(selected) {
        this.selectedRowData = this.selectedRowData.concat(selectedRows)
      } else {
        let changeRowsKeys = []
        changeRows.forEach(item => {
          changeRowsKeys.push(item.AgreementId)
        })
        this.selectedRowData = this.selectedRowData.filter(item => !changeRowsKeys.includes(item.AgreementId))
      }
    },
    handleSelectedRows(record, selected, selectedRows) {
      this.selectedRowDataKeys = this.$refs.table.selectedRowKeys
      if (selected) {
        this.selectedRowData = this.selectedRowData.concat(record)
      } else {
        this.selectedRowData = this.selectedRowData.filter(item => item.AgreementId !== record.AgreementId)
      }
    }
  },
}
</script>
