<!-- 供应商编辑 -->
<template>
  <a-modal
    :title="title"
    :visible="visible"
    :width="800"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <div
      :style="{
        width: '100%',
        padding: '10px 16px',
        background: '#fff',
      }"
    >
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model">
          <a-form-model-item
            label="供应商是否建档："
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            prop="IsSupplierRegistered"
          >
            <a-radio-group name="radioGroup" v-model="model.IsSupplierRegistered" @change="IsSupplierRegisteredChange">
              <a-radio :value="true"> 已建档 </a-radio>
              <a-radio :value="false"> 未建档 </a-radio>
            </a-radio-group>
          </a-form-model-item>
          <a-form-model-item
            v-if="model.IsSupplierRegistered"
            label="供应商名称："
            prop="SupplierId"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <SingleInputSearchView
              :style="{ width: !isEdit ? '80%' : '100%' }"
              placeholder="请选择"
              width="100%"
              :httpParams="{
                pageIndex: 1,
                pageSize: 200,
                AuditStatus: 2,
                IsValid: true,
              }"
              keyWord="KeyWord"
              httpHead="P36003"
              :Url="url.supplierList"
              :value="model.SupplierId"
              :dataKey="{ name: 'Name', value: 'Id' }"
              :name="model.SupplierName"
              :isAbsolute="true"
              @clear="IsSupplierRegisteredChange"
              @change="changeSearchInput"
            />
            <a-button type="link" v-if="!isEdit" @click="createLink" :disabled="haveSave"> 生成链接 </a-button>
          </a-form-model-item>
          <a-form-model-item
            v-else
            label="供应商名称："
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            prop="SupplierName"
          >
            <a-input
              :style="{ width: !isEdit ? '80%' : '100%' }"
              type="text"
              :maxLength="100"
              placeholder="请输入"
              v-model.trim="model.SupplierName"
            >
            </a-input>
            <a-button type="link" v-if="!isEdit" @click="createLink" :disabled="haveSave"> 生成链接 </a-button>
          </a-form-model-item>
          <a-form-model-item
            v-if="!isEdit"
            label="链接地址："
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            prop="address"
          >
            <a-input
              style="width: 80%"
              type="text"
              disabled
              :maxLength="100"
              placeholder="请先生成链接"
              v-model.trim="model.address"
            >
            </a-input>
            <a-button type="link" @click="copyLink"> 复制链接 </a-button>
          </a-form-model-item>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel"> {{ isEdit ? '取消' : '关闭' }}</a-button>
      <a-button
        v-if="isEdit"
        :disabled="confirmLoading"
        :style="{ marginRight: '8px' }"
        @click="handleOk"
        type="primary"
      >保存</a-button
      >
    </a-row>
  </a-modal>
</template>

<script>
import moment from 'moment'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'SupplierAddModal',
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      title: '编辑供应商',
      visible: false,
      isEdit: false,
      model: {
        SupplierId: '',
        SupplierName: '',
        IsSupplierRegistered: true,
      },
      confirmLoading: false,
      httpHead: 'P36009',
      url: {
        supplierList: '/{v}/Supplier/GetSuppliersForSelect', //获取供应商接口
        add: '/{v}/PurchaseQuotation/AddQuotationInvitationUrlAsync', // 生成链接接口
        update: '/{v}/PurchaseQuotation/EditQuoteSupplierAsync', // 获取明细
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      haveSave: false, // 是否已经生成链接了
    }
  },
  computed: {
    rules() {
      return {
        SupplierName: [{ required: true, message: '供应商不能为空', trigger: 'blur' }],
        SupplierId: [{ required: true, message: '供应商不能为空', trigger: 'change' }],
      }
    },
  },
  mounted() {},
  created() {},
  methods: {
    moment,
    add(record, PurchaseInquiryOrderId) {
      this.title = '新增链接'
      this.haveSave = false
      this.isEdit = false
      this.visible = true
      this.model = Object.assign({}, this.model, { PurchaseInquiryOrderId })
    },
    edit(record) {
      this.title = '编辑供应商'
      this.getInfo(record)
      this.isEdit = true
      this.visible = true
    },
    // 获取物流商关联详情
    getInfo(record) {
      this.model = Object.assign({}, record)
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let url = that.url.update
          let formData = {
            ...this.model,
            SupplierId: this.model.SupplierId ? this.model.SupplierId : null,
            PurchaseQuotationId: this.model.Id
          }
          putAction(url, formData, that.httpHead)
            .then((res) => {
              that.confirmLoading = false
              if (res.IsSuccess) {
                this.$message.success('操作成功!')
                this.close()
                this.$emit('update', 2)
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    changeTreeId(selectItem, key) {
      this.model[key] = selectItem.value
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.model = { IsSupplierRegistered: true }
    },
    // 供应商选择框改变
    changeSearchInput(val, txt) {
      this.$set(this.model, 'SupplierId', val)
      this.$set(this.model, 'SupplierName', txt)
      this.$refs.form.clearValidate()
    },
    copyLink() {
      const address = this.model.address
      if (!address) {
        this.$message.warning({
          content: '请您先生成链接',
          duration: 1,
        })
        return false
      }
      var input = document.createElement('input') // 创建input标签
      input.value = this.model.address // 将input的值设置为需要复制的内容
      document.body.appendChild(input) // 添加input标签
      input.select() // 选中input标签
      document.execCommand('copy') // 执行复制
      this.$message.success({
        content: '已复制到粘贴板',
        duration: 2,
      })
      document.body.removeChild(input) // 移除input标签
    },
    // 生成链接
    createLink() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let url = that.url.add
          let formData = {
            ...this.model,
            SupplierId: this.model.SupplierId ? this.model.SupplierId : null,
          }
          console.log(formData)
          postAction(url, formData, that.httpHead)
            .then((res) => {
              that.confirmLoading = false
              if (res.IsSuccess) {
                this.$message.success('操作成功!')
                this.haveSave = true
                this.model.address = res.Data
                this.$emit('update', 1)
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    IsSupplierRegisteredChange() {
      this.$set(this.model, 'SupplierId', '')
      this.$set(this.model, 'SupplierName', '')
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .ant-form .ant-form-item:nth-child(2) {
  & .ant-form-item-children {
    display: flex;
    align-items: center;
  }
}
::v-deep .ant-modal-body {
  overflow: visible;
}
</style>
