<!--
 * @Author: LP
 * @Description: 商品明细 modal
 * @Date: 2023/07/13
-->
<template>
  <a-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    @ok="hanleOK"
    :maskClosable="false"
    :destroyOnClose="true"
    :footer="null"
  >
    <!-- 列表 -->
    <TableView ref="tableView" :showCardBorder="false" :tab="tab" :columns="columnsA.length ? columnsA : columns" :dataList="dataSource" >
      <!-- 价格点击操作 -->
      <span slot="clickPriceA" slot-scope="{ text, record,index, column}">
        <a v-if="text || text == 0" @click="clickA(text, record, index, column)">
          <j-ellipsis :value="text == 0?(''+text):('¥' + text)" :length="50" />
        </a>
        <span v-else>--</span>
      </span>
       <!-- 全部点击操作 -->
       <span slot="clickAl" slot-scope="{text, record, index, column}">
        <a v-if="record.ErpGoodsId" @click="clickA(text, record, index, column)">
          <j-ellipsis :value="(text || text == 0)?('' + text):'--'" :length="50" />
        </a>
        <span v-else>--</span>
      </span>
    </TableView>
    <!-- <template slot="bottomView">
        <a-row style="padding-top: 20px;">
          <a-col span="24" style="font-weight: bold;">
            买赔金额： -¥10000.99
          </a-col>
        </a-row>
      </template>
    </TableView> -->
     <!-- 历史采购价 -->
     <HistoricalPurchasePriceModal ref="HistoricalPurchasePriceModal" />
     <!-- 库存明细 -->
    <InventoryDetailModal ref="InventoryDetailModal" />
    <!-- 本月销售明细 -->
    <SalesThisMonthModal ref="SalesThisMonthModal" />
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'GoodsDetailListModal',
  mixins: [ListMixin],
  components: {JEllipsis},
  props: {
    urlA:{
      type: String,
      default: ''
    },
    columnsA:{
      type: Array,
      default: ()=> []
    }
  },
  data() {
    return {
      title: '商品明细',
      visible: false,
      confirmLoading: false,
      tab: {
        bordered: false, //是否显示表格的边框
        rowKey: 'Id',
        customSlot: ['clickPriceA','clickAl'],
      },
      id: '',
      httpHead: 'P36009',
      isAduit: false,
      type: 1, //1采购订单  2采购入库 4采购退货  6采购调价 9采购票折
      url: {
        list: '',
        list1: '/{v}/PurchaseOrder/GetPurchaseOrderGoodsList',
        list2: '/{v}/PurchaseOrder/GetPurchaseInventoryGoodsList',
        list4: '/{v}/PurchaseBusiness/GetPurchaseRefundGoodsList',
        list6: '/{v}/PurchaseBusiness/GetPurchasePriceAdjustmentGoodsList',
        list9: '/{v}/PurchaseBusiness/GetPurchaseInvoiceDiscountGoodsList',
      },
    }
  },
  computed: {
    columns() {
      const basicList = [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          fixed: 'left',
          width: 150,
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          scopedSlots: { customRender: 'component' },
          width: 150,
          ellipsis: true,
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 150,
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
          width: 100,
        },
      ]
      const hsjjList = [
        {
          title: '含税进价',
          dataIndex: this.type == 2 ? 'TaxIncludedPrice' : 'TaxIncludedPurchasePrice',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ]
      const pyhList = [
        {
          title: '批号',
          dataIndex: 'ProductionBatchNumber',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '有效期',
          dataIndex: this.type == 2 ? 'ValidityPeriod' : 'ExpirationDate',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        ...hsjjList,
      ]
      const hsList = [
        {
          title: '新含税进价',
          dataIndex:
            this.type == 6 || this.type == 9 ? 'NewTaxIncludedPurchasePrice' : 'AdjustedTaxIncludedPurchasePrice',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '含税进价差价',
          dataIndex: 'TaxIncludedPurchasePriceDifference',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '含税进价差价小计',
          dataIndex: 'TaxIncludedPurchasePriceDifferenceTotal',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ]
      const hsjjxjList = [
        {
          title: '含税进价小计',
          dataIndex: this.type == 2 ? 'TaxIncludedAmount' : 'TaxIncludedPurchasePriceTotal',
          width: 150,
          precision: 6,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
      ]

      const auditList = this.isAduit
        ? [
            {
              title: '历史最低采购进价',
              dataIndex: 'HistoryLowestPurchasePrice',
              width: 200,
              scopedSlots: { customRender: 'component' },
              ellipsis: true,
            },
            {
              title: '可销库存',
              dataIndex: 'AvailableStock',
              width: 150,
              scopedSlots: { customRender: 'component' },
              ellipsis: true,
            },
          ]
        : []

      if (this.type == 1) {
        //采购订单
        return [
          ...basicList,
          ...hsjjList,
          ,
          {
            title: '采购数量',
            dataIndex: 'PurchaseQuantity',
            width: 100,
            scopedSlots: { customRender: 'number' },
            ellipsis: true,
          },
          ...hsjjxjList,
          ...auditList,
        ]
      } else if (this.type == 2) {
        //采购入库
        return [
          ...basicList,
          ...pyhList,
          {
            title: '入库数量',
            dataIndex: 'InboundQuantity',
            width: 100,
            scopedSlots: { customRender: 'number' },
            ellipsis: true,
          },
          ...hsjjxjList,
          ...auditList,
        ]
      } else if (this.type == 9) {
        //采购票折
        return [
          ...basicList,
          ...pyhList,
          {
            title: '票折数量',
            dataIndex: 'InvoiceDiscountQuantity',
            width: 100,
            scopedSlots: { customRender: 'number' },
            ellipsis: true,
          },
          ...hsList,
        ]
      } else if (this.type == 6) {
        //采购调价
        return [
          ...basicList,
          ...pyhList,
          {
            title: '调价数量',
            dataIndex: 'AdjustmentQuantity',
            width: 100,
            scopedSlots: { customRender: 'number' },
            ellipsis: true,
          },
          ...hsList,
        ]
      } else if (this.type == 4) {
        //采购退货
        return [
          ...basicList,
          ...hsjjList,
          {
            title: '退货数量',
            dataIndex: 'RefundQuantity',
            width: 150,
            scopedSlots: { customRender: 'component' },
            ellipsis: true,
          },
          {
            title: '退货出库数量',
            dataIndex: 'StockOutQuantity',
            width: 150,
            scopedSlots: { customRender: 'component' },
            ellipsis: true,
          },
          // ===4   TaxIncludedPurchaseAmount
          {
            title: '退货出库含税小计',
            dataIndex: this.type == 4 ? 'StockOutAmount' : 'stockOutAmount',
            width: 200,
            precision: 6,
            scopedSlots: { customRender: 'price' },
            ellipsis: true,
          },
        ]
      }
    },
  },
  mounted() {},
  created() {},
  methods: {
    /**
     * 显示
     * @param {* 查询数据id} id
     * @param {* 类型 1采购订单  2采购入库 6采购退货  8采购调价 9采购票折} type
     */
    show(id, type, audit) {
      this.type = type
      this.isAduit = audit
      this.id = id
      this.url.list = this.urlA || this.url['list' + type]
      console.log('list', this.url.list)
      this.loadData(1)
      this.visible = true
    },
    loadBefore() {
      this.queryParam['id'] = this.id
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
    },
    hanleOK() {
      this.close()
    },
    clickA(text, record, index, column) {
      let key = column.dataIndex
      // 可销总库存
      if (key == 'AvailableInventory') {
        this.$refs.InventoryDetailModal.show(record)
      } else if (key == 'ThisMonthCount') {
        // 本月销量
        this.$refs.SalesThisMonthModal.show(record)
      } else if (key == 'LastPurchasePrice') {
        // 末次进价
        this.$refs.HistoricalPurchasePriceModal.show(record)
      }
    },
  },
}
</script>
<style lang="less">

</style>
