<!--
 * @Author: LP
 * @Description: 预付款结算列表
 * @Date: 2023-12-21 17:03:21
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView ref="tableView" showCardBorder :tabDataList="tabDataList" :tab="tab" :columns="columns" :dataList="dataSource" @operateClick="onOperateClick" @actionClick="onActionClick" />
  </div>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PurPrepaymentSettlementList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'KeyWord',
        },
        {
          name: '结算单号',
          type: SEnum.INPUT,
          key: 'SettlementOrderNo',
        },
        {
          name: '单据编号',
          type: SEnum.INPUT,
          key: 'BusinessOrderNo',
        },
        {
          name: '审核状态',
          type: SEnum.ENUM,
          key: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          dataType: SEnum.INT,
        },
        {
          name: '执行状态',
          type: SEnum.ENUM,
          key: 'ExecuteStatus',
          dictCode: 'EnumExecuteStatus',
          dataType: SEnum.INT,
        },
        {
          name: '结算单状态',
          type: SEnum.ENUM,
          key: 'PurchaseSettlementStatus',
          dictCode: 'EnumPurchaseRedOffSetStatus',
          dataType: SEnum.INT,
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
      ],
      tab: {
        tabTitles: ['预付款结算'], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
        operateBtns: [
          {
            name: '新增结算',
            type: 'primary',
            icon: 'plus',
            key: '新增结算',
            id: '4cbace70-2144-43ac-9482-59bd254e8064',
          },
        ], //右上角按钮集合
        bordered: false, //是否显示表格的边框
        rowKey: 'Id',
      },

      columns: [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 200,
          ellipsis: true,
        },
        {
          title: '结算单号',
          dataIndex: 'SettlementOrderNo',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '结算金额',
          dataIndex: 'TotalCurrentSettledAmount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 120,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '执行状态',
          dataIndex: 'ExecuteStatusStr',
          width: 150,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '结算单状态',
          dataIndex: 'PurchaseSettlementStatusStr',
          width: 150,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '说明',
          dataIndex: 'Remark',
          width: 200,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: 'd1c383a5-cfbc-44df-a534-2d3a4ba714af',
              isShow: (record) => {
                return ![0,3].includes(record.AuditStatus)
              },
            },
            {
              name: '编辑',
              id: 'a1a542df-ea84-4ee2-a4d3-1fb1de5b9aca',
              isShow: (record) => {
                // 已冲红：执行成功的冲红成功，冲红后不能编辑和删除
                return [0,3].includes(record.AuditStatus) && record.PurchaseSettlementStatus!= 4101
              },
            },
            {
              name: '删除',
              id: '08e6accc-2af6-4583-a6e4-225c3e737d05',
              isShow: (record) => {
                // 已冲红：执行成功的冲红成功，冲红后不能编辑和删除
                return [0,3].includes(record.AuditStatus) && record.PurchaseSettlementStatus!= 4101
              },
            },
          ],
        },
      ],
      httpHead: 'P36009',
      url: {
        listType: 'POST',
        list: '/{v}/PurchaseSettlement/GetPurchaseSettlementOrders',
        tabCount: '',
        delete: '/{v}/PurchaseSettlement/RemovePurchaseSettlementOrder',
      },
    }
  },
  computed: {},
  created() { },

  methods: {
    onOperateClick(key) {
      if (key === '新增结算') {
        this.onDetailClick('PurSettlementPage')
      }
    },
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('PurPrepaymentSettlementDetail', { id: record.Id })
      } else if (key === '编辑') {
        this.onDetailClick('PurSettlementPage', { id: record.Id })
      } else if (key === '删除') {
        this.handleDeleteClick('', null, { OrderIdList: [record.Id] })
      }
    },
  },
}
</script>
<style scoped>

</style>
