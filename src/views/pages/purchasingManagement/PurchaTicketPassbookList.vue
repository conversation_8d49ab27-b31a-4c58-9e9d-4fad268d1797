<!--
 * @Author: LP
 * @Description: 采购票折列表
 * @Date: 2023/07/08
-->

<template>
  <div>
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />

    <!-- 列表 -->
    <TableView
      ref="tableView"
      showCardBorder
      :tabDataList="tabDataList"
      :tab="tab"
      :columns="columns"
      :dataList="dataSource"
      @operateClick="onOperateClick"
      @actionClick="onActionClick"
    />
  </div>
</template>
<script>
import { putAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
export default {
  name: 'PurchaTicketPassbookList',
  mixins: [ListMixin],
  components: {},
  data() {
    return {
      searchItems: [
        {
          name: '供应商',
          placeholder: '名称/拼音码/编号',
          type: SEnum.INPUT,
          key: 'SupplierKeyWords',
        },
        {
          name: '票折单号',
          type: SEnum.INPUT,
          key: 'InvoiceDiscountNo',
        },
        {
          name: '订单编号',
          type: SEnum.INPUT,
          key: 'PurchaseOrderNo',
        },
        {
          name: '审核状态',
          type: SEnum.ENUM,
          key: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
        },
        {
          name: '结算状态',
          type: SEnum.ENUM,
          key: 'SettlementStatus',
          dictCode: 'EnumSettlementStatus',
        },
        {
          name: '执行状态',
          type: SEnum.ENUM,
          key: 'ExecuteStatus',
          dictCode: 'EnumExecuteStatus',
        },
        {
          name: '同步状态',
          type: SEnum.ENUM,
          key: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
        },
        {
          name: '创建人',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '创建时间',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
        {
          name: '责任人',
          type: SEnum.INPUT,
          key: 'OwnerByName',
        },
        {
          name: '商品',
          type: SEnum.INPUT,
          key: 'ErpGoodsName',
        },
        {
          name: '关联协议类型',
          type: SEnum.ENUM,
          key: 'AgreementType',
          dictCode: 'EnumPurchaseAgreementType',
        },
        {
          name: '关联协议编号',
          type: SEnum.INPUT,
          key: 'AgreementNo',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        curStatus: '',
        tabStatusKey: '', //标签的切换关键字
        tabTitles: ['采购票折列表'],
        operateBtns: [
          { name: '取消导出', icon: '', isShow: false, key: '取消导出' },
          {
            name: '导出',
            type: 'primary',
            icon: 'download',
            key: '导出',
            id: 'acce1971-07e4-456b-9e44-73bcf842e4e9',
          },
        ],
        selectType: '',
        rowKey: 'Id',
      },
      httpHead: 'P36009',
      columns: [
        {
          title: '供应商名称',
          dataIndex: 'SupplierName',
          scopedSlots: { customRender: 'component' },
          fixed: 'left',
          width: 180,
          ellipsis: true,
        },
        {
          title: '票折单号',
          dataIndex: 'InvoiceDiscountNo',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '订单编号',
          dataIndex: 'PurchaseOrderNo',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '票折金额',
          dataIndex: 'TotalPriceInvoiceDiscount',
          width: 150,
          scopedSlots: { customRender: 'price' },
          ellipsis: true,
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 150,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '结算状态',
          dataIndex: 'SettlementStatusStr',
          width: 150,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '执行状态',
          dataIndex: 'ExecuteStatusStr',
          width: 150,
          scopedSlots: { customRender: 'state' },
          ellipsis: true,
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 150,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 180,
          scopedSlots: { customRender: 'component' },
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '详情',
              id: 'd97618a4-c4ac-4688-8df9-087d063808c0',
              isShow: (record) => {
                return ![0,3].includes(record.AuditStatus)
              },
            },
            {
              name: '编辑',
              id: '6d363282-1555-4f4d-81c5-610cc3ab91a3',
              isShow: (record) => {
                return [0,3].includes(record.AuditStatus)
              },
            },
            {
              name: '删除',
              id: '77bedcfe-ea75-4edd-b2a1-b6707e7dba56',
              isShow: (record) => {
                return [0,3].includes(record.AuditStatus)
              },
            },
            {
              name: '撤回',
              id: '58252174-28ba-481a-a964-8ba9bf9f7c1a',
              isShow: (record) => {
                return [1].includes(record.AuditStatus)
              },
            },
          ],
        },
      ],
      url: {
        list: '/{v}/PurchaseBusiness/QueryPurchaseInvoiceDiscountList',
        tabCount: '',
        delete: '/{v}/PurchaseBusiness/RemovePurchaseInvoiceDiscount',
        exportUrl: '/{v}/PurchaseBusiness/ExportInvoiceDiscount',
        cancelOrder: '/{v}/PurchaseBusiness/RevokePurchaseInvoiceDiscountAudit',//撤回
      },
    }
  },
  methods: {
    onOperateClick(key) {
      if (key == '导出') {
        this.tab.selectType = 'checkbox'
        this.tab.operateBtns.forEach((item) => {
          if (item.key == '取消导出') {
            item.isShow = true
          } else if (item.key == '导出') {
            item.key = '确认导出'
            item.name = '确认导出'
          }
        })
      } else if (key == '取消导出') {
        this.onClearSelected()
        this.tab.operateBtns.forEach((item) => {
          if (item.key == '取消导出') {
            item.isShow = false
          } else if (item.key == '确认导出') {
            item.key = '导出'
            item.name = '导出'
          }
        })
        this.tab.selectType = null
      } else if (key == '确认导出') {
        if (this.selectedRowKeys.length > 0) {
          if (this.loading) {
            return
          }
          this.loading = true
          let exportTitle = '采购票折列表(' + this.getCurrentDateStr() + ')共' + this.selectedRowKeys.length + '条记录'
          this.handleExportXls(
            exportTitle,
            'post',
            this.url.exportUrl,
            null,
            this.httpHead,
            {
              IdList: this.selectedRowKeys,
            },
            null,
            () => {
              this.loading = false
              this.onOperateClick('取消导出')
            }
          )
        } else {
          this.$message.warning('请先选择要导出的数据！')
        }
      }
    },
    onActionClick(record, key, index) {
      if (key === '详情') {
        this.onDetailClick('PurchaTicketPassbookDetail', { id: record.Id, type: 1 })
      } else if (key === '编辑') {
        this.onDetailClick('PurchaTicketPassbookDetail', { id: record.Id, type: 2 })
      } else if (key === '删除') {
        this.handleDeleteClick(record.Id, 'PUT')
      } else if (key === '撤回') {
        const that = this
        this.$confirm({
          title: '撤回票折单',
          content: '您确定要撤回该票折单吗？',
          onOk() {
            that.cancelOrder(record.Id)
          },
          onCancel() { },
        })
      }
    },
    // 撤回票折单
    cancelOrder(Id) {
      if (!Id) return
      this.loading = true
      putAction(this.url.cancelOrder, { Id: Id }, this.httpHead).then((res) => {
        this.loading = false
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          this.loadData()
          return
        }
        this.$message.success('操作成功!')
        this.loadData()
      })
    },
  },
}
</script>
<style scoped>

</style>
