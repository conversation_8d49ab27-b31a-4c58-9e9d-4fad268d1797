
<!-- 商销客户审核 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="httpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="onSetValid"
      @operate="operate"
      @changeTab="changeTab"
      @getListAmount="getListAmount" />

  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '工作交接确认',
  name: 'WorkHandoverConfirmList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchItems: [
        {
          name: '交接单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'TransferNo'
        }, {
          name: '交付人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'Deliver'
        }, {
          name: '接收人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'Receiver'
        }, {
          name: '归属人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'OwnerByName'
        }, {
          name: '创建日期',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime'
        }
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: 1,
        statusKey: 'TransferStatus', //标签的切换关键字
        statusList: [
          {
            name: '待接收',
            value: 1,
            count: '',
            key: 'TransferStatus'
          },
          {
            name: '已接收',
            value: 2,
            count: '',
            key: 'TransferStatus'
          },
          {
            name: '已拒收',
            value: 3,
            count: '',
            key: 'TransferStatus'
          },
        ],
      },
      columns: [
        {
          title: '交接单号',
          dataIndex: 'TransferNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '人员类型',
          dataIndex: 'UserTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '交付人',
          dataIndex: 'DeliverName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '接收人',
          dataIndex: 'ReceiverName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '交接范围',
          dataIndex: 'TransferRangeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '归属人',
          dataIndex: 'OwnerByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          actionBtn: [
            {
              name: '确认', icon: '', id: '0e815fe9-c427-4ba0-b254-14e881ee8158', specialShowFuc: record => {
                return this.tab.status == 1
              }
            },
            {
              name: '详情', icon: '', id: 'ca1169db-d4e0-4f2e-8f77-d96d4ccffaaa', specialShowFuc: record => {
                return this.tab.status == 2 || this.tab.status == 3
              }
            },
            {
              name: '拒收', icon: '', id: '60b82611-0e4a-44af-9530-44a6a2125222', specialShowFuc: record => {
                return this.tab.status == 1
              }
            }
          ],
          scopedSlots: { customRender: 'action' }
        }
      ],
      isTableInitData: true, //是否自动加载
      httpHead: 'P36010',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/WorkTransfer/ConfirmList',
        listAmount: '/{v}/WorkTransfer/GetTransferStatusCount',
        rejection: '/{v}/WorkTransfer/Rejection/Rejection'
      },
      queryParam: {
        TransferStatus: 1
      }
    }
  },
  created() { },
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '确认') {
        this.onDetailClick('WorkHandoverDetail', {
          id: record.Id,
          acType: 3
        })
      } else if (type == '详情') {
        this.onDetailClick('WorkHandoverDetail', {
          id: record.Id,
        })
      } else if (type == '拒收') {
        this.$confirm({
          title: '您确定要拒收工作交接信息吗?',
          content: '',
          onOk: ()  => {
            this.rejectWorkTransfer(record)
          },
          onCancel() { },
        });
      }
    },
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.$refs.table.tab.status = 1
      }
      queryParam['TransferStatus'] = this.$refs.table.tab.status
      this.$refs.table.loadDatas(null, queryParam)
    },
    getListAmount(data) {
      // console.log('getListAmount', data)
      if ((data || []).length) {
        this.tab.statusList = []
        data.map(v => {
          this.tab.statusList.push({
            name: v['TransferStatusStr'],
            value: v['TransferStatus'],
            count: v['Count']
          })
        })
      }
    },
    rejectWorkTransfer(record) {
      getAction(this.linkUrl.rejection, { workTransferId: record.Id }, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          this.$message.success('拒收成功')
          let queryParam = this.$refs.SimpleSearchArea ? this.$refs.SimpleSearchArea.queryParam : null
          this.$refs.table.loadDatas(1, queryParam)
        } else {
          this.$message.error(res.Msg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-table-tbody tr {
  height: 46px;
}
</style>