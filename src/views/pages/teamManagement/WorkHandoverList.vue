<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @operate="operate"
    />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction, deleteAction } from '@/api/manage'
export default {
  name: 'WorkHandoverList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchItems: [
        {
          name: '交接单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'TransferNo',
        },
        {
          name: '人员类型',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/SystemTool/GetEnumsByName',
          key: 'UserType',
          params: { enumName: 'EnumUserType' },
          dataKey: { name: 'ItemDescription', value: 'ItemValue' },
        },
        {
          name: '交付人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'Deliver',
        },
        {
          name: '接收人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'Receiver',
        },
        {
          name: '交接范围',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/SystemTool/GetEnumsByName',
          key: 'TransferRange',
          params: { enumName: 'EnumTransferRange' },
          dataKey: { name: 'ItemDescription', value: 'ItemValue' },
          notOption: [3],
        },
        {
          name: '审核状态',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/SystemTool/GetEnumsByName',
          key: 'AuditStatus',
          params: { enumName: 'EnumAuditStatus' },
          dataKey: { name: 'ItemDescription', value: 'ItemValue' },
        },
        {
          name: '交接状态',
          type: SEnum.RADIO,
          httpHead: 'P36001',
          url: '/{v}/SystemTool/GetEnumsByName',
          key: 'TransferStatus',
          params: { enumName: 'EnumTransferStatus' },
          dataKey: { name: 'ItemDescription', value: 'ItemValue' },
        },
        {
          name: '创建人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'CreateByName',
        },
        {
          name: '发生日期',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '新增', type: 'primary', icon: '', key: 'add', id: '5ff92768-cf31-4ffb-aae9-f4ddc90aa5cd' },
        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '交接单号',
          dataIndex: 'TransferNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '人员类型',
          dataIndex: 'UserTypeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '交付人',
          dataIndex: 'DeliverName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '接收人',
          dataIndex: 'ReceiverName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '交接范围',
          dataIndex: 'TransferRangeStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '交接状态',
          dataIndex: 'TransferStatusStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          // AuditStatus 0-未提交 1-审核中 2-审核通过 3-审核失败
          // TransferStatus 0-未提交 1-待接收 2-已接收 3-已拒收
          actionBtn: [
            {
              name: '编辑',
              icon: '',
              id: '04fde0bf-6ded-4e89-b156-f2ac11dd4a52',
              specialShowFuc: (record) => {
                return [0,3].includes(record.AuditStatus) && [0,2,3].includes(record.TransferStatus)
              },
            },
            {
              name: '详情',
              icon: '',
              id: 'b9ed9d0e-cecf-4a52-be18-116ae3774c41',
              specialShowFuc: (record) => {
                return [0,1,2].includes(record.AuditStatus) && [1,2].includes(record.TransferStatus)
              },
            },
            {
              name: '删除',
              icon: '',
              id: 'a158707c-a5ec-4a0b-b397-e2bfa36c6b2a',
              specialShowFuc: (record) => {
                return [0,3].includes(record.AuditStatus) && [0,2,3].includes(record.TransferStatus)
              },
            },
            {
              name: '撤回',
              icon: '',
              id: 'dac3a286-6d5d-4714-bee4-5ed81e25c6fe',
              specialShowFuc: (record) => {
                return [1].includes(record.AuditStatus) && [2].includes(record.TransferStatus)
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: true, //是否自动加载
      linkHttpHead: 'P36010',
      linkUrlType: '', //请求方式
      linkUrl: {
        list: '/{v}/WorkTransfer/List',
        deleteWorkTransfer: '/{v}/WorkTransfer/Delete/DeleteWorkTransfer',
        withdrawWorkTransfer: '/{v}/WorkTransfer/Revoke/Revoke',
      },
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 列表操作
    operate(record, type) {
      if (type == '明细') {
        this.onDetailClick('BusinessAccountInfo', { info: JSON.stringify(record) })
      } else if (type == 'add') {
        this.onDetailClick('WorkHandoverEdit', {
          acType: 1,
        })
      } else if (type == '编辑') {
        this.onDetailClick('WorkHandoverEdit', {
          acType: 2,
          id: record.Id,
        })
      } else if (type == '详情') {
        this.onDetailClick('WorkHandoverDetail', {
          id: record.Id,
        })
      } else if (type == '删除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认删除本条工作交接信息？删除后不能恢复',
          onOk() {
            that.deleteWorkTransfer(record)
          },
          onCancel() {},
        })
      } else if (type == '撤回') {
        let that = this
        this.$confirm({
          title: '您确定要撤回本条工作交接信息吗?',
          content: '',
          onOk() {
            that.withdraw(record)
          },
          onCancel() {},
        })
      }
    },
    deleteWorkTransfer(record) {
      deleteAction(`${this.linkUrl.deleteWorkTransfer}?workTransferId=${record.Id}`, {}, this.linkHttpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.$message.success('删除成功')
            let queryParam = this.$refs.SimpleSearchArea ? this.$refs.SimpleSearchArea.queryParam : null
            this.$refs.table.loadDatas(1, queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => {})
    },
    withdraw(record) {
      getAction(this.linkUrl.withdrawWorkTransfer, { workTransferId: record.Id }, this.linkHttpHead).then((res) => {
        if (res.IsSuccess) {
          this.$message.success('撤回成功')
          let queryParam = this.$refs.SimpleSearchArea ? this.$refs.SimpleSearchArea.queryParam : null
          this.$refs.table.loadDatas(1, queryParam)
        } else {
          this.$message.error(res.Msg)
        }
      })
    },
  },
}
</script>

<style></style>
