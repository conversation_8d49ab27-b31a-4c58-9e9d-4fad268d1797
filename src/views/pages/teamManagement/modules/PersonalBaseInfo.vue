<template>
  <div>
    <a-spin :spinning="spinning" tip="加载中...">
      <a-card :bodyStyle="{ padding: '0' }" :headStyle="{ padding: '0 16px ' }">
        <div class="title-box">
          <div class="title-top-box">
            <div class="title-top-left">
              <div>
                <span class="title-text">{{chooseModel.Deliver.Name}}</span>
                <span class="phone-text">{{chooseModel.Deliver.Phone}}</span>
              </div>
              <div class="title-person">
                <span style="margin-right:20px;">人员类型：{{getUserTypesStr(chooseModel.Deliver.UserTypesStr)}}</span>
                <span>所在部门：{{chooseModel.Deliver.DepartmentName}}</span>
              </div>
            </div>
            <div class="arrow-center" v-if="type==2&&WorkTransferState">
              <div>{{ WorkTransferState }}</div>
              <a-icon style="font-size:24px;color:#ffffff;cursor: pointer;" type="arrow-right" />
            </div>
            <div class="search-box" v-if="type==1">
              <SingleInputSearchView placeholder="搜索人员姓名/手机号" width="100%" :httpParams="{
                      pageIndex: 1,
                      pageSize: 200,
                      IsValid: true,
                    }" keyWord="keyWord" style="margin-right:20px;" :dataKey="{ name: 'Name', value: 'Id', nameList: [] }" :httpHead="httpHead" :Url="url.personList" :value="personModel.Deliver.Id" :name="personModel.Deliver.Name" :isAbsolute="true" @clear="()=>{personModel.Deliver.Id='';personModel.Deliver.Name = ''}" @change="changeDivisionName" />
              <a-icon style="font-size:36px;color:#ffffff;cursor: pointer;margin-right:10px;" type="left-square" @click="checkType('reduce')" />
              <a-icon style="font-size:36px;color:#ffffff;cursor: pointer;" type="right-square" @click="checkType('add')" />
            </div>
            <div v-if="type==2&&chooseModel.Receiver" style="text-align: right;">
              <div>
                <span class="phone-text">{{chooseModel.Receiver.Phone}}</span>
                <span class="title-text" style="margin-left:20px;">{{chooseModel.Receiver.Name}}</span>
              </div>
              <div class="title-person">
                <span>所在部门：{{chooseModel.Receiver.DepartmentName}}</span>
                <span style="margin-left:20px;">人员类型：{{getUserTypesStr(chooseModel.Receiver.UserTypesStr)}}</span>
              </div>
            </div>
          </div>
          <div class="title-ctn">
            <!-- 采购 -->
            <template  v-if="isCaiG(personType) && PurchaserTransferRange != 3">
              <div class="title-item" :style="{width:isShangX(personType) && SalerTransferRange != 3?'19%':'31%'}" v-for="(item,index) in moneyArray" :key="index">
                <div class="title-item-left" :style="{color:item.leftColor,background:item.leftBackground}">
                  <span>{{item.leftText}}</span>
                </div>
                <div class="title-item-right" :style="{background:item.rightBackground}">
                  <div class="money-text">{{item.name || 0}}</div>
                  <div class="money-title">{{item.text}}</div>
                </div>
              </div>
            </template>
            <!-- 商销 -->
            <template v-if="isShangX(personType) && SalerTransferRange != 3">
              <div class="title-item" :style="{width:isCaiG(personType)&& PurchaserTransferRange != 3?'19%':'49%'}" v-for="(item,index) in shopMoneyArray" :key="'shopMoneyArray-'+index">
                <div class="title-item-left" :style="{color:item.leftColor,background:item.leftBackground}">
                  <span>{{item.leftText}}</span>
                </div>
                <div class="title-item-right" :style="{background:item.rightBackground}">
                  <div class="money-text">{{item.name || 0}}</div>
                  <div class="money-title">{{item.text}}</div>
                </div>
              </div>
            </template>
          </div>

        </div>
        <!-- 采购 -->
        <div class="card-box" v-if="isCaiG(personType) && PurchaserTransferRange!=3">
          <a-row :gutter="24" v-for="(item,index) in purchaseNewArray" :key="'purchaseNewArray-'+index">
            <div class="card-title">{{item.name}}</div>
            <a-col class="card-item" :span="5" v-for="(rItem,rIndex) in item.array" :key="'purchaseNewArray-'+index +'-'+rIndex">
              <div class="card-ele">
                <div class="detail-text" @click="lookInfo(rItem)">明细</div>
                <div class="card-ele-ctn">
                  <div class="card-ele-ctn-num">{{rItem.num}}</div>
                  <div class="card-ele-ctn-text">{{rItem.name}}</div>
                </div>
              </div>
            </a-col>
          </a-row>

        </div>
        <!-- 商销 -->
        <div class="card-box" v-if="isShangX(personType) && SalerTransferRange != 3">
          <a-row :gutter="24" v-for="(item,index) in shopNewArray" :key="'shopNewArray-'+index">
            <div class="card-title">{{item.name}}</div>
            <a-col class="card-item" :span="5" v-for="(rItem,rIndex) in item.array" :key="'shopNewArray-'+ index +'-'+rIndex">
              <div class="card-ele">
                <div class="detail-text" @click="lookInfo(rItem)">明细</div>
                <div class="card-ele-ctn">
                  <div class="card-ele-ctn-num">{{rItem.num}}</div>
                  <div class="card-ele-ctn-text">{{rItem.name}}</div>
                </div>
              </div>
            </a-col>
          </a-row>

        </div>
      </a-card>
    </a-spin>
    <!-- 查看详情 -->
    <PersonalCheckInfoModal ref="PersonalCheckInfoModal"></PersonalCheckInfoModal>
  </div>
</template>

<script>
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'PersonalBaseInfo',
  components: {

  },
  props: {
    type: {
      type: Number,
      default: 1,//1 个人预览表 2工作交接
    },
    /*交付人Id*/
    Id: {
      type: String,
      default: ''
    },
    /*接收人Id*/
    ReceiverId: {
      type: String,
      default: ''
    },
    /*工作交接Id*/
    WorkTransferId: {
      type: String,
      default: ''
    },
    WorkTransferState: {
      type: String,
      default: ''
    },
    SalerTransferRange:{ //商销交接范围
      type: Number,
      default: null
    },
    PurchaserTransferRange:{ //采购交接范围
      type: Number,
      default: null
    },
    Customers: {
      type: Array,
      default: () => {
        return []
      }
    },
    GoodsSpus: {
      type: Array,
      default: () => {
        return []
      }
    },
    isEdit:{
      type:Boolean,
      default:false
    },
    isAdd:{
      type:Boolean,
      default:false
    }
  },
  created() {
    this.personType = this.type
    console.log('created   ',this.personType)
    if (this.type == 1) {
      this.getPersonalInfo()
    } else {
      this.getWorkTransferInfo()
    }
  },
  methods: {
    getPersonalInfo() {
      this.spinning = true
      getAction('/usercenter/baUser/getUserDetail', { id: this.Id }).then
    }
  },
  data() {
    return {
      model: [],
      personModel: {
        Deliver: {},
      },
      TotalAccount: undefined,
      chooseModel: {
        Deliver: {},
        ProcureUser: {
          AccountBalance: {},
          ProcureGoodsSpu: {},
          PurchaseOrder: {},
          PurchaseContract: {},
          PurchaseAgreement: {},
          ProcureFinance: {},
        },
        SaleUser: {
          AccountBalance: {},
          SaleCustomer: {},
          SaleOrder: {},
          SaleOrderAdjustPrice: {},
          SaleAfterOrder: {},
          SaleOrderInvoice: {},
          SaleSpecialReceipt: {},
          SaleFinance: {},
        }
      },
      pageIndex: 1,
      chooseIndex: 0,
      spinning: false,
      personType: [],//1 采购人员 2 商销人员
      moneyArray: [
        { name: '', text: '应付余额(元)', leftText: '货款', leftColor: '#D9001B', leftBackground: '#f7ccd1', rightBackground: '#eb7f8c', },
        { name: '', text: '预付余额(元)', leftText: '货款', leftColor: '#D9001B', leftBackground: '#f7ccd1', rightBackground: '#eb7f8c', },
        { name: '', text: '应收余额(元)', leftText: '返利', leftColor: '#008080', leftBackground: '#c2e8d8', rightBackground: '#67c59d', },
      ],
      shopMoneyArray: [
        { name: '', text: '应收余额(元)', leftText: '货款', leftColor: '#D9001B', leftBackground: '#f7ccd1', rightBackground: '#eb7f8c', },
        { name: '', text: '预收余额(元)', leftText: '货款', leftColor: '#D9001B', leftBackground: '#f7ccd1', rightBackground: '#eb7f8c', },
      ],
      purchaseNewArray: [
        {
          name: '采购-转移品种',
          array: [
            {
              num: '',
              name: '品种数量(个)',
              type: 0,
              index: 0,
              tab: [
                { name: '品种', url: '/{v}/TeamBusinessOverview/QueryRelationGoodsList', searchInput: [{ name: '商品：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, } },
                { name: '品种供应商', url: '/{v}/TeamBusinessOverview/QueryRelationSupplierList', searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, } },
              ],
            },
            {
              num: '',
              name: '品种供应商数量(家)',
              type: 0,
              index: 1,
              tab: [
                { name: '品种', url: '/{v}/TeamBusinessOverview/QueryRelationGoodsList', searchInput: [{ name: '商品：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },] ,params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, }},
                { name: '品种供应商', url: '/{v}/TeamBusinessOverview/QueryRelationSupplierList', searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },] ,params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, }},
              ],
            },
          ]
        },
        {
          name: '采购-转移单据',
          array: [
            {
              num: '',
              name: '采购计划数量(个)',
              type: 1,
              searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],
              url: '/{v}/TeamBusinessOverview/QueryPurchasePlanList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },
            {
              num: '',
              name: '采购订单数量(个)',
              type: 2,
              searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],
              url: '/{v}/TeamBusinessOverview/QueryPurchaseOrderList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },
            {
              num: '',
              name: '采购月结付款单数量(个)',
              type: 3,
              index: 1,
              tab: [
                { name: '预付款', url: '/{v}/TeamBusinessOverview/QueryPurchaseRemittedOrderList', searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchasePaymentMode: 1,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false }, },
                { name: '月结', url: '/{v}/TeamBusinessOverview/QueryPurchaseRemittedOrderList', searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchasePaymentMode: 2 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
                { name: '货到付款', url: '/{v}/TeamBusinessOverview/QueryPurchaseRemittedOrderList', searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchasePaymentMode: 3 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
              ],
            },
            {
              num: '',
              name: '采购预付款订单数量(个)',
              type: 3,
              index: 0,
              tab: [
                { name: '预付款', url: '/{v}/TeamBusinessOverview/QueryPurchaseRemittedOrderList', searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchasePaymentMode: 1 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
                { name: '月结', url: '/{v}/TeamBusinessOverview/QueryPurchaseRemittedOrderList', searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchasePaymentMode: 2 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
                { name: '货到付款', url: '/{v}/TeamBusinessOverview/QueryPurchaseRemittedOrderList', searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchasePaymentMode: 3 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
              ],
            },
            {
              num: '',
              name: '采购货到付款订单数量（个）',
              type: 3,
              index: 2,
              tab: [
                { name: '预付款', url: '/{v}/TeamBusinessOverview/QueryPurchaseRemittedOrderList', searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchasePaymentMode: 1 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
                { name: '月结', url: '/{v}/TeamBusinessOverview/QueryPurchaseRemittedOrderList', searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchasePaymentMode: 2 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
                { name: '货到付款', url: '/{v}/TeamBusinessOverview/QueryPurchaseRemittedOrderList', searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchasePaymentMode: 3 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
              ],
            },
            {
              num: '',
              name: '采购发票单数量(个)',
              type: 4,
              searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],
              url: '/{v}/TeamBusinessOverview/QueryPurchaseRemittedInvoiceList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },
            {
              num: '',
              name: '采购调价单数量(个)',
              type: 5,
              searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],
              url: '/{v}/TeamBusinessOverview/QueryPurchasePriceAdjustmentList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },
            {
              num: '',
              name: '采购票折单数量(个)',
              type: 6,
              searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],
              url: '/{v}/TeamBusinessOverview/QueryPurchaseInvoiceDiscountList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },
            {
              num: '',
              name: '采购退货单数量(个)',
              type: 7,
              tableTitle: '未完成的采购退货',
              searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],
              url: '/{v}/TeamBusinessOverview/QueryPurchaseRefundOrderList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },
            {
              num: '',
              name: '采购合同数量(个)',
              type: 8,
              searchInput: [{ name: '供应商：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],
              url: '/{v}/TeamBusinessOverview/QueryPurchaseContractList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },
            {
              num: '',
              name: '短期采购协议数量(个)',
              type: 9,
              index: 1,
              tab: [
                { name: '年度协议', url: '/{v}/TeamBusinessOverview/QueryPurchaseAgreementList', searchInput: [{ name: '甲方：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchaseAgreementType: 1,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false }, },
                { name: '短期采购协议', url: '/{v}/TeamBusinessOverview/QueryPurchaseShortAgreementList', params: { PurchaseAgreementType: 3,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false }, },
                { name: '短期销售协议', url: '/{v}/TeamBusinessOverview/QueryPurchaseAgreementList', params: { PurchaseAgreementType: 2,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false }, },
                { name: '通用协议', url: '/{v}/TeamBusinessOverview/QueryGeneralAgreementList', searchInput: [{ name: '甲方：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: {ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
              ],
            },
            {
              num: '',
              name: '短期销售协议数量(个)',
              type: 9,
              index: 2,
              tab: [
                { name: '年度协议', url: '/{v}/TeamBusinessOverview/QueryPurchaseAgreementList', searchInput: [{ name: '甲方：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchaseAgreementType: 1 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
                { name: '短期采购协议', url: '/{v}/TeamBusinessOverview/QueryPurchaseShortAgreementList', params: { PurchaseAgreementType: 3 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
                { name: '短期销售协议', url: '/{v}/TeamBusinessOverview/QueryPurchaseAgreementList', params: { PurchaseAgreementType: 2,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false }, },
                { name: '通用协议', url: '/{v}/TeamBusinessOverview/QueryGeneralAgreementList', searchInput: [{ name: '甲方：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: {ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
              ],
            },
            {
              num: '',
              name: '年度协议数量(个)',
              type: 9,
              index: 0,
              tab: [
                { name: '年度协议', url: '/{v}/TeamBusinessOverview/QueryPurchaseAgreementList', searchInput: [{ name: '甲方：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchaseAgreementType: 1,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false }, },
                { name: '短期采购协议', url: '/{v}/TeamBusinessOverview/QueryPurchaseShortAgreementList', params: { PurchaseAgreementType: 3 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
                { name: '短期销售协议', url: '/{v}/TeamBusinessOverview/QueryPurchaseAgreementList', params: { PurchaseAgreementType: 2,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false }, },
                { name: '通用协议', url: '/{v}/TeamBusinessOverview/QueryGeneralAgreementList', searchInput: [{ name: '甲方：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: {ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
              ],
            },
            {
              num: '',
              name: '通用协议数量(个)',
              type: 9,
              index: 3,
              tab: [
                { name: '年度协议', url: '/{v}/TeamBusinessOverview/QueryPurchaseAgreementList', searchInput: [{ name: '甲方：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { PurchaseAgreementType: 1,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false }, },
                { name: '短期采购协议', url: '/{v}/TeamBusinessOverview/QueryPurchaseShortAgreementList', params: { PurchaseAgreementType: 3 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
                { name: '短期销售协议', url: '/{v}/TeamBusinessOverview/QueryPurchaseAgreementList', params: { PurchaseAgreementType: 2 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
                { name: '通用协议', url: '/{v}/TeamBusinessOverview/QueryGeneralAgreementList', searchInput: [{ name: '甲方：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: {ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
              ],
            },
            {
              num: '',
              name: '认款单数量(个)',
              type: 10,
              searchInput: [{ name: '认款单号：', type: 'input', value: '', key: 'RecognitionOrderNo', defaultVal: '', placeholder: '请输入' },],
              url: '/{v}/TeamBusinessOverview/QueryPurchaseRecognitionOrderList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },

          ]
        }
      ],
      shopNewArray: [
        {
          name: '商销-负责客户',
          array: [
            {
              num: '',
              name: '客户数量(个)',
              type: 11,
              searchInput: [{ name: '客户：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],
              url: '/{v}/TeamBusinessOverview/QueryRelationCustomerList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },
          ]
        },
        {
          name: '商销-负责单据',
          array: [
            {
              num: '',
              name: '商销订单数量(个)',
              type: 12,
              searchInput: [{ name: '客户：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],
              url: '/{v}/TeamBusinessOverview/QuerySalesOrderList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },
            {
              num: '',
              name: '商销调价单数量(个)',
              type: 13,
              searchInput: [{ name: '客户：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],
              url: '/{v}/TeamBusinessOverview/QuerySalesOrderAdjustPriceList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },
            {
              num: '',
              name: '商销仅退款售后单数量(个)',
              type: 14,
              index: 0,
              tab: [
                { name: '仅退款', url: '/{v}/TeamBusinessOverview/QuerySalesAfterOrderList', searchInput: [{ name: '客户：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { IsOnlyReturnMoney: true,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false }, },
                { name: '退货退款', url: '/{v}/TeamBusinessOverview/QuerySalesAfterOrderList', searchInput: [{ name: '客户：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { IsOnlyReturnMoney: false ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false}, },
              ],
            },
            {
              num: '',
              name: '商销退货退款售后单数量(个)',
              type: 14,
              index: 1,
              tab: [
                { name: '仅退款', url: '/{v}/TeamBusinessOverview/QuerySalesAfterOrderList', searchInput: [{ name: '客户：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { IsOnlyReturnMoney: true ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false }, },
                { name: '退货退款', url: '/{v}/TeamBusinessOverview/QuerySalesAfterOrderList', searchInput: [{ name: '客户：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { IsOnlyReturnMoney: false,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false  }, },
              ],
            },
            {
              num: '',
              name: '商销开票单数量(个)',
              type: 15,
              index: 0,
              tab: [
                { name: '蓝票申请', url: '/{v}/TeamBusinessOverview/QuerySalesOrderInvoiceApplyList', searchInput: [{ name: '客户：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { InvoiceApplyType: 1,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false  }, },
                { name: '红票申请', url: '/{v}/TeamBusinessOverview/QuerySalesOrderInvoiceApplyList', searchInput: [{ name: '客户：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { InvoiceApplyType: 2 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false }, },
                { name: '作废申请', url: '/{v}/TeamBusinessOverview/QuerySalesOrderInvoiceApplyList', searchInput: [{ name: '客户：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' }], params: { InvoiceApplyType: 3 ,ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false }, },
              ],
            },
            {
              num: '',
              name: '商销含特回执单数量(个)',
              type: 16,
              searchInput: [{ name: '客户：', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '名称/编号/拼音码' },],
              url: '/{v}/TeamBusinessOverview/QuerySalesOrderReceiptList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },
            {
              num: '',
              name: '认款单数量(个)',
              type: 17,
              searchInput: [{ name: '认款单号：', type: 'input', value: '', key: 'RecognitionOrderNo', defaultVal: '', placeholder: '请输入' },],
              url: '/{v}/TeamBusinessOverview/QueryRecognitionOrderList',
              params: { ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false, },
            },
          ]
        },
      ],

      httpHead: "P36010",
      url: {
        info: '/{v}/TeamBusinessOverview/UserCheckListByToken',//POST
        info2: '/{v}/TeamBusinessOverview/UserCheckListByWorkTransferId',//根据交接ID获取数据 GET
        personList: '/{v}/CheckList/GetUserList',
      }
    }
  },
  created() {
    this.init()
  },
  activated() {
    if (this.$root.clickBtnRouterPush) {
      this.init()
      this.$root.clickBtnRouterPush = null
    }
  },
  methods: {
    init() {
      // console.log('this.type ',this.type)
      // console.log('this.Id ',this.Id)
      // console.log('this.personModel ',this.personModel)
      // this.personModel.Deliver = {}
      if (this.type == 1) {
        if(this.personModel&&this.personModel.Deliver&&this.personModel.Deliver.Id){
          this.getInfo(this.personModel.Deliver.Id);
        }else{
          this.getInfo();
        }
        
      } else if (this.type == 2) {
        this.getInfo(this.Id);
      }
    },
    // 是采购人员
    isCaiG(type=[]){
      return type.some(v=>v==1)
    },
    // 是商销人员
    isShangX(type=[]){
      return type.some(v=>v==2)
    },
    // 人员类型
    getUserTypesStr(type=[]){
      return type.length ? type.join('/') : ' -- '
    },
    
    getInfo(id) {
      this.spinning = true
      let params = {
        PageIndex: id ? 1 : this.pageIndex,
        PageSize: 1,
        DeliverId: id,
        ReceiverId: this.ReceiverId || undefined,
        WorkTransferId: this.WorkTransferId || null,
        ExcludeInWorkTransferOrder: this.isEdit||this.isAdd ? true : false,
      }
      // 新增交接时并且是部分交接
      if (!this.WorkTransferId || ( this.WorkTransferId && this.isEdit)) {
        if (this.Customers.length > 0) {
          let CustomerIds = this.Customers.map((item) => {
            return item.CustomerId
          })
          params.CustomerIds = CustomerIds
        }
        if (this.GoodsSpus.length > 0) {
          let GoodsSpuIds = this.GoodsSpus.map((item) => {
            return item.GoodsSpuId
          })
          params.GoodsSpuIds = GoodsSpuIds
        }
      }
      
      let postObj = {}
      console.log('this.isEdit === ',this.isEdit)
      if (this.WorkTransferId && !this.isEdit) {
        postObj = getAction(this.url.info2, params, this.httpHead)
      } else {
        postObj = postAction(this.url.info, params, this.httpHead)
      }
      console.log('this.WorkTransferId ===  ',this.WorkTransferId)
      postObj.then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg);
          return;
        }
        // console.log('res ===  ',res)
        let array = [this.chooseModel]
        if (!this.WorkTransferId ||  (this.WorkTransferId && this.isEdit)) {
          // 个人一览表
          this.model = res.Data.length > 0 ? res.Data : array
          if (res.Data.length == 0) {
            this.pageIndex = 1
            this.$message.warning("暂无更多人员")
          }
          this.chooseModel = this.model[this.chooseIndex]
        } else {
          // 工作交接
          this.model = res.Data
          this.chooseModel = res.Data
        }
        // console.log('人员类型chooseModel：',this.chooseModel )
        // Type 1 采购人员 2 商销人员
        this.personType = this.chooseModel.Deliver.UserTypes
        // console.log('人员类型：',this.personType)
        if (this.isCaiG(this.personType) && this.PurchaserTransferRange != 3) {
          // 采购人员
          this.setProcureUserData()
        }
        if (this.isShangX(this.personType) && this.SalerTransferRange != 3) {
          // 商销人员
          this.setSaleUserData()
        }
      }).catch((err) => {
        console.log('err    ',err)
        this.$message.error(err.Message || err.Msg || err);
      }).finally(() => {
        this.spinning = false
        this.$emit('spinning', false)
      });
    },
    // 采购人员
    setProcureUserData() {
      console.log('采购人员 setProcureUserData')
      this.moneyArray[0].name = this.chooseModel.PayableBalance //【货款】应付余额
      this.moneyArray[1].name = this.chooseModel.PrepaymentBalance //【货款】应付余额
      this.moneyArray[2].name = this.chooseModel.RebateReceivableBalance //【返利】应收余额

      // 负责品种
      this.purchaseNewArray[0].array[0].num = this.chooseModel.ProcureUser.ProcureGoodsSpu.ProductCount //品种数量
      this.purchaseNewArray[0].array[1].num = this.chooseModel.ProcureUser.ProcureGoodsSpu.SupplierCount //品种供应商数量

      // 负责单据
      this.purchaseNewArray[1].array[0].num = this.chooseModel.ProcureUser.PurchaseOrder.ProcurePlanCount //采购计划数量(个)
      this.purchaseNewArray[1].array[1].num = this.chooseModel.ProcureUser.PurchaseOrder.ProcureOrderCount //采购订单数量(个)
      this.purchaseNewArray[1].array[2].num = this.chooseModel.ProcureUser.PurchaseOrder.PurchaseRemittedOrder_YJ_Count //采购月结付款单数量(个)
      this.purchaseNewArray[1].array[3].num = this.chooseModel.ProcureUser.PurchaseOrder.PurchaseRemittedOrder_YFK_Count //采购预付款订单数量(个)
      this.purchaseNewArray[1].array[4].num = this.chooseModel.ProcureUser.PurchaseOrder.PurchaseRemittedOrder_HDFK_Count //采购货到付款订单数量（个）
      this.purchaseNewArray[1].array[5].num = this.chooseModel.ProcureUser.PurchaseOrder.PurchaseRemittedInvoiceCount //采购发票单数量(个) 暂定
      this.purchaseNewArray[1].array[6].num = this.chooseModel.ProcureUser.PurchaseOrder.PurchaseOrderAdjustPriceCount //采购调价单数量(个)
      this.purchaseNewArray[1].array[7].num = this.chooseModel.ProcureUser.PurchaseOrder.PurchaseOrderInvoiceDiscountCount //采购票折单数量(个)
      this.purchaseNewArray[1].array[8].num = this.chooseModel.ProcureUser.PurchaseOrder.PurchaseOrderRefundCount //采购退货单数量(个)
      this.purchaseNewArray[1].array[9].num = this.chooseModel.ProcureUser.PurchaseContract.ContractOrderCount //采购合同数量(个)
      this.purchaseNewArray[1].array[10].num = this.chooseModel.ProcureUser.PurchaseAgreement.ShortTermPurchaseAgreementCount //短期采购协议数量(个)
      this.purchaseNewArray[1].array[11].num = this.chooseModel.ProcureUser.PurchaseAgreement.ShortTermSalesAgreementCount //短期销售协议数量(个)
      this.purchaseNewArray[1].array[12].num = this.chooseModel.ProcureUser.PurchaseAgreement.YearTermAgreementCount //年度协议数量(个)
      this.purchaseNewArray[1].array[13].num = this.chooseModel.ProcureUser.PurchaseAgreement.GeneralAgreementCount //通用协议数量(个)
      this.purchaseNewArray[1].array[14].num = this.chooseModel.ProcureUser.ProcureFinance.RecognitionCount //认款单数量(个)
    },
    // 商销人员
    setSaleUserData() {
      console.log('商销人员 setSaleUserData',this.chooseModel)
      this.shopMoneyArray[0].name = this.chooseModel.ReceivableBalance //应收余额(元)
      this.shopMoneyArray[1].name = this.chooseModel.AdvancePaymentBalance //预收余额(元)

      // 负责客户
      this.shopNewArray[0].array[0].num = this.chooseModel.SaleUser.SaleCustomer.CustomerCount //客户数量

      // 负责单据
      this.shopNewArray[1].array[0].num = this.chooseModel.SaleUser.SaleOrder ? this.chooseModel.SaleUser.SaleOrder.OrderCount : 0 //商销订单数量(个)
      this.shopNewArray[1].array[1].num = this.chooseModel.SaleUser.SaleOrderAdjustPrice?this.chooseModel.SaleUser.SaleOrderAdjustPrice.SalesOrderAdjustPriceCount : 0 //商销调价单数量(个)
      this.shopNewArray[1].array[2].num = this.chooseModel.SaleUser.SaleAfterOrder?this.chooseModel.SaleUser.SaleAfterOrder.OnlyReturnMoneyCount : 0 //商销仅退款售后单数量(个)
      this.shopNewArray[1].array[3].num = this.chooseModel.SaleUser.SaleAfterOrder?this.chooseModel.SaleUser.SaleAfterOrder.ReturnGoodsCount : 0 //商销退货退款售后单数量(个)
      this.shopNewArray[1].array[4].num = this.chooseModel.SaleUser.SaleOrderInvoice?this.chooseModel.SaleUser.SaleOrderInvoice.InvoiceCount : 0 //商销开票单数量(个)
      this.shopNewArray[1].array[5].num = this.chooseModel.SaleUser.SaleSpecialReceipt?this.chooseModel.SaleUser.SaleSpecialReceipt.ReceiptCount : 0 //商销含特回执单数量(个)
      this.shopNewArray[1].array[6].num = this.chooseModel.SaleUser.SaleFinance?this.chooseModel.SaleUser.SaleFinance.RecognitionCount:0 //认款单数量(个)
    },
    changeDivisionName(val, txt, item) {
      this.personModel.Deliver.Id = val;
      this.personModel.Deliver.Name = txt;
      item.Deliver = {
        Id: val,
        Name: txt,
      };
      this.getInfo(val)
    },
    checkType(type) {
      if (type == 'add') {
        this.pageIndex++
        this.getInfo()
      } else {
        if (this.pageIndex == 1) {
          this.$message.warning("当前数据已经是首个了")
          return
        }
        this.pageIndex--
        this.getInfo()
      }
      if (this.model && this.model.length > 0) {
        this.chooseModel = this.model[this.chooseIndex]
        this.personType = this.chooseModel.Deliver.UserTypes
      }
      this.personModel = {
        Deliver: {},
      }
      if (this.isCaiG(this.personType) && this.PurchaserTransferRange != 3) {
        // 采购人员
        this.setProcureUserData()
      }
      if (this.isShangX(this.personType) && this.SalerTransferRange != 3) {
        // 商销人员
        this.setSaleUserData()
      }
    },
    lookInfo(item) {
      let GoodsSpuIds = []
      if (this.GoodsSpus) {
        this.GoodsSpus.map((item) => {
          GoodsSpuIds.push(item.GoodsSpuId)
        })
      }
      let CustomerIds = []
      if (this.Customers.length > 0) {
        this.Customers.map((item) => {
          CustomerIds.push(item.CustomerId)
        })
      }
      // 公用参数
      let params = {
        QueryType: this.type,//1 个人一览表 2 工作交接
        UserId: this.chooseModel.Deliver.Id,//个人一览表时候传
        WorkTransferId: this.WorkTransferId,//工作交接时候传
        GoodsSpuIds: GoodsSpuIds || [],//品种ID 工作交接时候传
        CustomerIds: CustomerIds || [],//客户ID 工作交接时候传
      }
      this.$refs.PersonalCheckInfoModal.queryParam = params
      this.$refs.PersonalCheckInfoModal.show(item, this.chooseModel, params, this.WorkTransferState, this.purchaseNewArray)
    },
    onSearch() {

    }
  }
}
</script>

<style lang="scss" scoped>
.title-box {
  padding: 26px 20px;
  background: rgba(0, 40, 77, 1);
  border-radius: 6px 6px 0 0;
}
.title-top-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.title-text {
  font-size: 24px;
  color: #ffffff;
  font-weight: 600;
}
.phone-text {
  font-size: 16px;
  color: #ffffff;
  margin-left: 20px;
}
.title-person {
  margin-top: 6px;
}
.title-person span {
  font-size: 14px;
  color: #ffffff;
}
.search-box {
  display: flex;
  align-items: center;
}
.title-ctn {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.title-item {
  width: 31%;
  height: 100px;
  border-radius: 6px;
  // padding: 30px 0;
  /* text-align: center; */
  display: flex;
  align-items: center;
  // border: 1px solid #ffffff;
}
.title-item-left {
  width: 10%;
  height: 100%;
  writing-mode: vertical-rl;
  text-orientation: upright;
  font-size: 18px;
  letter-spacing: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #ffffff;
  border-right: none;
  border-radius: 6px 0 0 6px;
}
.title-item-left span {
  margin-top: 10px;
}
.title-item-right {
  width: 90%;
  height: 100%;
  background: #eb7f8c;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  border: 1px solid #ffffff;
  border-left: none;
  border-radius: 0 6px 6px 0;
}
.money-text {
  font-size: 24px;
  color: #ffffff;
  font-weight: 600;
}
.money-title {
  font-size: 14px;
  color: #ffffff;
  margin-top: 2px;
}
.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin: 0 0 10px 10px;
}
.card-box {
  width: 100%;
  // display: flex;
  padding: 0 15px;
  margin-top: 20px;
}
.card-item {
  margin-bottom: 20px;
}
.card-item-box {
  min-height: 55px;
}
.card-item-ctn {
  margin-bottom: 5px;
}
.card-item-num {
  color: #333333;
}
.card-ele {
  width: 100%;
  height: 100px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #f2f2f2;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.card-ele:hover {
  background: #e7f4ff;
}
.card-ele:hover .card-ele-ctn-num {
  color: #1890ff;
}
.card-ele:hover .card-ele-ctn-text {
  color: #1890ff;
}
.card-ele-ctn {
  text-align: center;
}
.card-ele-ctn-num {
  font-weight: 600;
  font-size: 18px;
}
.card-ele-ctn-text {
  margin-top: 8px;
}
.detail-text {
  position: absolute;
  right: 12px;
  top: 6px;
  color: #1890ff;
  cursor: pointer;
}
.ant-col-5 {
  width: 20%;
}

.arrow-center {
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  color: #fff;
}
</style>