<template>
  <a-modal :title="title" :width="900" :visible="visible" :footer="null" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-row :getter="10">
        <a-col :md="24">
          <!-- 有tabs切换的数据 -->
          <!-- <div v-for="(item,index) in model.tab" :key="index">
            <a-row :gutter="10" v-if="index == tabIndex && item.searchInput && item.searchInput.length > 0">
              <a-col :md="queryCol" v-for="(rItem,rIndex) in item.searchInput" :key="rIndex">
                <a-form-item v-if="rItem.type == 'input'" :label="rItem.name" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input :placeholder="rItem.placeholder?rItem.placeholder:'请输入'" v-model="queryParam[rItem.key]"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="queryCol" style="text-align: right">
                <span class="table-page-search-buttons">
                  <a-button style="margin-right:10px;" @click="searchReset" icon="reload">重置</a-button>
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                </span>
              </a-col>
            </a-row>
          </div> -->
          <a-tabs v-if="model.tab && model.tab.length > 0" v-model="tabIndex" @change="changeTabs">
            <a-tab-pane :tab="item.name" v-for="(item,index) in model.tab" :key="index">
              <!-- 搜索 -->
              <a-card :bodyStyle="{ padding: '3px 0' }" style="margin-bottom: 12px;border: none;">
                <a-row :gutter="10" v-if="item.searchInput && item.searchInput.length > 0">
                  <a-col :md="queryCol" v-for="(rItem,rIndex) in item.searchInput" :key="rIndex">
                    <a-form-item v-if="rItem.type == 'input'" :label="rItem.name" :labelCol="labelCol" :wrapperCol="wrapperCol">
                      <a-input :placeholder="rItem.placeholder?rItem.placeholder:'请输入'" v-model="queryParam[rItem.key]"></a-input>
                    </a-form-item>
                  </a-col>
                  <a-col :md="queryCol" style="text-align: right">
                    <span class="table-page-search-buttons">
                      <a-button style="margin-right:10px;" @click="searchReset" icon="reload">重置</a-button>
                      <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                    </span>
                  </a-col>
                </a-row>
                <!-- 表格 -->
                <div style="border: 1px solid #e8e8e8;border-radius: 3px;border-bottom: none;padding: 8px 10px;">{{model.tableTitle || '查询结果'}}</div>
                <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="small" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length > 0?'400px':false, x: '1000px' }">
                  <!-- 字符串超长截取省略号显示-->
                  <span slot="component" slot-scope="text">
                    <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                    <j-ellipsis :value="text" v-else />
                  </span>
                  <!-- 注册地址 -->
                  <span slot="RegAreaName" slot-scope="text,record">
                    <j-ellipsis :value="text+(record.RegAddress?record.RegAddress:'')" />
                  </span>
                  <!-- 合同效期 -->
                  <span slot="ValidStartTime" slot-scope="text,record">
                    <j-ellipsis v-if="text || record.ValidEndTime" :value="text+'-'+(record.ValidEndTime?record.ValidEndTime:'')" />
                  </span>
                  <!-- 日期 -->
                  <span slot="CreateTime" slot-scope="text,record">
                    <j-ellipsis :value="text+'-'+(record.ExpirationTime?record.ExpirationTime:'')" />
                  </span>
                  <!-- 促销时间 -->
                  <span slot="AgreementStartTime" slot-scope="text,record">
                    <j-ellipsis :value="text+'-'+(record.AgreementEndTime?record.AgreementEndTime:'')" />
                  </span>
                  <!-- 是否活动 -->
                  <span slot="IsActiveVal" slot-scope="text">
                    <j-ellipsis :value="text==true?'是':'否'" />
                  </span>
                  <!-- 价格显示-->
                  <span slot="price" slot-scope="text">
                    <j-ellipsis :value="text?('¥'+text):(text==0?'0':'--')" />
                  </span>
                  <!-- 客户 -->
                  <span slot="CustomerName" slot-scope="text,record">
                    <j-ellipsis :value="text+(record.CustomerCode?('/'+record.CustomerCode):'')" />
                  </span>
                   <!-- 返利时间 -->
                  <span slot="RebateTime" slot-scope="text,record">
                    <j-ellipsis :value="text?text.substring(0, 11):' -- '" />
                  </span>
                </a-table>
              </a-card>
            </a-tab-pane>
          </a-tabs>

          <div v-else>
            <a-row :gutter="10" v-if="model.searchInput && model.searchInput.length > 0">
              <a-col :md="queryCol" v-for="(rItem,rIndex) in model.searchInput" :key="rIndex">
                <a-form-item v-if="rItem.type == 'input'" :label="rItem.name" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input :placeholder="rItem.placeholder?rItem.placeholder:'请输入'" v-model="queryParam[rItem.key]"></a-input>
                </a-form-item>
              </a-col>

              <a-col :md="queryCol" style="text-align: right">
                <span class="table-page-search-buttons">
                  <a-button style="margin-right:10px;" @click="searchReset" icon="reload">重置</a-button>
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                </span>
              </a-col>
            </a-row>
            <div style="border: 1px solid #e8e8e8;border-radius: 3px;border-bottom: none;padding: 8px 10px;">{{model.tableTitle || '查询结果'}}</div>
            <a-table :bordered="false" ref="table" :rowKey="(record,index)=>index" size="small" :columns="columns" :dataSource="dataSource" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: dataSource.length > 0?'400px':false, x: '1000px' }">
              <!-- 字符串超长截取省略号显示-->
              <span slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </span>
              <!-- 注册地址 -->
              <span slot="RegAreaName" slot-scope="text,record">
                <j-ellipsis :value="text+(record.RegAddress?record.RegAddress:'')" />
              </span>
              <!-- 合同效期 -->
              <span slot="ValidStartTime" slot-scope="text,record">
                <j-ellipsis v-if="text || record.ValidEndTime" :value="text+'-'+(record.ValidEndTime?record.ValidEndTime:'')" />
              </span>
              <!-- 日期 -->
              <span slot="CreateTime" slot-scope="text,record">
                <j-ellipsis :value="text+'-'+(record.ExpirationTime?record.ExpirationTime:'')" />
              </span>
              <!-- 促销时间 -->
              <span slot="AgreementStartTime" slot-scope="text,record">
                <j-ellipsis :value="text+'-'+(record.AgreementEndTime?record.AgreementEndTime:'')" />
              </span>
              <!-- 价格显示-->
              <span slot="price" slot-scope="text">
                <j-ellipsis :value="text||text==0?(text!=0?'¥'+text:0):'--'" />
              </span>
              <!-- 是否活动 -->
              <span slot="IsActiveVal" slot-scope="text">
                <j-ellipsis :value="text==true?'是':'否'" />
              </span>
              <!-- 客户 -->
              <span slot="CustomerName" slot-scope="text,record">
                <j-ellipsis :value="text+(record.CustomerCode?('/'+record.CustomerCode):'')" />
              </span>
              <!-- 返利时间 -->
              <span slot="RebateTime" slot-scope="text,record">
                <j-ellipsis :value="text?text.substring(0, 11):' -- '" />
              </span>
            </a-table>
          </div>

        </a-col>
      </a-row>
    </div>
    <!-- <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">
        关闭
      </a-button>
    </a-row> -->
  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from "@/mixins/ListMixin";
import { getAction, putAction, postAction } from "@/api/manage";
const JEllipsis = () => import("@/components/jeecg/JEllipsis");
const columns0 = [
  {
    title: '商品名称',
    dataIndex: 'ErpGoodsName',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '规格',
    dataIndex: 'PackingSpecification',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '单位',
    dataIndex: 'PackageUnit',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '生产厂商',
    dataIndex: 'BrandManufacturer',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '商品编号',
    dataIndex: 'ErpGoodsCode',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '首营状态',
    dataIndex: 'AuthBusinessTypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '是否活动',
    dataIndex: 'IsActive',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'IsActiveVal' },
  },
]
const columns0_1 = [
  {
    title: '供应商名称',
    dataIndex: 'SupplierName',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '供应商编号',
    dataIndex: 'SupplierCode',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '企业类别',
    dataIndex: 'TypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '注册地址',
    dataIndex: 'RegAreaName',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'RegAreaName' },
  },
  {
    title: '联系电话',
    dataIndex: 'LinkPhone',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '首营状态',
    dataIndex: 'AuthBusinessTypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '是否活动',
    dataIndex: 'IsActive',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'IsActiveVal' },
  },
]
const columns1 = [
  {
    title: '计划编号',
    dataIndex: 'PlanNo',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '供应商',
    dataIndex: 'SupplierName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '计划金额',
    dataIndex: 'PlanAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '付款方式',
    dataIndex: 'PaymentModeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '是否生成订单',
    dataIndex: 'IsGenerateOrder',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'IsActiveVal' },
  },
  {
    title: '计划类型',
    dataIndex: 'OrderTypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns2 = [
  {
    title: '订单编号',
    dataIndex: 'PurchaseOrderNo',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '供应商',
    dataIndex: 'SupplierName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单金额',
    dataIndex: 'OrderAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '付款方式',
    dataIndex: 'PaymentModeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单状态',
    dataIndex: 'StatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '付款状态',
    dataIndex: 'PaymentStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单类型',
    dataIndex: 'OrderTypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns3 = [
  {
    title: '付款单号',
    dataIndex: 'RemittedOrderNo',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '供应商',
    dataIndex: 'SupplierName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '打款状态',
    dataIndex: 'RemittanceStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns4 = [
  {
    title: '上传发票单号',
    dataIndex: 'InvoiceNo',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '供应商',
    dataIndex: 'SupplierName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '付款方式',
    dataIndex: 'PaymentModeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '执行状态',
    dataIndex: 'ExecuteStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns5 = [
  {
    title: '调价单号',
    dataIndex: 'PriceAdjustmentNo',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单编号',
    dataIndex: 'PurchaseOrderNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '供应商',
    dataIndex: 'SupplierName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '调价金额',
    dataIndex: 'TotalPriceAdjustment',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '结算状态',
    dataIndex: 'SettlementStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '执行状态',
    dataIndex: 'ExecuteStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns6 = [
  {
    title: '票折单号',
    dataIndex: 'InvoiceDiscountNo',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单编号',
    dataIndex: 'PurchaseOrderNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '供应商',
    dataIndex: 'SupplierName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '票折金额',
    dataIndex: 'TotalPriceInvoiceDiscount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '结算状态',
    dataIndex: 'SettlementStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '执行状态',
    dataIndex: 'ExecuteStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns7 = [
  {
    title: '退货单号',
    dataIndex: 'RefundOrderNo',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单编号',
    dataIndex: 'PurchaseOrderNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '供应商',
    dataIndex: 'SupplierName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '退货金额',
    dataIndex: 'TotalRefundAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '执行状态',
    dataIndex: 'ExecuteStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '退货单状态',
    dataIndex: 'RefundStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '结算状态',
    dataIndex: 'SettlementStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns8 = [
  {
    title: '合同编号',
    dataIndex: 'ContractNo',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '供应商',
    dataIndex: 'SupplierName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '合同类型',
    dataIndex: 'ContractTypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '合同效期',
    dataIndex: 'ValidStartTime',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'ValidStartTime' },
  },
  {
    title: '结算方式',
    dataIndex: 'SettlementMethodStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '结算账期',
    dataIndex: 'SettlementPeriod',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '关联订单数量',
    dataIndex: 'PurchaseOrderCount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '创建时间',
    dataIndex: 'CreateTime',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns9 = [
  {
    title: '协议编号',
    dataIndex: 'AgreementNo',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '甲方',
    dataIndex: 'PartyFirstName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '促销时间',
    dataIndex: 'AgreementStartTime',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'AgreementStartTime' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '协议状态',
    dataIndex: 'AgreementStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款状态',
    dataIndex: 'RecognitionStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '应收金额',
    dataIndex: 'TotalRebateAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '已认款金额',
    dataIndex: 'TotalRecognitionAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '额外收入金额',
    dataIndex: 'AdditionalAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '未认款余额',
    dataIndex: 'UnRecognitionAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
]
const columns9_1 = [
  {
    title: '订单编号',
    dataIndex: 'PurchaseOrderNo',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '供应商',
    dataIndex: 'SupplierName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '日期',
    dataIndex: 'CreateTime',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'CreateTime' },
  },
  {
    title: '协议状态',
    dataIndex: 'AgreementStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款状态',
    dataIndex: 'RecognitionStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '应收金额',
    dataIndex: 'TotalRebateAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '已收款金额',
    dataIndex: 'RecognitionAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '额外收入金额',
    dataIndex: 'AdditionalAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '未认款余额',
    dataIndex: 'UnRecognitionAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
// 通用协议
const columns9_2 = [
  {
    title: '协议编号',
    dataIndex: 'AgreementNo',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '甲方',
    dataIndex: 'PartyFirstName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '是否按品种',
    dataIndex: 'IsLimitGoods',
    width: 100,
    ellipsis: true,
    customRender:(r,t)=>{
      return r?'是':'否'
    }
  },
  {
    title: '返利时间',
    dataIndex: 'RebateTime',
    width: 120,
    ellipsis: true,
    scopedSlots: { customRender: 'RebateTime' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '协议状态',
    dataIndex: 'AgreementStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款状态',
    dataIndex: 'RecognitionStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '应收金额',
    dataIndex: 'RebateAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '已认款金额',
    dataIndex: 'TotalRecognitionAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '额外收入金额',
    dataIndex: 'TotalAdditionalAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '未认款余额',
    dataIndex: 'RemainingAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
]
const columns10 = [
  {
    title: '认款单号',
    dataIndex: 'RecognitionOrderNo',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款方式',
    dataIndex: 'RecognitionOrderModeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款单类型',
    dataIndex: 'RecognitionOrderTypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款金额',
    dataIndex: 'TotalRecognitionAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
// 商销
const columns11 = [
  {
    title: '客户名称',
    dataIndex: 'CustomerName',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '客户编号',
    dataIndex: 'CustomerCode',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '企业类别',
    dataIndex: 'Type',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '注册地址',
    dataIndex: 'RegAreaName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'RegAreaName' },
  },
  {
    title: '联系电话',
    dataIndex: 'LinkPhone',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '首营状态',
    dataIndex: 'AuthBusinessStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '是否活动',
    dataIndex: 'IsActive',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'IsActiveVal' },
  },
]
const columns12 = [
  {
    title: '客户',
    dataIndex: 'CustomerName',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'CustomerName' },
  },
  {
    title: '订单编号',
    dataIndex: 'SalesOrderCode',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单金额',
    dataIndex: 'TotalAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '付款方式',
    dataIndex: 'PaymentModeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单状态',
    dataIndex: 'OrderStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款状态',
    dataIndex: 'OrderAcceptanceStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单类型',
    dataIndex: 'TypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns13 = [
  {
    title: '调价单号',
    dataIndex: 'SalesOrderAdjustPriceCode',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单编号',
    dataIndex: 'SalesOrderCode',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '客户',
    dataIndex: 'CustomerName',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'CustomerName' },
  },
  {
    title: '调价金额',
    dataIndex: 'AdjustAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '结算状态',
    dataIndex: 'AdjustPriceSettleStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '执行状态',
    dataIndex: 'AdjustPriceExecuteStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns14 = [
  {
    title: '售后单号',
    dataIndex: 'SalesAfterOrderCode',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单编号',
    dataIndex: 'SalesOrderCode',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '客户',
    dataIndex: 'CustomerName',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'CustomerName' },
  },
  {
    title: '售后范围',
    dataIndex: 'SaleAfterScopeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '售后金额',
    dataIndex: 'RefundAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '售后单状态',
    dataIndex: 'SaleAfterOrderStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '付款状态',
    dataIndex: 'SaleAfterPayStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '执行状态',
    dataIndex: 'SaleAfterExecuteStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns15 = [
  {
    title: '开票单号',
    dataIndex: 'SalesOrderInvoiceApplyCode',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '订单编号',
    dataIndex: 'SalesOrderCode',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '客户',
    dataIndex: 'CustomerName',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'CustomerName' },
  },
  {
    title: '发票类型',
    dataIndex: 'InvoiceTypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '开票金额',
    dataIndex: 'InvoiceAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '开票状态',
    dataIndex: 'InvoiceStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },

]
const columns16 = [
  {
    title: '订单编号',
    dataIndex: 'SalesOrderCode',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '客户',
    dataIndex: 'CustomerName',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'CustomerName' },
  },
  {
    title: '订单金额',
    dataIndex: 'SalesOrderTotalAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: 'ERP登记',
    dataIndex: 'PushERPStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '七彩签章',
    dataIndex: 'CASignStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]
const columns17 = [
  {
    title: '认款单号',
    dataIndex: 'RecognitionOrderNo',
    width: 200,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款方式',
    dataIndex: 'RecognitionOrderModeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款单类型',
    dataIndex: 'RecognitionOrderTypeStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
  {
    title: '认款金额',
    dataIndex: 'TotalRecognitionAmount',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'price' },
  },
  {
    title: '审核状态',
    dataIndex: 'AuditStatusStr',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'component' },
  },
]

export default {
  name: "PersonalCheckInfoModal",
  mixins: [ListMixin],
  components: { JEllipsis },
  data() {
    return {
      title: "详情",
      titleVal: "人员",
      visible: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      chooseModel: {},
      purchaseNewArray: {},
      labelCol: {
        xs: { span: 8 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 16 },
        sm: { span: 20 }
      },
      queryCol: 12,
      queryParam: {},
      paramsCommon: {},
      tabIndex: 0,
      columns: columns0,
      isInitData: false,
      httpHead: 'P36010',
      url: {
        list: "",
        listType: "POST"
      },
    };
  },
  computed: {

  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(item, chooseModel, paramsCommon, WorkTransferState, purchaseNewArray) {
      console.log('show  ',item, chooseModel, paramsCommon, WorkTransferState)
      if (paramsCommon.QueryType) {
        this.titleVal = paramsCommon.QueryType == 1 ? '人员' : '个人'
      }
      // QueryType 1个人一览表 2工作交接
      // TransferRange 1全部交接 2部分交接
      let QueryType = paramsCommon.QueryType
      let TransferRange = ''
      if (QueryType == 2) {
        TransferRange = WorkTransferState == '全部交接' ? 1 : 2
      } else {
        TransferRange = ''
      }
      switch (item.type) {
        case 0://采购
          if (QueryType == 1 || TransferRange == 1) {
            this.title = '查看人员负责品种明细'
          } else if (TransferRange == 2) {
            this.title = '查看个人转移品种情况'
          }
          this.columns = columns0
          break;
        case 1:
          if (QueryType == 1 || TransferRange == 1) {
            this.title = '查看人员负责采购计划明细'
          } else if (TransferRange == 2) {
            this.title = '查看个人转移采购订单情况'
          }
          this.columns = columns1
          break;
        case 2:
          this.title = '查看' + this.titleVal + '负责采购订单明细'
          this.columns = columns2
          break;
        case 3:
          this.title = '查看' + this.titleVal + '负责采购付款单明细'
          this.columns = columns3
          break;
        case 4:
          this.title = '查看' + this.titleVal + '负责采购发票单明细'
          this.columns = columns4
          break;
        case 5:
          this.title = '查看' + this.titleVal + '负责采购调价单明细'
          this.columns = columns5
          break;
        case 6:
          this.title = '查看' + this.titleVal + '负责采购票折单明细'
          this.columns = columns6
          break;
        case 7:
          this.title = '查看' + this.titleVal + '负责采购退货单明细'
          this.columns = columns7
          break;
        case 8:
          this.title = '查看' + this.titleVal + '负责采购合同明细'
          this.columns = columns8
          break;
        case 9:
          this.title = '查看' + this.titleVal + '负责采购协议明细'
          this.columns = columns9
          break;
        case 10:
          this.title = '查看' + this.titleVal + '负责认款单明细'
          this.columns = columns10
          break;
        case 11:
          // 商销
          this.title = '查看' + this.titleVal + '负责客户明细'
          this.columns = columns11
          break;
        case 12:
          this.title = '查看' + this.titleVal + '负责商销订单明细'
          this.columns = columns12
          break;
        case 13:
          this.title = '查看' + this.titleVal + '负责商销调价单明细'
          this.columns = columns13
          break;
        case 14:
          this.title = '查看' + this.titleVal + '负责商销售后单明细'
          this.columns = columns14
          break;
        case 15:
          this.title = '查看' + this.titleVal + '负责商销开票单明细'
          this.columns = columns15
          break;
        case 16:
          this.title = '查看' + this.titleVal + '负责含特回执情况'
          this.columns = columns16
          break;
        case 17:
          this.title = '查看' + this.titleVal + '负责认款单明细'
          this.columns = columns17
          break;
        default: this.columns = columns0
      }
      this.model = Object.assign({}, item);
      this.purchaseNewArray = purchaseNewArray
      this.chooseModel = chooseModel
      this.paramsCommon = paramsCommon
      this.queryParam = Object.assign({}, paramsCommon)
      this.tabIndex = item.index || 0
      this.loadData(1, this.model)
      this.visible = true;
    },
    loadData(arg, item) {
      if (arg === 1) {
        this.ipagination.current = 1
      }
      let params = this.getQueryParams() //查询条件
      let url = ''
      if (item) {
        this.model = item
      }

      if (this.model.params) {
        params = {
          ...this.model.params,
          ...this.getQueryParams()
        }
      } else if (this.model.tab) {
        params = {
          ...this.model.tab[this.model.index].params,
          ...this.getQueryParams()
        }
      }

      if (this.model.url) {
        url = this.model.url
      } else if (this.model.tab && this.model.tab.length > 0) {
        url = this.tabIndex ? this.model.tab[this.tabIndex].url : this.model.tab[0].url
      }
      switch (this.model.type) {
        case 0: {
          // 负责品种
          if (this.tabIndex == 0) {
            this.columns = columns0
          } else if (this.tabIndex == 1) {
            this.columns = columns0_1
          }
        }
          break;
        case 1:
          this.columns = columns1
          break;
        case 2:
          this.columns = columns2
          break;
        case 3:
          this.columns = columns3
          break;
        case 4:
          this.columns = columns4
          break;
        case 5:
          this.columns = columns5
          break;
        case 6:
          this.columns = columns6
          break;
        case 7:
          this.columns = columns7
          break;
        case 8:
          this.columns = columns8
          break;
        case 9:
          // console.log('this.tabIndex', this.tabIndex)
          if (this.tabIndex == 0) {
            this.columns = columns9
          } else if (this.tabIndex == 1) {
            this.columns = columns9_1
          } else if (this.tabIndex == 2) {
            this.columns = columns9
          } else if (this.tabIndex == 3) {
            this.columns = columns9_2
          }
          break;
        case 10: {
          this.columns = columns10
        }
          break;
        case 11:
          this.columns = columns11
          break;
        case 12: {
          this.columns = columns12
        }
          break;
        case 13:
          this.columns = columns13
          break;
        case 14:
          this.columns = columns14
          break;
        case 15:
          this.columns = columns15
          break;
        case 16:
          this.columns = columns16
          break;
        case 17:
          this.columns = columns17
          break;
        default: this.columns = columns0
      }
      this.loading = true
      postAction(url, params, this.httpHead).then(res => {
        if (res.IsSuccess) {
          let source = res.Data ? res.Data : []
          source = this.handleLoadedData(source)
          if (typeof source === Array) {
            source.forEach(item => {
              item.loading = false
            })
          }
          this.dataSource = source
          this.ipagination.total = res.Count ? res.Count : 0
        } else {
          this.$message.warning(res.Msg)
          this.dataSource = []
          this.ipagination.total = 0
          if (typeof this.loadFail == 'function') {
            this.loadFail(res)
          }
        }
        this.loading = false
        if (typeof this.loadAfter == 'function') {
          this.loadAfter()
        }
      })
    },
    changeTabs(e) {
      this.tabIndex = e
      if (this.model.tab) {
        this.model.params = this.model.tab[this.tabIndex].params || {}
      }
      this.dataSource = []
      this.queryParam = Object.assign({}, this.paramsCommon)
      this.loadData(1, this.model)
    },
    searchQuery() {
      this.loadData(1, this.model)
    },
    searchReset() {
      this.queryParam = Object.assign({}, this.paramsCommon)
      this.loadData(1, this.model)
    },
    // 确定
    handleOk() {

    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
