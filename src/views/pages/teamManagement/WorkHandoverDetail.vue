<!--
 * @Author: yhy
 * @Description: 工作交接单详情
 * @Date: 2023/07/17
-->
<template>
  <div>
    <a-card style="margin-bottom: 10px" v-if="workTransferInfo && acType == 1">
      <a-descriptions title="工作交接单详情" :column="4">
        <a-descriptions-item label="交接单号">{{ workTransferInfo.TransferNo || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="人员类型">{{ getUserTypesStr(workTransferInfo.UserTypesStr)}}</a-descriptions-item>
        <!-- <a-descriptions-item label="采购交接范围" v-if="isCaiG(workTransferInfo.UserTypes)">{{ workTransferInfo.PurchaserTransferRangeStr || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="商销交接范围" v-if="isShangX(workTransferInfo.UserTypes)">{{ workTransferInfo.SalerTransferRangeStr || ' -- ' }}</a-descriptions-item> -->
        <a-descriptions-item label="交接范围">{{ workTransferInfo.TransferRangeStr || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="审核状态">{{ workTransferInfo.AuditStatusStr || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="交接单状态">{{ workTransferInfo.TransferStatusStr || ' -- ' }}</a-descriptions-item>
        <!-- <a-descriptions-item label="归属人">{{ workTransferInfo.OwnerByName || ' -- ' }}</a-descriptions-item> -->
        <a-descriptions-item label="创建人">{{ workTransferInfo.CreateByName || ' -- ' }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ workTransferInfo.CreateTime || ' -- ' }}</a-descriptions-item>
      </a-descriptions>
    </a-card>
    <a-card :bodyStyle="{ padding: '0 0 0 0' }">
      <a-tabs>
        <a-tab-pane :key="1" tab="交接内容">
          <div :class="acType == 1 ? 'content53' : 'content70'">
            <PersonalBaseInfo
              :type="2"
              :WorkTransferId="WorkTransferId"
              :WorkTransferState="getWorkTransferState(workTransferInfo.UserTypes)"
              :SalerTransferRange="workTransferInfo.SalerTransferRange"
              :PurchaserTransferRange="workTransferInfo.PurchaserTransferRange"
              v-if="WorkTransferId"
            />
          </div>
        </a-tab-pane>
        <a-tab-pane :key="6" tab="审核信息"
          ><div :class="acType == 1 ? 'content53' : 'content70'">
            <CusAuditInformation
              ref="CusAuditInformation"
              :AuditId="workTransferInfo.AuditId"
              v-if="workTransferInfo"
            />
          </div>
        </a-tab-pane>
      </a-tabs>
      <!-- <a-affix :offset-bottom="10" class="affix" style="padding-top: 20px" v-if="acType == 2 || acType == 3"> -->
      <a-affix
        class="affix"
        :offset-bottom="10"
        style="padding-top: 10px; padding-bottom: 10px"
        v-if="acType == 2 || acType == 3"
      >
        <template v-if="acType == 2">
          <YYLButton
            :loading="confirnLoading"
            type="primary"
            class="mr8"
            menuId="db27c162-7767-47ff-bf89-752d5250ed72"
            @click="onPassClick"
            text="通过审核"
          ></YYLButton>
          <YYLButton
            type="danger"
            menuId="25c57468-2b14-4917-9eba-699b273eaae4"
            @click="onRejectClick"
            text="驳回审核"
          ></YYLButton>
        </template>
        <template v-else>
          <YYLButton
            type="primary"
            menuId="b79d6288-ea13-4dfb-a453-b638250157ef"
            @click="onConfirmClick"
            :loading="loading"
            text="确认接收"
          ></YYLButton>
        </template>
      </a-affix>
    </a-card>

    <!-- 审核弹框 -->
    <AuditRemarkModal ref="auditRemarkModal" @ok="saveAuditData" />
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
export default {
  name: 'WorkHandoverDetail',
  mixins: [EditMixin],
  data() {
    return {
      activeTabKey: 1,
      loading: false,
      WorkTransferId: '', //供应商id
      httpHead: 'P36010',
      url: {
        detail: '/{v}/WorkTransfer/Detail',
        approval: '/v1/ApprovalWorkFlowInstance/GetApprovalResults',
        auditInstance: '/{v}/ApprovalWorkFlowInstance/AuditInstance',
        comfirm: '/{v}/WorkTransfer/Confirm',
      },
      workTransferInfo: {},
      userId: Vue.ls.get(USER_ID),
      acType: 1, //1详情 2审核 3确认
      confirnLoading: false,
    }
  },
  mounted() {
    if (this.$route.query) {
      this.WorkTransferId = this.$route.query.id || ''
      this.acType = this.$route.query.acType ? Number(this.$route.query.acType) : 1
      this.getInfo()
      this.setTitle(this.acType == 1 ? '工作交接单详情' : this.acType == 2 ? '审核工作交接单' : '确认工作交接单')
    }
  },
  created() {},
  methods: {
     // 交接范围
    // 商销交接范围 为null 采购交接范围不为null ,那么交接范围=采购交接范围
    // 采购交接范围 为null 商销交接范围不为null ,那么交接范围=商销交接范围
    // 采购交接范围不为null，商销交接范围不为null,采购交接和商销交接都为全部交接的情况下，为全部交接，其余都是部分交接
    getWorkTransferState(type){
      let str = ''
      // 是采购人员 不是商销人员
      if(this.isCaiG(type) && !this.isShangX(type)){
        return str = this.workTransferInfo.PurchaserTransferRange == 1 ? '全部交接' : '部分交接'
      }
      // 不是采购人员 是商销人员
      if(!this.isCaiG(type) && this.isShangX(type)){
        return str = this.workTransferInfo.SalerTransferRange == 1 ? '全部交接' : '部分交接'
      }
      // 是采购人员 是商销人员
      if(this.isCaiG(type) && this.isShangX(type)){
        if(this.workTransferInfo.PurchaserTransferRange == 1 && this.workTransferInfo.SalerTransferRange == 1){
          str = '全部交接'
        }else{
          str = '部分交接'
        }
        return str 
      }
    },
    // 是采购人员
    isCaiG(type=[]){
      return type.some(v=>v==1)
    },
    // 是商销人员
    isShangX(type=[]){
      return type.some(v=>v==2)
    },
    // 人员类型
    getUserTypesStr(type=[]){
      return type.length ? type.join('/') : ' -- '
    },
    getInfo() {
      this.loading = true
      getAction(this.url.detail, { id: this.WorkTransferId }, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.workTransferInfo = res.Data
            if(res.Data.AuditId) {
              this.getApprovalResults()
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    getApprovalResults() {
      if (this.workTransferInfo && this.workTransferInfo.AuditId) {
        getAction(this.url.approval, { bussinessNo: this.workTransferInfo.AuditId }, 'P36005').then((res) => {
          if (res.IsSuccess) {
            this.approval = res.Data || {}
          } else {
            this.$message.error(res.Msg)
          }
        })
      }
    },
    onTabsChange(key) {
      this.activeTabKey = 1
    },
    /**
     * 通过
     */
    onPassClick() {
      this.showAuditModel(1)
    },
    /**
     * 显示审核弹窗
     * @param {*} type 1通过  2驳回
     */
    showAuditModel(type) {
      if (this.$refs.auditRemarkModal) {
        this.$refs.auditRemarkModal.show(type, {
          ApprovalWorkFlowInstanceId: this.approval ? this.approval.ApprovalWorkFlowInstanceId : '',
          IsHasSuperior: this.approval ? this.approval.IsHasSuperior : false,
        })
      }
    },
    saveAuditData(formData) {
      if (formData && formData.Type == 1) {
        let params = {
          AuditStatus: 2,
          AuditOpinion: formData['Remark'],
          ApprovalWorkFlowInstanceId: formData['ApprovalWorkFlowInstanceId'],
        }

        postAction(this.url.auditInstance, params, 'P36005').then((res) => {
          if (!res.IsSuccess) {
            this.$message.error(res.Msg)
            return
          }
          this.$message.success('操作成功！')
          this.$router.go(-1)
        })
      } else {
        this.$router.go(-1)
      }
    },
    onRejectClick() {
      this.showAuditModel(2)
    },
    onConfirmClick() {
      this.loading = true
      getAction(
        this.url.comfirm,
        {
          WorkTransferId: this.WorkTransferId,
        },
        this.httpHead
      )
        .then((res) => {
          if (res.IsSuccess) {
            this.$message.success('操作成功')
            // this.initiateAudit(res.Data)
            this.$router.go(-1)
            // this.acType = 1;
            // this.getInfo();
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally((res) => {
          this.loading = false
        })
    },
    // initiateAudit(data) {
    //   postAction(
    //     '/v1/ApprovalWorkFlowInstance/CreateApproval',
    //     {
    //       MainBussinessNo: data.WorkTransferId,
    //       BussinessNo: data.WorkTransferAuditId,
    //       Scenes: 24,
    //       OpratorId: Vue.ls.get(USER_ID),
    //     },
    //     'P36005'
    //   ).then((res) => {
    //     if (res.IsSuccess) {
    //       this.$message.success('操作成功')
    //       this.backPage()
    //     } else {
    //       this.$message.warning(res.Msg)
    //     }
    //   })
    // }
    
  },
}
</script>

<style scoped>

.content53 {
  height: 53vh;
  overflow-y: auto;
}
.content70 {
  height: 70vh;
  overflow-y: auto;
}
</style>
