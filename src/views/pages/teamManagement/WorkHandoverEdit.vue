<!-- 新增工作交接单 -->
<template>
  <div>
    <div style="background: #fbe6e9;padding:10px 16px;color:red;" v-if="model.AuditStatus == 3 && model.AuditOpinion" :md="24"><span>驳回原因：{{model.AuditOpinion || ''}}</span></div>
    <a-card style="min-height:78vh;margin: 0 0 15px 0">
      <a-spin :spinning="spinning">
        <a-steps :current="curStep" class="w50-center">
          <a-step title="选择交接人员" />
          <a-step title="核对交接内容" />
        </a-steps>
        <a-divider />
        <div class="form-model-style" v-if="curStep === 0">
          <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical">
            <template>
              <a-row :gutter="gutter">
                <a-col :span="md">
                  <a-form-model-item label="交付人：" prop="DeliverName">
                    <SingleInputSearchView placeholder="搜索姓名/手机号" width="100%" :httpParams="{
                        pageIndex: 1,
                        pageSize: 200,
                        IsValid: true,
                      }" keyWord="keyWord" httpHead="P36010" :Url="url.userList" :value="model.DeliverId" :name="model.DeliverName" :disabled="deliveryDisaled" :isAbsolute="true" @clear="
                        () => {
                          model.DeliverId = ''
                          model.DeliverName = ''
                          model.ReceiverId = ''
                          model.ReceiverName = ''
                          curDeliver=undefined
                          receiverDis=true
                        }
                      " @change="changeUser" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="md">
                  <a-form-model-item label="接收人：" prop="ReceiverName">
                    <SingleInputSearchView ref="receiverSelect" :isAuto="false" :placeholder="receiverDis?'请先选择交付人':'搜索姓名/手机号'" width="100%" :disabled="receiverDis" :httpParams="{
                        pageIndex: 1,
                        pageSize: 200,
                        IsValid: true,
                        DeliverId: model.DeliverId,
                      }" keyWord="keyWord" httpHead="P36010" :Url="url.userList" :value="model.ReceiverId" :name="model.ReceiverName" :isAbsolute="true" @clear="
                        () => {
                          model.ReceiverId = ''
                          model.ReceiverName = ''
                        }
                      " @change="changeReceiver" />
                  </a-form-model-item>
                </a-col>
                <!-- 采购 -->
                <a-col :span="24"  v-if="curDeliver && isCaiG(curDeliver.Type)">
                  <a-card>
                    <a-row>
                      <a-col :span="md">
                        <a-form-model-item label="采购交接范围：" prop="PurchaserTransferRange">
                          <a-radio-group v-model="model.PurchaserTransferRange" @change="(e) => onTypeChange(e,'PurchaserTransferRange')">
                            <a-radio :value="1"> 全部交接 </a-radio>
                            <a-radio :value="2"> 部分交接 </a-radio>
                            <a-radio :value="3" v-if="curDeliver && isShangX(curDeliver.Type)"> 暂不交接 </a-radio>
                          </a-radio-group>
                        </a-form-model-item>
                      </a-col>
                      <a-col :span="24" v-if="model.PurchaserTransferRange && model.PurchaserTransferRange == 2">
                        <div class="erp-goods">转移品种：</div>
                        <a-card title="所选品种相关的采购业务单据将一并转移" style="width: 100%">
                          <div slot="extra">
                            <a-button style="margin-right: 10px" @click="clearTableData('采购')">清空</a-button>
                            <a-button style="margin-right: 10px" type="primary" @click="chooseTableData('导入')"> 导 入 </a-button>
                            <a-button type="primary" @click="chooseTableData(1)"> 选 择 </a-button>
                          </div>
                          <div>
                            <!--  v-if="isCaiG(model.UserTypes)" -->
                            <WorkHandoverEditTable ref="goodsWorkHandoverEditTable" :WorkTransferId="id" :type="1" @delTableData="delTableData" :dataList="tableData.dataSource" @loadAfter="loadAfterWorkHandoverEdit" />
                          </div>
                        </a-card>
                      </a-col>
                    </a-row>
                  </a-card>
                </a-col>
                <!-- 商销 -->
                <a-col :span="24" v-if="curDeliver && isShangX(curDeliver.Type)">
                  <a-card>
                    <a-row>
                      <a-col :span="md">
                        <a-form-model-item label="商销交接范围：" prop="SalerTransferRange">
                          <a-radio-group v-model="model.SalerTransferRange" @change="(e) => onTypeChange(e,'SalerTransferRange')">
                            <a-radio :value="1"> 全部交接 </a-radio>
                            <a-radio :value="2"> 部分交接 </a-radio>
                            <a-radio :value="3"  v-if="curDeliver && isCaiG(curDeliver.Type)"> 暂不交接 </a-radio>
                          </a-radio-group>
                        </a-form-model-item>
                      </a-col>
                      <a-col :span="24" v-if="model.SalerTransferRange && model.SalerTransferRange == 2">
                        <div class="erp-goods">转移客户：</div>
                        <a-card title="所选客户相关的商销业务单据将一并转移" style="width: 100%">
                          <div slot="extra">
                            <a-button style="margin-right: 10px" @click="clearTableData('商销')">清空</a-button>
                            <a-button type="primary" @click="chooseTableData(2)"> 选 择 </a-button>
                          </div>
                          <div>
                            <!--  v-if="isShangX(model.UserTypes)" -->
                            <WorkHandoverEditTable ref="cusWorkHandoverEditTable" :WorkTransferId="id" :type="2" @delTableData="delTableData" :dataList="tableData.dataSource1"   @loadAfter="loadAfterWorkHandoverEdit"/>
                          </div>
                        </a-card>
                      </a-col>
                    </a-row>
                  </a-card>
                </a-col>
              </a-row>
            </template>
          </a-form-model>
        </div>
        <PersonalBaseInfo 
        ref="PersonalBaseInfo" 
        :isEdit="!!isEdit" 
        :isAdd="!!isAdd" 
        :type="2" 
        v-else-if="curStep === 1" 
        :WorkTransferId="acType&&acType!=2?null:model.Id" 
        :Id="model.DeliverId" 
        :Customers="model.Customers" 
        :GoodsSpus="model.GoodsSpus" 
        :ReceiverId="model.ReceiverId" 
        :SalerTransferRange="model.SalerTransferRange"
        :PurchaserTransferRange="model.PurchaserTransferRange"
        :WorkTransferState="getWorkTransferState(curDeliver.Type)"
        @spinning="onSpinning"
        ></PersonalBaseInfo>
      </a-spin>
      <a-affix :offset-bottom="0" style="float: right; width: 100%; text-align: right">
        <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
          <a-button class="mr5" @click="prevStep()" v-if="curStep === 1" :loading="loading2">上一步</a-button>
          <YYLButton menuId="535dc29f-78f9-4cb0-a5c7-74096e835c35" :addHeader="false" type="primary" class="mr5" v-if="curStep === 0" @click="save(true, 1)" text="下一步" :loading="loading1"></YYLButton>
          <YYLButton menuId="4bd5ba94-adb6-4b46-a139-5f366a073408" type="primary" class="mr5" v-if="curStep === 1" @click="save(false, 2)" text="提交审核" :loading="loading2"></YYLButton>
          <YYLButton menuId="1cbf0aee-e302-4e3f-948e-c9448f724a86" type="" class="mr5" @click="save(true, 2)" text="存为草稿" :loading="loading1"></YYLButton>
        </a-card>
      </a-affix>
    </a-card>
    <!-- 表格选择数据弹窗 -->
     <!-- 商销 2-->
    <TableSelectDataModal ref="TableSelectDataShangXModal" showSelectedData :selectTableData="selectTableData1" @chooseData="(data)=>chooseData(data,2)"></TableSelectDataModal>
    <!-- 采购 1-->
    <TableSelectDataModal ref="TableSelectDataModal" showSelectedData :selectTableData="selectTableData" @chooseData="(data)=>chooseData(data,1)"></TableSelectDataModal>
  
     <!-- 导入 -->
   <BatchImportSimpleModal ref="iBatchImportGoodsModal" 
    :modalTitle="'导入交接商品'" 
    :tableColumns="goodsImportColumns" 
    :searchParamsList="searchImportInput" 
    :importConfig="goodsImportConfig" 
    :importUrl="`/v1/TeamGoods/ImportTempTeamGoodsList/ImportTempTeamGoodsListAsync?DeliverId=${model.DeliverId}`" 
    importHttpHead='P36010' 
    @ok="(e)=>handleBatchImportModalOk(e,'goods')" />
  </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import { getAction, putAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
import { max } from 'moment'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  description: '工作交接单编辑',
  name: 'WorkHandoverEdit',
  mixins: [EditMixin, ListMixin],
  components: { JEllipsis },
  data() {
    return {
      spinning: false,
      curStep: 0,
      id: '',
      info: undefined,
      qList: [], //资质list
      remark: '',
      loading1: false,
      loading2: false,
      // 导入配置
      goodsImportConfig: {
        templateFileName: '交接商品导入模板',//下载模板名字
        haveDataSourceKey:'ErpGoodsCode',
        ErrorShowKey: 'ErrorShow',//异常信息字段名称
        saveKey: 'BatchId',
        saveUrlType: 'GET',//保存接口请求方式
        saveUrl: '/{v}/TeamGoods/GetTempTeamGoodsDetailsList/GetTempTeamGoodsDetailsListAsync',//保存接口
        listQueryKey: 'BatchId',//列表的时候需要用到的字段
        batchRemoveId: '',//批量移除id
        listRemoveType: 'DELETE',//列表删除接口请求方式
        listRemoveUrl: '/{v}/TeamGoods/DeleteSpecifiedTempTeamGoods/DeleteSpecifiedTempTeamGoodsAsync',//列表删除 接口
        listRemoveKey: '',//列表删除 参数 key
        listHttpHead: 'P36010',
        listUrl: '/{v}/TeamGoods/GetTempTeamGoodsPageList/GetTempTeamGoodsPageListAsync',//列表的请求接口
        listUrlType: 'GET',//列表接口请求方式
        queryParamStatusKey: 'Status',//列表查询 异常 正常 key
        noLoadData: true,//是否默认弹出时候不加载数据
        importResKey: 'BatchId',//导出完成后返回的key 列表的时候用到
        dataSourceListKey: 'TeamGoodsList',
        clearUrlType: 'DELETE',
        clearUrl: '/{v}/TeamGoods/DeleteBatchTempTeamGoods/DeleteBatchTempTeamGoodsAsync',
        clearSaveKey: 'BatchId',
      },
      searchImportInput: [
        {
          name: '商品', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'ErpGoodsKey', //搜索key 必填
          placeholder: '名称/编码',
        },
        {
          name: '生产厂家', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'BrandManufacturer', //搜索key 必填
          placeholder: '请输入',
        }
      ],
      goodsImportColumns: [
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '异常原因',
          dataIndex: 'ErrorShow',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 80,
          actionBtns: [{ name: '移除', icon: '' }],
          scopedSlots: { customRender: 'action' },
        },
      ],
      model: {
        DeliverName: '',
        ReceiverName: '',
        SalerTransferRange: '',
        PurchaserTransferRange: '',
      },
      md: 8,
      url: {
        list: '',
        userList: '/{v}/WorkTransfer/GetTransferUserList',
        save: '/{v}/WorkTransfer/Create',
        editSave: '/{v}/WorkTransfer/Update',
      },
      tableData:{
        dataSource: [],
        dataSource1: [],
      },
      selectTableData: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '商品', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '请输入' },
        ],
        title: '新增授权品种',
        name: '授权品种',
        recordKey: 'GoodsSpuId',
        httpHead: 'P36010',
        isInitData: false,
        url: {
          list: '/{v}/WorkTransfer/GetTransferGoodsSpuList',
        },
        columns: [
          {
            title: '商品名称',
            dataIndex: 'ErpGoodsName',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '规格',
            dataIndex: 'Specification',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '单位',
            dataIndex: 'Unit',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '生产厂商',
            dataIndex: 'Manufacturer',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '商品编号',
            dataIndex: 'ErpGoodsCode',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '审核状态',
            dataIndex: 'AuditStatusStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '首营状态',
            dataIndex: 'AuthBusinessTypeStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '是否活动',
            dataIndex: 'IsValid',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'IsActivity' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
      selectTableData1: {
        searchInput: [
          //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
          { name: '客户', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '请输入' },
        ],
        title: '选择转移客户',
        name: '转移客户',
        recordKey: 'CustomerId',
        httpHead: 'P36010',
        isInitData: false,
        url: {
          list: '/{v}/WorkTransfer/GetTransferCustomerList',
        },
        columns: [
          {
            title: '客户',
            dataIndex: 'CustomerName',
            width: 200,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '注册地址',
            dataIndex: 'RegAddress',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '联系电话',
            dataIndex: 'LinkPhone',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '审核状态',
            dataIndex: 'AuditStatusStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '首营状态',
            dataIndex: 'AuthBusinessStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
          {
            title: '是否活动',
            dataIndex: 'IsValid',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'IsActivity' },
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
            scopedSlots: { customRender: 'action' },
          },
        ],
      },
      HttpHead: 'P36010',
      httpHead: 'P36010',
      receiverDis: true,
      deliveryDisaled: true,
      curDeliver: undefined,
      isWorkHandoverEditTableShow:true,
    }
  },
  created() { },
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      if (this.id) {
        this.deliveryDisaled = true
        this.getDetail()
      } else {
        this.deliveryDisaled = false
      }
      this.acType = this.$route.query.acType ? Number(this.$route.query.acType) : 1

      if (this.acType == 1) {
        this.setTitle('新增工作交接单')
      } else {
        this.setTitle('编辑工作交接单')
      }
    }
  },
  computed: {
    isEdit() {
      return this.acType === 2
    },
    isAdd() {
      return this.acType === 1
    },
    rules() {
      return {
        DeliverName: [{ required: true, message: '请选择!' }],
        ReceiverName: [{ required: true, message: '请选择!' }],
        SalerTransferRange: [{ required: true, message: '请选择!' }],
        PurchaserTransferRange: [{ required: true, message: '请选择!' }],
      }
    },
  },
  watch: {
  },
  methods: {
    handleWorkHandoverEditTableShow(){
      // console.log(this.isCaiG(this.model.UserTypes))
      // console.log(this.isShangX(this.model.UserTypes))
      // console.log(this.tableData)
      // console.log(this.$refs.goodsWorkHandoverEditTable)
      if((this.tableData.dataSource || []).length>0 && this.$refs.goodsWorkHandoverEditTable){
        this.$refs.goodsWorkHandoverEditTable.dataSource = this.tableData.dataSource
      }
      if((this.tableData.dataSource1 || []).length>0 && this.$refs.cusWorkHandoverEditTable){
        this.$refs.cusWorkHandoverEditTable.dataSource = this.tableData.dataSource1
      }
    },
    // 交接范围
    // 商销交接范围 为null 采购交接范围不为null ,那么交接范围=采购交接范围
    // 采购交接范围 为null 商销交接范围不为null ,那么交接范围=商销交接范围
    // 采购交接范围不为null，商销交接范围不为null,采购交接和商销交接都为全部交接的情况下，为全部交接，其余都是部分交接
    getWorkTransferState(type){
      let str = ''
      // 是采购人员 不是商销人员
      if(this.isCaiG(type) && !this.isShangX(type)){
        return str = this.model.PurchaserTransferRange == 1 ? '全部交接' : '部分交接'
      }
      // 不是采购人员 是商销人员
      if(!this.isCaiG(type) && this.isShangX(type)){
        return str = this.model.SalerTransferRange == 1 ? '全部交接' : '部分交接'
      }
      // 是采购人员 是商销人员
      if(this.isCaiG(type) && this.isShangX(type)){
        if(this.model.PurchaserTransferRange == 1 && this.model.SalerTransferRange == 1){
          str = '全部交接'
        }else{
          str = '部分交接'
        }
        return str 
      }
    },
    // 是采购人员
    isCaiG(type=[]){
      return type.some(v=>v==1)
    },
    // 是商销人员
    isShangX(type=[]){
      return type.some(v=>v==2)
    },
    // type 1 采购人员 2 商销人员
    delTableData(record, index,type) {
      // console.log('delTableData  ', record, index,type)
      let that = this
      this.$confirm({
        title: '确定移除当前数据吗？',
        content: '',
        onOk() {
          let delIndex = null
          if (type === 1) {
            let dataSource = that.$refs.goodsWorkHandoverEditTable.dataSource
            delIndex = dataSource.findIndex((item) => {
              return item.GoodsSpuId == record.GoodsSpuId
            })
            // console.log('dataSource  ', dataSource)
            // console.log('delIndex  ', delIndex)
            dataSource.splice(delIndex, 1)
            that.tableData.dataSource = dataSource
          } else if (type === 2) {
            let dataSource = that.$refs.cusWorkHandoverEditTable.dataSource
            delIndex = dataSource.findIndex((item) => {
              return item.CustomerId == record.CustomerId
            })
            dataSource.splice(delIndex, 1)
            that.tableData.dataSource1 = dataSource
          }
        },
        onCancel() { },
      })
    },
    clearTableData(key) {
      if(key === '采购'){
        this.$set(this.tableData,'dataSource',[])
      }else{
        this.$set(this.tableData,'dataSource1',[])
      }
      
    },
    
    onTypeChange(e,key) {
      // console.log('onTypeChange  ', e, key)
      this.$nextTick(() => {
        this.handleWorkHandoverEditTableShow()
      })
      // 编辑
      if (this.id) {
        this.receiverDis = true
        return
      }
      
      // if (this.model[key] == 1) {
      //   this.receiverDis = false
      // } else if (this.model[key] == 2) {
      //   this.receiverDis = false
      // }
     
    },
    changeUser(val, name, item) {
      this.model.DeliverId = val
      this.model.DeliverName = name
      if(val && item){
        this.curDeliver = item
        this.curDeliver['Type'] = item.UserTypes
      }
      
      // console.log('交付人curDeliver===  ', this.curDeliver)
      if (this.model.DeliverId) {
        this.$refs.receiverSelect.getData('', () => {
          this.receiverDis = false
        })
      }else{
        // console.log(6666666666)
        this.curDeliver = undefined
        this.receiverDis = true
      }
      this.tableData.dataSource = []
      this.tableData.dataSource1 = []
      this.model.ReceiverId = ''
      this.model.ReceiverName = ''
      this.model.SalerTransferRange = 1
      this.model.PurchaserTransferRange = 1
      this.$forceUpdate()
    },
    changeReceiver(val, name,item) {
      // console.log('接收人 === ',item)
      // this.tableData.dataSource = []
      this.model.ReceiverId = val
      this.model.ReceiverName = name
      this.$forceUpdate()
    },
    getDetail() {
      this.spinning = true
      getAction('/{v}/WorkTransfer/Detail', { id: this.id }, this.HttpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.model = res.Data
              this.curDeliver = {
                DeliverId: this.model.DeliverId,
                Type: this.model.UserTypes,
                // Type: this.model.UserType,
              }
              
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.spinning = false
        })
    },
    
    loadAfterWorkHandoverEdit(dataSource,type){
      if(type === 1){
        this.tableData.dataSource = dataSource
        // console.log('tableData.dataSource',this.tableData.dataSource)
      }
      if(type=== 2){
        this.tableData.dataSource1 = dataSource
        // console.log('tableData.dataSource1',this.tableData.dataSource1)
      }
    },
    onSpinning(spinning){
      // console.log('spinning',spinning)
      this.loading2 = false
      this.loading1 = false
    },
    save(IsDraft, type) {
      if (this.curStep == 1) {
        this.saveData(IsDraft, type)
      } else {
        this.$refs.ruleForm.validate((err, values) => {
          if (err) {
            this.saveData(IsDraft, type)
          }
        })
      }
    },
    saveData(IsDraft, type) {
      if(this.curDeliver && this.isCaiG(this.curDeliver.Type) && this.isShangX(this.curDeliver.Type) && this.model.SalerTransferRange === 3 && this.model.PurchaserTransferRange === 3){
        this.$message.warning('请选择交接范围！只能最多其中一项为暂不交接')
        return false
      }
      // SalerTransferRange  PurchaserTransferRange  1 全部交接 2 部分交接
      if (this.model.SalerTransferRange == 2 || this.model.PurchaserTransferRange == 2) {
        //1采销人员 2商销人员
        if (this.curDeliver && this.isCaiG(this.curDeliver.Type) && this.model.PurchaserTransferRange == 2) {
          if (this.tableData.dataSource.length == 0) {
            this.$message.warning('请选择转移品种！')
            return false
          } else {
            if(!(this.curDeliver && this.isShangX(this.curDeliver.Type))) this.model.Customers = []
            this.model.GoodsSpus = this.tableData.dataSource.map((e) => {
              return {
                GoodsSpuId: e.GoodsSpuId,
                GoodsName: e.ErpGoodsName,
              }
            })
          }
        } 

        if (this.curDeliver && this.isShangX(this.curDeliver.Type) && this.model.SalerTransferRange == 2){
          if (this.tableData.dataSource1.length == 0) {
            this.$message.warning('请选择转移客户！')
            return false
          } else {
            if(!(this.curDeliver && this.isCaiG(this.curDeliver.Type))) this.model.GoodsSpus = []
            this.model.Customers = this.tableData.dataSource1.map((e) => {
              return {
                CustomerId: e.CustomerId,
                CustomerName: e.CustomerName,
              }
            })
          }
        }
      }
      if(this.model.SalerTransferRange == 1 || this.model.SalerTransferRange == 3) this.model.Customers = []
      if(this.model.PurchaserTransferRange == 1 || this.model.PurchaserTransferRange == 3) this.model.GoodsSpus = []
      if(this.model.PurchaserTransferRange == 2 && (this.model.GoodsSpus || []).length === 0){
        this.model.PurchaserTransferRange = 3
      }
      if(this.model.SalerTransferRange == 2 && (this.model.Customers || []).length === 0){
        this.model.SalerTransferRange = 3
      }
      // 非采购 范围选暂不交接
      if(this.curDeliver && !this.isCaiG(this.curDeliver.Type))  this.model.PurchaserTransferRange = null
      // 非商销 范围选暂不交接
      if(this.curDeliver && !this.isShangX(this.curDeliver.Type))  this.model.SalerTransferRange = null
      if (type == 2) {
        this.model.IsDraft = IsDraft
        // if (IsDraft) {
        //   this.loading1 = true
        // } else {
          this.loading2 = true
          this.loading1 = true
        // }
        // console.log('编辑  == ',this.model)
        // return 
        postAction(this.id ? this.url.editSave : this.url.save, this.model, this.HttpHead)
          .then((res) => {
            if (res.IsSuccess) {
              if (!IsDraft) {
                // postAction(
                //   '/v1/ApprovalWorkFlowInstance/CreateApproval',
                //   {
                //     MainBussinessNo: res.Data.WorkTransferId,
                //     BussinessNo: res.Data.WorkTransferAuditId,
                //     Scenes: 24,
                //     OpratorId: Vue.ls.get(USER_ID),
                //   },
                //   'P36005'
                // ).then((res) => {
                //   if (res.IsSuccess) {
                //     this.$message.success('操作成功')
                //     this.backPage()
                //   } else {
                //     this.$message.warning(res.Msg)
                //   }
                // })
                this.backPage()
              } else {
                this.backPage()
                this.$message.success('操作成功')
              }
            } else {
              this.$message.warning(res.Msg)
            }
          })
          .finally(() => {
            this.loading1 = false
            this.loading2 = false
          })
      } else {
        let that = this
        this.loading2 = true
        this.loading1 = true
        let text = ''
        // console.log('initCount   ',this.selectTableData.initCount ,this.tableData.dataSource)
        // console.log('initCount1   ',this.selectTableData1.initCount ,this.tableData.dataSource1)
    
        
        
        if (this.model.PurchaserTransferRange == 2 && this.selectTableData.initCount == this.tableData.dataSource.length && this.model.SalerTransferRange != 2) {
          text = '所选转移品种为交付人所有负责品种，系统将自动改为全部交接，是否继续？'
          this.$confirm({
            title: text,
            content: '',
            onOk() {
              that.curStep = 1
              that.model.PurchaserTransferRange = 1
              that.model.GoodsSpus = []
            },
            onCancel() {
              that.loading1 = false
             },
          })
        }else if (this.model.SalerTransferRange == 2 && this.selectTableData1.initCount == this.tableData.dataSource1.length && this.model.PurchaserTransferRange != 2) {
          text = '所选转移客户为交付人所有负责客户，系统将自动改为全部交接，是否继续？'
          this.$confirm({
            title: text,
            content: '',
            onOk() {
              that.curStep = 1
              that.model.SalerTransferRange = 1
              that.model.Customers = []
            },
            onCancel() { 
              that.loading1 = false
            },
          })
        } else if (this.model.SalerTransferRange == 2 && this.model.PurchaserTransferRange == 2) {
          if (this.selectTableData.initCount == this.tableData.dataSource.length || this.selectTableData1.initCount == this.tableData.dataSource1.length) {
            text = '所选转移品种为交付人所有负责品种/所选转移客户为交付人所有负责客户，系统将自动改为全部交接，是否继续？'
            this.$confirm({
              title: text,
              content: '',
              onOk() {
                that.curStep = 1
                
                if(that.selectTableData.initCount == that.tableData.dataSource.length) {
                  that.model.PurchaserTransferRange = 1
                  that.model.GoodsSpus = []
                }
                if(that.selectTableData1.initCount == that.tableData.dataSource1.length) {
                  that.model.SalerTransferRange = 1
                  that.model.Customers = []
                }
              },
              onCancel() {
                that.loading1 = false
               },
            })
          }else{
            that.curStep = 1
          }
      
        } else {
          that.curStep = 1
        }
      }
    },
    backPage() {
      this.$root.$emit("removeTabsAndBack", this.$route.fullPath, '/pages/teamManagement/WorkHandoverList');
    },
    uploadSup(e) {
      if (e.SupplierQualificationVersion && e.SupplierQualificationVersion.QualificationGroups) {
        this.qList = e.SupplierQualificationVersion.QualificationGroups
      }

      this.remark = e.SupplierQualificationVersion ? e.SupplierQualificationVersion.Remarks || '' : ''
    },
    prevStep() {
      this.curStep = 0
      this.$nextTick(() => {
        this.handleWorkHandoverEditTableShow()
      })
    },
    // 导入保存
    handleBatchImportModalOk(data, type) {
      // console.log(data, type)
      if (data.length > 0) {
        data.map(v=>{
          v.Specification = v.PackingSpecification
          v.Unit = v.PackageUnit
          v.Manufacturer = v.BrandManufacturer
          v.GoodsSpuId = v.GoodsId
        })
        if (type == 'goods') {
          if ((this.tableData.dataSource || []).length) {
            this.tableData.dataSource = this.tableData.dataSource.concat(data)
          } else {
            this.tableData.dataSource = data
          }
          this.$refs.ruleForm.clearValidate()
        }

      }
    },
    // type 1 采购 2 商销
    chooseTableData(type) {
      if(type=='导入'){
        this.$refs.iBatchImportGoodsModal.show(this.tableData.dataSource)
        return
      }
      let dataSource = type === 1 ?( this.tableData.dataSource || [])  : (this.tableData.dataSource1 || [])
      let queryParam = {
        UserId: this.model.DeliverId,
      }
      if(type === 1){
        this.$refs.TableSelectDataModal.show(dataSource, queryParam)
        this.getCount(1,count => {
          this.selectTableData.initCount = count
          // console.log('11111111count  ',this.selectTableData.initCount)
        })

      }else{
        this.$refs.TableSelectDataShangXModal.show(dataSource, queryParam)
        this.getCount(2,count => {
          this.selectTableData1.initCount = count
          // console.log('count1  ',this.selectTableData1.initCount)
        })
      }
    },
    // 选择的数据
    // type 1 采购 2 商销
    chooseData(data,type) {
      if (data && data.length > 0) {
        if(type === 1){
          this.tableData.dataSource = this.tableData.dataSource.concat(data)
        }else{
          this.tableData.dataSource1 = this.tableData.dataSource1.concat(data)
        }
        
      }
      this.$nextTick(() => {
        this.handleWorkHandoverEditTableShow()
      })
    },

    // 查授权品种数量 type 1
    // 查转移客户数量 type 2
    getCount(type,call){
      getAction(type === 1 ?this.selectTableData.url.list : this.selectTableData1.url.list, { UserId: this.model.DeliverId ,PageIndex:1,PageSize:1},this.HttpHead).then((res) => {
          if (res.IsSuccess) {
            call&&call(res.Count)
          } else {
            call&&call(0)
          }
        })
    }
  },
}
</script>

<style lang="less" scoped>

.erp-goods {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
  padding-bottom: 10px;
}
.erp-goods::before {
  display: inline-block;
  margin-right: 4px;
  color: #f5222d;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: '*';
}


</style>
