
<!-- 商销客户审核 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchView ref="SimpleSearchArea" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="httpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="onSetValid"
      @operate="operate"
      @changeTab="changeTab"
      @getListAmount="getListAmount" />

  </a-row>
</template>

<script>
import Vue from 'vue'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import SEnum from '@/components/yyl/table/SearchItemEnum'
import { getAction, putAction, postAction } from '@/api/manage'
import { USER_ID } from '@/store/mutation-types'
export default {
  description: '工作交接审核',
  name: 'WorkHandoverApprovalList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchItems: [
        {
          name: '交接单号',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'TransferNo',
        },
        {
          name: '交付人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'Deliver',
        },
        {
          name: '接收人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'Receiver',
        },
        {
          name: '归属人',
          placeholder: '',
          type: SEnum.INPUT,
          key: 'OwnerByName',
        },
        {
          name: '创建日期',
          type: SEnum.DATE,
          rangeDate: this.rangeDate,
          key: 'CreateTime',
        },
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: 'true',
        statusKey: 'IsNeedAudit', //标签的切换关键字
        statusList: [
          {
            name: '待审核',
            value: 'true',
            count: '',
            key: 'AuditStatus',
          },
          {
            name: '审核记录',
            value: 'false',
            count: '',
            key: 'AuditStatus',
          },
        ],
        operateBtn: [],
      },
      columns: [],
      columnsStart: [
        {
          title: '交接单号',
          dataIndex: 'TransferNo',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '人员类型',
          dataIndex: 'UserTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '交付人',
          dataIndex: 'DeliverName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '接收人',
          dataIndex: 'ReceiverName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '交接范围',
          dataIndex: 'TransferRangeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '交接单状态',
          dataIndex: 'TransferStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
      ],
      columnsEnd: [
        {
          title: '归属人',
          dataIndex: 'OwnerByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          actionBtn: [
            {
              name: '审核',
              icon: '',
              id: '442cfccb-5827-4848-beb2-de2f6c052fe5',
              specialShowFuc: (record) => {
                return this.tab.status == 'true'
              },
            },
            {
              name: '详情',
              icon: '',
              id: 'f0635d8d-80ec-428a-a6a4-f2aa748c0278',
              specialShowFuc: (record) => {
                return this.tab.status == 'false'
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {
        IsNeedAudit: true,
      },
      isTableInitData: false, //是否自动加载
      httpHead: 'P36010',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/WorkTransfer/AuditList',
        listAmount: '/{v}/WorkTransfer/AuditStatusCount',
      },
      approval: undefined,
    }
  },
  created() { },
  // mounted() {
  //   this.columns = [].concat(this.columnsStart).concat(this.columnsEnd)
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  activated() {
    this.columns = [].concat(this.columnsStart).concat(this.columnsEnd)
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    changeTab(status, statusKey) {
      let columns = this.columnsStart.concat(this.columnsEnd)
      let columns1 = this.columnsStart
        .concat([
          {
            title: '审核状态',
            dataIndex: 'AuditStatusStr',
            width: 150,
            ellipsis: true,
            scopedSlots: { customRender: 'component' },
          },
        ])
        .concat(this.columnsEnd)
      this.$set(this, 'columns', String(status) == 'true' ? columns : columns1)
      if (String(status) == 'true') {
        this.queryParam.IsNeedAudit = true
      } else {
        this.queryParam.IsNeedAudit = false
      }
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '审核') {
        this.onDetailClick('/pages/teamManagement/WorkHandoverDetail', { id: record.Id, acType: 2 })
      } else if (type == '详情') {
        this.onDetailClick('/pages/teamManagement/WorkHandoverDetail', { id: record.Id, acType: 1 })
      }
    },
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.$refs.table.tab.status = 'true'
      }
      queryParam['IsNeedAudit'] = this.$refs.table.tab.status
      this.$refs.table.loadDatas(null, queryParam)
    },
    getListAmount(data) {
      if ((data || []).length) {
        this.tab.statusList = []
        data.map((v) => {
          this.tab.statusList.push({
            name: v['AuditStatusStr'],
            value: v['AuditStatus'] == 1 ? 'true' : 'false',
            count: v['Count'],
          })
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .ant-table-tbody tr {
  height: 46px;
}
</style>