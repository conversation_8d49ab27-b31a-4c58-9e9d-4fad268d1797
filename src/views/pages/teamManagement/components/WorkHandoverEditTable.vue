<!-- 工作交接单table -->
<template>
  <div>
    <a-table :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : null)" :bordered="false" ref="table" rowKey="type===1?GoodsSpuId : CustomerId" size="small" :columns="type===1?columns : columns1" :dataSource="dataSource" :pagination="ipagination" @change="handleTableChange" :scroll="{ y: '400px', x: '1000' }">
      <span slot="component" slot-scope="text">
        <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
        <j-ellipsis :value="text" v-else />
      </span>
      <!-- 是否活动 -->
      <template slot="IsActivity" slot-scope="text">
        <span>{{ text ? '是' : '否' }}</span>
      </template>
      <span slot="action" slot-scope="text, record, index">
        <a @click="delTableData(record, index)">
          <a-icon type="del" />
          移除
        </a>
      </span>
    </a-table>
  </div>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  description: '工作交接单编辑',
  name: 'WorkHandoverEditTable',
  props:{
    WorkTransferId:{
      type: String,
      default: null
    },
    type:{
      type: Number,
      default: 1
    },
    dataList:{
      type: Array,
      default: () => []
    }
  },
  mixins: [ ListMixin],
  components: { JEllipsis },
  data() {
    return {
      url: {
        list: this.dataList.length>0 || !this.WorkTransferId ? '' : (this.type===1?'/{v}/WorkTransfer/GetWorkTransferGoodsSpuList' : '/{v}/WorkTransfer/GetWorkTransferCustomerList'),
      },
      pageSizeNum:999999,
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'Specification',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'Unit',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'Manufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '首营状态',
          dataIndex: 'AuthBusinessTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否活动',
          dataIndex: 'IsValid',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'IsActivity' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
        },
      ],
      columns1: [
        {
          title: '客户',
          dataIndex: 'CustomerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '注册地址',
          dataIndex: 'RegAddress',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '联系电话',
          dataIndex: 'LinkPhone',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '首营状态',
          dataIndex: 'AuthBusinessStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否活动',
          dataIndex: 'IsValid',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'IsActivity' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
        },
      ],
      dataSource: [],
      httpHead: 'P36010',

    }
  },
  created() { },
  mounted() {
  },
  computed: {
  },
  watch: {
    dataList(to) {
      // console.log('dataList   ',to)
      this.dataSource = to
    },
    dataSource(to) {
      // console.log('dataSource   ',to)
      this.ipagination.total = to.length || 0
    },
    
  },
  methods: {
    loadBefore(){
      this.queryParam.WorkTransferId = this.WorkTransferId
    },
    delTableData(record, index){
      this.$emit('delTableData',record,index,this.type)
    },
    loadAfter(){
      this.$emit('loadAfter',this.dataSource,this.type)
    },
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
        this.isorter.SortFieldName = sorter.order ? sorter['columnKey'] : undefined
        if (this.sort) {
          // 订单列表 排序方式
          this.queryParam[this.sort] = 'ascend' == sorter.order ? '1' : '2'
        }
      }
      this.ipagination = pagination
    },
    
  


  },
}
</script>

<style lang="less" scoped>

 /* /deep/.ant-table-tbody tr.table-striped {
   color:#f81d22;
 } */
</style>
