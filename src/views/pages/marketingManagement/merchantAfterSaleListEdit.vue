
<!-- 商销售后列表 编辑售后单 -->
<template>
  <div>
    <a-alert type="error" :message="`驳回原因：${model.AuditOpinion || ' -- '}`" banner :showIcon="false" v-if="model.AuditOpinion && model.Id" />
    <a-card style="margin:15px 0">
      <a-spin :spinning="spinning">
        <div :class="'form-model-style'">
          <a-spin :spinning="loading">
            <a-descriptions title="客户信息">
              <a-descriptions-item label="客户名称">{{ model.CustomerName || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="联系人">{{ model.LinkName || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="联系电话">{{ model.LinkPhone || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单编号">{{ model.OrderCode || ' -- ' }}</a-descriptions-item>
            </a-descriptions>
            <!-- 售后信息 -->
            <a-row :gutter="20" style="margin-bottom:24px">
              <a-col :span="24">
                <div style="font-size: 16px; font-weight: 600;margin:0 0 15px 0">
                  售后信息
                </div>
              </a-col>
              <a-col :span="24">
                <ApplyAfterSalesServiceCom ref="iApplyAfterSalesServiceCom" :OrderId="model.OrderId" :TotalAmount="model.RefundAmount" :Info="model" v-if="model&&model.OrderId" />
              </a-col>
            </a-row>
          </a-spin>

        </div>

        <a-affix :offset-bottom="0" style="float: right;width: 100%;text-align: right;">
          <a-card :bordered="false" style="border-top:1px solid #e8e8e8">
            <YYLButton menuId="2dd58ed4-da3b-4fbb-a4ad-84b2c557032c" text="保存" type="primary" @click="handleSubmit(false)" />
            <YYLButton menuId="2dd58ed4-da3b-4fbb-a4ad-84b2c557032c" text="提交审核" type="primary" @click="handleSubmit(true)" />
            <a-button @click="goBack(true,true)">取消</a-button>
          </a-card>

        </a-affix>
      </a-spin>
    </a-card>
  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '编辑售后单',
  name: 'merchantAfterSaleListEdit',
  mixins: [EditMixin],
  components: { JEllipsis },
  data() {
    return {
      spinning: false,
      loading: false,
      md: 8,
      model: {},
      id: '',
      httpHead: 'P36008',
      url: {
        editSaleAfterOrder: '/{v}/SaleAfterOrder/EditSaleAfterOrderV15',
        detail: '/{v}/SaleAfterOrder/GetEditSaleAfterOrder', //'/{v}/Audit/GetSaleAfterOrder',
        createApproval: '/{v}/ApprovalWorkFlowInstance/CreateApproval', // 创建审核实例
      }
    }
  },
  created() { },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.loadDetailInfo()
    }
  },
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            // 列合并处理数据
            // let data = res.Data ? res.Data.SaleAfterOrderGoodses : []
            // let arr = this.getListRowSpanData(data || [], '', 'ErpGoodsCode', 'rowSpan') || []
            // let arr1 = this.getListRowSpanData(arr, 'ErpGoodsCode', 'GoodsBatchNumber', 'rowSpanGoodsBatchNumber','GoodsBatchNumber') || []
            // let arr2 = this.getListRowSpanData(arr1, 'GoodsBatchNumber', 'BatchNo', 'rowSpanBatchNo','BatchNo') || []
            // res.Data.SaleAfterOrderGoodses = arr2
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // 保存并审核
    handleSubmit(IsSubmit) {
      let formData = this.$refs.iApplyAfterSalesServiceCom.onSubmit()
      if (!formData) return
      formData.Id = this.model['Id']
      formData.IsSubmit = IsSubmit
      // 过滤为空值
      formData.SaleAfterOrderGoodses = (formData.SaleAfterOrderGoodses || []).filter((item) => {
        return item.SaleAfterCount > 0
      })
      this.spinning = true
      postAction(this.url.editSaleAfterOrder, formData, this.httpHead).then(res => {
        if (!res.IsSuccess) {
          this.spinning = false
          this.$message.error(res.Msg)
          return
        }
        // 草稿
        if (!IsSubmit) {
          this.spinning = false
          this.$message.success('操作成功！')
          setTimeout(() => {
            this.goBack(true, true)
          }, 500)
          return
        }
        this.createApproval(
          {
            // Remark: data.remark,
            BussinessNo: res.Data
          },
          bool => {
            this.spinning = false
            if (bool) {
              this.$message.success('操作成功！')
              setTimeout(() => {
                this.goBack(true, true)
              }, 500)
            }
          }
        )
        // 下一步
        call && call()
      })
    },
    // 创建审核实例
    createApproval(data, callback) {
      if (!data['BussinessNo']) {
        this.$message.error('BussinessNo未获取到值')
        return
      }
      postAction(
        this.url.createApproval,
        {
          BussinessNo: data['BussinessNo'],
          Scenes: 19,
          OpratorId: Vue.ls.get(USER_ID)
          // Remark: data['Remark']
        },
        'P36005'
      ).then(res => {
        if (!res.IsSuccess) {
          this.$message.error(res.Msg)
          callback && callback(false)
          return
        }
        // this.$message.success('操作成功！')
        callback && callback(true)
      })
    }
  }
}
</script>


