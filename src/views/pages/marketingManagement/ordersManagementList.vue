<!-- 商销订单列表 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="(item, chceked) => onSetValid(item, chceked, 'POST', 'GoodsSpuId')"
      isHandleTableChange
      @handleTableChange="handleTableChange"
      @operate="operate"
      @changeTab="changeTab"
    >
      <div style="margin-top: 5px" slot="bottomBtn">
        <span style="margin-right: 15px">合计订单金额：¥{{ countObj.AllTotalAmount || 0 }}</span>
        <span style="margin-right: 15px">合计出库金额：¥{{ countObj.AllOutboundAmount || 0 }}</span>
        <span style="margin-right: 15px">本页订单金额：¥{{ countObj.PageTotalAmount || 0 }}</span>
        <span style="margin-right: 15px">本页出库金额：¥{{ countObj.PageOutboundAmount || 0 }}</span>
      </div>
    </SimpleTable>
    <!-- 申请售后 -->
    <ApplyAfterSalesServiceModal ref="iApplyAfterSalesServiceModal" @ok="onAfterSalesServiceOk" />
    <!-- 商销调价 -->
    <PricesAreAdjustedModal ref="iPricesAreAdjustedModal" @ok="searchQuery" />
    <!-- 上传附件弹窗 -->
    <UploadAttachmentsModal ref="UploadAttachmentsModal" @ok="modalOk" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销订单列表',
  name: 'ordersManagementList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '下单时间',
          type: 'timeInput',
          value: '',
          key: ['PlaceOrderTimeBegin', 'PlaceOrderTimeEnd'],
          rangeDate: [],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '客户',
          type: 'selectLink',
          vModel: 'CustomerId',
          dataKey: { name: 'Name', value: 'Id' },
          httpParams: { PageIndex: 1, PageSize: 100 },
          keyWord: 'KeyWord',
          defaultVal: '',
          placeholder: '客户名称/编号/拼音码',
          httpType: 'GET',
          url: '/{v}/Customer/ListForOrder',
          httpHead: 'P36002',
          invalidChecked: true,
        },
        {
          name: '客户类别',
          type: 'selectLink',
          vModel: 'CustomerType',
          dataKey: { name: 'ItemValue', value: 'Id' },
          httpParams: { groupPY: 'kehulb' },
          keyWord: 'Name',
          defaultVal: '',
          placeholder: '请选择',
          httpType: 'GET',
          url: '/{v}/Global/GetListDictItemForCustomer',
          httpHead: 'P36001',
        },

        { name: '订单编号', type: 'input', value: '', key: 'Code', defaultVal: '', placeholder: '请输入' },
        {
          name: '联系人',
          type: 'input',
          value: '',
          key: 'CustomerLink',
          defaultVal: '',
          placeholder: '姓名/电话',
        },
        {
          name: '快递公司',
          type: 'selectBaseLink',
          httpHead: 'P36008',
          httpType: 'GET',
          url: '/{v}/LogisticsOrder/GetExpressCompanies',
          vModel: 'ExpressCompanyCode',
          placeholder: '请选择',
          httpParams: {},
          dataKey: { name: 'ItemName', value: 'ItemValue' },
        },
        {
          name: '付款方式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PaymentMode',
          dictCode: 'EnumPaymentMode',
          notOption: [0, 1, 4, 5, 6],
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '订单状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'OrderStatus',
          dictCode: 'EnumSalesOrderStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '认款状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'OrderAcceptanceStatus',
          dictCode: 'EnumOrderAcceptanceStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '发票状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'InvoiceStatus',
          dictCode: 'EnumSaleOrderInvoiceStatus',
          defaultVal: '',
          placeholder: '请选择',
        },

        { name: '发票号', type: 'input', value: '', key: 'InvoiceNo', defaultVal: '', placeholder: '请输入' },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        { name: '责任人', type: 'input', value: '', key: 'OwnerByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '订单类型',
          type: 'selectBasicLink',
          value: '',
          vModel: 'Type',
          dictCode: 'EnumSalesOrderType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '同步状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '出库时间',
          type: 'timeInput',
          value: '',
          key: ['OutboundTimeBegin', 'OutboundTimeEnd'],
          rangeDate: [],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '商品',
          type: 'input',
          value: '',
          key: 'GoodsNameKey',
          defaultVal: '',
          placeholder: '请输入商品名称/编号',
        },
        { name: '出库单号', type: 'input', value: '', key: 'OutboundNo', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          {
            name: '新增手工订单',
            type: 'primary',
            icon: 'plus',
            key: '新增手工订单',
            id: '81ce8c57-b015-4ab1-8343-5f35de472309',
          },
          {
            name: '新增含特订单',
            type: 'primary',
            icon: 'plus',
            key: '新增含特订单',
            id: '98e01756-3637-41c2-88e4-4750b07b267c',
          },
          {
            name: '新增普通订单',
            type: 'primary',
            icon: 'plus',
            key: '新增普通订单',
            id: '813e4d7e-d9ef-43ab-bbb6-b485d981fddc',
          },
        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户类别',
          dataIndex: 'CustomerTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单编号',
          dataIndex: 'Code',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单类型',
          dataIndex: 'TypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '快递公司',
          dataIndex: 'ExpressCompanyStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单金额(元)',
          dataIndex: 'TotalAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '出库金额',
          dataIndex: 'OutboundAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '已认款金额',
          dataIndex: 'RecognizedAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单状态',
          dataIndex: 'OrderStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '认款状态',
          dataIndex: 'OrderAcceptanceStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票状态',
          dataIndex: 'InvoiceStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '下单时间',
          dataIndex: 'PlaceOrderTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '出库时间',
          dataIndex: 'OutboundTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 280,
          fixed: 'right',
          actionBtn: [
            {
              name: '编辑',
              icon: '',
              id: '73f974c6-ac75-4c2b-9aeb-ccd3ba91c108',
              specialShowFuc: (e) => {
                let record = e || {}
                return [0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '删除',
              icon: '',
              id: 'd3fd600d-7588-44dd-a349-0bbb956e90da',
              specialShowFuc: (e) => {
                let record = e || {}
                return [0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '详情',
              icon: '',
              id: 'd1471f57-5417-452b-a2af-71ec9c7782d6',
              specialShowFuc: (e) => {
                let record = e || {}
                return ![0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '下载检验报告',
              icon: '',
              id: '016997b4-b50b-4391-9a1f-7446749420a1',
              specialShowFuc: (e) => {
                let record = e || {}
                return [2].includes(record.AuditStatus) && [2, 3].includes(record.OrderStatus)
              },
            },
            {
              name: '查看物流',
              icon: '',
              id: '682284fb-7476-45da-9956-8e46e426a575',
              specialShowFuc: (e) => {
                let record = e || {}
                return [2].includes(record.AuditStatus) && [4, 5].includes(record.OrderStatus)
              },
            },
            {
              name: '售后',
              icon: '',
              id: '0f471b0e-0bef-4370-b845-09f002eb0c29',
              specialShowFuc: (e) => {
                let record = e || {}
                return [2].includes(record.AuditStatus) && [3, 4, 5].includes(record.OrderStatus)
              },
            },
            {
              name: '调价',
              icon: '',
              id: 'b98c6c81-6a67-4dc6-82a8-7ddbd6a733df',
              specialShowFuc: (e) => {
                let record = e || {}
                return [2].includes(record.AuditStatus) && ![1, 2, 99, 200].includes(record.OrderStatus)
              },
            },
            {
              name: '查看发票',
              icon: '',
              id: '1ffb6d5b-43c3-43f5-974a-6286e4d1289f',
              specialShowFuc: (e) => {
                let record = e || {}
                return (
                  [2].includes(record.AuditStatus) &&
                  [5].includes(record.OrderStatus) &&
                  [3].includes(record.OrderAcceptanceStatus) &&
                  [3].includes(record.InvoiceStatus)
                )
              },
            },
            {
              name: '撤回',
              icon: '',
              id: '4e5128f4-abc0-4d90-83c4-6406c52bb39c',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.AuditStatus === 1
              },
            },
            {
              name: '作废',
              icon: '',
              id: '82bf3c4f-80b8-4cdd-84d7-fa644eaf9586',
              specialShowFuc: (e) => {
                let record = e || {}
                // AuditStatus == 2  && //等于审核通过
                // OrderStatus != 200 //不等于已作废
                // 1、审核状态必须为审核通过的
                // 2、同步状态不能是同步中的
                // 3、订单状态必须为待下发、待出库
                return record.AuditStatus === 2 && record.OrderStatus <= 2 && record.PushERPStatus != 3
              },
            },
            {
              name: '上传附件',
              icon: '',
              id: '37343b06-3454-4b94-9b90-e615192dce71',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.AuditStatus === 2
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      countObj: {},
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/Order/OrderList',
        orderStatistics: '/{v}/Order/OrderStatistics',
        deleteOrder: '/{v}/Order/Delete',
        cancelOrder: '/{v}/Order/RevokeOrderAudit',
        voidOrder: '/{v}/Order/VoidOrder',
      },
    }
  },
  mounted() {
    this.setThisMonthTime()
  },
  activated() {
    this.queryParam = this.$refs.SimpleSearchArea.queryParam
    this.initData()
  },
  methods: {
    initData() {
      // this.setThisMonthTime()
      this.$refs.table.loadDatas(null, this.queryParam)
      this.getOrderStatistics()
    },
    // 设置下单时间默认值
    setThisMonthTime() {
      // 月初
      var fristDayDate = new Date()
      fristDayDate.setDate(1)
      var year = fristDayDate.getFullYear()
      var month = fristDayDate.getMonth() + 1 // 月份从0开始，所以需要加1
      var day = fristDayDate.getDate()
      var fristDay = year + '-' + month.toString().padStart(2, '0') + '-' + day.toString().padStart(2, '0')
      // 今天
      var todayDate = new Date()
      var year = todayDate.getFullYear()
      var month = todayDate.getMonth() + 1 // 月份从0开始，所以需要加1
      var day = todayDate.getDate()
      var today = year + '-' + month.toString().padStart(2, '0') + '-' + day.toString().padStart(2, '0')
      // 设置下单时间
      let timeData = this.searchInput.find((item) => {
        return item.name == '下单时间'
      })
      if (timeData) {
        timeData.rangeDate = [fristDay, today]
      }
      this.$refs.SimpleSearchArea.queryParam.PlaceOrderTimeBegin = fristDay
      this.$refs.SimpleSearchArea.queryParam.PlaceOrderTimeEnd = today
      this.queryParam.PlaceOrderTimeBegin = fristDay
      this.queryParam.PlaceOrderTimeEnd = today
    },
    getOrderStatistics() {
      let params = this.$refs.table.getQueryParams()
      getAction(this.linkUrl.orderStatistics, params, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.countObj = res.Data || {}
      })
    },
    handleTableChange() {
      let params = this.$refs.table.getQueryParams()
      this.$refs.table.loadDatas(null, params)
      this.getOrderStatistics()
    },
    searchQuery(queryParam, type) {
      if (type == 'searchReset') {
        this.setThisMonthTime()
      }
      let params = queryParam ? JSON.parse(JSON.stringify(queryParam)) : this.$refs.SimpleSearchArea.queryParam
      this.$refs.table.loadDatas(1, params)
      this.getOrderStatistics()
    },
    // 列表操作
    operate(record, type) {
      const that = this
      switch (type) {
        case '新增手工订单':
          this.onDetailClick('ordersManagementListAdd', { id: record.Id, opType: 1, orderType: 3 })
          break
        case '新增普通订单':
          this.onDetailClick('ordersManagementListAdd', { id: record.Id, opType: 1, orderType: 1 })
          break
        case '新增含特订单':
          this.onDetailClick('ordersManagementListAdd', { id: record.Id, opType: 1, orderType: 2 })
          break
        case '编辑':
          this.onDetailClick('ordersManagementListAdd', { id: record.Id, opType: 2, orderType: record.Type })
          break
        case '删除':
          this.$confirm({
            title: '删除订单',
            content: '您确定要删除该笔订单吗？',
            onOk() {
              that.deleteOrder(record.Id)
            },
            onCancel() {},
          })
          break
        case '详情':
          this.onDetailClick('ordersManagementListDetail', { id: record.Id })
          break

        case '下载检验报告':
          this.onDetailClick('ordersManagementListDetail', {
            id: record.Id,
            key: 8,
            tabId: '016997b4-b50b-4391-9a1f-7446749420a1',
          })
          break
        case '查看物流':
          this.onDetailClick('ordersManagementListDetail', {
            id: record.Id,
            key: 9,
            tabId: '682284fb-7476-45da-9956-8e46e426a575',
          })
          break
        case '查看发票':
          this.onDetailClick('ordersManagementListDetail', {
            id: record.Id,
            key: 4,
            tabId: '1ffb6d5b-43c3-43f5-974a-6286e4d1289f',
          })
          break
        case '调价':
          this.$refs.iPricesAreAdjustedModal.show(record)
          break
        case '售后':
          this.$refs.iApplyAfterSalesServiceModal.show(record)
          break
        case '撤回':
          this.$confirm({
            title: '撤回订单',
            content: '您确定要撤回该笔订单吗？',
            onOk() {
              that.cancelOrder(record.Id)
            },
            onCancel() {},
          })
          break
        case '作废':
          this.$confirm({
            title: '作废订单',
            content: '您确定要作废该笔订单吗？',
            onOk() {
              that.voidOrder(record.Id)
            },
            onCancel() {},
          })
          break
        case '上传附件':
          this.$refs.UploadAttachmentsModal.show(record)
          break
      }
    },
    // 撤回订单
    cancelOrder(Id) {
      if (!Id) return
      putAction(this.linkUrl.cancelOrder, { OrderId: Id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          setTimeout(() => {
            this.$refs.table.loadDatas(1, this.queryParam)
          }, 1000)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    // 作废订单
    voidOrder(Id) {
      if (!Id) return
      putAction(this.linkUrl.voidOrder, { OrderId: Id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    // 删除订单
    deleteOrder(Id) {
      if (!Id) return
      getAction(this.linkUrl.deleteOrder, { orderId: Id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    onAfterSalesServiceOk() {
      this.$refs.table.loadDatas(1, this.queryParam)
    },
    modalOk() {}
  },
}
</script>

<style></style>
