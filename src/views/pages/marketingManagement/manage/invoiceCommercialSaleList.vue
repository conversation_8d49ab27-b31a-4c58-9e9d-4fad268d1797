<!-- 商销开票列表 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate" @getListAmount="getListAmount" @changeTab="changeTab" />
    <!-- 申请售后 -->
    <ApplyAfterSalesServiceModal ref="iApplyAfterSalesServiceModal" @ok="$refs.table.loadDatas(null, this.queryParam)" />
    <!-- 商销调价 -->
    <PricesAreAdjustedModal ref="iPricesAreAdjustedModal" @ok="$refs.table.loadDatas(null, this.queryParam)" />
  </a-row>
</template>

<script>
import moment from 'moment'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销开票列表',
  name: 'invoiceCommercialSaleList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择

        { name: '开票编号', type: 'input', value: '', key: 'Code', defaultVal: '', placeholder: '请输入' },
        {
          name: '对应订单编号',
          type: 'input',
          value: '',
          key: 'SalesOrderCode',
          defaultVal: '',
          placeholder: '请输入',
        },
        { name: '客户信息', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '请输入' },
        // {
        //   name: '申请类型',
        //   type: 'selectBasicLink',
        //   value: '',
        //   vModel: 'InvoiceApplyType',
        //   dictCode: 'EnumSalesOrderInvoiceApplyType',
        //   defaultVal: '',
        //   placeholder: '请选择'
        // },
        {
          name: '发票类型',
          type: 'selectBasicLink',
          value: '',
          vModel: 'InvoiceType',
          dictCode: 'EnumSaleInvoiceType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '开票状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'InvoiceStatus',
          dictCode: 'EnumInvoiceStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '申请时间',
          type: 'timeInput',
          value: '',
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
          rangeDate: [],
        },
        {
          name: '开票时间',
          type: 'timeInput',
          value: '',
          key: ['InvoiceTimeBegin', 'InvoiceTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
          rangeDate: [],
        },
        { name: '责任人', type: 'input', value: '', key: 'OwnerByName', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '导出', type: '', icon: 'download', key: '导出', id: '91bc871e-df01-4098-aa9b-2188c41cd6c0' },
          {
            name: '申请开票',
            type: 'primary',
            icon: 'plus',
            key: '申请开票',
            id: 'f9e23cfa-b3fc-4098-9841-e046fe054f21',
          },
        ],
        hintArray: [],
        status: 1,
        statusKey: '蓝票申请', //标签的切换关键字
        statusList: [
          {
            name: '蓝票申请',
            value: 1,
            count: '',
          },
          {
            name: '冲红记录',
            value: 2,
            count: '',
          },
          {
            name: '作废记录',
            value: 3,
            count: '',
          },
        ],
      },
      columns: [],
      columnsLP: [
        {
          title: '开票编号',
          dataIndex: 'Code',
          width: 250,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'SalesOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 220,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '申请类型',
        //   dataIndex: 'InvoiceApplyTypeStr',
        //   width: 150,
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'component' }
        // },
        {
          title: '发票类型',
          dataIndex: 'InvoiceTypeV2Str',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票金额（元）',
          dataIndex: 'InvoiceAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '申请人',
          dataIndex: 'ApplyByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票状态',
          dataIndex: 'InvoiceStatusStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '申请时间',
          dataIndex: 'ApplyTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票时间',
          dataIndex: 'InvoiceTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          actionBtn: [
            {
              name: '编辑',
              icon: '',
              id: 'cc5953e2-ab4c-4d43-9be6-f4669713d03c',
              specialShowFuc: (e) => {
                let record = e || {}
                return [0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '删除',
              icon: '',
              id: '85a40636-5992-4863-a9ba-bfe3a5a257e5',
              specialShowFuc: (e) => {
                let record = e || {}
                return [0, 3].includes(record.AuditStatus)
              },
            },
            // 未开票 = 1,
            // 开票中 = 2,
            // 已开票 = 3,
            // 已冲红 = 4,
            // 已作废 = 5,
            // 部分冲红 = 6,
            // 开票失败 = 7,
            // 部分作废 = 8,
            // 推送发票中心失败 = 9,
            {
              name: '重推',
              icon: '',
              id: 'b3d2deae-9f28-456d-8be7-b8615fc0507f',
              specialShowFuc: (e) => {
                let record = e || {}
                return [1, 9].includes(record.InvoiceStatus) && record.AuditStatus === 2
              },
            },
            {
              name: '详情',
              icon: '',
              id: 'e8bb3f63-89ff-4dfa-bdbf-6d8803f6b073',
              specialShowFuc: (e) => {
                let record = e || {}
                return ![0, 3].includes(record.AuditStatus)
              },
            },
            // {
            //   name: '查看发票',
            //   icon: '',
            //   id: '5eb8f568-188d-4e8e-847b-f1997851b5da',
            //   specialShowFuc: e => {
            //     let record = e || {}
            //     return [2].includes(record.AuditStatus) && [3].includes(record.InvoiceStatus)
            //   }
            // },
            // {
            //   name: '冲红',
            //   icon: '',
            //   id: 'accb855e-a0a5-497c-9784-3748057fa430',
            //   specialShowFuc: (e) => {
            //     let record = e || {}
            //     //普票
            //     if (record.InvoiceType == 2) {
            //       return (
            //         this.tab.status == 1 &&
            //         [2].includes(record.AuditStatus) &&
            //         [3, 6].includes(record.InvoiceStatus) &&
            //         !record.IsCancel &&
            //         record.RedOrCancelInvoiceCount <= 0
            //       )
            //     } else if (record.InvoiceType == 1) {
            //       //专票
            //       return (
            //         this.tab.status == 1 &&
            //         [2].includes(record.AuditStatus) &&
            //         [3, 6].includes(record.InvoiceStatus) &&
            //         !record.IsCancel &&
            //         this.retrunTime(record.ApplyTime) &&
            //         record.RedOrCancelInvoiceCount <= 0
            //       )
            //     }
            //   },
            // },
            // {
            //   name: '作废',
            //   icon: '',
            //   id: '0cd64dd5-d479-4b36-86bf-13d329650288',
            //   specialShowFuc: (e) => {
            //     let record = e || {}
            //     return (
            //       this.tab.status == 1 &&
            //       record.InvoiceType == 1 &&
            //       [2].includes(record.AuditStatus) &&
            //       [3].includes(record.InvoiceStatus) &&
            //       !record.IsCancel &&
            //       !this.retrunTime(record.ApplyTime) &&
            //       record.RedOrCancelInvoiceCount <= 0
            //     )
            //   },
            // },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      columnsCH: [
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单编号',
          dataIndex: 'SalesOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票类型',
          dataIndex: 'InvoiceTypeV2Str',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票金额',
          dataIndex: 'OriginalInvoiceAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '冲红金额',
          dataIndex: 'InvoiceAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作时间',
          dataIndex: 'ApplyTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtn: [
            {
              name: '详情',
              icon: '',
              id: 'e8bb3f63-89ff-4dfa-bdbf-6d8803f6b073',
              specialShowFuc: (e) => {
                return true
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      columnsZF: [
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单编号',
          dataIndex: 'SalesOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票类型',
          dataIndex: 'InvoiceTypeV2Str',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票金额',
          dataIndex: 'OriginalInvoiceAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '作废金额',
          dataIndex: 'InvoiceAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作时间',
          dataIndex: 'ApplyTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtn: [
            {
              name: '详情',
              icon: '',
              id: 'e8bb3f63-89ff-4dfa-bdbf-6d8803f6b073',
              specialShowFuc: (e) => {
                return true
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],

      queryParam: { InvoiceApplyType: 1 },
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/OrderInvoice/ListV2',
        listAmount: '/{v}/OrderInvoice/CountV2',
        deleteInvoice: '/{v}/OrderInvoice/InvoiceApplyDel',
        exportUrl: '/{v}/OrderInvoice/Export',
        rethrust: '/{v}/OrderInvoice/RedoPushApplyInvoice',//重推
      },
    }
  },
  created() { },
  mounted() {
    this.queryParam['InvoiceApplyType'] = 1
  },
  activated() {
    this.setTableColums(this.tab.status || 1)
    this.queryParam = this.$refs.SimpleSearchArea.queryParam || {}
    this.queryParam['InvoiceApplyType'] = this.$refs.table.tab.status
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    moment,
    getListAmount(data) {
      this.tab.statusList[0].count = (data && data.Count1) || 0
      this.tab.statusList[1].count = (data && data.Count2) || 0
      this.tab.statusList[2].count = (data && data.Count3) || 0
    },
    changeTab(value) {
      this.setTableColums(value)
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      this.queryParam.InvoiceApplyType = value
      this.$nextTick(() => {
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    setTableColums(value) {
      let auditIndex = this.searchInput.findIndex(item => {
        return item.name === '审核状态'
      })
      switch (value) {
        case 1:
          if (auditIndex < 0) {
            let obj = {
              name: '审核状态',
              type: 'selectBasicLink',
              value: '',
              vModel: 'AuditStatus',
              dictCode: 'EnumAuditStatus',
              defaultVal: '',
              placeholder: '请选择',
            }
            this.searchInput.splice(5, 0, obj)
          }
          this.columns = this.columnsLP
          break;
        case 2:
          if (auditIndex >= 0) {
            this.searchInput.splice(auditIndex, 1)
          }
          this.columns = this.columnsCH
          break;
        case 3:
          if (auditIndex >= 0) {
            this.searchInput.splice(auditIndex, 1)
          }
          this.columns = this.columnsZF
          break;
        default: this.columns = this.columnsLP
      }
      // 重新设置搜索框位置
      this.$refs.SimpleSearchArea.setInputRow()
    },

    searchQuery(queryParam, type) {
      if (type == 'searchReset') {
        this.$refs.table.tab.status = 1
        this.setTableColums(this.$refs.table.tab.status)
      }
      queryParam['InvoiceApplyType'] = this.$refs.table.tab.status
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 删除开票
    deleteInvoice(Id) {
      if (!Id) return
      postAction(this.linkUrl.deleteInvoice, { Id: Id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
      })
    },
    // 列表操作 invoiceApplyType 1蓝票 2红票 3作废 opType 1申请  2编辑
    operate(record, type) {
      let that = this
      switch (type) {
        case '申请开票':
          this.onDetailClick('invoiceCommercialSaleEdit', { id: record.Id, opType: 1, invoiceApplyType: 1 })
          break
        case '编辑':
          this.onDetailClick('invoiceCommercialSaleEdit', {
            id: record.Id,
            opType: 2,
            invoiceApplyType: this.$refs.table.tab.status,
          })
          break
        case '详情':
          this.onDetailClick('invoiceCommercialSaleDetail', { id: record.Id })
          break
        case '查看发票':
          this.onDetailClick('invoiceCommercialSaleDetail', {
            id: record.Id,
            key: 1,
            tabId: '5eb8f568-188d-4e8e-847b-f1997851b5da',
          })
          break
        case '冲红':
          this.$confirm({
            title: '提示',
            content: '冲红当前发票，需要继续吗？',
            okText: '继续',
            onOk() {
              that.onDetailClick('invoiceCommercialSaleEdit', { id: record.Id, opType: 1, invoiceApplyType: 2, red: 1 })
            },
            onCancel() { },
          })
          break
        case '作废':
          this.onDetailClick('invoiceCommercialSaleEdit', {
            id: record.Id,
            opType: 2,
            invoiceApplyType: 3,
            isCancel: 1,
          })
          break
        case '删除':
          this.$confirm({
            title: '删除',
            content: '您确定要删除该开票单吗？',
            onOk() {
              that.deleteInvoice(record.Id)
            },
            onCancel() { },
          })
          break
        case '导出':
          let currentDate = new Date();
          
          // 获取当前的年、月、日和时间
          let year = currentDate.getFullYear();
          let month = currentDate.getMonth() + 1; // 月份从0开始，需要加1
          let day = currentDate.getDate();
          let hours = currentDate.getHours();
          let minutes = currentDate.getMinutes();
          let seconds = currentDate.getSeconds();

          // 将日期和时间格式化为字符串
          let currentDateTime = year + "" + month + "" + day + "" + hours + "" + minutes + "" + seconds;
          let exportText = ''
          switch (this.tab.status) {
            case 1: exportText = '商销蓝票导出_' + currentDateTime
              break;
            case 2: exportText = '商销红票导出_' + currentDateTime
              break;
            case 3: exportText = '商销作废导出_' + currentDateTime
              break;
            default: exportText = '商销蓝票导出_' + currentDateTime
          }
          let timeArray = ['CreateTimeBegin', 'CreateTimeEnd', 'InvoiceTimeBegin', 'InvoiceTimeEnd']
          timeArray.map(item => {
            if (!this.$refs.SimpleSearchArea.queryParam[item]) {
              delete this.$refs.SimpleSearchArea.queryParam[item]
            }
          })
          this.handleExportXls(
            exportText,
            'POST',
            this.linkUrl.exportUrl,
            null,
            this.linkHttpHead,
            this.$refs.SimpleSearchArea.queryParam
          )
          break;
        case '重推':
          this.$confirm({
            title: '是否确定重推?',
            content: '',
            onOk() {
              that.rethrust(record)
            },
            onCancel() { },
          });
          break;
      }
    },
    // 重推
    rethrust(record) {
      this.$refs.table.loading = true
      getAction(this.linkUrl.rethrust, { salesOrderInvoiceApplyNo: record.Code }, this.linkHttpHead)
        .then(res => {
          if (res.Data) {
            this.$message.success('操作成功')
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(err => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => {
          this.$refs.table.loading = false
          setTimeout(() => {
            this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
          }, 2000)
        })
    },
    // 计算 是否展示冲红按钮
    retrunTime(date) {
      let month = moment(date).month() + 1
      let curMonth = moment().month() + 1

      let bool = curMonth > month
      return bool
    },
  },
}
</script>

<style></style>
