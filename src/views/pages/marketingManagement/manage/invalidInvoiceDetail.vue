<!-- 待冲红/作废发票详情-->
<template>
  <div>
    <a-card title="待冲红/作废发票详情" style="margin-bottom: 12px">
      <a-button @click="goBack(true,true)" slot="extra">返回</a-button>
    </a-card>
    <a-spin :spinning="loading">
      <a-card style="margin-bottom: 12px">
        <a-row :gutter="gutter">
          <a-col :md="6" :style="{ textAlign: 'center', color: getAuditColor(model && model['IsHandle']) }">
            <p>
              <a-icon :type="getAuditIcon(model && model['IsHandle'])" :style="{ color: getAuditColor(model && model['IsHandle']), fontSize: '32px' }" />
            </p>
            {{ (model && model['IsHandleStr']) || ' -- ' }}
          </a-col>
          <a-col :md="6">
            <p>开票金额</p>
            ￥{{ (model && model.InvoiceAmount) || 0 }}
          </a-col>
          <a-col :md="6">
            <p>开票编号</p>
            {{ (model && model.Code) || ' -- ' }}
          </a-col>
          <a-col :md="6">
            <p>申请时间</p>
            {{ (model && model.ApplyTime) || ' -- ' }}
          </a-col>
        </a-row>
      </a-card>
      <a-card>
        <a-tabs v-model="activeTabKey">
          <a-tab-pane :key="item['key']" :tab="item['value']" v-for="item in tabsList">
            <!-- 开票信息 -->
            <BasicInfoTemplateInvoiceDetail ref="iBasicInfoTemplateInvoiceDetail" source='dczf' :Info="model" v-if="activeTabKey == 1 && model && item['key'] == 1" />
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </a-spin>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'invalidInvoiceDetail',
  mixins: [EditMixin],
  data() {
    return {
      activeTabKey: 1,
      model: null,
      loading: false,
      id: '',
      tabsList: [
        {
          key: 1,
          value: '开票信息',
          id: '5eb8f568-188d-4e8e-847b-f1997851b5da',
        },
      ],
      httpHead: 'P36008',
      url: {
        detail: '/{v}/OrderInvoice/DetailV2',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.activeTabKey = Number(this.$route.query.key) || 1
      if (this.$route.query['tabId'] && !this.checkBtnPermissions('e8bb3f63-89ff-4dfa-bdbf-6d8803f6b073')) {
        this.tabsList = this.tabsList.filter((v) => v.id == this.$route.query['tabId'])
      }
      this.loadDetailInfo()
    }
  },
  created() { },
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    getAuditColor(status) {
      if (status) {
        return '#52c41a'
      } else {
        return 'red'
      }
    },
    getAuditIcon(status) {
      if (status == true) {
        return 'check-circle' //成功绿色图标
      } else {
        return 'close-circle' //失败红色图标
      }
    },
  },
}
</script>

<style scoped>

</style>
