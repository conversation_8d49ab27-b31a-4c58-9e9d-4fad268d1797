<!-- 开票详情-->
<template>
  <div>
    <a-card title="开票详情" style="margin-bottom: 12px">
      <a-button @click="goBack(true,true)" slot="extra">返回</a-button>
    </a-card>
    <a-spin :spinning="loading">
      <div v-if="model && model.InvoiceApplyType == 1">
        <a-alert type="error" v-if="model && (model['InvoiceStatus'] == 7 || model['InvoiceStatus'] == 9) &&  model['AuditOpinion']" :message="`失败原因：${model['AuditOpinion'] || ''}`" banner :showIcon="false" />
        <a-card style="margin-bottom: 12px">
          <a-row :gutter="gutter">
            <a-col :md="3" :style="{ textAlign: 'center', color: getAuditColor(model && model['AuditStatus']) }">
              <p>
                <a-icon :type="getAuditIcon(model && model['AuditStatus'])" :style="{ color: getAuditColor(model && model['AuditStatus']), fontSize: '32px' }" />
              </p>
              {{ (model && model['AuditStatusStr']) || ' -- ' }}
            </a-col>
            <a-col :md="3" :style="{ textAlign: 'center', color: getInvoiceAuditColor(model && model['InvoiceStatus']) }">
              <p>
                <a-icon :type="getInvoiceAuditIcon(model && model['InvoiceStatus'])" :style="{ color: getInvoiceAuditColor(model && model['InvoiceStatus']), fontSize: '32px' }" />
              </p>
              {{ (model && model['InvoiceStatusStr']) || ' -- ' }}
            </a-col>
            <a-col :md="3">
              <p>申请类型</p>
              {{ (model && model.InvoiceApplyTypeStr) || ' -- ' }}
            </a-col>
            <a-col :md="3">
              <p>开票金额</p>
              ￥{{ (model && model.InvoiceAmount) || 0 }}
            </a-col>
            <a-col :md="6">
              <p>开票编号</p>
              {{ (model && model.Code) || ' -- ' }}
            </a-col>
            <a-col :md="5">
              <p>申请时间</p>
              {{ (model && model.ApplyTime) || ' -- ' }}
            </a-col>
          </a-row>
        </a-card>
      </div>
      <a-card>
        <a-tabs v-if="model && model.InvoiceApplyType == 1" v-model="activeTabKey">
          <a-tab-pane :key="item['key']" :tab="item['value']" v-for="item in tabsList">
            <!-- 开票信息 -->
            <BasicInfoTemplateInvoiceDetail ref="iBasicInfoTemplateInvoiceDetail" :Info="model" v-if="activeTabKey == 1 && model && item['key'] == 1" />
            <!-- 审核信息 -->
            <SupAuditInformation ref="supAuditInformation" :bussinessNo="model.AuditId" v-if="activeTabKey === 2 && model && item['key'] == 2" />
          </a-tab-pane>
        </a-tabs>
        <div v-if="model && model.InvoiceApplyType !== 1">
          <!-- 开票信息 -->
          <BasicInfoTemplateInvoiceDetail ref="iBasicInfoTemplateInvoiceDetail" :Info="model" v-if="activeTabKey == 1 && model" />
        </div>
      </a-card>
    </a-spin>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'invoiceCommercialSaleDetail',
  mixins: [EditMixin],
  data() {
    return {
      activeTabKey: 1,
      model: null,
      loading: false,
      id: '',
      tabsList: [
        {
          key: 1,
          value: '开票信息',
          id: '5eb8f568-188d-4e8e-847b-f1997851b5da',
        },
        {
          key: 2,
          value: '审核信息',
          id: 'e8bb3f63-89ff-4dfa-bdbf-6d8803f6b073',
        },
      ],
      httpHead: 'P36008',
      url: {
        detail: '/{v}/OrderInvoice/DetailV2',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.activeTabKey = Number(this.$route.query.key) || 1
      if (this.$route.query['tabId'] && !this.checkBtnPermissions('e8bb3f63-89ff-4dfa-bdbf-6d8803f6b073')) {
        this.tabsList = this.tabsList.filter((v) => v.id == this.$route.query['tabId'])
      }
      this.loadDetailInfo()
    }
  },
  created() { },
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    //  /// 未开票
    //  /// </summary>
    //  未开票 = 1,
    //  /// <summary>
    //  /// 开票中
    //  /// </summary>
    //  开票中 = 2,
    //  /// <summary>
    //  /// 已开票
    //  /// </summary>
    //  已开票 = 3,
    //  /// <summary>
    //  /// 已冲红
    //  /// </summary>
    //  已冲红 = 4,
    //  /// <summary>
    //  /// 已作废
    //  /// </summary>
    //  已作废 = 5,
    //  /// <summary>
    //  /// 部分冲红
    //  /// </summary>
    //  部分冲红 = 6,
    //  /// <summary>
    //  /// 开票失败
    //  /// </summary>
    //  开票失败 = 7,

    //  /// <summary>
    //  /// 部分作废
    //  /// </summary>
    //  部分作废 = 8,

    //  /// <summary>
    //  ///推送发票中心失败 = 9,
    //  /// </summary>
    //  推送发票中心失败 = 9,
    getInvoiceAuditColor(status) {
      if (status == 7 || status == 9) {
        return 'red'
      } else {
        return '#52c41a'
      }
    },
    getInvoiceAuditIcon(status) {
      if (status == 7 || status == 9) {
        return 'close-circle' //失败红色图标
      } else if (status == 2) {
        return 'clock-circle' //进行中绿色图标
      } else {
        return 'check-circle' //成功绿色图标
      }
    },

  },
}
</script>

<style lang="less" scoped>

/* 设置table行的高度防止错位 */
/deep/.ant-table-tbody tr {
  height: 42px !important;
}
</style>
