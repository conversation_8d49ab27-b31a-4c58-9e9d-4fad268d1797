<!-- 申请开票 -->
<template>
  <div>
    <a-alert type="error" :message="`驳回原因：${model.AuditOpinion || ' -- '}`" banner :showIcon="false" v-if="model.AuditOpinion && model.AuditStatus == 3 && !isRed" />
    <a-card style="margin: 15px 0">
      <a-spin :spinning="spinning">
        <div :class="'form-model-style'">
          <a-spin :spinning="loading">
            <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical" v-if="[1, 2].includes(opType)">
              <!-- 客户信息 -->
              <template>
                <a-row :gutter="gutter">
                  <a-col :span="24">
                    <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">客户信息</div>
                  </a-col>
                  <a-col :span="md">
                    <a-form-model-item label="客户名称：" prop="CustomerId">
                      <SingleChoiceSearchView style="width: 100%" placeholder="请输入客户名称/客户编号" :httpParams="{ PageIndex: 1, PageSize: 100 }" httpHead="P36002" httpType="GET" keyWord="KeyWord" Url="/{v}/Customer/ListForOrder" v-model="model.CustomerId" :name="model.CustomerName" @dataLoaded="dataLoaded" @change="handleCustomer" :invalidChecked="true" :disabled="invoiceApplyType == 3 || invoiceApplyType == 2" />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="md">
                    <a-form-model-item label="发票类型：" prop="InvoiceType">
                      <EnumSingleChoiceView style="width: 100%" placeholder="请选择" dictCode="EnumSaleInvoiceType" v-model="model.InvoiceType" @change="
                          (value, txt) => {
                            model.InvoiceTypeStr = txt
                            model.InvoiceTypeV2Str = txt
                            setCustomerInvoiceInfo()
                          }
                        " :disabled="invoiceApplyType == 3 || invoiceApplyType == 2" />
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </template>
              <!-- 开票信息 -->
              <template>
                <a-row :gutter="gutter">
                  <a-col :span="24">
                    <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">开票信息</div>
                  </a-col>
                  <a-col :span="24" v-if="model.InvoiceType > 0 && model.InvoiceType != 1">
                    <a-form-model-item label="增值税一般纳税人申请表：" prop="TaxpayerApplicationForm">
                      <!-- <upload
                        :bg="model.TaxpayerApplicationForm"
                        @change="(url) => onUrlChange(url, 'TaxpayerApplicationForm')"
                        @clear="(url) => onClear('TaxpayerApplicationForm')"
                        :bName="BName"
                        :dir="Dir"
                      ></upload> -->
                      <template v-if="model.CustomerInfo && (model.CustomerInfo.TaxpayerApplicationForms || []).length > 0">
                        <a-radio-group v-model="model.TaxpayerApplicationForm" style="width: 100%" @change="onRadioGroupChange">
                          <a-col :span="4" v-for="(imgUrl, index) in model.CustomerInfo
                              ? model.CustomerInfo.TaxpayerApplicationForms || []
                              : []" :key="index">
                            <div class="cflex center" style="width: 120px">
                              <img :src="imgUrl" style="width: 120px; height: 120px" @click.stop="
                                  $refs.imageListModal.show(model.CustomerInfo.TaxpayerApplicationForms, index)
                                " />
                              <a-radio :value="imgUrl"></a-radio>
                            </div>
                          </a-col>
                        </a-radio-group>
                      </template>
                      <span v-else>无</span>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="24">
                    <a-col :span="md">
                      <a-form-model-item label="纳税人识别号：" prop="TaxNumber">
                        <a-input v-model="model.TaxNumber" style="width: 100%" placeholder="请输入" disabled />
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="md">
                      <a-form-model-item label="注册地址：" prop="RegAreaCode">
                        <!-- <SelectAreaView
                        style="width: 100%"
                        ref="selectCityAreas"
                        :selectCode="true"
                        :defaultCodes="areaCodes"
                        @change="(val, node) => onAreasChange(val, node, model, 'ReceivingArea')"
                        disabled
                        v-if="IsSelectCityAreas"
                      /> -->
                        <a-input v-model="model.RegAddress" style="width: 100%" placeholder="请输入" disabled />
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="md">
                      <a-form-model-item label="注册电话：" prop="RegPhone">
                        <a-input v-model="model.RegPhone" style="width: 100%" placeholder="请输入" disabled />
                      </a-form-model-item>
                    </a-col>
                  </a-col>
                  <a-col :span="24">
                    <a-col :span="md">
                      <a-form-model-item label="开户银行：" prop="BankName">
                        <a-select placeholder="请选择" v-model="model.BankName" :disabled="invoiceApplyType == 3 || invoiceApplyType == 2">
                          <a-select-option :value="''"> 请选择 </a-select-option>
                          <a-select-option v-for="(r, rIndex) in BankList" :key="rIndex" :value="r.value">
                            {{ r.title }}
                          </a-select-option>
                        </a-select>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="md">
                      <a-form-model-item label="银行账号：" prop="BankCardNumber">
                        <a-select placeholder="请选择" v-model="model.BankCardNumber" :disabled="invoiceApplyType == 3 || invoiceApplyType == 2">
                          <a-select-option :value="''"> 请选择 </a-select-option>
                          <a-select-option v-for="(r, rIndex) in BankCardNumberList" :key="rIndex" :value="r.value">
                            {{ r.title }}
                          </a-select-option>
                        </a-select>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="md">
                      <a-form-model-item label="电子邮箱：" prop="RecipientEmail">
                        <a-input v-model="model.RecipientEmail" :disabled="invoiceApplyType == 1?true:false" style="width: 100%" placeholder="请输入" />
                      </a-form-model-item>
                    </a-col>
                  </a-col>
                </a-row>
                <!-- 单据信息 -->
                <a-row :gutter="gutter" style="margin-bottom: 24px">
                  <a-col :span="24">
                    <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">单据信息</div>
                  </a-col>
                  <a-col :span="24">
                    <!-- 列表 -->
                    <SimpleTable ref="tableGoods" :showTab="invoiceApplyType == 1 ? true : false" :tab="invoiceApplyType != 1 ? invoiceApplyType1Tab : tabGoods" :columns="columnsGoods" @operate="operate" @onSelect="handleSelectedRows" @onSelectAll="onSelectAll">
                      <span slot="commonContent" slot-scope="{ text }">
                        <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                      </span>
                      <span slot="OrderNo" slot-scope="{ text }">
                        <a @click="
                            onDetailClick('/pages/marketingManagement/ordersManagementListDetail', { code: text })
                          ">
                          <j-ellipsis :value="text" :length="15" />
                        </a>
                      </span>
                      <span slot="BillsAmount" slot-scope="{ text, record }">
                        <j-ellipsis :value="`${
                            record.BillsType == 22 ? '￥-' + Number(text).toFixed(2) : '￥' + Number(text).toFixed(2)
                          }`" :length="15" />
                      </span>
                    </SimpleTable>
                  </a-col>
                  <a-col :span="24">
                    <span style="margin-right: 10px">合计金额：<span style="color: red; font-size: 30px">¥{{ model.InvoiceAmount || 0 }}</span>
                    </span>
                  </a-col>
                </a-row>
                <!-- 红票   作废    原发票信息 -->
                <a-row :gutter="gutter" style="margin-bottom: 24px" v-if="invoiceApplyType != 1">
                  <a-col :span="24">
                    <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">原发票信息</div>
                  </a-col>
                  <a-col :span="24">
                    <!-- 列表 -->
                    <SimpleTable ref="tableInvoice" :tab="tabInvoices" :columns="columnsInvoices" @operate="operate">
                      <span slot="commonContent" slot-scope="{ text }">
                        <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                      </span>
                    </SimpleTable>
                  </a-col>
                  <!-- <a-col :span="24" v-if="invoiceApplyType == 2">
                    <a-form-model-item label="冲红范围：" prop="InvoiceScope">
                      <EnumSingleChoiceView style="width: 100%" placeholder="请选择" dictCode="EnumInvoiceScope" tagType="radio" :notOption="isRed || (opType == 2 && invoiceApplyType == 2) ? [1] : []" v-model="model.InvoiceScope" />
                    </a-form-model-item>
                  </a-col> -->
                  <a-col :span="24" v-if="invoiceApplyType == 2">
                    <a-form-model-item label="退税证明：" prop="DebentureUrl">
                      <upload :bg="model.DebentureUrl" @change="(url) => onUrlChange(url, 'DebentureUrl')" @clear="(url) => onClear('DebentureUrl')" :bName="BName" :dir="Dir"></upload>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="24" v-if="invoiceApplyType == 2">
                    <a-form-model-item label="红字信息表：" prop="RedLetterFormUrl">
                      <upload :bg="model.RedLetterFormUrl" @change="(url) => onUrlChange(url, 'RedLetterFormUrl')" @clear="(url) => onClear('RedLetterFormUrl')" :bName="BName" :dir="Dir"></upload>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="24" v-if="invoiceApplyType == 3">
                    <a-form-model-item label="作废说明：" prop="CancelCertificate">
                      <upload :bg="model.CancelCertificate" @change="(url) => onUrlChange(url, 'CancelCertificate')" @clear="(url) => onClear('CancelCertificate')" :bName="BName" :dir="Dir"></upload>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </template>
            </a-form-model>
          </a-spin>
        </div>

        <a-affix :offset-bottom="0" style="float: right; width: 100%; text-align: right">
          <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
            <YYLButton menuId="0bd3768d-be27-4107-a905-52f0135b6486" text="保存" type="primary" @click="handleSave" />
            <YYLButton menuId="6cda3e6c-8f5d-449d-8d40-e6821b81d739" text="保存并提交审核" type="primary" @click="handleSubmit" />
            <a-button @click="goBack(true,true)">取消</a-button>
          </a-card>
        </a-affix>
      </a-spin>
    </a-card>
    <!-- 选择单据 -->
    <SelectReceiptListModal ref="iSelectReceiptListModal" @ok="handleSelectReceiptListModalOk" />
    <!-- 商品明细 -->
    <CommodityDetailListModal ref="iCommodityDetailListModal" />

    <!-- 查看图片 -->
    <ImageListModal ref="imageListModal" />
  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import moment from 'moment'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '申请开票',
  name: 'invoiceCommercialSaleEdit',
  mixins: [EditMixin, ListMixin],
  components: { JEllipsis },
  data() {
    return {
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
      spinning: false,
      loading: false,
      dateFormat: 'YYYY/MM/DD',
      IsSelectCityAreas: true,
      md: 8,
      model: {
        CustomerId: '',
        CustomerInfo: null,
        InvoiceAmount: 0,
      },
      areaCodes: [],
      BankList: [],
      BankCardNumberList: [],
      id: '',
      invoiceApplyType: '', // 1蓝票 2红票 3作废
      invoiceApplyType1Tab: {
        rowKey: 'Id',
        statusList: [],
        hideIpagination: true,
        sortArray: ['OrderNo', 'BillsAmount'],
      },
      opType: 1, //1 新增  2 编辑
      tabGoods: {
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        rowKey: 'Id',
        rowSelection: {
          type: 'checkbox',
          getCheckboxProps: this.getCheckboxProps,
        },
        sortArray: ['OrderNo', 'BillsAmount'],
        hideIpagination: true,
        operateBtn: [
          {
            name: '批量移除',
            type: '',
            icon: '',
            key: '批量移除',
            id: '3e0f543c-7bee-481e-9b24-9a05428d3211',
          },
          {
            name: '选 择',
            type: 'primary',
            icon: '',
            key: '选择',
            id: '2dc34433-5e9c-4655-a222-bd0036811d7f',
          },
        ],
      },
      columnsGoods: [
        {
          title: '单据编号',
          dataIndex: 'BillsNo',
          ellipsis: true,
          width: 200,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据类型',
          dataIndex: 'BillsTypeStr',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'OrderNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'OrderNo' },
        },
        {
          title: '订单付款方式',
          dataIndex: 'PaymentModeStr',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BillsTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '金额',
          dataIndex: 'BillsAmount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'BillsAmount' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          actionBtn: [
            {
              name: '移除',
              icon: '',
              id: '3270f283-ee5c-412c-8ddb-2975e9428dac',
              specialShowFuc: (e) => {
                let record = e || {}
                return this.invoiceApplyType != 3 && this.invoiceApplyType != 2
              },
            },
            {
              name: '商品明细',
              icon: '',
              id: '7e0aed05-238a-4922-a577-e42dc2e49254',
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      tabInvoices: {
        hideIpagination: true,
      },
      customerList: [],
      columnsInvoices: [],
      httpHead: 'P36008',
      isCancel: false, //是否作废
      isRed: false, //是否是冲红
      Details: [],
      url: {
        createInvoiceApply: '/{v}/OrderInvoice/CreateInvoiceApply',
        updateInvoiceApply: '/{v}/OrderInvoice/UpdateInvoiceApply',
        cancelInvoice: '/{v}/OrderInvoice/CancelInvoice',
        detail: '/{v}/OrderInvoice/DetailV2',
        createApproval: '/{v}/ApprovalWorkFlowInstance/CreateApproval', // 创建审核实例
        getBillsByCustomerForRedInvoice: '/{v}/Order/GetBillsByCustomerForRedInvoice',
      },
    }
  },
  created() { },
  computed: {
    rules() {
      let that = this
      return {
        CustomerId: [{ required: true, message: '请选择!' }],
        InvoiceType: [{ required: true, message: '请选择!' }],
        TaxpayerApplicationForm: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (that.model.TaxpayerApplicationForm) {
                callback()
              } else {
                if (
                  that.model.CustomerInfo &&
                  that.model.CustomerInfo.TaxpayerApplicationForms &&
                  that.model.CustomerInfo.TaxpayerApplicationForms.length > 0
                ) {
                  callback(new Error('请选择!'))
                } else {
                  callback()
                }
              }
            },
          },
        ],
        DebentureUrl: [{ required: false, message: '请选择!' }],
        InvoiceScope: [{ required: true, message: '请选择!' }],
        RedLetterFormUrl: [{ required: false, message: '请选择!' }],
        CancelCertificate: [{ required: false, message: '请选择!' }],
        TaxNumber: [{ required: true, message: '请输入!' }],
        RegPhone: [{ required: true, message: '请输入!' }],
        BankName: [{ required: false, message: '请输入!' }],
        RegAreaCode: [{ required: false, message: '请先选择客户!' }],
        BankCardNumber: [{ required: false, message: '请输入!' }],
        RecipientEmail: [{
          required: false, validator: (rule, value, callback) => {
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (that.model.RecipientEmail && (emailRegex.test(that.model.RecipientEmail) == false)) {
              callback(new Error('请输入正确的邮箱!'))
            } else {
              callback()
            }
          },
        }],
      }
    },
  },
  watch: {
    Details: {
      deep: true,
      handler: function (newForm, oldForm) {
        if (this.opType == 1) {
          // if (newForm.length > 0) {
          //   let InvoiceAmount = 0
          //   newForm.map((c) => {
          //     if (c['BillsType'] != 22) {
          //       InvoiceAmount += c['BillsAmount'] || 0
          //     } else {
          //       InvoiceAmount -= c['BillsAmount'] || 0
          //     }
          //     if ((c['children'] || []).length) {
          //       c['children'].map((v) => {
          //         if (v['BillsType'] != 22) {
          //           InvoiceAmount += v['BillsAmount'] || 0
          //         } else {
          //           InvoiceAmount -= v['BillsAmount'] || 0
          //         }
          //       })
          //     }
          //     // console.log('BillsAmount           ', c['BillsAmount'])
          //   })
          //   this.$set(this.model, 'InvoiceAmount', Number(InvoiceAmount || 0).toFixed(2))
          // } else {
          //   this.$set(this.model, 'InvoiceAmount', 0)
          // }
        }
      },
    },
  },
  mounted() {
    // invoiceApplyType 1蓝票 2红票 3作废 opType 1申请  2编辑
    if (this.$route.query) {
      this.isRed = this.$route.query.red == 1
      this.isCancel = this.$route.query.isCancel == 1
      this.id = this.$route.query.id || ''
      this.invoiceApplyType = this.$route.query.invoiceApplyType || ''
      this.opType = this.$route.query.opType ? Number(this.$route.query.opType) : 1
      this.setTitle(
        this.opType == 1
          ? `${this.invoiceApplyType == 1 ? '蓝票' : '红票'}申请`
          : this.invoiceApplyType == 3
            ? this.isCancel
              ? `作废申请`
              : '作废编辑'
            : `编辑${this.invoiceApplyType == 1 ? '蓝票' : '红票'}`
      )
      this.initColumnsInvoices()
      // 编辑时调用详情  或 冲红 申请 调用详情
      if (this.opType == 2 || (this.opType == 1 && this.invoiceApplyType == 2)) {
        this.loadDetailInfo()
      }
    }
  },
  methods: {
    moment,
    jiSuanInvoiceAmount() {
      if (this.Details.length > 0) {
        let InvoiceAmount = 0
        this.Details.map((c) => {
          if (c['BillsType'] != 22) {
            InvoiceAmount += c['BillsAmount'] || 0
          } else {
            // if(this.isRed) InvoiceAmount += c['BillsAmount'] || 0 ; else InvoiceAmount -= c['BillsAmount'] || 0;
            InvoiceAmount -= c['BillsAmount'] || 0
          }
          if ((c['children'] || []).length) {
            c['children'].map((v) => {
              if (v['BillsType'] != 22) {
                InvoiceAmount += v['BillsAmount'] || 0
              } else {
                // if(this.isRed) InvoiceAmount += v['BillsAmount'] || 0 ; else InvoiceAmount -= v['BillsAmount'] || 0;
                InvoiceAmount -= v['BillsAmount'] || 0
              }
            })
          }
          // console.log('BillsAmount           ', c['BillsAmount'])
        })
        this.$set(this.model, 'InvoiceAmount', Number(InvoiceAmount || 0).toFixed(2))
      } else {
        this.$set(this.model, 'InvoiceAmount', 0)
      }
    },
    getCheckboxProps(record) {
      // console.log(record)
      // 不是出库单不可选择
      return {
        props: {
          disabled: record['BillsType'] != 21,
          name: record.BillsNo,
        },
      }
    },
    onUrlChange(url, name) {
      if (typeof url == 'object') {
        this.$set(this.model, name, url.FileId)
      } else {
        this.$set(this.model, name, url)
      }
    },
    onClear(name) {
      this.$set(this.model, name, '')
    },
    /**
     * 选择地址区域
     **/

    onAreasChange(val, node, model, key) {
      if (!val) {
        return
      }
      // console.log(node)
      this.areaCodes = val
      if (model && key) {
        if (typeof node != 'undefined') {
          model[key + 'Code'] = node[node.length - 1].code
          model[key + 'Province	'] = node[0].code
          model[key + 'City	'] = node[1].code
          model[key + 'County	'] = node[2].code
          model[key + 'Town	'] = node[3].code
          model[key + 'FullName'] = node[node.length - 1].source.FullName
        } else {
          model[key + 'Code'] = null
          model[key + 'Province	'] = null
          model[key + 'City	'] = null
          model[key + 'County	'] = null
          model[key + 'Town	'] = null
          model[key + 'FullName'] = ''
        }
        // if (form) {
        //   form.clearValidate([key + 'Code'])
        // }
      }
    },
    // 列表操作
    operate(record, type) {
      let list = this.$refs.tableGoods.dataSource || []
      if (type == '移除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认移除该数据？',
          onOk() {
            let parent = list.filter((j) => j.OrderNo == record.OrderNo) || []
            if (parent.length) {
              that.$refs.tableGoods.dataSource = that.setTableData(list.filter((v) => v.Id != parent[0]['Id']))
            } else {
              that.$refs.tableGoods.dataSource = that.setTableData(list.filter((v) => v.Id != record['Id']))
            }

            that.$set(that, 'Details', that.$refs.tableGoods.dataSource)

            that.jiSuanInvoiceAmount()
          },
          onCancel() { },
        })
      } else if (type == '商品明细') {
        this.$refs.iCommodityDetailListModal.show(record)
      } else if (type == '批量移除') {
        let selectedRowKeys = this.$refs.tableGoods.selectedRowKeys || []
        if (list.length < 1) {
          this.$message.warning('没有可移除的数据!')
          return
        }
        if (selectedRowKeys.length < 1) {
          this.$message.warning('请先选择需要移除的数据!')
          return
        }
        let selectedRows = this.$refs.tableGoods.selectedRows || []
        let parent = list.filter((j) => selectedRows.findIndex((b) => b.OrderNo == j.OrderNo) > -1) || []
        if (parent.length) {
          this.$refs.tableGoods.dataSource = this.setTableData(
            list.filter((j) => parent.findIndex((v) => v['Id'] == j['Id']) < 0)
          )
        } else {
          this.$refs.tableGoods.dataSource = this.setTableData(
            list.filter((j) => selectedRowKeys.findIndex((v) => v == j['Id']) < 0)
          )
        }

        this.$set(this, 'Details', this.$refs.tableGoods.dataSource)
        this.$refs.tableGoods.selectedRowKeys = []
        this.$refs.tableGoods.selectedRows = []
        this.jiSuanInvoiceAmount()
      } else if (type == '选择') {
        let that = this
        if (!this.model.CustomerId) {
          that.$message.warning('请先选择客户!')
          return
        }
        this.$refs.iSelectReceiptListModal.show(this.model.CustomerInfo, list)
      } else if (type == '查看发票') {
        // this.onDetailClick('invoiceCommercialSaleDetail', {
        //   id: record.Id,
        //   key: 1,
        //   tabId: '5eb8f568-188d-4e8e-847b-f1997851b5da'
        // })
        // console.log('InvoiceFileUrl' + record['InvoiceFileUrl'], record)

        if (record['InvoiceFileUrl']) {
          this.getFileTempUrl(record['InvoiceFileUrl'], (data) => {
            window.open(data.TempUrl)
          })
        } else this.$message.warning('暂无文件')
      }
    },
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            that.$set(that, 'Details', that.model.Details || [])
            if ((that.Details || []).length && !this.isRed) {
              let goods = []
              that['Details'].map((v) => {
                let item = {
                  Id: v['Id'],
                  BillsNo: v['BillsCode'],
                  BillsType: v['BillsType'],
                  BillsTypeStr: v['BillsTypeStr'],
                  OrderId: v['OrderId'],
                  OrderNo: v['SalesOrderCode'],
                  PaymentMode: v['SalesOrderPaymentMode'],
                  PaymentModeStr: v['SalesOrderPaymentModeStr'],
                  BillsTime: v['BillsTime'],
                  BillsAmount: v['BillsAmount'],
                  children: [],
                  ParentCode: v['ParentCode'] || null,
                }
                let childs =
                  that['Details'].filter(
                    (j) =>
                      j['SalesOrderCode'] == item['OrderNo'] && j['ParentCode'] == v['BillsCode'] && j.BillsType != 21
                  ) || []
                childs.map((j) => {
                  item['children'].push({
                    Id: j['Id'],
                    BillsNo: j['BillsCode'],
                    BillsType: j['BillsType'],
                    BillsTypeStr: j['BillsTypeStr'],
                    OrderId: j['OrderId'],
                    OrderNo: j['SalesOrderCode'],
                    PaymentMode: j['SalesOrderPaymentMode'],
                    PaymentModeStr: j['SalesOrderPaymentModeStr'],
                    BillsTime: j['BillsTime'],
                    BillsAmount: j['BillsAmount'],
                    ParentCode: j['ParentCode'],
                  })
                })
                if ((item.children || []).length < 1) {
                  item.children = null
                }
                if (item.BillsType == 21) {
                  goods.push(item)
                } else if (!item.ParentCode) {
                  goods.push(item)
                }

                // console.log(' goods            ', goods)
              })
              // console.log(that.setTableData(goods))
              if (goods && goods.length > 0 && that.$refs.tableGoods) {
                that.$set(that.$refs.tableGoods, 'dataSource', that.setTableData(goods))
                that.$set(this.$refs.tableGoods, 'expandedRowKeys', [goods[0].Id])
              }
              that.jiSuanInvoiceAmount()
            }
            // 冲红
            if (this.invoiceApplyType == 2 && this.isRed) {
              this.setTableGoodsData()
            }
            if (this.invoiceApplyType != 1) {
              let list = []
              if (this.model.OriginalInvoices && this.model.OriginalInvoices.length > 0) {
                this.model.OriginalInvoices.forEach((x) => {
                  if (x.Results && x.Results.length > 0) {
                    list = list.concat(x.Results)
                  }
                })
              } else if (this.model.Results && this.model.Results.length > 0) {
                list = [].concat(this.model.Results)
              }
              that.$set(that.$refs.tableInvoice, 'dataSource', list)
            }
            this.$set(this.model, 'CustomerInfo', {
              Id: this.model.CustomerId,
              Name: this.model.CustomerName,
              Code: this.model.CustomerCode,
              PinyinCode: this.model.CustomerPinyinCode,
              Title: this.model.Title,
              TaxNumber: this.model.TaxNumber,
              RegAreaId: this.model.RegAreaId,
              ReceivingAreaCode: this.model.RegAreaCode,
              ReceivingAreaFullName: this.model.RegAreaFullName,
              RegAddress: this.model.RegAddress,
              RegPhone: this.model.RegPhone,
              BankName: this.model.BankName,
              BankCardNumber: this.model.BankCardNumber,
              RecipientEmail: this.model.RecipientEmail,
              CustomerCode: this.model.CustomerCode,
            })
            if (this.opType == 2) {
              this.getCustomerFiles(this.model.CustomerName)
            }

            if (this.model.RegAreaCode) this.areaCodes = this.getDefaultValueByCode(this.model.RegAreaCode)
            this.toggle('IsSelectCityAreas')
            that.model.InvoiceType = '' + (that.model.InvoiceType || '')

            // console.log('model = ', this.model)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // 冲红 获取单据信息
    setTableGoodsData() {
      let that = this
      getAction(that.url.getBillsByCustomerForRedInvoice, { blueInvoiceId: that.id }, that.httpHead).then((res) => {
        if (res.IsSuccess) {
          ; (res.Data || []).map((v) => {
            if (v.BillsType == 22) v.BillsAmount = Math.abs(v.BillsAmount)
          })
          that.$set(that, 'Details', res.Data || [])
          if ((that.Details || []).length) {
            let goods = []
            that['Details'].map((v) => {
              let item = {
                Id: v['Id'],
                BillsNo: v['BillsNo'],
                BillsType: v['BillsType'],
                BillsTypeStr: v['BillsTypeStr'],
                OrderId: v['OrderId'],
                OrderNo: v['OrderNo'],
                PaymentMode: v['PaymentMode'],
                PaymentModeStr: v['PaymentModeStr'],
                BillsTime: v['BillsTime'],
                BillsAmount: v['BillsAmount'],
                children: [],
                ParentCode: v['ParentCode'] || null,
              }
              let childs =
                that['Details'].filter(
                  (j) => j['OrderNo'] == item['OrderNo'] && j['ParentCode'] == v['BillsNo'] && j.BillsType != 21
                ) || []
              childs.map((j) => {
                item['children'].push({
                  Id: j['Id'],
                  BillsNo: j['BillsNo'],
                  BillsType: j['BillsType'],
                  BillsTypeStr: j['BillsTypeStr'],
                  OrderId: j['OrderId'],
                  OrderNo: j['OrderNo'],
                  PaymentMode: j['PaymentMode'],
                  PaymentModeStr: j['PaymentModeStr'],
                  BillsTime: j['BillsTime'],
                  BillsAmount: j['BillsAmount'],
                  ParentCode: j['ParentCode'],
                })
              })
              if ((item.children || []).length < 1) {
                item.children = null
              }
              if (item.BillsType == 21) {
                goods.push(item)
              } else if (!item.ParentCode) {
                goods.push(item)
              }

              // console.log(' goods            ', goods)
            })
            // console.log(that.setTableData(goods))
            if (goods && goods.length > 0 && that.$refs.tableGoods) {
              that.$set(that.$refs.tableGoods, 'dataSource', that.setTableData(goods))
              that.$set(this.$refs.tableGoods, 'expandedRowKeys', [goods[0].Id])
            }
            that.jiSuanInvoiceAmount()
          }
        } else {
          that.$message.error(res.Msg)
        }
      })
    },
    // 保存为草稿
    onSubmitIsSubmit() {
      // console.log(this.opType)

      let arr = []
      this.$refs.ruleForm.validateField(['CustomerId', 'RecipientEmail'], (valid) => {
        arr.push(valid ? false : true)
      })
      if (arr.some((v) => !v)) {
        return null
      }
      return this.setFormData()
    },
    onSubmit() {
      // console.log(this.opType)

      let returnValid = false
      this.$refs.ruleForm.validate((valid) => {
        returnValid = valid
      })
      if ((this.Details || []).length < 1) {
        this.$message.warning('请添加单据信息!')
        returnValid = false
        return
      }
      if (!returnValid) {
        return returnValid
      }
      return this.setFormData()
    },
    setFormData() {
      // return
      let formData = JSON.parse(JSON.stringify(this.model))
      let goods = []
      this['Details'].map((v) => {
        // console.log(v)
        goods.push({
          BillsCode: v['BillsNo'] || v['BillsCode'],
          BillsType: v['BillsType'],
          BillsTypeStr: v['BillsTypeStr'],
          OrderId: v['OrderId'],
          OrderNo: v['OrderNo'] || v['SalesOrderCode'],
          SalesOrderPaymentMode: v['PaymentMode'] || v['SalesOrderPaymentMode'],
          SalesOrderPaymentModeStr: v['PaymentModeStr'] || v['SalesOrderPaymentModeStr'],
          BillsTime: v['BillsTime'],
          BillsAmount: v['BillsAmount'],
          ParentCode: v['ParentCode'] || null,
        })
        if ((v['children'] || []).length) {
          v['children'].map((j) => {
            goods.push({
              BillsCode: j['BillsNo'] || j['BillsCode'],
              BillsType: j['BillsType'],
              BillsTypeStr: j['BillsTypeStr'],
              OrderId: j['OrderId'],
              OrderNo: j['OrderNo'] || j['SalesOrderCode'],
              SalesOrderPaymentMode: j['PaymentMode'] || j['SalesOrderPaymentMode'],
              SalesOrderPaymentModeStr: j['PaymentModeStr'] || j['SalesOrderPaymentModeStr'],
              BillsTime: j['BillsTime'],
              BillsAmount: j['BillsAmount'],
              ParentCode: v['BillsNo'] || v['BillsCode'],
            })
          })
        }
      })
      // console.log(goods)
      // return
      formData['Details'] = goods
      formData['InvoiceType'] = formData['InvoiceType'] == '' ? null : Number(formData['InvoiceType'])
      formData['InvoiceTypeV2'] = formData['InvoiceType'] == '' ? null : Number(formData['InvoiceType'])
      formData['InvoiceScope'] = formData['InvoiceScope'] || null
      formData['InvoiceApplyType'] = this.invoiceApplyType == '' ? null : Number(this.invoiceApplyType)
      formData['InvoiceApplyTypeStr'] = `${this.invoiceApplyType == 1 ? '蓝票' : this.invoiceApplyType == 3 ? '作废' : '红票'
        }申请`

      // 红票 原发票信息
      if (this.opType == 2) {
        if (this.isCancel) {
          formData['OriginalInvoiceCodes'] = this.model.Code
        } else if ((this.model.OriginalInvoices || []).length) {
          formData['OriginalInvoiceCodes'] = this.model.OriginalInvoices.map((v) => v.Code).join(',')
        }
        // else if ((this.model.Results || []).length) {
        //   formData['OriginalInvoiceCodes'] = this.model.Results.map((v) => v.InvoiceCode).join(',')
        // }
      } else {
        formData['OriginalInvoiceCodes'] = this.model.Code
      }
      // if (this.invoiceApplyType != 1 && (this.model.OriginalInvoices || []).length) {
      //   formData['OriginalInvoiceCodes'] = this.model.OriginalInvoices.map((v) => v.InvoiceCode).join(',')
      // } else if (this.invoiceApplyType != 1 && (this.model.Results || []).length) {
      //   formData['OriginalInvoiceCodes'] = this.model.Results.map((v) => v.InvoiceCode).join(',')
      // }
      delete formData['CustomerInfo']
      // console.log('formData       ', formData)
      return formData
    },
    // 保存
    handleSave() {
      this.$refs.ruleForm.clearValidate()
      let formData = this.onSubmitIsSubmit()
      // return
      if (formData) {
        // console.log('opType    ', this.opType)

        if (this.opType == 1) formData['IsSubmit'] = false
        // console.log('formData      ', formData)

        this.stepSubmitPost(formData)
      }
    },

    stepSubmitPost(formData, act, call) {
      if (this.id) formData['Id'] = this.id
      formData['RegLinkPhone'] = formData.RegPhone

      if (formData.InvoiceType == 1 && !formData.TaxpayerApplicationForm) {
        delete formData.TaxpayerApplicationForm
      }
      console.log(formData)

      if (formData.IsSystemCreate && this.invoiceApplyType == 2 && !this.isRed)
        formData['InvoiceAmount'] = Math.abs(formData['InvoiceAmount'])
      this.spinning = true
      // const url =
      //   this.opType == 1
      //     ? this.url.createInvoiceApply
      //     : this.invoiceApplyType == 3
      //     ? this.url.cancelInvoice
      //     : this.url.updateInvoiceApply
      const url =
        this.opType == 2
          ? this.isCancel
            ? this.url.cancelInvoice
            : this.url.updateInvoiceApply
          : this.url.createInvoiceApply

      let submitData = JSON.parse(JSON.stringify(formData))
      submitData.InvoiceAmount = formData.InvoiceAmount * 1
      submitData.InvoiceScope = 2 //formData.InvoiceScope ? formData.InvoiceScope * 1 : null

      postAction(url, submitData, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.spinning = false
          this.$message.error(res.Msg)
          return
        }
        // 草稿的情况下
        if (!submitData.IsSubmit) {
          this.spinning = false
          this.$message.success('操作成功！')
          setTimeout(() => {
            this.goBack(true,true)
          }, 500)
          return
        }
        if (res.Data && (typeof res.Data === 'string')) {
          res.Data = [res.Data]
        }
        this.$message.success('操作成功！')
        this.goBack(true,true)
        // 创建审判流程
        // if ((res.Data || []).length) {
        //   res.Data.map(v => {
        //     this.createApproval(
        //       {
        //         // Remark: data.remark,
        //         BussinessNo: v
        //       },
        //       bool => {
        //         this.spinning = false
        //         if (bool) {
        //           this.$message.success('操作成功！')
        //           setTimeout(() => {
        //             this.goBack(true,true)
        //           }, 500)
        //         }
        //       }
        //     )
        //   })
        // }

        // 下一步
        call && call()
      }).finally(() => {
        this.spinning = false
      })
    },
    // 创建审核实例
    createApproval(data, callback) {
      if (!data['BussinessNo']) {
        this.$message.error('BussinessNo未获取到值')
        return
      }
      postAction(
        this.url.createApproval,
        {
          BussinessNo: data['BussinessNo'],
          Scenes: 33,
          OpratorId: Vue.ls.get(USER_ID)
          // Remark: data['Remark']
        },
        'P36005'
      ).then(res => {
        if (!res.IsSuccess) {
          this.$message.error(res.Msg)
          callback && callback(false)
          return
        }
        // this.$message.success('操作成功！')
        callback && callback(true)
      })
    },
    // 保存并审核
    handleSubmit() {
      this.$refs.ruleForm.clearValidate()
      let formData = this.onSubmit()
      if (!formData) return
      if (formData.InvoiceType != 1 && !formData.TaxpayerApplicationForm) {
        this.$message.warning('请先到客户档案管理维护一般纳税人申请表后再申请!')
        return
      }


      formData.IsSubmit = true
      this.stepSubmitPost(formData)
    },
    // 选择单据
    handleSelectReceiptListModalOk(list) {
      this.$set(this, 'Details', list)
      this.$set(this.$refs.tableGoods, 'expandedRowKeys', [list[0].Id])
      this.$set(this.$refs.tableGoods, 'dataSource', this.setTableData(list))

      this.jiSuanInvoiceAmount()
      // 默认展开第一个
    },
    // 递归重组数据
    setTableData(data) {
      if (data == null || data.length <= 0) {
        return []
      }
      let tempArr = []
      try {
        data.forEach((item) => {
          var temp = item
          if (item.Childrens && item.Childrens.length > 0) {
            temp['children'] = this.setTableData(item.Childrens)
          }
          tempArr.push(temp)
        })
      } catch (e) {
        console.log(e)
      }
      return tempArr
    },
    dataLoaded(data) {
      this.customerList = data || []
      if (this.model.CustomerInfo) {
        let bankItem = this.customerList.find(item => {
          return this.model.CustomerInfo.CustomerCode == item.Code
        })
        if (bankItem) {
          this.BankList = [{ title: bankItem.BankName, value: bankItem.BankName }]
          this.BankCardNumberList = [{ title: bankItem.BankCardNumber, value: bankItem.BankCardNumber }]
        }
      }
    },
    // 选择客户
    handleCustomer(val, txt, item) {
      this.$set(this.model, 'CustomerInfo', val ? item : '')
      if (this.model.CustomerInfo) {
        if (
          this.model.CustomerInfo.TaxpayerApplicationForms &&
          this.model.CustomerInfo.TaxpayerApplicationForms.length == 1
        ) {
          this.model.TaxpayerApplicationForm = this.model.CustomerInfo.TaxpayerApplicationForms[0]
        } else {
          this.model.TaxpayerApplicationForm = ''
        }
        if (this.model.CustomerInfo.BankName) {
          this.BankList = [{ title: this.model.CustomerInfo.BankName, value: this.model.CustomerInfo.BankName }]
        }
        if (this.model.CustomerInfo.BankCardNumber) {
          this.BankCardNumberList = [
            { title: this.model.CustomerInfo.BankCardNumber, value: this.model.CustomerInfo.BankCardNumber },
          ]
        }
      }

      this.setCustomerInvoiceInfo()
      this.$set(this, 'Details', [])
      this.$set(this.$refs.tableGoods, 'dataSource', [])
    },
    // 自动带出客户注册时填写的开票信息，普通发票不显示申请表信息
    setCustomerInvoiceInfo() {
      // console.log(' CustomerInfo ',this.model.CustomerInfo)
      if (this.model.CustomerId && this.model.CustomerInfo) {
        this.$set(this.model, 'CustomerCode', this.model.CustomerInfo.Code || '')
        this.$set(this.model, 'CustomerName', this.model.CustomerInfo.Name || '')
        this.$set(this.model, 'CustomerPinyinCode', this.model.CustomerInfo.PinyinCode || '')
        // if (this.model.InvoiceType) {
        this.$set(this.model, 'Title', this.model.CustomerInfo.Title || '')
        this.$set(this.model, 'TaxNumber', this.model.CustomerInfo.TaxNumber || '')
        this.$set(this.model, 'RegAreaId', this.model.CustomerInfo.RegAreaId || '')
        this.$set(this.model, 'RegAreaCode', this.model.CustomerInfo.ReceivingAreaCode || '')
        this.$set(this.model, 'RegAreaFullName', this.model.CustomerInfo.ReceivingAreaFullName || '')
        this.$set(this.model, 'RegAddress', this.model.CustomerInfo.RegAddress || '')
        this.$set(this.model, 'RegPhone', this.model.CustomerInfo.RegPhone || '')
        this.$set(this.model, 'BankName', this.model.CustomerInfo.BankName || '')
        this.$set(this.model, 'BankCardNumber', this.model.CustomerInfo.BankCardNumber || '')
        this.$set(this.model, 'Email', this.model.CustomerInfo.Email || '')
        this.$set(this.model, 'RecipientEmail', this.model.CustomerInfo.Email || '')
        if (this.model.RegAreaCode) {
          this.areaCodes = this.getDefaultValueByCode(this.model.RegAreaCode)
          this.toggle('IsSelectCityAreas')
        }
        this.$nextTick(() => {
          this.$refs.ruleForm.validateField(['TaxNumber', 'RegPhone', 'RegAreaCode'])
        })
        // }
      }
    },
    toggle(key) {
      this[key] = false
      this.$nextTick(() => {
        this[key] = true
      })
    },
    // 选中 子级选中
    handleSelectedRows(record, selected, selectedRows) {
      let selectedRowKeys = this.$refs.tableGoods.selectedRowKeys || []
      // console.log('selectedRowKeys           ', selectedRowKeys)
      // console.log('record           ', record)
      let Rows = this.$refs.tableGoods.selectedRows || []
      if (selected) {
        if (record && (record['children'] || []).length) {
          let childIds = record.children.map((v) => v.Id)
          selectedRowKeys = (selectedRowKeys || []).concat(childIds)
        }
      } else {
        if (record && (record['children'] || []).length) {
          let childIds = record.children.map((v) => v.Id)
          selectedRowKeys = selectedRowKeys.filter((v) => childIds.indexOf(v) < 0)
        }
      }

      this.$refs.tableGoods.selectedRowKeys = selectedRowKeys
      this.$refs.tableGoods.selectedRows = Rows.filter((v) => v.BillsType == 21)
    },
    // 全选
    onSelectAll(record, selected, selectedRows) {
      // console.log('record           ', record)

      let selectedRowKeys = this.$refs.tableGoods.selectedRowKeys || []
      let Rows = this.$refs.tableGoods.selectedRows || []
      let dataSource = this.$refs.tableGoods.dataSource || []
      let selectedRowsCopy = dataSource.filter((v) => selectedRowKeys.indexOf(v.Id) > -1)
      // console.log('selectedRowKeys           ', selectedRowKeys)
      // console.log('selected           ', selectedRowKeys)
      // console.log('selectedRows           ', selectedRowsCopy)
      if (record) {
        if ((selectedRowsCopy || []).length) {
          selectedRowsCopy.map((v) => {
            if ((v['children'] || []).length) {
              let childIds = v.children.map((v) => v.Id)
              selectedRowKeys = (selectedRowKeys || []).concat(childIds)
            }
          })
        }
      } else {
        selectedRowKeys = []
      }
      // console.log(selectedRowKeys)
      this.$refs.tableGoods.selectedRowKeys = selectedRowKeys
      this.$refs.tableGoods.selectedRows = Rows.filter((v) => v.BillsType == 21)
    },
    initColumnsInvoices() {
      this.columnsInvoices = [
        {
          title: '原票编号',
          dataIndex: 'MakeInvoiceNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票流水号',
          dataIndex: 'MakeInvoiceRunningNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票号',
          dataIndex: 'InvoiceNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票代码',
          dataIndex: 'InvoiceCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票金额',
          dataIndex: 'InvoiceAmount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '开票时间',
          dataIndex: 'InvoiceTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtn: [
            {
              name: '查看发票',
              icon: '',
              id: 'cbdac41d-441b-4583-a229-3a95fcaf0122',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.InvoiceStatus == 3
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ]
    },
    /**
     * 图片选择change事件
     */
    onRadioGroupChange() {
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.clearValidate('TaxpayerApplicationForm')
      }
      this.$forceUpdate()
    },
    /**
     * 获取客户文件
     * @param {* 客户名称} customerName
     */
    getCustomerFiles(customerName) {
      let that = this
      getAction('/{v}/Customer/ListForOrder', { PageIndex: 1, PageSize: 10, KeyWord: customerName }, 'P36002').then(
        (res) => {
          if (res.IsSuccess) {
            if (res.Data && res.Data.length > 0) {
              let customer = res.Data.find((x) => x.BankAccount === customerName)
              if (customer) {
                that.model.CustomerInfo['TaxpayerApplicationForms'] = customer.TaxpayerApplicationForms

                that.$forceUpdate()
              }
            }
          } else {
            console.log(res.Msg)
          }
        }
      )
    },
  },
}
</script>
<style lang="less" scoped>
.form-model-style {
  /deep/.ant-form-item-control {
    min-height: 34px;
  }
}
/* 设置table的fixed行的高度防止错位 */
/deep/.ant-table-fixed tr {
  height: 42px;
}
</style>
