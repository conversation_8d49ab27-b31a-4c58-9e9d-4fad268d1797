<template>
  <div>
    <a-card title="平台开票类型设置：" style="margin-bottom: 12px">
      设置平台需要向金蝶发票云请求开具的发票类型
    </a-card>
    <a-card>
      <a-spin :spinning="spinning">
        <a-form id="components-form-demo-validate-other" layout="inline" :form="model">
          <a-form-item label="普通发票">
            <a-radio-group v-model="model.InvoiceTypeForm_Ordinary" @change="(e)=>change(e,1)">
              <a-radio :value="1">
                数电票
              </a-radio>
              <a-radio :value="2">
                电子发票
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="增值税专用发票">
            <a-radio-group v-model="model.InvoiceTypeForm_Special" @change="(e)=>change(e,2)">
              <a-radio :value="1">
                数电票
              </a-radio>
              <a-radio :value="3">
                纸质发票
              </a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-card>
  </div>
</template>

<script>
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '开票类型设置配置',
  name: 'invoicingTypeConfigList',
  data() {
    return {
      model: {
        InvoiceTypeForm_Ordinary: 1,
        InvoiceTypeForm_Special: 2,
      },
      spinning: false,
      httpHead: 'P36008',
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      this.spinning = true
      let params = {}
      getAction('/{v}/OrderInvoice/InvoiceTypeForm', params, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.model = res.Data || {}
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.spinning = false
        })
    },
    setData(value, type) {
      this.spinning = true
      let params = {
        InvoiceType: type,
        InvoiceTypeForm: value,
      }
      putAction('/{v}/OrderInvoice/SetInvoiceTypeForm', params, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.$message.success('操作成功')
              this.getInfo()
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => {
          this.spinning = false
        })
    },
    change(e, type) {
      this.setData(e.target.value, type)
    },

  },

}
</script>

<style lang="scss" scoped>
.ant-form-inline .ant-form-item {
  display: block;
}
</style>>