<!-- 商销开票列表 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate" />
    <!-- 处理 -->
    <InvalidInvoiceDisposeModal ref="InvalidInvoiceDisposeModal" @ok="modalOk" />
  </a-row>
</template>

<script>
import moment from 'moment'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '待冲红/作废发票',
  name: 'invalidInvoiceList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '开票编号', type: 'input', value: '', key: 'Code', defaultVal: '', placeholder: '请输入' },
        {
          name: '对应订单编号',
          type: 'input',
          value: '',
          key: 'SalesOrderCode',
          defaultVal: '',
          placeholder: '请输入',
        },
        { name: '客户信息', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '请输入' },
        {
          name: '发票类型',
          type: 'selectBasicLink',
          value: '',
          vModel: 'InvoiceType',
          dictCode: 'EnumSaleInvoiceType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '状态',
          type: 'select',
          value: '',
          key: 'IsHandle',
          defaultVal: [
            { title: '已处理', value: 'true' },
            { title: '待处理', value: 'false' },
          ],
          placeholder: '请选择',
        },
        {
          name: '发生时间',
          type: 'timeInput',
          value: '',
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
          rangeDate:[],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: 1,
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '开票编号',
          dataIndex: 'Code',
          width: 250,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'SalesOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 220,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票类型',
          dataIndex: 'InvoiceTypeV2Str',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票金额',
          dataIndex: 'OriginalInvoiceAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '待处理金额',
          dataIndex: 'InvoiceAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '发生时间',
          dataIndex: 'ApplyTime',
          width: 180,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '状态',
          dataIndex: 'IsHandleStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          actionBtn: [
            {
              name: '处理',
              icon: '',
              id: 'b87d013a-2dcb-408d-b11d-5b9c7f7baac1',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.IsShowHandle === true
              },
            },
            {
              name: '详情',
              icon: '',
              id: '05aa48e9-f93e-4fdb-b3b6-08ad72f35930',
              specialShowFuc: (e) => {
                return true
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: true, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/OrderInvoice/WaitFlushedRedAndCancelList',
      },
    }
  },
  created() { },
  mounted() {

  },
  methods: {
    moment,
    modalOk() {
      let params = {
        ...this.queryParam,
        ...this.$refs.SimpleSearchArea.queryParam,
      }
      this.$refs.table.loadDatas(null, params)
    },
    operate(record, type) {
      const that = this
      switch (type) {
        case '处理':
          this.$refs.InvalidInvoiceDisposeModal.show(record)
          break
        case '详情':
          this.onDetailClick('invalidInvoiceDetail', { id: record.Id })
          break
      }
    },
  },
}
</script>

<style></style>
