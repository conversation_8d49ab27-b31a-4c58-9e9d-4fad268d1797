<!-- 申请售后 -->
<template>
  <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical">
    <a-row :gutter="gutter">
      <a-col :span="md">
        <a-form-model-item label="售后范围：" prop="SaleAfterScope">
          <EnumSingleChoiceView style="width: 100%" placeholder="请选择" dictCode="EnumSaleAfterScope" v-model="model.SaleAfterScope" :disabled="Info?true:false" tagType="radio" @change="handleSaleAfterScope" :notOption="setSaleAfterScope" />
        </a-form-model-item>
      </a-col>
      <a-col :span="md">
        <a-form-model-item label="售后类型：" prop="SaleAfterType">
          <EnumSingleChoiceView style="width: 100%" placeholder="请选择" dictCode="EnumSaleAfterType" v-model="model.SaleAfterType" tagType="radio" @change="toggleIsRefundReason" />
        </a-form-model-item>
      </a-col>
      <a-col :span="md">
        <a-form-model-item label="售后原因：" prop="RefundReason" class="RefundReasonCheckbox">
          <!-- TKYYZTHTK 退款原因之退货退款 -->
          <!-- TKYYZJTK 退款原因之仅退款 -->
          <MultipleChoiceView style="width: 100%" placeholder="请选择" :httpParams="{
              groupPY: model.SaleAfterType == 1 ? 'TKYYZTHTK' : 'TKYYZJTK',
            }" httpHead="P36001" :dataKey="{ name: 'ItemValue', value: 'ItemValue' }" v-model="model.RefundReason" :Url="linkUrl.getListDictItem" tagType="checkbox" :key="model.SaleAfterType" />
        </a-form-model-item>
      </a-col>
      <a-col :span="md">
        <SimpleTable ref="table" :tab="tab" :columns="columns" v-if="model.SaleAfterScope != 1">
          <span slot="commonContent" slot-scope="{ text }">
            <j-ellipsis :value="text == 0 || text ? String('￥'+text) : '--'" />
          </span>
          <span slot="SaleAfterCount" slot-scope="{ record, index }">
            <a-input-number placeholder="请输入" v-model="record.SaleAfterCount" :min="0" :max="999999" :precision="0" style="width: 100%" @blur="(e) => onGoodCountBlur('Count', e, record, index)" />
          </span>
        </SimpleTable>
        <p style="margin-top: 10px" v-if="model.SaleAfterScope">
          售后金额：{{(model.TotalAmount || model.TotalAmount == 0)?'¥'+Number(model.TotalAmount):'--'}}
        </p>
      </a-col>
      <a-col :span="md">
        <a-form-model-item label="售后方式：" prop="SaleAfterRefundType">
          <EnumSingleChoiceView style="width: 100%" placeholder="请选择" dictCode="EnumSaleAfterRefundType" v-model="model.SaleAfterRefundType" tagType="radio" :notOption="model.SaleAfterType == 0 ? [2, 10] : [10]" />
        </a-form-model-item>
      </a-col>
      <a-col :span="md">
        <a-form-model-item label="备注：" prop="Remarks">
          <a-textarea v-model="model.Remarks" style="width: 100%" placeholder="请输入" :auto-size="{ minRows: 2, maxRows: 4 }" :maxLength="40" />
        </a-form-model-item>
      </a-col>
    </a-row>
  </a-form-model>
</template>
<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'ApplyAfterSalesServiceCom',
  components: { JEllipsis },
  mixins: [SimpleMixin, EditMixin],
  props: {
    OrderId: {
      type: String,
      default: '',
    },
    recordCount: {
      type: Number,
      default: 0,
    },
    // TotalAmount: {
    //   type: Number,
    //   default: null,
    // },
    Info: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      confirmLoading: false,
      visible: false,
      md: 24,
      TotalAmount: null,
      model: {
        SaleAfterType: '',
        SaleAfterScope: '',
        SaleAfterRefundType: '',
        RefundReason: [],
        Remarks: '',
        SaleAfterOrderGoodses: [],
        TotalAmount: null,
      },
      tab: { hideIpagination: true, sortArray: ['SaleAfterCount'], bordered: true },
      // 表头
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 200,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
            return obj;
          },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 120,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
            return obj;
          },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 120,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
            return obj;
          },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 120,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
            return obj;
          },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
            return obj;
          },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 120,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
            return obj;
          },
        },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber', // BatchNo
          ellipsis: true,
          width: 120,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
            return obj;
          },
        },
        {
          title: '批次号',
          dataIndex: 'BatchNo',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '仓库',
          dataIndex: 'WarehouseCode',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产日期',
          dataIndex: 'ProductionDate',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'dayTime' },
          // customRender: (text, record, index) => {
          //   const obj = {
          //     children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
          //     attrs: {},
          //   };
          //   obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
          //   return obj;
          // },
        },
        {
          title: '有效期',
          dataIndex: 'GoodsPeriod',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'dayTime' },
          // customRender: (text, record, index) => {
          //   const obj = {
          //     children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
          //     attrs: {},
          //   };
          //   obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
          //   return obj;
          // },
        },
        // {
        //   title: '是否赠品',
        //   dataIndex: 'IsGift',
        //   ellipsis: true,
        //   width: 100,
        //   // customRender: (t, r, i) => {
        //   //   return t == null ? '' : t ? '是' : '否'
        //   // },
        //   customRender: (text, record, index) => {
        //     const obj = {
        //       children: (<span>{text == null ? '' : text ? '是' : '否'}</span>),
        //       attrs: {},
        //     };
        //     obj.attrs.rowSpan = record.rowSpan
        //     return obj;
        //   },
        // },
        // {
        //   title: '进价',
        //   dataIndex: 'GoodsPurchaseCostPrice',
        //   ellipsis: true,
        //   width: 100,
        //   // scopedSlots: { customRender: 'commonSlot' },
        //   customRender: (text, record, index) => {
        //     const obj = {
        //       children: <j-ellipsis value={text == 0 || text ? String('￥' + text) : ''} />,
        //       attrs: {},
        //     };
        //     obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
        //     return obj;
        //   },
        // },
        {
          title: '销售单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
          // customRender: (text, record, index) => {
          //   const obj = {
          //     children: <j-ellipsis value={text == 0 || text ? String('￥' + text) : ''} />,
          //     attrs: {},
          //   };
          //   obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
          //   return obj;
          // },
        },

        {
          title: '出库数量',
          dataIndex: 'OutboundCount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : ''} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanBatchNo
            return obj;
          },
        },
        {
          title: '调价后单价',
          dataIndex: 'AdjustPrice',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
          // customRender: (text, record, index) => {
          //   const obj = {
          //     children: <span>{text}</span>,
          //     attrs: {},
          //   }

          //   obj.attrs.rowSpan = record.rowSpan || 1

          //   if (record.rowSpan == 0) {
          //     obj.attrs.colSpan = 0
          //   }
          //   return obj
          // },
        },
        {
          title: '已退款数量',
          dataIndex: 'ExsitSaleAfterCount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '可退款数量',
          dataIndex: 'CanSaleAfterCount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退货数量',
          dataIndex: 'SaleAfterCount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'SaleAfterCount' },
        },
      ],
      queryParam: {
        salesOrderId: this.OrderId,
      },
      SaleAfterOrderGoodses: [],
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        getListDictItem: '/{v}/Global/GetListDictItem',
        list: '/{v}/SaleAfterOrder/GetGoodsForCreateSaleAfterOrderV15',
        createSaleAfterOrder: '/{v}/SaleAfterOrder/CreateSaleAfterOrderV15',
        getComputeSaleAfterAmount: '/v1/SaleAfterOrder/ComputeSaleAfterAmount',
      },
    }
  },
  computed: {
    rules() {
      return {
        SaleAfterType: [{ required: true, message: '请选择!' }],
        SaleAfterScope: [{ required: true, message: '请选择!' }],
        SaleAfterRefundType: [{ required: true, message: '请选择!' }],
        RefundReason: [{ required: true, message: '请选择!' }],
      }
    },
    setSaleAfterScope() {
      if (this.Info) {
        return []
      } else {
        return this.recordCount > 0 ? [1] : []
      }
    },
  },
  mounted() {
    if (this.Info) {
      // console.log(this.Info)
      this.model.SaleAfterType = this.Info['SaleAfterType'] == null ? '' : '' + this.Info['SaleAfterType']
      this.model.SaleAfterScope = this.Info['SaleAfterScope'] == null ? '' : '' + this.Info['SaleAfterScope']
      this.model.SaleAfterRefundType =
        this.Info['SaleAfterRefundType'] == null ? '' : '' + this.Info['SaleAfterRefundType']
      this.model.RefundReason = this.Info['RefundReason'] || []
      this.model.Remarks = this.Info['Remarks'] || ''
      this.model.SaleAfterOrderGoodses = this.Info['SaleAfterOrderGoodses'] || []
      this.$set(this, 'SaleAfterOrderGoodses', this.model.SaleAfterOrderGoodses)
      this.$set(this.$refs.table, 'dataSource', this.model.SaleAfterOrderGoodses)
      // 编辑和详情的时候
      this.$set(this.model, 'TotalAmount', Number(this.Info.RefundAmount || 0).toFixed(2))
    }
  },
  watch: {
    SaleAfterOrderGoodses: {
      deep: true,
      handler: function (newForm, oldForm) {
        if (newForm.length > 0 && this.model.SaleAfterScope != 1) {
          // 【调价后单价*退货数量】计算
          let TotalAmount = 0
          newForm.map((item) => {
            if ((item['AdjustPrice'] == 0 || item['AdjustPrice']) && item['SaleAfterCount']) {
              let amount = item['AdjustPrice'] * item['SaleAfterCount']
              TotalAmount += this.getAmountOfMoney(amount) * 1
            } else if (item['Price'] && item['SaleAfterCount']) {
              let amount = item['Price'] * item['SaleAfterCount']
              TotalAmount += this.getAmountOfMoney(amount) * 1
            }
          })
          this.$set(this.model, 'TotalAmount', Number(TotalAmount).toFixed(2))
        }
      },
    },
  },
  created() { },
  methods: {
    handleSaleAfterScope() {
      if (this.model.SaleAfterScope == 1) {
        this.getComputeSaleAfterAmount()
      } else {
        // this.$set(this.model, 'TotalAmount', 0)
        this.loadDetailInfo()
      }
    },
    /**
     * 获取售后金额
     */
    getComputeSaleAfterAmount() {
      let that = this
      that.loading = true
      getAction(that.linkUrl.getComputeSaleAfterAmount, this.queryParam, that.linkHttpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$set(that, 'TotalAmount', res.Data || 0)
            that.$set(that.model, 'TotalAmount', that.TotalAmount || 0)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    /**
     * 获取商品信息
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.linkUrl.list, this.queryParam, that.linkHttpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if ((res.Data || []).length) {
              res.Data.map((v) => {
                v.SaleAfterCount = null
              })
            }

            // 列合并处理数据
            let arr = this.getListRowSpanData(res.Data || [], '', 'ErpGoodsCode', 'rowSpan') || []
            arr.map(item => {
              item['ErpGoodsCodeAndGoodsBatchNumber'] = (item.ErpGoodsCode || '') + (item.GoodsBatchNumber || '')
            })
            let arr1 = this.getListRowSpanData(arr, 'ErpGoodsCode', 'ErpGoodsCodeAndGoodsBatchNumber', 'rowSpanGoodsBatchNumber', 'GoodsBatchNumber') || []

            // let arr1 = this.getListRowSpanData(arr, 'ErpGoodsCode', 'GoodsBatchNumber', 'rowSpanGoodsBatchNumber','GoodsBatchNumber') || []
            // let arr2 = this.getListRowSpanData(arr1, 'GoodsBatchNumber', 'BatchNo', 'rowSpanBatchNo', 'BatchNo')
            // 重组数据
            that.$set(that, 'SaleAfterOrderGoodses', arr1 || [])
            that.$set(that.$refs.table, 'dataSource', that.SaleAfterOrderGoodses)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    getSameNum(Arr, call) {
      //排序
      let key = 'ErpGoodsId'
      let arr = Arr.sort((a, b) => {
        if (a[key] === b[key]) {
          return 0
        } else if (a[key] < b[key]) {
          return -1
        } else {
          return 1
        }
      })

      // console.log('arr', arr)
      // key的数组
      let sortArray = arr.map((item) => {
        return item[key]
      })
      let duplicates = sortArray.filter((val, i) => sortArray.indexOf(val) !== i)
      // 相同key的数组
      let sameArray = Array.from(new Set(duplicates.length > 1 ? duplicates : []))
      // console.log(sameArray)

      let dataArray = []
      sameArray.map((item) => {
        dataArray.push({
          key: item,
          num: sortArray.filter((e) => e === item).length, //相同元素出现的次数
          index: sortArray.indexOf(item), //相同元素首次出现的位置
          lasiIndex: sortArray.lastIndexOf(item), //相同元素末次出现的位置
        })
      })
      arr.map((item, index) => {
        dataArray.map((rItem) => {
          if (item[key] === rItem.key) {
            if (index === rItem.index) {
              item.rowSpan = rItem.num
            } else {
              item.rowSpan = 0
            }
          }
        })
      })
      call && call(arr)
      // console.log('dataArray', dataArray, arr)
    },
    toggleIsRefundReason() {
      this.model.RefundReason = []
    },
    onSubmit() {
      let returnValid = false

      this.$refs.ruleForm.validate((valid) => {
        returnValid = valid
      })
      // 部分退款
      if (this.model.SaleAfterScope == 0 && (this.SaleAfterOrderGoodses || []).length < 1) {
        this.$message.warning('商品信息有误!')
        return (returnValid = false)
      }
      if (this.model.SaleAfterScope == 0 && !(this.SaleAfterOrderGoodses || []).some((v) => v['SaleAfterCount'])) {
        this.$message.warning('请填写商品退货数量!')
        return (returnValid = false)
      }
      if (!returnValid) {
        return returnValid
      }
      return this.setFormData()
    },
    setFormData() {
      // return
      let formData = JSON.parse(JSON.stringify(this.model))
      if (this.model.SaleAfterScope == 0 && (this.SaleAfterOrderGoodses || []).length > 0) {
        formData['SaleAfterOrderGoodses'] = []
        this.SaleAfterOrderGoodses.map((v) => {
          formData['SaleAfterOrderGoodses'].push({
            SalesOrderWarehouseOutboundBillsDetailId: v['Id'],
            SaleAfterCount: v['SaleAfterCount'],
            AdjustPrice: v['AdjustPrice'],
            BatchNo: v.BatchNo,
            WarehouseCode: v.WarehouseCode,
            SalesOrderDetailId: v.SalesOrderDetailId,
            SalesOrderDetailResultId: v.SalesOrderDetailResultId
          })
        })
      } else {
        delete formData['SaleAfterOrderGoodses']
      }
      formData['OrderId'] = this.OrderId
      formData['SaleAfterType'] = formData['SaleAfterType'] == '' ? null : Number(formData['SaleAfterType'])
      formData['SaleAfterScope'] = formData['SaleAfterScope'] == '' ? null : Number(formData['SaleAfterScope'])
      formData['SaleAfterRefundType'] =
        formData['SaleAfterRefundType'] == '' ? null : Number(formData['SaleAfterRefundType'])
      return formData
    },
    // 退货数量 填写退货数量不能大于可退款数量
    onGoodCountBlur(key, e, record, index) {
      if ((record.SaleAfterCount && record.SaleAfterCount) > (record.CanSaleAfterCount || 0)) {
        this.$message.warning('填写的退货数量不能大于可退款数量')
        record.SaleAfterCount = null
        return
      }

      // this.$set(this.$refs.table.dataSource, index, record)
      this.$set(this, 'SaleAfterOrderGoodses', this.$refs.table.dataSource)
    },
  },
}
</script>
<style lang="less" scoped>
.RefundReasonCheckbox {
  /deep/.ant-checkbox {
    display: none;
  }
  /deep/.ant-checkbox-wrapper {
    border: 1px solid #e5e5e5;
    padding: 5px 10px;
    border-radius: 5px;
    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }
  /deep/.ant-checkbox-wrapper-checked {
    border-color: #40a9ff;
    color: #40a9ff;
  }
}
</style>
