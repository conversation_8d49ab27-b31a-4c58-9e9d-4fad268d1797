<!-- 开票详情 开票信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card :bordered="false" :body-style="{ padding: 0 }">
        <!-- 发票信息 -->
        <a-descriptions :title="model['InvoiceApplyType']==1?'发票信息':'开票信息'" :column="1">
          <a-descriptions-item label="公司名称">{{ model.Title || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="发票类型">{{ model.InvoiceTypeV2Str || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="增值税一般纳税人申请表" v-if="model.InvoiceTypeV2 != 1">
            <ImageControl :src="model.TaxpayerApplicationForm || ''" :width="100"></ImageControl>
          </a-descriptions-item>
          <a-descriptions-item label="纳税人识别号">{{ model.TaxNumber || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="注册地址">{{ model.RegAddress || ' -- ' }}</a-descriptions-item>
          <!-- {{ model.RegAreaFullName || ' -- ' }} -->
          <a-descriptions-item label="注册电话">{{ model.RegLinkPhone || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="开户银行">{{ model.BankName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="银行账户">{{ model.BankCardNumber || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item v-if="source !== 'dczf'" label="电子邮箱">{{ model.RecipientEmail || ' -- ' }}</a-descriptions-item>
        </a-descriptions>

        <!-- 单据信息 -->
        <a-row :gutter="20" v-if="source != 'dczf'" style="margin-bottom: 24px">
          <a-col :span="24">
            <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">单据信息</div>
          </a-col>
          <a-col :span="24">
            <!-- 列表 -->
            <SimpleTable ref="tableGoods" :tab="tabGoods" :columns="columnsGoods" @operate="operate">
              <span slot="commonContent" slot-scope="{ text }">
                <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
              </span>
              <span slot="OrderNo" slot-scope="{ text }">
                <a @click="onDetailClick('/pages/marketingManagement/ordersManagementListDetail', { code: text })">
                  <j-ellipsis :value="text" :length="15" /></a>
              </span>
              <span slot="BillsAmount" slot-scope="{ text, record }">
                <j-ellipsis :value="`${
                    record.BillsType == 22 ? '￥-' + Number(text).toFixed(2) : '￥' + Number(text).toFixed(2)
                  }`" :length="15" />
              </span>
            </SimpleTable>
          </a-col>
          <!-- <a-col :span="24" v-if="model['InvoiceApplyType'] != 1">
            <span style="margin-right: 10px">合计开票金额：<span style="color: red; font-size: 30px">¥{{ model.InvoiceAmount || 0 }}</span>
            </span>
          </a-col> -->
        </a-row>

        <template v-if="model['InvoiceApplyType'] != 1">
          <!-- 原发票信息 -->
          <a-row :gutter="20" style="margin-bottom: 24px">
            <a-col :span="24">
              <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">原发票信息</div>
            </a-col>
            <a-col :span="24">
              <!-- 列表 -->
              <SimpleTable ref="tableOld" :tab="tabOld" :columns="columnsOld" :tableDatas="invoicesList" @operate="operate">
                <span slot="commonContent" slot-scope="{ text }">
                  <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                </span>
              </SimpleTable>
            </a-col>
          </a-row>
          <!-- <a-descriptions title="" v-if="source != 'dczf'" :column="1">
            <a-descriptions-item label="退税证明" v-if="model.InvoiceApplyType == 2">
              <ImageControl :src="model.DebentureUrl || ''" :width="100"></ImageControl>
            </a-descriptions-item>
            <a-descriptions-item label="红字信息表" v-if="model.InvoiceApplyType == 2">
              <ImageControl :src="model.RedLetterFormUrl || ''" :width="100"></ImageControl>
            </a-descriptions-item>
            <a-descriptions-item label="作废证明" v-if="model.InvoiceApplyType == 3">
              <ImageControl :src="model.CancelCertificate || ''" :width="100"></ImageControl>
            </a-descriptions-item>
          </a-descriptions> -->
        </template>
        <!-- 开票信息  已开票 v-if="model['InvoiceStatus'] == 3"  作废信息-->
        <a-row :gutter="20" style="margin-bottom: 24px" v-if="source != 'dczf'">
          <a-col :span="24">
            <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">
              {{ table3Str }}
            </div>
          </a-col>
          <a-col :span="24">
            <!-- 列表 -->
            <SimpleTable ref="tableResults" :tab="tab" :columns="columns" @operate="operate">
              <span slot="commonContent" slot-scope="{ text }">
                <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
              </span>
            </SimpleTable>
          </a-col>
        </a-row>
        <!-- 待处理单据 -->
        <a-row :gutter="20" v-if="source == 'dczf'" style="margin-bottom: 24px">
          <a-col :span="24">
            <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">待处理单据</div>
          </a-col>
          <a-col :span="24">
            <!-- 列表 -->
            <SimpleTable ref="tableGoodsDCL" :tab="tabGoods" :columns="columnsGoods" @operate="operate">
              <span slot="commonContent" slot-scope="{ text }">
                <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
              </span>
              <!-- 对应订单编号 -->
              <span slot="OrderNo" slot-scope="{ text }">
                <a @click="onDetailClick('/pages/marketingManagement/ordersManagementListDetail', { code: text })">
                  <j-ellipsis :value="text" :length="15" /></a>
              </span>
              <span slot="BillsAmount" slot-scope="{ text, record }">
                <j-ellipsis :value="`${
                    record.BillsType == 22 ? '￥-' + Number(text).toFixed(2) : '￥' + Number(text).toFixed(2)
                  }`" :length="15" />
              </span>
            </SimpleTable>
          </a-col>
        </a-row>
        <!-- 处理结果 -->
        <a-row :gutter="20" style="margin-bottom: 24px" v-if="source == 'dczf'">
          <a-col :span="24">
            <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">处理结果</div>
          </a-col>
          <a-col :span="24">
            <!-- 列表 -->
            <SimpleTable ref="columnsDispose" :columns="columnsDispose" @operate="operate"></SimpleTable>
          </a-col>
        </a-row>

      </a-card>
    </a-spin>
    <!-- 商品明细 -->
    <CommodityDetailListModal ref="iCommodityDetailListModal" />
  </div>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { ListMixin } from '@/mixins/ListMixin'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '开票详情 开票信息',
  name: 'BasicInfoTemplateInvoiceDetail',
  mixins: [ListMixin, SimpleMixin],
  components: { JEllipsis },
  props: {
    Info: {
      type: Object,
      default: () => null,
    },
    // dczf 待冲红/作废详情
    source: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      loading: false,
      md: 8,
      model: {

      },
      table3Str: '',
      tabGoods: {
        rowKey: 'Id',
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        operateBtn: [],
        hideIpagination: true,
        sortArray: ['OrderNo', 'BillsAmount'],
      },
      columnsGoods: [
        {
          title: '单据编号',
          dataIndex: 'BillsNo',
          ellipsis: true,
          width: 180,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据类型',
          dataIndex: 'BillsTypeStr',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'OrderNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'OrderNo' },
        },
        {
          title: '订单付款方式',
          dataIndex: 'PaymentModeStr',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BillsTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '金额',
          dataIndex: 'BillsAmount',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'BillsAmount' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          fixed: 'right',
          actionBtn: [
            {
              name: '商品明细',
              icon: '',
              id: '7e0aed05-238a-4922-a577-e42dc2e49254',
              specialShowFuc: (e) => {
                let record = e || {}
                return ![0, 3].includes(record.AuditStatus)
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      tabOld: {
        hideIpagination: true,
      },
      columnsOld: [
        {
          title: '原票编号',
          dataIndex: 'MakeInvoiceNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票流水号',
          dataIndex: 'MakeInvoiceRunningNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票号',
          dataIndex: 'InvoiceNo',
          ellipsis: true,
          width: 180,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票代码',
          dataIndex: 'InvoiceCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票金额',
          dataIndex: 'InvoiceAmount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '开票时间',
          dataIndex: 'InvoiceTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          fixed: 'right',
          actionBtn: [
            {
              name: '查看发票',
              icon: '',
              specialShowFuc: (e) => {
                let record = e || {}
                return (record.InvoiceFileUrl && record.InvoiceFileUrl.indexOf('http') > -1) ? true : false
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      tab: {
        hideIpagination: true,
      },
      columns: [],
      columnsTable3LP: [
        {
          title: '开票流水号',
          dataIndex: 'MakeInvoiceRunningNo',
          ellipsis: true,
          width: 160,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票类型',
          dataIndex: 'InvoiceCenterTypeStr',
          ellipsis: true,
          width: 160,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票号',
          dataIndex: 'InvoiceNo',
          ellipsis: true,
          width: 180,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票代码',
          dataIndex: 'InvoiceCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票金额',
          dataIndex: 'InvoiceAmount',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '开票时间',
          dataIndex: 'InvoiceTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          fixed: 'right',
          actionBtn: [
            {
              name: '查看发票',
              icon: '',
              specialShowFuc: (e) => {
                let record = e || {}
                return (record.InvoiceFileUrl && record.InvoiceFileUrl.indexOf('http') > -1) ? true : false
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      columnsTable3CH: [
        {
          title: '开票流水号',
          dataIndex: 'MakeInvoiceRunningNo',
          ellipsis: true,
          width: 160,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票号',
          dataIndex: 'InvoiceNo',
          ellipsis: true,
          width: 180,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票类型',
          dataIndex: 'InvoiceCenterTypeStr',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '冲红金额',
          dataIndex: 'InvoiceAmount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '冲红时间',
          dataIndex: 'InvoiceTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          fixed: 'right',
          actionBtn: [
            {
              name: '查看发票',
              icon: '',
              specialShowFuc: (e) => {
                let record = e || {}
                return (record.InvoiceFileUrl && record.InvoiceFileUrl.indexOf('http') > -1) ? true : false
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      columnsTable3ZF: [
        {
          title: '发票号',
          dataIndex: 'InvoiceNo',
          ellipsis: true,
          width: 180,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票金额',
          dataIndex: 'InvoiceAmount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '发票类型',
          dataIndex: 'InvoiceCenterTypeStr',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '作废时间',
          dataIndex: 'InvoiceTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '作废金额',
          dataIndex: 'CancelAmount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
      ],
      columnsDispose: [
        {
          title: '处理时间',
          dataIndex: 'HandleTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'HandleRemark',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
      ],
      httpHead: 'P36008',
      invoicesList: [],
      url: {},
    }
  },
  computed: {},
  created() { },
  mounted() {
    if (this.Info) {
      console.log('source', this.source)
      this.setColumns()

      this.model = Object.assign({}, this.Info)
      if ((this.model.Details || []).length) {
        let goods = this.setBillsData(this.model.Details) || []
        this.$refs.tableGoods && this.$set(this.$refs.tableGoods, 'dataSource', this.setTableData(goods))
        this.$refs.tableGoods && this.$set(this.$refs.tableGoods, 'expandedRowKeys', goods.length > 0 ? [goods[0].Id] : [])
      }
      this.$nextTick(() => {
        if (this.$refs.tableResults) this.$set(this.$refs.tableResults, 'dataSource', this.model.Results || [])
      })

      this.invoicesList = []
      if (this.model.OriginalInvoices && this.model.OriginalInvoices.length > 0) {
        this.model.OriginalInvoices.forEach((x) => {
          if (x.Results && x.Results.length > 0) {
            this.invoicesList = this.invoicesList.concat(x.Results)
          }
        })
      } else if (this.model.Results && this.model.Results.length > 0) {
        this.invoicesList = [].concat(this.model.Results)
      }

      // 待处理单据
      let goods = []
      // if (this.model.OriginalInvoices && this.model.OriginalInvoices.length > 0) {
      //   this.model.OriginalInvoices.forEach((x) => {
      //     if (x.Details && x.Details.length > 0) {
      //       goods = goods.concat(x.Details)
      //     }
      //   })
      // }
      // goods = this.setBillsData(goods) || []
      goods = this.model.InvoiceDataSourceBusinessNoDetail ? [{
        ...this.model.InvoiceDataSourceBusinessNoDetail,
        BillsNo:this.model.InvoiceDataSourceBusinessNoDetail.BillsCode,
        OrderNo:this.model.InvoiceDataSourceBusinessNoDetail.SalesOrderCode,
        PaymentModeStr:this.model.InvoiceDataSourceBusinessNoDetail.SalesOrderPaymentModeStr,
      }] : []
      console.log('待处理单据 goods ', goods)
      this.$refs.tableGoodsDCL && this.$set(this.$refs.tableGoodsDCL, 'dataSource', goods)
      // this.$refs.tableGoodsDCL && this.$set(this.$refs.tableGoodsDCL, 'expandedRowKeys', goods.length > 0 ? [goods[0].Id] : [])
      // 原发票信息
      this.$refs.tableOld && this.$set(this.$refs.tableOld, 'dataSource', this.invoicesList)
      // 处理结果
      if (this.source == 'dczf') {
        let disposeArray = [
          {
            HandleTime: this.model.HandleTime,
            HandleRemark: this.model.HandleRemark,
          },
        ]
        this.$refs.columnsDispose && this.$set(this.$refs.columnsDispose, 'dataSource', disposeArray)
      }
    }
  },
  methods: {
    setBillsData(data) {
      let goods = []
      data.map((v) => {
        let item = {
          Id: v['Id'],
          BillsNo: v['BillsCode'],
          BillsType: v['BillsType'],
          BillsTypeStr: v['BillsTypeStr'],
          OrderId: v['OrderId'],
          OrderNo: v['SalesOrderCode'],
          PaymentMode: v['SalesOrderPaymentMode'],
          PaymentModeStr: v['SalesOrderPaymentModeStr'],
          BillsTime: v['BillsTime'],
          BillsAmount: v['BillsAmount'],
          children: [],
          ParentCode: v['ParentCode'],
        }
        let childs =
          data.filter(
            (j) => j['SalesOrderCode'] == item['OrderNo'] && j['ParentCode'] == v['BillsCode'] && j.BillsType != 21
          ) || []
        childs.map((j) => {
          item['children'].push({
            Id: j['Id'],
            BillsNo: j['BillsCode'],
            BillsType: j['BillsType'],
            BillsTypeStr: j['BillsTypeStr'],
            OrderId: j['OrderId'],
            OrderNo: j['SalesOrderCode'],
            PaymentMode: j['SalesOrderPaymentMode'],
            PaymentModeStr: j['SalesOrderPaymentModeStr'],
            BillsTime: j['BillsTime'],
            BillsAmount: j['BillsAmount'],
            ParentCode: j['ParentCode'],
          })
        })

        if ((item.children || []).length < 1) {
          item.children = null
        }
        if (item.BillsType == 21) {
          if ((item.children || []).length >= 1) item['children'].sort(this.sortUpDate) //反序
          goods.push(item)
        } else if (!item.ParentCode) {
          goods.push(item)
        }
      })
      return goods
    },
    sortUpDate(a, b) {
      return Date.parse(b.BillsTime) - Date.parse(a.BillsTime)
    },
    // 递归重组数据
    setTableData(data) {
      if (data == null || data.length <= 0) {
        return []
      }
      let tempArr = []
      try {
        data.forEach((item) => {
          var temp = item
          if (item.Childrens && item.Childrens.length > 0) {
            temp['children'] = this.setTableData(item.Childrens)
          }
          tempArr.push(temp)
        })
      } catch (e) {
        console.log(e)
      }
      return tempArr
    },
    setColumns() {
      switch (this.Info.InvoiceApplyType) {
        case 1:
          this.table3Str = '开票信息'
          this.columns = this.columnsTable3LP
          break
        case 2:
          this.table3Str = '冲红信息'
          this.columns = this.columnsTable3CH
          break
        case 3:
          this.table3Str = '作废信息'
          this.columns = this.columnsTable3ZF
          break
      }
      if (this.source == 'dczf') {
        let operateIndex = this.columnsOld.findIndex(item => {
          return item.title == '操作'
        })
        if (operateIndex >= 0) {
          this.columnsOld.splice(operateIndex, 1)
        }
      }
    },
    operate(record, type) {
      if (type == '商品明细') {
        this.$refs.iCommodityDetailListModal.show(record)
      } else if (type == '查看发票') {
        if (record['InvoiceFileUrl'] && record['InvoiceFileUrl'].indexOf('http') > -1) {
          window.open(record['InvoiceFileUrl'])

          // this.getFileTempUrl(record['InvoiceFileUrl'], (data) => {
          //   console.log(data)
          //   if (data && typeof (data) == 'string') {
          //     window.open(data)
          //   } else if (data && data.TempUrl) {
          //     window.open(data.TempUrl)
          //   }
          // })
        } else if (record['InvoiceFileUrl']) {
          this.$message.warning('文件地址不正确')
        } else {
          this.$message.warning('暂无文件')
        }
      }
    },
  },
}
</script>

<style lang="less" scoped></style>
