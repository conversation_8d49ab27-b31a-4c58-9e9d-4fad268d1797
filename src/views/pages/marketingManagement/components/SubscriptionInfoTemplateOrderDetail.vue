
<!-- 商销商品订单详情 认款信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card :bordered="false" :body-style="{padding:0}">
        <a-collapse v-model="activeKey" @change="changeCollapse" v-if="model.SalesOrderRecognitionAsync.length > 0">
          <a-collapse-panel :key="i.toString()" :header="item.Title" v-for="(item,i) in model.SalesOrderRecognitionAsync">
            <a slot="extra">{{activeKey.some(v=>v == i) ? '收起' : '展开'}}</a>
            <div v-if="item.type == 1">
              <!-- 现金认款 -->
              <a-descriptions title="" :column="1">
                <a-descriptions-item label="认款状态">{{ item.AcceptanceStatusStr || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="认款金额">￥{{ item.RecognitionAmount ||0 }}</a-descriptions-item>
                <a-descriptions-item label="银行名称">{{ item.BankName || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="银行卡号">{{ item.AccountNumber || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="打款方">{{ item.Payer || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="打款时间">{{ item.ReceiptTime || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="认款时间">{{ item.RecognitionTime || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="备注">{{ item.Note || ' -- ' }}</a-descriptions-item>
              </a-descriptions>
            </div>
            <div v-if="item.type == 2">
              <!-- 销货对抵认款 -->
              <a-descriptions title="" :column="1">
                <a-descriptions-item label="认款状态">{{ item.AcceptanceStatusStr || ' -- ' }}</a-descriptions-item>
                <!-- <a-descriptions-item label="收款方式">{{ item.ErpGoodsName || ' -- ' }}</a-descriptions-item> -->
                <a-descriptions-item label="销货对抵金额">￥{{ item.AcceptanceAmount ||0 }}</a-descriptions-item>
                <a-descriptions-item label="销货对抵时间">{{ item.AcceptanceTime || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="备注">{{ item.Note || ' -- ' }}</a-descriptions-item>
              </a-descriptions>
            </div>
          </a-collapse-panel>
        </a-collapse>
        <a-empty v-else />
      </a-card>

    </a-spin>

  </div>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销商品订单详情 认款信息',
  name: 'SubscriptionInfoTemplateOrderDetail',
  mixins: [SimpleMixin],
  components: {},
  props: {
    Info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      loading: false,
      md: 8,
      model: {
        SalesOrderRecognitionAsync: []
      },
      activeKey: [0],
      httpHead: 'P36012',
      url: {
        getSalesOrderRecognitionAsync: '/{v}/Recognition/GetSalesOrderRecognitionAsync'
      }
    }
  },
  computed: {},
  created() { },
  mounted() {
    if (this.Info) {
      this.model = {
        Id: this.Info.Id,
        SalesOrderRecognitionAsync: [],
        Code: this.Info.Code,
      };
      this.loadDetailInfo()
    }
  },
  methods: {
    changeCollapse(e) {

    },
    loadDetailInfo() {
      if (!this.model.Id) return
      getAction(this.url.getSalesOrderRecognitionAsync, { salesOrderNo: this.model.Code }, this.httpHead).then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        // 重组数据排序
        let array = []
        res.Data.CashRecognitionDto = res.Data.CashRecognitionDto.length > 0 ? res.Data.CashRecognitionDto : []
        res.Data.SalesOffsetRecognitionDto = res.Data.SalesOffsetRecognitionDto.length > 0 ? res.Data.SalesOffsetRecognitionDto : []
        res.Data.CashRecognitionDto.map((item, index) => {
          item.type = 1
        })
        res.Data.SalesOffsetRecognitionDto.map((item, index) => {
          item.type = 2
        })
        array = res.Data.CashRecognitionDto.concat(res.Data.SalesOffsetRecognitionDto)
        array.sort((a, b) => a.Index - b.Index);
        this.$set(this.model, 'SalesOrderRecognitionAsync', array)
        this.$forceUpdate()
      })
    }
  }
}
</script>

<style lang="less" scoped>
</style>