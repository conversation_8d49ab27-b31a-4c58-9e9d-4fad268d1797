<!-- 售后详情 入库信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <template v-if="(model.SaleAfterInbounds || []).length">
        <a-card
          :bordered="false"
          :body-style="{ padding: 0 }"
          v-for="item in model.SaleAfterInbounds || []"
          :key="item.Id"
        >
          <template #title>
            <span style="margin-right: 6px">入库单号：{{ item.Code || ' -- ' }}</span>
            <span style="margin-right: 6px">入库时间：{{ item.InBoundTime || ' -- ' }}</span>
            <span style="margin-right: 6px">入库金额：￥{{ item.InBoundAmount || ' -- ' }}</span>
            <!-- <span>订单金额：￥{{ item.SalesOrderAmount || ' -- ' }}</span> -->
          </template>
          <SimpleTable
            ref="tableGoods"
            :tab="tabGoods"
            :columns="columnsGoods"
            :tableDatas="item.SaleAfterInboundGoodses || []"
          >
            <span slot="commonContent" slot-scope="{ text }">
              <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
            </span>
          </SimpleTable>
        </a-card>
      </template>
      <a-empty v-else />
    </a-spin>
  </div>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '售后详情 入库信息',
  name: 'IncomingInfoAfterSaleDetail',
  mixins: [SimpleMixin],
  components: {},
  props: {
    Info: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      loading: false,
      md: 8,
      model: {},
      tabGoods: {
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        operateBtn: [],
        hideIpagination: true,
      },
      columnsGoods: [
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'price' },
        },
        // {
        //   title: '金额',
        //   dataIndex: 'SubtotalAmount',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'price' },
        // },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '有效期',
          dataIndex: 'GoodsPeriod',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退货数量',
          dataIndex: 'SaleAfterCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '入库数量',
          dataIndex: 'InboundCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
      ],
      httpHead: 'P36008',
      url: {
        getSaleAfterInbounds: '/{v}/SaleAfterOrder/GetSaleAfterInbounds',
      },
    }
  },
  computed: {},
  created() {},
  mounted() {
    // console.log(this.Info)

    if (this.Info) {
      this.model = Object.assign({}, this.Info)
      this.loadDetailInfo()
    }
  },
  methods: {
    loadDetailInfo() {
      if (!this.model.Id) return
      getAction(this.url.getSaleAfterInbounds, { id: this.model.Id }, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$set(this.model, 'SaleAfterInbounds', res.Data || [])
      })
    },
  },
}
</script>

<style lang="less" scoped></style>
