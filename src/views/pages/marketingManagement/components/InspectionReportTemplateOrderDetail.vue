<!-- 商销商品订单详情 检验报告 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card :bordered="false" :body-style="{ padding: 0 }">
        <a-row :gutter="gutter" style="margin-bottom: 24px">
          <a-col :span="24">
            <!-- 列表 -->
            <SimpleTable ref="table" :tab="tab" :columns="columns" @operate="operate">
              <span slot="commonContent" slot-scope="{ text }">
                <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
              </span>
            </SimpleTable>
          </a-col>
        </a-row>
      </a-card>
    </a-spin>
    <!-- 查看检验报告 -->
    <PdfModal ref="iPdfModal" />
  </div>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销商品订单详情 检验报告',
  name: 'InspectionReportTemplateOrderDetail',
  mixins: [ListMixin, SimpleMixin],
  components: {},
  props: {
    Info: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      loading: false,
      md: 8,
      gutter: 20,
      model: {
        TestReports: [],
      },
      tab: {},
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 250,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '是否有报告',
          dataIndex: 'IsHasReport',
          ellipsis: true,
          width: 150,
          customRender: (t, r, i) => {
            return t ? '是' : '否'
          },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          actionBtn: [
            // { name: '下载检验报告', icon: '', id: '13bba76d-fbc9-4bb4-91e1-1af06662351b' },
            { name: '查看检验报告', icon: '', id: '6c54d8c8-9314-4dd7-a47e-732a0801a809' },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      env: process.env.VUE_APP_PREVIEW.trim(),
      httpHead: 'P36008',
      url: {
        getTestReports: '/{v}/Order/GetTestReports',
        getGoodsReportFileList: '/GoodsFilesQuery/GetGoodsReportFileList',
      },
    }
  },
  computed: {},
  created() { },
  mounted() {
    // console.log(this.Info)

    if (this.Info) {
      this.getEnvType()
      this.loadDetailInfo()
    }
  },
  methods: {
    getEnvType() {
      let toast = ''
      switch (this.env) {
        case 'development':
        case 'test':
          this.url.getGoodsReportFileList = '/GoodsManageToErpForReport/GetGoodsReportFileList'
          break
        case 'production':
          this.url.getGoodsReportFileList = '/GoodsFilesQuery/GetGoodsReportFileList'
          break
        case 'stage':
          this.url.getGoodsReportFileList = '/GoodsManageToErpForReport/GetGoodsReportFileList'
          break
      }
      return toast
    },
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.getTestReports, { orderId: that.Info.Id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.$set(that.model, 'TestReports', res.Data || [])
            that.$set(that.$refs.table, 'dataSource', that.model.TestReports)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // 列表操作
    operate(record, type) {
      if (type == '下载检验报告') {
        this.getGoodsReportFileList(record)
      } else if (type == '查看检验报告') {
        this.getGoodsReportFileList(record)
      }
    },
    getGoodsReportFileList(record) {
      getAction(
        this.url.getGoodsReportFileList,
        {
          erpCoodsId: record['ErpGoodsId'],
          // batchNumber: record['GoodsBatchNumber']
        },
        'P46003'
      ).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        // console.log(res.Data)
        if (!(res.Data || []).length) {
          this.$message.warning('未找到该商品的检验报告！')
          return
        }
        let FileIds = res.Data.map((v) => v.FileId)
        this.getMoreFileTempUrls(FileIds, (data) => {
          if ((data || []).length) {
            let FileUrls = data.map((v) => v.TempUrl)
            FileUrls = FileUrls.map((item) => {
              return item.indexOf('http:') >= 0 ? item.replace('http:', 'https:') : item
            })
            this.$refs.iPdfModal.show('查看检验报告', FileUrls, '点击右上角箭头下载报告')
          }
        })
      })
    },
  },
}
</script>

<style lang="less" scoped></style>
