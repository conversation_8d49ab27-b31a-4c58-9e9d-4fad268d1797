<!-- 商销调价 详情   基本信息 -->
<template>
  <div>
    <!-- 订单信息 -->
    <a-descriptions title="订单信息">
      <a-descriptions-item label="客户名称">{{ model.CustomerName || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="客户类别">{{ model.CustomerTypeStr || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="订单类型">{{ model.TypeStr || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="收货人">{{ model.ReceivingName || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="联系电话">{{ model.ReceivingPhone || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="收货地址">{{
        (model.ReceivingAreaFullName || ' -- ') + '' + (model.ReceivingAddress || ' -- ')
      }}</a-descriptions-item>
      <a-descriptions-item label="快递公司">{{ model.ExpressCompanyStr || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="订单金额">￥{{ model.TotalAmount || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="订单编号">{{ model.Code || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="下单时间">{{ model.PlaceOrderTime || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="付款方式">{{ model.PaymentModeStr || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="审核状态">{{ model.AuditStatusStr || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="订单状态">{{ model.OrderStatusStr || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="开票状态">{{ model.InvoiceStatusStr || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="认款状态">{{ model.OrderAcceptanceStatusStr || ' -- ' }}</a-descriptions-item>
      <a-descriptions-item label="差价调整原因">{{
        getSalesOrderAdjustPrice.AdjustReason || ' -- '
      }}</a-descriptions-item>
    </a-descriptions>
    <!-- 调价信息 -->
    <a-row :gutter="20" style="margin-bottom: 24px">
      <a-col :span="24">
        <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">调价信息</div>
      </a-col>
      <a-col :span="24">
        <!-- 列表 -->
        <SimpleTable ref="table" :tab="tabGoods" :isTableInitData="isTableInitData" :columns="columnsGoods">
          <span slot="commonContent" slot-scope="{ text }">
            <j-ellipsis :value="`￥${text || 0}`" :length="15" />
          </span>
        </SimpleTable>
      </a-col>
    </a-row>
  </div>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'PricesAdjustedDetailBase',
  components: { JEllipsis },
  props: {
    model: {
      type: Object,
      default: () => {},
      required: true,
    },
    getSalesOrderAdjustPrice: {
      type: Object,
      default: () => {},
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      id: '',
      orderId: '',
      tabGoods: {
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        operateBtn: [],
        hideIpagination: true,
      },
      columnsGoods: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '有效期',
        //   dataIndex: 'GoodsPeriod',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        {
          title: '批次号',
          dataIndex: 'BatchNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '仓库',
          dataIndex: 'WarehouseCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '可销库存',
        //   dataIndex: 'GoodsCanSaleInventory',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        // {
        //   title: '最低商销价',
        //   dataIndex: 'GoodsLimitPrice',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'Price' },
        // },
        // {
        //   title: '商销报价',
        //   dataIndex: 'GoodsLimitPrice',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'Price' }
        // },
        // {
        //   title: '末次销售价',
        //   dataIndex: 'LastSalesPrice',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'Price' },
        // },
        // {
        //   title: '采购管理模式',
        //   dataIndex: 'GoodsProcurementManagementModelName',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        // {
        //   title: '是否赠品',
        //   dataIndex: 'IsGift',
        //   ellipsis: true,
        //   width: 150,
        //   customRender: (t, r, i) => {
        //     return t == null ? '' : t ? '是' : '否'
        //   },
        // },
        {
          title: '销售单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'Price' },
        },
        // {
        //   title: '最新单价',
        //   dataIndex: 'AdjustPrice',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'Price' },
        // },
        {
          title: '销售数量',
          dataIndex: 'Count',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '出库数量',
          dataIndex: 'OutboundCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '销售价格',
        //   dataIndex: 'Count1',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' }
        // },
        {
          title: '小计',
          dataIndex: 'OutboundSubtotalAmount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'Price' },
        },
        // {
        //   title: '是否特赠品',
        //   dataIndex: 'IsSpecialGift',
        //   ellipsis: true,
        //   width: 100,
        //   customRender: (t, r, i) => {
        //     return t == null ? '' : t ? '是' : '否'
        //   }
        // },
        // {
        //   title: '调价后单价',
        //   dataIndex: 'AdjustPrice',
        //   ellipsis: true,
        //   width: 100,
        //   fixed: 'right',
        //   scopedSlots: { customRender: 'component' },
        // },
        {
          title: '调价数量',
          dataIndex: 'AdjustCount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调价小计金额',
          dataIndex: 'AdjustTotalAmount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '每盒差额',
          dataIndex: 'AdjustPerSpreadPrice',
          ellipsis: true,
          width: 100,
          precision: 6,
          fixed: 'right',
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '备注',
          dataIndex: 'Remarks',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
      ],
      queryParam: {},
      isTableInitData: false,
    }
  },
  computed: {},
  mounted() {
    if (this.getSalesOrderAdjustPrice) {
      this.$refs.table.dataSource = this.getSalesOrderAdjustPrice.SalesOrderAdjustPriceDetails || []
    }
  },
  created() {},
  methods: {},
}
</script>

<style scoped></style>
