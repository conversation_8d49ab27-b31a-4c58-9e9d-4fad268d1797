<template>
  <a-modal :title="title" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="快递公司：" prop="ShipperCode">
                <SingleChoiceView ref="singleChoiceViewKD" style="width: 100%" :key="model.CustomerId" placeholder="请选择" v-model="model.ShipperCode" :disabled="!model.CustomerId" @change="
                    (val, txt, item) => {
                      if (item) {
                        model.ShipperCode = val
                        model.ShipperCodeStr = txt
                      } else {
                        model.ShipperCode = ''
                        model.ShipperCodeStr = ''
                      }
                    }
                  " />
              </a-form-model-item>
              <a-form-model-item label="包裹数量：" prop="PackageCount">
                <a-input-number placeholder="请输入包裹数量" v-model="model.PackageCount" :min="1" :parser="(text) => (/^\d+$/.test(text) ? text : text.slice(0,-1))" :max="999999999" style="width: 100%" />
              </a-form-model-item>
              <a-form-model-item v-if="['JD','SF','EMS','YKD'].includes(model.ShipperCode)" label="物流单号：" prop="LogisticCode">
                <a-input placeholder="请输入物流单号" v-model="model.LogisticCode" style="width: 100%" />
              </a-form-model-item>

            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button @click="handleCancel">
        取消
      </a-button>
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">提交</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "LogisticsInfoTemplateOrderAddModal",
  title: '填写快递信息',
  data() {
    return {
      title: "填写快递信息",
      visible: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {
        PackageCount: 1,
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      id: '',
      confirmLoading: false,
      httpHead: 'P36008',
      url: {
        add: "/{v}/LogisticsOrder/ManualCreateLogisticsOrder",
        expressList: '/{v}/LogisticsOrder/GetExpressCompanies'
      },
    };
  },
  computed: {
    rules() {
      return {
        ShipperCode: [{ required: true, message: '请选择快递公司!' }],
        PackageCount: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.model.PackageCount) {
                callback('请输入包裹数量!')
              } else if (this.model.PackageCount < 1) {
                callback('包裹数量最小值为1!')
              } else {
                callback()
              }
            }
          },
        ],
        LogisticCode: [{ required: true, message: '请输入物流单号!' }],
      };
    },
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    add(id, info) {
      this.title = '填写快递信息'
      this.model = {
        ...info,
        PackageCount: 1,
      }
      this.id = id
      this.visible = true;
      if (!this.model.CustomerId) return
      this.getExpressList()
    },
    async getExpressList() {
      const params = {
        PageIndex: 1,
        PageSize: 150
      }
      const res = await getAction(this.url.expressList, params, this.httpHead)
      if (!res.IsSuccess) return
      const data = (res.Data || []).filter(item => item.IsEnabled).map(it => ({ ...it, Item: it }))
      this.$refs.singleChoiceViewKD.setCurrentDictOptions(data)
    },
    changeCompany(val, name, item) {
      this.model.MerchantId = val
      this.model.MerchantName = name
      this.dataSource = []
      this.$forceUpdate()
    },
    // 确定
    handleOk() {
      // 触发表单验证
      const that = this;
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          let formData = {}
          let url = this.url.add
          this.model.SalesOrderId = this.id
          formData = {
            ShipperCode: this.model.ShipperCode,
            LogisticCode: this.model.LogisticCode,
            PackageCount: this.model.PackageCount,
            SalesOrderId: this.model.SalesOrderId,
          }
          postAction(url, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
