
<!-- 商销商品订单详情 操作信息 -->
<template>
  <div>
    <a-spin :spinning="loading">
    <a-timeline v-if="model && (model['OrderOperateRecords'] || []).length > 0">
      <a-timeline-item color="blue" v-for="(group, index) in (model['OrderOperateRecords'] || [])" :key="index">
        <p>{{group.OperateTime || ' -- '}}</p>
        <p>操作人：{{group.OperateByName || ' -- '}}</p>
        <p>操作描述：{{group.OperateDescription || ' -- '}}</p>
      </a-timeline-item>
    </a-timeline>
    <template v-else>
      <a-empty />
    </template>
  </a-spin>
  </div>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销商品订单详情 操作信息',
  name: 'OperationalInfoTemplateOrderDetail',
  mixins: [SimpleMixin],
  components: {},
  props: {
    Info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      loading: false,
      model: {},
      httpHead: 'P36008',
      url: {
        getOrderOperateRecords:"/{v}/Order/OrderOperateRecords"
      }
    }
  },
  computed: {},
  created() {},
  mounted() {
    // console.log(this.Info)

    if (this.Info) {
       this.model = {
        OrderId:this.Info['Id']
      }
      this.loadDetailInfo()
    }
  },
  methods: {
      /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.getOrderOperateRecords, { orderId: that.model.OrderId }, that.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            that.$set(that.model,'OrderOperateRecords',res.Data || [])
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
  }
}
</script>

<style lang="less" scoped>
</style>