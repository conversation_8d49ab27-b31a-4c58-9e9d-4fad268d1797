
<!-- 商销商品订单详情 物流信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card :bordered="false" :body-style="{padding:0}">
        <div style="width:100%;text-align: right;">
          <a-button type="primary" @click="addLogistice">填写快递信息</a-button>
        </div>
        <a-tabs type="editable-card" :hideAdd="true" @edit="onTabsEdit" v-model="activeTabKey" v-if="( model.LogisticsOrders ||[]).length">
          <a-tab-pane :key="item.key" :tab="item.LogisticCode" v-for="item in model.LogisticsOrders">
            <a-descriptions title="">
              <a-descriptions-item label="订单编号">{{ Info.Code || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="包裹数量">{{ item.PackageCount || 0 }}</a-descriptions-item>
              <a-descriptions-item label="物流单号">{{ item.LogisticCode || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="物流公司">{{ item.ShipperName || ' -- ' }}</a-descriptions-item>
            </a-descriptions>
            <!-- <div
              style="width: 100%; height: 580px"
              v-if="item.RouteMapUrl"
            >
              <iframe
                :src="item.RouteMapUrl"
                frameborder="0"
                width="100%"
                height="580px"
                scrolling="no"
              ></iframe>
            </div>
            <div
              style="font-size: 16px; font-weight: 600"
              v-else
            >暂无物流轨迹</div> -->
            <a-card title="物流信息">
              <a-timeline>
                <a-timeline-item v-for="(a, i) in item.LogisticsOrderLogs" :key="i" :color="i == 0 ? 'green' : ''">
                  <a-icon slot="dot" type="check-circle" style="font-size: 16px" v-if="i == 0" />
                  <a-icon slot="dot" type="clock-circle" style="font-size: 16px" v-else />
                  <p>{{ a.AcceptTime }}</p>
                  <div v-html="a.AcceptStation"></div>
                </a-timeline-item>
              </a-timeline>
            </a-card>
            <a-card title="物流回执单" style="margin-top:20px" v-if="!['JD'].includes(item.ShipperCode)">
              <template slot="extra">
                <a-button type="primary" @click="addLogisticeOrder(item)">上传</a-button>
              </template>
              <template v-if="getOutLogisticsOrderReceiptFiles(item).length">
                <!-- <ViewerImage :images="getOutLogisticsOrderReceiptFiles(item,'OutLogisticsOrderReceiptFiles')" v-if="(getOutLogisticsOrderReceiptFiles(item,'OutLogisticsOrderReceiptFiles') || []).length" /> -->
                <MultiUpload :images="getOutLogisticsOrderReceiptFiles(item,'OutLogisticsOrderReceiptFiles') || []" :readOnly="true" />
              </template>

            </a-card>
          </a-tab-pane>
        </a-tabs>
        <a-empty v-else />
      </a-card>

    </a-spin>
    <!-- 新增快递物流信息 -->
    <LogisticsInfoTemplateOrderAddModal ref="LogisticsInfoTemplateOrderAddModal" @ok="modalOk" />
    <!-- 上传物流回执单 -->
    <LogisticsInfoTemplateOrderModal ref="LogisticsInfoTemplateOrderModal" @ok="modalOk" />
  </div>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销商品订单详情 物流信息',
  name: 'LogisticsInfoTemplateOrderDetail',
  mixins: [SimpleMixin],
  components: {},
  props: {
    Info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      loading: false,
      md: 8,
      activeTabKey: 1,
      model: {
        LogisticsOrders: [],
      },
      httpHead: 'P36008',
      url: {
        getLogisticsOrders: '/{v}/LogisticsOrder/GetLogisticsOrders',
        delLogisticsOrders: '/{v}/LogisticsOrder/DeleteOrderLogisticsRelation',
      }
    }
  },
  computed: {

  },
  created() { },
  mounted() {
    // console.log(this.Info)

    if (this.Info) {
      this.loadDetailInfo()
    }
  },
  methods: {
    getOutLogisticsOrderReceiptFiles(item, key) {
      let arr = item.OutLogisticsOrderReceipt
      let list = []
      // arr.map(v=>{
      //   if((v.OutLogisticsOrderReceiptFiles || []).length && v.OutLogisticsOrderReceiptFiles[0].FileUrl){
      //     list.push(v.OutLogisticsOrderReceiptFiles[0].FileUrl)
      //   }
      // })
      arr.map(i => {
        if (i.OutLogisticsOrderReceiptFiles && i.OutLogisticsOrderReceiptFiles.length > 0) {
          i.OutLogisticsOrderReceiptFiles.map(rItem => {
            list.push(rItem.FileUrl || '')
          })
        }
      })
      if (key) {
        return list
      }
      return arr
    },
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.getLogisticsOrders, { orderId: that.Info.Id }, that.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            that.$set(that.model, 'LogisticsOrders', res.Data || [])
            // 设置key
            if (that.model.LogisticsOrders && that.model.LogisticsOrders.length > 0) {
              that.model.LogisticsOrders.map((item, index) => {
                item.key = index + 1
              })
            }

          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    modalOk() {
      this.$emit("ok")
      this.loadDetailInfo()
    },
    addLogistice() {
      this.$refs.LogisticsInfoTemplateOrderAddModal.add(this.Info.Id,this.Info)
    },
    // 上传物流回执单
    addLogisticeOrder(item) {
      let imgList = this.getOutLogisticsOrderReceiptFiles(item, 'OutLogisticsOrderReceiptFiles') || []
      this.$refs.LogisticsInfoTemplateOrderModal.show(this.Info.Id, item, imgList,this.Info)
    },
    onTabsEdit(targetKey, action) {
      if (action == 'remove') {
        let that = this
        this.$confirm({
          title: '您确定要删除当前物流吗?',
          content: '',
          onOk() {
            that.removeLogistics(targetKey)
          },
          onCancel() { },
        });
      }
    },
    removeLogistics(targetKey) {
      let item = this.model.LogisticsOrders.find(item => {
        return item.key == targetKey
      })
      let params = {
        salesOrderId: this.Info.Id,
        logisticsOrderId: item ? item.Id : '',
      }
      getAction(this.url.delLogisticsOrders, params, this.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            this.remove(targetKey)
            this.$emit("ok")
            this.$message.success('删除成功')
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {

        })
    },
    remove(targetKey) {
      let activeTabKey = this.activeTabKey;
      let lastIndex;
      this.model.LogisticsOrders.forEach((pane, i) => {
        if (pane.key === targetKey) {
          lastIndex = i - 1;
        }
      });
      const panes = this.model.LogisticsOrders.filter(pane => pane.key !== targetKey);
      if (panes.length && activeTabKey === targetKey) {
        if (lastIndex >= 0) {
          activeTabKey = panes[lastIndex].key;
        } else {
          activeTabKey = panes[0].key;
        }
      }
      this.model.LogisticsOrders = panes;
      this.activeTabKey = activeTabKey || 0;
    },
  }
}
</script>

<style lang="less" scoped>
/deep/.image-inline-item {
  border: 1px solid rgb(233, 233, 233);
  display: inline-block;
  width: 102px;

  & + .image-inline-item {
    margin-left: 10px;
  }
  img {
    object-fit: contain;
    text-align: center;
  }
}
/deep/.ant-tabs.ant-tabs-card > .ant-tabs-bar {
  .ant-tabs-tab {
    border: 1px solid #e8e8e8 !important;
  }
  .ant-tabs-tab-active {
    background: #f0f2f5 !important;
    .ant-tabs-close-x {
      opacity: 1 !important;
    }
  }
}
</style>