
<!-- 图片插件 -->
<template>
  <div style="background: rgba(0, 0, 0, 0.1);width: 60vw;padding:15px">
    <ImageViewer
      ref="ImageViewer"
      :imgList="imagesArr"
      title="照片"
      :imgIndex="containerIndex"
      v-if="imagesArr.length"
    ></ImageViewer>
  </div>
</template>

<script>
export default {
  description: '图片插件',
  name: 'ViewerImage',
  mixins: [],
  components: {},
  props: {
    images: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      imagesArr: [],
      containerIndex: 0,
      imgShow: false
    }
  },
  computed: {},
  created() {},
  mounted() {
    this.imagesArr = []
    if (this.images.length) {
      this.images.map(v => {
        this.imagesArr.push({
          FileUrl: v
        })
      })
      this.containerIndex = 0
      this.imgShow = false
      this.$nextTick(() => {
        this.imgShow = true
      })
      // console.log(this.imagesArr)
    }
  },
  methods: {}
}
</script>

<style lang="less" scoped>
</style>