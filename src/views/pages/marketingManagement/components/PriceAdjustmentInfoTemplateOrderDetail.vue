<!-- 商销商品订单详情 调价信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card :bordered="false" :body-style="{ padding: 0 }">
        <a-collapse v-model="activeKey" v-if="(model || []).length">
          <a-collapse-panel
            v-for="(item, index) in model"
            :key="'' + (index + 1)"
            :header="`调价编号：${item['Code'] || ' -- '}`"
          >
            <a slot="extra">{{ activeKey.some((v) => v == 1) ? '收起' : '展开' }}</a>
            <a-descriptions title="调价信息">
              <a-descriptions-item label="调价金额">￥{{ item.AdjustAmount || 0 }}</a-descriptions-item>
              <a-descriptions-item label="调价时间">{{ item.CreateTime || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="调价原因">{{ item.AdjustReason || ' -- ' }}</a-descriptions-item>
            </a-descriptions>
            <!-- 调价商品 -->
            <a-row :gutter="gutter" style="margin-bottom: 24px">
              <a-col :span="24">
                <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">调价商品</div>
              </a-col>
              <a-col :span="24">
                <!-- 列表 -->
                <SimpleTable
                  ref="table"
                  :tab="tabGoods"
                  :columns="columnsGoods"
                  :tableDatas="item.SalesOrderAdjustPriceDetails || []"
                >
                  <span slot="commonContent" slot-scope="{ text }">
                    <j-ellipsis :value="`￥${text || 0}`" :length="15" />
                  </span>
                </SimpleTable>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
        <a-empty v-else />
      </a-card>
    </a-spin>
  </div>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销商品订单详情 调价信息',
  name: 'PriceAdjustmentInfoTemplateOrderDetail',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  props: {
    Info: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      loading: false,
      md: 8,
      gutter: 20,
      model: [],
      activeKey: [1],
      tabGoods: {
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        operateBtn: [],
        hideIpagination: true,
      },
      columnsGoods: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '有效期',
          dataIndex: 'GoodsPeriod',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '可销库存',
          dataIndex: 'GoodsCanSaleInventory',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '最低商销价',
          dataIndex: 'GoodsPurchaseCostPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '商销底价',
          dataIndex: 'GoodsLimitPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '末次销售价',
          dataIndex: 'LastSalesPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '采购管理模式',
          dataIndex: 'GoodsProcurementManagementModelName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '是否赠品',
        //   dataIndex: 'IsGift',
        //   ellipsis: true,
        //   width: 150,
        //   customRender: (t, r, i) => {
        //     return t == null ? '' : t ? '是' : '否'
        //   },
        // },
        {
          title: '单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '出库数量',
          dataIndex: 'OutboundCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '小计',
          dataIndex: 'SubtotalAmount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'Price' },
        },
        // {
        //   title: '是否特赠品',
        //   dataIndex: 'IsSpecialGift',
        //   ellipsis: true,
        //   width: 100,
        //   customRender: (t, r, i) => {
        //     return t == null ? '' : t ? '是' : '否'
        //   }
        // },
        // {
        //   title: '调价后单价',
        //   dataIndex: 'AdjustPrice',
        //   ellipsis: true,
        //   width: 100,
        //   fixed: 'right',
        //   scopedSlots: { customRender: 'Price' },
        // },
        {
          title: '调价数量',
          dataIndex: 'AdjustCount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '每盒差额',
          dataIndex: 'AdjustPerSpreadPrice',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          precision:6,
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '调价小计金额',
          dataIndex: 'AdjustGrossSpreadAmount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'Price' },
        },
        // {
        //   title: '调价合计金额',
        //   dataIndex: 'AdjustTotalAmount',
        //   ellipsis: true,
        //   width: 100,
        //   fixed: 'right',
        //   scopedSlots: { customRender: 'Price' },
        // },
        {
          title: '备注',
          dataIndex: 'Remarks',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
      ],
      httpHead: 'P36008',
      url: {
        getSalesOrderAdjustPrices: '/{v}/SalesOrderAdjustPrice/GetSalesOrderAdjustPrices',
      },
    }
  },
  computed: {},
  created() {},
  mounted() {
    // console.log(this.Info)

    if (this.Info) {
      this.loadDetailInfo()
    }
  },
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.getSalesOrderAdjustPrices, { orderId: that.Info.Id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || []
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
  },
}
</script>

<style lang="less" scoped></style>
