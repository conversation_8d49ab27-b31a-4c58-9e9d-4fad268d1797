<!-- 商销商品订单详情 基本信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card :bordered="false" :body-style="{ padding: 0 }">
        <!-- 基本信息 -->
        <a-descriptions title="">
          <a-descriptions-item label="客户名称">{{ model.CustomerName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="客户类别">{{ model.CustomerTypeStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="委托人">{{ model.DelegateName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="订单类型">{{ model.TypeStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="收货人">{{ model.ReceivingName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ model.ReceivingPhone || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="收货地址">{{ model.ReceivingAddress || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="快递公司">{{ model.ExpressCompanyStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="备注信息">{{ model.Remarks || ' -- ' }}</a-descriptions-item>
        </a-descriptions>
        <!-- 应收/应付信息 -->
        <a-row :gutter="20" style="margin-bottom: 24px">
          <a-col :span="24">
            <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">应收/应付信息</div>
          </a-col>
          <a-col :span="24">
            <!-- 列表 -->
            <SimpleTable ref="table" :tab="tab" :columns="columns">
              <span slot="commonContent" slot-scope="{ text }">
                <j-ellipsis :value="`￥${text || 0}`" :length="15" />
              </span>
            </SimpleTable>
          </a-col>
        </a-row>
        <!-- 商品信息 -->
        <a-row :gutter="20" style="margin-bottom: 24px">
          <a-col :span="24">
            <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">商品信息</div>
          </a-col>
          <a-col :span="24">
            <!-- 列表 -->
            <SimpleTable ref="tableGoods" :tab="tabGoods" :columns="columnsGoods">
              <span slot="commonContent" slot-scope="{ text }">
                <j-ellipsis :value="`￥${text || 0}`" :length="15" />
              </span>
              <span slot="LastSalesPrice" slot-scope="{ text, record }">
                <a @click="$refs.iViewHistoricalSalesPricesListModal.show(record.GoodsId)">
                  <j-ellipsis :value="text ? `￥${text}` : '0'" :length="15" />
                </a>
              </span>
            </SimpleTable>
          </a-col>
          <a-col :span="24">
            <span style="margin-right: 10px">订单金额：<span style="color: red; font-size: 30px">¥{{ model.TotalAmount || 0 }}</span>
            </span>
            <span style="margin-right: 10px">整单成本：<span>¥{{ model.TotalCost || 0 }}</span>
            </span>
            <span style="margin-right: 10px">整单毛利：<span>¥{{ model.TotalGrossMargin || 0 }}</span>
            </span>
            <span style="margin-right: 10px">整单毛利率：<span>{{ model.TotalGrossMarginRate || 0 || ' -- ' }}%</span>
            </span>
          </a-col>
          <a-col :span="24" v-if="model.Attachments && model.Attachments.length">
            <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">上传附件</div>
          </a-col>
          <a-col :md="24" v-if="model.Attachments && model.Attachments.length">
            <MultiUpload
              :max="10"
              :images="model.Attachments"
              :uploadText="uploadText"
              :fileType="4"
              :bName="BName"
              :dir="Dir"
              :readOnly="true"
              :fileSize="50" />
          </a-col>
        </a-row>
      </a-card>
    </a-spin>

    <!-- 查看历史销售价 -->
    <ViewHistoricalSalesPricesListModal ref="iViewHistoricalSalesPricesListModal" />
    <!-- 查看照片 -->
    <PurchaseDealImgModal ref="PurchaseDealImgModal"></PurchaseDealImgModal>
  </div>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销商品订单详情 基本信息',
  name: 'BasicInfoTemplateOrderDetail',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  props: {
    Info: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      loading: false,
      md: 8,
      model: {},
      tab: { hideIpagination: true },
      columns: [
        {
          title: '应收余额',
          dataIndex: 'ReceivableBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '预收余额',
          dataIndex: 'AdvancePaymentBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '应付余额',
          dataIndex: 'PayableBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '预付余额',
          dataIndex: 'PrepaymentBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
      ],
      tabGoods: {
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        operateBtn: [],
        sortArray: ['LastSalesPrice'],
        hideIpagination: true,
      },
      columnsGoods: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产日期',
          dataIndex: 'ProductionDate',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '有效期',
          dataIndex: 'GoodsPeriod',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批次号',
          dataIndex: 'BatchNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '仓库',
          dataIndex: 'WarehouseCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批次可销库存',
          dataIndex: 'BatchCanSaleInventory',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商销政策底价',
          dataIndex: 'PolicyPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '采购含税价',
          dataIndex: 'GoodsPurchaseCostPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        // {
        //   title: '移动平均成本',
        //   dataIndex: 'AvgCostPrice',
        //   ellipsis: true,
        //   width: 100,
        //   scopedSlots: { customRender: 'commonSlot' },
        // },
        {
          title: '商销底价',
          dataIndex: 'GoodsLimitPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '末次销售价',
          dataIndex: 'LastSalesPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'LastSalesPrice' },
        },
        // {
        //   title: '采购管理模式',
        //   dataIndex: 'GoodsProcurementManagementModelName',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        // {
        //   title: '是否赠品',
        //   dataIndex: 'IsGift',
        //   ellipsis: true,
        //   width: 150,
        //   customRender: (t, r, i) => {
        //     return t == null ? '' : t ? '是' : '否'
        //   },
        // },
        // {
        //   title: '商品条码',
        //   dataIndex: 'GoodsBarCode',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        {
          title: '单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '销售数量',
          dataIndex: 'Count',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '小计金额',
          dataIndex: 'SubtotalAmount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '单品毛利',
          dataIndex: 'GrossMargin',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '单品总毛利',
          dataIndex: 'SubtotalGrossMargin',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '出库数量',
          dataIndex: 'OutboundCount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '是否特赠品',
        //   dataIndex: 'IsSpecialGift',
        //   ellipsis: true,
        //   width: 100,
        //   fixed: 'right',
        //   customRender: (t, r, i) => {
        //     return t == null ? '' : t ? '是' : '否'
        //   }
        // },
        {
          title: '备注',
          dataIndex: 'Remarks',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
      ],
      httpHead: 'P36008',
      url: {
        getTotalBalanceAsync: '/{v}/Account/GetTotalBalanceAsync',
      },
      uploadText: '上传图片',
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
    }
  },
  computed: {},
  created() { },
  mounted() {
    if (this.Info) {
      this.model = Object.assign({}, this.Info)
      if (this.$refs.tableGoods) {
        this.$refs.tableGoods.dataSource = (this.Info['Details'] || []).map(it => {
          return { ...it, ProductionDate: (it.ProductionDate || '').slice(0, 10), GoodsPeriod: (it.GoodsPeriod || '').slice(0, 10) }
        })
      }
      this.getTotalBalanceAsync()
    }
  },
  methods: {
    // 应收/应付信息
    getTotalBalanceAsync() {
      if (!this.model.CustomerId) return
      getAction(this.url.getTotalBalanceAsync, { CustomerId: this.model.CustomerId }, 'P36011').then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (this.$refs.table) {
          this.$refs.table.dataSource = [res.Data]
        }
      })
    },
    showImgModal(item, index) {
      this.$refs.PurchaseDealImgModal.show(this.model.FileList, item, index)
    },
  },
}
</script>

<style lang="less" scoped></style>
