<!-- 商销调价详情 结算信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card :bordered="false" :body-style="{ padding: 0 }">
        <a-collapse v-model="activeKey" v-if="(model.SalesRecognitionList || []).length">
          <a-collapse-panel
            :key="'' + (i + 1)"
            :header="'销货对抵'"
            v-for="(item, i) in model.SalesRecognitionList"
          >
            <a slot="extra">{{ activeKey.some((v) => v == 1) ? '收起' : '展开' }}</a>
            <a-descriptions title="" :column="1">
              <!-- 销货对抵 -->
              <a-descriptions-item label="对抵金额">￥{{ item.AcceptanceAmount || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="对抵编号">{{ item.AcceptanceNo || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="对抵时间">{{ item.AcceptanceTime || ' -- ' }}</a-descriptions-item>
            </a-descriptions>
          </a-collapse-panel>
        </a-collapse>
        <!-- <a-empty v-else /> -->
      </a-card>
      <a-card :bordered="false" :body-style="{ padding: 0 }">
        <a-collapse v-model="activeKey1" v-if="(model.CashRecognitionList || []).length">
          <a-collapse-panel
            :key="'' + (i + 1)"
            :header="'认款'"
            v-for="(item, i) in model.CashRecognitionList"
          >
            <a slot="extra">{{ activeKey1.some((v) => v == 1) ? '收起' : '展开' }}</a>
            <a-descriptions title="" :column="1">
              <a-descriptions-item label="认款金额">￥{{ item.RecognitionAmount }}</a-descriptions-item>
              <a-descriptions-item label="收款方式">{{ item.ReceiptTypeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="银行名称">{{ item.BankName || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="银行卡号">{{ item.AccountNumber || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="打款方">{{ item.Payer || '--' }}</a-descriptions-item>
              <a-descriptions-item label="打款时间">{{ item.ReceiptTime || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="认款时间">{{ item.RecognitionTime || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="备注">{{ item.Note || ' -- ' }}</a-descriptions-item>
            </a-descriptions>
          </a-collapse-panel>
        </a-collapse>
        <!-- <a-empty v-else /> -->
      </a-card>
      <a-empty v-if="(model.SalesRecognitionList || []).length === 0 && (model.CashRecognitionList || []).length ===0 " />
    </a-spin>
  </div>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销调价详情 结算信息',
  name: 'PricesAdjustedDetailSettle',
  mixins: [SimpleMixin],
  components: {},
  props: {
    Info: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      loading: false,
      md: 8,
      model: {},
      activeKey: ['1'],
      activeKey1: ['1'],
      httpHead: 'P36008',
      url: {
        getSaleAfterRefunds: '/{v}/SalesOrderAdjustPrice/GetSalesOrderAdjustSettlementInfo',
      },
    }
  },
  computed: {},
  created() {},
  mounted() {
    if (this.Info) {
      this.model = Object.assign({}, this.Info)
      this.loadDetailInfo()
    }
  },
  methods: {
    loadDetailInfo() {
      if (! this.$route.query.id) return
      getAction(this.url.getSaleAfterRefunds, { salesOrderAdjustPriceId: this.$route.query.id }, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$set(this.model, 'SalesRecognitionList', res.Data&&res.Data.SalesRecognitionList || [])
        this.$set(this.model, 'CashRecognitionList', res.Data&&res.Data.CashRecognitionList || [])
      })
    },
  },
}
</script>

<style lang="less" scoped></style>
