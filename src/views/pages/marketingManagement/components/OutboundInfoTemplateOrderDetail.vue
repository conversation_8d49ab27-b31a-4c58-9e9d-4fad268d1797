
<!-- 商销商品订单详情 出库信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card
        :bordered="false"
        :body-style="{padding:0}"
      >
        <a-collapse
          v-model="activeKey"
          v-if="(model.OrderOutbounds || []).length"
        >
          <a-collapse-panel
            :key="'' + (index + 1)"
            v-for="(item,index) in model.OrderOutbounds"
          >
            <template slot="header">
              出库单号：{{item.Code || ' -- '}}
              <a-button
                type="primary"
                style="margin-left:6px"
                :loading="item.loadingFileUrl"
                @click.stop="handelFileUrlsClick(item)"
                v-if="checkBtnPermissions('1fdd0053-2606-47d5-ab0f-39d69acbba9b') && index === 0 && item.Id"
              >
                查看电子版出库单
              </a-button>
            </template>
            <a slot="extra">{{activeKey.some(v=>v == 1) ? '收起' : '展开'}}</a>
            <a-descriptions title="基本信息">
              <a-descriptions-item label="客户名称">{{ model.CustomerName || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="单据ID">{{ item.BillsId || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="开票时间">{{ item.BillsTime || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="出库时间">{{ item.OutboundTime || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="出库单号">{{ item.OutboundNo || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="实际收款总计">￥{{ item.ActualAmount || 0 }}</a-descriptions-item>
              <a-descriptions-item label="应收款总计">￥{{ item.ReceivableAmount || 0}}</a-descriptions-item>
              <a-descriptions-item label="本单折扣金额">￥{{ item.DiscountAmount || 0 }}</a-descriptions-item>
            </a-descriptions>
            <!-- 调价商品 -->
            <a-row
              :gutter="gutter"
              style="margin-bottom:24px"
            >
              <a-col :span="24">
                <div style="font-size: 16px; font-weight: 600;margin:0 0 15px 0">
                  出库单明细
                </div>
              </a-col>
              <a-col :span="24">
                <!-- 列表 -->
                <SimpleTable
                  ref="table"
                  :tab="tab"
                  :columns="columns"
                  :tableDatas="item['Details'] || []"
                >
                  <span
                    slot="commonContent"
                    slot-scope="{text}"
                  >
                    <j-ellipsis
                      :value="`￥${Number(text).toFixed(2)}`"
                      :length="15"
                    />
                  </span>
                </SimpleTable>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
        <a-empty v-else />
      </a-card>

    </a-spin>
    <!-- 查看电子版出库单 -->
    <PdfModal ref="iPdfModal" />
  </div>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销商品订单详情 出库信息',
  name: 'OutboundInfoTemplateOrderDetail',
  mixins: [SimpleMixin, ListMixin],
  components: { JEllipsis },
  props: {
    Info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      md: 8,
      gutter: 20,
      model: {
        OrderOutbounds: []
      },
      activeKey: [1],
      tab: {
        sortArray: [],
        hideIpagination: true
      },
      columns: [
        {
          title: '产地/生产厂家/上市许可持有人',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 250,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '货品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '订单数量',
          dataIndex: 'OrderCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '出库数量',
          dataIndex: 'OutboundCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '单价',
          dataIndex: 'OutboundPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' }
        },
        {
          title: '金额',
          dataIndex: 'SubtotalAmount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' }
        },
        {
          title: '批号',
          dataIndex: 'BatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '有效期',
          dataIndex: 'Period',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '批准文号',
          dataIndex: 'ApprovalNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '剂型',
          dataIndex: 'DosageFormName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '生产日期',
          dataIndex: 'ProductionTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '折扣单价',
          dataIndex: 'DiscountPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' }
        }
      ],
      httpHead: 'P36008',
      url: {
        getOrderOutbounds: '/{v}/Order/OrderOutbounds',
        getSalesOrderOutboundBillsFiles: '/{v}/Order/GetSalesOrderOutboundBillsFiles'
      }
    }
  },
  computed: {},
  created() {},
  mounted() {
    // console.log(this.Info)

    if (this.Info) {
      this.model = {
        OrderId: this.Info['Id'],
        CustomerName: this.Info['CustomerName'],
        OrderOutbounds: []
      }
      this.loadDetailInfo()
    }
  },
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.getOrderOutbounds, { orderId: that.model.OrderId }, that.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            if (res.Data || [].length) {
              res.Data.map(v => {
                v.loadingFileUrl = false
              })
            }
            that.$set(that.model, 'OrderOutbounds', res.Data || [])
            // console.log(that.model.OrderOutbounds)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // 查看电子版出库单
    handelFileUrlsClick(item) {
      // console.log(item.Id)
      // window.open(FileUrls[0])
      item.loadingFileUrl = true
      // if(!item.Id) return
      getAction(this.url.getSalesOrderOutboundBillsFiles, { billsId: item.Id }, this.httpHead).then(res => {
        item.loadingFileUrl = false
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        if (res.Count) {
          this.$refs.iPdfModal.show('查看电子版出库单', res.Data)
        } else {
          this.$message.warning('暂无出库单！')
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
</style>