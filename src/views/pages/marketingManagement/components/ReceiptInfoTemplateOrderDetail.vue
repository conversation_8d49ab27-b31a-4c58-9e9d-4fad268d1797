
<!-- 商销商品订单详情 回执单信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card
        :bordered="false"
        :body-style="{padding:0}"
      >
        <!-- 回执单信息 -->
        <a-descriptions
          title="回执单信息"
          :column="1"
        >
          <a-descriptions-item label="上传时间">{{ ((model.SalesOrderReceiptFiles || []).length&&model.SalesOrderReceiptFiles[0].UploadTime )|| ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="">
            <ViewerImage
              :images="model.SalesOrderReceiptFiles.map(v=>v.FileUrl)"
              v-if="(model.SalesOrderReceiptFiles || []).length"
            />
            <span v-else> -- </span>
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions
          title="签章信息"
          :column="1"
        >
          <a-descriptions-item label="签章时间">{{ model.SignTime || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="">
            <ViewerImage
              :images="[model.SignFileUrl]"
              v-if="model.SignFileUrl"
            />
            <span v-else> -- </span>
          </a-descriptions-item>
        </a-descriptions>

      </a-card>

    </a-spin>

  </div>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销商品订单详情 回执单信息',
  name: 'ReceiptInfoTemplateOrderDetail',
  mixins: [SimpleMixin],
  components: {},
  props: {
    Info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      loading: false,
      md: 8,
      model: {},
      httpHead: 'P36008',
      url: {
        getSalesOrderReceipts: '/{v}/Order/GetSalesOrderReceipts'
      }
    }
  },
  computed: {},
  created() {},
  mounted() {
    // console.log(this.Info)

    if (this.Info) {
      this.loadDetailInfo()
    }
  },
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.getSalesOrderReceipts, { orderId: that.Info.Id }, that.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    }
  }
}
</script>

<style lang="less" scoped>
</style>