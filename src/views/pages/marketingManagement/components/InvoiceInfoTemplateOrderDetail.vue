<!-- 商销商品订单详情 发票信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card :bordered="false" :body-style="{ padding: 0 }">
        <a-collapse v-model="activeKey" v-if="(model.OrderInvoices || []).length">
          <a-collapse-panel :key="'' + (index + 1)" :header="`开票单号：${item.Code || ' -- '}`" v-for="(item, index) in model.OrderInvoices">
            <a slot="extra">{{ activeKey.some((v) => v == index + 1) ? '收起' : '展开' }}</a>
            <a-descriptions title="发票信息">
              <a-descriptions-item label="申请类型">{{ item.InvoiceApplyTypeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="发票类型">{{ item.InvoiceTypeV2Str || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="增值税一般纳税人申请表">
                <ImageControl v-if="item.TaxpayerApplicationForm" :src="item.TaxpayerApplicationForm" :width="30"></ImageControl>
              </a-descriptions-item>
              <a-descriptions-item label="公司名称">{{ item.Title || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="纳税人识别号">{{ item.TaxNumber || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="注册地址">{{ item.RegAreaFullName || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="注册电话">{{ item.RegLinkPhone || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="开户银行">{{ item.BankName || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="银行账户">{{ item.BankCardNumber || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="电子邮箱">{{ item.RecipientEmail || ' -- ' }}</a-descriptions-item>
            </a-descriptions>
            <!-- 单据信息 -->
            <a-row :gutter="gutter" style="margin-bottom: 24px">
              <a-col :span="24">
                <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">单据信息</div>
              </a-col>
              <a-col :span="24">
                <!-- 列表 -->
                <SimpleTable ref="tableBills" :tab="tab" :columns="columns" :tableDatas="item['Details'] || []" :expandedRowKeysArray='expandedRowKeysArray' @operate="operate">
                  <span slot="commonContent" slot-scope="{ text }">
                    <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                  </span>
                  <!-- 开票金额 -->
                  <span slot="BillsAmount" slot-scope="{ text, record }">
                    <j-ellipsis :value="`${
                    record.BillsType == 22 ? '￥-' + Number(text).toFixed(2) : '￥' + Number(text).toFixed(2)
                  }`" :length="15" />
                  </span>
                </SimpleTable>
              </a-col>
            </a-row>
            <!-- 开票信息 -->
            <a-row :gutter="gutter" style="margin-bottom: 24px">
              <a-col :span="24">
                <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">开票信息</div>
              </a-col>
              <a-col :span="24">
                <!-- 列表 -->
                <SimpleTable ref="table" :tab="tabInvoice" :tableDatas="item['Results'] || []" :columns="columnsInvoice" @operate="operate">
                  <span slot="commonContent" slot-scope="{ text }">
                    <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                  </span>
                </SimpleTable>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
        <a-empty v-else />
      </a-card>
    </a-spin>
    <!-- 商品明细 -->
    <CommodityDetailListModal ref="iCommodityDetailListModal" />
  </div>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { ListMixin } from '@/mixins/ListMixin'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
import { Item } from 'ant-design-vue/es/vc-menu'
export default {
  description: '商销商品订单详情 发票信息',
  name: 'InvoiceInfoTemplateOrderDetail',
  mixins: [SimpleMixin, ListMixin],
  components: { JEllipsis },
  props: {
    Info: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      loading: false,
      md: 8,
      gutter: 20,
      model: {},
      activeKey: [1],
      tab: {
        rowKey: 'Id',
        sortArray: ['BillsAmount'],
      },
      expandedRowKeysArray: [],
      columns: [
        {
          title: '单据编号',
          dataIndex: 'BillsNo',
          ellipsis: true,
          width: 180,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据类型',
          dataIndex: 'BillsTypeStr',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'OrderNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单付款方式',
          dataIndex: 'PaymentModeStr',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BillsTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '金额',
          dataIndex: 'BillsAmount',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'BillsAmount' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          actionBtn: [{ name: '商品明细', icon: '', id: '25148b15-c8b4-48d6-ae47-b4c8b563dd1d' }],
          scopedSlots: { customRender: 'action' },
        },
      ],
      tabInvoice: {},
      columnsInvoice: [
        {
          title: '开票编号',
          dataIndex: 'MakeInvoiceNo',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票流水号',
          dataIndex: 'MakeInvoiceRunningNo',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票号',
          dataIndex: 'InvoiceNo',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发票代码',
          dataIndex: 'InvoiceCode',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票金额',
          dataIndex: 'InvoiceAmount',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '开票时间',
          dataIndex: 'InvoiceTime',
          ellipsis: true,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '发票状态',
          dataIndex: 'InvoiceStatusStr',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 120,
          actionBtn: [
            {
              name: '查看发票',
              icon: '',
              id: '68e29a82-47dc-4d48-8227-4adb3468bed9',
              specialShowFuc: (e) => {
                let record = e || {}
                return (record.InvoiceFileUrl && record.InvoiceFileUrl.indexOf('http') > -1) ? true : false
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      httpHead: 'P36008',
      url: {
        getOrderInvoices: '/{v}/Order/OrderInvoices',
      },
    }
  },
  computed: {},
  created() { },
  mounted() {
    // console.log(this.Info)

    if (this.Info) {
      this.model = {
        OrderId: this.Info['Id'],
      }
      this.loadDetailInfo()
    }
  },
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.getOrderInvoices, { orderId: that.model.OrderId }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            // 重组数据
            if (res.Data.length > 0) {
              res.Data.map(item => {
                item.Details = this.setBillsData(item.Details) || []
                this.expandedRowKeysArray = item.Details.length > 0 ? [item.Details[0].Id] : []
              })
            }
            that.$set(that.model, 'OrderInvoices', res.Data || [])
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    setBillsData(data) {
      let goods = []
      data.map((v) => {
        let item = {
          Id: v['Id'],
          BillsNo: v['BillsCode'],
          BillsType: v['BillsType'],
          BillsTypeStr: v['BillsTypeStr'],
          OrderId: v['OrderId'],
          OrderNo: v['SalesOrderCode'],
          PaymentMode: v['SalesOrderPaymentMode'],
          PaymentModeStr: v['SalesOrderPaymentModeStr'],
          BillsTime: v['BillsTime'],
          BillsAmount: v['BillsAmount'],
          children: [],
          ParentCode: v['ParentCode'],
        }
        let childs =
          data.filter(
            (j) => j['SalesOrderCode'] == item['OrderNo'] && j['ParentCode'] == v['BillsCode'] && j.BillsType != 21
          ) || []
        childs.map((j) => {
          item['children'].push({
            Id: j['Id'],
            BillsNo: j['BillsCode'],
            BillsType: j['BillsType'],
            BillsTypeStr: j['BillsTypeStr'],
            OrderId: j['OrderId'],
            OrderNo: j['SalesOrderCode'],
            PaymentMode: j['SalesOrderPaymentMode'],
            PaymentModeStr: j['SalesOrderPaymentModeStr'],
            BillsTime: j['BillsTime'],
            BillsAmount: j['BillsAmount'],
            ParentCode: j['ParentCode'],
          })
        })

        if ((item.children || []).length < 1) {
          item.children = null
        }
        if (item.BillsType == 21) {
          if ((item.children || []).length >= 1) item['children'].sort(this.sortUpDate) //反序
          goods.push(item)
        } else if (!item.ParentCode) {
          goods.push(item)
        }
      })
      return goods
    },
    // 列表操作
    operate(record, type) {
      if (type == '商品明细') {
        this.$refs.iCommodityDetailListModal.show(record)
      } else if (type == '查看发票') {
        if (record['InvoiceFileUrl'] && record['InvoiceFileUrl'].indexOf('http') > -1) {
          window.open(record['InvoiceFileUrl'])
          // this.getFileTempUrl(record['InvoiceFileUrl'], (data) => {
          //   if (data && typeof (data) == 'string') {
          //     window.open(data)
          //   } else {
          //     window.open(data.TempUrl)
          //   }
          // })
        } else if (record['InvoiceFileUrl']) {
          this.$message.warning('文件地址不正确')
        } else {
          this.$message.warning('暂无文件')
        }
      }
    },
  },
}
</script>

<style lang="less" scoped></style>
