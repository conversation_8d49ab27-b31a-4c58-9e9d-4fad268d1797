<!-- 商销商品订单详情 售后信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card :bordered="false" :body-style="{ padding: 0 }">
        <a-collapse v-model="activeKey" v-if="(model.SaleAfterOrders || []).length">
          <a-collapse-panel :key="'' + (index + 1)" v-for="(item, index) in model.SaleAfterOrders">
            <template slot="header">
              退款编号: {{ item.Code || ' -- ' }}
              <YYLButton text="查看售后详情" type="primary" style="margin-left: 6px" @click="onDetailClick('merchantAfterSaleListDetail', { id: item.Id })" />
            </template>
            <a slot="extra">{{ activeKey.some((v) => v == 1) ? '收起' : '展开' }}</a>
            <a-descriptions title="退款信息">
              <a-descriptions-item label="退款金额">￥{{ item.RefundAmount || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="退款申请时间">{{ item.CreateTime || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="退款类型">{{ item.SaleAfterTypeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="退款范围">{{ item.SaleAfterScopeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="退款原因">{{ item.RefundReason.toString() || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="退款方式">{{ item.SaleAfterRefundTypeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="原因备注">{{ item.Remarks || ' -- ' }}</a-descriptions-item>
            </a-descriptions>
            <!-- 调价商品 -->
            <a-row :gutter="gutter" style="margin-bottom: 24px">
              <a-col :span="24">
                <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">退款商品</div>
              </a-col>
              <a-col :span="24">
                <!-- 列表 -->
                <SimpleTable ref="table" :tab="tab" :columns="columns" :tableDatas="item['SaleAfterOrderGoodses'] || []">
                  <span slot="commonContent" slot-scope="{ text }">
                    <j-ellipsis :value="text == 0 || text ? String('￥'+text) : '--'" />
                  </span>
                </SimpleTable>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
        <a-empty v-else />
      </a-card>
    </a-spin>
  </div>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销商品订单详情 售后信息',
  name: 'AfterSalesInfoTemplateOrderDetail',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  props: {
    Info: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      loading: false,
      md: 8,
      gutter: 20,
      model: {
        SaleAfterOrders: [],
      },
      activeKey: [1],
      tab: {
        bordered: true,
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 200,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 120,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 100,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 100,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 100,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },

        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 120,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
            return obj;
          },
        },
        {
          title: '有效期',
          dataIndex: 'GoodsPeriod',
          ellipsis: true,
          width: 150,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
            return obj;
          },
        },
        {
          title: '是否赠品',
          dataIndex: 'IsGift',
          ellipsis: true,
          width: 100,
          // customRender: (t, r, i) => {
          //   return t == null ? '' : t ? '是' : '否'
          // },
          customRender: (text, record, index) => {
            const obj = {
              children: (<span>{text == null ? '' : text ? '是' : '否'}</span>),
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '进价',
          dataIndex: 'GoodsPurchaseCostPrice',
          ellipsis: true,
          width: 100,
          // scopedSlots: { customRender: 'commonSlot' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String('￥' + text) : ''} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
            return obj;
          },
        },
        {
          title: '销售单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 100,
          // scopedSlots: { customRender: 'commonSlot' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String('￥' + text) : ''} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
            return obj;
          },
        },
        {
          title: '出库数量',
          dataIndex: 'Count',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : ''} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanBatchNo
            return obj;
          },
        },
        {
          title: '调价后单价',
          dataIndex: 'AdjustPrice',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
          // customRender: (text, record, index) => {
          //   const obj = {
          //     children: <span>{text}</span>,
          //     attrs: {},
          //   }

          //   obj.attrs.rowSpan = record.rowSpan || 1
          //   if (record.rowSpan == 0) {
          //     obj.attrs.colSpan = 0
          //   }
          //   return obj
          // },
        },
        {
          title: '已退款数量',
          dataIndex: 'ExsitSaleAfterCount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '可退款数量',
          dataIndex: 'CanSaleAfterCount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退货数量',
          dataIndex: 'SaleAfterCount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退款金额',
          dataIndex: 'GoodsRefundTotalAmount',
          ellipsis: true,
          width: 120,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
        },
      ],
      httpHead: 'P36008',
      url: {
        getSaleAfterOrders: '/{v}/SaleAfterOrder/GetSaleAfterOrders',
      },
    }
  },
  computed: {},
  created() { },
  mounted() {
    // console.log(this.Info)

    if (this.Info) {
      this.loadDetailInfo()
    }
  },
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.getSaleAfterOrders, { orderId: that.Info.Id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data && res.Data.length > 0) {
              res.Data.forEach((item, index) => {
                // 列合并处理数据
                let arr = this.getListRowSpanData(item.SaleAfterOrderGoodses || [], '', 'ErpGoodsCode', 'rowSpan') || []
                arr.map(item => {
                  item['ErpGoodsCodeAndGoodsBatchNumber'] = (item.ErpGoodsCode || '') + (item.GoodsBatchNumber || '')
                })
                let arr1 = this.getListRowSpanData(arr, 'ErpGoodsCode', 'ErpGoodsCodeAndGoodsBatchNumber', 'rowSpanGoodsBatchNumber', 'GoodsBatchNumber') || []
                // let arr1 = this.getListRowSpanData(arr, 'ErpGoodsCode', 'GoodsBatchNumber', 'rowSpanGoodsBatchNumber', 'GoodsBatchNumber') || []
                // let arr2 = this.getListRowSpanData(arr1, 'GoodsBatchNumber', 'BatchNo', 'rowSpanBatchNo', 'BatchNo') || []
                item.SaleAfterOrderGoodses = arr1
              })
              that.$set(that.model, 'SaleAfterOrders', res.Data || [])
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },

  },
}
</script>

<style lang="less" scoped></style>
