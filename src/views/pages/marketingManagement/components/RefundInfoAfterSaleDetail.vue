<!-- 售后详情 退款信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card :bordered="false" :body-style="{ padding: 0 }">
        <a-collapse v-model="activeKey" v-if="(model.SaleAfterRefunds || []).length">
          <a-collapse-panel
            :key="'' + (i + 1)"
            :header="item.isRenKuan == true ? '认款' : item.SaleAfterRefundOrderTypeStr"
            v-for="(item, i) in model.SaleAfterRefunds"
          >
            <a slot="extra">{{ activeKey.some((v) => v == 1) ? '收起' : '展开' }}</a>
            <a-descriptions title="" :column="1">
              <template v-if="item.isRenKuan == true">
                <a-descriptions-item label="认款金额">￥{{ item.RecognitionAmount }}</a-descriptions-item>
                <a-descriptions-item label="收款方式">{{ item.ReceiptTypeStr || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="银行名称">{{ item.BankName || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="银行卡号">{{ item.AccountNumber || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="打款方">{{ item.Payer || '--' }}</a-descriptions-item>
                <a-descriptions-item label="打款时间">{{ item.ReceiptTime || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="认款时间">{{ item.RecognitionTime || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="备注">{{ item.Note || ' -- ' }}</a-descriptions-item>
              </template>
              <template v-else>
                <!-- 销货对抵 -->
                <template v-if="item.SaleAfterRefundOrderType == 0">
                  <a-descriptions-item label="对抵金额">{{ item.RefundAmount || ' -- ' }}</a-descriptions-item>
                  <a-descriptions-item label="对抵时间">{{ item.RefundTime || ' -- ' }}</a-descriptions-item>
                </template>
                <!-- 出纳打款 -->
                <template v-else>
                  <a-descriptions-item label="打款人">{{ item.RefundUser || ' -- ' }}</a-descriptions-item>
                  <a-descriptions-item label="打款时间">{{ item.RefundTime || ' -- ' }}</a-descriptions-item>
                  <a-descriptions-item label="打款金额">￥{{ item.RefundAmount || ' -- ' }}</a-descriptions-item>
                  <a-descriptions-item label="打款凭证">
                    <ImageControl :src="item.RefundVoucher" :width="100"></ImageControl>
                  </a-descriptions-item>
                </template>
              </template>
            </a-descriptions>
          </a-collapse-panel>
        </a-collapse>
        <a-empty v-else />
      </a-card>
    </a-spin>
  </div>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '售后详情 退款信息',
  name: 'RefundInfoAfterSaleDetail',
  mixins: [SimpleMixin],
  components: {},
  props: {
    Info: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      loading: false,
      md: 8,
      model: {},
      activeKey: ['1'],
      httpHead: 'P36008',
      url: {
        getSaleAfterRefunds: '/{v}/SaleAfterOrder/GetSaleAfterRefunds',
        getRecognitionSales: '/{v}/SaleAfterOrder/GetRecognitionSales',
      },
    }
  },
  computed: {},
  created() {},
  mounted() {
    // console.log(this.Info)

    if (this.Info) {
      this.model = Object.assign({}, this.Info)
      this.loadDetailInfo()
    }
  },
  methods: {
    loadDetailInfo() {
      if (!this.model.Id) return
      getAction(this.url.getSaleAfterRefunds, { id: this.model.Id }, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        // this.$set(this.model, 'SaleAfterRefunds', res.Data || [])
        this.loadRecognitionSales(res.Data || [])
      })
    },
    loadRecognitionSales(list) {
      if (!this.model.Id) return
      getAction(this.url.getRecognitionSales, { id: this.model.Id }, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        let newList = res.Data || []
        newList.forEach((x) => {
          x.isRenKuan = true
        })
        this.$set(this.model, 'SaleAfterRefunds', list.concat(newList))
      })
    },
  },
}
</script>

<style lang="less" scoped></style>
