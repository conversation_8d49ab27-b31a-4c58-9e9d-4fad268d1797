
<!-- 售后详情 基本信息 -->
<template>
  <div :class="'form-model-style'">
    <a-spin :spinning="loading">
      <a-card :bordered="false" :body-style="{padding:0}">
        <!-- 订单信息 -->
        <a-descriptions title="订单信息">
          <a-descriptions-item label="客户名称">{{ orderDetail.CustomerName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="客户类别">{{ orderDetail.CustomerTypeStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="订单类型">{{ orderDetail.TypeStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="收货人">{{ orderDetail.ReceivingName || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ orderDetail.ReceivingPhone || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="收货地址">{{ (orderDetail.ReceivingAreaFullName || ' -- ') + '' + (orderDetail.ReceivingAddress || '') }}</a-descriptions-item>
          <a-descriptions-item label="快递公司">{{ orderDetail.ExpressCompanyStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="订单金额">￥{{ orderDetail.TotalAmount || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="订单编号"><a @click="goOrderInfo">{{ orderDetail.Code || ' -- ' }}</a></a-descriptions-item>
          <a-descriptions-item label="下单时间">{{ orderDetail.PlaceOrderTime || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="付款方式">{{ orderDetail.PaymentModeStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="审核状态">{{ orderDetail.AuditStatusStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="订单状态">{{ orderDetail.OrderStatusStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="开票状态">{{ orderDetail.InvoiceStatusStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="认款状态">{{ orderDetail.OrderAcceptanceStatusStr || ' -- ' }}</a-descriptions-item>
        </a-descriptions>
        <!-- 售后信息 -->
        <a-descriptions title="售后信息">
          <a-descriptions-item label="售后编号">{{ model.Code || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="售后申请时间">{{ model.CreateTime || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="退款金额">￥{{ model.RefundAmount || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="售后范围">{{ model.SaleAfterScopeStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="退款类型">{{ model.SaleAfterTypeStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="退款原因">{{ (model.RefundReason || ' -- ')  }}</a-descriptions-item>
          <a-descriptions-item label="退款方式">{{ model.SaleAfterRefundTypeStr || ' -- ' }}</a-descriptions-item>
          <a-descriptions-item label="备注">{{ model.Remarks || ' -- ' }}</a-descriptions-item>
        </a-descriptions>
        <!-- 售后商品 -->
        <a-row :gutter="20" style="margin-bottom:24px">
          <a-col :span="24">
            <div style="font-size: 16px; font-weight: 600;margin:0 0 15px 0">
              售后商品
            </div>
          </a-col>
          <a-col :span="24">
            <!-- 列表 -->
            <SimpleTable ref="tableGoods" :tab="tabGoods" :columns="columnsGoods">
              <span slot="commonContent" slot-scope="{text}">
                <j-ellipsis :value="text == 0 || text ? String('￥'+text) : '--'" />
              </span>
            </SimpleTable>

          </a-col>

        </a-row>
      </a-card>

    </a-spin>

  </div>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '售后详情 基本信息',
  name: 'BasicInfoTemplateAfterSaleDetail',
  mixins: [SimpleMixin],
  components: { JEllipsis },
  props: {
    Info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      loading: false,
      md: 8,
      model: {},
      orderDetail: {},
      tabGoods: {
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        operateBtn: [],
        hideIpagination: true,
        bordered: true,
      },
      columnsGoods: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 200,
          // scopedSlots: { customRender: 'component' }
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 150,
          // scopedSlots: { customRender: 'component' }
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 120,
          // scopedSlots: { customRender: 'component' }
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 60,
          // scopedSlots: { customRender: 'component' }
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          // scopedSlots: { customRender: 'component' }
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 60,
          // scopedSlots: { customRender: 'component' }
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpan
            return obj;
          },
        },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 120,
          // scopedSlots: { customRender: 'component' }
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
            return obj;
          },
        },
        {
          title: '批次号',
          dataIndex: 'BatchNo',
          ellipsis: true,
          width: 200,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '仓库',
          dataIndex: 'WarehouseCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产日期',
          dataIndex: 'ProductionDate',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'dayTime' },
          // customRender: (text, record, index) => {
          //   const obj = {
          //     children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
          //     attrs: {},
          //   };
          //   obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
          //   return obj;
          // },
        },
        {
          title: '有效期',
          dataIndex: 'GoodsPeriod',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'dayTime' },
          // customRender: (text, record, index) => {
          //   const obj = {
          //     children: (<j-ellipsis value={text ? String(text) : '--'} />),
          //     attrs: {},
          //   };
          //   obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
          //   return obj;
          // },
        },
        // {
        //   title: '是否赠品',
        //   dataIndex: 'IsGift',
        //   ellipsis: true,
        //   width: 100,
        //   // customRender: (t, r, i) => {
        //   //   return t == null ? '' : t ? '是' : '否'
        //   // }
        //   customRender: (text, record, index) => {
        //     const obj = {
        //       children: (<span>{text == null ? '' : text ? '是' : '否'}</span>),
        //       attrs: {},
        //     };
        //     obj.attrs.rowSpan = record.rowSpan
        //     return obj;
        //   },
        // },
        // {
        //   title: '进价',
        //   dataIndex: 'GoodsPurchaseCostPrice',
        //   ellipsis: true,
        //   width: 100,
        //   // scopedSlots: { customRender: 'commonSlot' },
        //   customRender: (text, record, index) => {
        //     const obj = {
        //       children: <j-ellipsis value={text == 0 || text ? String('￥' + text) : '--'} />,
        //       attrs: {},
        //     };
        //     obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
        //     return obj;
        //   },
        // },
        {
          title: '销售单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
          // customRender: (text, record, index) => {
          //   const obj = {
          //     children: <j-ellipsis value={text == 0 || text ? String('￥' + text) : '--'} />,
          //     attrs: {},
          //   };
          //   obj.attrs.rowSpan = record.rowSpanGoodsBatchNumber
          //   return obj;
          // },
        },
        {
          title: '出库数量',
          dataIndex: 'Count',
          ellipsis: true,
          width: 100,
          // scopedSlots: { customRender: 'component' },
          customRender: (text, record, index) => {
            const obj = {
              children: <j-ellipsis value={text == 0 || text ? String(text) : '--'} />,
              attrs: {},
            };
            obj.attrs.rowSpan = record.rowSpanBatchNo
            return obj;
          },
        },
        {
          title: '调价后单价',
          dataIndex: 'AdjustPrice',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '已退款数量',
          dataIndex: 'ExsitSaleAfterCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '可退款数量',
          dataIndex: 'CanSaleAfterCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退货数量',
          dataIndex: 'SaleAfterCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退款金额',
          dataIndex: 'GoodsRefundTotalAmount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'commonSlot' }
        }
      ],
      httpHead: 'P36008',
      url: {
        detail: '/{v}/Order/OrderDetail'
      }
    }
  },
  computed: {},
  created() { },
  mounted() {
    if (this.Info) {
      this.model = Object.assign({}, this.Info)
      // 列合并处理数据
      let arr = this.getListRowSpanData(this.model.SaleAfterOrderGoodses || [], '', 'ErpGoodsCode', 'rowSpan') || []
      arr.map(item => {
        item['ErpGoodsCodeAndGoodsBatchNumber'] = (item.ErpGoodsCode || '') + (item.GoodsBatchNumber || '')
      })
      let arr1 = this.getListRowSpanData(arr, 'ErpGoodsCode', 'ErpGoodsCodeAndGoodsBatchNumber', 'rowSpanGoodsBatchNumber', 'GoodsBatchNumber') || []
      // let arr1 = this.getListRowSpanData(arr, 'ErpGoodsCode', 'GoodsBatchNumber', 'rowSpanGoodsBatchNumber', 'GoodsBatchNumber') || []
      // let arr2 = this.getListRowSpanData(arr1, 'GoodsBatchNumber', 'BatchNo', 'rowSpanBatchNo', 'BatchNo') || []
      if (this.$refs.tableGoods) this.$refs.tableGoods.dataSource = arr1 || []
      this.loadDetailInfo()
    }
  },
  methods: {
    loadDetailInfo() {
      if (!this.model.OrderId) return
      getAction(this.url.detail, { id: this.model.OrderId }, this.httpHead).then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.orderDetail = res.Data || {}
      })
    },
    // 商销订单详情
    goOrderInfo() {
      this.onDetailClick('/pages/marketingManagement/ordersManagementListDetail', {
        id: this.model.OrderId,
      })
    },
  }
}
</script>

<style lang="less" scoped>
</style>