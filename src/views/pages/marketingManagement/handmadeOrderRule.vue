<!--
 * @Description: wenjingGao Create File 手工单销售单价规则
 * @Version: 1.0
 * @Author: wenjingGao
 * @Date: 2025-02-06 11:23:29
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-04-08 14:02:11
-->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea 
      ref="SimpleSearchArea" 
      :searchInput="searchInput" 
      @searchQuery="searchQuery" 
    />
    <!-- 列表 -->
    <SimpleTable 
      ref="table" 
      showTab 
      cardBordered 
      isHandleTableChange 
      :tab="tab" 
      :linkHttpHead="linkHttpHead" 
      :linkUrl="linkUrl" 
      :linkUrlType="linkUrlType" 
      :queryParam="queryParam" 
      :columns="columns" 
      :isTableInitData="isTableInitData"  
      @handleTableChange="handleTableChange" 
      @operate="operate"
    />
    <!-- 设置销售单价规则 -->
    <SalesUnitPriceRuleModal 
      ref="iSalesUnitPriceRuleModal" 
      :title="title" 
      @handleOk="handleOkSuccess"
    />
  </a-row>
</template>

<script>
import { getAction, putAction, postAction, deleteAction } from "@/api/manage";

export default {
  description: '手工单销售单价规则',
  name: 'handmadeOrderRule',
  data() {
    return {
      searchInput: [
        {
          name: '客户信息',
          type: 'input',
          value: '',
          key: 'CustomerInfo',
          defaultVal: '',
          placeholder: '请输入客户名称/编号/拼音码',
        },
        {
          name: '创建人',
          type: 'input',
          value: '',
          key: 'CreateByName',
          defaultVal: '',
          placeholder: '请输入',
        },
        {
          name: '创建时间',
          type: 'timeInput',
          value: null,
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          rangeDate: [],
          placeholder: ['开始日期', '结束日期'],
        }
      ],
      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          {
            name: '新增销售单价规则',
            type: 'primary',
            icon: 'plus',
            key: '新增销售单价规则',
            id: '32fd67a3-000e-4a1d-95bb-16b7127c3b2f',
          }
        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      queryParam: {},
      columns: [
        {
          title: '客户名称',
          dataIndex: 'CustomerNames',
          width: 250,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '销售单价规则',
          dataIndex: 'PriceRuleDisplay',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 280,
          fixed: 'right',
          actionBtn: [
            {
              name: '编辑',
              icon: '',
              id: '4db5847d-16d9-400d-bc59-e52913f37236',
              specialShowFuc: (e) => {
                // let record = e || {}
                // return [0, 3].includes(record.AuditStatus)
                return true
              },
            },
            {
              name: '删除',
              icon: '',
              id: '26c68a81-e7c4-4f78-91c7-441b484e8b82',
              specialShowFuc: (e) => {
                // let record = e || {}
                // return [0, 3].includes(record.AuditStatus)
                return true
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/SalesOrderManualPriceRule/PageList',
        deleteOrderRule: '/{v}/SalesOrderManualPriceRule',
      },
      tableDatas: [{
        CustomerName: '客户1'
      }],
      title: '新增销售单价规则'
    }
  },
  mounted() {

  },
  activated() {
    this.queryParam = this.$refs.SimpleSearchArea.queryParam
    this.initData()
  },
  methods: {
    initData() {
      this.$refs.table.loadDatas(null, this.queryParam)
    },
    searchQuery(queryParam, type) {
      console.log('searchQuery', queryParam, type)
      let params = queryParam ? JSON.parse(JSON.stringify(queryParam)) : this.$refs.SimpleSearchArea.queryParam
      this.$refs.table.loadDatas(1, params)
    },
    // 分页回调
    handleTableChange() {
      let params = this.$refs.table.getQueryParams()
      this.$refs.table.loadDatas(null, params)
    },
    // 列表操作
    operate(record, type) {
      const that = this
      switch (type) {
        case '新增销售单价规则':
          that.title = '新增销售单价规则'
          that.$refs.iSalesUnitPriceRuleModal.show()
          break
        case '编辑':
          that.title = '编辑销售单价规则'
          that.$refs.iSalesUnitPriceRuleModal.show(record.Id)
          break
        case '删除':
          this.$confirm({
            title: '删除手工单销售单价规则',
            content: '您确定要删除该笔手工单销售单价规则吗？',
            onOk() {
              that.deleteOrderRule(record.Id)
            },
            onCancel() { },
          })
          break
      }
    },
    deleteOrderRule(Id) {
      if (!Id) return
      deleteAction(`${this.linkUrl.deleteOrderRule}/${Id}`, {}, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        let params = this.$refs.table.getQueryParams()
        this.$refs.table.loadDatas(1, params)
      })
    },
    handleOkSuccess() {
      // console.log('this.queryParam', this.queryParam)
      // this.$refs.table.loadDatas(1, this.queryParam)
      let params = this.$refs.table.getQueryParams()
      this.$refs.table.loadDatas(1, params)
    }
  }
}
</script>
