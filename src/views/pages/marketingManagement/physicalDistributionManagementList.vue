<template>
  <div>
    <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
    <SimpleSearchView ref="searchView" :searchParamsList="searchItems" @search="searchQuery" />
    <!-- 列表 -->
    <TableNewView ref="tableView" :table="table" :columns="columns" :tabDataList="tabDataList" :dataList="dataSource" @operate="operate"/>
    <!-- 编辑 -->
    <physicalDistributionAddModal ref="physicalDistributionAddModal" @ok="modalFormOk"></physicalDistributionAddModal>
  </div>
</template>
  <script>

import { ListMixin } from '@/mixins/ListMixin'
import { PublicMixin } from '@/mixins/PublicMixin'//非必要 公用的 有需要时候用其他mixin也行
import { getAction, postAction, putAction } from '@/api/manage'
export default {
  title: '物流管理',
  name: 'physicalDistributionManagementList',
  mixins: [ListMixin, PublicMixin],
  components: {},
  data() {
    return {
      doctorRole: null,
      searchItems: [
        {
          name: '物流商名称', //输入框名称 必填
          type: this.$SEnum.INPUT, //类型 必填
          key: 'keyword', //搜索key 必填
          placeholder: '请输入',
        }
      ],
      queryParam: {
      },
      table: {
        curStatus: 1,
        tabStatusKey: 'ApproavalStatus',
        operateBtns: [
          {
            name: '同步',
            type: 'primary',
            icon: '',
            key: '同步',
          },
        ], //右上角按钮集合
        rowKey: 'ExpressCompanyId',
        tabTitles: ['查询结果'],
        showPagination: false
      },
      tabDataList: [
      ],
      columns: [
        {
          title: '物流商名称',
          dataIndex: 'ExpressCompanyName',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '适用区域',
          dataIndex: 'AreaFullNames',
          ellipsis: true
        },
        {
          title: '启用/禁用',
          dataIndex: 'IsEnabled',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'isValid' },
          onChange: async (record, checked) => {
            const params = {
              ShipperCode: record.ExpressCompanyCode,
              IsEnabled: checked,
            }
            const res = await postAction('/{v}/LogisticsOrder/UpdateShipperStatus', params, 'P36008')
            if (!res.IsSuccess) {
              this.$message.warning(res.Msg)
              record.IsEnabled = !checked
              return
            }
            this.loadData()
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
          actionBtns: [
            {
              name: '编辑',
              isShow: (record) => {
                return true
              },
            }
          ],
        },

      ],
      httpHead: 'P36008',
      url: {
        listType: 'GET', //列表接口请求类型
        list: '/{v}/LogisticsOrder/GetShipperAreas',//列表数据接口
        syncShippers: '/{v}/LogisticsOrder/SyncShippers',//同步物流商
      },
    }
  },
  methods: {
    // 操作
    operate(record, key, index) {
      if (key == '编辑') {
        this.$refs.physicalDistributionAddModal.edit(record)
      } else if (key == '同步') {
        postAction(this.url.syncShippers, {}, this.httpHead).then((res) => {
          if (!res.IsSuccess) {
            this.$message.warning(res.Msg)
            return
          }
          this.$message.success('同步成功')
          this.loadData(1)
        })
      }
    },
    // 物流商区域编辑完成回调
    editOk() {
    }
  },
}
  </script>
  <style scoped>

</style>
  