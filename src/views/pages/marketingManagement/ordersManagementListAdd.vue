<!-- 新建订单 -->
<template>
  <div>
    <a-alert type="error" :message="`驳回原因：${model.AuditOpinion || ' -- '}`" banner :showIcon="false" v-if="model.AuditOpinion && model.Id" />
    <a-card style="margin: 15px 0;padding-bottom:100px;">
      <a-spin :spinning="spinning">
        <div :class="'form-model-style'">
          <a-spin :spinning="loading">
            <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical" v-if="[1, 2].includes(opType)">
              <!-- 客户信息 -->
              <template>
                <a-row :gutter="gutter">
                  <a-col :span="24">
                    <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">客户信息</div>
                  </a-col>
                  <a-col :span="md">
                    <a-form-model-item label="客户名称：" prop="CustomerId">
                      <SingleChoiceSearchView style="width: 100%" placeholder="请输入客户名称/客户编号" :httpParams="{ PageIndex: 1, PageSize: 100,PushERPStatus:5 }" httpHead="P36002" httpType="GET" keyWord="KeyWord" :Url="url.getCustomerListForOrder" v-model="model.CustomerId" :name="model.CustomerName" @change="handleCustomer" />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="md">
                    <a-form-model-item label="客户类别：" prop="CustomerTypeStr">
                      <!-- <SingleChoiceSearchView
                        style="width: 100%"
                        placeholder="请选择"
                        :httpParams="{groupPY: 'kehulb'}"
                        dataKey: { name: 'ItemValue', value: 'Id' },
                        httpHead="P36001"
                        httpType="GET"
                        keyWord="Name"
                        Url="/{v}/Global/GetListDictItemForCustomer"
                        v-model="model.CustomerType"
                      /> -->
                      <a-input placeholder="请输入" v-model="model.CustomerTypeStr" disabled />
                    </a-form-model-item>
                  </a-col>
                  <!-- <a-col :span="md">
                    <a-form-model-item
                      label="订单类型："
                      prop="Type"
                    >
                      <EnumSingleChoiceView
                        style="width: 100%;"
                        placeholder="请选择"
                        dictCode="EnumSalesOrderType"
                        v-model="model.Type"
                        @change="(value,txt)=>{model.TypeStr = txt}"
                      />
                    </a-form-model-item>
                  </a-col> -->
                  <a-col :span="md">
                    <a-form-model-item label="委托人" prop="DelegateId">
                      <SingleChoiceView ref="singleChoiceViewWTR" style="width: 100%" :key="model.CustomerId" placeholder="请选择" :httpParams="{ customerId: model.CustomerId,orderType:orderType }" httpHead="P36008" :dataKey="{ name: 'DelegateName', value: 'DelegateId' }" v-model="model.DelegateId" :disabled="!model.CustomerId" :Url="(model.CustomerId && orderType)?'/{v}/Order/GetCustomerDelegateScopes':''" @dataLoaded="(data)=>onLoadSuccess(data,'wtr')" @change="changeWTR" />
                      <!-- <span slot=" help" class="cred" v-if="model.DelegateErr">{{ model.DelegateErr }}</span> -->
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="md">
                    <a-form-model-item label="付款方式：" prop="PaymentMode">
                      <EnumSingleChoiceView style="width: 100%" placeholder="请选择" dictCode="EnumPaymentMode" v-model="model.PaymentMode" @change="
                          (value, txt) => {
                            model.PaymentModeStr = txt
                          }
                        " :notOption="[0, 1, 4, 5, 6]" />
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </template>
              <!-- 收货信息 -->
              <template>
                <a-row :gutter="gutter">
                  <a-col :span="24">
                    <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">收货信息</div>
                  </a-col>
                  <a-col :span="md">
                    <a-form-model-item label="收货人：" prop="ReceivingName">
                      <a-input v-model="model.ReceivingName" style="width: 100%" placeholder="请输入" disabled />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="md">
                    <a-form-model-item label="联系电话：" prop="ReceivingPhone">
                      <a-input v-model="model.ReceivingPhone" style="width: 100%" placeholder="请输入" disabled />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="md">
                    <a-form-model-item label="省市区：" prop="ReceivingAreaCode">
                      <SelectAreaView style="width: 100%" ref="selectCityAreas" :selectCode="true" :defaultCodes="areaCodes" @change="(val, node) => onAreasChange(val, node, model, 'ReceivingArea')" disabled v-if="IsSelectCityAreas" />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="md">
                    <a-form-model-item label="详细地址：" prop="ReceivingAddress">
                      <a-input v-model="model.ReceivingAddress" style="width: 100%" placeholder="请输入" disabled />
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="md">
                    <a-form-model-item label="快递公司：" prop="ExpressCompany">
                      <SingleChoiceView ref="singleChoiceViewKD" style="width: 100%" placeholder="请选择" v-model="model.ExpressCompany" @change="
                    (val, txt, item) => {
                      if (item) {
                        model.ExpressCompany = val
                        model.ExpressCompanyStr = txt
                      } else {
                        model.ExpressCompany = ''
                        model.ExpressCompanyStr = ''
                      }
                    }
                  " />

                    </a-form-model-item>
                  </a-col>
                </a-row>
                <!-- 应收/应付信息 -->
                <a-row :gutter="gutter" style="margin-bottom: 24px">
                  <a-col :span="24">
                    <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">应收/应付信息</div>
                  </a-col>
                  <a-col :span="24">
                    <!-- 列表 -->
                    <SimpleTable ref="table" :tab="tab" :columns="columns">
                      <span slot="commonContent" slot-scope="{ text }">
                        <j-ellipsis :value="`￥${text || 0}`" :length="15" />
                      </span>
                    </SimpleTable>
                  </a-col>
                </a-row>
                <!-- 商品信息 -->
                <a-row :gutter="gutter" style="margin-bottom: 24px">
                  <a-col :span="24">
                    <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">商品信息</div>
                  </a-col>
                  <a-col :span="24">
                    <!-- 列表 -->
                    <SimpleTable ref="tableGoods" showTab :tab="tabGoods" :columns="columnsGoods" @operate="operate">
                      <span slot="LastSalesPrice" slot-scope="{ text, record }">
                        <a @click="$refs.iViewHistoricalSalesPricesListModal.show(record.GoodsSpuId)">
                          <j-ellipsis :value="text ? `￥${text}` : '0'" :length="15" />
                        </a>
                      </span>
                      <span slot="commonContent" slot-scope="{ text }">
                        <j-ellipsis :value="text ? `￥${text || 0}` : '0'" :length="15" />
                      </span>
                      <!-- 有效期 -->
                      <span slot="time" slot-scope="{ text }">
                        <j-ellipsis :value="`${text ? moment(text).format('YYYY-MM-DD') : ' -- '}`" :length="15" />
                      </span>
                      <a-row slot="opBtns" slot-scope="{ record, index }" type="flex" align="middle" :gutter="5">
                        <a-col :md="3">
                          <a-tooltip :title="'单价' + (record.Price ? ':' + record.Price : '')">
                            <a-input-number placeholder="单价" v-model="record.Price" :min="0" :max="999999.99" :precision="2" style="width: 100%" @change="(e) => changeInput('Price', e, record, index)" @blur="(e) => onPriceBlur('Price', e, record, index)" />
                          </a-tooltip>
                        </a-col>
                        <a-col :md="4">
                          <a-tooltip :title="'销售数量' + (record.Count ? ':' + record.Count : '')">
                            <a-input-number placeholder="销售数量" v-model="record.Count" :min="0" :max="999999" :precision="0" style="width: 100%" @change="(e) => changeInput('Count', e, record, index)" @blur="(e) => onGoodCountBlur('Count', e, record, index)" />
                          </a-tooltip>
                        </a-col>
                        <a-col :md="4">
                          <a-tooltip :title="'小计金额' + (record.SubtotalAmount ? ':' + record.SubtotalAmount : '')">
                            <a-input-number placeholder="小计金额" disabled :min="0" :max="999999.99" :precision="2" v-model="record.SubtotalAmount" style="width: 100%" />
                          </a-tooltip>
                        </a-col>
                        <a-col :md="4">
                          <a-tooltip :title="'单品毛利' + (record.GrossMargin ? ':' + record.GrossMargin : '')">
                            <a-input-number placeholder="单品毛利" v-model="record.GrossMargin" disabled :max="999999.9999" :precision="4" style="width: 100%" />
                          </a-tooltip>
                        </a-col>
                        <a-col :md="4">
                          <a-tooltip :title="'单品总毛利' + (record.SubtotalGrossMargin ? ':' + record.SubtotalGrossMargin : '')">
                            <a-input-number placeholder="单品总毛利" v-model="record.SubtotalGrossMargin" disabled :max="999999.999999" :precision="6" style="width: 100%" />
                          </a-tooltip>
                        </a-col>
                        <!-- <a-col :md="4">
                          <a-tooltip title="是否特赠品">
                            <a-select
                              v-if="refreshIsSpecialGift"
                              placeholder="是否特赠品"
                              v-model="record.IsSpecialGift"
                              style="width: 100%"
                              @change="(e) => onIsSpecialGiftChange(record)"
                            >
                              <a-select-option :value="''"> 请选择 </a-select-option>
                              <a-select-option
                                v-for="(r, rIndex) in [
                                  { title: '特赠品', value: 'true' },
                                  { title: '非特赠品', value: 'false' },
                                ]"
                                :key="rIndex"
                                :value="r.value"
                              >
                                {{ r.title }}
                              </a-select-option>
                            </a-select>
                          </a-tooltip>
                        </a-col> -->
                        <a-col :md="3">
                          <a-tooltip :title="'备注' + (record.Remarks ? ':' + record.Remarks : '')">
                            <a-input placeholder="备注" v-model="record.Remarks" style="width: 100%" @blur="(e) => onGoodRemarksBlur('Remarks', e, record, index, 10)" />
                          </a-tooltip>
                        </a-col>
                        <a-col :md="2">
                          <a v-if="checkBtnPermissions('9d25ae3d-79a0-4c8c-8c42-63abceb48b5b')" @click="operate(record, '移除')">移除</a>
                        </a-col>
                      </a-row>
                    </SimpleTable>
                  </a-col>
                  <a-col :span="24">
                    <span style="margin-right: 10px">订单金额：<span style="color: red; font-size: 30px">¥{{ model.TotalAmount || 0 }}</span>
                    </span>
                    <span style="margin-right: 10px">整单成本：<span>¥{{ model.TotalCost || 0 }}</span>
                    </span>
                    <span style="margin-right: 10px">整单毛利：<span>¥{{ model.TotalGrossMargin || 0 }}</span>
                    </span>
                    <span style="margin-right: 10px">整单毛利率：<span>{{ model.TotalGrossMarginRate || ' -- ' }}%</span>
                    </span>
                  </a-col>
                </a-row>
              </template>
              <!-- 备注信息 -->
              <template>
                <a-row :gutter="gutter">
                  <a-col :span="24">
                    <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">备注信息</div>
                  </a-col>
                  <a-col :span="24">
                    <a-form-model-item label="" prop="Remarks">
                      <a-textarea v-model="model.Remarks" style="width: 100%" placeholder="请输入" :auto-size="{ minRows: 4, maxRows: 6 }" />
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </template>
              <!-- 上传附件 -->
              <template>
                <a-row :gutter="gutter" style="margin-top: 48px">
                  <a-col :span="24">
                    <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">上传附件</div>
                  </a-col>
                  <a-col :span="24">
                    <a-form-model-item label="" prop="Attachments">
                      <MultiUpload
                        :max="10"
                        :images="model.Attachments"
                        :uploadText="uploadText"
                        :fileType="4"
                        :bName="BName"
                        :dir="Dir"
                        :readOnly="false"
                        :fileSize="50"
                        @change="multiUploadChange" />
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </template>
            </a-form-model>
          </a-spin>
        </div>

        <a-affix :offset-bottom="0" style="float: right; width: 100%; text-align: right">
          <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
            <YYLButton menuId="b7aae4c9-6035-404b-a59b-bfe7e1b64df1" text="保存" type="primary" @click="handleSave" />
            <YYLButton menuId="5e80bf15-cb4c-4d4d-94d9-010f7ed60319" text="保存并提交审核" type="primary" @click="handleSubmit" />
            <a-button @click="goBack(true,true)">取消</a-button>
          </a-card>
        </a-affix>
      </a-spin>
    </a-card>
    <!-- 选择商品 -->
    <SelectGoodsListModal ref="iSelectGoodsListModal" @ok="handleSelectGoodsListModalOk" />
    <!-- 查看历史销售价 -->
    <ViewHistoricalSalesPricesListModal ref="iViewHistoricalSalesPricesListModal" />
  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import moment from 'moment'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '新建订单',
  name: 'ordersManagementListAdd',
  mixins: [EditMixin],
  components: { JEllipsis },
  data() {
    return {
      spinning: false,
      loading: false,
      dateFormat: 'YYYY/MM/DD',
      IsSelectCityAreas: true,
      md: 8,
      model: {
        CustomerId: '',
        CustomerInfo: null,
        TotalAmount: 0,
        TotalCost: 0,
        TotalGrossMargin: 0,
        TotalGrossMarginRate: 0,
        DelegateId: '',
        DelegateName: '',
        DelegateIdCardNo: '',
        ExpressCompany: '',
        ExpressCompanyStr: '',
        IsSubmit: false,
        Attachments: []
      },
      areaCodes: [], //资质list
      id: '',
      opType: 1, //1 新增  2 编辑
      orderType: 1, //1 普通  2 含特 3 手工单
      tab: { hideIpagination: true },
      columns: [
        {
          title: '应收余额',
          dataIndex: 'ReceivableBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '预收余额',
          dataIndex: 'AdvancePaymentBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '应付余额',
          dataIndex: 'PayableBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '预付余额',
          dataIndex: 'PrepaymentBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
      ],
      NotReceiptCount: 0,
      tabGoods: {
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        rowKey: 'uniqueId',
        rowSelection: {
          type: 'checkbox',
        },
        sortArray: ['LastSalesPrice', 'time'],
        hideIpagination: true,
        operateBtn: [
          {
            name: '批量移除',
            type: '',
            icon: '',
            key: '批量移除',
            id: 'a6e4c82a-2426-4b71-af41-4350e2ee8630',
          },
          {
            name: '选 择',
            type: 'primary',
            icon: '',
            key: '选择',
            id: 'd8bee9f4-1212-4f5a-b4fd-c76038ada7e2',
          },
        ],
      },
      columnsGoods: [
        {
          title: '序号',
          dataIndex: 'Order',
          fixed: 'left',
          ellipsis: true,
          width: 50,
          scopedSlots: { customRender: 'Order' },
        },
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          fixed: 'left',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 80,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购人员',
          dataIndex: 'PurchaserName',
          ellipsis: true,
          width: 80,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          ellipsis: true,
          width: 60,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          ellipsis: true,
          width: 60,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          ellipsis: true,
          width: 60,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品可销总库存',
          dataIndex: 'TotalCanSaleInventory',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'BatchNumber',
          ellipsis: true,
          width: 80,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产日期',
          dataIndex: 'ProductionDate',
          ellipsis: true,
          width: 90,
          scopedSlots: { customRender: 'time' },
        },
        {
          title: '有效期',
          dataIndex: 'Period',
          ellipsis: true,
          width: 90,
          scopedSlots: { customRender: 'time' },
        },
        {
          title: '批次号',
          dataIndex: 'BatchNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '仓库',
          dataIndex: 'WarehouseCode',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批次可销库存',
          dataIndex: 'BatchCanSaleInventory',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商销政策底价',
          dataIndex: 'PolicyPrice',
          ellipsis: true,
          width: 100,
          precision: 6,
          scopedSlots: { customRender: 'price' },
        },
        // {
        //   title: '移动平均成本',
        //   dataIndex: 'AvgCostPrice',
        //   ellipsis: true,
        //   width: 100,
        //   scopedSlots: { customRender: 'commonSlot' },
        // },
        {
          title: '商销底价',
          dataIndex: 'LimitPrice',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '末次销售价',
          dataIndex: 'LastSalesPrice',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'LastSalesPrice' },
        },
        // {
        //   title: '采购管理模式',
        //   dataIndex: 'ProcurementManagementModelName',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        // {
        //   title: '是否赠品',
        //   dataIndex: 'IsGift',
        //   ellipsis: true,
        //   width: 100,
        //   customRender: (t) => {
        //     return t ? '是' : '否'
        //   },
        // },
        // {
        //   title: '商品条码',
        //   dataIndex: 'BarCode',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 600,
          fixed: 'right',
          actionBtn: [],
          scopedSlots: { customRender: 'action' },
        },
      ],
      httpHead: 'P36008',
      Details: [],
      expressList: [], // 快递公司列表
      refreshIsSpecialGift: true,
      seleltHandleCustomer: false,//是否点击选择客户 true 点击选择 false 非点击选择是第一次加载为false状态
      url: {
        createOrEditOrder: '/{v}/Order/CreateOrEditOrder',
        detail: '/{v}/Order/OrderDetail',
        expressList: '/{v}/LogisticsOrder/GetExpressCompanies',
        createApproval: '/{v}/ApprovalWorkFlowInstance/CreateApproval', // 创建审核实例
        getSpecialOrderNotReceiptCount: '/{v}/Order/SpecialOrderNotReceiptCount',
        getTotalBalanceAsync: '/{v}/Account/GetTotalBalanceAsync',
        getCustomerListForOrder: '/{v}/Customer/ListForOrder',
        attachmentsDetail: '/{v}/Order/GetSalesOrderAttachments',
      },
      courierServicesCompanyList: [], // 快递公司列表
      uploadText: '上传图片',
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
    }
  },
  created() { },
  computed: {
    rules() {
      return {
        CustomerId: [{ required: true, validator: this.returnCustomerId }],
        // Type: [{ required: true, validator: this.returnType }],
        PaymentMode: [{ required: true, message: '请选择!' }],
        CustomerTypeStr: [{ required: true, message: '请先选择客户!' }],
        ReceivingName: [{ required: true, message: '请先选择客户!' }],
        ReceivingPhone: [{ required: true, message: '请先选择客户!' }],
        ReceivingAreaCode: [{ required: true, message: '请先选择客户!' }],
        ReceivingAddress: [{ required: true, message: '请先选择客户!' }],
        ExpressCompany: [{ required: true, message: '请选择!' }],
        DelegateId: [{ required: true, validator: this.returnDelegateId }],
        Remarks: [
          { required: false, validator: (rule, value, callback) => this.validStrMaxLength(rule, value, callback, 100) },
        ],
      }
    },
  },
  watch: {
    Details: {
      deep: true,
      handler: function (newForm, oldForm) {
        // console.log(newForm)
        if (newForm.length > 0) {
          // 订单金额=所选商品小计金额相加
          // 整单成本=所选商品的库存平均成本相加
          // 整单毛利=所选商品单品总毛利相加
          // .整单毛利率=整单毛利/整单成本
          let TotalAmount = 0,
            TotalCost = 0,
            TotalGrossMargin = 0,
            TotalGrossMarginRate = 0
          newForm.map((c) => {
            TotalAmount += Number(c['SubtotalAmount'] || 0)
            TotalCost += Number(c['PolicyPrice'] || 0) * Number(c['Count'] || 0)
            TotalGrossMargin += Number(c['SubtotalGrossMargin'] || 0)
          })
          // 整单毛利率=整单毛利/整单成本
          // console.log(SubtotalAmount)
          TotalGrossMarginRate = TotalCost > 0 ? TotalGrossMargin / TotalCost : 0
          // console.log(TotalCost)
          // console.log(TotalGrossMarginRate)
          // console.log(TotalAmount)
          this.$set(this.model, 'TotalAmount', Number(TotalAmount).toFixed(2))
          this.$set(this.model, 'TotalCost', Number(TotalCost).toFixed(4))
          this.$set(this.model, 'TotalGrossMargin', Number(TotalGrossMargin).toFixed(4))
          this.$set(this.model, 'TotalGrossMarginRate', Number(TotalGrossMarginRate * 100).toFixed(2))
        } else {
          let TotalAmount = 0,
            TotalCost = 0,
            TotalGrossMargin = 0,
            TotalGrossMarginRate = 0
          this.$set(this.model, 'TotalAmount', Number(TotalAmount).toFixed(2))
          this.$set(this.model, 'TotalCost', Number(TotalCost).toFixed(4))
          this.$set(this.model, 'TotalGrossMargin', Number(TotalGrossMargin).toFixed(4))
          this.$set(this.model, 'TotalGrossMarginRate', Number(TotalGrossMarginRate * 100).toFixed(2))
        }
      },
    },
  },
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.opType = this.$route.query.opType ? Number(this.$route.query.opType) : 1
      this.orderType = this.$route.query.orderType ? Number(this.$route.query.orderType) : 1
      // $nextTick 处理setTitle时因为对应的顶部tabs组件尚未渲染完成导致的setPageTitle事件的监听不到导致tab的title修改失败
      this.$nextTick(() => {
        this.setTitle(
          this.opType == 1
            ? `新增${this.orderType == 1 ? '普通' : this.orderType == 2 ? '含特': '手工'}订单`
            : `编辑${this.orderType == 1 ? '普通' : this.orderType == 2 ? '含特': '手工'}订单`
        )
      })
    }
    if (this.opType == 2) {
      this.loadDetailInfo()
      this.getAttachments()
    } else {
      this.getExpressList()
    }
  },
  methods: {
    // 快递公司选择框变化事件
    handleChange(value, key) {
      const courierServicesCompanyList = this.courierServicesCompanyList
      let itemInfo = courierServicesCompanyList.find(f => f.ItemValue === value)
      if (itemInfo) {
        this.model.ExpressCompanyStr = itemInfo.ItemDescription
      } else {
        this.model.ExpressCompanyStr = ''
      }
      // this.$forceUpdate()
    },
    moment,
    // 验证客户名称
    returnCustomerId(rule, value, callback) {
      if (!value) {
        return callback(new Error('请选择!'))
      }
      if (this.model.CustomerInfo && this.model.CustomerInfo['AuthBusiness'] != 2) {
        return callback(new Error('首营状态未通过，无法下单，请先维护!'))
      }
      if (this.model.CustomerInfo && !this.model.CustomerInfo['IsValid']) {
        return callback(new Error('该客户是否活动为“否”，无法下单，请先维护!'))
      }
      // 【客户营业执照、企业年度报告有效期≥当前日期】
      if (this.model.CustomerInfo && this.returnDate(this.model.CustomerInfo['BusinessLicenseDate']) < 0) {
        return callback(new Error('该客户营业执照已过期，无法下单，请先维护!'))
      }
      // 【客户营业执照、企业年度报告有效期≥当前日期】
      if (this.model.CustomerInfo && this.returnDate(this.model.CustomerInfo['AnnualReportDate']) < 0) {
        return callback(new Error('该客户企业年度报告已过期，无法下单，请先维护!'))
      }
      // .普通订单： ①判断该客户是否上传委托书，如果未上传提示”该客户未上传委托书，无法下单，请先维护“   
      if (this.orderType == 1 && this.model.CustomerInfo && !this.model.CustomerInfo['EntrustLetterDate']) {
        return callback(new Error('该客户未上传委托书，无法下单，请先维护!'))
      }
      // .普通订单 .②判断判断客户委托书有效期≥当前时间，如果已过期提示，”委托书已过期，无法下单，请先维护“ 
      //③当普通订单审核时，审核通过需要判断客户委托书有效期≥当前时间，如果已过期提示，”委托书已过期，无法下单，请先维护“
      // if (this.orderType == 1 && this.model.CustomerInfo && this.returnDate(this.model.CustomerInfo['EntrustLetterDate']) < 0) {
      //   return callback(new Error('委托书已过期，无法下单，请先维护!'))
      // }
      // 含特订单
      if (this.orderType == 2) {
        return this.returnType(rule, this.orderType, callback)
      }
      return callback()
    },
    // 验证委托人
    returnDelegateId(rule, value, callback) {
      if (!value) {
        return callback(new Error('请选择!'))
      }
      if (this.model.DelegateErr) {
        return callback(new Error(this.model.DelegateErr))
      }
      return callback()
    },
    onLoadSuccess(data, type) {
      // console.log(data, type, this.seleltHandleCustomer, this.model)
      switch (type) {
        case 'wtr':
          if (!data.IsSuccess && data.Msg) {
            this.model.DelegateId = ''
            this.model.DelegateName = ''
            this.model.DelegateIdCardNo = ''
            this.model.DelegateErr = data.Msg
          } else {
            // seleltHandleCustomer 是否点击选择客户 true 点击选择 false 非点击选择是第一次加载为false状态
            switch (this.seleltHandleCustomer) {
              case true:
                if (data && data.length == 1) {
                  this.model.DelegateId = data[0].ItemValue
                  this.model.DelegateName = data[0].ItemName
                  this.model.DelegateIdCardNo = data[0].Item.DelegateIdCardNo
                  this.model.DelegateErr = data[0].Item.Note || ''
                } else {
                  this.model.DelegateId = ''
                  this.model.DelegateName = ''
                  this.model.DelegateIdCardNo = ''
                  this.model.DelegateErr = ''
                }
                break;
              case false:
                if (data && data.length > 0) {
                  let haveData = data.find(item => {
                    return item.ItemValue == this.model.DelegateId
                  })
                  if (haveData) {
                    this.model.DelegateErr = haveData.Item.Note || ''
                  }
                } else {
                  this.model.DelegateErr = ''
                }
                break;
            }

            this.$refs.ruleForm.validateField(['DelegateId'])
            if (data && data.length > 1) {
              this.$refs.ruleForm.clearValidate(['DelegateId'])
            }
          }

          break;
      }

      this.$forceUpdate()
    },
    // 验证订单类型
    returnType(rule, value, callback) {
      // if (!value) {
      //   return callback(new Error('请选择!'))
      // }
      if (value != 2) {
        return callback()
      }
      // 含特制剂订单
      if (
        this.model.CustomerInfo &&
        !(this.model.CustomerInfo['Classifies'] || []).some((v) =>
          ['化学药制剂-含特殊药品复方制剂', '中成药-含特殊药品复方制剂'].includes(v)
        )
      ) {
        return callback(new Error('该客户没有含特制剂药品经营范围，无法下单，请先维护!'))
      }

      if (this.model.CustomerInfo && !this.model.CustomerInfo['SpecialEntrustLetterDate']) {
        return callback(new Error('该客户未上传含特制剂委托书，无法下单，请先维护!'))
      }
      // 判断客户含特制剂委托书有效期≥当前时间
      // if (this.model.CustomerInfo && this.returnDate(this.model.CustomerInfo['SpecialEntrustLetterDate']) < 0) {
      //   return callback(new Error('含特制剂委托书已过期，无法下单，请先维护!'))
      // }
      // 判断客户含特制剂委托书有效期≥当前时间
      // console.log(this.NotReceiptCount)
      if (this.NotReceiptCount > 0) {
        return callback(new Error('该客户含特制剂委订单未上传回执单，无法下单，请先维护!'))
      }

      return callback()
    },
    // 比较日期的大小  0 相等    负数   正数
    returnDate(validDate) {
      // console.log('validDate             ', validDate)
      const startTime = moment(moment(new Date()).format(this.dateFormat), this.dateFormat)
      const endTime = moment(validDate, this.dateFormat)
      const diff3 = moment(endTime).diff(moment(startTime), 'days')
      // console.log('moment               ', diff3)
      return diff3
    },
    getSpecialOrderNotReceiptCount() {
      if (!this.model.CustomerId) return
      getAction(this.url.getSpecialOrderNotReceiptCount, { customerId: this.model.CustomerId }, this.httpHead).then(
        (res) => {
          if (!res.IsSuccess) {
            this.$message.warning(res.Msg)
            return
          }
          this.NotReceiptCount = res.Data || 0
        }
      )
    },
    changeInput(key, e, record, index) {
      if (key == 'Price') {
        this.returnSubtotalAmount(key, e, record, index)
      } else if (key == 'Count') {
        this.onGoodCountBlur(key, e, record, index)
      }
    },
    // 销售单价 销售单价不能小于商销底价
    onPriceBlur(key, e, record, index) {
      if(this.orderType === 3) return
      if (record['ManageClassifyStr1'] != '促销品' && (record[key] || 0) < (record.LimitPrice || 0)) {
        this.$message.warning('您填写的单价小于商销底价请重新填写 ')
        record[key] = null
        return
      }
      this.returnSubtotalAmount(key, e, record, index)
    },
    // 销售数量 填写销售数量不能大于批次可销库存
    onGoodCountBlur(key, e, record, index) {
      if ((record.Count || 0) > (record.BatchCanSaleInventory || 0)) {
        this.$message.warning('填写的销售数量大于可销库存')
        record.Count = null
        return
      }
      this.returnSubtotalAmount(key, e, record, index)
    },
    // 备注 字符长度限制
    onGoodRemarksBlur(key, e, record, index, maxLength) {
      if (record['Remarks'] && this.getStrMaxLength(record['Remarks']) > maxLength) {
        this.$message.warning(`备注不能超过${maxLength}字符,请重新输入`)
        record.Remarks = null
        return
      }
    },
    returnSubtotalAmount(key, e, record, index) {
      // record[key] = e.target.value
      // 小计金额=单价x销售数量
      record.SubtotalAmount = Number((record.Price || 0) * (record.Count || 0)).toFixed(2)
      // if (key == 'Price') {
        // .单品毛利=单价-商销政策底价，单品毛利可为负数
        // if (record.Price) {
        // record.GrossMargin = this.numReduce(record.Price || 0, (record.PolicyPrice || 0).toFixed(6))
        // } else {
        //   record.GrossMargin = record['ManageClassifyStr1'] == '促销品' ? 0 : null
        // }
      // }
      // console.log(record.Price)
      // console.log(record.AvgCostPrice)
      if (key == 'Count' || key == 'Price') {
        // 单品总毛利=单品毛利x销售数量
        record.SubtotalGrossMargin = Number((record.GrossMargin || 0) * (record.Count || 0)).toFixed(4)
        // .单品毛利=单价-商销政策底价，单品毛利可为负数
        record.GrossMargin = this.numReduce(record.Price || 0, (record.PolicyPrice || 0).toFixed(6))
      }
      this.$set(this.$refs.tableGoods.dataSource, index, record)
      this.$set(this, 'Details', this.$refs.tableGoods.dataSource)
      // this.$forceUpdate()
    },
    /**
     * 选择地址区域
     **/

    onAreasChange(val, node, model, key) {
      if (!val) {
        return
      }
      // console.log(node)
      this.areaCodes = val
      if (model && key) {
        if (typeof node != 'undefined') {
          model[key + 'Code'] = node[node.length - 1].code
          model[key + 'Province	'] = node[0].code
          model[key + 'City	'] = node[1].code
          model[key + 'County	'] = node[2].code
          model[key + 'Town	'] = node[3].code
          model[key + 'FullName'] = node[node.length - 1].source.FullName
        } else {
          model[key + 'Code'] = null
          model[key + 'Province	'] = null
          model[key + 'City	'] = null
          model[key + 'County	'] = null
          model[key + 'Town	'] = null
          model[key + 'FullName'] = ''
        }
        // if (form) {
        //   form.clearValidate([key + 'Code'])
        // }
      }
    },
    changeWTR(val, txt, item) {
      if (item) {
        this.model.DelegateId = val
        this.model.DelegateName = txt
        this.model.DelegateIdCardNo = item.DelegateIdCardNo
        this.model.DelegateErr = item.Note || ''
      } else {
        this.model.DelegateId = ''
        this.model.DelegateName = ''
        this.model.DelegateIdCardNo = ''
        this.model.DelegateErr = ''
      }
    },
    // 列表操作
    operate(record, type) {
      let list = this.$refs.tableGoods.dataSource || []
      if (type == '移除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认移除该数据？',
          onOk() {
            that.$refs.tableGoods.dataSource = list.filter((v) => v.uniqueId != record.uniqueId)
            that.$set(that, 'Details', that.$refs.tableGoods.dataSource)
          },
          onCancel() { },
        })
      } else if (type == '批量移除') {
        let selectedRowKeys = this.$refs.tableGoods.selectedRowKeys || []
        if (list.length < 1) {
          this.$message.warning('没有可移除的数据!')
          return
        }
        if (selectedRowKeys.length < 1) {
          this.$message.warning('请先选择需要移除的数据!')
          return
        }
        this.$refs.tableGoods.dataSource = list.filter((j) => selectedRowKeys.findIndex((v) => v == j.uniqueId) < 0)
        this.$set(this, 'Details', this.$refs.tableGoods.dataSource)
        this.$refs.tableGoods.clearSelected()
      } else if (type == '选择') {
        let that = this
        if (!this.model.CustomerId) {
          that.$message.warning('请先选择客户!')
          return
        }
        // if (!this.model.Type) {
        //   that.$message.warning('请先选择订单类型!')
        //   return
        // }

        this.$refs.iSelectGoodsListModal.show(this.orderType == 2 ? true : false, this.model.CustomerInfo, list, this.orderType)
        // this.$refs.iSelectGoodsListModal.show(false, this.model.CustomerInfo, list)
      }
    },
    async getExpressList() {
      const params = {
        PageIndex: 1,
        PageSize: 150
      }
      const res = await getAction(this.url.expressList, params, this.httpHead)
      if (!res.IsSuccess) return
      const data = (res.Data || []).filter(item => item.IsEnabled).map(it => ({ ...it, Item: it }))
      this.expressList = data
      this.$refs.singleChoiceViewKD.setCurrentDictOptions(data)
    },
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then(async (res) => {
          if (res.IsSuccess) {
            that.model = Object.assign({ ...that.model ,IsSubmit: false }, res.Data)
            await that.getExpressList()
            if ((that.model.Details || []).length) {
              let goods = []
              that.model.Details.map((v, i) => {
                // 重新计算毛利
                this.returnSubtotalAmount('Price', null, v, i)

                goods.push({
                  Id: v['Id'],
                  GoodsSpuId: v['GoodsId'],
                  ErpGoodsId: v['ErpGoodsId'],
                  ErpGoodsCode: v['ErpGoodsCode'],
                  ErpGoodsName: v['ErpGoodsName'],
                  PurchaserName: v['GoodsPurchaserName'],
                  PackingSpecification: v['GoodsPackingSpecification'],
                  PackageUnit: v['GoodsPackageUnit'],
                  BrandManufacturer: v['GoodsBrandManufacturer'],
                  PackageCount: v['GoodsPackageCount'],
                  BatchNumber: v['GoodsBatchNumber'],
                  BatchNo: v.BatchNo,
                  BatchCanSaleInventory: v.BatchCanSaleInventory,
                  WarehouseCode: v.WarehouseCode,
                  PolicyPrice: v.PolicyPrice,
                  Period: v['GoodsPeriod'],
                  ProductionDate: v['ProductionDate'],
                  CanSaleInventory: v['GoodsCanSaleInventory'],
                  TotalCanSaleInventory: v['TotalCanSaleInventory'],
                  PurchaseCostPrice: v['GoodsPurchaseCostPrice'],
                  AvgCostPrice: v['AvgCostPrice'],
                  LastSalesPrice: v.LastSalesPrice,
                  LimitPrice: v['GoodsLimitPrice'],
                  ProcurementManagementModelCode: v['GoodsProcurementManagementModelCode'],
                  ProcurementManagementModelName: v['GoodsProcurementManagementModelName'],
                  BarCode: v['GoodsBarCode'],
                  Price: v['Price'],
                  Count: v['Count'],
                  SubtotalAmount: v['SubtotalAmount'],
                  GrossMargin: v['GrossMargin'],
                  SubtotalGrossMargin: v['SubtotalGrossMargin'],
                  IsSpecialGift: v['IsSpecialGift'] ? 'true' : 'false',
                  Remarks: v['Remarks'],
                  IsGift: v['IsGift'],
                  DataType: v['DataType'],
                  DataTypeStr: v['DataTypeStr'],
                  SupplierCode: v['SupplierCode'],
                  SupplierId: v['SupplierId'],
                  SupplierName: v['SupplierName'],
                  uniqueId: `${v.Id}_${v.BatchNo}_${v.WarehouseCode}`
                })
              })
              that.$set(that, 'Details', goods || [])
            }

            that.$set(that.$refs.tableGoods, 'dataSource', that.Details || [])
            that.model.PaymentMode = '' + (that.model.PaymentMode || '')
            that.model.Type = that.orderType
            if (!that.model.DelegateId) {
              that.model.DelegateId = ''
            }
            // 快递公司重组数据
            const idx = that.expressList.findIndex(it => it.ItemValue === res.Data.ExpressCompanyCode)
            this.$set(that.model, 'ExpressCompany', !!~idx ? res.Data.ExpressCompanyCode : null)
            if (this.model.ReceivingAreaCode) this.areaCodes = this.getDefaultValueByCode(this.model.ReceivingAreaCode)
            this.toggle('IsSelectCityAreas')
            if (this.orderType == 2) this.getSpecialOrderNotReceiptCount()
            this.getTotalBalanceAsync()
            this.getCustomerListForOrder(that.model.CustomerCode)
            this.$nextTick(() => {
              this.$refs.ruleForm.validateField([
                'CustomerTypeStr',
                'ReceivingName',
                'ReceivingPhone',
                'ReceivingAreaCode',
                'ReceivingAddress',
              ])
            })
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // 获取附件详情
    getAttachments() {
      getAction(this.url.attachmentsDetail, { salesOrderId: this.id, isAudit: false }, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            let Attachments = []
            if (res.Data && res.Data.length) {
              res.Data.forEach(item => {
                Attachments.push(item.Url)
              })
            }
            this.model = Object.assign(this.model, { Attachments:Attachments })
          } else {
            this.model = Object.assign(this.model, { Attachments:[] })
          }
        })
    },
    // 编辑进来查客户信息
    getCustomerListForOrder(CustomerCode) {
      getAction(this.url.getCustomerListForOrder, { KeyWord: CustomerCode }, 'P36002')
        .then((res) => {
          if (res.IsSuccess) {
            this.model.CustomerInfo = (res.Data || []).length ? res.Data[0] : null
            // 填写客户的信息
            let item = this.model.CustomerInfo
            if (item) this.handleCustomer(item.Id, item.Name, item, false)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 保存为草稿
    //     按钮判断：
    // 1、至少填写了商品名称、生产厂家、规格、商品类别、商品分类
    // 2、商品名称、生产厂家、规格合起来看和商品中其他商品不能重复。
    onSubmitIsSubmit() {
      // console.log(this.opType)

      let arr = []
      this.$refs.ruleForm.validateField(['CustomerId', 'Remarks'], (valid) => {
        arr.push(valid ? false : true)
      })
      // if (
      //   (this.Details || []).length &&
      //   this.Details.some((v) => (!v['Count'] && v['Count'] != 0) || (!v['Price'] && v['Price'] != 0))
      // ) {
      //   this.$message.warning(`请完善商品需要填写的内容!`)
      //   arr.push(false)
      // }
      if (arr.some((v) => !v)) {
        return null
      }
      return this.setFormData()
    },
    onSubmit() {
      // console.log(this.opType)
      let returnValid = false

      this.$refs.ruleForm.validate((valid) => {
        returnValid = valid
      })
      if ((this.Details || []).length < 1) {
        this.$message.warning('请添加商品信息!')
        return (returnValid = false)
      }
      console.log('Details  ', this.Details)
      if (this.Details.some((v) => (!v['Count']) || (!v['Price'] && v['Price'] != 0))) {
        this.$message.warning(`请填写商品信息!`)
        return (returnValid = false)
      }
      if (this.Details.some((v) => (v['Count'] || 0) > (v['TotalCanSaleInventory'] || 0))) {
        let list = this.Details.filter((v) => (v['Count'] || 0) > (v['TotalCanSaleInventory'] || 0)) || []
        let nameStr = list.map((v) => v['ErpGoodsName']).join('；')
        this.$message.warning(`${nameStr}销售数量大于可销库存，请重新填写!`)
        return (returnValid = false)
      }
      // if (this.model.TotalGrossMargin < 0) {
      //   this.$message.warning('提交失败，整单毛利为负!')
      //   return (returnValid = false)
      // }
      if (!returnValid) {
        return returnValid
      }
      return this.setFormData()
    },
    setFormData() {
      // return
      // let formData = JSON.parse(JSON.stringify(this.model))
      let formData = {
        CustomerId: this.model['CustomerId'],
        // Type: this.model['Type'] == '' ? null : Number(this.model['Type']),
        // TypeStr: this.model['TypeStr'],
        Type: this.orderType,
        TypeStr: this.orderType == 1 ? '普通订单' : this.orderType == 2 ? '含特制剂订单': '手工订单',
        PaymentMode: this.model['PaymentMode'] == '' ? null : Number(this.model['PaymentMode']),
        PaymentModeStr: this.model['PaymentModeStr'],
        ReceivingName: this.model['ReceivingName'],
        ReceivingPhone: this.model['ReceivingPhone'],
        ReceivingAreaCode: this.model['ReceivingAreaCode'],
        ReceivingAreaProvince: this.model['ReceivingAreaProvince'],
        ReceivingAreaCity: this.model['ReceivingAreaCity'],
        ReceivingAreaCounty: this.model['ReceivingAreaCounty'],
        ReceivingAreaTown: this.model['ReceivingAreaTown'] || '',
        ReceivingAreaFullName: this.model['ReceivingAreaFullName'],
        ReceivingAddress: this.model['ReceivingAddress'],
        // ExpressCompany: this.model['ExpressCompany'],//旧版快递公司
        // ExpressCompanyStr: this.model['ExpressCompanyStr'],
        ShipperCode: this.model['ExpressCompany'],//新版快递公司
        TotalAmount: this.model['TotalAmount'],
        TotalCost: this.model['TotalCost'],
        TotalGrossMargin: this.model['TotalGrossMargin'],
        TotalGrossMarginRate: this.model['TotalGrossMarginRate'],
        Remarks: this.model['Remarks'],
        DelegateId: this.model['DelegateId'] || null,
        DelegateIdCardNo: this.model['DelegateIdCardNo'] || '',
        DelegateName: this.model['DelegateName'],
        Attachments: this.model['Attachments']
      }
      let goods = []
      this['Details'].map((v) => {
        let item = {
          GoodsId: v['GoodsSpuId'],
          ErpGoodsId: v['ErpGoodsId'],
          ErpGoodsCode: v['ErpGoodsCode'],
          ErpGoodsName: v['ErpGoodsName'],
          GoodsPurchaserName: v['PurchaserName'],
          GoodsPackingSpecification: v['PackingSpecification'],
          GoodsPackageUnit: v['PackageUnit'],
          GoodsBrandManufacturer: v['BrandManufacturer'],
          GoodsPackageCount: v['PackageCount'],
          GoodsBatchNumber: v['BatchNumber'] || '',
          BatchNo: v.BatchNo,
          BatchCanSaleInventory: v.BatchCanSaleInventory,
          WarehouseCode: v.WarehouseCode,
          PolicyPrice: v.PolicyPrice,
          GoodsPeriod: v['Period'],
          ProductionDate: v['ProductionDate'],
          GoodsCanSaleInventory: v['CanSaleInventory'],
          GoodsTotalCanSaleInventory: v['TotalCanSaleInventory'],
          GoodsPurchaseCostPrice: v['PurchaseCostPrice'],
          AvgCostPrice: v['AvgCostPrice'],
          GoodsLimitPrice: v['LimitPrice'],
          GoodsProcurementManagementModelCode: v['ProcurementManagementModelCode'],
          GoodsProcurementManagementModelName: v['ProcurementManagementModelName'],
          GoodsBarCode: v['BarCode'],
          Price: v['Price'],
          Count: v['Count'],
          SubtotalAmount: v['SubtotalAmount'],
          GrossMargin: v['GrossMargin'],
          SubtotalGrossMargin: v['SubtotalGrossMargin'],
          IsSpecialGift: this.reBool(v['IsSpecialGift']),
          Remarks: v['Remarks'],
          IsGift: v['IsGift'],
          DataType: v['DataType'],
          DataTypeStr: v['DataTypeStr'],
          SupplierCode: v['SupplierCode'],
          SupplierId: v['SupplierId'],
          SupplierName: v['SupplierName'],
        }
        if (item.Price == null) delete item['Price']
        if (item.Count == null) delete item['Count']
        if (item.GrossMargin == null) delete item['GrossMargin']
        if (item.SubtotalGrossMargin == null) delete item['SubtotalGrossMargin']
        if (item.SubtotalAmount == null) delete item['SubtotalAmount']
        goods.push(item)
      })
      formData['Details'] = goods
      if (this.model['Id']) formData['Id'] = this.model['Id']
      delete formData['CustomerInfo']
      return formData
    },
    reBool(key) {
      return key == '' ? null : key === 'false' ? false : true
    },
    // 保存
    handleSave() {
      this.$refs.ruleForm.clearValidate()
      let formData = this.onSubmitIsSubmit()
      if (formData) {
        // console.log('opType    ', this.opType)

        if (this.opType == 1) formData['IsSubmit'] = false
        // console.log('formData      ', formData)

        this.stepSubmitPost(formData)
      }
    },

    stepSubmitPost(formData, act, call) {
      if (this.id) formData['Id'] = this.id
      this.spinning = true
      postAction(this.url.createOrEditOrder, formData, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.spinning = false
          this.$message.error(res.Msg)
          return
        }
        // this.id = this.id ? this.id : res.Data
        // if (act != 'next') {
        if (formData['IsSubmit']) {
          this.createApproval(
            {
              // Remark: data.remark,
              BussinessNo: res.Data,
            },
            (bool) => {
              this.spinning = false
              if (bool) {
                this.$message.success('操作成功！')
                setTimeout(() => {
                  this.goBack(true, true)
                }, 500)
              }
            }
          )
          return
        }
        this.spinning = false
        this.$message.success('操作成功！')
        setTimeout(() => {
          this.goBack(true, true)
        }, 500)
        return
        // }
        // 下一步
        call && call()
      })
    },
    // 创建审核实例
    //     商销订单审核有两个场景哈，
    // 普通订单  Scenes  14
    // 含特制剂订单    Scenes 15
    // 手工订单    Scenes 40
    createApproval(data, callback) {
      if (!data['BussinessNo']) {
        this.$message.error('BussinessNo未获取到值')
        return
      }
      postAction(
        this.url.createApproval,
        {
          BussinessNo: data['BussinessNo'],
          Scenes: this.orderType == 3 ? 40: this.orderType == 2 ? 15 : 14,
          // Scenes: 14,
          OpratorId: Vue.ls.get(USER_ID),
          // Remark: data['Remark']
        },
        'P36005'
      ).then((res) => {
        if (!res.IsSuccess) {
          this.$message.error(res.Msg)
          callback && callback(false)
          return
        }
        // this.$message.success('操作成功！')
        callback && callback(true)
      })
    },
    // 保存并审核
    handleSubmit() {
      this.$refs.ruleForm.clearValidate()
      let formData = this.onSubmit()
      if (!formData) return
      formData['IsSubmit'] = true

      this.stepSubmitPost(formData)
    },
    // 选择商品
    handleSelectGoodsListModalOk(list) {
      console.log('list', list, JSON.stringify(this.Details))
      list.map((v) => {
        // console.log(this.Details.findIndex(j => j['GoodsSpuId'] == v['GoodsSpuId']))
        //goodsSpuId  batchNumber批号 都一样才认为是同一个商品
        let index = this.Details.findIndex(
          (j) => j['GoodsSpuId'] == v['GoodsSpuId'] && j['BatchNumber'] == v['BatchNumber'] && j['BatchNo'] == v['BatchNo']
        )
        let price = v.Price
        if (index > -1) {
          v.Price = price? price: this.Details[index].Price || null
          v.Count = this.Details[index].Count || null
          v.SubtotalAmount = this.Details[index].SubtotalAmount || null
          v.GrossMargin = price? Number((price || 0) - (v.AvgCostPrice || 0)).toFixed(6):this.Details[index].GrossMargin || null
          v.SubtotalGrossMargin = price?Number((v.GrossMargin || 0) * (v.Count || 0)).toFixed(6):this.Details[index].SubtotalGrossMargin || null 
          v.IsSpecialGift = this.Details[index].IsSpecialGift || 'false'
          this.Details[index] = v
        } else {
          v.Price = price? price:null
          v.Count = null
          v.SubtotalAmount = null
          v.GrossMargin = price? Number((price || 0) - (v.AvgCostPrice || 0)).toFixed(6):null
          v.SubtotalGrossMargin = price?Number((v.GrossMargin || 0) * (v.Count || 0)).toFixed(4):null
          v.IsSpecialGift = 'false'
          this.Details.push(v)
        }
      })
      // this.Details = [...list]
      // this.$set(this, 'Details', newList)
      this.$set(this.$refs.tableGoods, 'dataSource', this.Details)
    },
    // 选择客户
    handleCustomer(val, txt, item, seleltHandleCustomer) {
      // console.log(this.model.CustomerId, val, txt, item)
      this.seleltHandleCustomer = seleltHandleCustomer == undefined ? true : seleltHandleCustomer
      this.$refs.ruleForm.clearValidate()
      this.$set(this.model, 'CustomerTypeStr', val ? item.TypeStr : '')
      this.$set(this.model, 'ReceivingName', val ? item.ReceivingName : '')
      this.$set(this.model, 'ReceivingPhone', val ? item.ReceivingPhone : '')
      this.$set(this.model, 'ReceivingAreaCode', val ? item.ReceivingAreaCode : '')
      this.$set(this.model, 'ReceivingAreaFullName', val ? item.ReceivingAreaFullName : '')
      this.$set(this.model, 'ReceivingAreaProvince', val ? item.ReceivingAreaProvince : '')
      this.$set(this.model, 'ReceivingAreaCity', val ? item.ReceivingAreaCity : '')
      this.$set(this.model, 'ReceivingAreaCounty', val ? item.ReceivingAreaCounty : '')
      this.$set(this.model, 'ReceivingAreaTown', val ? item.ReceivingAreaTown : '')
      this.$set(this.model, 'ReceivingAddress', val ? item.ReceivingAddress : '')
      this.$set(this.model, 'CustomerInfo', val ? item : '')
      if (!this.model.CustomerId) {
        this.$set(this.model, 'DelegateId', '')
        this.$set(this.model, 'DelegateName', '')
        this.$set(this.model, 'DelegateIdCardNo', '')
      }

      if (this.model.ReceivingAreaCode) this.areaCodes = this.getDefaultValueByCode(this.model.ReceivingAreaCode)
      this.toggle('IsSelectCityAreas')
      if (this.orderType == 2) this.getSpecialOrderNotReceiptCount()
      this.getTotalBalanceAsync()
      this.$nextTick(() => {
        this.$refs.ruleForm.validateField([
          'CustomerTypeStr',
          'ReceivingName',
          'ReceivingPhone',
          'ReceivingAreaCode',
          'ReceivingAddress',
          'DelegateId'
        ])
      })
    },
    toggle(key) {
      this[key] = false
      this.$nextTick(() => {
        this[key] = true
      })
    },
    // 应收/应付信息
    getTotalBalanceAsync() {
      if (!this.model.CustomerId) return
      getAction(this.url.getTotalBalanceAsync, { CustomerId: this.model.CustomerId }, 'P36011').then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.TotalBalanceAsync = [res.Data]
        this.$refs.table.dataSource = this.TotalBalanceAsync
      })
    },
    onIsSpecialGiftChange(record) {
      this.refreshIsSpecialGift = false
      this.$nextTick(() => {
        this.refreshIsSpecialGift = true
      })
    },
    // 上传回调
    multiUploadChange(images) {
      this.model.Attachments = images
    },
  },
}
</script>
<style lang="less" scoped>
.form-model-style {
  /deep/.ant-form-item-control {
    height: 34px;
  }
}
</style>
