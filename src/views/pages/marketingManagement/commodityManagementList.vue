<!-- 商销商品管理 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="columns"
      :isTableInitData="isTableInitData"
      @onSetValid="(item, chceked) => onSetValid(item, chceked, 'POST', 'GoodsSpuId')"
      @operate="operate"
      @changeTab="changeTab"
    />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销商品管理',
  name: 'commodityManagementList',
  mixins: [SimpleMixin, ListMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '商品', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '商品名称/编号' },
        { name: '商品条码', type: 'input', value: '', key: 'BarCode', defaultVal: '', placeholder: '请输入' },
        {
          name: '商品类别',
          type: 'selectLink',
          vModel: 'ManageClassify1',
          dataKey: { name: 'Name', value: 'Id' },
          httpParams: { Name: '', Level: 1 },
          keyWord: 'Name',
          defaultVal: '',
          placeholder: '请选择',
          httpType: 'POST',
          url: '/{v}/GoodsManage/QueryGoodsManageClassifyList',
          httpHead: 'P36006',
        },
        { name: '生产厂家', type: 'input', value: '', key: 'BrandManufacturer', defaultVal: '', placeholder: '请输入' },
        { name: '采购人', type: 'input', value: '', key: 'PurchaserName', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [
          { name: '导出', type: '', icon: 'download', key: '导出', id: '912f5511-683b-45e1-9ffe-21d53802217c' },
        ],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          width: 180,
          fixed: 'left',
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品条码',
          dataIndex: 'BarCode',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品类别',
          dataIndex: 'ManageClassifyStr1',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品规格',
          dataIndex: 'PackingSpecification',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂家',
          dataIndex: 'BrandManufacturer',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          width: 80,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批准文号',
          dataIndex: 'ApprovalNumber',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购人',
          dataIndex: 'PurchaserName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '总库存',
          dataIndex: 'RealInventory',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '可销库存',
          dataIndex: 'CanSaleInventory',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '占用库存',
          dataIndex: 'LockInventory',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '采购管理模式',
          dataIndex: 'ProcurementManagementModelName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '进价(元)',
          dataIndex: 'PurchaseCostPrice',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商销底价(元)',
          dataIndex: 'LimitPrice',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '最高进价(元)',
          dataIndex: 'MaxCostPrice',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '最低进价(元)',
          dataIndex: 'MinCostPrice',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '平均成本价(元)',
          dataIndex: 'AvgCostPrice',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '30天平均销量',
          dataIndex: 'AvgSalesCount30Day',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '30天总销量',
          dataIndex: 'SumSalesCount30Day',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '60天平均销量',
          dataIndex: 'AvgSalesCount60Day',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '60天总销量',
          dataIndex: 'SumSalesCount60Day',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '90天平均销量',
          dataIndex: 'AvgSalesCount90Day',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '90天总销量',
          dataIndex: 'SumSalesCount90Day',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品ABC',
          dataIndex: 'GoodsABCLabel',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '毛利标签',
        //   dataIndex: 'GrossProfitLabel',
        //   width: 150,
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'component' }
        // },
        // {
        //   title: '销售区域',
        //   dataIndex: 'SalesAreas',
        //   width: 150,
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'component' }
        // }
      ],
      queryParam: {},
      isTableInitData: true, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/Order/SalesGoodsList',
        exportUrl: '/{v}/Order/ExportSalesGoodsList', //导出
      },
    }
  },
  created() {},
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    searchQuery(queryParam, type) {
      let params = JSON.parse(JSON.stringify(queryParam))
      params['AuditStatus'] = Number(params['AuditStatus'])
      params['AuthBusinessStatus'] = Number(params['AuthBusinessStatus'])
      this.$refs.table.loadDatas(1, params)
    },
    // 列表操作
    operate(record, type) {
      if (type == '导出') {
        this.handleExportXls(
          '商销商品管理',
          'Get',
          this.linkUrl.exportUrl,
          null,
          this.linkHttpHead,
          this.$refs.SimpleSearchArea.queryParam
        )
      }
    },
  },
}
</script>

<style></style>
