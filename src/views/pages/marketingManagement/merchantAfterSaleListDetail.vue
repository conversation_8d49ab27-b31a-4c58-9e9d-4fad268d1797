<!-- 售后详情-->
<template>
  <div>
    <a-card
      title="售后详情"
      style="margin-bottom:12px"
    >
      <a-button
        @click="goBack(true,true)"
        slot="extra"
      >返回</a-button>
    </a-card>
    <a-spin :spinning="loading">

      <a-card style="margin-bottom:12px">
        <a-row :gutter="gutter">
          <a-col
            :md="8"
            :style="{textAlign:'center',color:getAuditColor( model&&model['SaleAfterPayStatus'])}"
          >
            <p>
              <a-icon
                :type="getAuditIcon(model&&model['SaleAfterPayStatus'])"
                :style="{color:getAuditColor( model&&model['SaleAfterPayStatus']),fontSize:'32px'}"
              />
            </p>
            {{(model && model['SaleAfterPayStatusStr']) || ' -- '}}
          </a-col>
          <a-col :md="5">
            <p>退款金额</p>
            ￥{{(model && model.RefundAmount) || 0}}
          </a-col>
          <a-col :md="5">
            <p>售后编号</p>
            {{( model &&  model.Code) || ' -- '}}
          </a-col>
          <a-col :md="5">
            <p>售后申请时间</p>
            {{(model && model.CreateTime) || ' -- '}}
          </a-col>
        </a-row>
      </a-card>
      <a-card>
        <a-tabs v-model="activeTabKey">
          <a-tab-pane
            :key="item['key']"
            :tab="item['value']"
            v-for="item in (model&&model.SaleAfterType != 0 ? tabsList : tabsList.filter(v=>v.key !=4))"
          >
            <!-- 基本信息 -->
            <BasicInfoTemplateAfterSaleDetail
              ref="iBasicInfoTemplateOrderDetail"
              :Info="model"
              v-if="activeTabKey == 1 && model && item['key'] == 1"
            />

            <!-- 审核信息 -->
            <SupAuditInformation
              ref="supAuditInformation"
              :bussinessNo="model.AuditId"
              v-if="activeTabKey === 2 && model && item['key'] == 2"
            />
            <!-- 退款信息 -->
            <RefundInfoAfterSaleDetail
              ref="iRefundInfoAfterSaleDetail"
              :Info="model"
              v-if="activeTabKey == 3 && model && item['key'] == 3"
            />
            <!-- 入库信息 -->
            <IncomingInfoAfterSaleDetail
              ref="iIncomingInfoAfterSaleDetail"
              :Info="model"
              v-if="activeTabKey == 4 && model && item['key'] == 4 && model.SaleAfterType != 0"
            />
          </a-tab-pane>

        </a-tabs>

      </a-card>
    </a-spin>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'merchantAfterSaleListDetail',
  mixins: [EditMixin],
  data() {
    return {
      activeTabKey: 1,
      model: null,
      loading: false,
      id: '',
      tabsList: [
        {
          key: 1,
          value: '基本信息',
          id: '263a1f80-3e18-4f99-9979-5040984406aa'
        },
        {
          key: 2,
          value: '审核信息',
          id: '263a1f80-3e18-4f99-9979-5040984406aa'
        },
        {
          key: 3,
          value: '退款信息',
          id: '263a1f80-3e18-4f99-9979-5040984406aa'
        },
        {
          key: 4,
          value: '入库信息',
          id: '263a1f80-3e18-4f99-9979-5040984406aa'
        }
      ],
      httpHead: 'P36008',
      url: {
        detail: '/{v}/SaleAfterOrder/GetSaleAfterOrderBaseInfo'
      }
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.activeTabKey = Number(this.$route.query.key) || 1
      if (this.$route.query['tabId'] && !this.checkBtnPermissions('263a1f80-3e18-4f99-9979-5040984406aa')) {
        this.tabsList = this.tabsList.filter(v => v.id == this.$route.query['tabId'])
      }
      this.loadDetailInfo()
    }
  },
  created() {},
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    getAuditIcon(status) {
      if (status == 3) {
        return 'close-circle'
      } else if (status == 2) {
        return 'check-circle'
      } else {
        return 'clock-circle'
      }
    },
    getAuditColor(status) {
      if (status == 3) {
        return '#999999'
      } else if (status == 2) {
        return '#52c41a'
      } else {
        return '#1890ff'
      }
    }
  }
}
</script>

<style scoped>

</style>
