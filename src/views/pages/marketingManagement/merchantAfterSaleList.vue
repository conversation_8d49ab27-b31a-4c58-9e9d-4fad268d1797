<!-- 商销售后列表 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="(item, chceked) => onSetValid(item, chceked, 'POST', 'GoodsSpuId')" @operate="operate" @changeTab="changeTab" />
    <!-- 申请售后 -->
    <ApplyAfterSalesServiceModal ref="iApplyAfterSalesServiceModal" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销售后列表',
  name: 'merchantAfterSaleList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        { name: '售后编号', type: 'input', value: '', key: 'Code', defaultVal: '', placeholder: '请输入' },
        { name: '订单编号', type: 'input', value: '', key: 'OrderCode', defaultVal: '', placeholder: '请输入' },
        {
          name: '客户',
          type: 'input',
          value: '',
          key: 'CustomerKey',
          defaultVal: '',
          placeholder: '请输入',
        },
        {
          name: '联系电话',
          type: 'input',
          value: '',
          key: 'LinkPhone',
          defaultVal: '',
          placeholder: '请输入',
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '付款状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'SaleAfterPayStatus',
          dictCode: 'EnumSaleAfterPayStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '执行状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'SaleAfterExecuteStatus',
          dictCode: 'EnumExecuteStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '同步状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '售后状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'SaleAfterOrderStatus',
          dictCode: 'EnumSaleAfterOrderStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        { name: '责任人', type: 'input', value: '', key: 'OwnerByName', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '售后编号',
          dataIndex: 'Code',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单编号',
          dataIndex: 'OrderCode',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退款类型',
          dataIndex: 'SaleAfterTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '售后范围',
          dataIndex: 'SaleAfterScopeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '售后方式',
          dataIndex: 'SaleAfterRefundTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '售后金额(元)',
          dataIndex: 'RefundAmount',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '售后单状态',
          dataIndex: 'SaleAfterOrderStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款状态',
          dataIndex: 'SaleAfterPayStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '执行状态',
          dataIndex: 'SaleAfterExecuteStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 180,
          fixed: 'right',
          actionBtn: [
            {
              name: '编辑',
              icon: '',
              id: 'be70e3bc-d0a1-4658-b642-3134170721b4',
              specialShowFuc: (e) => {
                let record = e || {}
                return [0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '删除',
              icon: '',
              id: 'a6bbfee9-04f6-4ebf-a405-98d8f508cb28',
              specialShowFuc: (e) => {
                let record = e || {}
                return [0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '作废',
              icon: '',
              id: 'acbf6c45-ba2d-4c5d-a39e-9c4935ef8a30',
              specialShowFuc: (e) => {
                let record = e || {}
                return [2].includes(record.AuditStatus) && record.SaleAfterOrderStatus < 4
              },
            },
            {
              name: '详情',
              icon: '',
              id: '263a1f80-3e18-4f99-9979-5040984406aa',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.AuditStatus != 3
              },
            },
            {
              name: '撤回',
              icon: '',
              id: '322f660d-ddbe-421f-94d7-ae3593c14aca',
              specialShowFuc: (e) => {
                let record = e || {}
                return record.AuditStatus === 1
              },
            },
            {
              name: '已完成',
              icon: '',
              id: '7f26147c-27bc-44db-b31f-de7e4847dd42',
              specialShowFuc: (e) => {
                let record = e || {}
                // 当售后单退货退款时，如果入库金额＜售后金额，售后单的状态为“已入库”
                return [1].includes(record.SaleAfterType) && record.SaleAfterOrderStatus === 4 && (record.InBoundAmount || 0) < (record.RefundAmount || 0)
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: true, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '/{v}/SaleAfterOrder/QuerySaleAfterOrders',
        del: '/{v}/SaleAfterOrder/SaleAfterOrderDel',//删除
        cancelOrder: '/{v}/SaleAfterOrder/RevokeSaleAfterOrder',//撤回
        completeOrder: '/{v}/SaleAfterOrder/SaleAfterOrderComplete',//完成
        voidOrder: '/{v}/SaleAfterOrder/SetSaleAfterOrderInvalid',//作废
      },
    }
  },
  created() { },
  methods: {
    searchQuery(queryParam, type) {
      let params = JSON.parse(JSON.stringify(queryParam))
      // console.log(queryParam.SaleAfterPayStatus >= 0)
      // console.log(params)
      params['AuditStatus'] = queryParam['AuditStatus'] >= 0 ? Number(queryParam['AuditStatus']) : null
      params['SaleAfterExecuteStatus'] =
        queryParam['SaleAfterExecuteStatus'] >= 0 ? Number(queryParam['SaleAfterExecuteStatus']) : null
      params['SaleAfterOrderStatus'] =
        queryParam['SaleAfterOrderStatus'] >= 0 ? Number(queryParam['SaleAfterOrderStatus']) : null
      params['SaleAfterPayStatus'] =
        queryParam['SaleAfterPayStatus'] >= 0 ? Number(queryParam['SaleAfterPayStatus']) : null
      this.$refs.table.loadDatas(1, params)
    },
    // 列表操作
    operate(record, type) {
      const that = this
      switch (type) {
        case '编辑':
          this.onDetailClick('merchantAfterSaleListEdit', { id: record.Id, opType: 2 })
          break

        case '详情':
          this.onDetailClick('merchantAfterSaleListDetail', { id: record.Id })
          break
        case '撤回':
          this.$confirm({
            title: '撤回售后单',
            content: '您确定要撤回该售后单吗？',
            onOk() {
              that.cancelOrder(record.Id)
            },
            onCancel() { },
          })
          break
        case '删除':
          this.$confirm({
            title: '删除售后单',
            content: '您确定要删除该售后单吗？',
            onOk() {
              that.deleteOrder(record.Id)
            },
            onCancel() { },
          })
          break
        case '作废':
          this.$confirm({
            title: '作废售后单',
            content: '您确定要作废该售后单吗？',
            onOk() {
              that.voidOrder(record.Id)
            },
            onCancel() { },
          })
          break
        case '已完成':
          this.$confirm({
            title: '完成售后单',
            content: '您确定要完成该售后单吗？',
            onOk() {
              that.completeOrder(record.Id)
            },
            onCancel() { },
          })
          break
      }
    },
    // 删除售后单
    deleteOrder(Id) {
      if (!Id) return
      postAction(this.linkUrl.del, { Id: Id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
      })
    },
    // 作废售后单
    voidOrder(Id) {
      if (!Id) return
      postAction(this.linkUrl.voidOrder, { Id: Id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
      })
    },
    // 撤回售后单
    cancelOrder(Id) {
      if (!Id) return
      postAction(this.linkUrl.cancelOrder, { Id: Id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
      })
    },
    // 完成售后单
    completeOrder(Id) {
      if (!Id) return
      postAction(this.linkUrl.completeOrder, { Id: Id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
      })
    },
  },
}
</script>

<style></style>
