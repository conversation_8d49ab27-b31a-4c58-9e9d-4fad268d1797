<!-- 订单详情-->
<template>
  <div>
    <a-card title="订单详情" style="margin-bottom: 12px">
      <a-button @click="goBack(true,true)" slot="extra">返回</a-button>
    </a-card>
    <a-spin :spinning="loading">
      <a-card style="margin-bottom: 12px">
        <a-row :gutter="gutter">
          <a-col :md="4" :style="{ textAlign: 'center', color: getAuditColor(model && model['OrderStatus']) }">
            <p>
              <a-icon
                :type="getAuditIcon(model && model['OrderStatus'])"
                :style="{ color: getAuditColor(model && model['OrderStatus']), fontSize: '32px' }"
              />
            </p>
            {{ (model && model['OrderStatusStr']) || ' -- ' }}
          </a-col>
          <a-col :md="4">
            <p>订单金额</p>
            ￥{{ (model && model.TotalAmount) || 0 }}
          </a-col>
          <a-col :md="4">
            <p>已认款金额</p>
            ￥{{ model && Number((model.OrderAcceptanceAmount|| 0)  + (model.OffsetAmount|| 0) ) }}
          </a-col>
          <a-col :md="4">
            <p>付款方式</p>
            {{ (model && model.PaymentModeStr) || ' -- ' }}
          </a-col>
          <a-col :md="4">
            <p>订单编号</p>
            {{ (model && model.Code) || ' -- ' }}
          </a-col>
          <a-col :md="4">
            <p>下单时间</p>
            {{ (model && model.PlaceOrderTime) || ' -- ' }}
          </a-col>
        </a-row>
      </a-card>
      <a-card>
        <a-tabs v-model="activeTabKey">
          <a-tab-pane
            :key="item['key']"
            :tab="item['value']"
            v-for="item in tabs"
          >
            <!-- 基本信息 -->
            <BasicInfoTemplateOrderDetail
              ref="iBasicInfoTemplateOrderDetail"
              :Info="{...model, Attachments: Attachments}"
              v-if="activeTabKey == 1 && model && item['key'] == 1"
            />

            <!-- 审核信息 -->
            <SupAuditInformation
              ref="supAuditInformation"
              :bussinessNo="model.AuditId"
              v-if="activeTabKey === 2 && model && item['key'] == 2"
            />
            <!-- 认款信息 -->
            <SubscriptionInfoTemplateOrderDetail
              ref="iSubscriptionInfoTemplateOrderDetail"
              :Info="model"
              v-if="activeTabKey == 3 && model && item['key'] == 3"
            />
            <!-- 发票信息 -->
            <InvoiceInfoTemplateOrderDetail
              ref="iInvoiceInfoTemplateOrderDetail"
              :Info="model"
              v-if="activeTabKey == 4 && model && item['key'] == 4"
            />
            <!-- 调价信息 -->
            <PriceAdjustmentInfoTemplateOrderDetail
              ref="iPriceAdjustmentInfoTemplateOrderDetail"
              :Info="model"
              v-if="activeTabKey == 5 && model && item['key'] == 5"
            />
            <!-- 出库信息 -->
            <OutboundInfoTemplateOrderDetail
              ref="iOutboundInfoTemplateOrderDetail"
              :Info="model"
              v-if="activeTabKey == 6 && model && item['key'] == 6"
            />
            <!-- 售后信息 -->
            <AfterSalesInfoTemplateOrderDetail
              ref="iAfterSalesInfoTemplateOrderDetail"
              :Info="model"
              v-if="activeTabKey == 7 && model && item['key'] == 7"
            />
            <!-- 检验报告 -->
            <InspectionReportTemplateOrderDetail
              ref="iInspectionReportTemplateOrderDetail"
              :Info="model"
              v-if="activeTabKey == 8 && model && item['key'] == 8"
            />
            <!-- 物流信息 -->
            <LogisticsInfoTemplateOrderDetail
              ref="iLogisticsInfoTemplateOrderDetail"
              :Info="model"
              @ok="modalOk"
              v-if="activeTabKey == 9 && model && item['key'] == 9"
            />
            <!-- 操作信息 -->
            <OperationalInfoTemplateOrderDetail
              ref="iOperationalInfoTemplateOrderDetail"
              :Info="model"
              v-if="activeTabKey == 10 && model && item['key'] == 10"
            />
            <!-- 回执单信息 含特订单才有回执信息 -->
            <ReceiptInfoTemplateOrderDetail
              ref="iReceiptInfoTemplateOrderDetail"
              :Info="model"
              v-if="activeTabKey == 11 && model && item['key'] == 11 && model.Type == 2"
            />
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </a-spin>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'ordersManagementListDetail',
  mixins: [EditMixin],
  data() {
    return {
      activeTabKey: 1,
      model: null,
      loading: false,
      id: '',
      code: '',
      tabsList: [
        {
          key: 1,
          value: '基本信息',
          id: 'd1471f57-5417-452b-a2af-71ec9c7782d6',
        },
        {
          key: 2,
          value: '审核信息',
          id: 'd1471f57-5417-452b-a2af-71ec9c7782d6',
        },
        {
          key: 3,
          value: '认款信息',
          id: 'd1471f57-5417-452b-a2af-71ec9c7782d6',
        },
        {
          key: 4,
          value: '发票信息',
          id: '1ffb6d5b-43c3-43f5-974a-6286e4d1289f',
        },
        {
          key: 5,
          value: '调价信息',
          id: 'd1471f57-5417-452b-a2af-71ec9c7782d6',
        },
        {
          key: 6,
          value: '出库信息',
          id: 'd1471f57-5417-452b-a2af-71ec9c7782d6',
        },
        {
          key: 7,
          value: '售后信息',
          id: 'd1471f57-5417-452b-a2af-71ec9c7782d6',
        },
        {
          key: 8,
          value: '检验报告',
          id: '016997b4-b50b-4391-9a1f-7446749420a1',
        },
        {
          key: 9,
          value: '物流信息',
          id: '682284fb-7476-45da-9956-8e46e426a575',
        },
        {
          key: 10,
          value: '操作信息',
          id: 'd1471f57-5417-452b-a2af-71ec9c7782d6',
        },
        {
          key: 11,
          value: '回执单信息',
          id: 'd1471f57-5417-452b-a2af-71ec9c7782d6',
        },
      ],
      httpHead: 'P36008',
      url: {
        detail: '/{v}/Order/OrderDetail',
        getOrderDetailByCode: '/{v}/Order/OrderDetailByCode',
        attachmentsDetail: '/{v}/Order/GetSalesOrderAttachments',
      },
      Attachments: []
    }
  },
  computed: {
    tabs(){
      let tabsList = this.model && this.model.Type != 1 ? this.tabsList : this.tabsList.filter((v) => v.key != 11);
      return this.model && this.model.InvoiceStatus < 3 ? tabsList : tabsList;
    }
  },
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.code = this.$route.query.code || ''
      this.activeTabKey = Number(this.$route.query.key) || 1
      if (this.$route.query['tabId'] && !this.checkBtnPermissions('d1471f57-5417-452b-a2af-71ec9c7782d6')) {
        this.tabsList = this.tabsList.filter((v) => v.id == this.$route.query['tabId'])
      }
      this.loadDetailInfo()
      this.getAttachments()
    }
  },
  created() {},
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      let obj
      if (that.code) {
        obj = getAction(that.url.getOrderDetailByCode, { code: that.code }, that.httpHead)
      } else {
        obj = getAction(that.url.detail, { id: that.id }, that.httpHead)
      }
      obj
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // 获取附件详情
    getAttachments() {
      getAction(this.url.attachmentsDetail, { salesOrderId: this.id, isAudit: false }, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            let Attachments = []
            if (res.Data && res.Data.length) {
              res.Data.forEach(item => {
                Attachments.push(item.Url)
              })
            }
            this.Attachments = Attachments 
          } else {
            this.Attachments = [] 
          }
        })
    },

    modalOk(){
      this.loadDetailInfo()
    },
    getAuditIcon(status) {
      if (status == 99) {
        return 'close-circle'
      } else if (status == 5) {
        return 'check-circle'
      } else {
        return 'clock-circle'
      }
    },
    getAuditColor(status) {
      if (status == 99) {
        return '#999999'
      } else if (status == 5) {
        return '#52c41a'
      } else {
        return '#1890ff'
      }
    },
  },
}
</script>

<style scoped>

</style>
