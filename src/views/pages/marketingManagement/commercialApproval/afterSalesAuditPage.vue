<!--
 * @Author: HL
 * @Description: 财务管理的财务审批-售后审核
 * @Date: 2023-12-14 15:42:59
-->
<template>
  <div>
    <a-card title="商销售后审核详情" style="margin-bottom: 12px" v-if="!isAudit">
      <a-button @click="goBack(true,true)" slot="extra">返回</a-button>
    </a-card>
    <a-card style="margin-bottom: 12px">
      <a-row :gutter="gutter">
        <a-col :md="8" :style="{ textAlign: 'center', color: getAuditColor(model && model['AuditStatus']) }">
          <p>
            <a-icon :type="getAuditIcon(model && model['AuditStatus'])" :style="{ color: getAuditColor(model && model['AuditStatus']), fontSize: '32px' }" />
          </p>
          {{ (model && model['AuditStatusStr']) || ' -- ' }}
        </a-col>
        <a-col :md="5">
          <p>退款金额</p>
          ￥{{ (model && model.RefundAmount) || 0 }}
        </a-col>
        <a-col :md="5">
          <p>退款编号</p>
          {{ (model && model.Code) || ' -- ' }}
        </a-col>

        <a-col :md="5">
          <p>退款申请时间</p>
          {{ (model && model.CreateTime) || ' -- ' }}
        </a-col>
      </a-row>
    </a-card>
    <a-card>
      <div :class="'form-model-style'">
        <a-spin :spinning="loading">
          <a-card :bordered="false" :body-style="{ padding: 0 }">
            <!-- 订单信息 -->
            <a-descriptions title="订单信息">
              <a-descriptions-item label="客户名称">{{ orderDetail.CustomerName || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="客户类别">{{ orderDetail.CustomerTypeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单类型">{{ orderDetail.TypeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="收货人">{{ orderDetail.ReceivingName || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="联系电话">{{ orderDetail.ReceivingPhone || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="收货地址">{{
                (orderDetail.ReceivingAreaFullName || ' -- ') + '' + (orderDetail.ReceivingAddress || ' -- ')
              }}</a-descriptions-item>
              <a-descriptions-item label="快递公司">{{ orderDetail.ExpressCompanyStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单金额">￥{{ orderDetail.TotalAmount || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单编号"><a @click="goOrderInfo">{{ orderDetail.Code || ' -- ' }}</a></a-descriptions-item>
              <a-descriptions-item label="下单时间">{{ orderDetail.PlaceOrderTime || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="付款方式">{{ orderDetail.PaymentModeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="审核状态">{{ orderDetail.AuditStatusStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单状态">{{ orderDetail.OrderStatusStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="开票状态">{{ orderDetail.InvoiceStatusStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="认款状态">{{
                orderDetail.OrderAcceptanceStatusStr || ' -- '
              }}</a-descriptions-item>
            </a-descriptions>
            <!-- 售后信息 -->
            <a-descriptions title="售后信息">
              <a-descriptions-item label="售后金额">￥{{ model.RefundAmount || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="售后编号">{{ model.Code || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="售后申请时间">{{ model.CreateTime || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="售后范围">{{ model.SaleAfterScopeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="售后类型">{{ model.SaleAfterTypeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="售后原因">{{ model.RefundReason || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="售后方式">{{ model.SaleAfterRefundTypeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="备注">{{ model.Remarks || ' -- ' }}</a-descriptions-item>
            </a-descriptions>
            <!-- 退款商品 -->
            <a-row :gutter="20" style="margin-bottom: 24px">
              <a-col :span="24">
                <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">退款商品</div>
              </a-col>
              <a-col :span="24">
                <!-- 列表 -->
                <SimpleTable ref="table" :tab="tabGoods" :columns="columnsGoods">

                </SimpleTable>
              </a-col>
            </a-row>
            <!-- 审核信息 -->
            <SupAuditInformation ref="supAuditInformation" :bussinessNo="model.AuditId" v-if="model.AuditId" @loadAuditResData="loadAuditResData" />
          </a-card>
          <a-affix v-if="isAudit" :offset-bottom="0" style="float: right; width: 100%; text-align: right">
            <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
              <YYLButton :menuId="source == 1?'a9419236-cf4e-4c5a-899e-821a9e005485':(source == 2?'0c518a3f-c8d6-47a2-a700-d3dd8d9dbcee':null)" text="审核通过" type="primary" @click="handlePass" v-if="IsShowBtn" />
              <YYLButton :menuId="source == 1?'31d5d1fe-9e45-4b9b-816e-ad760b1871ee':(source == 2?'59fc31be-2cab-4891-bf1e-f0c4676d9ab3':null)" text="审核驳回" type="danger" @click="handleNoPass" v-if="IsShowBtn" />
              <span v-if="!IsShowBtn" style="padding-right: 8px">无审核权限</span>
              <a-button @click="goBack(true,true)">取消</a-button>
            </a-card>
          </a-affix>
        </a-spin>
        <!-- 审核弹框 -->
        <AuditRemarkModal ref="iAuditRemarkModal" @ok="handleAuditModalOk" v-if="isAudit" />
      </div>
    </a-card>
  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import { getAction, postAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'afterSalesAuditPage',
  mixins: [ListMixin, EditMixin],
  data() {
    return {
      model: {},
      orderDetail: {},
      loading: false,
      id: '',
      isAudit: false,
      tabGoods: {
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        operateBtn: [],
        hideIpagination: true,
      },
      columnsGoods: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批次号',
          dataIndex: 'BatchNo',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '仓库',
          dataIndex: 'WarehouseCode',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产日期',
          dataIndex: 'ProductionDate',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'dayTime' },
        },
        {
          title: '有效期',
          dataIndex: 'GoodsPeriod',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'dayTime' },
        },
        // {
        //   title: '是否赠品',
        //   dataIndex: 'IsGift',
        //   ellipsis: true,
        //   width: 150,
        //   customRender: (t, r, i) => {
        //     return t == null ? '' : t ? '是' : '否'
        //   },
        // },
        {
          title: '销售单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '出库数量',
          dataIndex: 'OutboundCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调价后单价',
          dataIndex: 'AdjustPrice',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '已退款数量',
          dataIndex: 'ExsitSaleAfterCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '可退款数量',
          dataIndex: 'CanSaleAfterCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退货数量',
          dataIndex: 'SaleAfterCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退款金额',
          dataIndex: 'GoodsRefundTotalAmount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'price' },
        },
      ],
      IsHasSuperior: false,
      IsShowBtn: false,
      source: 1,//1来自商销管理审核 2来自财务管理审核
      ApprovalWorkFlowInstanceId: null,
      httpHead: 'P36008',
      url: {
        detail: '/{v}/Audit/GetSaleAfterOrder',
        orderDetail: '/{v}/Order/OrderDetail',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.isAudit = this.$route.query.audit == 1 ? true : false
      this.source = this.$route.query.source || 1
      this.setTitle(this.isAudit ? '商销售后审核' : '商销售后审核详情')
      this.loadDetailInfo()
    }
  },
  created() { },
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data.RefundReason) {
              res.Data.RefundReason = res.Data.RefundReason.join(',')
            }
            that.model = res.Data || {}
            if (this.$refs.table) {
              this.$refs.table.dataSource = this.model.SaleAfterOrderGoodses || []
            }
            this.loadOrderDetailInfo()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    loadAuditResData(data) {
      if (data) {
        this.IsHasSuperior = data['IsHasSuperior'] //是否有上级 true 有上级 false 没有上级
        this.ApprovalWorkFlowInstanceId = data['ApprovalWorkFlowInstanceId']
        let userId = Vue.ls.get(USER_ID)
        if ((data['ApprovalUserIds'] || []).length && this.isAudit) {
          this.IsShowBtn = data['ApprovalUserIds'].some((v) => v == userId)
        }
      }
    },
    // 订单详情
    loadOrderDetailInfo() {
      if (!this.model.OrderId) return
      getAction(this.url.orderDetail, { id: this.model.OrderId }, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.orderDetail = res.Data || {}
      })
    },
    // 商销订单详情
    goOrderInfo() {
      this.onDetailClick('/pages/marketingManagement/ordersManagementListDetail', {
        id: this.model.OrderId,
      })
    },
    /**
     * @description: 通过审核
     * @param {*}
     * @return {*}
     */
    handlePass() {
      this.$refs.iAuditRemarkModal.show(1, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
      })
    },
    /**
     * @description: 驳回审核
     * @param {*}
     * @return {*}
     */
    handleNoPass() {
      this.$refs.iAuditRemarkModal.show(2, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
        IsHasSuperior: this.IsHasSuperior,
      })
    },
    // 审核弹框回调
    handleAuditModalOk(AuditModalOkformData) {
      if (AuditModalOkformData) {
        this.$refs.iAuditRemarkModal.postAuditInstance(AuditModalOkformData, (bool) => {
          if (bool) {
            this.goBack(true, true)
          }
        })
      } else {
        this.goBack(true, true)
      }
    },
  },
}
</script>

<style scoped>

</style>
