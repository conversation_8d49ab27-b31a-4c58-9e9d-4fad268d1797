<!-- 商销开票审核详情-->
<template>
  <div>
    <a-card title="商销开票审核详情" style="margin-bottom: 12px">
      <a-button @click="goBack(true,true)" slot="extra">返回</a-button>
    </a-card>
    <a-spin :spinning="loading">
      <a-card style="margin-bottom: 12px">
        <a-row :gutter="gutter">
          <a-col :md="4" :style="{ textAlign: 'center', color: getAuditColor(model && model['AuditStatus']) }">
            <p>
              <a-icon :type="getAuditIcon(model && model['AuditStatus'])" :style="{ color: getAuditColor(model && model['AuditStatus']), fontSize: '32px' }" />
            </p>
            {{ (model && model['AuditStatusStr']) || ' -- ' }}
          </a-col>
          <a-col :md="4">
            <p>申请类型</p>
            {{ (model && model.InvoiceApplyTypeStr) || ' -- ' }}
          </a-col>
          <a-col :md="4">
            <p>开票金额</p>
            ￥{{ (model && model.InvoiceAmount) || 0 }}
          </a-col>
          <a-col :md="6">
            <p>开票编号</p>
            {{ (model && model.Code) || ' -- ' }}
          </a-col>
          <a-col :md="6">
            <p>申请时间</p>
            {{ (model && model.ApplyTime) || ' -- ' }}
          </a-col>
        </a-row>
      </a-card>
      <a-card>
        <a-tabs v-model="activeTabKey">
          <a-tab-pane :key="item['key']" :tab="item['value']" v-for="item in tabsList">
            <!-- 开票信息 -->
            <BasicInfoTemplateInvoiceDetail ref="iBasicInfoTemplateInvoiceDetail" :Info="model" v-if="activeTabKey == 1 && model && item['key'] == 1" />

            <!-- 审核信息 -->
            <div v-if="activeTabKey === 2 && model && item['key'] == 2">
              <!-- <a-timeline>
                <template v-if="(model.AuditRecords || []).length > 0">
                  <a-timeline-item color="blue" v-for="(group, index) in model.AuditRecords" :key="index">
                    <p>审核人：{{ group.AuditorName || ' -- ' }}</p>
                    <p>审核时间：{{ group.AuditTime || ' -- ' }}</p>
                    <p>审核状态：{{ group.AuditStatusStr || ' -- ' }}</p>
                    <p>审核备注：{{ group.AuditOpinion || ' -- ' }}</p>
                  </a-timeline-item>
                </template>
                <a-timeline-item color="blue">
                  <p>提交人：{{ model.ApplyByName || ' -- ' }}</p>
                  <p>提交时间：{{ model.ApplyTime || ' -- ' }}</p>
                </a-timeline-item>
              </a-timeline> -->
              <SupAuditInformation :bussinessNo="model.AuditId" v-if="activeTabKey == 2 && model.AuditId" />
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>

      <a-affix :offset-bottom="15" class="affix" v-if="isAudit">
        <div style="margin-right: 15px">
          <YYLButton menuId="33602e97-90fa-4642-9600-e0e81c491d62" v-if="IsShowBtn" text="审核通过" type="primary" :loading="confirmLoading" @click="onPassClick" />
          <YYLButton menuId="507f53d3-2931-4caf-a0aa-b625df30b9be" v-if="IsShowBtn" text="审核驳回" type="danger" :loading="confirmLoading" @click="onRejectClick" />
          <span style="margin-right: 15px" v-if="!IsShowBtn">无审核权限</span>
          <a-button @click="goBack(true,true)">取消</a-button>
        </div>
      </a-affix>
    </a-spin>
     <!-- 审核弹框 -->
     <AuditRemarkModal ref="iAuditRemarkModal" @ok="handleAuditModalOk" v-if="isAudit" />
  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'reviewInvoiceForCommercialSaleAudit',
  mixins: [EditMixin],
  data() {
    return {
      isAudit: null,
      isAuditInfo: null,
      IsShowBtn: false,
      IsHasSuperior: false,
      ApprovalWorkFlowInstanceId: null,
      activeTabKey: 1,
      model: null,
      loading: false,
      confirmLoading: false,
      id: '',
      tabsList: [
        {
          key: 1,
          value: '开票信息',
          id: '5eb8f568-188d-4e8e-847b-f1997851b5da',
        },
        {
          key: 2,
          value: '审核信息',
          id: 'e8bb3f63-89ff-4dfa-bdbf-6d8803f6b073',
        },
      ],
      httpHead: 'P36008',
      url: {
        detail: '/{v}/Audit/GetSalesOrderInvoiceV2',
        getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.activeTabKey = Number(this.$route.query.key) || 1
      if (this.$route.query['tabId'] && !this.checkBtnPermissions('e8bb3f63-89ff-4dfa-bdbf-6d8803f6b073')) {
        this.tabsList = this.tabsList.filter((v) => v.id == this.$route.query['tabId'])
      }
      this.isAudit = this.$route.query.audit || null
      this.loadDetailInfo()
    }
  },
  created() { },
  methods: {
    // 获取审核权限和信息
    getApprovalResults(bussinessNo) {
      if (!bussinessNo) return
      getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.IsHasSuperior = res.Data['IsHasSuperior'] //是否有上级 true 有上级 false 没有上级
              this.ApprovalWorkFlowInstanceId = res.Data['ApprovalWorkFlowInstanceId']
              let userId = Vue.ls.get(USER_ID)
              console.log(userId, 'userId')
              if ((res.Data['ApprovalUserIds'] || []).length && this.isAudit) {
                this.IsShowBtn = res.Data['ApprovalUserIds'].some((v) => v == userId)
              }
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => { })
    },
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            if (that.isAudit) {
              that.getApprovalResults(this.model.AuditId)
            }
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // 审核通过
    onPassClick(){
      this.$refs.iAuditRemarkModal.show(1, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
      })

    },
    // 审核失败
    onRejectClick(){
      this.$refs.iAuditRemarkModal.show(2, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
        IsHasSuperior: this.IsHasSuperior,
      })
    },
    // 审核弹框回调
    handleAuditModalOk(AuditModalOkformData) {
      if (AuditModalOkformData) {
        this.confirmLoading = true
        console.log('AuditModalOkformData             ', AuditModalOkformData)
        this.$refs.iAuditRemarkModal.postAuditInstance(AuditModalOkformData, (bool,code) => {
          this.confirmLoading = false
          setTimeout(() => {
            if (bool) {
              this.goBack(true,true)
            }else{
              if(code === 6067){
                this.goBack(true,true)
              }
            }
          },1000);
        })
      } else {
        this.goBack(true,true)
      }
    },
  },
}
</script>

<style scoped>

</style>
