
<!-- 商销订单审核 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea
      ref="SimpleSearchArea"
      :searchInput="searchInput"
      @searchQuery="searchQuery"
    />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="returnColumns"
      :isTableInitData="isTableInitData"
      @operate="operate"
      @changeTab="changeTab"
      @getListAmount="getListAmount"
    />

  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销订单审核',
  name: 'commercialSalesOrderReviewList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '客户信息', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '请输入' },
        {
          name: '客户类别',
          type: 'selectLink',
          vModel: 'CustomerType',
          dataKey: { name: 'ItemValue', value: 'Id' },
          httpParams: { groupPY: 'kehulb' },
          keyWord: 'Name',
          defaultVal: '',
          placeholder: '请选择',
          httpType: 'GET',
          url: '/{v}/Global/GetListDictItemForCustomer',
          httpHead: 'P36001',
        },
        { name: '订单编号', type: 'input', value: '', key: 'Code', defaultVal: '', placeholder: '请输入' },
        {
          name: '订单类型',
          type: 'selectBasicLink',
          value: '',
          vModel: 'Type',
          dictCode: 'EnumSalesOrderType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '付款方式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PaymentMode',
          dictCode: 'EnumPaymentMode',
          notOption: [1, 4, 5, 6],
          defaultVal: '',
          placeholder: '请选择',
        },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '下单时间',
          type: 'timeInput',
          value: '',
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: 1,
        statusKey: '待审核', //标签的切换关键字
        statusList: [
          {
            name: '待审核',
            value: 1,
            count: '',
          },
          {
            name: '审核记录',
            value: 4,
            count: '',
          },
        ],
      },
      columns: [
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户类别',
          dataIndex: 'CustomerTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单编号',
          dataIndex: 'Code',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单类型',
          dataIndex: 'TypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '快递公司',
          dataIndex: 'ExpressCompanyStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单金额(元)',
          dataIndex: 'TotalAmount',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '整单成本(元)',
          dataIndex: 'TotalCost',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '整单毛利(元)',
          dataIndex: 'TotalGrossMargin',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'PlaceOrderByName',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtn: [
            {
              name: '审核',
              icon: '',
              id: 'c7ab9c2f-1cf2-4b3a-932d-b6b3a46fd3fa',
              specialShowFuc: (e) => {
                let record = e || {}
                return this.$refs.table.tab.status == 1
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      columnsLog: [
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户类别',
          dataIndex: 'CustomerTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '联系电话',
          dataIndex: 'CustomerLinkPhone',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单编号',
          dataIndex: 'Code',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '快递公司',
          dataIndex: 'ExpressCompanyStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单金额(元)',
          dataIndex: 'TotalAmount',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '整单成本(元)',
          dataIndex: 'TotalCost',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '整单毛利(元)',
          dataIndex: 'TotalGrossMargin',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '整单毛利率(%)',
          dataIndex: 'TotalGrossMarginRate',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'PlaceOrderByName',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '下单时间',
          dataIndex: 'PlaceOrderTime',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtn: [
            {
              name: '详情',
              icon: '',
              id: '35faa3f2-7d46-4c15-a702-7ffbedaad9e9',
              specialShowFuc: (e) => {
                let record = e || {}
                return this.$refs.table.tab.status != 1
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      returnColumns: [],
      queryParam: {
        IsNeedAudit: true,
      },
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/Audit/SalesOrderList',
        listAmountType: 'GET',
        listAmount: '/{v}/Audit/SalesOrderCount',
      },
    }
  },
  created() {},
  computed: {},
  mounted() {
    this.queryParam['IsNeedAudit'] = true
  },
  activated() {
    // let msgKey = 'purchaseDemandList'
    // this.checkedFromMsg(msgKey)
    this.returnColumns = this.getColumns(this.tab.status)
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    getColumns(status) {
      return status == 1 ? this.columns : this.columnsLog
    },
    getListAmount(data) {
      this.tab.statusList[0].count = (data && data.NeedAuditCount) || 0
      this.tab.statusList[1].count = (data && data.AuditedCount) || 0
    },
    changeTab(value) {
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      this.queryParam.IsNeedAudit = value == 1 ? true : false
      this.returnColumns = this.getColumns(value)
      this.$nextTick(() => {
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.$refs.table.tab.status = 1
      }
      queryParam['IsNeedAudit'] = this.$refs.table.tab.status == 1 ? true : false
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '审核') {
        this.onDetailClick('commercialSalesOrderAuditPage', {
          id: record.Id,
          code:record.Code,
          audit: 1,
        })
      } else if (type == '详情') {
        this.onDetailClick('commercialSalesOrderAuditPage', {
          id: record.Id,
          audit: 0,
        })
      }
    },
  },
}
</script>

<style>
</style>