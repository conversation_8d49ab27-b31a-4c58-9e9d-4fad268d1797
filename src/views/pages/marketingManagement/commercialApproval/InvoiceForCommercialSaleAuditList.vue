
<!-- 商销开票审核 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="returnColumns" :isTableInitData="isTableInitData" @operate="operate" @changeTab="changeTab" @getListAmount="getListAmount" />

  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销开票审核',
  name: 'InvoiceForCommercialSaleAuditList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '开票编号', type: 'input', value: '', key: 'Code', defaultVal: '', placeholder: '请输入' },
        {
          name: '对应订单编号',
          type: 'input',
          value: '',
          key: 'SalesOrderCode',
          defaultVal: '',
          placeholder: '请输入',
        },
        { name: '客户信息', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '请输入' },
        {
          name: '发票类型',
          type: 'selectBasicLink',
          value: '',
          vModel: 'InvoiceType',
          dictCode: 'EnumSaleInvoiceType',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '开票状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'InvoiceStatus',
          dictCode: 'EnumInvoiceStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '申请时间',
          type: 'timeInput',
          value: '',
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
          rangeDate:[],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: 1,
        statusKey: '待审核', //标签的切换关键字
        statusList: [
          {
            name: '待审核',
            value: 1,
            count: '',
          },
          {
            name: '审核记录',
            value: 4,
            count: '',
          },
        ],
      },
      columns: [
        {
          title: '开票编号',
          dataIndex: 'Code',
          width: 250,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'SalesOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '申请类型',
          dataIndex: 'InvoiceApplyTypeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '发票类型',
          dataIndex: 'InvoiceTypeV2Str',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '开票金额',
          dataIndex: 'InvoiceAmount',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'price' },
        },
        {
          title: '申请人',
          dataIndex: 'ApplyByName',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        }
      ],
      columnsEnd: [
        {
          title: '申请时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          actionBtn: [
            {
              name: '审核',
              icon: '',
              id: 'f84673e7-b973-4786-9117-e507d9914b51',
              specialShowFuc: (e) => {
                let record = e || {}
                return this.$refs.table.tab.status == 1
              },
            },
            {
              name: '详情',
              icon: '',
              id: 'c56c87af-6be1-4bc5-8448-0f700bf921e9',
              specialShowFuc: (e) => {
                let record = e || {}
                return this.$refs.table.tab.status != 1
              },
            },
            // 未开票 = 1,
            // 开票中 = 2,
            // 已开票 = 3,
            // 已冲红 = 4,
            // 已作废 = 5,
            // 部分冲红 = 6,
            // 开票失败 = 7,
            // 部分作废 = 8,
            // 推送发票中心失败 = 9,
            // {
            //   name: '重推',
            //   icon: '',
            //   id: 'b3d2deae-9f28-456d-8be7-b8615fc0507f',
            //   specialShowFuc: (e) => {
            //     let record = e || {}
            //     return this.$refs.table.tab.status != 1 && [1, 9].includes(record.InvoiceStatus) && record.AuditStatus === 2
            //   },
            // },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      columnsA: [{
        title: '开票状态',
        dataIndex: 'InvoiceStatusStr',
        width: 150,
        ellipsis: true,
        scopedSlots: { customRender: 'component' },
      }],
      returnColumns: [],
      queryParam: {
        IsNeedAudit: true,
      },
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/Audit/SalesOrderInvoiceAuditListV2',
        listAmount: '/{v}/Audit/SalesOrderInvoiceCountV2',
        rethrust: '/{v}/OrderInvoice/RedoPushApplyInvoice',//重推
      },
    }
  },
  created() { },
  computed: {},
  mounted() {
    this.queryParam['IsNeedAudit'] = true
  },
  activated() {
    // let msgKey = 'purchaseDemandList'
    // this.checkedFromMsg(msgKey)
    this.returnColumns = this.getColumns(this.tab.status)
    this.$refs.table.loadDatas(null, this.$refs.SimpleSearchArea.queryParam)
  },
  methods: {
    getColumns(status) {
      return status === 1 ? this.columns.concat(this.columnsEnd) : this.columns.concat(this.columnsA).concat(this.columnsEnd)
    },
    getListAmount(data) {
      this.tab.statusList[0].count = (data && data.NeedAuditCount) || 0
      this.tab.statusList[1].count = (data && data.AuditedCount) || 0
    },
    changeTab(value) {
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      this.queryParam.IsNeedAudit = value == 1 ? true : false
      this.returnColumns = this.getColumns(value)
      this.$nextTick(() => {
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.$refs.table.tab.status = 1
      }
      queryParam['IsNeedAudit'] = this.$refs.table.tab.status == 1 ? true : false
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '审核') {
        this.onDetailClick('reviewInvoiceForCommercialSaleAudit', {
          id: record.Id,
          audit: 1,
        })
      } else if (type == '详情') {
        this.onDetailClick('reviewInvoiceForCommercialSaleAudit', {
          id: record.Id,
          audit: 0,
        })
      } else if (type == '重推') {
        this.rethrust(record)
      }
    },
    // 重推
    rethrust(record) {
      this.$refs.table.loading = true
      getAction(this.linkUrl.rethrust, { salesOrderInvoiceApplyNo: record.Code }, this.linkHttpHead)
        .then(res => {
          if (res.Data) {
            this.$message.success('操作成功')
            this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch(err => {
          this.$message.error(err.Msg || err)
        })
        .finally(() => {
          this.$refs.table.loading = false
        })
    },
  },
}
</script>

<style>
</style>