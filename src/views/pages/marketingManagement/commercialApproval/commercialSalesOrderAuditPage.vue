<!-- 订单审核-->
<template>
  <div>
    <a-card title="订单审核详情" style="margin-bottom: 12px" v-if="!isAudit">
      <a-button @click="goBack(true,true)" slot="extra">返回</a-button>
    </a-card>
    <a-spin :spinning="loading">
      <a-card style="margin-bottom: 12px">
        <a-row :gutter="gutter">
          <a-col :md="8" :style="{ textAlign: 'center', color: getAuditColor(model && model['AuditStatus']) }">
            <p>
              <a-icon :type="getAuditIcon(model && model['AuditStatus'])" :style="{ color: getAuditColor(model && model['AuditStatus']), fontSize: '32px' }" />
            </p>
            {{ (model && model['AuditStatusStr']) || ' -- ' }}
          </a-col>
          <a-col :md="4">
            <p>订单金额</p>
            ￥{{ (model && model.TotalAmount) || 0 }}
          </a-col>
          <a-col :md="4">
            <p>付款方式</p>
            {{ (model && model.PaymentModeStr) || ' -- ' }}
          </a-col>
          <a-col :md="4">
            <p>订单编号</p>
            {{ (model && model.Code) || ' -- ' }}
          </a-col>

          <a-col :md="4">
            <p>下单时间</p>
            {{ (model && model.PlaceOrderTime) || ' -- ' }}
          </a-col>
        </a-row>
      </a-card>
      <a-card>
        <div :class="'form-model-style'">
          <a-spin :spinning="loading">
            <a-card :bordered="false" :body-style="{ padding: 0 }">
              <!-- 订单信息 -->
              <a-descriptions title="订单信息">
                <a-descriptions-item label="客户名称">{{ model.CustomerName || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="客户类别">{{ model.CustomerTypeStr || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="委托人">{{ model.DelegateName || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="订单类型">{{ model.TypeStr || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="收货人">{{ model.ReceivingName || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="联系电话">{{ model.ReceivingPhone || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="收货地址">{{
                  (model.ReceivingAreaFullName || ' -- ') + '' + (model.ReceivingAddress || ' -- ')
                }}</a-descriptions-item>
                <a-descriptions-item label="快递公司">{{ model.ExpressCompanyStr || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="备注信息">{{ model.Remarks || ' -- ' }}</a-descriptions-item>
              </a-descriptions>
              <!-- 应收/应付信息 -->
              <a-row :gutter="gutter" style="margin-bottom: 24px">
                <a-col :span="24">
                  <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">应收/应付信息</div>
                </a-col>
                <a-col :span="24">
                  <!-- 列表 -->
                  <SimpleTable ref="table" :tab="tab" :columns="columns">
                    <span slot="commonContent" slot-scope="{ text }">
                      <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                    </span>
                  </SimpleTable>
                </a-col>
              </a-row>
              <!-- 商品信息 -->
              <a-row :gutter="gutter" style="margin-bottom: 24px">
                <a-col :span="24">
                  <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">商品信息</div>
                </a-col>
                <a-col :span="24">
                  <!-- 列表 -->
                  <SimpleTable ref="tableGoods" showTab :tab="tabGoods" :columns="columnsGoods">
                    <span slot="commonContent" slot-scope="{ text }">
                      <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                    </span>
                  </SimpleTable>
                </a-col>
                <a-col :span="24">
                  <span style="margin-right: 10px">订单金额：<span style="color: red; font-size: 30px">¥{{ model.TotalAmount || 0 }}</span>
                  </span>
                  <span style="margin-right: 10px">整单成本：<span>¥{{ model.TotalCost || 0 }}</span>
                  </span>
                  <span style="margin-right: 10px">整单毛利：<span>¥{{ model.TotalGrossMargin || 0 }}</span>
                  </span>
                  <span style="margin-right: 10px">整单毛利率：<span>{{ model.TotalGrossMarginRate || ' -- ' }}%</span>
                  </span>
                </a-col>
                <a-col :span="24">
                  <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">上传附件</div>
                </a-col>
                <a-col :span="24">
                  <MultiUpload
                    :max="10"
                    :images="Attachments"
                    :uploadText="uploadText"
                    :fileType="4"
                    :bName="BName"
                    :dir="Dir"
                    :readOnly="!isAudit"
                    :fileSize="50" 
                    @change="multiUploadChange" />
                </a-col>
              </a-row>
              <!-- 审核信息 -->
              <a-row :gutter="gutter" style="margin-bottom: 24px">
                <a-col :span="24">
                  <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">审核信息</div>
                </a-col>
                <a-col :span="24">
                  <SupAuditInformation ref="supAuditInformation" :bussinessNo="model.AuditId" v-if="model && model.AuditId" />
                </a-col>
              </a-row>
            </a-card>
            <a-affix v-if="isAudit" :offset-bottom="0" style="float: right; width: 100%; text-align: right">
              <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
                <YYLButton menuId="68f3d46f-5b7e-4f5f-a5f1-b8a71ba7165a" text="审核通过" type="primary" :loading="confirmLoading" @click="handlePass" v-if="IsShowBtn" />
                <YYLButton menuId="1008ac38-6f4c-4964-a324-1e478d81850e" text="审核驳回" type="danger" :loading="confirmLoading" @click="handleNoPass" v-if="IsShowBtn" />
                <span v-if="!IsShowBtn" style="margin-right: 15px">无审核权限</span>
                <a-button @click="goBack(true,true)">取消</a-button>
              </a-card>
            </a-affix>
          </a-spin>
          <!-- 审核弹框 -->
          <AuditRemarkModal ref="iAuditRemarkModal" @ok="handleAuditModalOk" v-if="isAudit" />
          <!-- 查看照片 -->
          <PurchaseDealImgModal ref="PurchaseDealImgModal"></PurchaseDealImgModal>
        </div>
      </a-card>
    </a-spin>
  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'commercialSalesOrderAuditPage',
  mixins: [EditMixin],
  components: { JEllipsis },
  data() {
    return {
      model: {},
      TotalBalanceAsync: [],
      loading: false,
      confirmLoading: false,
      id: '',
      code: '',
      isAudit: false,
      tab: { hideIpagination: true },
      columns: [
        {
          title: '应收余额',
          dataIndex: 'ReceivableBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '预收余额',
          dataIndex: 'AdvancePaymentBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '应付余额',
          dataIndex: 'PayableBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '预付余额',
          dataIndex: 'PrepaymentBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
      ],
      tabGoods: {
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        rowKey: 'Id',
        rowSelection: {
          type: 'checkbox',
        },
        sortArray: ['LastSalesPrice'],
        hideIpagination: true,
        operateBtn: [],
      },
      columnsGoods: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产日期',
          dataIndex: 'ProductionDate',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '有效期',
          dataIndex: 'GoodsPeriod',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批次号',
          dataIndex: 'BatchNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '仓库',
          dataIndex: 'WarehouseCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批次可销库存',
          dataIndex: 'BatchCanSaleInventory',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商销政策底价',
          dataIndex: 'PolicyPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '采购含税价',
          dataIndex: 'GoodsPurchaseCostPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        // {
        //   title: '移动平均成本',
        //   dataIndex: 'AvgCostPrice',
        //   ellipsis: true,
        //   width: 100,
        //   scopedSlots: { customRender: 'commonSlot' },
        // },
        {
          title: '商销底价',
          dataIndex: 'GoodsLimitPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '末次销售价',
          dataIndex: 'LastSalesPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        // {
        //   title: '采购管理模式',
        //   dataIndex: 'GoodsProcurementManagementModelName',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        // {
        //   title: '是否赠品',
        //   dataIndex: 'IsGift',
        //   ellipsis: true,
        //   width: 150,
        //   customRender: (t) => {
        //     return t ? '是' : '否'
        //   },
        // },
        // {
        //   title: '商品条码',
        //   dataIndex: 'GoodsBarCode',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        {
          title: '销售单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '销售数量',
          dataIndex: 'Count',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '小计金额',
          dataIndex: 'SubtotalAmount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '单品毛利',
          dataIndex: 'GrossMargin',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '单品总毛利',
          dataIndex: 'SubtotalGrossMargin',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
        },
        // {
        //   title: '是否特赠品',
        //   dataIndex: 'IsSpecialGift',
        //   ellipsis: true,
        //   width: 100,
        //   fixed: 'right',
        //   customRender: t => {
        //     return t ? '是' : '否'
        //   }
        // },
        {
          title: '备注',
          dataIndex: 'Remarks',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
      ],
      IsHasSuperior: false,
      IsShowBtn: false,
      ApprovalWorkFlowInstanceId: null,
      httpHead: 'P36008',
      url: {
        detail: '/{v}/Audit/SalesOrderDetail',
        getTotalBalanceAsync: '/{v}/Account/GetTotalBalanceAsync',
        getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
        checkDataBeforeAudit: '/{v}/Order/CheckDataBeforeAudit',
        attachmentsDetail: '/{v}/Order/GetSalesOrderAttachments',
        modifyAttachments: '/{v}/Order/ModifyAttachments'
      },
      uploadText: '上传图片',
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
      Attachments: []
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.code = this.$route.query.code || ''
      this.isAudit = this.$route.query.audit == 1 ? true : false
      this.setTitle(this.isAudit ? '商销订单审核' : '商销订单审核详情')
      this.loadDetailInfo()
      this.getAttachments()
    }
  },
  created() { },
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            this.$set(this.$refs.tableGoods, 'dataSource', (this.model['Details'] || []).map(it => {
              return { ...it, ProductionDate: (it.ProductionDate || '').slice(0, 10), GoodsPeriod: (it.GoodsPeriod || '').slice(0, 10) }
            }))
            this.getTotalBalanceAsync()
            if (this.isAudit) this.getApprovalResults(this.model.AuditId)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // 获取附件详情
    getAttachments() {
      getAction(this.url.attachmentsDetail, { salesOrderId: this.id, isAudit: true }, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            let Attachments = []
            if (res.Data && res.Data.length) {
              res.Data.forEach(item => {
                Attachments.push(item.Url)
              })
            }
            this.Attachments = Attachments
          } else {
            this.Attachments = []
          }
        })
    },
    // 应收/应付信息
    getTotalBalanceAsync() {
      if (!this.model.CustomerId) return
      getAction(this.url.getTotalBalanceAsync, { CustomerId: this.model.CustomerId }, 'P36011').then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.TotalBalanceAsync = [res.Data]
        this.$refs.table.dataSource = this.TotalBalanceAsync
      })
    },
    // IsHasSuperior
    getApprovalResults(bussinessNo) {
      if (!bussinessNo) return
      getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.IsHasSuperior = res.Data['IsHasSuperior'] //是否有上级 true 有上级 false 没有上级
              this.ApprovalWorkFlowInstanceId = res.Data['ApprovalWorkFlowInstanceId']
              let userId = Vue.ls.get(USER_ID)
              if ((res.Data['ApprovalUserIds'] || []).length && this.isAudit) {
                console.log(res.Data, userId)
                this.IsShowBtn = res.Data['ApprovalUserIds'].some((v) => v == userId)
              }
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => { })
    },
    // 审核通过  先验证客户
    checkDataBeforeAudit(OrderId, call) {
      if (!OrderId) return
      getAction(this.url.checkDataBeforeAudit, { salesOrderId: OrderId }, 'P36008')
        .then((res) => {
          if (res.IsSuccess && res.Data) {
            call && call(true)
          } else {
            call && call(false)
            this.$message.error(res.Msg)
          }
        })
        .finally(() => { })
    },
    /**
     * @description: 通过审核
     * @param {*}
     * @return {*}
     */
    handlePass() {
      this.checkReturnInventory(this.goPassClick)
    },
    goPassClick() {
      this.checkDataBeforeAudit(this.id, isBool => {
        if (isBool) {
          this.$refs.iAuditRemarkModal.show(1, {
            ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
          })
        }
      })
    },
    // 校验采购退货单库存是否足够
    checkReturnInventory(callback) {
      if (!this.code) {
        return
      }
      let formData = {
        saleOrderNo: this.code,
      }
      this.confirmLoading = true
      getAction('/{v}/Order/ValidateGoodsStockIsEnough', formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            callback && callback()
          } else {
            this.$message.error(res.Msg)
          }
        })
        .catch((e) => {
          this.confirmLoading = false
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    /**
     * @description: 驳回审核
     * @param {*}
     * @return {*}
     */
    handleNoPass() {
      this.$refs.iAuditRemarkModal.show(2, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
        IsHasSuperior: this.IsHasSuperior,
      })
    },
    // 审核弹框回调
    handleAuditModalOk(AuditModalOkformData) {
      postAction(this.url.modifyAttachments, {
        SalesOrderId: this.id,
        IsAudit: this.isAudit,
        NewAttachments: this.Attachments
      }, this.httpHead).then((res) => {
        if (res.IsSuccess) {
          this.$message.success('上传附件成功！')
        } else {
          this.$message.error('上传附件失败！')
        }
      })
      if (AuditModalOkformData) {
        console.log('AuditModalOkformData             ', AuditModalOkformData)
        this.$refs.iAuditRemarkModal.postAuditInstance(AuditModalOkformData, (bool, code) => {
          setTimeout(() => {
            if (bool) {
              this.goBack(true,true)
            } else {
              if (code === 6067) {
                this.goBack(true,true)
              }
            }
          }, 1000);
        })
      } else {
        this.goBack(true,true)
      }
    },
    // 上传回调
    multiUploadChange(images) {
      this.Attachments = images
    },
  },
}
</script>

<style scoped>

</style>
