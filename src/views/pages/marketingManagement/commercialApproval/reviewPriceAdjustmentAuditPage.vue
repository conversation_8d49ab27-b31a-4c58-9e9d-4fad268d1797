<!-- 商销调价审核-->
<template>
  <div>
    <a-card title="商销调价审核详情" style="margin-bottom: 12px" v-if="!isAudit">
      <a-button @click="goBack(true,true)" slot="extra">返回</a-button>
    </a-card>
    <a-card style="margin-bottom: 12px">
      <a-row :gutter="gutter">
        <a-col :md="8" :style="{ textAlign: 'center', color: getAuditColor(model && model['AuditStatus']) }">
          <p>
            <a-icon
              :type="getAuditIcon(model && model['AuditStatus'])"
              :style="{ color: getAuditColor(model && model['AuditStatus']), fontSize: '32px' }"
            />
          </p>
          {{ (model && model['AuditStatusStr']) || ' -- ' }}
        </a-col>
        <a-col :md="5">
          <p>调价金额</p>
          ￥{{ (model && model.AdjustAmount) || 0 }}
        </a-col>
        <a-col :md="5">
          <p>调价编号</p>
          {{ (model && model.Code) || ' -- ' }}
        </a-col>

        <a-col :md="5">
          <p>差价调整时间</p>
          {{ (model && model.CreateTime) || ' -- ' }}
        </a-col>
      </a-row>
    </a-card>
    <a-card>
      <div :class="'form-model-style'">
        <a-spin :spinning="loading">
          <a-card :bordered="false" :body-style="{ padding: 0 }">
            <!-- 订单信息 -->
            <a-descriptions title="订单信息">
              <a-descriptions-item label="客户名称">{{ orderDetail.CustomerName || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="客户类别">{{ orderDetail.CustomerTypeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单类型">{{ orderDetail.TypeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="收货人">{{ orderDetail.ReceivingName || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="联系电话">{{ orderDetail.ReceivingPhone || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="收货地址">{{
                (orderDetail.ReceivingAreaFullName || ' -- ') + '' + (orderDetail.ReceivingAddress || ' -- ')
              }}</a-descriptions-item>
              <a-descriptions-item label="快递公司">{{ orderDetail.ExpressCompanyStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单金额">￥{{ orderDetail.TotalAmount || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单编号">{{ orderDetail.Code || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="下单时间">{{ orderDetail.PlaceOrderTime || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="付款方式">{{ orderDetail.PaymentModeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="审核状态">{{ orderDetail.AuditStatusStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单状态">{{ orderDetail.OrderStatusStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="开票状态">{{ orderDetail.InvoiceStatusStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="认款状态">{{
                orderDetail.OrderAcceptanceStatusStr || ' -- '
              }}</a-descriptions-item>
              <a-descriptions-item label="差价调整原因">{{ model.AdjustReason || ' -- ' }}</a-descriptions-item>
            </a-descriptions>
            <!-- 调价信息 -->
            <a-row :gutter="20" style="margin-bottom: 24px">
              <a-col :span="24">
                <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">调价信息</div>
              </a-col>
              <a-col :span="24">
                <!-- 列表 -->
                <SimpleTable ref="table" :tab="tabGoods" :isTableInitData="isTableInitData" :columns="columnsGoods">
                  <span slot="commonContent" slot-scope="{ text }">
                    <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                  </span>
                </SimpleTable>
              </a-col>
            </a-row>
            <a-row :gutter="20" style="margin-bottom: 24px">
              <a-col :span="24">
                <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">审核信息</div>
              </a-col>
              <a-col :span="24">
                <!-- 审核信息 -->
                <SupAuditInformation
                  ref="supAuditInformation"
                  :bussinessNo="model.AuditId"
                  v-if="model && model.AuditId"
                />
              </a-col>
            </a-row>
          </a-card>
          <a-affix v-if="isAudit" :offset-bottom="0" style="float: right; width: 100%; text-align: right">
            <a-card :bordered="false" style="border-top: 1px solid #e8e8e8">
              <YYLButton
                menuId="e59e9e84-336c-497a-b270-38b88fec2d62"
                text="审核通过"
                type="primary"
                @click="handlePass"
                v-if="IsShowBtn"
              />
              <YYLButton
                menuId="90c6f64e-d449-4655-b46c-27570aec3346"
                text="审核驳回"
                type="danger"
                @click="handleNoPass"
                v-if="IsShowBtn"
              />
              <span v-if="!IsShowBtn" style="margin-right: 15px">无审核权限</span>
              <a-button @click="goBack(true,true)">取消</a-button>
            </a-card>
          </a-affix>
        </a-spin>
        <!-- 审核弹框 -->
        <AuditRemarkModal ref="iAuditRemarkModal" @ok="handleAuditModalOk" v-if="isAudit" />
      </div>
    </a-card>
  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'reviewPriceAdjustmentAuditPage',
  mixins: [EditMixin],
  components: { JEllipsis },
  data() {
    return {
      model: {},
      orderDetail: {},
      loading: false,
      id: '',
      isAudit: false,
      tabGoods: {
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        operateBtn: [],
        hideIpagination: true,
      },
      columnsGoods: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批次号',
          dataIndex: 'BatchNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '仓库',
          dataIndex: 'WarehouseCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '最低商销价',
        //   dataIndex: 'GoodsPurchaseCostPrice',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'commonSlot' },
        // },
        // {
        //   title: '商销报价',
        //   dataIndex: 'GoodsLimitPrice',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'commonSlot' },
        // },
        // {
        //   title: '末次销售价',
        //   dataIndex: 'LastSalesPrice',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'commonSlot' },
        // },
        // {
        //   title: '采购管理模式',
        //   dataIndex: 'GoodsProcurementManagementModelName',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        // {
        //   title: '是否赠品',
        //   dataIndex: 'IsGift',
        //   ellipsis: true,
        //   width: 150,
        //   customRender: (t, r, i) => {
        //     return t == null ? '' : t ? '是' : '否'
        //   },
        // },
        {
          title: '销售单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '出库数量',
          dataIndex: 'OutboundCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '小计',
          dataIndex: 'SubtotalAmount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'commonSlot' },
        },
        // {
        //   title: '是否特赠品',
        //   dataIndex: 'IsSpecialGift',
        //   ellipsis: true,
        //   width: 100,
        //   customRender: (t, r, i) => {
        //     return t == null ? '' : t ? '是' : '否'
        //   }
        // },
        {
          title: '调价后单价',
          dataIndex: 'AdjustPrice',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调价数量',
          dataIndex: 'AdjustCount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调价小计金额',
          dataIndex: 'AdjustTotalAmount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '每盒差额',
          dataIndex: 'AdjustPerSpreadPrice',
          ellipsis: true,
          width: 100,
          precision:6,
          fixed: 'right',
          scopedSlots: { customRender: 'Price' },
        },
        {
          title: '备注',
          dataIndex: 'Remarks',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
      ],
      isTableInitData: false,
      IsHasSuperior: false,
      IsShowBtn: false,
      ApprovalWorkFlowInstanceId: null,
      httpHead: 'P36008',
      url: {
        detail: '/{v}/Audit/GetSalesOrderAdjustPrice',
        orderDetail: '/{v}/Order/OrderDetail',
        getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.isAudit = this.$route.query.audit == 1 ? true : false
      this.setTitle(this.isAudit ? '商销调价审核' : '商销调价审核详情')
      this.loadDetailInfo()
    }
  },
  created() {},
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            this.$refs.table.dataSource = that.model.SalesOrderAdjustPriceDetails || []
            this.loadOrderDetailInfo()
            if (this.isAudit) this.getApprovalResults(this.model.AuditId)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // 订单详情
    loadOrderDetailInfo() {
      if (!this.model.SalesOrderId) return
      getAction(this.url.orderDetail, { id: this.model.SalesOrderId }, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.orderDetail = res.Data || {}
      })
    },
    // IsHasSuperior
    getApprovalResults(bussinessNo) {
      if (!bussinessNo) return
      getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
        .then((res) => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.IsHasSuperior = res.Data['IsHasSuperior'] //是否有上级 true 有上级 false 没有上级
              this.ApprovalWorkFlowInstanceId = res.Data['ApprovalWorkFlowInstanceId']
              let userId = Vue.ls.get(USER_ID)
              if ((res.Data['ApprovalUserIds'] || []).length && this.isAudit) {
                this.IsShowBtn = res.Data['ApprovalUserIds'].some((v) => v == userId)
              }
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {})
    },
    /**
     * @description: 通过审核
     * @param {*}
     * @return {*}
     */
    handlePass() {
      this.$refs.iAuditRemarkModal.show(1, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
      })
    },
    /**
     * @description: 驳回审核
     * @param {*}
     * @return {*}
     */
    handleNoPass() {
      this.$refs.iAuditRemarkModal.show(2, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
        IsHasSuperior: this.IsHasSuperior,
      })
    },
    // 审核弹框回调
    handleAuditModalOk(AuditModalOkformData) {
      if (AuditModalOkformData) {
        console.log('AuditModalOkformData             ', AuditModalOkformData)
        this.$refs.iAuditRemarkModal.postAuditInstance(AuditModalOkformData, (bool,code) => {
          setTimeout(() => {
            if (bool) {
              this.goBack(true,true)
            }else{
              if(code === 6067){
                this.goBack(true,true)
              }
            }
          },1000);
        })
      } else {
        this.goBack(true,true)
      }
    },
  },
}
</script>

<style scoped>

</style>
