
<!-- 含特回执审核 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea
      ref="SimpleSearchArea"
      :searchInput="searchInput"
      @searchQuery="searchQuery"
    />
    <!-- 列表 -->
    <SimpleTable
      ref="table"
      :linkHttpHead="linkHttpHead"
      :linkUrl="linkUrl"
      :linkUrlType="linkUrlType"
      showTab
      cardBordered
      :tab="tab"
      :queryParam="queryParam"
      :columns="returnColumns"
      :isTableInitData="isTableInitData"
      @operate="operate"
      @changeTab="changeTab"
      @getListAmount="getListAmount"
    />

  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '含特回执审核',
  name: 'includingSpecialReceiptReviewList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择

        {
          name: '客户信息',
          type: 'input',
          value: '',
          key: 'CustomerKey',
          defaultVal: '',
          placeholder: '客户名称/编号/拼音码',
        },
        {
          name: '订单编号',
          type: 'input',
          value: '',
          key: 'SalesOrderCode',
          defaultVal: '',
          placeholder: '请输入',
        },
        { name: '提交人', type: 'input', value: '', key: 'UploadByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '提交时间',
          type: 'timeInput',
          value: '',
          key: ['UploadTimeBegin', 'UploadTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: 1,
        statusKey: '待审核', //标签的切换关键字
        statusList: [
          {
            name: '待审核',
            value: 1,
            count: '',
          },
          {
            name: '审核记录',
            value: 4,
            count: '',
          },
        ],
      },
      columns: [
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '订单编号',
          dataIndex: 'SalesOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单类型',
          dataIndex: 'SalesOrderTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单金额',
          dataIndex: 'SalesOrderTotalAmount',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '提交人',
          dataIndex: 'UploadByName',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '提交时间',
          dataIndex: 'UploadTime',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtn: [
            {
              name: '审核',
              icon: '',
              id: '0e519999-24ef-4e79-896a-26e163b488ea',
              specialShowFuc: (e) => {
                let record = e || {}
                return this.$refs.table.tab.status == 1
              },
            },
            {
              name: '详情',
              icon: '',
              id: '80eb05c9-5541-4386-b25c-2db16f7541e3',
              specialShowFuc: (e) => {
                let record = e || {}
                return this.$refs.table.tab.status != 1
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],

      returnColumns: [],
      queryParam: {
        IsNeedAudit: true,
      },
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/Audit/SalesOrderReceiptAuditList',
        listAmountType: 'GET',
        listAmount: '/{v}/Audit/SalesOrderReceiptAuditCount',
      },
    }
  },
  created() {},
  computed: {},
  mounted() {
    this.queryParam['IsNeedAudit'] = true
  },
  activated() {
    // let msgKey = 'purchaseDemandList'
    // this.checkedFromMsg(msgKey)
    this.returnColumns = this.getColumns(this.tab.status)
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    getColumns(status) {
      return this.columns
    },
    getListAmount(data) {
      this.tab.statusList[0].count = (data && data.NeedAuditCount) || 0
      this.tab.statusList[1].count = (data && data.AuditedCount) || 0
    },
    changeTab(value) {
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      this.queryParam.IsNeedAudit = value == 1 ? true : false
      this.returnColumns = this.getColumns(value)
      this.$nextTick(() => {
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.$refs.table.tab.status = 1
      }
      queryParam['IsNeedAudit'] = this.$refs.table.tab.status == 1 ? true : false
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '审核') {
        this.onDetailClick('includingSpecialReceiptReviewAuditPage', {
          id: record.Id,
          audit: 1,
        })
      } else if (type == '详情') {
        this.onDetailClick('includingSpecialReceiptReviewAuditPage', {
          id: record.Id,
          audit: 0,
        })
      }
    },
  },
}
</script>

<style>
</style>