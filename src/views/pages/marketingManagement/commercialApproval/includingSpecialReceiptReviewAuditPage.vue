<!-- 含特回执审核-->
<template>
  <div>
    <a-card
      title="含特回执审核详情"
      style="margin-bottom:12px"
      v-if="!isAudit"
    >
      <a-button
        @click="goBack(true,true)"
        slot="extra"
      >返回</a-button>
    </a-card>
    <a-card style="margin-bottom:12px">
      <a-row :gutter="gutter">
        <a-col
          :md="8"
          :style="{textAlign:'center',color:getAuditColor( model&&model['AuditStatus'])}"
        >
          <p>
            <a-icon
              :type="getAuditIcon(model&&model['AuditStatus'])"
              :style="{color:getAuditColor( model&&model['AuditStatus']),fontSize:'32px'}"
            />
          </p>
          {{(model && model['AuditStatusStr']) || ' -- '}}
        </a-col>
        <a-col :md="5">
          <p>订单金额</p>
          ￥{{(model && model.SalesOrderTotalAmount) || 0}}
        </a-col>
        <a-col :md="5">
          <p>订单编号</p>
          {{(model && model.SalesOrderCode)  || ' -- '}}
        </a-col>

        <a-col :md="5">
          <p>下单时间</p>
          {{( orderDetail &&  orderDetail.PlaceOrderTime) || ' -- '}}
        </a-col>

      </a-row>
    </a-card>
    <a-card>
      <div :class="'form-model-style'">
        <a-spin :spinning="loading">
          <a-card
            :bordered="false"
            :body-style="{padding:0}"
          >
            <!-- 订单信息 -->
            <a-descriptions title="订单信息">
              <a-descriptions-item label="客户名称">{{ orderDetail.CustomerName || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="客户类别">{{ orderDetail.CustomerTypeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单类型">{{ orderDetail.TypeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="收货人">{{ orderDetail.ReceivingName || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="联系电话">{{ orderDetail.ReceivingPhone || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="收货地址">{{ (orderDetail.ReceivingAreaFullName || ' -- ') + '' + (orderDetail.ReceivingAddress || ' -- ') }}</a-descriptions-item>
              <a-descriptions-item label="快递公司">{{ orderDetail.ExpressCompanyStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单金额">￥{{ orderDetail.TotalAmount || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单编号"><a @click="onDetailClick('/pages/marketingManagement/ordersManagementListDetail', { id: orderDetail.Id })">{{ orderDetail.Code || ' -- ' }}</a></a-descriptions-item>
              <a-descriptions-item label="下单时间">{{ orderDetail.PlaceOrderTime || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="付款方式">{{ orderDetail.PaymentModeStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="审核状态">{{ orderDetail.AuditStatusStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="订单状态">{{ orderDetail.OrderStatusStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="开票状态">{{ orderDetail.InvoiceStatusStr || ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="认款状态">{{ orderDetail.OrderAcceptanceStatusStr || ' -- ' }}</a-descriptions-item>
            </a-descriptions>
            <!-- 回执单信息 -->
            <a-descriptions
              title="回执单信息"
              :column="1"
            >
              <a-descriptions-item label="上传时间">{{ ((model.SalesOrderReceiptFiles || []).length&&model.SalesOrderReceiptFiles[0].UploadTime )|| ' -- ' }}</a-descriptions-item>
              <a-descriptions-item label="">
                <ViewerImage
                  :images="model.SalesOrderReceiptFiles.map(v=>v.FileUrl)"
                  v-if="(model.SalesOrderReceiptFiles || []).length"
                />
                <span v-else> -- </span>
              </a-descriptions-item>
            </a-descriptions>
            <!-- 审核信息 -->
            <a-descriptions
              title="审核信息"
              :column="1"
            >
              <a-descriptions-item label="">
                <!-- 审核信息 -->
                <SupAuditInformation
                  ref="supAuditInformation"
                  :bussinessNo="model.AuditId"
                  v-if="model && model.AuditId"
                />
              </a-descriptions-item>
            </a-descriptions>

          </a-card>
          <a-affix
            v-if="isAudit"
            :offset-bottom="0"
            style="float: right;width: 100%;text-align: right;"
          >
            <a-card
              :bordered="false"
              style="border-top:1px solid #e8e8e8"
            >
              <YYLButton
                menuId="a871fa7a-0bb2-4378-b0dc-3e8d31efa5c3"
                text="审核通过"
                type="primary"
                @click="handlePass"
                v-if="IsShowBtn"
              />
              <YYLButton
                menuId="d2c338b5-04bf-4a7a-892f-e61e05790005"
                text="审核驳回"
                type="danger"
                @click="handleNoPass"
                v-if="IsShowBtn"
              />
              <span
                v-if="!IsShowBtn"
                style="margin-right:15px"
              >无审核权限</span>
              <a-button @click="goBack(true,true)">取消</a-button>
            </a-card>

          </a-affix>
        </a-spin>
        <!-- 审核弹框 -->
        <AuditRemarkModal
          ref="iAuditRemarkModal"
          @ok="handleAuditModalOk"
          v-if="isAudit"
        />
      </div>
    </a-card>
  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'

import { getAction, postAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'includingSpecialReceiptReviewAuditPage',
  mixins: [ListMixin, EditMixin],
  data() {
    return {
      model: {},
      orderDetail: {},
      loading: false,
      id: '',
      isAudit: false,

      IsHasSuperior: false,
      IsShowBtn: false,
      ApprovalWorkFlowInstanceId: null,
      httpHead: 'P36008',
      url: {
        detail: '/{v}/Audit/GetSalesOrderReceipt',
        orderDetail: '/{v}/Order/OrderDetail',
        getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults'
      }
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.isAudit = this.$route.query.audit == 1 ? true : false
      this.setTitle(this.isAudit ? '含特回执审核' : '含特回执审核详情')
      this.loadDetailInfo()
    }
  },
  created() {},
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then(res => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            this.loadOrderDetailInfo()
            if (this.isAudit) this.getApprovalResults(this.model.AuditId)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // 订单详情
    loadOrderDetailInfo() {
      if (!this.model.SalesOrderId) return
      getAction(this.url.orderDetail, { id: this.model.SalesOrderId }, this.httpHead).then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.orderDetail = res.Data || {}
      })
    },
    // IsHasSuperior
    getApprovalResults(bussinessNo) {
      if (!bussinessNo) return
      getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
        .then(res => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.IsHasSuperior = res.Data['IsHasSuperior'] //是否有上级 true 有上级 false 没有上级
              this.ApprovalWorkFlowInstanceId = res.Data['ApprovalWorkFlowInstanceId']
              let userId = Vue.ls.get(USER_ID)
              if ((res.Data['ApprovalUserIds'] || []).length && this.isAudit) {
                this.IsShowBtn = res.Data['ApprovalUserIds'].some(v => v == userId)
              }
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {})
    },
    /**
     * @description: 通过审核
     * @param {*}
     * @return {*}
     */
    handlePass() {
      this.$refs.iAuditRemarkModal.show(1, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId
      })
    },
    /**
     * @description: 驳回审核
     * @param {*}
     * @return {*}
     */
    handleNoPass() {
      this.$refs.iAuditRemarkModal.show(2, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
        IsHasSuperior: this.IsHasSuperior
      })
    },
    // 审核弹框回调
    handleAuditModalOk(AuditModalOkformData) {
      if (AuditModalOkformData) {
        // console.log('AuditModalOkformData             ', AuditModalOkformData)
        this.$refs.iAuditRemarkModal.postAuditInstance(AuditModalOkformData, (bool,code) => {
          setTimeout(() => {
            if (bool) {
              this.goBack(true,true)
            }else{
              if(code === 6067){
                this.goBack(true,true)
              }
            }
          },1000);
        })
      } else {
        this.goBack(true,true)
      }
    }
  }
}
</script>

<style scoped>

</style>
