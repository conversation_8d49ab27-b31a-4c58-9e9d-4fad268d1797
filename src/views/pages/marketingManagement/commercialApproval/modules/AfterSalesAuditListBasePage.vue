<!--
 * @Author: HL
 * @Description: 商销管理的商销售后审核列表 
 * @Date: 2023-12-14 15:42:23
-->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="returnColumns" :isTableInitData="isTableInitData" @operate="operate" @changeTab="changeTab" @getListAmount="getListAmount" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销售后审核',
  name: 'AfterSalesAuditListBasePage',
  mixins: [SimpleMixin],
  props: {
    source: {
      type: Number,
      default: 1,//1来自商销管理审核 2来自财务管理审核
    }
  },
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '售后单号',
          type: 'input',
          value: '',
          key: 'SaleAfterOrderCode',
          defaultVal: '',
          placeholder: '请输入',
        },
        { name: '订单编号', type: 'input', value: '', key: 'SalesOrderCode', defaultVal: '', placeholder: '请输入' },
        { name: '客户信息', type: 'input', value: '', key: 'CustomerName', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: 1,
        statusKey: '待审核', //标签的切换关键字
        statusList: [
          {
            name: '待审核',
            value: 1,
            count: '',
          },
          {
            name: '审核记录',
            value: 4,
            count: '',
          },
        ],
      },
      columns: [
        {
          title: '售后单号',
          dataIndex: 'Code',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单编号',
          dataIndex: 'OrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'SaleAfterPayStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '售后类型',
          dataIndex: 'SaleAfterTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '售后范围',
          dataIndex: 'SaleAfterScopeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '售后方式',
          dataIndex: 'SaleAfterRefundTypeStr',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单金额',
          dataIndex: 'TotalAmount',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '售后金额',
          dataIndex: 'RefundAmount',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtn: [
            {
              name: '审核',
              icon: '',
              id: this.source == 1 ? '95bc9e19-08d9-4c39-80c2-5c7b3e9535a5' : (this.source == 2 ? '3d866e28-66df-44c4-8949-3e82b0ffbf8d' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                return this.$refs.table.tab.status == 1
              },
            },
            {
              name: '详情',
              icon: '',
              id: this.source == 1 ? '3b580cb0-89d9-4fc7-ab6d-329e5384b2bd' : (this.source == 2 ? '796b15ac-c4eb-4c61-aa7d-442a635ad0bf' : null),
              specialShowFuc: (e) => {
                let record = e || {}
                return this.$refs.table.tab.status != 1
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],

      returnColumns: [],
      queryParam: {
        IsNeedAudit: true,
      },
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/Audit/SaleAfterAuditList',
        listAmountType: 'GET',
        listAmount: '/{v}/Audit/SaleAfterAuditCount',
      },
    }
  },
  created() { },
  computed: {},
  mounted() {
    this.queryParam['IsNeedAudit'] = true
  },
  activated() {
    this.returnColumns = this.getColumns(this.tab.status)
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    getColumns(status) {
      return this.columns
    },
    getListAmount(data) {
      this.tab.statusList[0].count = (data && data.NeedAuditCount) || 0
      this.tab.statusList[1].count = (data && data.AuditedCount) || 0
    },
    changeTab(value) {
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      this.queryParam.IsNeedAudit = value == 1 ? true : false
      this.returnColumns = this.getColumns(value)
      this.$nextTick(() => {
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.$refs.table.tab.status = 1
      }
      queryParam['IsNeedAudit'] = this.$refs.table.tab.status == 1 ? true : false
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '审核') {
        this.onDetailClick('/pages/marketingManagement/commercialApproval/afterSalesAuditPage', {
          id: record.Id,
          source: this.source,
          audit: 1,
        })
      } else if (type == '详情') {
        this.onDetailClick('/pages/marketingManagement/commercialApproval/afterSalesAuditPage', {
          id: record.Id,
          source: this.source,
          audit: 0,
        })
      }
    },
  },
}
</script>

<style></style>
