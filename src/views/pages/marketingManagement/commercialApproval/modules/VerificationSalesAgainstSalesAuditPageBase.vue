<!-- 销货对抵审核-->
<template>
  <div>
    <a-card title="销货对抵审核详情" style="margin-bottom:12px" v-if="!isAudit">
      <a-button @click="goBack(true,true)" slot="extra">返回</a-button>
    </a-card>
    <a-card style="margin-bottom:12px">
      <a-row :gutter="gutter">
        <a-col :md="8" :style="{textAlign:'center',color:getAuditColor( model&&model.SalesOffsetInfo['AuditStatus'])}">
          <p>
            <a-icon :type="getAuditIcon(model&&model.SalesOffsetInfo['AuditStatus'])" :style="{color:getAuditColor( model&&model.SalesOffsetInfo['AuditStatus']),fontSize:'32px'}" />
          </p>
          {{(model && model.SalesOffsetInfo['AuditStatusStr']) || ' -- '}}
        </a-col>
        <a-col :md="5">
          <p>对抵金额</p>
          ￥{{(model.SalesOffsetInfo && model.SalesOffsetInfo.OffsetAmount) || 0}}
        </a-col>
        <a-col :md="5">
          <p>销货对抵编号</p>
          {{(model.SalesOffsetInfo && model.SalesOffsetInfo.SalesOffsetOrderNo)  || ' -- '}}
        </a-col>

        <a-col :md="5">
          <p>创建时间</p>
          {{( model.SalesOffsetInfo &&  model.SalesOffsetInfo.CreateTime) || ' -- '}}
        </a-col>

      </a-row>
    </a-card>
    <a-card>
      <div :class="'form-model-style'">
        <a-spin :spinning="loading">
          <a-card :bordered="false" :body-style="{padding:0}">
            <a-card style="margin-bottom:10px;" :bodyStyle="{ padding: '0' }" title="基础信息" :bordered="false">
              <a-form-model style="margin-top:10px;" ref="form" :rules="rules" :model="model" layout="vertical">
                <a-row :getter="10">
                  <a-col :md="8">
                    <a-form-model-item label="对抵方：" prop="SupplierName">
                      <a-input placeholder="请输入" v-model="model.SalesOffsetInfo.SupplierName" style="width: 100%" :maxLength="50" disabled />
                    </a-form-model-item>
                  </a-col>
                  <a-col :md="8">
                    <a-form-model-item style="margin:0 15px;" label="业务员：" prop="SalesManName">
                      <a-input placeholder="请输入" v-model="model.SalesOffsetInfo.SalesManName" style="width: 100%" :maxLength="50" disabled />
                    </a-form-model-item>
                  </a-col>
                  <a-col :md="8">
                    <a-form-model-item label="对抵金额：" prop="OffsetAmount">
                      <a-input placeholder="请输入" v-model="model.SalesOffsetInfo.OffsetAmount" style="width: 100%" :maxLength="50" disabled />
                    </a-form-model-item>
                  </a-col>
                  <a-col :md="24">
                    <!-- 应收/应付信息 -->
                    <a-row :gutter="gutter" style="margin-bottom:24px">
                      <a-col :span="24">
                        <!-- 列表 -->
                        <SimpleTable ref="table" :tab="tab" :columns="columns">
                          <span slot="commonContent" slot-scope="{text}">
                            <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                          </span>
                        </SimpleTable>

                      </a-col>

                    </a-row>
                    <!-- 应付明细 -->
                    <a-row :gutter="20" style="margin-bottom:24px">
                      <a-col :span="24">
                        <div style="font-size: 16px; font-weight: 600;margin:0 0 15px 0">
                          应付明细
                        </div>
                      </a-col>
                      <a-col :span="24">
                        <!-- 列表 -->
                        <SimpleTable ref="tablePay" :tab="tabGoods" :isTableInitData="isTableInitData" :columns="columnsGoods">
                          <span slot="commonContent" slot-scope="{text}">
                            <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                          </span>
                        </SimpleTable>
                      </a-col>
                      <a-col :span="24">
                        <span style="margin-right:10px">合计数量：{{model.PayableBill.TotalCount || 0}}</span>
                        <span style="margin-right:10px">应付对抵合计：<span style="color:red;font-size:30px">¥{{model.PayableBill.PayableOffsetAmount || 0}}</span> </span>
                        <span style="margin-right:10px">采购退货：<span style="color:red;font-size:30px">¥{{model.PayableBill.RefundOrderAmount || 0}}</span> </span>
                        <span style="margin-right:10px">差价调整：<span style="color:red;font-size:30px">¥{{model.PayableBill.PriceAdjustmentAmount || 0}}</span> </span>
                        <span style="margin-right:10px">订单票折：<span style="color:red;font-size:30px">¥{{model.PayableBill.InvoiceDiscountAmount || 0}}</span> </span>
                        <!-- <span style="margin-right:10px">买赔入库：<span style="color:red;font-size:30px">¥{{model.PayableBill.BuyCompensationAmount || 0}}</span> </span> -->
                      </a-col>
                    </a-row>
                    <!-- 应收明细 -->
                    <a-row :gutter="20" style="margin-bottom:24px">
                      <a-col :span="24">
                        <div style="font-size: 16px; font-weight: 600;margin:0 0 15px 0">
                          应收明细
                        </div>
                      </a-col>
                      <a-col :span="24">
                        <!-- 列表 -->
                        <SimpleTable ref="tableReceivable" :tab="tabGoods" :isTableInitData="isTableInitData" :columns="columnsGoods" :tableDatas="model.SaleAfterOrderGoodses || []">
                          <span slot="commonContent" slot-scope="{text}">
                            <j-ellipsis :value="`￥${Number(text).toFixed(2)}`" :length="15" />
                          </span>
                        </SimpleTable>
                      </a-col>
                      <a-col :span="24">
                        <span style="margin-right:10px">合计数量：{{model.ReceivableBill.TotalCount || 0}}</span>
                        <span style="margin-right:10px">应收对抵合计：<span style="color:red;font-size:30px">¥{{model.ReceivableBill.PayableOffsetAmount || 0}}</span> </span>
                        <span style="margin-right:10px">商销退货：<span style="color:red;font-size:30px">¥{{model.ReceivableBill.RefundOrderAmount || 0}}</span> </span>
                        <span style="margin-right:10px">差价调整：<span style="color:red;font-size:30px">¥{{model.ReceivableBill.PriceAdjustmentAmount || 0}}</span> </span>
                      </a-col>
                    </a-row>
                    <a-row :gutter="gutter">
                      <a-col :span="24">
                        <div style="font-size: 16px; font-weight: 600;margin:0 0 15px 0">
                          备注信息
                        </div>
                      </a-col>
                      <a-col :span="24">
                        <a-form-model-item label="" prop="Remark">
                          <a-textarea v-model="model.Remark" style="width:100%" :disabled="source?true:false" placeholder="请输入" :auto-size="{ minRows: 4, maxRows: 6 }" :maxLength="100" />
                        </a-form-model-item>
                      </a-col>

                    </a-row>
                  </a-col>
                </a-row>
              </a-form-model>
            </a-card>
            <!-- 审核信息 -->
            <SupAuditInformation ref="supAuditInformation" :bussinessNo="model.SalesOffsetInfo.AuditId" v-if="model.SalesOffsetInfo.AuditId" />
          </a-card>
          <a-affix v-if="isAudit" :offset-bottom="0" style="float: right;width: 100%;text-align: right;">
            <a-card :bordered="false" style="border-top:1px solid #e8e8e8">
              <!-- 来自商销 -->
              <YYLButton :menuId="YYLButtonArrayShangxiao[0]" text="审核通过" type="primary" @click="handlePass" v-if="IsShowBtn && source == 'shangxiao'" />
              <YYLButton :menuId="YYLButtonArrayShangxiao[1]" text="审核驳回" type="danger" @click="handleNoPass" v-if="IsShowBtn && source == 'shangxiao'" />
              <!-- 来自财务 -->
              <YYLButton :menuId="YYLButtonArrayCaiwu[0]" text="审核通过" type="primary" @click="handlePass" v-if="IsShowBtn && source == 'caiwu'" />
              <YYLButton :menuId="YYLButtonArrayCaiwu[1]" text="审核驳回" type="danger" @click="handleNoPass" v-if="IsShowBtn && source == 'caiwu'" />
              <span v-if="!IsShowBtn" style="margin-right:15px">无审核权限</span>
              <a-button @click="goBack(true,true)">取消</a-button>
            </a-card>

          </a-affix>
        </a-spin>
        <!-- 审核弹框 -->
        <AuditRemarkModal ref="iAuditRemarkModal" @ok="handleAuditModalOk" v-if="isAudit" />
      </div>
    </a-card>
  </div>
</template>

<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'

import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'VerificationSalesAgainstSalesAuditPageBase',
  mixins: [EditMixin],
  components: { JEllipsis },
  data() {
    return {
      model: {
        SalesOffsetInfo: {},
        PayableBill: {},
        ReceivableBill: {},
      },
      loading: false,
      id: '',
      isAudit: false,
      tab: { hideIpagination: true },
      columns: [
        // {
        //   title: '总余额',
        //   dataIndex: 'TotalBalance',
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'commonSlot' }
        // },
        {
          title: '应收余额',
          dataIndex: 'ReceivableBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' }
        },
        {
          title: '应付余额',
          dataIndex: 'PayableBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' }
        },
        {
          title: '预付余额',
          dataIndex: 'PrepaymentBalance',
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' }
        },
        {
          title: '预收余额',
          dataIndex: 'AdvancePaymentBalance',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' },
        },
      ],
      tabGoods: {
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        operateBtn: [],
        hideIpagination: true,
        rowClassNameFun: true,
      },
      columnsGoods: [
        {
          title: '单据编号',
          dataIndex: 'BillNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '单据类型',
          dataIndex: 'BusinessOrderStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '对应订单编号',
          dataIndex: 'OrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '订单付款方式',
          dataIndex: 'PaymentModeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '发生日期',
          dataIndex: 'TransactionTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '应付金额',
          dataIndex: 'PayableAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' }
        },
        {
          title: '剩余应付金额',
          dataIndex: 'ResiduePayableAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' }
        },
        {
          title: '本次对抵金额',
          dataIndex: 'OffsetAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'commonSlot' }
        }
      ],
      source: '',
      YYLButtonArrayShangxiao: ['546f463e-66fb-4f23-8017-97051de677c9', '974dc921-9803-4fdf-a133-31d33628b265'],
      YYLButtonArrayCaiwu: ['f34bb686-6ac8-424d-9a43-dda820acd425', '5e2e43d7-e725-448b-9eb4-1522e2afc9fd'],
      IsHasSuperior: false,
      IsShowBtn: false,
      ApprovalWorkFlowInstanceId: null,
      isTableInitData: false,
      httpHead: 'P36008',
      url: {
        detail: '/{v}/SalesOffset/Detail',
        getTotalBalanceAsync: '/{v}/Account/GetTotalBalanceAsync',
        getApprovalResults: '/{v}/ApprovalWorkFlowInstance/GetApprovalResults'
      }
    }
  },
  computed: {
    rules() {
      return {
        SupplierName: [{ required: true, message: '请输入!' }],
        SalesManName: [{ required: true, message: '请选择!' }],
        OffsetAmount: [{ required: true, message: '请输入!' }],
      }
    }
  },
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.isAudit = this.$route.query.audit == 1 ? true : false
      this.source = this.$route.query.source || ''
      this.setTitle(this.isAudit ? '销货对抵审核' : '销货对抵审核详情')
      this.loadDetailInfo()
    }
  },
  created() { },
  methods: {
    // 应收/应付信息
    getTotalBalanceAsync() {
      if (!this.model.SalesOffsetInfo.SupplierId) return
      getAction(this.url.getTotalBalanceAsync, { SupplierId: this.model.SalesOffsetInfo.SupplierId }, 'P36011').then(res => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        res.Data.TotalBalance = Number((res.Data.ReceivableBalance + res.Data.PayableBalance + res.Data.PrepaymentBalance).toFixed(2))
        this.$refs.table.dataSource = res.Data ? [res.Data] : []
      })
    },
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, 'P36012')
        .then(res => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            this.$refs.tablePay.dataSource = that.model.PayableBill['Bills'] || []
            this.$refs.tableReceivable.dataSource = that.model.ReceivableBill['Bills'] || []
            this.model.Remark = this.model.SalesOffsetInfo.Remark
            this.getTotalBalanceAsync()
            this.getApprovalResults(this.model.SalesOffsetInfo.AuditId)
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    // IsHasSuperior
    getApprovalResults(bussinessNo) {
      if (!bussinessNo) return
      getAction(this.url.getApprovalResults, { bussinessNo: bussinessNo }, 'P36005')
        .then(res => {
          if (res.IsSuccess) {
            if (res.Data) {
              this.IsHasSuperior = res.Data['IsHasSuperior'] //是否有上级 true 有上级 false 没有上级
              this.ApprovalWorkFlowInstanceId = res.Data['ApprovalWorkFlowInstanceId']
              let userId = Vue.ls.get(USER_ID)
              if ((res.Data['ApprovalUserIds'] || []).length && this.isAudit) {
                this.IsShowBtn = res.Data['ApprovalUserIds'].some(v => v == userId)
              }
            }
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => { })
    },
    /**
     * @description: 通过审核
     * @param {*}
     * @return {*}
     */
    handlePass() {
      this.$refs.iAuditRemarkModal.show(1, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId
      })
    },
    /**
     * @description: 驳回审核
     * @param {*}
     * @return {*}
     */
    handleNoPass() {
      this.$refs.iAuditRemarkModal.show(2, {
        ApprovalWorkFlowInstanceId: this.ApprovalWorkFlowInstanceId,
        IsHasSuperior: this.IsHasSuperior
      })
    },
    // 审核弹框回调
    handleAuditModalOk(AuditModalOkformData) {
      if (AuditModalOkformData) {
        this.$refs.iAuditRemarkModal.postAuditInstance(AuditModalOkformData, (bool,code) => {
          setTimeout(() => {
            if (bool) {
              this.goBack(true,true)
            }else{
              if(code === 6067){
                this.goBack(true,true)
              }
            }
          },1000);
        })
      } else {
        this.goBack(true,true)
      }
    }
  }
}
</script>

<style scoped>

</style>
