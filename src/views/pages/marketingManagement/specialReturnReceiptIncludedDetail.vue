<!-- 含特回执详情-->
<template>
  <div>
    <a-card title="含特回执详情" style="margin-bottom: 12px">
      <a-button @click="goBack(true,true)" slot="extra">返回</a-button>
    </a-card>
    <a-spin :spinning="loading">
      <a-card style="margin-bottom: 12px">
        <a-row :gutter="gutter">
          <a-col :md="8" :style="{ textAlign: 'center', color: getAuditColor(model && model['AuditStatus']) }">
            <p>
              <a-icon
                :type="getAuditIcon(model && model['AuditStatus'])"
                :style="{ color: getAuditColor(model && model['AuditStatus']), fontSize: '32px' }"
              />
            </p>
            {{ (model && model['AuditStatusStr']) || ' -- ' }}
          </a-col>
          <a-col :md="5">
            <p>订单金额</p>
            ￥{{ (model && model.SalesOrderTotalAmount) || 0 }}
          </a-col>
          <a-col :md="5">
            <p>订单编号</p>
            {{ (model && model.SalesOrderCode) || ' -- ' }}
          </a-col>

          <a-col :md="5">
            <p>下单时间</p>
            {{ (orderDetail && orderDetail.PlaceOrderTime) || ' -- ' }}
          </a-col>
        </a-row>
      </a-card>
      <a-card>
        <div :class="'form-model-style'">
          <a-spin :spinning="loading">
            <a-card :bordered="false" :body-style="{ padding: 0 }">
              <!-- 订单信息 -->
              <a-descriptions title="订单信息">
                <a-descriptions-item label="客户名称">{{ orderDetail.CustomerName || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="客户类别">{{ orderDetail.CustomerTypeStr || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="订单类型">{{ orderDetail.TypeStr || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="收货人">{{ orderDetail.ReceivingName || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="联系电话">{{ orderDetail.ReceivingPhone || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="收货地址">{{
                  (orderDetail.ReceivingAreaFullName || ' -- ') + '' + (orderDetail.ReceivingAddress || ' -- ')
                }}</a-descriptions-item>
                <a-descriptions-item label="快递公司">{{
                  orderDetail.ExpressCompanyStr || ' -- '
                }}</a-descriptions-item>
                <a-descriptions-item label="订单金额">￥{{ orderDetail.TotalAmount || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="订单编号">{{ orderDetail.Code || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="下单时间">{{ orderDetail.PlaceOrderTime || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="付款方式">{{ orderDetail.PaymentModeStr || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="审核状态">{{ orderDetail.AuditStatusStr || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="订单状态">{{ orderDetail.OrderStatusStr || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="开票状态">{{ orderDetail.InvoiceStatusStr || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="认款状态">{{
                  orderDetail.OrderAcceptanceStatusStr || ' -- '
                }}</a-descriptions-item>
              </a-descriptions>
              <!-- 回执单信息 -->
              <a-descriptions title="回执单信息" :column="1">
                <a-descriptions-item label="上传时间">{{
                  ((model.SalesOrderReceiptFiles || []).length && model.SalesOrderReceiptFiles[0].UploadTime) || ' -- '
                }}</a-descriptions-item>
                <a-descriptions-item label="">
                  <ViewerImage
                    :images="model.SalesOrderReceiptFiles.map((v) => v.FileUrl)"
                    v-if="(model.SalesOrderReceiptFiles || []).length"
                  />
                  <span v-else> -- </span>
                </a-descriptions-item>
              </a-descriptions>
              <a-descriptions title="签章信息" :column="1">
                <a-descriptions-item label="签章时间">{{ model.SignTime || ' -- ' }}</a-descriptions-item>
                <a-descriptions-item label="">
                  <!-- <ViewerImage
                    :images="[model.SignFileUrl]"
                    v-if="model.SignFileUrl"
                  /> -->
                  <iframe
                    v-if="model.SignFileUrl"
                    :src="model.SignFileUrl"
                    frameborder="0"
                    width="100%"
                    height="580px"
                  />
                  <span v-else> -- </span>
                </a-descriptions-item>
              </a-descriptions>
              <!-- 审核信息 -->
              <a-descriptions title="审核信息" :column="1">
                <a-descriptions-item label="">
                  <SupAuditInformation
                    ref="supAuditInformation"
                    :bussinessNo="model.AuditId"
                    v-if="model && model.AuditId"
                  />
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-spin>
        </div>
      </a-card>
    </a-spin>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'specialReturnReceiptIncludedDetail',
  mixins: [EditMixin],
  data() {
    return {
      model: {},
      orderDetail: {},
      loading: false,
      id: '',

      httpHead: 'P36008',
      url: {
        detail: '/{v}/Order/GetSalesOrderReceipt',
        orderDetail: '/{v}/Order/OrderDetail',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.loadDetailInfo()
    }
  },
  created() {},
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.url.detail, { id: that.id }, that.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
            this.loadOrderDetailInfo()
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    loadOrderDetailInfo() {
      if (!this.model.SalesOrderId) return
      getAction(this.url.orderDetail, { id: this.model.SalesOrderId }, this.httpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.orderDetail = res.Data || {}
      })
    },
  },
}
</script>

<style scoped>

</style>
