<!-- 商销调价 详情-->
<template>
  <div>
    <a-card title="商销调价详情" style="margin-bottom: 12px">
      <a-button @click="goBack(true,true)" slot="extra">返回</a-button>
    </a-card>
    <a-spin :spinning="loading">
      <a-card style="margin-bottom: 12px">
        <a-row :gutter="20">
          <a-col
            :md="8"
            :style="{
              textAlign: 'center',
              color: getAuditColor(getSalesOrderAdjustPrice && getSalesOrderAdjustPrice['AuditStatus']),
            }"
          >
            <p>
              <a-icon
                :type="getAuditIcon(getSalesOrderAdjustPrice && getSalesOrderAdjustPrice['AuditStatus'])"
                :style="{
                  color: getAuditColor(getSalesOrderAdjustPrice && getSalesOrderAdjustPrice['AuditStatus']),
                  fontSize: '32px',
                }"
              />
            </p>
            {{ getSalesOrderAdjustPrice['AuditStatusStr'] || ' -- ' }}
          </a-col>
          <a-col :md="5">
            <p>调价金额</p>
            ￥{{ getSalesOrderAdjustPrice.AdjustAmount || 0 }}
          </a-col>
          <a-col :md="5">
            <p>调价编号</p>
            {{ getSalesOrderAdjustPrice.Code || ' -- ' }}
          </a-col>
          <a-col :md="5">
            <p>差价调整时间</p>
            {{ getSalesOrderAdjustPrice.CreateTime || ' -- ' }}
          </a-col>
        </a-row>
        <a-alert
          type="error"
          :message="`驳回原因：${(getSalesOrderAdjustPrice && getSalesOrderAdjustPrice.AuditOpinion) || ' -- '}`"
          banner
          :showIcon="false"
          v-if="
            getSalesOrderAdjustPrice &&
            getSalesOrderAdjustPrice.AuditOpinion &&
            getSalesOrderAdjustPrice['AuditStatus'] == 3
          "
        />
      </a-card>
      <a-card>
        <a-tabs v-model="activeTabKey">
          <a-tab-pane
            :key="item['key']"
            :tab="item['value']"
            v-for="item in tabsList"
          >

          <PricesAdjustedDetailBase v-if="activeTabKey == 1 && model && getSalesOrderAdjustPrice['Id'] && item['key'] == 1" :model="model" :getSalesOrderAdjustPrice="getSalesOrderAdjustPrice"/>
          <PricesAdjustedDetailSettle v-if="activeTabKey == 2 && model && item['key'] == 2" :Info="model"/>

          <!-- 审核信息 -->
        <a-row :gutter="20" style="margin-bottom: 24px"  v-if="activeTabKey == 3 && model && item['key'] == 3" >
          <a-col :span="24">
            <div style="font-size: 16px; font-weight: 600; margin: 0 0 15px 0">审核信息</div>
          </a-col>
          <a-col :span="24">
            <SupAuditInformation
              ref="iSupAuditInformation"
              :bussinessNo="getSalesOrderAdjustPrice.AuditId"
              v-if="getSalesOrderAdjustPrice && getSalesOrderAdjustPrice.AuditId"
            />
          </a-col>
        </a-row>
        </a-tab-pane>
      </a-tabs>
      </a-card>
    </a-spin>
  </div>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'pricesAdjustedDetail',
  mixins: [EditMixin],
  components: { JEllipsis },
  data() {
    return {
      activeTabKey: 1,
      model: {},
      tabsList: [
        {
          key: 1,
          value: '基本信息',
          id: ''
        },
        {
          key: 2,
          value: '结算信息',
          id: ''
        },
        {
          key: 3,
          value: '审核信息',
          id: ''
        },
      ],
      getSalesOrderAdjustPrice: {},
      loading: false,
      id: '',
      orderId: '',
      tabGoods: {
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
        operateBtn: [],
        hideIpagination: true,
      },
      columnsGoods: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '有效期',
          dataIndex: 'GoodsPeriod',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '可销库存',
          dataIndex: 'GoodsCanSaleInventory',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '最低商销价',
          dataIndex: 'GoodsLimitPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        // {
        //   title: '商销报价',
        //   dataIndex: 'GoodsLimitPrice',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'commonSlot' }
        // },
        {
          title: '末次销售价',
          dataIndex: 'LastSalesPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '采购管理模式',
          dataIndex: 'GoodsProcurementManagementModelName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '是否赠品',
          dataIndex: 'IsGift',
          ellipsis: true,
          width: 150,
          customRender: (t, r, i) => {
            return t == null ? '' : t ? '是' : '否'
          },
        },
        {
          title: '销售单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '最新单价',
          dataIndex: 'AdjustPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '销售数量',
          dataIndex: 'Count',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '出库数量',
          dataIndex: 'OutboundCount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '销售价格',
        //   dataIndex: 'Count1',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' }
        // },
        {
          title: '销售小计金额',
          // dataIndex: 'SubtotalAmount',
          dataIndex: 'OutboundSubtotalAmount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'commonSlot' },
        },
        // {
        //   title: '是否特赠品',
        //   dataIndex: 'IsSpecialGift',
        //   ellipsis: true,
        //   width: 100,
        //   customRender: (t, r, i) => {
        //     return t == null ? '' : t ? '是' : '否'
        //   }
        // },
        // {
        //   title: '调价后单价',
        //   dataIndex: 'AdjustPrice',
        //   ellipsis: true,
        //   width: 100,
        //   fixed: 'right',
        //   scopedSlots: { customRender: 'component' },
        // },
        {
          title: '调价数量',
          dataIndex: 'AdjustCount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '每盒差额',
          dataIndex: 'AdjustPerSpreadPrice',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '调价小计金额',
          dataIndex: 'AdjustTotalAmount',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '备注',
          dataIndex: 'Remarks',
          ellipsis: true,
          width: 100,
          fixed: 'right',
          scopedSlots: { customRender: 'component' },
        },
      ],
      queryParam: {},
      isTableInitData: false,
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        detail: '/{v}/Order/OrderDetail',
        list: '/{v}/Audit/GetSalesOrderAdjustPrice',
        getSalesOrderAdjustPrice: '/{v}/Audit/GetSalesOrderAdjustPrice',
      },
    }
  },
  computed: {},
  mounted() {
    if (this.$route.query) {
      this.id = this.$route.query.id || ''
      this.orderId = this.$route.query.orderId || ''
      this.loadDetailInfo()
    }
  },
  created() {},
  methods: {
    /**
     * 获取详情
     */
    loadDetailInfo() {
      let that = this
      that.loading = true
      getAction(that.linkUrl.detail, { id: that.orderId }, that.linkHttpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.model = res.Data || {}
          } else {
            that.$message.error(res.Msg)
          }
        })
        .finally(() => {
          that.loading = false
        })
        getAction(this.linkUrl.getSalesOrderAdjustPrice, { id: this.$route.query.id}, this.linkHttpHead)
        .then((res) => {
          if (res.IsSuccess) {
            this.getSalesOrderAdjustPrice = res.Data || {}
          } else {
            this.$message.error(res.Msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
      
    },
  },
}
</script>

<style scoped>

</style>
