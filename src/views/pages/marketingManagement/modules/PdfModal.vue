<template>
  <a-modal
    :title="title || '查看电子版出库单'"
    :width="1100"
    :visible="visible"
    @cancel="visible = false"
    @close="visible = false"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :footer="null"
  >
    <span v-if="toast" style="color: red">{{ toast }}</span>
    <ImageControl isPdf :src="pdfFileUrls[0]" :PdfUrls="pdfFileUrls" :height="600" />
  </a-modal>
</template>
<script>
export default {
  name: 'PdfModal',
  components: {},
  mixins: [],
  data() {
    return {
      title: '',
      visible: false,
      pdfFileUrls: [],
      toast: '',
    }
  },

  mounted() {},
  created() {},
  methods: {
    show(title, FileUrls = [], toast) {
      // console.log(data)
      this.title = title
      this.pdfFileUrls = FileUrls
      this.toast = toast

      this.$nextTick(() => {
        this.visible = true
      })
    },
  },
}
</script>
<style scoped></style>
