<template>
  <a-modal title="商销调价" :width="1500" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="ruleForm" :rules="rules" :model="model" layout="vertical">
        <a-card>
          <div style="background: #fbe6e9; padding: 10px 16px; color: red" v-if="model.AuditStatus == 3 && model.AuditOpinion" :md="24">
            <span>驳回原因：{{ model.AuditOpinion || '' }}</span>
          </div>
          <a-tabs v-model="activeTabKey" @change="handleTab">
            <a-tab-pane :key="item['key']" :tab="item['value']" v-for="item in tabsList">
              <a-row :gutter="gutter" v-if="activeTabKey == item['key'] && isToggleTable">
                <!-- 
                    :linkHttpHead="linkHttpHead"
                    :linkUrlType="linkUrlType"
                    :linkUrl="linkUrl"
               -->
                <a-col :span="md">
                  <SimpleTable :ref="`table${activeTabKey}`" :tab="tab" :isTableInitData="isTableInitData" :queryParam="queryParam" :columns="columns" :tableDatas="tableDatas">
                    <span slot="commonContent" slot-scope="{ text }">
                      <j-ellipsis :value="`￥${text || 0}`" :length="15" />
                    </span>
                    <a-row slot="opBtns" slot-scope="{ record, index }" type="flex" align="middle" :gutter="5">
                      <a-col :md="6">
                        <a-input-number placeholder="调价数量" v-model="record.AdjustCountInput" @blur="(e)=>onGoodCountBlur('AdjustCountInput',e,record,index)" :min="0" :max="999999" :precision="0" style="width:100%" />
                      </a-col>
                      <!-- <a-col :md="6">
                        <a-input-number placeholder="调整后金额" v-model="record.AdjustPrice" :min="0" :max="999999.99" :precision="6" style="width: 100%" @blur="(e) => onGoodCountBlur('AdjustPrice', e, record, index)" />
                      </a-col> -->
                      <a-col :md="7">
                        <a-input-number placeholder="调价小计金额" v-model="record.AdjustGrossSpreadAmount" :precision="2" style="width: 100%" @blur="(e)=>onGoodCountBlur('AdjustGrossSpreadAmount',e,record,index)" />
                      </a-col>
                      <a-col :md="6">
                        <a-input-number placeholder="每盒差额" v-model="record.AdjustPerSpreadPrice" :precision="6" style="width: 100%" disabled />
                      </a-col>
                      <a-col :md="5">
                        <a-input placeholder="备注" v-model="record.Remarks" style="width: 100%" :maxLength="40" />
                      </a-col>
                    </a-row>
                  </SimpleTable>
                  <p style="margin-top: 10px">调价金额：¥{{ TotalAdjustPrice || 0 }}</p>
                </a-col>
                <a-col :span="md">
                  <a-form-model-item label="调价原因：" prop="AdjustReason">
                    <a-textarea v-model="model.AdjustReason" style="width: 100%" placeholder="请输入" :auto-size="{ minRows: 2, maxRows: 4 }" :maxLength="100" />
                  </a-form-model-item>
                </a-col>
              </a-row>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </a-form-model>
    </a-spin>
    <div slot="footer">
      <YYLButton text="保存" type="primary" @click="handleOk(1)" />
      <YYLButton text="提交审核" type="primary" @click="handleOk(2)" />
      <a-button @click="handleCancel">取消</a-button>
    </div>
  </a-modal>
</template>
<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'PricesAreAdjustedModal',
  components: { JEllipsis },
  mixins: [SimpleMixin, EditMixin, ListMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      md: 24,
      model: {
        AdjustReason: '',
      },
      tab: { hideIpagination: true },
      // 表头
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 80,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 80,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 60,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 80,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 120,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批次号',
          dataIndex: 'BatchNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '仓库',
          dataIndex: 'WarehouseCode',
          ellipsis: true,
          width: 80,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: '有效期',
        //   dataIndex: 'GoodsPeriod',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        // {
        //   title: '可销库存',
        //   dataIndex: 'GoodsCanSaleInventory',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        // {
        //   title: '最低商销价',
        //   dataIndex: 'GoodsLimitPrice',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'commonSlot' },
        // },
        // {
        //   title: '末次销售价',
        //   dataIndex: 'LastSalesPrice',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'commonSlot' },
        // },
        // {
        //   title: '采购管理模式',
        //   dataIndex: 'GoodsProcurementManagementModelName',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        // {
        //   title: '是否赠品',
        //   dataIndex: 'IsGift',
        //   ellipsis: true,
        //   width: 150,
        //   customRender: (t, r, i) => {
        //     return t == null ? '' : t ? '是' : '否'
        //   },
        // },
        {
          title: '销售单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 80,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '销售数量',
          dataIndex: 'Count',
          ellipsis: true,
          width: 80,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '出库数量',
          dataIndex: 'OutboundCount',
          ellipsis: true,
          width: 80,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '小计',
          // dataIndex: 'SubtotalAmount',
          dataIndex: 'OutboundSubtotalAmount',
          ellipsis: true,
          width: 80,
          scopedSlots: { customRender: 'price' },
        },
        // {
        //   title: '是否特赠品',
        //   dataIndex: 'IsSpecialGift',
        //   ellipsis: true,
        //   width: 150,
        //   customRender: (t, r, i) => {
        //     return t == null ? '' : t ? '是' : '否'
        //   },
        // },
        {
          title: '可调价数量',
          dataIndex: 'AdjustableCount',
          ellipsis: true,
          width: 80,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 460,
          fixed: 'right',
          actionBtn: [],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      tableDatas: [],
      tabsList: [],
      activeTabKey: 1,
      isToggleTable: false, //
      isTableInitData: false, //是否自动加载
      isEdit: false, //是否是编辑状态
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '',
        createApproval: '/{v}/ApprovalWorkFlowInstance/CreateApproval', // 创建审核实例
        createSalesOrderAdjustPrice: '/{v}/SalesOrderAdjustPrice/CreateSalesOrderAdjustPriceV15',
        editSalesOrderAdjustPrice: '/{v}/SalesOrderAdjustPrice/EditSalesOrderAdjustPriceV15',//编辑调价
        GetSalesOrderAdjustPrice: '/{v}/Audit/GetSalesOrderAdjustPrice',//调价详情
      },
    }
  },
  computed: {
    rules() {
      return {
        ManageClassify1: [{ required: true, message: '请选择!' }],
      }
    },
    // 调价金额
    TotalAdjustPrice() {
      let list = this.tableDatas || []
      let Price = 0
      if (list.length) {
        list.map((c) => {
          // 调价金额=（A调价后金额-A销售价格）x数量+（B调价后金额-B销售价格）x数量
          // if (c['AdjustPrice'] || c['AdjustPrice'] == 0) {
          //   // Price += ((c['AdjustPrice'] || 0) - (c['OutboundPrice'] || 0)) * (c['AdjustCount'] || 0) // (c['AdjustCount'] || 0)
          //   Price += c['AdjustPriceTotal'] ? c['AdjustPriceTotal'] : 0
          // }
          if (c['AdjustCountInput'] && (c['AdjustGrossSpreadAmount'] || c['AdjustGrossSpreadAmount'] == 0)) {
            // Price += ((c['AdjustPrice'] || 0) - (c['OutboundPrice'] || 0)) * (c['AdjustCount'] || 0) // (c['AdjustCount'] || 0)
            Price += c['AdjustGrossSpreadAmount'] ? c['AdjustGrossSpreadAmount'] : 0
          }
        })
      }
      Price = (Math.round(Number(Price) * 100) / 100) || 0
      return Price
    },
  },
  mounted() { },
  created() { },
  methods: {
    loadAfter() {
      let data = this.dataSource || []
      // console.log(data)
      if (data.length == 0) {
        this.$message.warning('暂无可调价单据!')
        return
      }
      // 设置tab 出库单号数据
      this.tabsList = []
      data.map((v, i) => {
        if (this.tabsList.findIndex((item) => item.id == v['SalesOrderWarehouseOutboundBillsId']) == -1) {
          this.tabsList.push({
            key: i + 1,
            value: v['SalesOrderWarehouseOutboundBillsNo'],
            id: v['SalesOrderWarehouseOutboundBillsId'],
          })
        }
      })
      let SalesOrderWarehouseOutboundBillsId = this.tabsList.find(v => v.key == this.activeTabKey) && this.tabsList.find(v => v.key == this.activeTabKey).id

      // 根据出库单号 设置table数据
      if (SalesOrderWarehouseOutboundBillsId) {
        let dataList = JSON.parse(JSON.stringify(data))
        let tableDatas = dataList.filter(
          (v) => v['SalesOrderWarehouseOutboundBillsId'] == SalesOrderWarehouseOutboundBillsId
        )
        // 还原调价小计
        if (this.isEdit) {
          tableDatas.map(item => {
            // let AdjustPriceV = item.AdjustPrice
            // // 根据（调价后金额-销售金额）x可调价数量计算调价小计
            // let AdjustPriceTotalVal = Number(
            //   this.numReduce(AdjustPriceV, item.OutboundPrice) * (item.AdjustCount || 0)
            // )
            // item.AdjustPriceTotal = Math.round(AdjustPriceTotalVal * 100) / 100
            item.AdjustCountInput = item.AdjustCount
            // 每盒差额
            item.AdjustPerSpreadPrice = this.numMath((item.AdjustGrossSpreadAmount || 0), (item.AdjustCountInput || 0), '/', 6) //运算
          })
        }
        this.$set(this, 'tableDatas', tableDatas)
        // console.log('加载完成拉     ', this.tableDatas)
        this.toggleTable()
      }
      // console.log('加载完成拉     ', data)
    },
    handleTab() {
      let data = this.dataSource || []
      // console.log('切换     ', this.activeTabKey)
      let SalesOrderWarehouseOutboundBillsId = this.tabsList.find(v => v.key == this.activeTabKey) && this.tabsList.find(v => v.key == this.activeTabKey).id
      // 根据出库单号 设置table数据
      if (SalesOrderWarehouseOutboundBillsId) {
        let dataList = JSON.parse(JSON.stringify(data))
        let tableDatas = dataList.filter(
          (v) => v['SalesOrderWarehouseOutboundBillsId'] == SalesOrderWarehouseOutboundBillsId
        )
        this.$set(this, 'tableDatas', tableDatas)
        // console.log('加载完成拉     ', this.tableDatas)
        this.toggleTable()
      }
    },
    toggleTable() {
      this.isToggleTable = false
      this.$nextTick(() => {
        this.isToggleTable = true
      })
    },
    show(data, isEdit) {
      // console.log(data)
      this.model = {
        Id: data['Id'],
        OrderId: data['Id'],
        SalesOrderId: data['SalesOrderId'],//编辑的时候才有值
        AdjustReason: '',
      }
      this.isEdit = isEdit
      this.visible = true
      if (isEdit) {
        this.linkUrl.list = this.linkUrl.GetSalesOrderAdjustPrice
      } else {
        this.linkUrl.list = '/{v}/SalesOrderAdjustPrice/GetSalesOrderAdjustPriceDetailInfosV15'
      }
      this.$nextTick(() => {
        if (isEdit) {
          this.queryParam = { id: this.model['Id'] }
          this.loadDatas(1, this.queryParam, () => { })
        } else {
          this.queryParam = { orderId: this.model['OrderId'] }
          this.loadDatas(1, this.queryParam)
        }
      })
    },
    loadDatas(arg, queryParam, callback) {
      this.queryParam = queryParam || {}
      if (callback) {
        this.loadData(arg, (data) => {
          if (data) {
            if (this.isEdit) {
              this.model = data
              this.dataSource = data.SalesOrderAdjustPriceDetails
              this.loadAfter()
            } else {
              this.loadData(arg, callback)
            }
          }
        })
      } else {
        this.loadData(arg)
      }
    },
    // 差价调整的数量不能大于出库数量
    onGoodCountBlur(key, e, record, index) {
      if (!e.target.value) {
        record.AdjustPerSpreadPrice = null
        return
      }
      // if (key == 'AdjustPrice' && record.AdjustPrice == record.Price) {
      //   this.$message.warning('填写的调整后金额不能等于销售单价')
      //   record.AdjustPrice = null
      //   record.AdjustPriceTotal = null
      //   return
      // }

      if (key == 'AdjustCountInput' && record.AdjustCountInput < 1) {
        this.$message.warning('填写的调整的数量不能小于1')
        record.AdjustCountInput = null
        record.AdjustPerSpreadPrice = null
        return
      }
      if (key == 'AdjustCountInput' && (record.AdjustCountInput || 0) > (record.OutboundCount || 0)) {
        this.$message.warning('填写的调整的数量大于出库数量')
        record.AdjustCountInput = null
        record.AdjustPerSpreadPrice = null
        return
      }
      if (key == 'AdjustGrossSpreadAmount' && record.AdjustGrossSpreadAmount == 0) {
        this.$message.warning('填写的调价小计金额不能为0')
        record.AdjustGrossSpreadAmount = null
        record.AdjustPerSpreadPrice = null
        return
      }
      // let AdjustPriceV = Number(e.target.value)
      // 根据（调价后金额-销售金额）x可调价数量计算调价小计
      // let AdjustPriceTotalVal = Number(
      //   this.numReduce(AdjustPriceV, record.OutboundPrice) * (record.AdjustCountInput || 0)
      // )
      // record.AdjustPriceTotal = Math.round(AdjustPriceTotalVal * 100) / 100
      // 每盒差额
      if (record.AdjustGrossSpreadAmount && record.AdjustCountInput) {
        record.AdjustPerSpreadPrice = this.numMath((record.AdjustGrossSpreadAmount || 0), (record.AdjustCountInput || 0), '/', 6) //运算
      }
      // 调整后单价
      record.AdjustPrice = this.numMath((record.AdjustPerSpreadPrice || 0), (record.Price || 0), '+', 6) //运算

      // this.$set(this.$refs.table.dataSource, index, record)
      this.$set(this.tableDatas, index, record)
      // this.$set(this, 'tableDatas', this.$refs.table.dataSource || [])
    },
    handleCancel() {
      this.$set(this, 'tableDatas', [])
      this.visible = false
      this.activeTabKey = 1
      this.tabsList = []
      this.linkUrl.list = ''
    },
    handleOk(OperationType) {
      // if (this.$refs.table && (this.$refs.table.dataSource || []).length < 1) {
      //   this.$message.warning('商品信息有误!')
      //   return
      // }
      if ((this.tableDatas || []).length < 1) {
        this.$message.warning('商品信息有误!')
        return
      }
      // 判断 销售单价+每盒差额必须要大于等于0.01
      let lessThanNumArray = []
      this.tableDatas.map((item, index) => {
        let num = this.numMath(Number(item.Price), Number(item.AdjustPerSpreadPrice), '+', 2)
        if (item.AdjustPerSpreadPrice && item.Price && num < 0.01) {
          lessThanNumArray.push(index + 1)
        }
      })
      if (lessThanNumArray.length > 0) {
        let lessThanNumArrayText = lessThanNumArray.join(',')
        this.$message.warning(`调价第${lessThanNumArrayText}行商品，销售单价+每盒差额必须要大于等于0.01!`)
        return
      }
      // (!v.AdjustCount && v.AdjustCount!=0)
      // if (this.$refs.table && (this.$refs.table.dataSource || []).some((v) => !v.AdjustPrice && v.AdjustPrice != 0)) {
      //   this.$message.warning('请填写调整后金额!')
      //   return
      // }
      if (this.tableDatas.length >= 0) {
        let AdjustPriceArray = []
        let AdjustCountInputArray = []
        this.tableDatas.map((item) => {
          if (item.AdjustGrossSpreadAmount && item.AdjustGrossSpreadAmount != 0) {
            AdjustPriceArray.push(item.AdjustGrossSpreadAmount)
          }
          if (item.AdjustCountInput && item.AdjustCountInput != 0) {
            AdjustCountInputArray.push(item.AdjustCountInput)
          }
        })
        if (AdjustCountInputArray.length == 0) {
          this.$message.warning('请填写调价数量!')
          return
        }
        if (AdjustPriceArray.length == 0) {
          this.$message.warning('请填写调价小计金额!')
          return
        }
      }
      let CreateSalesOrderAdjustPriceDetails = []
      this.tableDatas.map((v) => {
        if (v.AdjustGrossSpreadAmount && v.AdjustCountInput) {
          CreateSalesOrderAdjustPriceDetails.push({
            SalesOrderDetailId: v.SalesOrderDetailId,
            SalesOrderWarehouseOutboundBillsDetailId: v.SalesOrderWarehouseOutboundBillsDetailId,
            AdjustGrossSpreadAmount: v.AdjustGrossSpreadAmount,
            SalesOrderDetailResultId: v.SalesOrderDetailResultId,
            BatchNo: v.BatchNo,
            WarehouseCode: v.WarehouseCode,
            // AdjustCount: v.AdjustCount,
            AdjustCount: v.AdjustCountInput,
            AdjustPerSpreadPrice: v.AdjustPerSpreadPrice,
            AdjustPrice: v.AdjustPrice,
            Remarks: v.Remarks,
          })
        }
      })

      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let url = ''
          let formData = {
            OperationType: OperationType,
            SalesOrderId: this.model.OrderId,
            AdjustReason: this.model.AdjustReason,
            AdjustAmount: this.TotalAdjustPrice,
            CreateSalesOrderAdjustPriceDetails: CreateSalesOrderAdjustPriceDetails,
          }
          if (this.isEdit) {
            url = this.linkUrl.editSalesOrderAdjustPrice
            formData.SalesOrderId = this.model.SalesOrderId //商销订单ID
            formData.Id = this.model.Id ? this.model.Id : this.model.OrderId //调价单ID
          } else {
            url = this.linkUrl.createSalesOrderAdjustPrice
          }
          this.confirmLoading = true
          postAction(url, formData, this.linkHttpHead).then((res) => {
            if (!res.IsSuccess) {
              this.confirmLoading = false

              this.$message.error(res.Msg)
              return
            }
            // 当调价金额为正数时，不需要审核；当调价金额为负数时，需要审核
            // if (this.TotalAdjustPrice > 0) {
            //   this.$message.success('操作成功！')
            //   this.$emit('ok')
            //   this.handleCancel()
            //   return
            // }
            // 保存草稿 不需要审核
            if (OperationType === 1) {
              this.confirmLoading = false
              this.$message.success('操作成功！')
              this.$emit('ok')
              this.handleCancel()
              return
            }
            this.createApproval(
              {
                // Remark: data.remark,
                BussinessNo: res.Data,
              },
              (bool) => {
                this.confirmLoading = false
                if (bool) {
                  this.$message.success('操作成功！')
                  this.$emit('ok')
                  this.handleCancel()
                }
              }
            )
            // 下一步
            // call && call()
          })
        }
      })
    },
    // 创建审核实例
    createApproval(data, callback) {
      if (!data['BussinessNo']) {
        this.$message.error('BussinessNo未获取到值')
        return
      }
      postAction(
        this.linkUrl.createApproval,
        {
          BussinessNo: data['BussinessNo'],
          Scenes: 18,
          OpratorId: Vue.ls.get(USER_ID),
          // Remark: data['Remark']
        },
        'P36005'
      ).then((res) => {
        if (!res.IsSuccess) {
          this.$message.error(res.Msg)
          callback && callback(false)
          return
        }
        // this.$message.success('操作成功！')
        callback && callback(true)
      })
    },
  },
}
</script>
<style scoped></style>
