<!-- 物流管理编辑 -->
<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <div :style="{
        width: '100%',
        padding: '10px 16px',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <div style="padding-bottom: 20px;color:rgba(0, 0, 0, 0.85)">物流商名称：{{model.ExpressCompanyName || '--'}}</div>
        <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
          <a-row :getter="10">
            <a-col :md="24">
              <a-form-model-item label="销售区域：" prop="jyKeys">
                <div style="display: flex;align-items: center;justify-content: space-between;">
                  <MultipleChoiceView style="flex: 1;margin-right:8px;" placeholder="请选择区域" :open="false" :maxTagCount="maxTagCount" :localDataList="model['jcDataList']" v-model="model['jcKeys']" :dataKey="{ name: 'Name', value: 'Code' }" ref="iCustomerType" @change="(value, contents)=>changeArea(value, contents,'jcDataList')" />
                  <a style="white-space: nowrap;" @click="chooseArea('jcDataList')">选择区域</a>
                </div>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
      <a-button @click="handleCancel"> 取消 </a-button>
    </a-row>
    <!-- 选择区域 -->
    <SelectAreaTree ref="SelectAreaTree" filterCountry="onlyCountry" :coverCountryInfo="coverCountryInfo" :level="3" :title="areaTitle" @ok="selectAreaOk" />
  </a-modal>
</template>

<script>
import moment from 'moment'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'physicalDistributionAddModal',
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      areaTitle: '选择区域',
      title: '编辑物流',
      visible: false,
      isEdit: false,
      model: {
        jcDataList: [],
        jcKeys: [],
      },
      confirmLoading: false,
      httpHead: 'P36008',
      url: {
        info: '/{v}/LogisticsOrder/GetShipperAreaDetail',
        update: '/{v}/LogisticsOrder/EditShipperArea',
      },
      maxTagCount: 4,
      coverCountryInfo: {
        Code: "10",
      }
    }
  },
  computed: {
    rules() {
      return {
      }
    },
  },
  mounted() { },
  created() {
    this.updateScreenWidth(); // 初始获取屏幕宽度
    window.addEventListener('resize', this.updateScreenWidth); // 监听屏幕大小改变
  },
  methods: {
    moment,
    edit(record) {
      this.title = '编辑'
      this.getInfo(record)
      this.isEdit = true
      this.visible = true
    },
    // 获取物流商关联详情
    getInfo(record) {
      const { ExpressCompanyCode } = record
      let jcDataList = []
      let jcKeys = []
      let formData = {
        expressCompanyCode: ExpressCompanyCode,
      }
      getAction(this.url.info, formData, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            // 数据源
            this.model = Object.assign({}, res.Data)
            // 销售区域
            if (res.Data.AdministrativeAreas && res.Data.AdministrativeAreas.length > 0) {
              res.Data.AdministrativeAreas.map(item => {
                jcKeys.push(item.AreaCode)
                jcDataList.push({ Code: item.AreaCode, Name: item.AreaFullName })
              })
            }
            this.model = {
              ...this.model,
              jcDataList: jcDataList || [],
              jcKeys: jcKeys || [],
            }
          } else {
            this.$message.warning(res.Msg)
          }
        })
        .finally(() => { })
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true
          let formData = {}
          let url = ''
          const AdministrativeAreas = this.model.jcDataList.map(f => {
            return {
              AreaCode: f.Code,
              AreaFullName: f.Name
            }
          })
          if (this.isEdit) {
            url = this.url.update
            formData = {
              ExpressCompanyCode: this.model.ExpressCompanyCode,
              AdministrativeAreas: AdministrativeAreas || []
            }
          }
          postAction(url, formData, this.httpHead)
            .then((res) => {
              if (res.IsSuccess) {
                that.$message.success('操作成功')
                that.$emit('ok')
                that.close()
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    changeTreeId(selectItem, key) {
      this.model[key] = selectItem.value
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    selectAreaOk(data) {
      this.setAreaData(data, this.modelType)
    },
    changeArea(value, contents, type) {
      this.setAreaData(contents, type)
    },
    setAreaData(data, type) {
      let codeArray = data.map(item => {
        return item.Code
      })
      let keys = null
      this.model[type] = data || []
      switch (type) {
        case 'jcDataList':
          keys = 'jcKeys'
          this.model[keys] = codeArray
          break;
      }
    },
    // 动态的获取区域最大显示数
    updateScreenWidth() {
      let screenWidth = window.innerWidth;
      if (screenWidth <= 890) {
        this.maxTagCount = 1
      } else if (screenWidth <= 910) {
        this.maxTagCount = 2
      } else if (screenWidth <= 1256) {
        this.maxTagCount = 3
      } else {
        this.maxTagCount = 4
      }
    },
    chooseArea(type) {
      let defaultSelected = this.model[type] || []
      this.modelType = type
      this.$refs.SelectAreaTree.show(defaultSelected)
    }
  },
}
</script>
