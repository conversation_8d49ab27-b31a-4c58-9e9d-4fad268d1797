<template>
  <a-modal
    title="选择商品"
    :width="'90vw'"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    v-drag-modal
  >
    <a-spin :spinning="confirmLoading">
      <!-- 搜索 -->
      <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
      <SimpleTable
        ref="table"
        :tab="tab"
        :isTableInitData="isTableInitData"
        :queryParam="queryParam"
        :columns="columns"
        :linkHttpHead="linkHttpHead"
        :linkUrlType="linkUrlType"
        :linkUrl="linkUrl"
        @getLoadData="getLoadData"
        @selectedRows="(selectedRowKeys, selectedRows) => handleSelectedRows(selectedRowKeys, selectedRows, 'table')"
      >
        <span slot="commonContent" slot-scope="{ text }">
          <j-ellipsis :value="`￥${text || 0}`" :length="15" />
        </span>
        <!-- 有效期 -->
        <span slot="time" slot-scope="{ text }">
          <j-ellipsis :value="`${text ? moment(text).format('YYYY-MM-DD') : ' -- '}`" :length="15" />
        </span>
        <div slot="PolicyPriceSlot" slot-scope="{ text, record }">
          <a @click="handleClickPolicyPrice(record)">
            <j-ellipsis
              :value="
                text == undefined || text == null
                  ? '--'
                  : (text < 0 ? '-' : '') +
                    '￥' +
                    Number(text < 0 ? String(text).replace('-', '') : text).toFixed(6)
              "
            />
          </a>
        </div>
      </SimpleTable>
    </a-spin>
    <div slot="footer">
      <div>
        <a-button
          type="primary"
          class="mr5"
          v-if="checkBtnPermissions('d8bee9f4-1212-4f5a-b4fd-c76038ada7e2')"
          @click="handleOk"
          >保存</a-button
        >
        <a-button @click="handleCancel">取消</a-button>
      </div>
    </div>
    <!-- 历史记录 弹窗 -->
    <PolicyBasePriceHistoricalRecordsModal ref="PolicyBasePriceHistoricalRecordsModal" />
  </a-modal>
</template>
<script>
import moment from 'moment'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'SelectGoodsListModal',
  components: { JEllipsis },
  mixins: [SimpleMixin, EditMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      tab: {
        rowKey: 'uniqueId',
        rowSelection: {
          type: 'checkbox',
          getCheckboxProps: this.getCheckboxProps,
        },
        customerPager: true,
        hideIpagination: true,
        bordered: true,
        sortArray: ['ErpGoodsName', 'time', 'PolicyPriceSlot'],
      },
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '商品名称', type: 'input', value: '', key: 'KeyWord', defaultVal: '', placeholder: '商品名称/编号' },
        {
          name: '商品类别',
          type: 'selectLink',
          vModel: 'ManageClassify1',
          dataKey: { name: 'Name', value: 'Id' },
          httpParams: { Name: '', Level: 1 },
          keyWord: 'Name',
          defaultVal: '',
          placeholder: '请选择',
          httpType: 'POST',
          url: '/{v}/GoodsManage/QueryGoodsManageClassifyList',
          httpHead: 'P36006',
        },
        { name: '生产厂家', type: 'input', value: '', key: 'BrandManufacturer', defaultVal: '', placeholder: '请输入' },
        { name: '批次号', type: 'input', value: '', key: 'BatchNo', defaultVal: '', placeholder: '请输入' },
      ],
      // 表头
      columns: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 250,
          fixed: 'left',
          customRender: (text, record, index) => {
            const { rowSpan, colSpan } = record
            const { CustomerInfo, returnDate } = this || {}
            const showErrorScope = !(CustomerInfo['Classifies'] || []).some((v) => v == record['BusinessScopeName'])
            const showErrorDrug = !CustomerInfo['DrugLicenseDate'] && record['ManageClassifyStr1']
            const obj = {
              children: 
                <span>
                  <j-ellipsis value={text} length={20} />
                  {
                    showErrorScope &&
                      <p style="color: red; font-size: 13px; margin-bottom: 0">
                        {
                          showErrorScope &&
                          record['ManageClassifyStr1'] != '促销品'
                            ? '超出经营范围'
                            : ''
                        }
                      </p>
                  }
                  {/* 没有药品经营许可证 */}
                  {
                    showErrorDrug &&
                    <p style="color: red; font-size: 13px; margin-bottom: 0">
                      未上传药品经营许可证
                    </p>
                  }
                  {/* 没有食品经营许可证 */}
                  {
                    !CustomerInfo['FoodLicenseDate'] &&
                      (record['ManageClassifyStr1'] == '食品' || record['ManageClassifyStr1'] == '保健食品') &&
                        <p style="color: red; font-size: 13px; margin-bottom: 0" >
                          未上传食品经营许可证
                        </p>
                  }
                  {/* 没有第二类医疗器械经营备案凭证 */}
                  {
                    !CustomerInfo['SecondTypeInstrumentDate'] && (record['BusinessScopeName'] || '').indexOf('Ⅱ') > -1 &&
                    <p style="color: red; font-size: 13px; margin-bottom: 0">
                      未上传第二类医疗器械经营备案凭证
                    </p>
                  }
                  {/* 没有医疗器械经营许可证 */}
                  {
                    !CustomerInfo['InstrumentLicenseDate'] && (record['BusinessScopeName'] || '').indexOf('Ⅲ') > -1 &&
                    <p style="color: red; font-size: 13px; margin-bottom: 0">
                      未上传医疗器械经营许可证
                    </p>
                  }
                  {/* 药品经营许可证过期 */}
                  {
                    CustomerInfo['DrugLicenseDate'] &&
                    record['ManageClassifyStr1'] == '药品' &&
                    returnDate(CustomerInfo['DrugLicenseDate']) < 0 &&
                      <p style="color: red; font-size: 13px; margin-bottom: 0">
                        药品经营许可证已过期
                      </p>
                  }
                  {/* 食品经营许可证已过期 */}
                  {
                    CustomerInfo['FoodLicenseDate'] &&
                    returnDate(CustomerInfo['FoodLicenseDate']) < 0 &&
                    (record['ManageClassifyStr1'] == '食品' || record['ManageClassifyStr1'] == '保健食品') &&
                      <p style="color: red; font-size: 13px; margin-bottom: 0">
                        食品经营许可证已过期
                      </p>
                  }
                  {/* 第二类医疗器械经营备案凭证已过期 */}
                  {
                    CustomerInfo['SecondTypeInstrumentDate'] &&
                    returnDate(CustomerInfo['SecondTypeInstrumentDate']) < 0 &&
                    (record['BusinessScopeName'] || '').indexOf('Ⅱ') > -1 &&
                      <p style="color: red; font-size: 13px; margin-bottom: 0">
                        第二类医疗器械经营备案凭证已过期
                      </p>
                  }
                  {/* 医疗器械经营许可证已过期 */}
                  {
                    CustomerInfo['InstrumentLicenseDate'] &&
                    returnDate(CustomerInfo['InstrumentLicenseDate']) < 0 &&
                    (record['BusinessScopeName'] || '').indexOf('Ⅲ') > -1 &&
                    <p style="color: red; font-size: 13px; margin-bottom: 0">
                      医疗器械经营许可证已过期
                    </p>
                  }
                </span>,
              attrs: {
                rowSpan: rowSpan || 1,
                colSpan: typeof colSpan === 'number' ? colSpan : 1,
              },
            }

            return obj
          }
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 120,
          fixed: 'left',
          customRender: (text, record, index) => {
            const { rowSpan, colSpan } = record
            const obj = {
              children: <span>{text}</span>,
              attrs: {
                rowSpan: rowSpan || 1,
                colSpan: typeof colSpan === 'number' ? colSpan : 1,
              },
            }

            return obj
          }
        },
        {
          title: '商品类别',
          dataIndex: 'ManageClassifyStr1',
          ellipsis: true,
          width: 120,
          customRender: (text, record, index) => {
            const { rowSpan, colSpan } = record
            const obj = {
              children: <span>{text}</span>,
              attrs: {
                rowSpan: rowSpan || 1,
                colSpan: typeof colSpan === 'number' ? colSpan : 1,
              },
            }

            return obj
          }
        },
        {
          title: '规格',
          dataIndex: 'PackingSpecification',
          ellipsis: true,
          width: 150,
          customRender: (text, record, index) => {
            const { rowSpan, colSpan } = record
            const obj = {
              children: <span>{text}</span>,
              attrs: {
                rowSpan: rowSpan || 1,
                colSpan: typeof colSpan === 'number' ? colSpan : 1,
              },
            }

            return obj
          }
        },
        {
          title: '单位',
          dataIndex: 'PackageUnit',
          ellipsis: true,
          width: 100,
          customRender: (text, record, index) => {
            const { rowSpan, colSpan } = record
            const obj = {
              children: <span>{text}</span>,
              attrs: {
                rowSpan: rowSpan || 1,
                colSpan: typeof colSpan === 'number' ? colSpan : 1,
              },
            }

            return obj
          }
        },
        {
          title: '件装量',
          dataIndex: 'PackageCount',
          ellipsis: true,
          width: 100,
          customRender: (text, record, index) => {
            const { rowSpan, colSpan } = record
            const obj = {
              children: <span>{text}</span>,
              attrs: {
                rowSpan: rowSpan || 1,
                colSpan: typeof colSpan === 'number' ? colSpan : 1,
              },
            }

            return obj
          }
        },
        {
          title: '生产厂商',
          dataIndex: 'BrandManufacturer',
          ellipsis: true,
          width: 200,
          customRender: (text, record, index) => {
            const { rowSpan, colSpan } = record
            const obj = {
              children: <span>{text}</span>,
              attrs: {
                rowSpan: rowSpan || 1,
                colSpan: typeof colSpan === 'number' ? colSpan : 1,
              },
            }

            return obj
          }
        },
        {
          title: '可销总库存',
          dataIndex: 'TotalCanSaleInventory',
          ellipsis: true,
          width: 200,
          customRender: (text, record, index) => {
            const { rowSpan, colSpan } = record
            const obj = {
              children: <span>{text}</span>,
              attrs: {
                rowSpan: rowSpan || 1,
                colSpan: typeof colSpan === 'number' ? colSpan : 1,
              },
            }

            return obj
          }
        },
        {
          title: '批号',
          dataIndex: 'BatchNumber',
          ellipsis: true,
          width: 150,
          customRender: (text, record, index) => {
            const { batchRowSpan, batchColSpan } = record
            const obj = {
              children: <span>{text}</span>,
              attrs: {
                rowSpan: batchRowSpan || 1,
                colSpan: typeof batchColSpan === 'number' ? batchColSpan : 1,
              },
            }

            return obj
          }
        },
        {
          title: '批号可销库存',
          dataIndex: 'CanSaleInventory',
          ellipsis: true,
          width: 150,
          customRender: (text, record, index) => {
            const { batchRowSpan, batchColSpan } = record
            const obj = {
              children: <span>{text}</span>,
              attrs: {
                rowSpan: batchRowSpan || 1,
                colSpan: typeof batchColSpan === 'number' ? batchColSpan : 1,
              },
            }

            return obj
          }
        },
        {
          title: '生产日期',
          dataIndex: 'ProductionDate',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'time' },
        },
        {
          title: '有效期',
          dataIndex: 'Period',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'time' },
        },
        {
          title: '批次号',
          dataIndex: 'BatchNo',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '仓库',
          dataIndex: 'WarehouseCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '批次可销库存',
          dataIndex: 'BatchCanSaleInventory',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商销政策底价',
          dataIndex: 'PolicyPrice',
          ellipsis: true,
          width: 150,
          // precision: 6,
          scopedSlots: { customRender: 'PolicyPriceSlot' },
        },
        // {
        //   title: '移动平均成本',
        //   dataIndex: 'AvgCostPrice',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'commonSlot' },
        // },
        // {
        //   title: '采购管理模式',
        //   dataIndex: 'ProcurementManagementModelName',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
        // {
        //   title: '是否赠品',
        //   dataIndex: 'IsGift',
        //   ellipsis: true,
        //   width: 100,
        //   customRender: (t) => {
        //     return t ? '是' : '否'
        //   },
        // },
        // {
        //   title: '采购人员',
        //   dataIndex: 'PurchaserName',
        //   ellipsis: true,
        //   width: 150,
        //   scopedSlots: { customRender: 'component' },
        // },
      ],
      mergedGoodsMap: {}, // hashMap 合并相同商品
      queryParam: {},
      CustomerInfo: {},
      IsSpecialGoods: false,
      isTableInitData: false, //是否自动加载
      tableSelectedRows: [],
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/Order/SalesGoodsListForOrderV15',
      },
      orderType: null
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    moment,
    getCheckboxProps(record) {
      // console.log(record['BusinessScopeName'])
      // console.log(this.CustomerInfo['Classifies'])
      // 超出客户经营范围不可勾选
      // <!-- 没有药品经营许可证  -->
      // v-if="!CustomerInfo['DrugLicenseDate'] && record['ManageClassifyStr1'] == '药品'"
      // <!-- 没有食品经营许可证  -->
      // v-if="!CustomerInfo['FoodLicenseDate'] && (record['ManageClassifyStr1'] == '食品' || record['ManageClassifyStr1'] == '保健食品')"
      // <!-- 没有第二类医疗器械经营备案凭证  -->
      // v-if="!CustomerInfo['SecondTypeInstrumentDate'] && record['BusinessScopeName'].indexOf('Ⅱ')> -1"
      // <!-- 没有医疗器械经营许可证  -->
      // v-if="!CustomerInfo['InstrumentLicenseDate'] && record['BusinessScopeName'].indexOf('Ⅲ')> -1"
      let disabledBool = false
      if (
        !(this.CustomerInfo['Classifies'] || []).some((v) => v == record['BusinessScopeName']) &&
        record['ManageClassifyStr1'] != '促销品'
      ) {
        disabledBool = true
      } else if (!this.CustomerInfo['DrugLicenseDate'] && record['ManageClassifyStr1'] == '药品') {
        disabledBool = true
      } else if (
        this.CustomerInfo['DrugLicenseDate'] &&
        record['ManageClassifyStr1'] == '药品' &&
        this.returnDate(this.CustomerInfo['DrugLicenseDate']) < 0
      ) {
        disabledBool = true
      } else if (
        !this.CustomerInfo['FoodLicenseDate'] &&
        (record['ManageClassifyStr1'] == '食品' || record['ManageClassifyStr1'] == '保健食品')
      ) {
        disabledBool = true
      } else if (
        this.CustomerInfo['FoodLicenseDate'] &&
        (record['ManageClassifyStr1'] == '食品' || record['ManageClassifyStr1'] == '保健食品') &&
        this.returnDate(this.CustomerInfo['FoodLicenseDate']) < 0
      ) {
        disabledBool = true
      } else if (
        !this.CustomerInfo['SecondTypeInstrumentDate'] &&
        (record['BusinessScopeName'] || '').indexOf('Ⅱ') > -1
      ) {
        disabledBool = true
      } else if (
        this.CustomerInfo['SecondTypeInstrumentDate'] &&
        (record['BusinessScopeName'] || '').indexOf('Ⅱ') > -1 &&
        this.returnDate(this.CustomerInfo['SecondTypeInstrumentDate']) < 0
      ) {
        disabledBool = true
      } else if (!this.CustomerInfo['InstrumentLicenseDate'] && (record['BusinessScopeName'] || '').indexOf('Ⅲ') > -1) {
        disabledBool = true
      } else if (
        this.CustomerInfo['InstrumentLicenseDate'] &&
        (record['BusinessScopeName'] || '').indexOf('Ⅲ') > -1 &&
        this.returnDate(this.CustomerInfo['InstrumentLicenseDate']) < 0
      ) {
        disabledBool = true
      }
      return {
        props: {
          disabled: disabledBool, // Column configuration not to be checked
          name: record.ErpGoodsName,
        },
      }
    },
    // 比较日期的大小  0 相等    负数   正数
    // this.returnDate(this.model.CustomerInfo['SpecialEntrustLetterDate']) < 0
    returnDate(validDate) {
      // console.log('validDate             ', validDate)
      const startTime = moment(moment(new Date()).format(this.dateFormat), this.dateFormat)
      const endTime = moment(validDate, this.dateFormat)
      const diff3 = moment(endTime).diff(moment(startTime), 'days')
      // console.log('moment               ', diff3)
      return diff3
    },
    show(IsSpecialGoods, CustomerInfo = {}, data = [], orderType) {
      // console.log(data)
      this.tableSelectedRows = []
      this.CustomerInfo = CustomerInfo
      this.IsSpecialGoods = IsSpecialGoods
      this.orderType = orderType
      this.visible = true
      this.queryParam = {
        IsSpecialGoods: IsSpecialGoods,
        CustomerId: CustomerInfo['Id'],
        SalesOrderType: orderType
      }
      this.$nextTick(() => {
        // this.$refs.table.selectedRowKeys = data.map(v => v.Id)
        // this.$refs.table.selectedRows = data
        this.lodaDataFun()
      })
    },
    lodaDataFun() {
      if (this.$refs.table)
        this.$refs.table.loadDatas(1, this.queryParam, (data) => {
          // 重组数据
          this.mergeTableData(data)
        })
    },
    getLoadData(data) {
      this.mergeTableData(data)
    },
    mergeTableData(data) {
      const mergeData = (data) => {
        const goodsCodeMap = {}
        const batchNoMap = {}

        data.forEach((item) => {
          item.uniqueId = `${item.Id}_${item.BatchNo}_${item.WarehouseCode}`
          // 批号唯一的key
          const key = `${item.ErpGoodsCode}${item.BatchNumber}`
          if (!batchNoMap[key]) {
            item.batchRowSpan = 1
            batchNoMap[key] = item
          } else {
            batchNoMap[key].batchRowSpan += 1
            item.batchColSpan = 0
          }
          // 商品编码唯一的key
          if (!goodsCodeMap[item.ErpGoodsCode]) {
            item.rowSpan = 1
            goodsCodeMap[item.ErpGoodsCode] = item
          } else {
            goodsCodeMap[item.ErpGoodsCode].rowSpan += 1
            item.colSpan = 0
          }
          this.mergedGoodsMap[item.ErpGoodsCode] = item
        })
      }
      const help = data.reduce((prev, curr) => {
        curr.Children && mergeData(curr.Children)
        return prev.concat(curr.Children || [])
      }, [])
      this.$refs.table.dataSource = help
    },
    getSameNum(Arr) {
      //排序
      let key = 'ErpGoodsId'
      let arr = Arr.sort((a, b) => {
        if (a[key] === b[key]) {
          return 0
        } else if (a[key] < b[key]) {
          return -1
        } else {
          return 1
        }
      })

      // console.log('arr', arr)
      // key的数组
      let sortArray = arr.map((item) => {
        return item[key]
      })
      let duplicates = sortArray.filter((val, i) => sortArray.indexOf(val) !== i)
      // 相同key的数组
      let sameArray = Array.from(new Set(duplicates.length > 1 ? duplicates : []))
      // console.log(sameArray)

      let dataArray = []
      sameArray.map((item) => {
        dataArray.push({
          key: item,
          num: sortArray.filter((e) => e === item).length, //相同元素出现的次数
          index: sortArray.indexOf(item), //相同元素首次出现的位置
          lasiIndex: sortArray.lastIndexOf(item), //相同元素末次出现的位置
        })
      })
      arr.map((item, index) => {
        dataArray.map((rItem) => {
          if (item[key] === rItem.key) {
            if (index === rItem.index) {
              item.rowSpan = rItem.num
            } else {
              item.rowSpan = 0
            }
          }
        })
      })
      this.$refs.table.dataSource = arr
      // console.log('dataArray', dataArray, arr)
    },
    searchQuery() {
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      this.queryParam.IsSpecialGoods = this.IsSpecialGoods
      this.queryParam.CustomerId = this.CustomerInfo['Id']
      this.queryParam.SalesOrderType = this.orderType
      this.$nextTick(() => {
        if (this.$refs.table) this.lodaDataFun()
      })
    },
    handleCancel() {
      this.visible = false
      this.tableSelectedRows = []
      this.mergedGoodsMap = {}
    },
    handleSelectedRows(selectedRowKeys, selectedRows, key) {
      // console.log('selectedRowKeys   ', selectedRowKeys)
      // console.log('selectedRows   ', selectedRows)
      selectedRows.map((j) => {
        if (this.tableSelectedRows.findIndex((item) => item.uniqueId == j.uniqueId) == -1) {
          this.tableSelectedRows.push(j)
        }
      })
      this.tableSelectedRows = this.tableSelectedRows.filter(
        (item) => selectedRowKeys.findIndex((v) => v == item.uniqueId) > -1
      )

      // console.log('tableSelectedRows   ', this.tableSelectedRows)
    },
    handleOk(record) {      
      console.log('record',JSON.stringify(record))
      console.log('mergedGoodsMap',JSON.stringify(this.mergedGoodsMap))
      console.log('tableSelectedRows',JSON.stringify(this.tableSelectedRows))
      let tableSelectedRows = this.tableSelectedRows.map(item => ({        
        ...item,
        // TotalCanSaleInventory: this.mergedGoodsMap[item.ErpGoodsCode].TotalCanSaleInventory
      }))
      console.log('tableSelectedRows',JSON.stringify(tableSelectedRows))
      // console.log(tableSelectedRows.length)
      // console.log('table1SelectedRows   ', table1SelectedRows.length)
      if (!tableSelectedRows.length) {
        this.$message.warning('请选择数据!')
        return
      }
      this.$emit('ok', tableSelectedRows)
      this.handleCancel()
    },
    handleClickPolicyPrice(record) {
      console.log('点击了')
      this.$refs.PolicyBasePriceHistoricalRecordsModal.show(record, false, true)
    }
  },
}
</script>
<style scoped></style>
