<!--
 * @Description: wenjingGao Create File 销售单价规则弹窗
 * @Version: 1.0
 * @Author: wenjingGao
 * @Date: 2025-02-06 14:16:07
 * @LastEditors: wenjingGao
 * @LastEditTime: 2025-02-10 10:52:49
-->
<template>
  <a-modal 
    :title="title" 
    :width="900" 
    v-model="visible" 
    :confirmLoading="confirmLoading" 
    @cancel="handleCancel" 
    @ok="handleOk" 
    :maskClosable="false" 
    :destroyOnClose="true"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model
        ref="PriceRuleForm"
        :model="form"
        :rules="rules"
        layout="vertical"
      >
        <a-form-model-item label="客户名称" prop="customerName">
          <!-- 普通多选 -->
          <SelectMultipleInput 
            :value="form.customerName" 
            :httpParams="httpParams" 
            httpHead='P36002' 
            url='/{v}/Customer/ListForOrder' 
            keyWord="KeyWord" 
            :dataKey="{ name: 'Name', value: 'Id' }" 
            placeholder="搜索名称/编号，可多选" 
            @change="(key, selectItem, e) => changeMultipleInput(key, selectItem, e)" 
          />
        </a-form-model-item>
        <a-form-model-item label="销售单价规则" prop="priceRule">
          <div class="priceRuleStyle">
            <span class="priceRuleStyleLabel">商销政策底价*</span>
            <a-input 
              v-model="form.priceRule" 
              placeholder="例：加3个点，则填写1.03"  
              oninput="value=value.replace(/[^\d^\.]+/g, '').replace(/^0+(\d)/, '$1').replace(/^\./, '0.').match(/^\d*(\.?\d{0,2})/g)[0] || ''"
            />
          </div>
        </a-form-model-item>
      </a-form-model>
    </a-spin>
  </a-modal>  
</template>

<script>
import { getAction, putAction, postAction, deleteAction } from "@/api/manage";

export default {
  name: 'SalesUnitPriceRuleModal',
  props: {
    title: {
      type: String,
      default: "新增销售单价规则"
    }
  },
  data() {
    return {
      visible: false,
      confirmLoading: false,
      form: {
        customerName: [],// 编辑时返显的数据格式{ key: "e7c9dd15-2d20-4e33-b75c-e9613ecf705a", label: "小胡的批发企业" }
        priceRule: ''
      },
      rules: {
        customerName: [{ required: true, message: '请添加客户名称', type: 'array', trigger: 'blur' }],
        priceRule: [{ required: true, message: '请添加销售单价规则', trigger: 'blur' }]
      },
      httpParams: { PageIndex: 1, PageSize: 100 },
      linkHttpHead: 'P36008',
      linkUrl: {
        saveRule: '/{v}/SalesOrderManualPriceRule/Save',
        queryRule: '/{v}/SalesOrderManualPriceRule'
      },
      customerId: null
    }
  },
  methods: {
    show(Id) { 
      if (Id) {
        this.customerId = Id
        this.queryRule(Id)
      }
      this.visible = true
    },
    queryRule(Id) {
      getAction(`${this.linkUrl.queryRule}/${Id}`, {}, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        let customers = []
        let {Customers , PointValue } = res.Data
        this.form.priceRule = PointValue
        Customers.forEach(element => {
          customers.push({
            key: element.Id,
            label: element.Name
          })
        });
        this.form.customerName = customers
        console.log(res)
      })
    },
    changeMultipleInput(key, selectItem, e) {
      console.log('changeMultipleInput', key, selectItem, e)
      this.form.customerName = e
    },
    handleCancel() {
      this.$refs.PriceRuleForm.resetFields()
      this.customerId = null
      this.visible = false
    },
    handleOk() {
      console.log('校验', this.$refs.PriceRuleForm)
      this.$refs.PriceRuleForm.validate(Valid => {
        console.log('表单校验', Valid)
        if (!Valid) return false
        let customers = []
        this.form.customerName.forEach(element => {
          customers.push({
            CustomerId: element.key,
            CustomerName: element.label
          })
        });
        let params = {
          Id: this.customerId,
          PointValue: this.form.priceRule,
          Customers: customers
        }
        postAction(this.linkUrl.saveRule, params, this.linkHttpHead).then((res) => {
          if (!res.IsSuccess) {
            this.$message.warning(res.Msg)
            return
          }
          this.$message.success('保存成功!')
          this.$emit('handleOk')
          this.$refs.PriceRuleForm.resetFields()
          this.customerId = null
          this.visible = false
        })
        
      })
    } 
  }
}
</script>

<style scoped lang="scss">
.priceRuleStyle {
  display: flex;
  align-items: center;
}
.priceRuleStyleLabel {
  display: inline-block;
  width: 110px;
}
</style>