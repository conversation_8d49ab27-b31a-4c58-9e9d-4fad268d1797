<template>
  <a-modal
    title="查看历史销售价"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
    :footer="null"
  >
    <a-spin :spinning="confirmLoading">
      <!-- 搜索 -->
      <SimpleSearchArea
        ref="SimpleSearchArea"
        :searchInput="searchInput"
        @searchQuery="searchQuery"
      />
      <SimpleTable
        ref="table"
        :tab="tab"
        :isTableInitData="isTableInitData"
        :queryParam="queryParam"
        :columns="columns"
        :linkHttpHead="linkHttpHead"
        :linkUrlType="linkUrlType"
        :linkUrl="linkUrl"
      >
        <span
          slot="commonContent"
          slot-scope="{text}"
        >
          <j-ellipsis
            :value="text?`￥${Number(text).toFixed(2)}` : '0'"
            :length="15"
          />
        </span>
      </SimpleTable>
    </a-spin>

  </a-modal>
</template>
<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'ViewHistoricalSalesPricesListModal',
  components: { JEllipsis },
  mixins: [SimpleMixin, EditMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      tab: {},
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '客户名称', type: 'input', value: '', key: 'CustomerName', defaultVal: '', placeholder: '请输入' },
        { name: '客户编号', type: 'input', value: '', key: 'CustomerCode', defaultVal: '', placeholder: '请输入' },
        { name: '联系电话', type: 'input', value: '', key: 'CustomerLinkPhone', defaultVal: '', placeholder: '请输入' }
      ],
      // 表头
      columns: [
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '客户编号',
          dataIndex: 'CustomerCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '联系人',
          dataIndex: 'CustomerLinkName',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '联系电话',
          dataIndex: 'CustomerLinkPhone',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '末次销售时间',
          dataIndex: 'LastOrderTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' }
        },
        {
          title: '末次销售价格',
          dataIndex: 'LastPrice',
          ellipsis: true,
          // sorter: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' }
        }
      ],
      queryParam: {},
      GoodsId: null,
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/Order/GoodsSalesRecords'
      }
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show(GoodsId) {
      // console.log(data)
      this.GoodsId = GoodsId
      this.queryParam.GoodsId = GoodsId
      this.visible = true
      this.$nextTick(() => {
        if (this.$refs.table) this.$refs.table.loadDatas(1, this.queryParam)
      })
    },

    searchQuery(queryParam, type) {
      queryParam['GoodsId'] = this.GoodsId
      this.$refs.table.loadDatas(1, queryParam)
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>
<style scoped></style>
