<template>
  <a-modal
    title="选择单据"
    :width="1100"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <a-spin :spinning="confirmLoading">
      <!-- 搜索 -->
      <SimpleSearchArea ref="SimpleSearchArea" :queryCol="8" :searchInput="searchInput" @searchQuery="searchQuery" />
      <SimpleTable
        ref="table"
        :tab="tab"
        :isTableInitData="isTableInitData"
        :queryParam="queryParam"
        :columns="columns"
        :linkHttpHead="linkHttpHead"
        :linkUrlType="linkUrlType"
        :linkUrl="linkUrl"
        @onSelect="handleSelectedRows"
        @onSelectAll="onSelectAll"
        @simpleTableLoadAfter="simpleTableLoadAfter"
      >
        <span slot="OrderNo" slot-scope="{ record }">
          <a @click="onOrderNoClick(record)"><j-ellipsis :value="record.OrderNo" :length="20" /></a>
        </span>
        <span slot="BillsAmount" slot-scope="{ text, record }">
          <j-ellipsis
            :value="`${record.BillsType == 22 ? '￥-' + Number(text).toFixed(2) : '￥' + Number(text).toFixed(2)}`"
            :length="15"
          />
        </span>
      </SimpleTable>
    </a-spin>
    <div slot="footer">
      <div>
        <a-button
          type="primary"
          class="mr5"
          v-if="checkBtnPermissions('0bd3768d-be27-4107-a905-52f0135b6486')"
          @click="handleOk"
          >保存</a-button
        >
        <a-button @click="handleCancel">取消</a-button>
      </div>
    </div>
  </a-modal>
</template>
<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'SelectReceiptListModal',
  components: { JEllipsis },
  mixins: [SimpleMixin, EditMixin, ListMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      tab: {
        rowKey: 'Id',
        rowSelection: {
          type: 'checkbox',
          getCheckboxProps: this.getCheckboxProps,
        },
        sortArray: ['BillsAmount', 'OrderNo'],
      },
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '单据编号', type: 'input', value: '', key: 'BillsNo', defaultVal: '', placeholder: '请输入' },
        // {
        //   name: '单据类型',
        //   type: 'selectBasicLink',
        //   value: '',
        //   vModel: 'BusinessOrderType',
        //   dictCode: 'EnumBusinessOrder',
        //   defaultVal: '',
        //   placeholder: '请选择',
        //   notOption: [1, 2, 3, 4, 5, 6, 9, 10, 11, 12, 13, 14, 20, 24, 25, 50, 51, 55, 60, 70, 80],
        // },
        {
          name: '对应订单编号',
          type: 'input',
          value: '',
          key: 'OrderNo',
          defaultVal: '',
          placeholder: '请输入',
        },
        {
          name: '订单付款方式',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PaymentMode',
          dictCode: 'EnumPaymentMode',
          defaultVal: '',
          placeholder: '请选择',
          notOption: [0, 1, 4, 5],
        },
        {
          name: '发生日期',
          type: 'timeInput',
          value: '',
          rangeDate: [],
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],
      // 表头
      columns: [
        {
          title: '单据编号',
          dataIndex: 'BillsNo',
          ellipsis: true,
          width: 200,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单据类型',
          dataIndex: 'BillsTypeStr',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对应订单编号',
          dataIndex: 'OrderNo',
          ellipsis: true,
          width: 200,
          scopedSlots: { customRender: 'OrderNo' },
        },
        {
          title: '订单付款方式',
          dataIndex: 'PaymentModeStr',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '发生日期',
          dataIndex: 'BillsTime',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '金额',
          dataIndex: 'BillsAmount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'BillsAmount' },
        },
      ],
      selectedRowData: [], //选择的项数据
      queryParam: {},
      CustomerInfo: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/Order/GetBillsByCustomer',
      },
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    getCheckboxProps(record) {
      // 不是出库单不可选择
      return {
        props: {
          disabled: record['BillsType'] != 21,
          name: record.BillsNo,
        },
      }
    },
    show(CustomerInfo = {}, data = []) {
      // console.log(data)
      this.selectedRowData = []
      this.CustomerInfo = CustomerInfo
      this.visible = true
      this.queryParam = {
        CustomerId: CustomerInfo['Id'],
      }
      this.$nextTick(() => {
        if (this.$refs.table) {
          this.$refs.table.clearSelected()
          this.$refs.table.loadDatas(1, this.queryParam)
        }
        if (this.$refs.SimpleSearchArea) {
          this.$refs.SimpleSearchArea.resetQueryParam()
        }
      })
    },
    // loadAfter方法
    simpleTableLoadAfter(data) {
      if (data && data.length > 0) {
        // 默认展开第一个
        this.$refs.table.expandedRowKeys = [data[0].Id]
        this.$refs.table.dataSource = this.setTableData(data)
        // console.log(this.$refs.table.dataSource)
      } else {
        this.$refs.table.dataSource = []
      }
    },
    // 递归重组数据
    setTableData(data, level = 1, parentId = null) {
      if (data == null || data.length <= 0) {
        return []
      }
      let tempArr = []
      try {
        data.forEach((item) => {
          item.level = level
          item.parentId = parentId
          item.Id = (item.Id || '') + (item.parentId || '')
          var temp = item
          if (item.Childrens && item.Childrens.length > 0) {
            temp['children'] = this.setTableData(item.Childrens, level + 1, item.Id)
          }
          tempArr.push(temp)
        })
      } catch (e) {
        console.log(e)
      }
      return tempArr
    },
    searchQuery() {
      this.$refs.table.clearSelected()
      this.selectedRowData = []
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      this.queryParam.CustomerId = this.CustomerInfo['Id']
      this.$nextTick(() => {
        if (this.$refs.table) this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    handleCancel() {
      this.visible = false
      this.selectedRowData = []
    },

    handleOk(record) {
      if (!(this.$refs.table.selectedRowKeys || []).length) {
        this.$message.warning('请选择数据!')
        return
      }
      let rowsArray = []
      this.$refs.table.selectedRows.map((v) => {
        let rowItem = this.$refs.table.selectedRowKeys.find((item) => {
          return item == v.Id
        })
        if (rowItem) {
          rowsArray.push(v)
        }
      })
      console.log(this.$refs.table.selectedRowKeys, this.$refs.table.selectedRows, rowsArray)
      this.$emit('ok', rowsArray)
      this.handleCancel()
    },
    // 选中 子级选中
    handleSelectedRows(record, selected, selectedRows) {
      let selectedRowKeys = this.$refs.table.selectedRowKeys || []
      // console.log('handleSelectedRows', this.$refs.table.selectedRowKeys)
      if (selected) {
        if (record && (record['Childrens'] || []).length) {
          let childIds = record.Childrens.map((v) => v.Id)
          selectedRowKeys = (selectedRowKeys || []).concat(childIds)
        }
      } else {
        if (record && (record['Childrens'] || []).length) {
          let childIds = record.Childrens.map((v) => v.Id)
          selectedRowKeys = selectedRowKeys.filter((v) => childIds.indexOf(v) < 0)
        }
      }

      this.$refs.table.selectedRowKeys = selectedRowKeys

      this.initSelectRows()
      // this.$refs.table.selectedRows = Rows.filter((v) => v.BillsType == 21)
    },
    initSelectRows() {
      let dataList = this.$refs.table.dataSource
      if (dataList.length > 0 && this.$refs.table.selectedRowKeys.length > 0) {
        this.$refs.table.selectedRowKeys.forEach((idKey) => {
          let row = dataList.find((x) => x.Id == idKey)
          if (row) {
            let index = this.selectedRowData.findIndex((x) => x.Id == row.Id)
            if (index == -1) {
              this.selectedRowData.push(row)
            }
          }
        })
      }
      this.$refs.table.selectedRows = this.selectedRowData.filter((v) => v.BillsType == 21)
    },
    // 全选
    onSelectAll(record, selected, selectedRows) {
      console.log('onSelectAll', selectedRows)

      let selectedRowKeys = this.$refs.table.selectedRowKeys || []
      // let Rows = this.$refs.table.selectedRows || []
      if (record) {
        if ((selected || []).length) {
          selected.map((v) => {
            if ((v['Childrens'] || []).length) {
              let childIds = v.Childrens.map((v) => v.Id)
              selectedRowKeys = (selectedRowKeys || []).concat(childIds)
            }
          })
        }
      } else {
        selectedRowKeys = []
      }
      this.$refs.table.selectedRowKeys = selectedRowKeys
      this.initSelectRows()

      // this.$refs.table.selectedRows = Rows.filter((v) => v.BillsType == 21)
    },
    onOrderNoClick(record) {
      this.visible = false
      this.goPage('/pages/marketingManagement/ordersManagementListDetail', { id: record.OrderId })
    },
  },
}
</script>
<style scoped></style>
