<template>
  <a-modal :title="title" :width="900" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <a-alert type="error" :message="`驳回原因：${model.AuditOpinion || ' -- '}`" banner :showIcon="false" v-if="isReupload" />
      <a-form-model ref="ruleForm" :rules="rules" :model="model" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="gutter">

          <a-col :span="md">
            <a-form-model-item label="公司名称：" prop="CustomerName">
              {{model.CustomerName || ' -- '}}
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item label="订单编号：" prop="SalesOrderCode">
              {{model.SalesOrderCode || ' -- '}}
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item label="订单金额：" prop="TotalAmount">
              ￥{{model.TotalAmount || ' -- '}}
            </a-form-model-item>
          </a-col>
          <a-col :span="md">
            <a-form-model-item label="上传回执单：" prop="FileUrls">
              <MultiUpload :max="10" :images="model.FileUrls" :uploadText="'上传'" :fileType='4' :bName="BName" :dir="Dir" :readOnly="false" :fileSize="50" @change="multiUploadChange" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
    <div slot="footer">
      <YYLButton menuId="f96809a1-8b3d-4f93-a9f3-8bd406aeb5e2" text="提交" type="primary" @click="handleOk" />
      <a-button @click="handleCancel">取消</a-button>
    </div>
  </a-modal>
</template>
<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'ReceiptUploadModal',
  components: {},
  mixins: [EditMixin],
  data() {
    return {
      confirmLoading: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      visible: false,
      md: 24,
      title: '',
      isReupload: null,
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
      model: {
        FileUrls: []
      },
      linkHttpHead: 'P36008',
      linkUrl: {
        createApproval: '/{v}/ApprovalWorkFlowInstance/CreateApproval', // 创建审核实例
        uploadSalesOrderReceipt: '/{v}/Order/UploadSalesOrderReceipt'
      }
    }
  },
  computed: {
    rules() {
      return {
        FileUrls: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if ((this.model.FileUrls || []).length == 0) {
                callback(new Error('请上传!'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    }
  },
  mounted() { },
  created() { },
  methods: {
    // 上传回调
    multiUploadChange(images) {
      this.model.FileUrls = images
    },
    show(data, isReupload) {
      this.model = {
        ...data,
        FileUrls: []
      }
      this.title = isReupload ? '重新上传' : '上传'
      this.isReupload = isReupload
      this.visible = true
    },
    handleCancel() {
      this.visible = false
    },

    handleOk() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          let formData = {
            Id: this.model.Id,
            FileUrls: this.model.FileUrls
          }
          this.confirmLoading = true
          postAction(this.linkUrl.uploadSalesOrderReceipt, formData, this.linkHttpHead).then(res => {
            if (!res.IsSuccess) {
              this.confirmLoading = false
              this.$message.error(res.Msg)
              return
            }
            this.createApproval(
              {
                // Remark: data.remark,
                BussinessNo: res.Data
              },
              bool => {
                this.confirmLoading = false
                if (bool) {
                  this.$message.success('操作成功！')
                  this.$emit('ok')
                  this.handleCancel()
                }
              }
            )
            // 下一步
            // call && call()
          })
        }
      })
    },
    // 创建审核实例
    createApproval(data, callback) {
      if (!data['BussinessNo']) {
        this.$message.error('BussinessNo未获取到值')
        return
      }
      postAction(
        this.linkUrl.createApproval,
        {
          BussinessNo: data['BussinessNo'],
          Scenes: 20,
          OpratorId: Vue.ls.get(USER_ID)
          // Remark: data['Remark']
        },
        'P36005'
      ).then(res => {
        if (!res.IsSuccess) {
          this.$message.error(res.Msg)
          callback && callback(false)
          return
        }
        // this.$message.success('操作成功！')
        callback && callback(true)
      })
    }
  }
}
</script>
<style scoped></style>
