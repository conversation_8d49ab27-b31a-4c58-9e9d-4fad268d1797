<template>
  <a-modal v-model="visible" title="处理" :width="600" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
        <a-row :getter="10">
          <a-col :md="24">
            <a-form-model-item prop="handleRemark">
              <a-textarea placeholder="请输入备注" v-model="model.handleRemark" :rows="4" :maxLength="200" />
            </a-form-model-item>

          </a-col>
        </a-row>
      </a-form-model>
      <a-row :style="{ textAlign: 'right' }" slot="footer">
        <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="handleOk" type="primary">保存</a-button>
        <a-button @click="handleCancel">
          取消
        </a-button>
      </a-row>
    </a-spin>
  </a-modal>
</template>

<script>
import { EditMixin } from '@/mixins/EditMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: 'InvalidInvoiceDisposeModal',
  mixins: [EditMixin],
  data() {
    return {
      visible: false,
      model: {

      },
      confirmLoading: false,
      httpHead: 'P36008',
      url: {
        add: "/{v}/OrderInvoice/UpdateInvoiceApplyHandel",
      },
    }
  },
  computed: {
    rules() {
      return {
        handleRemark: [{ required: true, message: '请输入备注!' }],
      };
    },
  },
  created() {

  },
  methods: {
    show(record) {
      record.handleRemark = ''
      this.model = Object.assign({}, record);
      this.visible = true
    },
    // 确定
    handleOk() {
      const that = this;
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          that.confirmLoading = true;
          let formData = {}
          let url = this.url.add + '?id=' + this.model.Id + '&handleRemark=' + this.model.handleRemark
          postAction(url, formData, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              that.$message.success("操作成功");
              that.$emit("ok");
              that.close();
            } else {
              that.$message.warning(res.Msg);
            }
          }).finally(() => {
            that.confirmLoading = false;
          });
        }
      });
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },

}
</script>

<style>
</style>