
<!-- 销货对抵审核 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="returnColumns" :isTableInitData="isTableInitData" @operate="operate" @changeTab="changeTab" @getListAmount="getListAmount" />

  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '销货对抵审核',
  name: 'VerificationSalesModules',
  mixins: [SimpleMixin],
  props: {
    btnIds: {
      type: Array,
      default: () => {
        return []
      },
    },
    // shangxiao 来自商销管理 caiwu 来自财务管理
    source: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        {
          name: '销货对抵编号',
          type: 'input',
          value: '',
          key: 'BillNo',
          defaultVal: '',
          placeholder: '请输入',
        },
        { name: '订单编号', type: 'input', value: '', key: 'OrderNo', defaultVal: '', placeholder: '请输入' },
        { name: '客户名称', type: 'input', value: '', key: 'OffsetPartyName', defaultVal: '', placeholder: '请输入' },
        { name: '联系电话', type: 'input', value: '', key: 'OffsetPartyPhone', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: 1,
        statusKey: '待审核', //标签的切换关键字
        statusList: [
          {
            name: '待审核',
            value: 1,
            count: '',
          },
          {
            name: '审核记录',
            value: 4,
            count: '',
          },
        ],
      },
      columns: [
        {
          title: '销货对抵编号',
          dataIndex: 'SalesOffsetOrderNo',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对抵方',
          dataIndex: 'SupplierName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '业务员',
          dataIndex: 'SalesManName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '对抵金额',
          dataIndex: 'OffsetAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '备注',
          dataIndex: 'Remark',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          // sorter: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
          actionBtn: [
            {
              name: '审核',
              icon: '',
              id: this.btnIds[0].id,
              specialShowFuc: (e) => {
                let record = e || {}
                return this.$refs.table.tab.status == 1
              },
            },
            {
              name: '详情',
              icon: '',
              id: this.btnIds[1].id,
              specialShowFuc: (e) => {
                let record = e || {}
                return this.$refs.table.tab.status != 1
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],

      returnColumns: [],
      queryParam: {
        IsNeedAudit: true,
      },
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36012',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/SalesOffset/AuditList',
        listAmountType: 'GET',
        listAmount: '/{v}/SalesOffset/AuditStatusCount',
      },
    }
  },
  created() { },
  computed: {},
  activated() {
    this.queryParam = this.$refs.SimpleSearchArea.queryParam
    this.returnColumns = this.getColumns(this.tab.status)
    this.queryParam['IsNeedAudit'] = true
    this.$refs.table.loadDatas(null, this.queryParam)
  },
  methods: {
    getColumns(status) {
      return this.columns
    },
    getListAmount(data) {
      this.tab.statusList[0].count = (data && data.NeedAuditCount) || 0
      this.tab.statusList[1].count = (data && data.AuditedCount) || 0
    },
    changeTab(value) {
      this.queryParam = this.$refs.SimpleSearchArea.queryParam
      this.queryParam.IsNeedAudit = value == 1 ? true : false
      this.returnColumns = this.getColumns(value)
      this.$nextTick(() => {
        this.$refs.table.loadDatas(1, this.queryParam)
      })
    },
    searchQuery(queryParam, type) {
      if (!this.$refs.table) {
        return
      }
      if (type == 'searchReset') {
        this.$refs.table.tab.status = 1
      }
      queryParam['IsNeedAudit'] = this.$refs.table.tab.status == 1 ? true : false
      this.$refs.table.loadDatas(1, queryParam)
    },
    // 列表操作
    operate(record, type) {
      if (type == '审核') {
        let path = this.source == 'shangxiao'
          ? '/pages/marketingManagement/commercialApproval/verificationSalesAgainstSalesAuditPage'
          : '/pages/financialManagement/financialExamination/purchaseCounterAuditInfo'
        this.goPage(path, {
          id: record.Id,
          audit: 1,
          source: this.source
        })
      } else if (type == '详情') {
        let path = this.source == 'shangxiao'
          ? '/pages/marketingManagement/commercialApproval/verificationSalesAgainstSalesAuditPage'
          : '/pages/financialManagement/financialExamination/purchaseCounterAuditInfo'
        this.goPage(path, {
          id: record.Id,
          source: this.source
        })

      }
    },
  },
}
</script>

<style>
</style>