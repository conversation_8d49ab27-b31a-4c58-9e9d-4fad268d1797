<template>
  <a-modal title="申请售后" :width="1200" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <a-spin :spinning="confirmLoading">
      <ApplyAfterSalesServiceCom ref="iApplyAfterSalesServiceCom" :OrderId="model.OrderId" :TotalAmount="model.TotalAmount" :recordCount="recordCount" v-if="model && recordCount >= 0" />
      <!--  -->
    </a-spin>
    <div slot="footer">
      <YYLButton menuId="0f471b0e-0bef-4370-b845-09f002eb0c29" text="保存" type="primary" @click="handleOk(false)" />
      <YYLButton menuId="0f471b0e-0bef-4370-b845-09f002eb0c29" text="提交审核" type="primary" @click="handleOk(true)" />
      <a-button @click="handleCancel">取消</a-button>
    </div>
  </a-modal>
</template>
<script>
import Vue from 'vue'
import { USER_ID } from '@/store/mutation-types'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'ApplyAfterSalesServiceModal',
  components: { JEllipsis },
  mixins: [SimpleMixin, EditMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      md: 24,
      model: null,
      recordCount: null,
      linkHttpHead: 'P36008',
      linkUrlType: 'POST', //请求方式
      linkUrl: {
        list: '/Customer/ListByLabels',
        createApproval: '/{v}/ApprovalWorkFlowInstance/CreateApproval', // 创建审核实例
        createSaleAfterOrder: '/{v}/SaleAfterOrder/CreateSaleAfterOrderV15',
        recordCount: '/{v}/SaleAfterOrder/GetOrderSaleAfterRecordCount',
      },
    }
  },
  computed: {
    rules() {
      return {}
    },
  },
  mounted() { },
  created() { },
  methods: {
    show(data) {
      // console.log(data)
      this.model = {
        OrderId: data['Id'],
        TotalAmount: data['TotalAmount'],
      }
      this.getRecordCount(data['Id'])
      this.visible = true
    },
    handleCancel() {
      this.visible = false
    },
    getRecordCount(oId) {
      let that = this
      that.confirmLoading = true
      getAction(this.linkUrl.recordCount, { orderId: oId }, that.linkHttpHead)
        .then((res) => {
          if (res.IsSuccess) {
            that.recordCount = res.Data || 0
          }
        })
        .finally(() => {
          that.confirmLoading = false
        })
    },

    handleOk(IsSubmit) {
      let formData = this.$refs.iApplyAfterSalesServiceCom.onSubmit()
      if (!formData) return
      let submitData = JSON.parse(JSON.stringify(formData))
      submitData.IsSubmit = IsSubmit
      if (submitData.SaleAfterOrderGoodses && submitData.SaleAfterOrderGoodses.length > 0) {
        submitData.SaleAfterOrderGoodses.forEach((x) => {
          if (!x.SaleAfterCount) {
            x.SaleAfterCount = 0
          }
        })
      }

      // let list = formData.SaleAfterOrderGoodses.filter((item) => !item.SaleAfterCount)
      // if (list && list.length > 0) {
      //   this.$message.warning('请把退货数量填写完整！')
      //   return
      // }
      this.confirmLoading = true
      postAction(this.linkUrl.createSaleAfterOrder, submitData, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.error(res.Msg)
          this.confirmLoading = false
          return
        }
        // 草稿
        if (!IsSubmit) {
          this.$message.success('操作成功！')
          this.$emit('ok')
          this.handleCancel()
          return
        }
        this.createApproval(
          {
            // Remark: data.remark,
            BussinessNo: res.Data,
          },
          (bool) => {
            if (bool) {
              this.$message.success('操作成功！')
              this.$emit('ok')
              this.handleCancel()
            }
          }
        )
      })
    },
    // 创建审核实例
    createApproval(data, callback) {
      if (!data['BussinessNo']) {
        this.$message.error('BussinessNo未获取到值')
        return
      }
      if (!this.confirmLoading) {
        this.confirmLoading = true
      }
      postAction(
        this.linkUrl.createApproval,
        {
          BussinessNo: data['BussinessNo'],
          Scenes: 19,
          OpratorId: Vue.ls.get(USER_ID),
          // Remark: data['Remark']
        },
        'P36005'
      )
        .then((res) => {
          if (!res.IsSuccess) {
            this.$message.error(res.Msg)
            this.confirmLoading = false
            callback && callback(false)
            return
          }
          // this.$message.success('操作成功！')
          callback && callback(true)
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
  },
}
</script>
<style scoped></style>
