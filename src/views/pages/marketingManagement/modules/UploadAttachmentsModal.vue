<template>
  <a-modal
    title="上传附件"
    :width="600"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
        <a-row :getter="10">
          <a-col :md="24">
            <a-form-model-item prop="fileUrls">
              <MultiUpload
                :max="10"
                :images="model.fileUrls"
                :uploadText="'上传图片'"
                :fileType="4"
                :bName="BName"
                :dir="Dir"
                :readOnly="false"
                :fileSize="50"
                @change="multiUploadChange"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
import { postAction, getAction } from '@/api/manage'

export default {
  name: 'UploadAttachmentsModal',
  data() {
    return {
      title: '上传附件',
      visible: false,
      confirmLoading: false,
      model: {
        fileUrls: []
      },
      rules: {
        // fileUrls: [
        //   { required: true, type: 'array', message: '请上传原件图片', trigger: 'change' }
        // ]
      },
      id: null,
      recordData: null,
      BName: window._CONFIG.AliOssConfig['AuthBucketName'],
      Dir: window._CONFIG.AliOssConfig['AuthDir'],
      httpHead: 'P36008',
      url: {
        attachmentsDetail: '/{v}/Order/GetSalesOrderAttachments',
        modifyAttachments: '/{v}/Order/ModifyAttachments'
      },
    }
  },
  methods: {
    /**
     * 显示原件变更弹窗
     * @param {Object} record - 需要变更的记录对象
     */
    show(record) {
      this.visible = true
      this.id = record.Id
      this.recordData = record
      this.confirmLoading = true
      
      // 获取已有的原件图片
      getAction(this.url.attachmentsDetail, { salesOrderId: this.id, isAudit: false }, this.httpHead)
        .then((res) => {
          if (res.IsSuccess) {
            let Attachments = []
            if (res.Data && res.Data.length) {
              res.Data.forEach(item => {
                Attachments.push(item.Url)
              })
            }
            this.model.fileUrls = Attachments
          } else {
            this.model.fileUrls = []
            this.$message.error(res.Msg || '获取原件图片失败')
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    /**
     * 处理多文件上传变更事件
     * @param {Array} images - 上传的图片文件数组
     */
    multiUploadChange(images) {
      this.model.fileUrls = images
    },
    /**
     * 处理确认按钮点击事件
     * 验证表单并提交原件变更请求
     */
    handleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading = true
          postAction(this.url.modifyAttachments, {
            SalesOrderId: this.id,
            IsAudit: false,
            NewAttachments: this.model.fileUrls
          }, this.httpHead).then((res) => {
            if (res.IsSuccess) {
              this.$message.success('上传附件成功')
              this.$emit('ok')
              this.handleCancel()
            } else {
              this.$message.error(res.Msg || '上传附件失败！')
            }
          })
          .finally(() => {
            this.confirmLoading = false
          })
        }
      })
    },
    /**
     * 处理取消按钮点击事件
     * 关闭弹窗并重置表单数据
     */
    handleCancel() {
      this.visible = false
      this.model = {
        fileUrls: []
      }
    }
  }
}
</script>