<template>
  <a-modal title="商品明细" :width="1100" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true" :footer="null">
    <a-spin :spinning="confirmLoading">
      <SimpleTable ref="table" :tab="tab" :isTableInitData="isTableInitData" :queryParam="queryParam" :columns="columns" :linkHttpHead="linkHttpHead" :linkUrlType="linkUrlType" :linkUrl="linkUrl">
        <span slot="commonContent" slot-scope="{ text }">
          <j-ellipsis :value="text ? `￥${Number(text).toFixed(2)}` : '--'" :length="15" />
        </span>
        <span slot="SellsTaxRate" slot-scope="{ text }">
          <j-ellipsis :value="text ? `${Number(text)}%` : '--'" :length="10" />
        </span>
      </SimpleTable>
    </a-spin>
  </a-modal>
</template>
<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { EditMixin } from '@/mixins/EditMixin'
import { deleteAction, getAction, putAction, postAction } from '@/api/manage'
export default {
  name: 'CommodityDetailListModal',
  components: { JEllipsis },
  mixins: [SimpleMixin, EditMixin],
  data() {
    return {
      confirmLoading: false,
      visible: false,
      tab: {
        sortArray: [],
        hideIpagination: true,
        sortArray:['SellsTaxRate'],
      },
      // 表头
      columns: [],
      columnsBase: [
        {
          title: '商品名称',
          dataIndex: 'ErpGoodsName',
          ellipsis: true,
          width: 150,
          fixed: 'left',
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '商品编号',
          dataIndex: 'ErpGoodsCode',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '规格',
          dataIndex: 'GoodsPackingSpecification',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '单位',
          dataIndex: 'GoodsPackageUnit',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '生产厂商',
          dataIndex: 'GoodsBrandManufacturer',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '件装量',
          dataIndex: 'GoodsPackageCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '销售税率',
          dataIndex: 'SellsTaxRate',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'SellsTaxRate' },
        },
        {
          title: '批号',
          dataIndex: 'GoodsBatchNumber',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
      ],
      AdjustPriceGoods: [
        {
          title: '有效期',
          dataIndex: 'GoodsPeriod',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '进价',
          dataIndex: 'GoodsPurchaseCostPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '销售单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '销售数量',
          dataIndex: 'Count',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调价数量',
          dataIndex: 'AdjustCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调价后单价',
          dataIndex: 'AdjustPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '差价小计',
          dataIndex: 'AdjustAmount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
      ],
      OutboundGoods: [
        {
          title: '有效期',
          dataIndex: 'GoodsPeriod',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '出库数量',
          dataIndex: 'OutboundCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '销售单价',
          dataIndex: 'OutboundPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '金额',
          dataIndex: 'SubtotalAmount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
      ],
      SaleAfterGoods: [
        {
          title: '进价',
          dataIndex: 'GoodsPurchaseCostPrice',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '销售单价',
          dataIndex: 'Price',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'commonSlot' },
        },
        {
          title: '出库数量',
          dataIndex: 'OutboundCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '退货数量',
          dataIndex: 'SaleAfterCount',
          ellipsis: true,
          width: 100,
          scopedSlots: { customRender: 'component' },
        },

        {
          title: '退款小计',
          dataIndex: 'GoodsRefundTotalAmount',
          ellipsis: true,
          width: 150,
          scopedSlots: { customRender: 'component' },
        },
      ],
      queryParam: {},
      isTableInitData: false, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '',
        getSalesOrderSaleAfterGoods: '/{v}/SaleAfterOrder/GetSalesOrderSaleAfterGoods', // 售后
        getSalesOrderAdjustPriceGoods: '/{v}/SalesOrderAdjustPrice/GetSalesOrderAdjustPriceGoods', // 调价
        getSalesOrderOutboundGoods: '/{v}/Order/GetSalesOrderOutboundGoods', // 出库
      },
    }
  },
  computed: {},
  mounted() { },
  created() { },
  methods: {
    show(data = {}) {
      // console.log(data)
      // 22 售后  23调价 20出库
      this.visible = true
      this.linkUrl.list = [22].includes(data.BillsType)
        ? this.linkUrl.getSalesOrderSaleAfterGoods
        : [23].includes(data.BillsType)
          ? this.linkUrl.getSalesOrderAdjustPriceGoods
          : this.linkUrl.getSalesOrderOutboundGoods
      this.columns = [22].includes(data.BillsType)
        ? [].concat(this.columnsBase).concat(this.SaleAfterGoods)
        : [23].includes(data.BillsType)
          ? [].concat(this.columnsBase).concat(this.AdjustPriceGoods)
          : [].concat(this.columnsBase).concat(this.OutboundGoods)
      this.queryParam = { billsNo: data['BillsNo'] || data['BillsCode'] }
      this.$nextTick(() => {
        if (this.$refs.table) this.$refs.table.loadDatas(1, this.queryParam)
      })
    },

    handleCancel() {
      this.linkUrl.list = ''
      this.columns = []
      this.visible = false
    },
  },
}
</script>
<style scoped></style>
