<!-- 含特回执列表 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @operate="operate" />
    <!-- 上传 | 重新上传 -->
    <ReceiptUploadModal ref="iReceiptUploadModal" @ok="modalOk" />
  </a-row>
</template>

<script>
import moment from 'moment'
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '含特回执列表',
  name: 'specialReturnReceiptIncludedList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择

        {
          name: '客户信息',
          type: 'input',
          value: '',
          key: 'CustomerKey',
          defaultVal: '',
          placeholder: '客户名称/编号/拼音码',
        },
        {
          name: '订单编号',
          type: 'input',
          value: '',
          key: 'SalesOrderCode',
          defaultVal: '',
          placeholder: '请输入',
        },
        { name: '创建人', type: 'input', value: '', key: 'CreateByName', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },
        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '同步状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        // {
        //   name: 'ERP登记状态',
        //   type: 'selectBasicLink',
        //   value: '',
        //   vModel: 'ERPRegisteStatus',
        //   dictCode: 'EnumERPRegisteStatus',
        //   defaultVal: '',
        //   placeholder: '请选择',
        // },
        {
          name: '七彩签章状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'CASignStatus',
          dictCode: 'EnumCASignStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        { name: '责任人', type: 'input', value: '', key: 'OwnerByName', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {},
      columns: [
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单编号',
          dataIndex: 'SalesOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单类型',
          dataIndex: 'SalesOrderTypeStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单金额（元）',
          dataIndex: 'TotalAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '下单时间',
          dataIndex: 'PlaceOrderTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        // {
        //   title: 'ERP登记',
        //   dataIndex: 'ERPRegisteStatusStr',
        //   width: 150,
        //   ellipsis: true,
        //   scopedSlots: { customRender: 'component' },
        // },
        {
          title: '七彩签章',
          dataIndex: 'CASignStatusStr',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 220,
          fixed: 'right',
          actionBtn: [
            {
              name: '上传',
              icon: '',
              id: 'ca6ab100-4d0f-4ab4-8fad-4b7c4e129b8e',
              specialShowFuc: (e) => {
                let record = e || {}
                return [0].includes(record.AuditStatus)
              },
            },
            {
              name: '重新上传',
              icon: '',
              id: 'c00fca13-01a3-4e4f-945b-c9001cb78fa1',
              specialShowFuc: (e) => {
                let record = e || {}
                return [3].includes(record.AuditStatus)
              },
            },
            {
              name: '详情',
              icon: '',
              id: '3e0ba81c-832e-4613-b3fb-2ae0c9701da7',
              specialShowFuc: (e) => {
                let record = e || {}
                return [1, 2].includes(record.AuditStatus)
              },
            },
            {
              name: '重新登记',
              icon: '',
              id: 'd77c258b-5393-49b5-95c0-5248554fb7d2',
              specialShowFuc: (e) => {
                let record = e || {}
                return [2].includes(record.AuditStatus) && [1, 3].includes(record.ERPRegisteStatus)
              },
            },
            {
              name: '重新签章',
              icon: '',
              id: '437831ac-3090-45bd-b7bd-39fbf9944ad9',
              specialShowFuc: (e) => {
                let record = e || {}
                return [2].includes(record.AuditStatus) && [2].includes(record.CASignStatus)
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: true, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/Order/QuerySalesOrderReceipts',
        pushSalesOrderReceiptToCASign: '/{v}/Order/PushSalesOrderReceiptToCASign',
        pushSalesOrderReceiptToERP: '/{v}/Order/PushSalesOrderReceiptToERP',
      },
    }
  },
  created() { },
  // mounted() {
  //   this.$refs.table.loadDatas(1, this.queryParam)
  // },
  methods: {
    moment,
    searchQuery(queryParam, type) {
      let params = JSON.parse(JSON.stringify(queryParam))
      // params['AuditStatus'] = Number(params['AuditStatus'])
      // params['AuthBusinessStatus'] = Number(params['AuthBusinessStatus'])
      this.$refs.table.loadDatas(1, params)
    },
    modalOk() {
      this.$refs.table.loadDatas(null, this.queryParam)
    },
    // 列表操作 invoiceApplyType 1蓝票 2红票 3作废 opType 1申请  2编辑
    operate(record, type) {
      const that = this
      switch (type) {
        case '上传':
          this.$refs.iReceiptUploadModal.show(record, null)
          break
        case '重新上传':
          this.$refs.iReceiptUploadModal.show(record, true)
          break
        case '详情':
          this.onDetailClick('specialReturnReceiptIncludedDetail', { id: record.Id })
          break
        case '重新登记':
          this.pushSalesOrderReceiptToERP(record.Id)

          break
        case '重新签章':
          this.pushSalesOrderReceiptToCASign(record.Id)
          break
      }
    },
    // 删除订单
    pushSalesOrderReceiptToCASign(Id) {
      if (!Id) return
      getAction(this.linkUrl.pushSalesOrderReceiptToCASign, { id: Id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(null, this.queryParam)
      })
    },
    // 删除订单
    pushSalesOrderReceiptToERP(Id) {
      if (!Id) return
      getAction(this.linkUrl.pushSalesOrderReceiptToERP, { id: Id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(null, this.queryParam)
      })
    },
  },
}
</script>

<style></style>
