<!-- 商销调价列表 -->
<template>
  <a-row>
    <!-- 搜索 -->
    <SimpleSearchArea ref="SimpleSearchArea" :searchInput="searchInput" @searchQuery="searchQuery" />
    <!-- 列表 -->
    <SimpleTable ref="table" :linkHttpHead="linkHttpHead" :linkUrl="linkUrl" :linkUrlType="linkUrlType" showTab cardBordered :tab="tab" :queryParam="queryParam" :columns="columns" :isTableInitData="isTableInitData" @onSetValid="(item, chceked) => onSetValid(item, chceked, 'POST', 'GoodsSpuId')" @operate="operate" @changeTab="changeTab" />
    <!-- 商销调价 -->
    <PricesAreAdjustedModal ref="iPricesAreAdjustedModal" @ok="searchQuery" />
  </a-row>
</template>

<script>
import { SimpleMixin } from '@/mixins/SimpleMixin'
import { getAction, putAction, postAction } from '@/api/manage'
export default {
  description: '商销调价列表',
  name: 'pricesAdjustedList',
  mixins: [SimpleMixin],
  data() {
    return {
      searchInput: [
        //type select 下拉筛选 input 输入框 timeInput 日期时间 selectLink 链接版搜索 selectInputLink 输入搜索链接版选择框 selectBaseLink 自由枚举读取数据的选择
        { name: '调价单号', type: 'input', value: '', key: 'AdjustPriceCode', defaultVal: '', placeholder: '请输入' },
        { name: '订单编号', type: 'input', value: '', key: 'SalesOrderCode', defaultVal: '', placeholder: '请输入' },
        { name: '客户信息', type: 'input', value: '', key: 'CustomerKey', defaultVal: '', placeholder: '请输入' },
        { name: '联系电话', type: 'input', value: '', key: 'LinkPhone', defaultVal: '', placeholder: '请输入' },
        {
          name: '创建时间',
          type: 'timeInput',
          value: '',
          key: ['CreateTimeBegin', 'CreateTimeEnd'],
          defaultVal: ['', ''],
          placeholder: ['开始日期', '结束日期'],
        },

        {
          name: '审核状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AuditStatus',
          dictCode: 'EnumAuditStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '结算状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AdjustPriceSettleStatus',
          dictCode: 'EnumSettlementStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '执行状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'AdjustPriceExecuteStatus',
          dictCode: 'EnumExecuteStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        {
          name: '同步状态',
          type: 'selectBasicLink',
          value: '',
          vModel: 'PushERPStatus',
          dictCode: 'EnumPushERPStatus',
          defaultVal: '',
          placeholder: '请选择',
        },
        { name: '责任人', type: 'input', value: '', key: 'OwnerByName', defaultVal: '', placeholder: '请输入' },
      ],

      tab: {
        bordered: false, //是否显示表格的边框
        operateBtn: [],
        hintArray: [],
        status: '',
        statusKey: '', //标签的切换关键字
        statusList: [],
      },
      columns: [
        {
          title: '调价单号',
          dataIndex: 'Code',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单编号',
          dataIndex: 'SalesOrderCode',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '客户名称',
          dataIndex: 'CustomerName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '付款方式',
          dataIndex: 'PaymentModeStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '订单金额(元)',
          dataIndex: 'SalesOrderTotalAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '调价金额（元）',
          dataIndex: 'AdjustAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '已结算金额（元）',
          dataIndex: 'SettledAmount',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建人',
          dataIndex: 'CreateByName',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '创建时间',
          dataIndex: 'CreateTime',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '责任人',
          dataIndex: 'OwnerByName',
          width: 120,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '审核状态',
          dataIndex: 'AuditStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '结算状态',
          dataIndex: 'AdjustPriceSettleStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '执行状态',
          dataIndex: 'AdjustPriceExecuteStatusStr',
          width: 100,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '同步状态',
          dataIndex: 'PushERPStatusStr',
          width: 150,
          ellipsis: true,
          scopedSlots: { customRender: 'component' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: 150,
          fixed: 'right',
          actionBtn: [
            {
              name: '详情',
              icon: '',
              id: '2be673c5-e495-4b42-b160-e27d4e7d649a',
            },
            {
              name: '编辑',
              icon: '',
              id: 'f423dd34-68d4-4a87-a8da-71a2aa93110a',
              specialShowFuc: (e) => {
                let record = e || {}
                return [0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '删除',
              icon: '',
              id: '8c9c70ae-e453-4718-bef8-67be3d0408ea',
              specialShowFuc: (e) => {
                let record = e || {}
                return [0, 3].includes(record.AuditStatus)
              },
            },
            {
              name: '撤回',
              icon: '',
              id: '8f2cf2d8-e2f2-4fc0-901b-d58f36b599f6',
              specialShowFuc: (e) => {
                let record = e || {}
                return [1].includes(record.AuditStatus)
              },
            },
          ],
          scopedSlots: { customRender: 'action' },
        },
      ],
      queryParam: {},
      isTableInitData: true, //是否自动加载
      linkHttpHead: 'P36008',
      linkUrlType: 'GET', //请求方式
      linkUrl: {
        list: '/{v}/SalesOrderAdjustPrice/SearchSalesOrderAdjustPrices',
        del: '/{v}/SalesOrderAdjustPrice/DeleteSalesOrderAdjustPrice',//删除调价单
        cancelOrder: '/{v}/SalesOrderAdjustPrice/RevokeAdjustPriceAudit',//撤回调价单
      },
    }
  },
  methods: {
    // 删除订单
    deleteOrder(Id) {
      if (!Id) return
      getAction(this.linkUrl.del, { id: Id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
      })
    },
    // 撤回
    cancelOrder(Id) {
      if (!Id) return
      putAction(this.linkUrl.cancelOrder, { id: Id }, this.linkHttpHead).then((res) => {
        if (!res.IsSuccess) {
          this.$message.warning(res.Msg)
          this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
          return
        }
        this.$message.success('操作成功!')
        this.$refs.table.loadDatas(1, this.$refs.SimpleSearchArea.queryParam)
      })
    },
    // 列表操作
    operate(record, type) {
      const that = this
      switch (type) {
        case '详情':
          this.onDetailClick('pricesAdjustedDetail', { id: record.Id, orderId: record.SalesOrderId })
          break
        case '编辑':
          let isEdit = true
          this.$refs.iPricesAreAdjustedModal.show(record, isEdit)
          break
        case '删除':
          this.$confirm({
            title: '删除调价单',
            content: '您确定要删除该笔调价单吗？',
            onOk() {
              that.deleteOrder(record.Id)
            },
            onCancel() { },
          })
          break
        case '撤回':
          this.$confirm({
            title: '撤回调价单',
            content: '您确定要撤回该笔调价单吗？',
            onOk() {
              that.cancelOrder(record.Id)
            },
            onCancel() { },
          })
          break
        default:
          break
      }
    },
  },
}
</script>

<style></style>
