<!-- 空白页 -->
<template>
  <div class="page-wrap">
    <div class="shadow" v-show="isShow">
      <img class="img-box" :src="shareImg" alt="" />
      <span class="close-btn" @click="changeShadow">我知道了</span>
    </div>
    <h2>{{ curType.title }}</h2>
    <p v-html="(curType.desc && curType.desc.replaceAll('\n', '<br/>')) || ''"></p>
  </div>
</template>

<script>
import { getAction, putAction, postAction, apiHead } from '@/api/manage'
import { wexinShare } from '@/utils/wexinShare'
export default {
  name: 'EmptyPage',
  data() {
    return {
      routerQuery: {},
      curType: {},
      isShow: true,
      imgUrl: require('@/assets/image/logo.png'),
      shareImg: require('@/assets/image/shareImg.png'),
      url: {
        shareResume: '/Base/{v}/WeiXin/GetWxJsApiSignInfo'
      }
    }
  },
  computed: {},
  mounted() {
    const typeList = {
      afterSale: {
        url: apiHead('P31300') + '/#/trading/GoodsOrders/AfterSaleDetail?id=' + this.routerQuery.AfterSaleId,
        title: this.routerQuery.ErpName + '售后单-' + this.routerQuery.AfterSaleNo, // 分享标题
        desc:
          '售后场景：' +
          this.routerQuery.SaleAfterSceneName +
          '\n售后类型：' +
          this.routerQuery.SaleAfterTypeStr +
          '\n主订单号：' +
          this.routerQuery.MainOrderNo
      },
      abnormalDeliveryRegistration: {
        url:
          apiHead('ERPWEBURL') +
          '/#/baseData/OrdersManagement/AbnormalDeliveryRegistrationListDetails?id=' +
          this.routerQuery.Id,
        title: this.routerQuery.ErpName + '订单发货异常-' + this.routerQuery.SplitOrderNo, // 分享标题
        desc: '主订单号：' + this.routerQuery.OrderNo + '\n异常描述：' + this.routerQuery.AbnormalDesperation
      },
      urgeDesperation: {
        url:
          apiHead('ERPWEBURL') + '/#/baseData/OrdersManagement/OrdersMusterModalInfoDetails?id=' + this.routerQuery.Id,
        title: this.routerQuery.ErpName + '订单催发-' + this.routerQuery.SplitOrderNo, // 分享标题
        desc: '主订单号：' + this.routerQuery.OrderNo + '\n催发描述：' + this.routerQuery.UrgeDesperation
      }
    }
    this.curType = typeList[this.routerQuery.type]
    this.weixin()
  },
  created() {
    this.routerQuery = this.$route.query
  },
  methods: {
    // 关闭遮罩
    changeShadow() {
      this.isShow = false
    },
    weixin() {
      //请求微信配置参数接口（获取签名），由后台给接口给
      //分享数据，这段主要是为了在hash模式下分享出去的链接不被浏览器截取，保证完全把链接分享出去
      getAction(this.url.shareResume, { pageUrl: this.curType.url }, 'P31200').then(res => {
        if (res.IsSuccess) {
          const shareData = {
            title: this.curType.title, // 分享标题
            desc: this.curType.desc, //分享描述
            imgUrl: this.imgUrl,
            link: this.curType.url
          }
          //微信加签
          const obj = {
            appId: res.Data.appId,
            nonceStr: res.Data.nonceStr,
            signature: res.Data.signature,
            timestamp: res.Data.timeStamp,
            jsApiList: [
              'updateAppMessageShareData',
              'updateTimelineShareData',
              'onMenuShareAppMessage',
              'onMenuShareTimeline'
            ]
          }
          //引用
          wexinShare(obj, shareData)
        } else {
          this.$message.error('获取sdk参数失败！')
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.page-wrap {
  padding: 16px;
  position: relative;
  .shadow {
    position: absolute;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    object-fit: contain;
    background-color: rgba(0, 0, 0, 0.5);
  }
  .img-box {
    width: 96%;
  }
  .close-btn {
    border: 2px #fff dashed;
    border-radius: 8px;
    color: #fff;
    text-align: center;
    display: block;
    margin: 60px auto 0;
    width: 50%;
    height: 40px;
    line-height: 38px;
    font-size: 18px;
    opacity: 0.7;
  }
}
</style>

