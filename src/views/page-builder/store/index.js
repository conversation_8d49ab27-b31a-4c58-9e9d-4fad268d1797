/**
 * 页面构建器 - Vuex Store 主入口
 * 集成所有页面构建器相关的状态管理模块
 */

import Vue from 'vue'
import Vuex from 'vuex'

// 导入页面构建器的状态管理模块
import designer from './modules/designer'
import components from './modules/components'
import pages from './modules/pages'

Vue.use(Vuex)

// 页面构建器专用的store实例
const pageBuilderStore = new Vuex.Store({
  namespaced: true,
  modules: {
    designer,
    components,
    pages
  },
  
  // 全局状态
  state: {
    // 页面构建器初始化状态
    initialized: false,
    // 当前激活的模块
    activeModule: 'designer',
    // 全局加载状态
    loading: false,
    // 全局错误信息
    error: null
  },

  // 全局getters
  getters: {
    // 是否已初始化
    isInitialized: state => state.initialized,
    
    // 当前激活模块
    currentModule: state => state.activeModule,
    
    // 是否正在加载
    isLoading: state => state.loading,
    
    // 错误信息
    errorMessage: state => state.error,
    
    // 获取所有模块的状态
    allModulesState: (state, getters, rootState) => ({
      designer: rootState.designer,
      components: rootState.components,
      pages: rootState.pages
    })
  },

  // 全局mutations
  mutations: {
    // 设置初始化状态
    SET_INITIALIZED(state, initialized) {
      state.initialized = initialized
    },
    
    // 设置激活模块
    SET_ACTIVE_MODULE(state, module) {
      state.activeModule = module
    },
    
    // 设置加载状态
    SET_LOADING(state, loading) {
      state.loading = loading
    },
    
    // 设置错误信息
    SET_ERROR(state, error) {
      state.error = error
    },
    
    // 清除错误
    CLEAR_ERROR(state) {
      state.error = null
    }
  },

  // 全局actions
  actions: {
    // 初始化页面构建器
    async initialize({ commit, dispatch }) {
      try {
        commit('SET_LOADING', true)
        commit('CLEAR_ERROR')
        
        // 初始化各个模块
        await Promise.all([
          dispatch('components/loadYYLComponents'),
          dispatch('designer/initializeDesigner'),
          dispatch('pages/loadPageList')
        ])
        
        commit('SET_INITIALIZED', true)
        console.log('页面构建器初始化完成')
        
      } catch (error) {
        console.error('页面构建器初始化失败:', error)
        commit('SET_ERROR', error.message || '初始化失败')
        throw error
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    // 切换激活模块
    switchModule({ commit }, module) {
      commit('SET_ACTIVE_MODULE', module)
    },
    
    // 重置所有状态
    async resetAll({ commit, dispatch }) {
      try {
        commit('SET_LOADING', true)
        
        // 重置各个模块
        await Promise.all([
          dispatch('designer/reset'),
          dispatch('components/reset'),
          dispatch('pages/reset')
        ])
        
        commit('SET_INITIALIZED', false)
        commit('SET_ACTIVE_MODULE', 'designer')
        commit('CLEAR_ERROR')
        
      } catch (error) {
        console.error('重置状态失败:', error)
        commit('SET_ERROR', error.message || '重置失败')
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    // 处理全局错误
    handleError({ commit }, error) {
      console.error('页面构建器错误:', error)
      commit('SET_ERROR', error.message || '未知错误')
    }
  },

  // 严格模式（开发环境）
  strict: process.env.NODE_ENV !== 'production'
})

// 导出store实例
export default pageBuilderStore

// 导出模块，供主应用集成使用
export const pageBuilderModules = {
  'pageBuilder/designer': designer,
  'pageBuilder/components': components,
  'pageBuilder/pages': pages
}

// 集成到主应用store的辅助函数
export function integrateToMainStore(mainStore) {
  // 动态注册页面构建器模块到主store
  if (mainStore && mainStore.registerModule) {
    mainStore.registerModule('pageBuilder', {
      namespaced: true,
      modules: {
        designer,
        components,
        pages
      },
      state: pageBuilderStore.state,
      getters: pageBuilderStore.getters,
      mutations: pageBuilderStore.mutations,
      actions: pageBuilderStore.actions
    })
    
    console.log('页面构建器模块已集成到主store')
    return true
  }
  
  console.warn('无法集成到主store，将使用独立store')
  return false
}

// 获取页面构建器store实例的辅助函数
export function getPageBuilderStore() {
  return pageBuilderStore
}

// 状态持久化配置
export const persistConfig = {
  key: 'pageBuilder',
  storage: 'localStorage',
  whitelist: [
    'designer.canvasSettings',
    'designer.recentComponents',
    'pages.recentPages'
  ]
}
