/**
 * 页面构建器 - 组件状态管理模块
 * 管理YYL组件库的加载、分类、搜索等功能
 */

import { YYL_COMPONENT_CONFIGS } from '../../configs/yyl-components'
import componentScanner from '../../utils/componentScanner'

const state = {
  // 组件库加载状态
  loaded: false,
  loading: false,
  
  // YYL组件配置
  yylComponents: {},
  
  // 组件分类
  categories: {
    business: [],
    form: [],
    table: [],
    components: []
  },
  
  // 搜索相关
  searchText: '',
  filteredComponents: {},
  
  // 组件使用统计
  usageStats: {},
  
  // 收藏的组件
  favoriteComponents: [],
  
  // 组件加载错误
  loadErrors: []
}

const getters = {
  // 是否已加载
  isLoaded: state => state.loaded,
  
  // 是否正在加载
  isLoading: state => state.loading,
  
  // 获取所有组件
  allComponents: state => state.yylComponents,
  
  // 按分类获取组件
  componentsByCategory: state => category => {
    return state.categories[category] || []
  },
  
  // 获取过滤后的组件
  getFilteredComponents: state => {
    if (!state.searchText) {
      return state.categories
    }
    return state.filteredComponents
  },
  
  // 获取组件配置
  getComponentConfig: state => componentType => {
    return state.yylComponents[componentType] || null
  },
  
  // 获取热门组件（按使用次数排序）
  popularComponents: state => {
    const components = Object.entries(state.usageStats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([type]) => state.yylComponents[type])
      .filter(Boolean)
    
    return components
  },
  
  // 获取收藏组件
  getFavoriteComponents: state => {
    return state.favoriteComponents
      .map(type => state.yylComponents[type])
      .filter(Boolean)
  },
  
  // 组件总数
  totalComponents: state => Object.keys(state.yylComponents).length,
  
  // 各分类组件数量
  categoryStats: state => {
    const stats = {}
    Object.keys(state.categories).forEach(category => {
      stats[category] = state.categories[category].length
    })
    return stats
  }
}

const mutations = {
  // 设置加载状态
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  // 设置已加载状态
  SET_LOADED(state, loaded) {
    state.loaded = loaded
  },
  
  // 设置YYL组件配置
  SET_YYL_COMPONENTS(state, components) {
    state.yylComponents = components
  },
  
  // 设置组件分类
  SET_CATEGORIES(state, categories) {
    state.categories = categories
  },
  
  // 设置搜索文本
  SET_SEARCH_TEXT(state, text) {
    state.searchText = text
  },
  
  // 设置过滤后的组件
  SET_FILTERED_COMPONENTS(state, components) {
    state.filteredComponents = components
  },
  
  // 增加组件使用次数
  INCREMENT_USAGE(state, componentType) {
    state.usageStats[componentType] = (state.usageStats[componentType] || 0) + 1
  },
  
  // 添加收藏组件
  ADD_FAVORITE(state, componentType) {
    if (!state.favoriteComponents.includes(componentType)) {
      state.favoriteComponents.push(componentType)
    }
  },
  
  // 移除收藏组件
  REMOVE_FAVORITE(state, componentType) {
    const index = state.favoriteComponents.indexOf(componentType)
    if (index !== -1) {
      state.favoriteComponents.splice(index, 1)
    }
  },
  
  // 添加加载错误
  ADD_LOAD_ERROR(state, error) {
    state.loadErrors.push({
      message: error.message || error,
      timestamp: Date.now()
    })
  },
  
  // 清除加载错误
  CLEAR_LOAD_ERRORS(state) {
    state.loadErrors = []
  },
  
  // 重置状态
  RESET_STATE(state) {
    state.loaded = false
    state.loading = false
    state.yylComponents = {}
    state.categories = {
      business: [],
      form: [],
      table: [],
      components: []
    }
    state.searchText = ''
    state.filteredComponents = {}
    state.loadErrors = []
  }
}

const actions = {
  // 加载YYL组件
  async loadYYLComponents({ commit, dispatch }) {
    try {
      commit('SET_LOADING', true)
      commit('CLEAR_LOAD_ERRORS')
      
      // 首先尝试使用预配置的组件
      let components = { ...YYL_COMPONENT_CONFIGS }
      
      // 尝试动态扫描组件（如果扫描器可用）
      try {
        const scannedComponents = await componentScanner.scanAllComponents()
        if (scannedComponents && Object.keys(scannedComponents).length > 0) {
          // 合并扫描结果和预配置
          components = { ...components, ...scannedComponents }
          console.log('动态扫描组件成功，共发现', Object.keys(scannedComponents).length, '个组件')
        }
      } catch (scanError) {
        console.warn('动态扫描组件失败，使用预配置组件:', scanError.message)
        commit('ADD_LOAD_ERROR', `动态扫描失败: ${scanError.message}`)
      }
      
      // 按分类整理组件
      const categories = {
        business: [],
        form: [],
        table: [],
        components: []
      }
      
      Object.entries(components).forEach(([name, config]) => {
        const componentData = { name, ...config }
        const category = config.category || 'components'
        if (categories[category]) {
          categories[category].push(componentData)
        }
      })
      
      commit('SET_YYL_COMPONENTS', components)
      commit('SET_CATEGORIES', categories)
      commit('SET_LOADED', true)
      
      console.log('YYL组件加载完成，共', Object.keys(components).length, '个组件')
      
    } catch (error) {
      console.error('加载YYL组件失败:', error)
      commit('ADD_LOAD_ERROR', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 搜索组件
  searchComponents({ commit, state }, searchText) {
    commit('SET_SEARCH_TEXT', searchText)
    
    if (!searchText.trim()) {
      commit('SET_FILTERED_COMPONENTS', {})
      return
    }
    
    const searchLower = searchText.toLowerCase()
    const filtered = {
      business: [],
      form: [],
      table: [],
      components: []
    }
    
    Object.keys(state.categories).forEach(category => {
      filtered[category] = state.categories[category].filter(component => {
        return component.displayName.toLowerCase().includes(searchLower) ||
               component.description.toLowerCase().includes(searchLower) ||
               component.name.toLowerCase().includes(searchLower)
      })
    })
    
    commit('SET_FILTERED_COMPONENTS', filtered)
  },
  
  // 记录组件使用
  recordComponentUsage({ commit }, componentType) {
    commit('INCREMENT_USAGE', componentType)
  },
  
  // 切换收藏状态
  toggleFavorite({ commit, state }, componentType) {
    if (state.favoriteComponents.includes(componentType)) {
      commit('REMOVE_FAVORITE', componentType)
    } else {
      commit('ADD_FAVORITE', componentType)
    }
  },
  
  // 重新加载组件
  async reloadComponents({ dispatch }) {
    await dispatch('reset')
    await dispatch('loadYYLComponents')
  },
  
  // 重置组件状态
  reset({ commit }) {
    commit('RESET_STATE')
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
