/**
 * 页面构建器 - 页面状态管理模块
 * 管理页面的保存、加载、列表等功能
 */

const state = {
  // 页面列表
  pageList: [],
  
  // 当前页面信息
  currentPage: {
    id: null,
    name: '未命名页面',
    description: '',
    components: [],
    settings: {
      width: 1200,
      height: 800,
      backgroundColor: '#ffffff'
    },
    createTime: null,
    updateTime: null,
    version: 1
  },
  
  // 页面加载状态
  loading: false,
  saving: false,
  
  // 最近打开的页面
  recentPages: [],
  maxRecentPages: 10,
  
  // 页面模板
  templates: [],
  templatesLoaded: false,
  
  // 页面操作历史
  operationHistory: [],
  
  // 错误信息
  errors: []
}

const getters = {
  // 获取所有页面
  allPages: state => state.pageList,
  
  // 获取当前页面
  getCurrentPage: state => state.currentPage,
  
  // 是否有未保存的更改
  hasUnsavedChanges: state => {
    // 这里可以通过比较当前状态和最后保存状态来判断
    return false // 暂时返回false，后续可以完善
  },
  
  // 是否正在加载
  isLoading: state => state.loading,
  
  // 是否正在保存
  isSaving: state => state.saving,
  
  // 获取最近页面
  getRecentPages: state => {
    return state.recentPages
      .map(pageId => state.pageList.find(page => page.id === pageId))
      .filter(Boolean)
  },
  
  // 获取页面模板
  getTemplates: state => state.templates,
  
  // 页面统计
  pageStats: state => ({
    total: state.pageList.length,
    recent: state.recentPages.length,
    templates: state.templates.length
  }),
  
  // 按创建时间排序的页面
  pagesByCreateTime: state => {
    return [...state.pageList].sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
  },
  
  // 按更新时间排序的页面
  pagesByUpdateTime: state => {
    return [...state.pageList].sort((a, b) => new Date(b.updateTime) - new Date(a.updateTime))
  }
}

const mutations = {
  // 设置页面列表
  SET_PAGE_LIST(state, pages) {
    state.pageList = pages
  },
  
  // 添加页面
  ADD_PAGE(state, page) {
    state.pageList.push(page)
  },
  
  // 更新页面
  UPDATE_PAGE(state, { id, updates }) {
    const index = state.pageList.findIndex(page => page.id === id)
    if (index !== -1) {
      state.pageList.splice(index, 1, { ...state.pageList[index], ...updates })
    }
  },
  
  // 删除页面
  REMOVE_PAGE(state, id) {
    const index = state.pageList.findIndex(page => page.id === id)
    if (index !== -1) {
      state.pageList.splice(index, 1)
    }
    // 从最近页面中移除
    const recentIndex = state.recentPages.indexOf(id)
    if (recentIndex !== -1) {
      state.recentPages.splice(recentIndex, 1)
    }
  },
  
  // 设置当前页面
  SET_CURRENT_PAGE(state, page) {
    state.currentPage = { ...state.currentPage, ...page }
  },
  
  // 更新当前页面
  UPDATE_CURRENT_PAGE(state, updates) {
    state.currentPage = { ...state.currentPage, ...updates }
  },
  
  // 设置加载状态
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  // 设置保存状态
  SET_SAVING(state, saving) {
    state.saving = saving
  },
  
  // 添加到最近页面
  ADD_RECENT_PAGE(state, pageId) {
    // 移除已存在的相同页面
    state.recentPages = state.recentPages.filter(id => id !== pageId)
    // 添加到开头
    state.recentPages.unshift(pageId)
    // 限制数量
    if (state.recentPages.length > state.maxRecentPages) {
      state.recentPages = state.recentPages.slice(0, state.maxRecentPages)
    }
  },
  
  // 设置页面模板
  SET_TEMPLATES(state, templates) {
    state.templates = templates
    state.templatesLoaded = true
  },
  
  // 添加操作历史
  ADD_OPERATION_HISTORY(state, operation) {
    state.operationHistory.unshift({
      ...operation,
      timestamp: Date.now()
    })
    // 限制历史记录数量
    if (state.operationHistory.length > 100) {
      state.operationHistory = state.operationHistory.slice(0, 100)
    }
  },
  
  // 添加错误
  ADD_ERROR(state, error) {
    state.errors.push({
      message: error.message || error,
      timestamp: Date.now()
    })
  },
  
  // 清除错误
  CLEAR_ERRORS(state) {
    state.errors = []
  },
  
  // 重置状态
  RESET_STATE(state) {
    state.pageList = []
    state.currentPage = {
      id: null,
      name: '未命名页面',
      description: '',
      components: [],
      settings: {
        width: 1200,
        height: 800,
        backgroundColor: '#ffffff'
      },
      createTime: null,
      updateTime: null,
      version: 1
    }
    state.loading = false
    state.saving = false
    state.recentPages = []
    state.templates = []
    state.templatesLoaded = false
    state.operationHistory = []
    state.errors = []
  }
}

const actions = {
  // 生成页面ID
  generatePageId() {
    return 'page_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  },
  
  // 加载页面列表
  async loadPageList({ commit }) {
    try {
      commit('SET_LOADING', true)
      commit('CLEAR_ERRORS')
      
      // 这里应该调用API获取页面列表
      // 暂时使用模拟数据
      const mockPages = [
        {
          id: 'page_demo_1',
          name: '示例页面1',
          description: '这是一个示例页面',
          components: [],
          settings: { width: 1200, height: 800, backgroundColor: '#ffffff' },
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString(),
          version: 1
        }
      ]
      
      commit('SET_PAGE_LIST', mockPages)
      console.log('页面列表加载完成')
      
    } catch (error) {
      console.error('加载页面列表失败:', error)
      commit('ADD_ERROR', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 创建新页面
  async createPage({ commit, dispatch }, { name, description, template }) {
    try {
      commit('SET_SAVING', true)
      
      const pageId = dispatch('generatePageId')
      const now = new Date().toISOString()
      
      const newPage = {
        id: pageId,
        name: name || '未命名页面',
        description: description || '',
        components: template ? template.components : [],
        settings: template ? template.settings : {
          width: 1200,
          height: 800,
          backgroundColor: '#ffffff'
        },
        createTime: now,
        updateTime: now,
        version: 1
      }
      
      // 这里应该调用API保存页面
      // await api.createPage(newPage)
      
      commit('ADD_PAGE', newPage)
      commit('SET_CURRENT_PAGE', newPage)
      commit('ADD_RECENT_PAGE', pageId)
      commit('ADD_OPERATION_HISTORY', {
        type: 'create',
        pageId,
        pageName: newPage.name
      })
      
      console.log('页面创建成功:', newPage.name)
      return newPage
      
    } catch (error) {
      console.error('创建页面失败:', error)
      commit('ADD_ERROR', error)
      throw error
    } finally {
      commit('SET_SAVING', false)
    }
  },
  
  // 保存页面
  async savePage({ commit, state }, { components, settings }) {
    try {
      commit('SET_SAVING', true)
      
      const updates = {
        components: components || state.currentPage.components,
        settings: settings || state.currentPage.settings,
        updateTime: new Date().toISOString(),
        version: state.currentPage.version + 1
      }
      
      // 这里应该调用API保存页面
      // await api.updatePage(state.currentPage.id, updates)
      
      commit('UPDATE_CURRENT_PAGE', updates)
      commit('UPDATE_PAGE', { id: state.currentPage.id, updates })
      commit('ADD_OPERATION_HISTORY', {
        type: 'save',
        pageId: state.currentPage.id,
        pageName: state.currentPage.name
      })
      
      console.log('页面保存成功')
      
    } catch (error) {
      console.error('保存页面失败:', error)
      commit('ADD_ERROR', error)
      throw error
    } finally {
      commit('SET_SAVING', false)
    }
  },
  
  // 加载页面
  async loadPage({ commit }, pageId) {
    try {
      commit('SET_LOADING', true)
      
      // 这里应该调用API获取页面详情
      // const page = await api.getPage(pageId)
      
      // 暂时从页面列表中查找
      const page = state.pageList.find(p => p.id === pageId)
      if (!page) {
        throw new Error('页面不存在')
      }
      
      commit('SET_CURRENT_PAGE', page)
      commit('ADD_RECENT_PAGE', pageId)
      commit('ADD_OPERATION_HISTORY', {
        type: 'load',
        pageId,
        pageName: page.name
      })
      
      console.log('页面加载成功:', page.name)
      return page
      
    } catch (error) {
      console.error('加载页面失败:', error)
      commit('ADD_ERROR', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 删除页面
  async deletePage({ commit }, pageId) {
    try {
      // 这里应该调用API删除页面
      // await api.deletePage(pageId)
      
      const page = state.pageList.find(p => p.id === pageId)
      commit('REMOVE_PAGE', pageId)
      commit('ADD_OPERATION_HISTORY', {
        type: 'delete',
        pageId,
        pageName: page ? page.name : '未知页面'
      })
      
      console.log('页面删除成功')
      
    } catch (error) {
      console.error('删除页面失败:', error)
      commit('ADD_ERROR', error)
      throw error
    }
  },
  
  // 加载页面模板
  async loadTemplates({ commit }) {
    try {
      // 这里应该调用API获取模板列表
      // const templates = await api.getTemplates()
      
      // 暂时使用模拟数据
      const mockTemplates = [
        {
          id: 'template_1',
          name: '基础表单页面',
          description: '包含常用表单组件的页面模板',
          thumbnail: '',
          components: [],
          settings: { width: 1200, height: 800, backgroundColor: '#ffffff' }
        }
      ]
      
      commit('SET_TEMPLATES', mockTemplates)
      console.log('页面模板加载完成')
      
    } catch (error) {
      console.error('加载页面模板失败:', error)
      commit('ADD_ERROR', error)
    }
  },
  
  // 重置页面状态
  reset({ commit }) {
    commit('RESET_STATE')
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
