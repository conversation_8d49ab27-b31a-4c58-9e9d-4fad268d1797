/**
 * 页面构建器 - 设计器状态管理模块
 * 管理设计器的界面状态、画布状态、选中状态等
 */

// 默认画布设置
const defaultCanvasSettings = {
  width: 1200,
  height: 800,
  backgroundColor: '#ffffff',
  showGrid: true,
  showRuler: false,
  gridSize: 10,
  snapToGrid: true,
  zoomLevel: 1
}

// 默认工具栏设置
const defaultToolbarSettings = {
  showComponentPanel: true,
  showPropertyPanel: false,
  showLayerPanel: false,
  showOutlinePanel: false
}

const state = {
  // 设计器初始化状态
  initialized: false,
  
  // 当前页面组件列表
  pageComponents: [],
  
  // 选中的组件ID
  selectedComponentId: null,
  
  // 悬停的组件ID
  hoverComponentId: null,
  
  // 剪贴板组件
  clipboardComponent: null,
  
  // 历史记录
  history: [],
  historyIndex: -1,
  maxHistorySize: 50,
  
  // 画布设置
  canvasSettings: { ...defaultCanvasSettings },
  
  // 工具栏设置
  toolbarSettings: { ...defaultToolbarSettings },
  
  // 拖拽状态
  dragState: {
    isDragging: false,
    dragType: null, // 'component' | 'move' | 'resize'
    dragData: null,
    startPosition: null,
    currentPosition: null
  },
  
  // 设计器模式
  mode: 'design', // 'design' | 'preview' | 'code'
  
  // 最近使用的组件
  recentComponents: [],
  maxRecentComponents: 10,
  
  // 设计器设置
  designerSettings: {
    autoSave: true,
    autoSaveInterval: 30000, // 30秒
    showComponentBounds: true,
    showComponentNames: false,
    enableKeyboardShortcuts: true
  }
}

const getters = {
  // 是否已初始化
  isInitialized: state => state.initialized,
  
  // 获取所有页面组件
  allComponents: state => state.pageComponents,
  
  // 获取选中的组件
  selectedComponent: state => {
    if (!state.selectedComponentId) return null
    return state.pageComponents.find(comp => comp.id === state.selectedComponentId)
  },
  
  // 获取悬停的组件
  hoverComponent: state => {
    if (!state.hoverComponentId) return null
    return state.pageComponents.find(comp => comp.id === state.hoverComponentId)
  },
  
  // 是否可以撤销
  canUndo: state => state.historyIndex > 0,
  
  // 是否可以重做
  canRedo: state => state.historyIndex < state.history.length - 1,
  
  // 是否有剪贴板内容
  hasClipboard: state => state.clipboardComponent !== null,
  
  // 是否正在拖拽
  isDragging: state => state.dragState.isDragging,
  
  // 获取画布样式
  canvasStyle: state => ({
    width: `${state.canvasSettings.width}px`,
    height: `${state.canvasSettings.height}px`,
    backgroundColor: state.canvasSettings.backgroundColor,
    transform: `scale(${state.canvasSettings.zoomLevel})`,
    transformOrigin: 'top left'
  }),
  
  // 获取网格样式
  gridStyle: state => {
    if (!state.canvasSettings.showGrid) return {}
    return {
      backgroundSize: `${state.canvasSettings.gridSize}px ${state.canvasSettings.gridSize}px`,
      backgroundImage: `
        linear-gradient(to right, #e8e8e8 1px, transparent 1px),
        linear-gradient(to bottom, #e8e8e8 1px, transparent 1px)
      `
    }
  },
  
  // 组件数量统计
  componentStats: state => {
    const stats = {}
    state.pageComponents.forEach(comp => {
      stats[comp.type] = (stats[comp.type] || 0) + 1
    })
    return stats
  }
}

const mutations = {
  // 设置初始化状态
  SET_INITIALIZED(state, initialized) {
    state.initialized = initialized
  },
  
  // 设置页面组件列表
  SET_PAGE_COMPONENTS(state, components) {
    state.pageComponents = components
  },
  
  // 添加组件
  ADD_COMPONENT(state, component) {
    state.pageComponents.push(component)
  },
  
  // 更新组件
  UPDATE_COMPONENT(state, { id, updates }) {
    const index = state.pageComponents.findIndex(comp => comp.id === id)
    if (index !== -1) {
      state.pageComponents.splice(index, 1, { ...state.pageComponents[index], ...updates })
    }
  },
  
  // 删除组件
  REMOVE_COMPONENT(state, id) {
    const index = state.pageComponents.findIndex(comp => comp.id === id)
    if (index !== -1) {
      state.pageComponents.splice(index, 1)
    }
    // 如果删除的是选中组件，清除选中状态
    if (state.selectedComponentId === id) {
      state.selectedComponentId = null
    }
  },
  
  // 设置选中组件
  SET_SELECTED_COMPONENT(state, id) {
    state.selectedComponentId = id
  },
  
  // 设置悬停组件
  SET_HOVER_COMPONENT(state, id) {
    state.hoverComponentId = id
  },
  
  // 设置剪贴板组件
  SET_CLIPBOARD_COMPONENT(state, component) {
    state.clipboardComponent = component
  },
  
  // 保存到历史记录
  SAVE_TO_HISTORY(state) {
    const currentState = {
      components: JSON.parse(JSON.stringify(state.pageComponents)),
      selectedComponentId: state.selectedComponentId,
      timestamp: Date.now()
    }
    
    // 删除当前位置之后的历史记录
    state.history = state.history.slice(0, state.historyIndex + 1)
    state.history.push(currentState)
    state.historyIndex = state.history.length - 1
    
    // 限制历史记录数量
    if (state.history.length > state.maxHistorySize) {
      state.history.shift()
      state.historyIndex--
    }
  },
  
  // 撤销
  UNDO(state) {
    if (state.historyIndex > 0) {
      state.historyIndex--
      const historyState = state.history[state.historyIndex]
      state.pageComponents = JSON.parse(JSON.stringify(historyState.components))
      state.selectedComponentId = historyState.selectedComponentId
    }
  },
  
  // 重做
  REDO(state) {
    if (state.historyIndex < state.history.length - 1) {
      state.historyIndex++
      const historyState = state.history[state.historyIndex]
      state.pageComponents = JSON.parse(JSON.stringify(historyState.components))
      state.selectedComponentId = historyState.selectedComponentId
    }
  },
  
  // 更新画布设置
  UPDATE_CANVAS_SETTINGS(state, settings) {
    state.canvasSettings = { ...state.canvasSettings, ...settings }
  },
  
  // 更新工具栏设置
  UPDATE_TOOLBAR_SETTINGS(state, settings) {
    state.toolbarSettings = { ...state.toolbarSettings, ...settings }
  },
  
  // 设置拖拽状态
  SET_DRAG_STATE(state, dragState) {
    state.dragState = { ...state.dragState, ...dragState }
  },
  
  // 设置设计器模式
  SET_MODE(state, mode) {
    state.mode = mode
  },
  
  // 添加到最近使用组件
  ADD_RECENT_COMPONENT(state, componentType) {
    // 移除已存在的相同组件类型
    state.recentComponents = state.recentComponents.filter(type => type !== componentType)
    // 添加到开头
    state.recentComponents.unshift(componentType)
    // 限制数量
    if (state.recentComponents.length > state.maxRecentComponents) {
      state.recentComponents = state.recentComponents.slice(0, state.maxRecentComponents)
    }
  },
  
  // 更新设计器设置
  UPDATE_DESIGNER_SETTINGS(state, settings) {
    state.designerSettings = { ...state.designerSettings, ...settings }
  },
  
  // 重置状态
  RESET_STATE(state) {
    state.pageComponents = []
    state.selectedComponentId = null
    state.hoverComponentId = null
    state.clipboardComponent = null
    state.history = []
    state.historyIndex = -1
    state.canvasSettings = { ...defaultCanvasSettings }
    state.toolbarSettings = { ...defaultToolbarSettings }
    state.dragState = {
      isDragging: false,
      dragType: null,
      dragData: null,
      startPosition: null,
      currentPosition: null
    }
    state.mode = 'design'
    state.recentComponents = []
  }
}

const actions = {
  // 初始化设计器
  async initializeDesigner({ commit }) {
    try {
      // 初始化历史记录
      commit('SAVE_TO_HISTORY')
      commit('SET_INITIALIZED', true)
      console.log('设计器初始化完成')
    } catch (error) {
      console.error('设计器初始化失败:', error)
      throw error
    }
  },
  
  // 生成唯一ID
  generateId() {
    return 'comp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  },
  
  // 添加组件
  addComponent({ commit, dispatch }, { componentType, componentConfig, position }) {
    const id = dispatch('generateId')
    const newComponent = {
      id,
      type: componentType,
      props: { ...componentConfig.defaultProps },
      style: { ...componentConfig.style },
      position: position || { x: 100, y: 100 },
      createTime: Date.now()
    }
    
    commit('ADD_COMPONENT', newComponent)
    commit('SET_SELECTED_COMPONENT', id)
    commit('ADD_RECENT_COMPONENT', componentType)
    commit('SAVE_TO_HISTORY')
    
    return newComponent
  },
  
  // 更新组件
  updateComponent({ commit }, { id, updates }) {
    commit('UPDATE_COMPONENT', { id, updates })
    commit('SAVE_TO_HISTORY')
  },
  
  // 删除组件
  deleteComponent({ commit }, id) {
    commit('REMOVE_COMPONENT', id)
    commit('SAVE_TO_HISTORY')
  },
  
  // 复制组件
  copyComponent({ commit, state }) {
    if (state.selectedComponentId) {
      const component = state.pageComponents.find(comp => comp.id === state.selectedComponentId)
      if (component) {
        commit('SET_CLIPBOARD_COMPONENT', { ...component })
      }
    }
  },
  
  // 粘贴组件
  pasteComponent({ commit, dispatch, state }) {
    if (state.clipboardComponent) {
      const id = dispatch('generateId')
      const newComponent = {
        ...state.clipboardComponent,
        id,
        position: {
          x: (state.clipboardComponent.position.x || 0) + 20,
          y: (state.clipboardComponent.position.y || 0) + 20
        },
        createTime: Date.now()
      }
      
      commit('ADD_COMPONENT', newComponent)
      commit('SET_SELECTED_COMPONENT', id)
      commit('SAVE_TO_HISTORY')
      
      return newComponent
    }
  },
  
  // 撤销操作
  undo({ commit, getters }) {
    if (getters.canUndo) {
      commit('UNDO')
    }
  },
  
  // 重做操作
  redo({ commit, getters }) {
    if (getters.canRedo) {
      commit('REDO')
    }
  },
  
  // 清空画布
  clearCanvas({ commit }) {
    commit('SET_PAGE_COMPONENTS', [])
    commit('SET_SELECTED_COMPONENT', null)
    commit('SAVE_TO_HISTORY')
  },
  
  // 重置设计器
  reset({ commit }) {
    commit('RESET_STATE')
    commit('SET_INITIALIZED', false)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
