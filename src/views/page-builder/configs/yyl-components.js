/**
 * YYL组件配置
 * 为页面构建器提供YYL组件的详细配置信息
 */

// YYL组件的详细配置模板
export const YYL_COMPONENT_CONFIGS = {
  // 业务组件配置
  YYLButton: {
    displayName: 'YYL按钮',
    category: 'business',
    icon: 'button',
    description: '带权限控制的按钮组件',
    defaultProps: {
      text: '按钮',
      type: 'default',
      size: 'default',
      menuId: '',
      loading: false,
      disabled: false,
      addHeader: true
    },
    propSchema: {
      text: { 
        type: 'string', 
        label: '按钮文本', 
        required: true,
        placeholder: '请输入按钮文本'
      },
      menuId: { 
        type: 'string', 
        label: '权限ID', 
        required: true,
        placeholder: '请输入权限菜单ID',
        description: '用于权限控制的菜单ID'
      },
      type: { 
        type: 'select', 
        label: '按钮类型',
        options: [
          { label: '默认', value: 'default' },
          { label: '主要', value: 'primary' },
          { label: '虚线', value: 'dashed' },
          { label: '危险', value: 'danger' },
          { label: '链接', value: 'link' }
        ]
      },
      size: {
        type: 'select',
        label: '按钮尺寸',
        options: [
          { label: '小', value: 'small' },
          { label: '默认', value: 'default' },
          { label: '大', value: 'large' }
        ]
      },
      icon: { 
        type: 'string', 
        label: '图标',
        placeholder: '请输入图标名称'
      },
      loading: { 
        type: 'boolean', 
        label: '加载状态' 
      },
      disabled: { 
        type: 'boolean', 
        label: '禁用状态' 
      },
      addHeader: {
        type: 'boolean',
        label: '传递权限参数',
        description: '是否把menuId作为参数传给后端'
      }
    },
    events: {
      click: { 
        label: '点击事件', 
        params: [] 
      }
    },
    permissions: {
      required: true,
      field: 'menuId'
    },
    style: {
      margin: '8px',
      display: 'inline-block'
    }
  },

  YYLForm: {
    displayName: 'YYL表单',
    category: 'form',
    icon: 'form',
    description: '动态表单组件，支持多种表单控件',
    defaultProps: {
      formFields: [],
      layout: 'horizontal',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      disabled: false,
      isSearchMode: false
    },
    propSchema: {
      formFields: { 
        type: 'array', 
        label: '表单字段',
        editor: 'FormFieldsEditor',
        description: '表单字段配置数组'
      },
      layout: {
        type: 'select',
        label: '表单布局',
        options: [
          { label: '水平', value: 'horizontal' },
          { label: '垂直', value: 'vertical' },
          { label: '行内', value: 'inline' }
        ]
      },
      labelCol: {
        type: 'object',
        label: '标签列配置',
        editor: 'ColEditor',
        description: '标签列的栅格配置'
      },
      wrapperCol: {
        type: 'object',
        label: '控件列配置',
        editor: 'ColEditor',
        description: '控件列的栅格配置'
      },
      disabled: { 
        type: 'boolean', 
        label: '禁用状态' 
      },
      isSearchMode: {
        type: 'boolean',
        label: '搜索模式',
        description: '是否为搜索表单模式'
      }
    },
    events: {
      submit: { 
        label: '提交事件', 
        params: ['formData'] 
      },
      change: { 
        label: '值变化事件', 
        params: ['field', 'value'] 
      },
      validate: {
        label: '验证事件',
        params: ['valid', 'errors']
      }
    },
    style: {
      width: '100%',
      padding: '16px'
    }
  },

  YYLTable: {
    displayName: 'YYL表格',
    category: 'table',
    icon: 'table',
    description: '动态表格组件，支持分页和操作按钮',
    defaultProps: {
      columns: [],
      tableData: [],
      loading: false,
      pagination: { current: 1, pageSize: 10, total: 0 },
      rowSelection: null,
      scrollX: 0,
      isShowPagination: true,
      rowKey: 'id'
    },
    propSchema: {
      columns: {
        type: 'array',
        label: '表格列配置',
        editor: 'TableColumnsEditor',
        description: '表格列的配置数组'
      },
      tableData: {
        type: 'array',
        label: '表格数据',
        dataBinding: true,
        description: '表格显示的数据数组'
      },
      loading: { 
        type: 'boolean', 
        label: '加载状态' 
      },
      pagination: {
        type: 'object',
        label: '分页配置',
        editor: 'PaginationEditor',
        description: '分页相关配置'
      },
      rowSelection: {
        type: 'object',
        label: '行选择配置',
        editor: 'RowSelectionEditor',
        description: '行选择功能配置'
      },
      scrollX: { 
        type: 'number', 
        label: '横向滚动宽度',
        description: '表格横向滚动的宽度'
      },
      isShowPagination: {
        type: 'boolean',
        label: '显示分页',
        description: '是否显示分页组件'
      },
      rowKey: {
        type: 'string',
        label: '行键',
        description: '表格行的唯一标识字段'
      }
    },
    events: {
      change: { 
        label: '表格变化事件', 
        params: ['pagination', 'filters', 'sorter'] 
      },
      rowClick: { 
        label: '行点击事件', 
        params: ['record', 'index'] 
      },
      select: {
        label: '行选择事件',
        params: ['selectedRows']
      }
    },
    style: {
      width: '100%'
    }
  },

  YYLDetails: {
    displayName: 'YYL详情',
    category: 'form',
    icon: 'profile',
    description: '详情展示组件',
    defaultProps: {
      detailFields: [],
      layout: 'horizontal',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 }
    },
    propSchema: {
      detailFields: {
        type: 'array',
        label: '详情字段',
        editor: 'DetailFieldsEditor',
        description: '详情字段配置数组'
      },
      layout: {
        type: 'select',
        label: '布局方式',
        options: [
          { label: '水平', value: 'horizontal' },
          { label: '垂直', value: 'vertical' }
        ]
      },
      labelCol: {
        type: 'object',
        label: '标签列配置',
        editor: 'ColEditor'
      },
      wrapperCol: {
        type: 'object',
        label: '内容列配置',
        editor: 'ColEditor'
      }
    },
    events: {},
    style: {
      width: '100%',
      padding: '16px'
    }
  },

  ImageControl: {
    displayName: '图片控制',
    category: 'components',
    icon: 'picture',
    description: '图片控制和展示组件',
    defaultProps: {
      src: '',
      width: 100,
      height: 100,
      preview: true,
      fallback: ''
    },
    propSchema: {
      src: {
        type: 'string',
        label: '图片地址',
        placeholder: '请输入图片URL'
      },
      width: {
        type: 'number',
        label: '宽度'
      },
      height: {
        type: 'number',
        label: '高度'
      },
      preview: {
        type: 'boolean',
        label: '支持预览'
      },
      fallback: {
        type: 'string',
        label: '加载失败图片',
        placeholder: '请输入备用图片URL'
      }
    },
    events: {
      change: {
        label: '图片变化事件',
        params: ['src']
      },
      preview: {
        label: '预览事件',
        params: ['src']
      }
    },
    style: {
      display: 'inline-block'
    }
  },

  MultiUpload: {
    displayName: '多文件上传',
    category: 'components',
    icon: 'upload',
    description: '支持多文件上传的组件',
    defaultProps: {
      multiple: true,
      maxCount: 5,
      accept: '',
      listType: 'text'
    },
    propSchema: {
      multiple: {
        type: 'boolean',
        label: '多文件上传'
      },
      maxCount: {
        type: 'number',
        label: '最大文件数'
      },
      accept: {
        type: 'string',
        label: '接受的文件类型',
        placeholder: '如: .jpg,.png,.pdf'
      },
      listType: {
        type: 'select',
        label: '列表类型',
        options: [
          { label: '文本', value: 'text' },
          { label: '图片', value: 'picture' },
          { label: '卡片', value: 'picture-card' }
        ]
      }
    },
    events: {
      change: {
        label: '文件变化事件',
        params: ['fileList']
      },
      success: {
        label: '上传成功事件',
        params: ['file', 'fileList']
      },
      error: {
        label: '上传失败事件',
        params: ['error', 'file']
      }
    },
    style: {
      width: '100%'
    }
  }
}

/**
 * 根据分类获取组件配置
 * @param {string} category 分类名称
 * @returns {Object} 组件配置对象
 */
export function getComponentsByCategory(category) {
  return Object.entries(YYL_COMPONENT_CONFIGS)
    .filter(([name, config]) => config.category === category)
    .reduce((acc, [name, config]) => {
      acc[name] = config
      return acc
    }, {})
}

/**
 * 获取所有组件分类
 * @returns {Array} 分类数组
 */
export function getAllCategories() {
  const categories = new Set()
  Object.values(YYL_COMPONENT_CONFIGS).forEach(config => {
    categories.add(config.category)
  })
  return Array.from(categories)
}

/**
 * 根据组件名称获取配置
 * @param {string} componentName 组件名称
 * @returns {Object|null} 组件配置
 */
export function getComponentConfig(componentName) {
  return YYL_COMPONENT_CONFIGS[componentName] || null
}

/**
 * 获取组件的默认属性
 * @param {string} componentName 组件名称
 * @returns {Object} 默认属性对象
 */
export function getComponentDefaultProps(componentName) {
  const config = YYL_COMPONENT_CONFIGS[componentName]
  return config ? { ...config.defaultProps } : {}
}

/**
 * 获取组件的属性模式
 * @param {string} componentName 组件名称
 * @returns {Object} 属性模式对象
 */
export function getComponentPropSchema(componentName) {
  const config = YYL_COMPONENT_CONFIGS[componentName]
  return config ? { ...config.propSchema } : {}
}

export default YYL_COMPONENT_CONFIGS
