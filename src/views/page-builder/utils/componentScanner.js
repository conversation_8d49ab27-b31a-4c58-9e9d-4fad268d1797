/**
 * YYL组件扫描器
 * 自动扫描src/components/yyl目录下的所有Vue组件
 * 解析组件的props、events、slots等信息
 * 生成页面构建器可用的组件配置
 */

class YYLComponentScanner {
  constructor() {
    this.components = new Map()
    this.scanPath = 'src/components/yyl'
    this.categories = ['business', 'form', 'table', 'components']
  }

  /**
   * 扫描所有YYL组件
   * @returns {Map} 组件配置映射表
   */
  async scanYYLComponents() {
    console.log('开始扫描YYL组件库...')

    // 清空之前的扫描结果
    this.components.clear()

    // 扫描各个分类
    for (const category of this.categories) {
      await this.scanCategory(category)
    }

    console.log(`扫描完成，共发现 ${this.components.size} 个组件`)
    return this.components
  }

  /**
   * 扫描特定分类的组件
   * @param {string} category 分类名称
   */
  async scanCategory(category) {
    console.log(`扫描分类: ${category}`)

    try {
      // 使用webpack的require.context动态扫描组件
      const componentContext = this.getComponentContext(category)

      if (!componentContext) {
        console.warn(`分类 ${category} 未找到组件`)
        // 使用预定义的组件信息作为备用方案
        this.addPredefinedComponents(category)
        return
      }

      const componentKeys = componentContext.keys()

      for (const key of componentKeys) {
        try {
          const component = componentContext(key)
          const componentInfo = this.parseComponent(component.default || component, key, category)

          if (componentInfo) {
            this.components.set(componentInfo.name, componentInfo)
            console.log(`✓ 扫描到组件: ${componentInfo.name}`)
          }
        } catch (error) {
          console.error(`解析组件失败: ${key}`, error)
          // 如果动态扫描失败，使用预定义信息
          this.addPredefinedComponentByKey(key, category)
        }
      }
    } catch (error) {
      console.error(`扫描分类 ${category} 失败:`, error)
      // 完全失败时，使用预定义的组件信息
      this.addPredefinedComponents(category)
    }
  }

  /**
   * 添加预定义的组件信息（当动态扫描失败时使用）
   * @param {string} category 分类名称
   */
  addPredefinedComponents(category) {
    const predefinedComponents = {
      'business': [
        {
          name: 'YYLButton',
          displayName: 'YYL按钮',
          category: 'business',
          filePath: './YYLButton.vue',
          description: '带权限控制的按钮组件',
          icon: 'button',
          props: {
            text: { type: 'string', required: true, default: '按钮' },
            menuId: { type: 'string', required: true, default: '' },
            type: { type: 'string', required: false, default: 'default' },
            size: { type: 'string', required: false, default: 'default' },
            loading: { type: 'boolean', required: false, default: false },
            disabled: { type: 'boolean', required: false, default: false }
          },
          events: [
            { name: 'click', description: '点击事件', params: [] }
          ],
          slots: [],
          mixins: [],
          defaultProps: {
            text: '按钮',
            menuId: '',
            type: 'default',
            size: 'default',
            loading: false,
            disabled: false
          },
          configSchema: {
            type: 'object',
            properties: {
              text: { type: 'string', title: 'text', default: '按钮' },
              menuId: { type: 'string', title: 'menuId', default: '' }
            }
          }
        }
      ],
      'form': [
        {
          name: 'YYLForm',
          displayName: 'YYL表单',
          category: 'form',
          filePath: './YYLForm.vue',
          description: '动态表单组件',
          icon: 'form',
          props: {
            formFields: { type: 'array', required: false, default: [] },
            layout: { type: 'string', required: false, default: 'horizontal' }
          },
          events: [
            { name: 'submit', description: '提交事件', params: ['formData'] },
            { name: 'change', description: '值变化事件', params: ['field', 'value'] }
          ],
          slots: [],
          mixins: [],
          defaultProps: {
            formFields: [],
            layout: 'horizontal'
          },
          configSchema: {
            type: 'object',
            properties: {
              formFields: { type: 'array', title: 'formFields', default: [] },
              layout: { type: 'string', title: 'layout', default: 'horizontal' }
            }
          }
        }
      ],
      'table': [
        {
          name: 'YYLTable',
          displayName: 'YYL表格',
          category: 'table',
          filePath: './YYLTable.vue',
          description: '动态表格组件',
          icon: 'table',
          props: {
            columns: { type: 'array', required: false, default: [] },
            tableData: { type: 'array', required: false, default: [] },
            loading: { type: 'boolean', required: false, default: false }
          },
          events: [
            { name: 'change', description: '表格变化事件', params: ['pagination', 'filters', 'sorter'] },
            { name: 'rowClick', description: '行点击事件', params: ['record', 'index'] }
          ],
          slots: [],
          mixins: [],
          defaultProps: {
            columns: [],
            tableData: [],
            loading: false
          },
          configSchema: {
            type: 'object',
            properties: {
              columns: { type: 'array', title: 'columns', default: [] },
              tableData: { type: 'array', title: 'tableData', default: [] }
            }
          }
        }
      ],
      'components': [
        {
          name: 'ImageControl',
          displayName: '图片控制',
          category: 'components',
          filePath: './ImageControl.vue',
          description: '图片控制和展示组件',
          icon: 'picture',
          props: {
            src: { type: 'string', required: false, default: '' },
            width: { type: 'number', required: false, default: 100 },
            height: { type: 'number', required: false, default: 100 }
          },
          events: [
            { name: 'change', description: '图片变化事件', params: ['src'] }
          ],
          slots: [],
          mixins: [],
          defaultProps: {
            src: '',
            width: 100,
            height: 100
          },
          configSchema: {
            type: 'object',
            properties: {
              src: { type: 'string', title: 'src', default: '' },
              width: { type: 'number', title: 'width', default: 100 }
            }
          }
        }
      ]
    }

    const categoryComponents = predefinedComponents[category] || []
    categoryComponents.forEach(componentInfo => {
      this.components.set(componentInfo.name, componentInfo)
      console.log(`✓ 添加预定义组件: ${componentInfo.name}`)
    })
  }

  /**
   * 根据文件key添加预定义组件
   * @param {string} key 文件key
   * @param {string} category 分类
   */
  addPredefinedComponentByKey(key, category) {
    // 从文件名推断组件名
    const componentName = key.replace('./', '').replace('.vue', '')

    // 创建基本的组件信息
    const basicComponentInfo = {
      name: componentName,
      displayName: this.getDisplayName(componentName),
      category: category,
      filePath: key,
      description: `${componentName}组件`,
      icon: this.getComponentIcon(componentName, category),
      props: {},
      events: [],
      slots: [],
      mixins: [],
      defaultProps: {},
      configSchema: { type: 'object', properties: {} }
    }

    this.components.set(componentName, basicComponentInfo)
    console.log(`✓ 添加基本组件信息: ${componentName}`)
  }

  /**
   * 获取组件上下文
   * @param {string} category 分类名称
   * @returns {Function} webpack require.context
   */
  getComponentContext(category) {
    try {
      switch (category) {
        case 'business':
          // 尝试获取business分类的组件
          try {
            return require.context('@/components/yyl/business', false, /\.vue$/)
          } catch (e) {
            console.warn('business分类组件上下文获取失败，尝试备用方案')
            return this.getFallbackContext('business')
          }
        case 'form':
          try {
            return require.context('@/components/yyl/form', false, /\.vue$/)
          } catch (e) {
            console.warn('form分类组件上下文获取失败，尝试备用方案')
            return this.getFallbackContext('form')
          }
        case 'table':
          try {
            return require.context('@/components/yyl/table', false, /\.vue$/)
          } catch (e) {
            console.warn('table分类组件上下文获取失败，尝试备用方案')
            return this.getFallbackContext('table')
          }
        case 'components':
          try {
            return require.context('@/components/yyl/components', false, /\.vue$/)
          } catch (e) {
            console.warn('components分类组件上下文获取失败，尝试备用方案')
            return this.getFallbackContext('components')
          }
        default:
          return null
      }
    } catch (error) {
      console.error(`获取 ${category} 分类上下文失败:`, error)
      return null
    }
  }

  /**
   * 获取备用上下文（当require.context失败时使用）
   * @param {string} category 分类名称
   * @returns {Object} 模拟的上下文对象
   */
  getFallbackContext(category) {
    // 返回一个模拟的上下文对象，包含已知的组件
    const knownComponents = {
      'business': ['./YYLButton.vue'],
      'form': ['./YYLForm.vue', './YYLDetails.vue'],
      'table': ['./YYLTable.vue'],
      'components': ['./ImageControl.vue', './MultiUpload.vue', './SelectTreeView.vue', './QRCodeView.vue']
    }

    const componentFiles = knownComponents[category] || []

    return {
      keys: () => componentFiles,
      resolve: (key) => key,
      id: `fallback-${category}`,
      // 模拟组件加载
      [Symbol.iterator]: function* () {
        for (const file of componentFiles) {
          yield file
        }
      }
    }
  }

  /**
   * 解析单个组件
   * @param {Object} component Vue组件对象
   * @param {string} filePath 文件路径
   * @param {string} category 分类
   * @returns {Object} 组件信息
   */
  parseComponent(component, filePath, category) {
    if (!component || !component.name) {
      console.warn(`组件 ${filePath} 缺少name属性`)
      return null
    }

    const componentInfo = {
      name: component.name,
      displayName: this.getDisplayName(component.name),
      category: category,
      filePath: filePath,
      description: this.extractDescription(component),
      icon: this.getComponentIcon(component.name, category),
      props: this.extractProps(component),
      events: this.extractEvents(component),
      slots: this.extractSlots(component),
      mixins: this.extractMixins(component),
      defaultProps: this.generateDefaultProps(component),
      configSchema: this.generateConfigSchema(component)
    }

    return componentInfo
  }

  /**
   * 获取组件显示名称
   * @param {string} componentName 组件名称
   * @returns {string} 显示名称
   */
  getDisplayName(componentName) {
    const nameMap = {
      'YYLButton': 'YYL按钮',
      'YYLForm': 'YYL表单',
      'YYLTable': 'YYL表格',
      'YYLDetails': 'YYL详情',
      'ImageControl': '图片控制',
      'MultiUpload': '多文件上传',
      'SelectTreeView': '树形选择',
      'QRCodeView': '二维码',
      'RemoteSelect': '远程选择',
      'UploadImage': '图片上传',
      'SimpleTable': '简单表格',
      'TableView': '表格视图',
      'SimpleSearchView': '简单搜索'
    }

    return nameMap[componentName] || componentName
  }

  /**
   * 提取组件描述
   * @param {Object} component Vue组件对象
   * @returns {string} 组件描述
   */
  extractDescription(component) {
    // 尝试从组件的注释或其他地方提取描述
    if (component.__docgenInfo && component.__docgenInfo.description) {
      return component.__docgenInfo.description
    }

    // 根据组件名称生成默认描述
    const descriptions = {
      'YYLButton': '带权限控制的按钮组件',
      'YYLForm': '动态表单组件，支持多种表单控件',
      'YYLTable': '动态表格组件，支持分页和操作按钮',
      'YYLDetails': '详情展示组件',
      'ImageControl': '图片控制和展示组件',
      'MultiUpload': '支持多文件上传的组件',
      'SelectTreeView': '树形结构选择组件',
      'QRCodeView': '二维码生成和展示组件'
    }

    return descriptions[component.name] || `${component.name}组件`
  }

  /**
   * 获取组件图标
   * @param {string} componentName 组件名称
   * @param {string} category 分类
   * @returns {string} 图标名称
   */
  getComponentIcon(componentName, category) {
    const iconMap = {
      'YYLButton': 'button',
      'YYLForm': 'form',
      'YYLTable': 'table',
      'YYLDetails': 'profile',
      'ImageControl': 'picture',
      'MultiUpload': 'upload',
      'SelectTreeView': 'tree',
      'QRCodeView': 'qrcode',
      'RemoteSelect': 'select',
      'UploadImage': 'file-image'
    }

    if (iconMap[componentName]) {
      return iconMap[componentName]
    }

    // 根据分类返回默认图标
    const categoryIcons = {
      'business': 'component',
      'form': 'form',
      'table': 'table',
      'components': 'appstore'
    }

    return categoryIcons[category] || 'component'
  }

  /**
   * 提取组件属性
   * @param {Object} component Vue组件对象
   * @returns {Object} 属性配置
   */
  extractProps(component) {
    const props = {}

    if (!component.props) {
      return props
    }

    Object.keys(component.props).forEach(propName => {
      const propConfig = component.props[propName]

      props[propName] = {
        type: this.getTypeString(propConfig.type),
        required: propConfig.required || false,
        default: this.getDefaultValue(propConfig.default),
        description: this.extractPropDescription(propConfig),
        validator: propConfig.validator ? 'custom' : null,
        options: this.extractPropOptions(propConfig)
      }
    })

    return props
  }

  /**
   * 获取类型字符串
   * @param {Function|Array} type 类型构造函数
   * @returns {string} 类型字符串
   */
  getTypeString(type) {
    if (!type) return 'any'

    if (Array.isArray(type)) {
      return type.map(t => t.name.toLowerCase()).join('|')
    }

    return type.name.toLowerCase()
  }

  /**
   * 获取默认值
   * @param {*} defaultValue 默认值
   * @returns {*} 处理后的默认值
   */
  getDefaultValue(defaultValue) {
    if (typeof defaultValue === 'function') {
      try {
        return defaultValue()
      } catch (error) {
        return null
      }
    }
    return defaultValue
  }

  /**
   * 提取属性描述
   * @param {Object} propConfig 属性配置
   * @returns {string} 属性描述
   */
  extractPropDescription(propConfig) {
    // 这里可以从注释或其他地方提取属性描述
    return propConfig.description || ''
  }

  /**
   * 提取属性选项
   * @param {Object} propConfig 属性配置
   * @returns {Array} 选项数组
   */
  extractPropOptions(propConfig) {
    // 如果有validator，尝试提取选项
    if (propConfig.validator) {
      // 这里可以分析validator函数来提取可能的选项
      return null
    }
    return null
  }

  /**
   * 提取组件事件
   * @param {Object} component Vue组件对象
   * @returns {Array} 事件列表
   */
  extractEvents(component) {
    const events = []

    // 从组件的$emit调用中提取事件
    // 这里需要静态分析组件代码，暂时返回常见事件
    const commonEvents = {
      'YYLButton': ['click'],
      'YYLForm': ['submit', 'change', 'validate'],
      'YYLTable': ['change', 'rowClick', 'select'],
      'ImageControl': ['change', 'preview'],
      'MultiUpload': ['change', 'success', 'error'],
      'SelectTreeView': ['change', 'select']
    }

    const componentEvents = commonEvents[component.name] || []

    componentEvents.forEach(eventName => {
      events.push({
        name: eventName,
        description: `${eventName}事件`,
        params: this.getEventParams(component.name, eventName)
      })
    })

    return events
  }

  /**
   * 获取事件参数
   * @param {string} componentName 组件名称
   * @param {string} eventName 事件名称
   * @returns {Array} 参数列表
   */
  getEventParams(componentName, eventName) {
    const eventParams = {
      'YYLButton': {
        'click': []
      },
      'YYLForm': {
        'submit': ['formData'],
        'change': ['field', 'value'],
        'validate': ['valid', 'errors']
      },
      'YYLTable': {
        'change': ['pagination', 'filters', 'sorter'],
        'rowClick': ['record', 'index'],
        'select': ['selectedRows']
      }
    }

    return (eventParams[componentName] && eventParams[componentName][eventName]) || []
  }

  /**
   * 提取组件插槽
   * @param {Object} component Vue组件对象
   * @returns {Array} 插槽列表
   */
  extractSlots(component) {
    // 暂时返回空数组，后续可以通过模板分析提取插槽信息
    return []
  }

  /**
   * 提取组件混入
   * @param {Object} component Vue组件对象
   * @returns {Array} 混入列表
   */
  extractMixins(component) {
    if (!component.mixins) {
      return []
    }

    return component.mixins.map(mixin => {
      return mixin.name || 'UnnamedMixin'
    })
  }

  /**
   * 生成默认属性
   * @param {Object} component Vue组件对象
   * @returns {Object} 默认属性对象
   */
  generateDefaultProps(component) {
    const defaultProps = {}

    if (component.props) {
      Object.keys(component.props).forEach(propName => {
        const propConfig = component.props[propName]
        defaultProps[propName] = this.getDefaultValue(propConfig.default)
      })
    }

    return defaultProps
  }

  /**
   * 生成配置模式
   * @param {Object} component Vue组件对象
   * @returns {Object} 配置模式
   */
  generateConfigSchema(component) {
    const schema = {
      type: 'object',
      properties: {}
    }

    if (component.props) {
      Object.keys(component.props).forEach(propName => {
        const propConfig = component.props[propName]

        schema.properties[propName] = {
          type: this.getSchemaType(propConfig.type),
          title: propName,
          description: this.extractPropDescription(propConfig),
          default: this.getDefaultValue(propConfig.default)
        }

        if (propConfig.required) {
          if (!schema.required) schema.required = []
          schema.required.push(propName)
        }
      })
    }

    return schema
  }

  /**
   * 获取模式类型
   * @param {Function|Array} type 类型构造函数
   * @returns {string} 模式类型
   */
  getSchemaType(type) {
    if (!type) return 'string'

    const typeMap = {
      'String': 'string',
      'Number': 'number',
      'Boolean': 'boolean',
      'Array': 'array',
      'Object': 'object'
    }

    if (Array.isArray(type)) {
      return 'string' // 多类型默认为string
    }

    return typeMap[type.name] || 'string'
  }

  /**
   * 获取所有扫描到的组件
   * @returns {Map} 组件映射表
   */
  getAllComponents() {
    return this.components
  }

  /**
   * 根据分类获取组件
   * @param {string} category 分类名称
   * @returns {Array} 组件列表
   */
  getComponentsByCategory(category) {
    const components = []

    this.components.forEach(component => {
      if (component.category === category) {
        components.push(component)
      }
    })

    return components
  }

  /**
   * 根据名称获取组件
   * @param {string} name 组件名称
   * @returns {Object|null} 组件信息
   */
  getComponentByName(name) {
    return this.components.get(name) || null
  }
}

// 创建单例实例
const yylComponentScanner = new YYLComponentScanner()

export default yylComponentScanner
export { YYLComponentScanner }
