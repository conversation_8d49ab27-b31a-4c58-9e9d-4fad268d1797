<template>
  <div class="page-builder-container">
    <div class="page-builder-header">
      <h1>YYL页面构建器</h1>
      <div class="header-actions">
        <a-button type="primary" @click="openDesigner">
          <a-icon type="plus" />
          新建页面
        </a-button>
        <a-button @click="openTemplates">
          <a-icon type="appstore" />
          模板库
        </a-button>
        <a-button @click="openPageManager">
          <a-icon type="folder" />
          页面管理
        </a-button>
      </div>
    </div>

    <div class="page-builder-content">
      <div class="welcome-section" v-if="!$route.params.action">
        <div class="welcome-card">
          <a-icon type="build" class="welcome-icon" />
          <h2>欢迎使用YYL页面构建器</h2>
          <p>基于现有YYL组件库的可视化页面构建系统</p>

          <div class="feature-list">
            <div class="feature-item">
              <a-icon type="drag" />
              <span>可视化拖拽设计</span>
            </div>
            <div class="feature-item">
              <a-icon type="component" />
              <span>基于YYL组件库</span>
            </div>
            <div class="feature-item">
              <a-icon type="safety" />
              <span>完整的权限控制</span>
            </div>
            <div class="feature-item">
              <a-icon type="mobile" />
              <span>响应式设计支持</span>
            </div>
          </div>

          <div class="action-buttons">
            <a-button type="primary" size="large" @click="openFullscreenDesigner">
              <a-icon type="build" />
              开始设计（推荐）
            </a-button>
            <a-button size="large" @click="openDesigner">
              <a-icon type="desktop" />
              内嵌设计器
            </a-button>
            <a-button size="large" @click="openTemplates">
              <a-icon type="appstore" />
              浏览模板
            </a-button>
          </div>
        </div>
      </div>

      <router-view v-else />
    </div>
  </div>
</template>

<script>
export default {
  name: 'PageBuilder',
  data() {
    return {
      // 页面构建器主入口数据
    }
  },
  methods: {
    openFullscreenDesigner() {
      // 在新标签页中打开全屏设计器
      const url = this.$router.resolve('/page-builder/fullscreen').href
      window.open(url, '_blank', 'width=1400,height=900,scrollbars=no,resizable=yes')

      this.$message.success('设计器已在新窗口中打开')
    },
    openDesigner() {
      this.$router.push('/page-builder/designer')
    },
    openTemplates() {
      this.$router.push('/page-builder/templates')
    },
    openPageManager() {
      this.$router.push('/page-builder/pages')
    }
  },
  mounted() {
    console.log('YYL页面构建器已加载')
  }
}
</script>

<style lang="less" scoped>
.page-builder-container {
  min-height: 100vh;
  background: #f0f2f5;

  .page-builder-header {
    background: #fff;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h1 {
      margin: 0;
      color: #1890ff;
      font-size: 24px;
    }

    .header-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .page-builder-content {
    padding: 24px;

    .welcome-section {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 60vh;

      .welcome-card {
        background: #fff;
        padding: 48px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        text-align: center;
        max-width: 600px;

        .welcome-icon {
          font-size: 64px;
          color: #1890ff;
          margin-bottom: 24px;
        }

        h2 {
          margin-bottom: 16px;
          color: #262626;
        }

        p {
          color: #8c8c8c;
          margin-bottom: 32px;
          font-size: 16px;
        }

        .feature-list {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
          margin-bottom: 32px;

          .feature-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #f6f8fa;
            border-radius: 4px;

            .anticon {
              margin-right: 8px;
              color: #1890ff;
            }
          }
        }

        .action-buttons {
          .ant-btn {
            margin: 0 8px;
          }
        }
      }
    }
  }
}
</style>
