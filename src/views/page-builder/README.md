# YYL页面构建器

## 概述
基于现有YYL组件库的可视化页面构建系统，允许用户通过拖拽的方式快速构建页面。

## 功能特性
- 🎨 **可视化拖拽设计** - 直观的拖拽界面，所见即所得
- 🧩 **基于YYL组件库** - 充分利用现有的40+个YYL组件
- 🔐 **完整的权限控制** - 继承现有系统的权限管理机制
- 📱 **响应式设计支持** - 支持多设备预览和适配
- 💾 **页面保存加载** - 支持页面配置的保存和加载
- 🔧 **代码生成功能** - 自动生成Vue单文件组件代码

## 技术架构

### 核心技术栈
- **Vue 2.5.22** - 主框架
- **Ant Design Vue 1.7.8** - UI组件库
- **Vuex 3.0.1** - 状态管理
- **现有YYL组件库** - 业务组件基础

### 目录结构
```
src/views/page-builder/
├── README.md                    # 项目说明文档
├── index.vue                   # 主入口页面
├── router.js                   # 路由配置
├── components/                 # 构建器组件
│   ├── Designer/              # 设计器核心组件
│   ├── PropertyEditors/       # 属性编辑器
│   ├── RenderEngine/          # 渲染引擎
│   ├── Common/                # 通用组件
│   └── PageManager/           # 页面管理
├── configs/                   # 配置文件
├── mixins/                    # 混入
├── utils/                     # 工具函数
├── store/                     # 状态管理
├── styles/                    # 样式文件
├── api/                       # API接口
└── docs/                      # 文档
```

## 开发状态

### 当前进度
- ✅ **任务1.1**: 创建项目基础结构 - 已完成
- ⏳ **任务1.2**: YYL组件扫描器开发 - 待开始
- ⏳ **任务1.3**: 设计器主界面开发 - 待开始
- ⏳ **任务1.4**: 状态管理配置 - 待开始

### 开发阶段
1. **阶段一**: 基础架构搭建 (当前阶段)
2. **阶段二**: 核心功能开发
3. **阶段三**: YYL组件深度集成
4. **阶段四**: 高级功能开发

## 使用方式

### 访问路径
- 主入口: `/page-builder`
- 页面设计器: `/page-builder/designer`
- 模板库: `/page-builder/templates`
- 页面管理: `/page-builder/pages`

### 权限要求
- 基础访问: `PAGE_BUILDER_ACCESS`
- 创建页面: `PAGE_BUILDER_CREATE`
- 编辑页面: `PAGE_BUILDER_EDIT`
- 删除页面: `PAGE_BUILDER_DELETE`

## 集成说明

### 路由集成
```javascript
// 在主路由配置中添加
import { pageBuilderRoutes } from '@/views/page-builder/router'

// 添加到路由配置中
const routes = [
  // ... 其他路由
  pageBuilderRoutes
]
```

### 权限集成
页面构建器完全集成现有的权限系统，自动继承：
- 用户token验证
- 按钮权限控制
- 菜单权限管理

### API集成
使用现有的请求配置：
```javascript
import { axios } from '@/utils/request'
// 自动包含所有现有的请求拦截器
```

## 开发指南

### 开发环境启动
```bash
# 启动开发服务器
npm run serve

# 访问页面构建器
http://localhost:8080/#/page-builder
```

### 代码规范
- 遵循项目现有的ESLint规则
- 使用现有的代码风格和命名规范
- 充分利用现有的工具函数和组件

### 测试要求
- 每个功能开发完成后进行基础测试
- 确保与现有系统的兼容性
- 验证YYL组件的正确集成

## 贡献指南

### 开发流程
1. 查看开发规划文档
2. 按任务顺序进行开发
3. 完成后更新任务状态
4. 提交代码并记录变更

### 问题反馈
如遇到问题，请在对应任务的问题记录中详细描述。

## 更新日志

### [0.4.0] - 2025-01-28
#### 新增功能 - 全屏设计器布局优化 ✅
- ✅ **新标签页打开** - 设计器在独立窗口中运行，无左侧菜单干扰
- ✅ **全屏布局设计** - 充分利用屏幕空间，提供更大的设计区域
- ✅ **可收起组件面板** - 左侧组件面板支持收起，释放更多画布空间
- ✅ **右侧抽屉属性面板** - 属性面板采用抽屉式设计，按需显示，不占用固定空间
- ✅ **强化工具栏** - 集成面板控制、网格标尺、缩放等功能
- ✅ **专业设计体验** - 类似专业设计软件的界面布局和交互

#### 技术实现
- 创建 `FullscreenDesigner.vue` 全屏设计器组件
- 新增 `fullscreen.vue` 独立页面路由
- 优化 `Canvas.vue` 支持外部控制网格和标尺
- 更新主入口页面，提供"开始设计（推荐）"选项
- 添加演示页面 `fullscreen-demo.vue`

#### 用户体验提升
- **更大画布空间** - 中间设计区域占据更多屏幕空间
- **智能面板管理** - 组件面板可收起，属性面板智能显示
- **无干扰设计** - 移除不必要界面元素，专注页面设计
- **独立窗口运行** - 不影响主系统，可同时进行其他操作

### [0.3.0] - 2025-01-28
#### 新增功能 - 任务1.3完成
- ✅ 设计器主界面开发完成
- ✅ 工具栏组件 (Toolbar.vue) - 支持撤销/重做、复制/粘贴、缩放控制
- ✅ 组件面板 (ComponentPanel.vue) - YYL组件分类展示和拖拽功能
- ✅ 画布区域 (Canvas.vue) - 网格背景、组件拖入、选中编辑
- ✅ 属性面板 (PropertyPanel.vue) - 动态属性编辑、样式配置
- ✅ 设计器样式文件 (designer.less) - 完整的界面样式定义
- ✅ 基础交互功能 - 键盘快捷键、历史记录管理

#### 技术实现
- 完整的三栏布局设计器界面
- 基于YYL组件配置的动态渲染
- 拖拽交互和组件管理
- 响应式设计和用户体验优化

### [0.2.0] - 2025-01-28
#### 新增功能 - 任务1.2完成
- YYL组件扫描器开发
- 组件配置模板生成
- 支持business、form、table、components四个分类

### [0.1.0] - 2025-01-28
#### 新增功能 - 任务1.1完成
- 创建项目基础结构
- 主入口页面开发
- 路由配置完成
- 项目文档编写

#### 技术架构
- 基于Vue 2 + Ant Design Vue
- 集成现有YYL组件库
- 复用现有权限和请求系统
