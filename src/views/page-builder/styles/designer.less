/**
 * 页面设计器样式文件
 * 定义设计器界面的全局样式和布局
 */

// 设计器主容器
.page-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  overflow: hidden;

  // 工具栏区域
  .designer-toolbar {
    flex-shrink: 0;
    z-index: 100;
  }

  // 主工作区域
  .designer-workspace {
    flex: 1;
    display: flex;
    min-height: 0;

    // 左侧组件面板
    .designer-left {
      width: 280px;
      flex-shrink: 0;
      background: #fafafa;
      border-right: 1px solid #e8e8e8;
      display: flex;
      flex-direction: column;
    }

    // 中间画布区域
    .designer-center {
      flex: 1;
      background: #f5f5f5;
      display: flex;
      flex-direction: column;
      min-width: 0;
    }

    // 右侧属性面板
    .designer-right {
      width: 320px;
      flex-shrink: 0;
      background: #fafafa;
      border-left: 1px solid #e8e8e8;
      display: flex;
      flex-direction: column;
    }
  }
}

// 面板通用样式
.panel-header {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 12px 16px;

  h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
  }
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  background: #fafafa;
}

// 滚动条样式
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

// 拖拽相关样式
.dragging {
  cursor: grabbing !important;
  user-select: none;

  * {
    pointer-events: none;
  }
}

.drag-ghost {
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  background: rgba(24, 144, 255, 0.1);
  border: 2px dashed #1890ff;
  border-radius: 4px;
  padding: 8px;
  font-size: 12px;
  color: #1890ff;
  transform: rotate(5deg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

// 组件选中状态样式
.component-selected {
  outline: 2px solid #1890ff !important;
  outline-offset: -2px;
}

.component-hover {
  outline: 1px dashed #1890ff !important;
  outline-offset: -1px;
}

// 网格背景样式
.canvas-grid {
  background-image: 
    linear-gradient(to right, #e8e8e8 1px, transparent 1px),
    linear-gradient(to bottom, #e8e8e8 1px, transparent 1px);
  background-size: 10px 10px;
}

// 标尺样式
.ruler-horizontal {
  position: absolute;
  top: 0;
  left: 20px;
  right: 20px;
  height: 20px;
  background: #fff;
  border-bottom: 1px solid #d9d9d9;
  z-index: 10;
}

.ruler-vertical {
  position: absolute;
  top: 20px;
  left: 0;
  bottom: 20px;
  width: 20px;
  background: #fff;
  border-right: 1px solid #d9d9d9;
  z-index: 10;
}

// 响应式设计
@media (max-width: 1200px) {
  .page-designer .designer-workspace {
    .designer-left {
      width: 240px;
    }
    
    .designer-right {
      width: 280px;
    }
  }
}

@media (max-width: 992px) {
  .page-designer .designer-workspace {
    .designer-left {
      width: 200px;
    }
    
    .designer-right {
      width: 240px;
    }
  }
}

// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s;
}

.slide-enter,
.slide-leave-to {
  transform: translateX(-100%);
}

// 工具提示样式
.designer-tooltip {
  font-size: 12px;
  
  .tooltip-title {
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .tooltip-desc {
    color: #8c8c8c;
    line-height: 1.4;
  }
}

// 快捷键提示
.shortcut-hint {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s;

  &.show {
    opacity: 1;
  }

  .shortcut-key {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 2px;
    margin: 0 2px;
    font-family: monospace;
  }
}

// 加载状态
.designer-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .loading-content {
    text-align: center;
    color: #1890ff;

    .anticon {
      font-size: 32px;
      margin-bottom: 12px;
      display: block;
    }

    .loading-text {
      font-size: 14px;
    }
  }
}

// 错误状态
.designer-error {
  padding: 40px 20px;
  text-align: center;
  color: #ff4d4f;

  .anticon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
  }

  .error-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .error-message {
    font-size: 14px;
    color: #8c8c8c;
    margin-bottom: 16px;
  }

  .error-actions {
    .ant-btn {
      margin: 0 4px;
    }
  }
}

// 空状态样式
.empty-state {
  padding: 60px 20px;
  text-align: center;
  color: #bfbfbf;

  .anticon {
    font-size: 64px;
    margin-bottom: 16px;
    display: block;
  }

  .empty-title {
    font-size: 16px;
    color: #8c8c8c;
    margin-bottom: 8px;
  }

  .empty-description {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 16px;
  }

  .empty-actions {
    .ant-btn {
      margin: 0 4px;
    }
  }
}

// 打印样式
@media print {
  .page-designer {
    .designer-toolbar,
    .designer-left,
    .designer-right {
      display: none !important;
    }

    .designer-center {
      width: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
    }
  }
}
