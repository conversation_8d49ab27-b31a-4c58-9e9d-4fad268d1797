<template>
  <div class="fullscreen-page">
    <FullscreenDesigner />
  </div>
</template>

<script>
import FullscreenDesigner from './components/Designer/FullscreenDesigner.vue'

export default {
  name: 'FullscreenPageBuilder',
  components: {
    FullscreenDesigner
  },
  mounted() {
    // 设置页面标题
    document.title = 'YYL页面设计器'
    
    // 隐藏浏览器滚动条
    document.documentElement.style.overflow = 'hidden'
    document.body.style.overflow = 'hidden'
    
    console.log('全屏页面构建器已加载')
  },
  beforeDestroy() {
    // 恢复滚动条
    document.documentElement.style.overflow = ''
    document.body.style.overflow = ''
  }
}
</script>

<style lang="less" scoped>
.fullscreen-page {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
</style>

<style lang="less">
// 全局样式，移除页面边距和布局
html, body {
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  overflow: hidden !important;
}

// 隐藏Ant Design的默认布局
.ant-layout {
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
}

.ant-layout-sider {
  display: none !important;
}

.ant-layout-header {
  display: none !important;
}

.ant-layout-content {
  padding: 0 !important;
  margin: 0 !important;
}

// 确保全屏显示
#app {
  height: 100vh !important;
  overflow: hidden !important;
  padding: 0 !important;
  margin: 0 !important;
}

// 隐藏面包屑导航
.ant-breadcrumb {
  display: none !important;
}

// 隐藏页面头部
.page-header-wrapper {
  display: none !important;
}

// 隐藏全局头部
.global-header {
  display: none !important;
}
</style>
