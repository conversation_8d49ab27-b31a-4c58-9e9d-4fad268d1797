<template>
  <div class="fullscreen-page">
    <FullscreenDesigner />
  </div>
</template>

<script>
import FullscreenDesigner from './components/Designer/FullscreenDesigner.vue'

export default {
  name: 'FullscreenPageBuilder',
  components: {
    FullscreenDesigner
  },
  mounted() {
    // 设置页面标题
    document.title = 'YYL页面设计器'

    // 添加全屏模式类，只影响当前页面
    document.body.classList.add('fullscreen-mode')

    console.log('全屏页面构建器已加载')
  },
  beforeDestroy() {
    // 移除全屏模式类，恢复正常状态
    document.body.classList.remove('fullscreen-mode')

    // 恢复页面标题
    document.title = '云医贸供应链管理系统'
  }
}
</script>

<style lang="less" scoped>
.fullscreen-page {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
</style>

<style lang="less">
// 只在全屏页面生效的样式
.fullscreen-page {
  // 当前页面的全局样式重置
  html, body {
    margin: 0 !important;
    padding: 0 !important;
    height: 100% !important;
    overflow: hidden !important;
  }

  // 隐藏主布局的各个部分
  ~ .ant-layout-sider,
  ~ .ant-layout-header {
    display: none !important;
  }
}

// 使用body类选择器，只在全屏页面时生效
body.fullscreen-mode {
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  overflow: hidden !important;

  // 隐藏Ant Design的默认布局组件
  .ant-layout {
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .ant-layout-sider {
    display: none !important;
  }

  .ant-layout-header {
    display: none !important;
  }

  .ant-layout-content {
    padding: 0 !important;
    margin: 0 !important;
  }

  // 确保应用容器全屏显示
  #app {
    height: 100vh !important;
    overflow: hidden !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  // 隐藏其他界面元素
  .ant-breadcrumb,
  .page-header-wrapper,
  .global-header {
    display: none !important;
  }
}
</style>
