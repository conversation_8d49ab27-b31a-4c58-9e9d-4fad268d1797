<template>
  <div class="fullscreen-page">
    <FullscreenDesigner />
  </div>
</template>

<script>
import FullscreenDesigner from './components/Designer/FullscreenDesigner.vue'

export default {
  name: 'FullscreenPageBuilder',
  components: {
    FullscreenDesigner
  },
  mounted() {
    // 设置页面标题
    document.title = 'YYL页面设计器'
    
    // 隐藏浏览器滚动条
    document.documentElement.style.overflow = 'hidden'
    document.body.style.overflow = 'hidden'
    
    console.log('全屏页面构建器已加载')
  },
  beforeDestroy() {
    // 恢复滚动条
    document.documentElement.style.overflow = ''
    document.body.style.overflow = ''
  }
}
</script>

<style lang="less" scoped>
.fullscreen-page {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
</style>

<style lang="less">
// 全局样式，移除页面边距
html, body {
  margin: 0 !important;
  padding: 0 !important;
}

// 隐藏Ant Design的默认布局
.ant-layout {
  background: transparent !important;
}

// 确保全屏显示
#app {
  height: 100vh !important;
  overflow: hidden !important;
}
</style>
