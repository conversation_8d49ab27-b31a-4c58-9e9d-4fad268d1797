/**
 * YYL页面构建器路由配置
 * 基于现有路由系统的扩展配置
 */

// 页面构建器路由配置
export const pageBuilderRoutes = {
  path: '/page-builder',
  name: 'PageBuilder',
  component: () => import('./index.vue'),
  meta: {
    title: '页面构建器',
    icon: 'build',
    permission: ['PAGE_BUILDER_ACCESS']
  },
  children: [
    {
      path: '',
      name: 'PageBuilderHome',
      component: () => import('./index.vue'),
      meta: {
        title: '页面构建器首页',
        hideInMenu: true
      }
    },
    {
      path: 'designer',
      name: 'PageDesigner',
      component: () => import('./components/Designer/index.vue'),
      meta: {
        title: '页面设计器',
        permission: ['PAGE_BUILDER_CREATE', 'PAGE_BUILDER_EDIT']
      }
    },
    {
      path: 'templates',
      name: 'PageTemplates',
      component: () => import('./components/PageManager/TemplateGallery.vue'),
      meta: {
        title: '页面模板',
        permission: ['PAGE_BUILDER_TEMPLATE_ACCESS']
      }
    },
    {
      path: 'pages',
      name: 'PageManager',
      component: () => import('./components/PageManager/PageList.vue'),
      meta: {
        title: '页面管理',
        permission: ['PAGE_BUILDER_ACCESS']
      }
    }
  ]
}

// 页面构建器权限配置
export const pageBuilderPermissions = {
  // 页面构建器基础权限
  ACCESS: 'PAGE_BUILDER_ACCESS',           // 访问页面构建器
  CREATE: 'PAGE_BUILDER_CREATE',           // 创建页面
  EDIT: 'PAGE_BUILDER_EDIT',               // 编辑页面
  DELETE: 'PAGE_BUILDER_DELETE',           // 删除页面
  PUBLISH: 'PAGE_BUILDER_PUBLISH',         // 发布页面

  // 模板权限
  TEMPLATE_ACCESS: 'PAGE_BUILDER_TEMPLATE_ACCESS',
  TEMPLATE_CREATE: 'PAGE_BUILDER_TEMPLATE_CREATE',
  TEMPLATE_EDIT: 'PAGE_BUILDER_TEMPLATE_EDIT',
  TEMPLATE_DELETE: 'PAGE_BUILDER_TEMPLATE_DELETE',

  // 组件权限
  COMPONENT_ACCESS: 'PAGE_BUILDER_COMPONENT_ACCESS',
  COMPONENT_CONFIG: 'PAGE_BUILDER_COMPONENT_CONFIG'
}

// 集成到主路由配置的辅助函数
export function integratePageBuilderRoutes(existingRoutes) {
  // 查找合适的位置插入页面构建器路由
  const insertIndex = existingRoutes.findIndex(route =>
    route.path === '/dashboard' || route.path === '/'
  )

  if (insertIndex !== -1) {
    existingRoutes.splice(insertIndex + 1, 0, pageBuilderRoutes)
  } else {
    existingRoutes.push(pageBuilderRoutes)
  }

  return existingRoutes
}

export default pageBuilderRoutes
