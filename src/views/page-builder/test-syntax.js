/**
 * 语法兼容性测试
 * 验证所有组件是否能正确编译
 */

// 测试可选链操作符的替代方案
function testOptionalChaining() {
  const obj = {
    position: { x: 100, y: 200 },
    config: { icon: 'button', displayName: '按钮' }
  }

  // 使用传统语法替代可选链
  const x = (obj.position && obj.position.x) || 0
  const y = (obj.position && obj.position.y) || 0
  const icon = (obj.config && obj.config.icon) || 'component'
  const displayName = (obj.config && obj.config.displayName) || 'Unknown'

  console.log('Position:', { x, y })
  console.log('Config:', { icon, displayName })

  return { x, y, icon, displayName }
}

// 测试组件配置获取
function testComponentConfig() {
  const configs = {
    'YYLButton': { 
      displayName: 'YYL按钮', 
      icon: 'button',
      category: 'business'
    },
    'YYLForm': { 
      displayName: 'YYL表单', 
      icon: 'form',
      category: 'form'
    },
    'YYLTable': { 
      displayName: 'YYL表格', 
      icon: 'table',
      category: 'table'
    }
  }

  function getComponentConfig(componentType) {
    return configs[componentType] || null
  }

  function getComponentIcon(componentType) {
    const componentConfig = getComponentConfig(componentType)
    return (componentConfig && componentConfig.icon) || 'component'
  }

  function getComponentDisplayName(componentType) {
    const componentConfig = getComponentConfig(componentType)
    return (componentConfig && componentConfig.displayName) || componentType
  }

  // 测试各种组件类型
  const testTypes = ['YYLButton', 'YYLForm', 'YYLTable', 'UnknownComponent']
  
  testTypes.forEach(type => {
    console.log(`${type}:`, {
      icon: getComponentIcon(type),
      displayName: getComponentDisplayName(type),
      config: getComponentConfig(type)
    })
  })

  return { getComponentConfig, getComponentIcon, getComponentDisplayName }
}

// 测试组件样式计算
function testComponentStyle() {
  const components = [
    {
      id: 'comp1',
      position: { x: 100, y: 200 },
      style: { width: '200px', height: '100px' }
    },
    {
      id: 'comp2',
      position: null,
      style: { margin: '10px' }
    },
    {
      id: 'comp3',
      // 没有position属性
      style: { padding: '5px' }
    }
  ]

  function getComponentStyle(component) {
    return {
      position: 'absolute',
      left: `${(component.position && component.position.x) || 0}px`,
      top: `${(component.position && component.position.y) || 0}px`,
      ...component.style
    }
  }

  components.forEach(comp => {
    console.log(`Component ${comp.id} style:`, getComponentStyle(comp))
  })

  return { getComponentStyle }
}

// 运行所有测试
function runAllTests() {
  console.log('=== 语法兼容性测试开始 ===')
  
  try {
    console.log('\n1. 测试可选链操作符替代方案:')
    testOptionalChaining()
    
    console.log('\n2. 测试组件配置获取:')
    testComponentConfig()
    
    console.log('\n3. 测试组件样式计算:')
    testComponentStyle()
    
    console.log('\n=== 所有测试通过 ===')
    return true
  } catch (error) {
    console.error('测试失败:', error)
    return false
  }
}

// 导出测试函数
export {
  testOptionalChaining,
  testComponentConfig,
  testComponentStyle,
  runAllTests
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.syntaxTest = { runAllTests }
} else if (typeof module !== 'undefined' && module.exports) {
  // Node.js环境
  module.exports = { runAllTests }
}

// 自动运行测试（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  setTimeout(() => {
    console.log('自动运行语法兼容性测试...')
    runAllTests()
  }, 1000)
}
