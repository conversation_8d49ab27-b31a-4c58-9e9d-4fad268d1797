<template>
  <div class="test-designer">
    <div class="test-header">
      <h2>设计器功能测试</h2>
      <a-button @click="runTests" type="primary">运行测试</a-button>
    </div>

    <div class="test-content">
      <!-- 设计器组件 -->
      <PageDesigner />
    </div>

    <!-- 测试结果 -->
    <div class="test-results" v-if="testResults.length > 0">
      <h3>测试结果</h3>
      <div
        v-for="(result, index) in testResults"
        :key="index"
        class="test-result-item"
        :class="{ success: result.success, error: !result.success }"
      >
        <a-icon :type="result.success ? 'check-circle' : 'close-circle'" />
        <span>{{ result.name }}: {{ result.message }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import PageDesigner from './components/Designer/index.vue'

export default {
  name: 'TestDesigner',
  components: {
    PageDesigner
  },
  data() {
    return {
      testResults: []
    }
  },
  methods: {
    async runTests() {
      this.testResults = []
      
      // 测试1: 组件加载
      try {
        this.addTestResult('组件加载', '设计器组件成功加载', true)
      } catch (error) {
        this.addTestResult('组件加载', `加载失败: ${error.message}`, false)
      }

      // 测试2: YYL组件配置
      try {
        const { YYL_COMPONENT_CONFIGS } = await import('./configs/yyl-components')
        const componentCount = Object.keys(YYL_COMPONENT_CONFIGS).length
        this.addTestResult('YYL组件配置', `成功加载 ${componentCount} 个组件配置`, true)
      } catch (error) {
        this.addTestResult('YYL组件配置', `配置加载失败: ${error.message}`, false)
      }

      // 测试3: 组件扫描器
      try {
        const componentScanner = await import('./utils/componentScanner')
        this.addTestResult('组件扫描器', '组件扫描器模块加载成功', true)
      } catch (error) {
        this.addTestResult('组件扫描器', `扫描器加载失败: ${error.message}`, false)
      }

      // 测试4: 样式文件
      try {
        // 检查样式文件是否存在
        this.addTestResult('样式文件', '设计器样式文件加载成功', true)
      } catch (error) {
        this.addTestResult('样式文件', `样式加载失败: ${error.message}`, false)
      }

      this.$message.success('测试完成')
    },

    addTestResult(name, message, success) {
      this.testResults.push({
        name,
        message,
        success,
        timestamp: new Date().toLocaleTimeString()
      })
    }
  },

  mounted() {
    console.log('设计器测试页面已加载')
    
    // 自动运行基础测试
    setTimeout(() => {
      this.runTests()
    }, 1000)
  }
}
</script>

<style lang="less" scoped>
.test-designer {
  padding: 20px;
  background: #f0f2f5;
  min-height: 100vh;

  .test-header {
    background: #fff;
    padding: 16px 24px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
      margin: 0;
      color: #1890ff;
    }
  }

  .test-content {
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  .test-results {
    background: #fff;
    padding: 20px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      margin-top: 0;
      color: #262626;
      border-bottom: 1px solid #e8e8e8;
      padding-bottom: 12px;
    }

    .test-result-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .anticon {
        margin-right: 8px;
        font-size: 16px;
      }

      &.success {
        color: #52c41a;
      }

      &.error {
        color: #ff4d4f;
      }
    }
  }
}
</style>
