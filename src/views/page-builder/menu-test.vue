<template>
  <div class="menu-test">
    <div class="test-header">
      <h2>菜单点击行为测试</h2>
      <a-button @click="testMenuClick" type="primary">
        <a-icon type="experiment" />
        测试菜单点击
      </a-button>
    </div>

    <div class="test-content">
      <a-card title="测试说明" style="margin-bottom: 20px;">
        <p>此页面用于测试左侧菜单的"页面设计器"点击行为是否正确。</p>
        <ul>
          <li>点击左侧菜单的"页面设计器"应该在新标签页中打开全屏设计器</li>
          <li>新窗口应该没有左侧菜单，完全占据浏览器空间</li>
          <li>设计器应该正常工作，包括组件面板、画布和属性面板</li>
        </ul>
      </a-card>

      <a-card title="菜单配置信息">
        <a-descriptions bordered :column="1">
          <a-descriptions-item label="菜单ID">PAGE_BUILDER_DESIGNER</a-descriptions-item>
          <a-descriptions-item label="菜单名称">页面设计器</a-descriptions-item>
          <a-descriptions-item label="路径">page-builder/fullscreen</a-descriptions-item>
          <a-descriptions-item label="打开方式">新标签页 (OpenInNewTab: true)</a-descriptions-item>
          <a-descriptions-item label="窗口大小">1400x900</a-descriptions-item>
        </a-descriptions>
      </a-card>

      <a-card title="测试步骤" style="margin-top: 20px;">
        <a-steps direction="vertical" size="small" :current="currentStep">
          <a-step title="点击左侧菜单">
            <template slot="description">
              在左侧菜单中找到"页面构建器" > "页面设计器"并点击
            </template>
          </a-step>
          <a-step title="验证新窗口打开">
            <template slot="description">
              确认新标签页/窗口正确打开，没有左侧菜单
            </template>
          </a-step>
          <a-step title="验证设计器功能">
            <template slot="description">
              确认设计器界面正常显示，功能正常工作
            </template>
          </a-step>
          <a-step title="测试完成">
            <template slot="description">
              所有功能验证通过
            </template>
          </a-step>
        </a-steps>
      </a-card>

      <a-card title="快速测试" style="margin-top: 20px;">
        <p>点击下面的按钮直接测试新窗口打开功能：</p>
        <a-button-group>
          <a-button @click="openFullscreenDesigner" type="primary">
            <a-icon type="build" />
            打开全屏设计器
          </a-button>
          <a-button @click="openNormalDesigner">
            <a-icon type="desktop" />
            打开内嵌设计器
          </a-button>
        </a-button-group>
      </a-card>

      <a-card title="测试结果" style="margin-top: 20px;" v-if="testResults.length > 0">
        <a-timeline>
          <a-timeline-item
            v-for="(result, index) in testResults"
            :key="index"
            :color="result.success ? 'green' : 'red'"
          >
            <a-icon :type="result.success ? 'check-circle' : 'close-circle'" />
            {{ result.message }}
            <div style="color: #8c8c8c; font-size: 12px;">{{ result.time }}</div>
          </a-timeline-item>
        </a-timeline>
      </a-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MenuTest',
  data() {
    return {
      currentStep: 0,
      testResults: []
    }
  },
  methods: {
    testMenuClick() {
      this.addTestResult('开始测试菜单点击行为', true)
      this.currentStep = 1
      
      // 模拟菜单点击测试
      setTimeout(() => {
        this.addTestResult('菜单配置已更新，OpenInNewTab: true', true)
        this.currentStep = 2
      }, 1000)
      
      setTimeout(() => {
        this.addTestResult('请手动点击左侧菜单验证新窗口打开', true)
        this.currentStep = 3
      }, 2000)
    },

    openFullscreenDesigner() {
      try {
        const url = this.$router.resolve('/page-builder/fullscreen').href
        const newWindow = window.open(url, '_blank', 'width=1400,height=900,scrollbars=no,resizable=yes')
        
        if (newWindow) {
          this.addTestResult('全屏设计器窗口已打开', true)
        } else {
          this.addTestResult('窗口打开失败，可能被浏览器阻止', false)
        }
      } catch (error) {
        this.addTestResult(`打开失败: ${error.message}`, false)
      }
    },

    openNormalDesigner() {
      this.$router.push('/page-builder/designer')
      this.addTestResult('跳转到内嵌设计器', true)
    },

    addTestResult(message, success) {
      this.testResults.push({
        message,
        success,
        time: new Date().toLocaleTimeString()
      })
    }
  },

  mounted() {
    console.log('菜单测试页面已加载')
    this.addTestResult('菜单测试页面初始化完成', true)
  }
}
</script>

<style lang="less" scoped>
.menu-test {
  padding: 20px;
  background: #f0f2f5;
  min-height: 100vh;

  .test-header {
    background: #fff;
    padding: 16px 24px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
      margin: 0;
      color: #1890ff;
    }
  }

  .test-content {
    .ant-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .ant-descriptions {
      .ant-descriptions-item-label {
        font-weight: 500;
        color: #262626;
      }
    }

    .ant-timeline {
      margin-top: 16px;

      .anticon {
        margin-right: 8px;
      }
    }

    .ant-button-group {
      .ant-btn {
        margin-right: 8px;
      }
    }
  }
}
</style>
