<template>
  <div class="designer-toolbar">
    <div class="toolbar-left">
      <div class="toolbar-title">
        <a-icon type="build" />
        <span>页面设计器</span>
      </div>
      <a-divider type="vertical" />
      <div class="toolbar-actions">
        <a-button-group size="small">
          <a-button @click="handleUndo" :disabled="!canUndo">
            <a-icon type="undo" />
            撤销
          </a-button>
          <a-button @click="handleRedo" :disabled="!canRedo">
            <a-icon type="redo" />
            重做
          </a-button>
        </a-button-group>
        
        <a-divider type="vertical" />
        
        <a-button-group size="small">
          <a-button @click="handleCopy" :disabled="!selectedComponent">
            <a-icon type="copy" />
            复制
          </a-button>
          <a-button @click="handlePaste" :disabled="!clipboardComponent">
            <a-icon type="snippets" />
            粘贴
          </a-button>
          <a-button @click="handleDelete" :disabled="!selectedComponent" type="danger">
            <a-icon type="delete" />
            删除
          </a-button>
        </a-button-group>
      </div>
    </div>

    <div class="toolbar-center">
      <div class="zoom-controls">
        <a-button-group size="small">
          <a-button @click="handleZoomOut" :disabled="zoomLevel <= 0.25">
            <a-icon type="zoom-out" />
          </a-button>
          <a-button @click="handleZoomReset">
            {{ Math.round(zoomLevel * 100) }}%
          </a-button>
          <a-button @click="handleZoomIn" :disabled="zoomLevel >= 2">
            <a-icon type="zoom-in" />
          </a-button>
        </a-button-group>
      </div>
    </div>

    <div class="toolbar-right">
      <div class="toolbar-actions">
        <a-button-group size="small">
          <a-button @click="handlePreview">
            <a-icon type="eye" />
            预览
          </a-button>
          <a-button @click="handleSave" type="primary">
            <a-icon type="save" />
            保存
          </a-button>
        </a-button-group>
        
        <a-divider type="vertical" />
        
        <a-dropdown>
          <a-button size="small">
            <a-icon type="more" />
          </a-button>
          <a-menu slot="overlay" @click="handleMenuClick">
            <a-menu-item key="export">
              <a-icon type="export" />
              导出代码
            </a-menu-item>
            <a-menu-item key="import">
              <a-icon type="import" />
              导入配置
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="clear">
              <a-icon type="clear" />
              清空画布
            </a-menu-item>
          </a-menu>
        </a-dropdown>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DesignerToolbar',
  props: {
    canUndo: {
      type: Boolean,
      default: false
    },
    canRedo: {
      type: Boolean,
      default: false
    },
    selectedComponent: {
      type: Object,
      default: null
    },
    clipboardComponent: {
      type: Object,
      default: null
    },
    zoomLevel: {
      type: Number,
      default: 1
    }
  },
  methods: {
    handleUndo() {
      this.$emit('undo')
    },
    handleRedo() {
      this.$emit('redo')
    },
    handleCopy() {
      this.$emit('copy')
    },
    handlePaste() {
      this.$emit('paste')
    },
    handleDelete() {
      this.$emit('delete')
    },
    handleZoomIn() {
      this.$emit('zoom-in')
    },
    handleZoomOut() {
      this.$emit('zoom-out')
    },
    handleZoomReset() {
      this.$emit('zoom-reset')
    },
    handlePreview() {
      this.$emit('preview')
    },
    handleSave() {
      this.$emit('save')
    },
    handleMenuClick({ key }) {
      this.$emit('menu-action', key)
    }
  }
}
</script>

<style lang="less" scoped>
.designer-toolbar {
  height: 48px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

  .toolbar-left,
  .toolbar-right {
    display: flex;
    align-items: center;
  }

  .toolbar-center {
    display: flex;
    align-items: center;
  }

  .toolbar-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    color: #1890ff;

    .anticon {
      margin-right: 8px;
      font-size: 18px;
    }
  }

  .toolbar-actions {
    display: flex;
    align-items: center;
    gap: 8px;

    .ant-btn-group {
      margin-right: 8px;
    }
  }

  .zoom-controls {
    .ant-btn-group .ant-btn {
      min-width: 40px;
    }
  }

  .ant-divider-vertical {
    height: 20px;
    margin: 0 12px;
  }
}
</style>
