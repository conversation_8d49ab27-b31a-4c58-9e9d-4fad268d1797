<template>
  <div class="property-panel">
    <div class="panel-header">
      <h3>属性配置</h3>
    </div>

    <div class="panel-content">
      <!-- 未选中组件时的提示 -->
      <div v-if="!selectedComponent" class="empty-state">
        <a-icon type="setting" />
        <p>请选择一个组件来配置属性</p>
      </div>

      <!-- 选中组件时显示属性配置 -->
      <div v-else class="property-form">
        <!-- 组件基本信息 -->
        <div class="component-info">
          <div class="component-title">
            <a-icon :type="componentConfig.icon || 'component'" />
            <span>{{ componentConfig.displayName }}</span>
          </div>
          <div class="component-id">ID: {{ selectedComponent.id }}</div>
        </div>

        <a-divider />

        <!-- 属性配置表单 -->
        <a-form layout="vertical" :colon="false">
          <!-- 基础属性 -->
          <div class="property-section">
            <h4>基础属性</h4>
            <div
              v-for="(propConfig, propName) in componentConfig.propSchema"
              :key="propName"
              class="property-item"
            >
              <!-- 字符串输入 -->
              <a-form-item
                v-if="propConfig.type === 'string'"
                :label="propConfig.label"
                :required="propConfig.required"
              >
                <a-input
                  v-model="localProps[propName]"
                  :placeholder="propConfig.placeholder"
                  @change="handlePropChange(propName, $event.target.value)"
                />
                <div v-if="propConfig.description" class="prop-description">
                  {{ propConfig.description }}
                </div>
              </a-form-item>

              <!-- 布尔值开关 -->
              <a-form-item
                v-else-if="propConfig.type === 'boolean'"
                :label="propConfig.label"
              >
                <a-switch
                  v-model="localProps[propName]"
                  @change="handlePropChange(propName, $event)"
                />
                <div v-if="propConfig.description" class="prop-description">
                  {{ propConfig.description }}
                </div>
              </a-form-item>

              <!-- 下拉选择 -->
              <a-form-item
                v-else-if="propConfig.type === 'select'"
                :label="propConfig.label"
                :required="propConfig.required"
              >
                <a-select
                  v-model="localProps[propName]"
                  :placeholder="propConfig.placeholder"
                  @change="handlePropChange(propName, $event)"
                >
                  <a-select-option
                    v-for="option in propConfig.options"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-select-option>
                </a-select>
                <div v-if="propConfig.description" class="prop-description">
                  {{ propConfig.description }}
                </div>
              </a-form-item>

              <!-- 数字输入 -->
              <a-form-item
                v-else-if="propConfig.type === 'number'"
                :label="propConfig.label"
                :required="propConfig.required"
              >
                <a-input-number
                  v-model="localProps[propName]"
                  :placeholder="propConfig.placeholder"
                  style="width: 100%"
                  @change="handlePropChange(propName, $event)"
                />
                <div v-if="propConfig.description" class="prop-description">
                  {{ propConfig.description }}
                </div>
              </a-form-item>

              <!-- 复杂类型（暂时显示JSON编辑器） -->
              <a-form-item
                v-else
                :label="propConfig.label"
                :required="propConfig.required"
              >
                <a-textarea
                  v-model="localPropsJson[propName]"
                  :placeholder="`请输入${propConfig.label}的JSON配置`"
                  :rows="3"
                  @change="handleJsonPropChange(propName, $event.target.value)"
                />
                <div v-if="propConfig.description" class="prop-description">
                  {{ propConfig.description }}
                </div>
              </a-form-item>
            </div>
          </div>

          <a-divider />

          <!-- 样式配置 -->
          <div class="property-section">
            <h4>样式配置</h4>
            <a-row :gutter="8">
              <a-col :span="12">
                <a-form-item label="宽度">
                  <a-input
                    v-model="localStyle.width"
                    placeholder="auto"
                    @change="handleStyleChange('width', $event.target.value)"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="高度">
                  <a-input
                    v-model="localStyle.height"
                    placeholder="auto"
                    @change="handleStyleChange('height', $event.target.value)"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="8">
              <a-col :span="12">
                <a-form-item label="外边距">
                  <a-input
                    v-model="localStyle.margin"
                    placeholder="0px"
                    @change="handleStyleChange('margin', $event.target.value)"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="内边距">
                  <a-input
                    v-model="localStyle.padding"
                    placeholder="0px"
                    @change="handleStyleChange('padding', $event.target.value)"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <a-divider />

          <!-- 位置配置 -->
          <div class="property-section">
            <h4>位置配置</h4>
            <a-row :gutter="8">
              <a-col :span="12">
                <a-form-item label="X坐标">
                  <a-input-number
                    v-model="localPosition.x"
                    style="width: 100%"
                    @change="handlePositionChange('x', $event)"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="Y坐标">
                  <a-input-number
                    v-model="localPosition.y"
                    style="width: 100%"
                    @change="handlePositionChange('y', $event)"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script>
import { YYL_COMPONENT_CONFIGS } from '../../configs/yyl-components'

export default {
  name: 'PropertyPanel',
  props: {
    selectedComponent: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      localProps: {},
      localPropsJson: {},
      localStyle: {},
      localPosition: { x: 0, y: 0 }
    }
  },
  computed: {
    componentConfig() {
      if (!this.selectedComponent) return null
      return YYL_COMPONENT_CONFIGS[this.selectedComponent.type] || {}
    }
  },
  watch: {
    selectedComponent: {
      handler(newComponent) {
        if (newComponent) {
          this.initializeLocalData(newComponent)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    initializeLocalData(component) {
      // 初始化属性数据
      this.localProps = { ...component.props }
      
      // 初始化复杂属性的JSON字符串
      this.localPropsJson = {}
      if (this.componentConfig.propSchema) {
        Object.entries(this.componentConfig.propSchema).forEach(([propName, propConfig]) => {
          if (!['string', 'boolean', 'select', 'number'].includes(propConfig.type)) {
            this.localPropsJson[propName] = JSON.stringify(component.props[propName] || null, null, 2)
          }
        })
      }
      
      // 初始化样式数据
      this.localStyle = { ...component.style }
      
      // 初始化位置数据
      this.localPosition = { ...component.position }
    },

    handlePropChange(propName, value) {
      this.localProps[propName] = value
      this.emitUpdate()
    },

    handleJsonPropChange(propName, jsonString) {
      try {
        const value = JSON.parse(jsonString)
        this.localProps[propName] = value
        this.emitUpdate()
      } catch (error) {
        console.warn('JSON格式错误:', error)
      }
    },

    handleStyleChange(styleName, value) {
      this.localStyle[styleName] = value
      this.emitUpdate()
    },

    handlePositionChange(axis, value) {
      this.localPosition[axis] = value
      this.emitUpdate()
    },

    emitUpdate() {
      if (!this.selectedComponent) return

      const updatedComponent = {
        ...this.selectedComponent,
        props: { ...this.localProps },
        style: { ...this.localStyle },
        position: { ...this.localPosition }
      }

      this.$emit('update-component', updatedComponent)
    }
  }
}
</script>

<style lang="less" scoped>
.property-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fafafa;

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fff;

    h3 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #262626;
    }
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;

    .empty-state {
      padding: 40px 20px;
      text-align: center;
      color: #bfbfbf;

      .anticon {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }

    .property-form {
      padding: 16px;

      .component-info {
        .component-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          color: #262626;
          margin-bottom: 8px;

          .anticon {
            margin-right: 8px;
            color: #1890ff;
          }
        }

        .component-id {
          font-size: 12px;
          color: #8c8c8c;
        }
      }

      .property-section {
        margin-bottom: 16px;

        h4 {
          margin: 0 0 12px 0;
          font-size: 13px;
          font-weight: 500;
          color: #595959;
        }

        .property-item {
          margin-bottom: 12px;

          .prop-description {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 4px;
            line-height: 1.4;
          }
        }
      }

      .ant-form-item {
        margin-bottom: 12px;

        .ant-form-item-label {
          padding-bottom: 4px;

          label {
            font-size: 12px;
            color: #595959;
          }
        }
      }

      .ant-divider {
        margin: 16px 0;
      }
    }
  }
}
</style>
