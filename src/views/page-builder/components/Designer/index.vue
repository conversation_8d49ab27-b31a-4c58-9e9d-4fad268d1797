<template>
  <div class="page-designer">
    <!-- 工具栏 -->
    <DesignerToolbar
      :can-undo="canUndo"
      :can-redo="canRedo"
      :selected-component="selectedComponent"
      :clipboard-component="clipboardComponent"
      :zoom-level="zoomLevel"
      @undo="handleUndo"
      @redo="handleRedo"
      @copy="handleCopy"
      @paste="handlePaste"
      @delete="handleDelete"
      @zoom-in="handleZoomIn"
      @zoom-out="handleZoomOut"
      @zoom-reset="handleZoomReset"
      @preview="handlePreview"
      @save="handleSave"
      @menu-action="handleMenuAction"
    />

    <!-- 主工作区域 -->
    <div class="designer-workspace">
      <!-- 左侧组件面板 -->
      <div class="designer-left">
        <ComponentPanel
          @drag-start="handleComponentDragStart"
          @drag-end="handleComponentDragEnd"
        />
      </div>

      <!-- 中间画布区域 -->
      <div class="designer-center">
        <DesignerCanvas
          :components="pageComponents"
          :selected-component-id="selectedComponentId"
          :zoom-level="zoomLevel"
          :canvas-width="canvasWidth"
          :canvas-height="canvasHeight"
          @add-component="handleAddComponent"
          @select-component="handleSelectComponent"
          @edit-component="handleEditComponent"
          @copy-component="handleCopyComponent"
          @delete-component="handleDeleteComponent"
        />
      </div>

      <!-- 右侧属性面板 -->
      <div class="designer-right">
        <PropertyPanel
          :selected-component="selectedComponent"
          @update-component="handleUpdateComponent"
        />
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="designer-loading">
      <div class="loading-content">
        <a-icon type="loading" spin />
        <div class="loading-text">正在加载...</div>
      </div>
    </div>
  </div>
</template>

<script>
import DesignerCanvas from './Canvas.vue'
import ComponentPanel from './ComponentPanel.vue'
import PropertyPanel from './PropertyPanel.vue'
import DesignerToolbar from './Toolbar.vue'

export default {
  name: 'PageDesigner',
  components: {
    DesignerToolbar,
    ComponentPanel,
    DesignerCanvas,
    PropertyPanel
  },
  data() {
    return {
      loading: false,
      // 页面组件列表
      pageComponents: [],
      // 选中的组件ID
      selectedComponentId: null,
      // 剪贴板组件
      clipboardComponent: null,
      // 历史记录
      history: [],
      historyIndex: -1,
      // 画布配置
      zoomLevel: 1,
      canvasWidth: 1200,
      canvasHeight: 800,
      // 拖拽状态
      isDragging: false,
      dragComponent: null
    }
  },
  computed: {
    selectedComponent() {
      if (!this.selectedComponentId) return null
      return this.pageComponents.find(comp => comp.id === this.selectedComponentId)
    },
    canUndo() {
      return this.historyIndex > 0
    },
    canRedo() {
      return this.historyIndex < this.history.length - 1
    }
  },
  methods: {
    // 生成唯一ID
    generateId() {
      return 'comp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    },

    // 添加组件到画布
    handleAddComponent({ componentType, componentConfig, position }) {
      const newComponent = {
        id: this.generateId(),
        type: componentType,
        props: { ...componentConfig.defaultProps },
        style: { ...componentConfig.style },
        position: position || { x: 100, y: 100 }
      }

      this.pageComponents.push(newComponent)
      this.selectedComponentId = newComponent.id
      this.saveToHistory()

      this.$message.success(`已添加 ${componentConfig.displayName}`)
    },

    // 选择组件
    handleSelectComponent(componentId) {
      this.selectedComponentId = componentId
    },

    // 更新组件
    handleUpdateComponent(updatedComponent) {
      const index = this.pageComponents.findIndex(comp => comp.id === updatedComponent.id)
      if (index !== -1) {
        this.$set(this.pageComponents, index, updatedComponent)
        this.saveToHistory()
      }
    },

    // 删除组件
    handleDeleteComponent(componentId) {
      const index = this.pageComponents.findIndex(comp => comp.id === componentId)
      if (index !== -1) {
        this.pageComponents.splice(index, 1)
        if (this.selectedComponentId === componentId) {
          this.selectedComponentId = null
        }
        this.saveToHistory()
        this.$message.success('组件已删除')
      }
    },

    // 复制组件
    handleCopyComponent(component) {
      this.clipboardComponent = { ...component }
      this.$message.success('组件已复制')
    },

    // 编辑组件
    handleEditComponent(component) {
      this.selectedComponentId = component.id
    },

    // 工具栏事件处理
    handleUndo() {
      if (this.canUndo) {
        this.historyIndex--
        this.restoreFromHistory()
      }
    },

    handleRedo() {
      if (this.canRedo) {
        this.historyIndex++
        this.restoreFromHistory()
      }
    },

    handleCopy() {
      if (this.selectedComponent) {
        this.handleCopyComponent(this.selectedComponent)
      }
    },

    handlePaste() {
      if (this.clipboardComponent) {
        const newComponent = {
          ...this.clipboardComponent,
          id: this.generateId(),
          position: {
            x: (this.clipboardComponent.position?.x || 0) + 20,
            y: (this.clipboardComponent.position?.y || 0) + 20
          }
        }
        this.pageComponents.push(newComponent)
        this.selectedComponentId = newComponent.id
        this.saveToHistory()
        this.$message.success('组件已粘贴')
      }
    },

    handleDelete() {
      if (this.selectedComponentId) {
        this.handleDeleteComponent(this.selectedComponentId)
      }
    },

    // 缩放控制
    handleZoomIn() {
      this.zoomLevel = Math.min(this.zoomLevel + 0.1, 2)
    },

    handleZoomOut() {
      this.zoomLevel = Math.max(this.zoomLevel - 0.1, 0.25)
    },

    handleZoomReset() {
      this.zoomLevel = 1
    },

    // 预览和保存
    handlePreview() {
      this.$message.info('预览功能开发中...')
    },

    handleSave() {
      this.$message.success('页面已保存')
    },

    // 菜单操作
    handleMenuAction(action) {
      switch (action) {
        case 'export':
          this.$message.info('导出功能开发中...')
          break
        case 'import':
          this.$message.info('导入功能开发中...')
          break
        case 'clear':
          this.handleClearCanvas()
          break
      }
    },

    // 清空画布
    handleClearCanvas() {
      this.$confirm({
        title: '确认清空画布？',
        content: '此操作将删除画布上的所有组件，且无法恢复。',
        onOk: () => {
          this.pageComponents = []
          this.selectedComponentId = null
          this.saveToHistory()
          this.$message.success('画布已清空')
        }
      })
    },

    // 拖拽事件处理
    handleComponentDragStart(component) {
      this.isDragging = true
      this.dragComponent = component
    },

    handleComponentDragEnd() {
      this.isDragging = false
      this.dragComponent = null
    },

    // 历史记录管理
    saveToHistory() {
      const state = {
        components: JSON.parse(JSON.stringify(this.pageComponents)),
        selectedComponentId: this.selectedComponentId
      }

      // 删除当前位置之后的历史记录
      this.history = this.history.slice(0, this.historyIndex + 1)
      this.history.push(state)
      this.historyIndex = this.history.length - 1

      // 限制历史记录数量
      if (this.history.length > 50) {
        this.history.shift()
        this.historyIndex--
      }
    },

    restoreFromHistory() {
      if (this.historyIndex >= 0 && this.historyIndex < this.history.length) {
        const state = this.history[this.historyIndex]
        this.pageComponents = JSON.parse(JSON.stringify(state.components))
        this.selectedComponentId = state.selectedComponentId
      }
    },

    // 键盘快捷键处理
    handleKeydown(event) {
      // Ctrl+Z 撤销
      if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
        event.preventDefault()
        this.handleUndo()
      }
      // Ctrl+Shift+Z 或 Ctrl+Y 重做
      else if ((event.ctrlKey && event.shiftKey && event.key === 'Z') ||
               (event.ctrlKey && event.key === 'y')) {
        event.preventDefault()
        this.handleRedo()
      }
      // Ctrl+C 复制
      else if (event.ctrlKey && event.key === 'c') {
        event.preventDefault()
        this.handleCopy()
      }
      // Ctrl+V 粘贴
      else if (event.ctrlKey && event.key === 'v') {
        event.preventDefault()
        this.handlePaste()
      }
      // Delete 删除
      else if (event.key === 'Delete') {
        event.preventDefault()
        this.handleDelete()
      }
    }
  },

  mounted() {
    // 初始化历史记录
    this.saveToHistory()

    // 添加键盘快捷键
    document.addEventListener('keydown', this.handleKeydown)
  },

  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown)
  }
}
</script>

<style lang="less">
@import '../../styles/designer.less';
</style>
