<template>
  <div class="component-detail">
    <!-- 组件基本信息 -->
    <div class="detail-section">
      <div class="section-header">
        <div class="component-icon">
          <a-icon :type="component.icon || 'build'" />
        </div>
        <div class="component-basic">
          <h3 class="component-name">{{ component.displayName }}</h3>
          <p class="component-desc">{{ component.description }}</p>
        </div>
      </div>
      
      <div class="component-meta">
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="meta-item">
              <span class="meta-label">分类</span>
              <a-tag :color="getCategoryColor(component.category)">
                {{ getCategoryName(component.category) }}
              </a-tag>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="meta-item">
              <span class="meta-label">使用次数</span>
              <span class="meta-value">{{ usageCount }}</span>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 组件属性 -->
    <div class="detail-section" v-if="component.props && Object.keys(component.props).length > 0">
      <h4 class="section-title">
        <a-icon type="setting" />
        属性配置
      </h4>
      <div class="props-list">
        <div 
          v-for="(prop, propName) in component.props" 
          :key="propName"
          class="prop-item"
        >
          <div class="prop-header">
            <span class="prop-name">{{ propName }}</span>
            <a-tag v-if="prop.required" color="red" size="small">必填</a-tag>
            <a-tag color="blue" size="small">{{ prop.type || 'any' }}</a-tag>
          </div>
          <div class="prop-desc" v-if="prop.description">
            {{ prop.description }}
          </div>
          <div class="prop-default" v-if="prop.default !== undefined">
            <span class="default-label">默认值:</span>
            <code class="default-value">{{ formatDefaultValue(prop.default) }}</code>
          </div>
        </div>
      </div>
    </div>

    <!-- 组件事件 -->
    <div class="detail-section" v-if="component.events && Object.keys(component.events).length > 0">
      <h4 class="section-title">
        <a-icon type="thunderbolt" />
        事件
      </h4>
      <div class="events-list">
        <div 
          v-for="(event, eventName) in component.events" 
          :key="eventName"
          class="event-item"
        >
          <div class="event-header">
            <span class="event-name">@{{ eventName }}</span>
          </div>
          <div class="event-desc" v-if="event.description">
            {{ event.description }}
          </div>
          <div class="event-params" v-if="event.params && event.params.length > 0">
            <span class="params-label">参数:</span>
            <span class="params-value">{{ event.params.join(', ') }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用示例 -->
    <div class="detail-section" v-if="component.example">
      <h4 class="section-title">
        <a-icon type="code" />
        使用示例
      </h4>
      <div class="example-code">
        <pre><code>{{ component.example }}</code></pre>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="detail-actions">
      <a-button 
        type="primary" 
        block 
        size="large"
        @click="handleAddToCanvas"
      >
        <a-icon type="plus" />
        添加到画布
      </a-button>
      
      <a-row :gutter="8" style="margin-top: 8px;">
        <a-col :span="12">
          <a-button 
            block
            @click="handleToggleFavorite"
            :type="isFavorite ? 'primary' : 'default'"
          >
            <a-icon :type="isFavorite ? 'star' : 'star-o'" />
            {{ isFavorite ? '已收藏' : '收藏' }}
          </a-button>
        </a-col>
        <a-col :span="12">
          <a-button block @click="handleCopyCode">
            <a-icon type="copy" />
            复制代码
          </a-button>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ComponentDetail',
  props: {
    component: {
      type: Object,
      required: true
    }
  },
  
  computed: {
    // 使用次数
    usageCount() {
      try {
        return this.$store.state.pageBuilder.components.usageStats[this.component.name] || 0
      } catch (e) {
        return 0
      }
    },
    
    // 是否收藏
    isFavorite() {
      try {
        const favorites = this.$store.state.pageBuilder.components.favoriteComponents || []
        return favorites.includes(this.component.name)
      } catch (e) {
        return false
      }
    }
  },
  
  methods: {
    // 获取分类颜色
    getCategoryColor(category) {
      const colors = {
        business: 'gold',
        form: 'green',
        table: 'blue',
        components: 'purple'
      }
      return colors[category] || 'default'
    },
    
    // 获取分类名称
    getCategoryName(category) {
      const names = {
        business: '业务组件',
        form: '表单组件',
        table: '表格组件',
        components: '通用组件'
      }
      return names[category] || '未知分类'
    },
    
    // 格式化默认值
    formatDefaultValue(value) {
      if (typeof value === 'string') {
        return `"${value}"`
      } else if (typeof value === 'object') {
        return JSON.stringify(value, null, 2)
      }
      return String(value)
    },
    
    // 添加到画布
    handleAddToCanvas() {
      this.$emit('add-component', this.component, { x: 100, y: 100 })
      this.$message.success(`已添加 ${this.component.displayName} 到画布`)
    },
    
    // 切换收藏状态
    handleToggleFavorite() {
      this.$store.dispatch('pageBuilder/components/toggleFavorite', this.component.name)
      const action = this.isFavorite ? '取消收藏' : '收藏'
      this.$message.success(`${action} ${this.component.displayName}`)
    },
    
    // 复制代码
    handleCopyCode() {
      const code = this.generateComponentCode()
      
      // 复制到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(code).then(() => {
          this.$message.success('代码已复制到剪贴板')
        }).catch(() => {
          this.fallbackCopyText(code)
        })
      } else {
        this.fallbackCopyText(code)
      }
    },
    
    // 生成组件代码
    generateComponentCode() {
      const props = this.component.props || {}
      const propsStr = Object.keys(props)
        .map(key => {
          const prop = props[key]
          if (prop.default !== undefined) {
            return `  ${key}="${prop.default}"`
          }
          return `  ${key}=""`
        })
        .join('\n')
      
      return `<${this.component.name}\n${propsStr}\n></${this.component.name}>`
    },
    
    // 备用复制方法
    fallbackCopyText(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        document.execCommand('copy')
        this.$message.success('代码已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
      
      document.body.removeChild(textArea)
    }
  }
}
</script>

<style lang="less" scoped>
.component-detail {
  .detail-section {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  // 组件基本信息
  .section-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    
    .component-icon {
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f5f5;
      border-radius: 8px;
      margin-right: 16px;
      
      .anticon {
        font-size: 24px;
        color: #1890ff;
      }
    }
    
    .component-basic {
      flex: 1;
      
      .component-name {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: #262626;
      }
      
      .component-desc {
        margin: 0;
        color: #8c8c8c;
        line-height: 1.5;
      }
    }
  }
  
  .component-meta {
    .meta-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .meta-label {
        font-size: 12px;
        color: #8c8c8c;
      }
      
      .meta-value {
        font-weight: 500;
        color: #262626;
      }
    }
  }
  
  // 章节标题
  .section-title {
    display: flex;
    align-items: center;
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
    
    .anticon {
      margin-right: 8px;
      color: #1890ff;
    }
  }
  
  // 属性列表
  .props-list {
    .prop-item {
      padding: 12px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .prop-header {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        
        .prop-name {
          font-family: 'Monaco', 'Menlo', monospace;
          font-weight: 500;
          color: #262626;
          margin-right: 8px;
        }
      }
      
      .prop-desc {
        font-size: 12px;
        color: #8c8c8c;
        margin-bottom: 4px;
      }
      
      .prop-default {
        font-size: 12px;
        
        .default-label {
          color: #8c8c8c;
          margin-right: 4px;
        }
        
        .default-value {
          background: #f5f5f5;
          padding: 2px 4px;
          border-radius: 2px;
          font-family: 'Monaco', 'Menlo', monospace;
        }
      }
    }
  }
  
  // 事件列表
  .events-list {
    .event-item {
      padding: 12px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .event-name {
        font-family: 'Monaco', 'Menlo', monospace;
        font-weight: 500;
        color: #722ed1;
      }
      
      .event-desc {
        font-size: 12px;
        color: #8c8c8c;
        margin: 4px 0;
      }
      
      .event-params {
        font-size: 12px;
        
        .params-label {
          color: #8c8c8c;
          margin-right: 4px;
        }
        
        .params-value {
          font-family: 'Monaco', 'Menlo', monospace;
          color: #262626;
        }
      }
    }
  }
  
  // 示例代码
  .example-code {
    background: #f5f5f5;
    border-radius: 6px;
    padding: 12px;
    
    pre {
      margin: 0;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      line-height: 1.5;
      color: #262626;
    }
  }
  
  // 操作按钮
  .detail-actions {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
