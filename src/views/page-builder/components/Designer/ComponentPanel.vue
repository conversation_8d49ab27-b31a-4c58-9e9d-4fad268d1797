<template>
  <div class="component-panel">
    <div class="panel-header">
      <h3>组件库</h3>
      <a-input-search
        v-model="searchText"
        placeholder="搜索组件"
        size="small"
        @change="handleSearch"
      />
    </div>

    <div class="panel-content">
      <a-collapse v-model="activeKey" ghost>
        <!-- 业务组件 -->
        <a-collapse-panel key="business" header="业务组件">
          <div class="component-list">
            <div
              v-for="component in filteredComponents.business"
              :key="component.name"
              class="component-item"
              draggable="true"
              @dragstart="handleDragStart(component, $event)"
              @dragend="handleDragEnd"
            >
              <div class="component-icon">
                <a-icon :type="component.icon || 'component'" />
              </div>
              <div class="component-info">
                <div class="component-name">{{ component.displayName }}</div>
                <div class="component-desc">{{ component.description }}</div>
              </div>
            </div>
          </div>
        </a-collapse-panel>

        <!-- 表单组件 -->
        <a-collapse-panel key="form" header="表单组件">
          <div class="component-list">
            <div
              v-for="component in filteredComponents.form"
              :key="component.name"
              class="component-item"
              draggable="true"
              @dragstart="handleDragStart(component, $event)"
              @dragend="handleDragEnd"
            >
              <div class="component-icon">
                <a-icon :type="component.icon || 'form'" />
              </div>
              <div class="component-info">
                <div class="component-name">{{ component.displayName }}</div>
                <div class="component-desc">{{ component.description }}</div>
              </div>
            </div>
          </div>
        </a-collapse-panel>

        <!-- 表格组件 -->
        <a-collapse-panel key="table" header="表格组件">
          <div class="component-list">
            <div
              v-for="component in filteredComponents.table"
              :key="component.name"
              class="component-item"
              draggable="true"
              @dragstart="handleDragStart(component, $event)"
              @dragend="handleDragEnd"
            >
              <div class="component-icon">
                <a-icon :type="component.icon || 'table'" />
              </div>
              <div class="component-info">
                <div class="component-name">{{ component.displayName }}</div>
                <div class="component-desc">{{ component.description }}</div>
              </div>
            </div>
          </div>
        </a-collapse-panel>

        <!-- 通用组件 -->
        <a-collapse-panel key="components" header="通用组件">
          <div class="component-list">
            <div
              v-for="component in filteredComponents.components"
              :key="component.name"
              class="component-item"
              draggable="true"
              @dragstart="handleDragStart(component, $event)"
              @dragend="handleDragEnd"
            >
              <div class="component-icon">
                <a-icon :type="component.icon || 'appstore'" />
              </div>
              <div class="component-info">
                <div class="component-name">{{ component.displayName }}</div>
                <div class="component-desc">{{ component.description }}</div>
              </div>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<script>
import { YYL_COMPONENT_CONFIGS } from '../../configs/yyl-components'

export default {
  name: 'ComponentPanel',
  data() {
    return {
      searchText: '',
      activeKey: ['business', 'form', 'table', 'components'],
      components: YYL_COMPONENT_CONFIGS
    }
  },
  computed: {
    filteredComponents() {
      const result = {
        business: [],
        form: [],
        table: [],
        components: []
      }

      Object.entries(this.components).forEach(([name, config]) => {
        if (this.matchesSearch(config)) {
          const componentData = {
            name,
            ...config
          }
          result[config.category].push(componentData)
        }
      })

      return result
    }
  },
  methods: {
    matchesSearch(component) {
      if (!this.searchText) return true

      const searchLower = this.searchText.toLowerCase()
      return component.displayName.toLowerCase().includes(searchLower) ||
             component.description.toLowerCase().includes(searchLower)
    },

    handleSearch() {
      // 搜索逻辑已在computed中处理
    },

    handleDragStart(component, event) {
      // 设置拖拽数据
      const dragData = {
        type: 'component',
        componentType: component.name,
        componentConfig: component
      }
      
      event.dataTransfer.setData('text/plain', JSON.stringify(dragData))
      event.dataTransfer.effectAllowed = 'copy'
      
      // 触发拖拽开始事件
      this.$emit('drag-start', component)
    },

    handleDragEnd() {
      this.$emit('drag-end')
    }
  }
}
</script>

<style lang="less" scoped>
.component-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fafafa;

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fff;

    h3 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 500;
      color: #262626;
    }
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;

    .ant-collapse {
      background: transparent;
      border: none;

      .ant-collapse-item {
        border: none;
        margin-bottom: 8px;
        background: #fff;
        border-radius: 6px;
      }

      .ant-collapse-header {
        padding: 12px 16px !important;
        font-weight: 500;
        color: #262626;
      }

      .ant-collapse-content {
        border-top: 1px solid #f0f0f0;
      }

      .ant-collapse-content-box {
        padding: 8px;
      }
    }
  }

  .component-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .component-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: grab;
    transition: all 0.2s;
    border: 1px solid transparent;

    &:hover {
      background: #f5f5f5;
      border-color: #d9d9d9;
    }

    &:active {
      cursor: grabbing;
    }

    .component-icon {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #1890ff;
      color: #fff;
      border-radius: 4px;
      margin-right: 12px;
      flex-shrink: 0;

      .anticon {
        font-size: 16px;
      }
    }

    .component-info {
      flex: 1;
      min-width: 0;

      .component-name {
        font-size: 13px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .component-desc {
        font-size: 12px;
        color: #8c8c8c;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }
}
</style>
