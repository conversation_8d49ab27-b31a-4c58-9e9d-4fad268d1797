<template>
  <div class="component-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <div class="panel-title">
        <a-icon type="appstore" />
        <span>组件库</span>
      </div>
      <div class="panel-actions">
        <a-tooltip title="刷新组件库">
          <a-button
            type="text"
            size="small"
            icon="reload"
            @click="refreshComponents"
            :loading="componentsLoading"
          />
        </a-tooltip>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="search-section">
      <a-input-search
        v-model="searchText"
        placeholder="搜索组件..."
        @search="handleSearch"
        @change="handleSearchChange"
        allowClear
      >
        <a-icon slot="prefix" type="search" />
      </a-input-search>
    </div>

    <!-- 组件统计 -->
    <div class="stats-section" v-if="!searchText">
      <a-row :gutter="8">
        <a-col :span="12">
          <div class="stat-item">
            <span class="stat-number">{{ totalComponents }}</span>
            <span class="stat-label">总计</span>
          </div>
        </a-col>
        <a-col :span="12">
          <div class="stat-item">
            <span class="stat-number">{{ favoriteComponents.length }}</span>
            <span class="stat-label">收藏</span>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 组件分类标签 -->
    <div class="category-tabs">
      <a-tabs
        v-model="activeCategory"
        size="small"
        @change="handleCategoryChange"
        :animated="false"
      >
        <a-tab-pane key="all" tab="全部">
          <template slot="tab">
            <a-icon type="appstore" />
            全部 ({{ totalComponents }})
          </template>
        </a-tab-pane>
        <a-tab-pane key="business" tab="业务">
          <template slot="tab">
            <a-icon type="gold" />
            业务 ({{ getCategoryCount('business') }})
          </template>
        </a-tab-pane>
        <a-tab-pane key="form" tab="表单">
          <template slot="tab">
            <a-icon type="form" />
            表单 ({{ getCategoryCount('form') }})
          </template>
        </a-tab-pane>
        <a-tab-pane key="table" tab="表格">
          <template slot="tab">
            <a-icon type="table" />
            表格 ({{ getCategoryCount('table') }})
          </template>
        </a-tab-pane>
        <a-tab-pane key="components" tab="通用">
          <template slot="tab">
            <a-icon type="build" />
            通用 ({{ getCategoryCount('components') }})
          </template>
        </a-tab-pane>
        <a-tab-pane key="favorite" tab="收藏">
          <template slot="tab">
            <a-icon type="star" />
            收藏 ({{ favoriteComponents.length }})
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 组件列表 -->
    <div class="components-list" ref="componentsList">
      <div v-if="componentsLoading" class="loading-state">
        <a-spin size="large">
          <div class="loading-text">加载组件中...</div>
        </a-spin>
      </div>

      <div v-else-if="displayComponents.length === 0" class="empty-state">
        <a-empty
          description="暂无组件"
        >
          <a-button v-if="!searchText" type="primary" @click="refreshComponents">
            重新加载
          </a-button>
        </a-empty>
      </div>

      <div v-else class="component-grid">
        <div
          v-for="component in displayComponents"
          :key="component.name"
          class="component-item"
          :class="{ 'is-favorite': isFavorite(component.name) }"
          :draggable="true"
          @dragstart="handleDragStart($event, component)"
          @dragend="handleDragEnd"
          @click="handleComponentClick(component)"
        >
          <!-- 组件图标 -->
          <div class="component-icon">
            <a-icon :type="component.icon || 'build'" />
          </div>

          <!-- 组件信息 -->
          <div class="component-info">
            <div class="component-name">{{ component.displayName }}</div>
            <div class="component-desc">{{ component.description }}</div>
          </div>

          <!-- 组件操作 -->
          <div class="component-actions">
            <a-tooltip :title="isFavorite(component.name) ? '取消收藏' : '收藏组件'">
              <a-button
                type="text"
                size="small"
                :class="{ 'is-favorite': isFavorite(component.name) }"
                @click.stop="toggleFavorite(component.name)"
              >
                <a-icon :type="isFavorite(component.name) ? 'star' : 'star-o'" />
              </a-button>
            </a-tooltip>

            <a-tooltip title="查看详情">
              <a-button
                type="text"
                size="small"
                @click.stop="showComponentDetail(component)"
              >
                <a-icon type="eye" />
              </a-button>
            </a-tooltip>
          </div>

          <!-- 使用次数标记 -->
          <div v-if="getUsageCount(component.name) > 0" class="usage-badge">
            {{ getUsageCount(component.name) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 组件详情抽屉 -->
    <a-drawer
      title="组件详情"
      :visible="detailDrawerVisible"
      @close="detailDrawerVisible = false"
      width="400"
    >
      <component-detail
        v-if="selectedComponent"
        :component="selectedComponent"
        @add-component="handleAddComponent"
      />
    </a-drawer>
  </div>
</template>


<script>
export default {
  name: 'ComponentPanel',

  data() {
    return {
      // 搜索相关
      searchText: '',

      // 分类相关
      activeCategory: 'all',

      // 详情抽屉
      detailDrawerVisible: false,
      selectedComponent: null,

      // 拖拽状态
      draggedComponent: null
    }
  },

  computed: {
    // 组件库状态
    componentsLoading() {
      try {
        return this.$store.getters['pageBuilder/components/isLoading'] || false
      } catch (e) {
        return false
      }
    },

    totalComponents() {
      try {
        return this.$store.getters['pageBuilder/components/totalComponents'] || 0
      } catch (e) {
        return 0
      }
    },

    allComponents() {
      try {
        return this.$store.getters['pageBuilder/components/allComponents'] || {}
      } catch (e) {
        return {}
      }
    },

    favoriteComponents() {
      try {
        return this.$store.state.pageBuilder.components.favoriteComponents || []
      } catch (e) {
        return []
      }
    },

    // 显示的组件列表
    displayComponents() {
      let components = []

      if (this.activeCategory === 'all') {
        // 显示所有组件
        components = Object.values(this.allComponents)
      } else if (this.activeCategory === 'favorite') {
        // 显示收藏的组件
        components = this.favoriteComponents
          .map(name => this.allComponents[name])
          .filter(Boolean)
      } else {
        // 显示特定分类的组件
        try {
          components = this.$store.getters['pageBuilder/components/componentsByCategory'](this.activeCategory) || []
        } catch (e) {
          components = []
        }
      }

      // 应用搜索过滤
      if (this.searchText) {
        const searchLower = this.searchText.toLowerCase()
        components = components.filter(component => {
          return component.displayName.toLowerCase().includes(searchLower) ||
                 component.description.toLowerCase().includes(searchLower) ||
                 component.name.toLowerCase().includes(searchLower)
        })
      }

      return components
    }
  },

  methods: {
    // 获取分类组件数量
    getCategoryCount(category) {
      try {
        const components = this.$store.getters['pageBuilder/components/componentsByCategory'](category)
        return components ? components.length : 0
      } catch (e) {
        return 0
      }
    },

    // 获取组件使用次数
    getUsageCount(componentName) {
      try {
        return this.$store.state.pageBuilder.components.usageStats[componentName] || 0
      } catch (e) {
        return 0
      }
    },

    // 检查是否收藏
    isFavorite(componentName) {
      return this.favoriteComponents.includes(componentName)
    },

    // 切换收藏状态
    toggleFavorite(componentName) {
      this.$store.dispatch('pageBuilder/components/toggleFavorite', componentName)
    },

    // 搜索处理
    handleSearch(value) {
      this.searchText = value
    },

    handleSearchChange(e) {
      this.searchText = e.target.value
      // 实时搜索
      this.$store.dispatch('pageBuilder/components/searchComponents', this.searchText)
    },

    // 分类切换
    handleCategoryChange(activeKey) {
      this.activeCategory = activeKey
    },

    // 刷新组件库
    async refreshComponents() {
      try {
        await this.$store.dispatch('pageBuilder/components/reloadComponents')
        this.$message.success('组件库刷新成功')
      } catch (error) {
        this.$message.error('组件库刷新失败')
        console.error('刷新组件库失败:', error)
      }
    },

    // 组件点击
    handleComponentClick(component) {
      // 记录使用统计
      this.$store.dispatch('pageBuilder/components/recordComponentUsage', component.name)
    },

    // 显示组件详情
    showComponentDetail(component) {
      this.selectedComponent = component
      this.detailDrawerVisible = true
    },

    // 拖拽开始
    handleDragStart(event, component) {
      this.draggedComponent = component

      // 准备拖拽数据
      const dragData = {
        type: 'component',
        componentType: component.name,
        componentConfig: component
      }

      const dragDataString = JSON.stringify(dragData)

      // 设置多种格式的拖拽数据，确保兼容性
      event.dataTransfer.setData('application/json', dragDataString)
      event.dataTransfer.setData('text/plain', dragDataString)

      // 设置拖拽效果
      event.dataTransfer.effectAllowed = 'copy'

      // 通知设计器开始拖拽
      this.$emit('drag-start', {
        type: 'component',
        component: component
      })

      console.log('开始拖拽组件:', component.displayName)
      console.log('拖拽数据:', dragData)
    },

    // 拖拽结束
    handleDragEnd(event) {
      this.draggedComponent = null

      // 通知设计器拖拽结束
      this.$emit('drag-end')

      console.log('拖拽结束')
    },

    // 添加组件到画布
    handleAddComponent(component, position) {
      this.$emit('add-component', {
        componentType: component.name,
        componentConfig: component,
        position: position || { x: 100, y: 100 }
      })

      // 记录使用统计
      this.$store.dispatch('pageBuilder/components/recordComponentUsage', component.name)

      // 关闭详情抽屉
      this.detailDrawerVisible = false
    }
  },

  mounted() {
    console.log('ComponentPanel mounted')
  }
}
</script>

<style lang="less" scoped>
.component-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fafafa;

  // 面板头部
  .panel-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .panel-title {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      color: #262626;

      .anticon {
        margin-right: 8px;
        color: #1890ff;
      }
    }

    .panel-actions {
      .ant-btn {
        border: none;
        box-shadow: none;
      }
    }
  }

  // 搜索区域
  .search-section {
    padding: 12px 16px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;

    .ant-input-search {
      .ant-input {
        border-radius: 6px;
      }
    }
  }

  // 统计区域
  .stats-section {
    padding: 8px 16px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;

    .stat-item {
      text-align: center;
      padding: 4px;

      .stat-number {
        display: block;
        font-size: 16px;
        font-weight: 600;
        color: #1890ff;
      }

      .stat-label {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }

  // 分类标签
  .category-tabs {
    background: #fff;
    border-bottom: 1px solid #f0f0f0;

    .ant-tabs {
      .ant-tabs-bar {
        margin: 0;
        border-bottom: 1px solid #f0f0f0;
      }

      .ant-tabs-tab {
        padding: 8px 12px;
        font-size: 12px;

        .anticon {
          margin-right: 4px;
        }
      }
    }
  }

  // 组件列表
  .components-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;

    // 加载状态
    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;

      .loading-text {
        margin-top: 16px;
        color: #8c8c8c;
        font-size: 14px;
      }
    }

    // 空状态
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
    }

    // 组件网格
    .component-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 8px;

      .component-item {
        position: relative;
        background: #fff;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        padding: 12px;
        cursor: grab;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
          transform: translateY(-1px);
        }

        &:active {
          cursor: grabbing;
        }

        &.is-favorite {
          border-color: #faad14;

          .component-actions .is-favorite {
            color: #faad14;
          }
        }

        // 组件图标
        .component-icon {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f5f5f5;
          border-radius: 4px;
          margin-right: 12px;

          .anticon {
            font-size: 16px;
            color: #1890ff;
          }
        }

        // 组件信息
        .component-info {
          flex: 1;
          min-width: 0;

          .component-name {
            font-size: 13px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .component-desc {
            font-size: 11px;
            color: #8c8c8c;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        // 组件操作
        .component-actions {
          display: flex;
          opacity: 0;
          transition: opacity 0.2s ease;

          .ant-btn {
            border: none;
            box-shadow: none;
            padding: 4px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;

            .anticon {
              font-size: 12px;
            }

            &.is-favorite {
              color: #faad14;
            }
          }
        }

        &:hover .component-actions {
          opacity: 1;
        }

        // 使用次数标记
        .usage-badge {
          position: absolute;
          top: -6px;
          right: -6px;
          background: #ff4d4f;
          color: #fff;
          border-radius: 50%;
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          font-weight: 500;
        }
      }
    }
  }
}
</style>

