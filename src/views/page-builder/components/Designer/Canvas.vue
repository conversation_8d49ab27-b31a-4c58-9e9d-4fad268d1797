<template>
  <div class="canvas-container">
    <div class="canvas-header">
      <div class="canvas-info">
        <span>画布尺寸: {{ canvasWidth }} × {{ canvasHeight }}</span>
        <a-divider type="vertical" />
        <span>缩放: {{ Math.round(zoomLevel * 100) }}%</span>
      </div>
      <div class="canvas-actions">
        <a-button-group size="small">
          <a-button @click="toggleGrid" :type="showGrid ? 'primary' : 'default'">
            <a-icon type="border" />
            网格
          </a-button>
          <a-button @click="toggleRuler" :type="showRuler ? 'primary' : 'default'">
            <a-icon type="column-width" />
            标尺
          </a-button>
        </a-button-group>
      </div>
    </div>

    <div class="canvas-workspace" ref="workspace">
      <!-- 标尺 -->
      <div v-if="showRuler" class="ruler-horizontal"></div>
      <div v-if="showRuler" class="ruler-vertical"></div>

      <!-- 画布区域 -->
      <div
        class="canvas-area"
        :style="canvasStyle"
        @drop="handleDrop"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave"
        @click="handleCanvasClick"
        ref="canvas"
      >
        <!-- 网格背景 -->
        <div v-if="showGrid" class="canvas-grid" :style="gridStyle"></div>

        <!-- 拖拽提示 -->
        <div v-if="isDragOver" class="drop-indicator">
          <a-icon type="plus-circle" />
          <span>释放以添加组件</span>
        </div>

        <!-- 空状态提示 -->
        <div v-if="components.length === 0 && !isDragOver" class="empty-canvas">
          <a-icon type="drag" />
          <h3>开始设计你的页面</h3>
          <p>从左侧组件面板拖拽组件到这里</p>
        </div>

        <!-- 渲染组件 -->
        <div
          v-for="component in components"
          :key="component.id"
          class="canvas-component"
          :class="{
            'selected': selectedComponentId === component.id,
            'hover': hoverComponentId === component.id
          }"
          :style="getComponentStyle(component)"
          @click.stop="handleComponentClick(component)"
          @mouseenter="handleComponentMouseEnter(component)"
          @mouseleave="handleComponentMouseLeave"
        >
          <!-- 组件选中边框 -->
          <div v-if="selectedComponentId === component.id" class="selection-border">
            <div class="selection-handles">
              <div class="handle handle-nw"></div>
              <div class="handle handle-ne"></div>
              <div class="handle handle-sw"></div>
              <div class="handle handle-se"></div>
            </div>
            <div class="selection-toolbar">
              <a-button size="small" type="link" @click.stop="handleComponentEdit(component)">
                <a-icon type="edit" />
              </a-button>
              <a-button size="small" type="link" @click.stop="handleComponentCopy(component)">
                <a-icon type="copy" />
              </a-button>
              <a-button size="small" type="link" @click.stop="handleComponentDelete(component)">
                <a-icon type="delete" />
              </a-button>
            </div>
          </div>

          <!-- 组件内容 -->
          <div class="component-content">
            <!-- 暂时使用简单的占位符来显示组件 -->
            <div class="component-placeholder" :class="`component-${component.type}`">
              <div class="placeholder-header">
                <a-icon :type="getComponentIcon(component.type)" />
                <span>{{ getComponentDisplayName(component.type) }}</span>
              </div>
              <div class="placeholder-content">
                <!-- 根据组件类型显示不同的预览内容 -->
                <div v-if="component.type === 'YYLButton'" class="button-preview">
                  <a-button
                    :type="component.props.type"
                    :size="component.props.size"
                    :loading="component.props.loading"
                    :disabled="component.props.disabled"
                  >
                    {{ component.props.text || '按钮' }}
                  </a-button>
                </div>
                <div v-else-if="component.type === 'YYLForm'" class="form-preview">
                  <a-form layout="horizontal">
                    <a-form-item label="示例字段">
                      <a-input placeholder="这是一个表单预览" />
                    </a-form-item>
                  </a-form>
                </div>
                <div v-else-if="component.type === 'YYLTable'" class="table-preview">
                  <a-table
                    :columns="[{title: '列1', dataIndex: 'col1'}, {title: '列2', dataIndex: 'col2'}]"
                    :data-source="[{col1: '数据1', col2: '数据2'}]"
                    size="small"
                    :pagination="false"
                  />
                </div>
                <div v-else class="generic-preview">
                  <p>{{ component.type }} 组件预览</p>
                  <small>属性: {{ Object.keys(component.props).length }} 个</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DesignerCanvas',
  props: {
    components: {
      type: Array,
      default: () => []
    },
    selectedComponentId: {
      type: String,
      default: null
    },
    zoomLevel: {
      type: Number,
      default: 1
    },
    canvasWidth: {
      type: Number,
      default: 1200
    },
    canvasHeight: {
      type: Number,
      default: 800
    },
    showGrid: {
      type: Boolean,
      default: true
    },
    showRuler: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isDragOver: false,
      hoverComponentId: null,
      gridSize: 10
    }
  },
  computed: {
    canvasStyle() {
      return {
        width: `${this.canvasWidth}px`,
        height: `${this.canvasHeight}px`,
        transform: `scale(${this.zoomLevel})`,
        transformOrigin: 'top left'
      }
    },
    gridStyle() {
      return {
        backgroundSize: `${this.gridSize}px ${this.gridSize}px`,
        backgroundImage: `
          linear-gradient(to right, #e8e8e8 1px, transparent 1px),
          linear-gradient(to bottom, #e8e8e8 1px, transparent 1px)
        `
      }
    }
  },
  methods: {
    handleDragOver(event) {
      event.preventDefault()
      event.dataTransfer.dropEffect = 'copy'
    },
    handleDragEnter(event) {
      event.preventDefault()
      this.isDragOver = true
    },
    handleDragLeave(event) {
      // 只有当离开画布区域时才隐藏提示
      if (!this.$refs.canvas.contains(event.relatedTarget)) {
        this.isDragOver = false
      }
    },
    handleDrop(event) {
      event.preventDefault()
      this.isDragOver = false

      try {
        const dragData = JSON.parse(event.dataTransfer.getData('text/plain'))
        if (dragData.type === 'component') {
          const rect = this.$refs.canvas.getBoundingClientRect()
          const x = (event.clientX - rect.left) / this.zoomLevel
          const y = (event.clientY - rect.top) / this.zoomLevel

          this.$emit('add-component', {
            componentType: dragData.componentType,
            componentConfig: dragData.componentConfig,
            position: { x, y }
          })
        }
      } catch (error) {
        console.error('处理拖拽数据失败:', error)
      }
    },
    handleCanvasClick() {
      this.$emit('select-component', null)
    },
    handleComponentClick(component) {
      this.$emit('select-component', component.id)
    },
    handleComponentMouseEnter(component) {
      this.hoverComponentId = component.id
    },
    handleComponentMouseLeave() {
      this.hoverComponentId = null
    },
    handleComponentEdit(component) {
      this.$emit('edit-component', component)
    },
    handleComponentCopy(component) {
      this.$emit('copy-component', component)
    },
    handleComponentDelete(component) {
      this.$emit('delete-component', component.id)
    },
    getComponentType(component) {
      // 返回组件类型，这里暂时返回一个占位符
      return 'div'
    },

    getComponentIcon(componentType) {
      // 从YYL组件配置中获取图标，如果没有则使用默认图标
      const componentConfig = this.getComponentConfig(componentType)
      return (componentConfig && componentConfig.icon) || 'component'
    },

    getComponentDisplayName(componentType) {
      // 从YYL组件配置中获取显示名称
      const componentConfig = this.getComponentConfig(componentType)
      return (componentConfig && componentConfig.displayName) || componentType
    },

    getComponentConfig(componentType) {
      // 导入YYL组件配置
      try {
        // 这里应该从store或props中获取，暂时使用简单映射
        const configs = {
          'YYLButton': {
            displayName: 'YYL按钮',
            icon: 'button'
          },
          'YYLForm': {
            displayName: 'YYL表单',
            icon: 'form'
          },
          'YYLTable': {
            displayName: 'YYL表格',
            icon: 'table'
          }
        }
        return configs[componentType] || null
      } catch (error) {
        console.error('获取组件配置失败:', error)
        return null
      }
    },
    getComponentStyle(component) {
      return {
        position: 'absolute',
        left: `${(component.position && component.position.x) || 0}px`,
        top: `${(component.position && component.position.y) || 0}px`,
        ...component.style
      }
    },

    // 切换网格显示
    toggleGrid() {
      this.$emit('toggle-grid')
    },

    // 切换标尺显示
    toggleRuler() {
      this.$emit('toggle-ruler')
    }
  }
}
</script>

<style lang="less" scoped>
.canvas-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  .canvas-header {
    height: 40px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;

    .canvas-info {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .canvas-workspace {
    flex: 1;
    position: relative;
    overflow: auto;
    padding: 20px;

    .canvas-area {
      position: relative;
      background: #fff;
      border: 1px solid #d9d9d9;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      margin: 0 auto;

      .canvas-grid {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        opacity: 0.5;
      }

      .drop-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #1890ff;
        font-size: 16px;
        pointer-events: none;

        .anticon {
          font-size: 48px;
          margin-bottom: 8px;
          display: block;
        }
      }

      .empty-canvas {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #bfbfbf;

        .anticon {
          font-size: 64px;
          margin-bottom: 16px;
          display: block;
        }

        h3 {
          margin: 0 0 8px 0;
          color: #8c8c8c;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }
  }

  .canvas-component {
    position: relative;
    cursor: pointer;

    &.hover {
      outline: 1px dashed #1890ff;
    }

    &.selected {
      outline: 2px solid #1890ff;
    }

    .selection-border {
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border: 2px solid #1890ff;
      pointer-events: none;

      .selection-handles {
        .handle {
          position: absolute;
          width: 8px;
          height: 8px;
          background: #1890ff;
          border: 1px solid #fff;
          pointer-events: auto;
          cursor: nw-resize;

          &.handle-nw { top: -4px; left: -4px; }
          &.handle-ne { top: -4px; right: -4px; cursor: ne-resize; }
          &.handle-sw { bottom: -4px; left: -4px; cursor: sw-resize; }
          &.handle-se { bottom: -4px; right: -4px; cursor: se-resize; }
        }
      }

      .selection-toolbar {
        position: absolute;
        top: -32px;
        right: 0;
        background: #1890ff;
        border-radius: 4px;
        padding: 2px;
        pointer-events: auto;

        .ant-btn {
          color: #fff;
          border: none;
          padding: 0 4px;
          height: 24px;
          line-height: 24px;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
          }
        }
      }
    }

    .component-content {
      position: relative;
      z-index: 1;

      .component-placeholder {
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        background: #fff;
        min-height: 60px;
        overflow: hidden;

        .placeholder-header {
          background: #f5f5f5;
          padding: 8px 12px;
          border-bottom: 1px solid #e8e8e8;
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #666;

          .anticon {
            margin-right: 6px;
            color: #1890ff;
          }
        }

        .placeholder-content {
          padding: 12px;

          .button-preview {
            .ant-btn {
              pointer-events: none;
            }
          }

          .form-preview {
            .ant-form-item {
              margin-bottom: 8px;
            }

            .ant-input {
              pointer-events: none;
            }
          }

          .table-preview {
            .ant-table {
              pointer-events: none;
            }
          }

          .generic-preview {
            text-align: center;
            color: #8c8c8c;
            padding: 20px;

            p {
              margin: 0 0 8px 0;
              font-size: 14px;
            }

            small {
              font-size: 12px;
            }
          }
        }

        &.component-YYLButton {
          .placeholder-header {
            background: #e6f7ff;
            color: #1890ff;
          }
        }

        &.component-YYLForm {
          .placeholder-header {
            background: #f6ffed;
            color: #52c41a;
          }
        }

        &.component-YYLTable {
          .placeholder-header {
            background: #fff2e8;
            color: #fa8c16;
          }
        }
      }
    }
  }
}
</style>
