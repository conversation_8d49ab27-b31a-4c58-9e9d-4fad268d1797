<template>
  <div class="fullscreen-designer">
    <!-- 顶部工具栏 -->
    <div class="designer-header">
      <div class="header-left">
        <div class="logo-section">
          <a-icon type="build" />
          <span class="title">YYL页面设计器</span>
        </div>
        <a-divider type="vertical" />
        <div class="page-info">
          <span>{{ currentPageName || '未命名页面' }}</span>
          <a-button type="link" size="small" @click="showPageSettings">
            <a-icon type="edit" />
          </a-button>
        </div>
      </div>

      <div class="header-center">
        <div class="toolbar-actions">
          <a-button-group size="small">
            <a-button @click="handleUndo" :disabled="!canUndo" title="撤销 (Ctrl+Z)">
              <a-icon type="undo" />
            </a-button>
            <a-button @click="handleRedo" :disabled="!canRedo" title="重做 (Ctrl+Y)">
              <a-icon type="redo" />
            </a-button>
          </a-button-group>

          <a-divider type="vertical" />

          <div class="zoom-controls">
            <a-button-group size="small">
              <a-button @click="handleZoomOut" :disabled="zoomLevel <= 0.25" title="缩小">
                <a-icon type="zoom-out" />
              </a-button>
              <a-button @click="handleZoomReset" title="重置缩放">
                {{ Math.round(zoomLevel * 100) }}%
              </a-button>
              <a-button @click="handleZoomIn" :disabled="zoomLevel >= 2" title="放大">
                <a-icon type="zoom-in" />
              </a-button>
            </a-button-group>
          </div>

          <a-divider type="vertical" />

          <a-button-group size="small">
            <a-button @click="toggleGrid" :type="showGrid ? 'primary' : 'default'" title="网格">
              <a-icon type="border" />
            </a-button>
            <a-button @click="toggleRuler" :type="showRuler ? 'primary' : 'default'" title="标尺">
              <a-icon type="column-width" />
            </a-button>
          </a-button-group>
        </div>
      </div>

      <div class="header-right">
        <a-button-group size="small">
          <a-button @click="toggleComponentPanel" :type="showComponentPanel ? 'primary' : 'default'" title="组件面板">
            <a-icon type="appstore" />
            组件
          </a-button>
          <a-button @click="togglePropertyPanel" :type="showPropertyPanel ? 'primary' : 'default'" title="属性面板">
            <a-icon type="setting" />
            属性
          </a-button>
        </a-button-group>

        <a-divider type="vertical" />

        <a-button-group size="small">
          <a-button @click="handlePreview" title="预览">
            <a-icon type="eye" />
            预览
          </a-button>
          <a-button @click="handleSave" type="primary" title="保存">
            <a-icon type="save" />
            保存
          </a-button>
        </a-button-group>

        <a-divider type="vertical" />

        <a-button @click="handleClose" title="关闭设计器">
          <a-icon type="close" />
        </a-button>
      </div>
    </div>

    <!-- 主工作区域 -->
    <div class="designer-workspace">
      <!-- 左侧组件面板（可收起） -->
      <div
        v-show="showComponentPanel"
        class="component-panel-container"
        :class="{ 'panel-collapsed': !showComponentPanel }"
      >
        <ComponentPanel
          @drag-start="handleComponentDragStart"
          @drag-end="handleComponentDragEnd"
        />
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-container" :class="{ 'full-width': !showComponentPanel }">
        <DesignerCanvas
          :components="pageComponents"
          :selected-component-id="selectedComponentId"
          :zoom-level="zoomLevel"
          :canvas-width="canvasWidth"
          :canvas-height="canvasHeight"
          :show-grid="showGrid"
          :show-ruler="showRuler"
          @add-component="handleAddComponent"
          @select-component="handleSelectComponent"
          @edit-component="handleEditComponent"
          @copy-component="handleCopyComponent"
          @delete-component="handleDeleteComponent"
        />
      </div>
    </div>

    <!-- 右侧属性面板（抽屉式） -->
    <a-drawer
      title="属性配置"
      placement="right"
      :closable="true"
      :visible="showPropertyPanel"
      :width="360"
      @close="togglePropertyPanel"
      :mask="false"
      :get-container="false"
      :style="{ position: 'absolute' }"
    >
      <PropertyPanel
        :selected-component="selectedComponent"
        @update-component="handleUpdateComponent"
      />
    </a-drawer>

    <!-- 页面设置弹窗 -->
    <a-modal
      title="页面设置"
      :visible="pageSettingsVisible"
      @ok="handlePageSettingsOk"
      @cancel="pageSettingsVisible = false"
      width="500px"
    >
      <a-form layout="vertical">
        <a-form-item label="页面名称">
          <a-input v-model="currentPageName" placeholder="请输入页面名称" />
        </a-form-item>
        <a-form-item label="页面描述">
          <a-textarea v-model="currentPageDesc" placeholder="请输入页面描述" :rows="3" />
        </a-form-item>
        <a-form-item label="画布尺寸">
          <a-row :gutter="8">
            <a-col :span="12">
              <a-input-number v-model="canvasWidth" :min="800" :max="2000" style="width: 100%" />
              <small>宽度 (px)</small>
            </a-col>
            <a-col :span="12">
              <a-input-number v-model="canvasHeight" :min="600" :max="1500" style="width: 100%" />
              <small>高度 (px)</small>
            </a-col>
          </a-row>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import DesignerCanvas from './Canvas.vue'
import ComponentPanel from './ComponentPanel.vue'
import PropertyPanel from './PropertyPanel.vue'

export default {
  name: 'FullscreenDesigner',
  components: {
    ComponentPanel,
    DesignerCanvas,
    PropertyPanel
  },
  data() {
    return {
      // 页面信息
      currentPageName: '新页面',
      currentPageDesc: '',
      pageSettingsVisible: false,

      // 面板显示状态
      showComponentPanel: true,
      showPropertyPanel: false,
      showGrid: true,
      showRuler: false,

      // 页面组件和状态
      pageComponents: [],
      selectedComponentId: null,
      clipboardComponent: null,

      // 历史记录
      history: [],
      historyIndex: -1,

      // 画布配置
      zoomLevel: 1,
      canvasWidth: 1200,
      canvasHeight: 800,

      // 拖拽状态
      isDragging: false,
      dragComponent: null
    }
  },
  computed: {
    selectedComponent() {
      if (!this.selectedComponentId) return null
      return this.pageComponents.find(comp => comp.id === this.selectedComponentId)
    },
    canUndo() {
      return this.historyIndex > 0
    },
    canRedo() {
      return this.historyIndex < this.history.length - 1
    }
  },
  methods: {
    // 面板切换
    toggleComponentPanel() {
      this.showComponentPanel = !this.showComponentPanel
    },
    togglePropertyPanel() {
      this.showPropertyPanel = !this.showPropertyPanel
    },
    toggleGrid() {
      this.showGrid = !this.showGrid
    },
    toggleRuler() {
      this.showRuler = !this.showRuler
    },

    // 页面设置
    showPageSettings() {
      this.pageSettingsVisible = true
    },
    handlePageSettingsOk() {
      this.pageSettingsVisible = false
      this.$message.success('页面设置已更新')
    },

    // 工具栏操作
    handleUndo() {
      // TODO: 实现撤销逻辑
      this.$message.info('撤销功能开发中...')
    },
    handleRedo() {
      // TODO: 实现重做逻辑
      this.$message.info('重做功能开发中...')
    },
    handleZoomIn() {
      this.zoomLevel = Math.min(this.zoomLevel + 0.1, 2)
    },
    handleZoomOut() {
      this.zoomLevel = Math.max(this.zoomLevel - 0.1, 0.25)
    },
    handleZoomReset() {
      this.zoomLevel = 1
    },
    handlePreview() {
      this.$message.info('预览功能开发中...')
    },
    handleSave() {
      this.$message.success('页面已保存')
    },
    handleClose() {
      this.$confirm({
        title: '确认关闭设计器？',
        content: '未保存的更改将会丢失',
        onOk: () => {
          window.close()
        }
      })
    },

    // 组件操作（继承自原设计器）
    generateId() {
      return 'comp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    },
    handleAddComponent({ componentType, componentConfig, position }) {
      const newComponent = {
        id: this.generateId(),
        type: componentType,
        props: { ...componentConfig.defaultProps },
        style: { ...componentConfig.style },
        position: position || { x: 100, y: 100 }
      }

      this.pageComponents.push(newComponent)
      this.selectedComponentId = newComponent.id
      this.showPropertyPanel = true // 自动打开属性面板

      this.$message.success(`已添加 ${componentConfig.displayName}`)
    },
    handleSelectComponent(componentId) {
      this.selectedComponentId = componentId
      if (componentId) {
        this.showPropertyPanel = true // 选中组件时自动打开属性面板
      }
    },
    handleUpdateComponent(updatedComponent) {
      const index = this.pageComponents.findIndex(comp => comp.id === updatedComponent.id)
      if (index !== -1) {
        this.$set(this.pageComponents, index, updatedComponent)
      }
    },
    handleDeleteComponent(componentId) {
      const index = this.pageComponents.findIndex(comp => comp.id === componentId)
      if (index !== -1) {
        this.pageComponents.splice(index, 1)
        if (this.selectedComponentId === componentId) {
          this.selectedComponentId = null
          this.showPropertyPanel = false
        }
        this.$message.success('组件已删除')
      }
    },
    handleCopyComponent(component) {
      this.clipboardComponent = { ...component }
      this.$message.success('组件已复制')
    },
    handleEditComponent(component) {
      this.selectedComponentId = component.id
      this.showPropertyPanel = true
    },
    handleComponentDragStart(component) {
      this.isDragging = true
      this.dragComponent = component
    },
    handleComponentDragEnd() {
      this.isDragging = false
      this.dragComponent = null
    },

    // 键盘快捷键处理
    handleKeydown(event) {
      // 键盘快捷键处理
      if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
        event.preventDefault()
        this.handleUndo()
      } else if ((event.ctrlKey && event.shiftKey && event.key === 'Z') ||
                 (event.ctrlKey && event.key === 'y')) {
        event.preventDefault()
        this.handleRedo()
      }
    }
  },
  mounted() {
    // 设置全屏样式
    document.body.style.overflow = 'hidden'

    // 添加键盘快捷键
    document.addEventListener('keydown', this.handleKeydown)

    console.log('全屏设计器已加载')
  },
  beforeDestroy() {
    // 恢复页面样式
    document.body.style.overflow = ''
    document.removeEventListener('keydown', this.handleKeydown)
  }
}
</script>

<style lang="less" scoped>
.fullscreen-designer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f0f2f5;
  display: flex;
  flex-direction: column;
  z-index: 1000;

  .designer-header {
    height: 56px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;

    .header-left {
      display: flex;
      align-items: center;

      .logo-section {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: #1890ff;

        .anticon {
          margin-right: 8px;
          font-size: 18px;
        }
      }

      .page-info {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #262626;
      }
    }

    .header-center {
      display: flex;
      align-items: center;

      .toolbar-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .zoom-controls .ant-btn {
          min-width: 50px;
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
    }

    .ant-divider-vertical {
      height: 20px;
      margin: 0 12px;
    }
  }

  .designer-workspace {
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden;

    .component-panel-container {
      width: 280px;
      background: #fafafa;
      border-right: 1px solid #e8e8e8;
      flex-shrink: 0;
      transition: all 0.3s ease;

      &.panel-collapsed {
        width: 0;
        overflow: hidden;
      }
    }

    .canvas-container {
      flex: 1;
      background: #f5f5f5;
      transition: all 0.3s ease;

      &.full-width {
        margin-left: 0;
      }
    }
  }
}

// 抽屉样式覆盖
:global(.ant-drawer-content-wrapper) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15) !important;
}

:global(.ant-drawer-body) {
  padding: 16px !important;
}
</style>
