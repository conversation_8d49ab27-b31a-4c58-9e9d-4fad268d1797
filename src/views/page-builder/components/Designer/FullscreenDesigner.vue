<template>
  <div class="fullscreen-designer">
    <!-- 顶部工具栏 -->
    <div class="designer-header">
      <div class="header-left">
        <div class="logo-section">
          <a-icon type="build" />
          <span class="title">YYL页面设计器</span>
        </div>
        <a-divider type="vertical" />
        <div class="page-info">
          <span>{{ currentPageName || '未命名页面' }}</span>
          <a-button type="link" size="small" @click="showPageSettings">
            <a-icon type="edit" />
          </a-button>
        </div>
      </div>

      <div class="header-center">
        <div class="toolbar-actions">
          <a-button-group size="small">
            <a-button @click="handleUndo" :disabled="!canUndo" title="撤销 (Ctrl+Z)">
              <a-icon type="undo" />
            </a-button>
            <a-button @click="handleRedo" :disabled="!canRedo" title="重做 (Ctrl+Y)">
              <a-icon type="redo" />
            </a-button>
          </a-button-group>

          <a-divider type="vertical" />

          <div class="zoom-controls">
            <a-button-group size="small">
              <a-button @click="handleZoomOut" :disabled="zoomLevel <= 0.25" title="缩小">
                <a-icon type="zoom-out" />
              </a-button>
              <a-button @click="handleZoomReset" title="重置缩放">
                {{ Math.round(zoomLevel * 100) }}%
              </a-button>
              <a-button @click="handleZoomIn" :disabled="zoomLevel >= 2" title="放大">
                <a-icon type="zoom-in" />
              </a-button>
            </a-button-group>
          </div>

          <a-divider type="vertical" />

          <a-button-group size="small">
            <a-button @click="toggleGrid" :type="showGrid ? 'primary' : 'default'" title="网格">
              <a-icon type="border" />
            </a-button>
            <a-button @click="toggleRuler" :type="showRuler ? 'primary' : 'default'" title="标尺">
              <a-icon type="column-width" />
            </a-button>
          </a-button-group>
        </div>
      </div>

      <div class="header-right">
        <a-button-group size="small">
          <a-button @click="toggleComponentPanel" :type="showComponentPanel ? 'primary' : 'default'" title="组件面板">
            <a-icon type="appstore" />
            组件
          </a-button>
          <a-button @click="togglePropertyPanel" :type="showPropertyPanel ? 'primary' : 'default'" title="属性面板">
            <a-icon type="setting" />
            属性
          </a-button>
        </a-button-group>

        <a-divider type="vertical" />

        <a-button-group size="small">
          <a-button @click="handlePreview" title="预览">
            <a-icon type="eye" />
            预览
          </a-button>
          <a-button @click="handleSave" type="primary" title="保存">
            <a-icon type="save" />
            保存
          </a-button>
        </a-button-group>

        <a-divider type="vertical" />

        <a-button @click="handleClose" title="关闭设计器">
          <a-icon type="close" />
        </a-button>
      </div>
    </div>

    <!-- 主工作区域 -->
    <div class="designer-workspace">
      <!-- 左侧组件面板（可收起） -->
      <div
        v-show="showComponentPanel"
        class="component-panel-container"
        :class="{ 'panel-collapsed': !showComponentPanel }"
      >
        <ComponentPanel
          @drag-start="handleComponentDragStart"
          @drag-end="handleComponentDragEnd"
          @add-component="handleAddComponent"
        />
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-container" :class="{ 'full-width': !showComponentPanel }">
        <DesignerCanvas
          :components="pageComponents"
          :selected-component-id="selectedComponentId"
          :zoom-level="zoomLevel"
          :canvas-width="canvasWidth"
          :canvas-height="canvasHeight"
          :show-grid="showGrid"
          :show-ruler="showRuler"
          @add-component="handleAddComponent"
          @select-component="handleSelectComponent"
          @edit-component="handleEditComponent"
          @copy-component="handleCopyComponent"
          @delete-component="handleDeleteComponent"
          @toggle-grid="toggleGrid"
          @toggle-ruler="toggleRuler"
        />
      </div>
    </div>

    <!-- 右侧属性面板（抽屉式） -->
    <a-drawer
      title="属性配置"
      placement="right"
      :closable="true"
      :visible="showPropertyPanel"
      :width="360"
      @close="togglePropertyPanel"
      :mask="false"
      :get-container="false"
      :style="{ position: 'absolute' }"
    >
      <PropertyPanel
        :selected-component="selectedComponent"
        @update-component="handleUpdateComponent"
      />
    </a-drawer>

    <!-- 页面设置弹窗 -->
    <a-modal
      title="页面设置"
      :visible="pageSettingsVisible"
      @ok="handlePageSettingsOk"
      @cancel="pageSettingsVisible = false"
      width="500px"
    >
      <a-form layout="vertical">
        <a-form-item label="页面名称">
          <a-input v-model="currentPageName" placeholder="请输入页面名称" />
        </a-form-item>
        <a-form-item label="页面描述">
          <a-textarea v-model="currentPageDesc" placeholder="请输入页面描述" :rows="3" />
        </a-form-item>
        <a-form-item label="画布尺寸">
          <a-row :gutter="8">
            <a-col :span="12">
              <a-input-number v-model="canvasWidth" :min="800" :max="2000" style="width: 100%" />
              <small>宽度 (px)</small>
            </a-col>
            <a-col :span="12">
              <a-input-number v-model="canvasHeight" :min="600" :max="1500" style="width: 100%" />
              <small>高度 (px)</small>
            </a-col>
          </a-row>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import DesignerCanvas from './Canvas.vue'
import ComponentPanel from './ComponentPanel.vue'
import PropertyPanel from './PropertyPanel.vue'

export default {
  name: 'FullscreenDesigner',
  components: {
    ComponentPanel,
    DesignerCanvas,
    PropertyPanel
  },
  data() {
    return {
      // 页面信息
      currentPageName: '新页面',
      currentPageDesc: '',
      pageSettingsVisible: false
    }
  },
  computed: {
    // 从Vuex获取设计器状态
    pageComponents() {
      return this.$store.getters['pageBuilder/designer/allComponents']
    },
    selectedComponentId() {
      return this.$store.state.pageBuilder.designer.selectedComponentId
    },
    selectedComponent() {
      return this.$store.getters['pageBuilder/designer/selectedComponent']
    },
    canUndo() {
      return this.$store.getters['pageBuilder/designer/canUndo']
    },
    canRedo() {
      return this.$store.getters['pageBuilder/designer/canRedo']
    },
    clipboardComponent() {
      return this.$store.state.pageBuilder.designer.clipboardComponent
    },

    // 从Vuex获取画布设置
    zoomLevel() {
      return this.$store.state.pageBuilder.designer.canvasSettings.zoomLevel
    },
    canvasWidth() {
      return this.$store.state.pageBuilder.designer.canvasSettings.width
    },
    canvasHeight() {
      return this.$store.state.pageBuilder.designer.canvasSettings.height
    },
    showGrid() {
      return this.$store.state.pageBuilder.designer.canvasSettings.showGrid
    },
    showRuler() {
      return this.$store.state.pageBuilder.designer.canvasSettings.showRuler
    },

    // 从Vuex获取工具栏设置
    showComponentPanel() {
      return this.$store.state.pageBuilder.designer.toolbarSettings.showComponentPanel
    },
    showPropertyPanel() {
      return this.$store.state.pageBuilder.designer.toolbarSettings.showPropertyPanel
    },

    // 拖拽状态
    isDragging() {
      return this.$store.getters['pageBuilder/designer/isDragging']
    }
  },
  methods: {
    // 面板切换
    toggleComponentPanel() {
      this.$store.commit('pageBuilder/designer/UPDATE_TOOLBAR_SETTINGS', {
        showComponentPanel: !this.showComponentPanel
      })
    },
    togglePropertyPanel() {
      this.$store.commit('pageBuilder/designer/UPDATE_TOOLBAR_SETTINGS', {
        showPropertyPanel: !this.showPropertyPanel
      })
    },
    toggleGrid() {
      this.$store.commit('pageBuilder/designer/UPDATE_CANVAS_SETTINGS', {
        showGrid: !this.showGrid
      })
    },
    toggleRuler() {
      this.$store.commit('pageBuilder/designer/UPDATE_CANVAS_SETTINGS', {
        showRuler: !this.showRuler
      })
    },

    // 页面设置
    showPageSettings() {
      this.pageSettingsVisible = true
    },
    handlePageSettingsOk() {
      this.pageSettingsVisible = false
      this.$message.success('页面设置已更新')
    },

    // 工具栏操作
    handleUndo() {
      this.$store.dispatch('pageBuilder/designer/undo')
    },
    handleRedo() {
      this.$store.dispatch('pageBuilder/designer/redo')
    },
    handleZoomIn() {
      const newZoomLevel = Math.min(this.zoomLevel + 0.1, 2)
      this.$store.commit('pageBuilder/designer/UPDATE_CANVAS_SETTINGS', {
        zoomLevel: newZoomLevel
      })
    },
    handleZoomOut() {
      const newZoomLevel = Math.max(this.zoomLevel - 0.1, 0.25)
      this.$store.commit('pageBuilder/designer/UPDATE_CANVAS_SETTINGS', {
        zoomLevel: newZoomLevel
      })
    },
    handleZoomReset() {
      this.$store.commit('pageBuilder/designer/UPDATE_CANVAS_SETTINGS', {
        zoomLevel: 1
      })
    },
    handlePreview() {
      this.$message.info('预览功能开发中...')
    },
    handleSave() {
      this.$message.success('页面已保存')
    },
    handleClose() {
      this.$confirm({
        title: '确认关闭设计器？',
        content: '未保存的更改将会丢失',
        onOk: () => {
          window.close()
        }
      })
    },

    // 组件操作（使用Vuex）
    async handleAddComponent({ componentType, componentConfig, position }) {
      try {
        await this.$store.dispatch('pageBuilder/designer/addComponent', {
          componentType,
          componentConfig,
          position
        })

        // 自动打开属性面板
        this.$store.commit('pageBuilder/designer/UPDATE_TOOLBAR_SETTINGS', {
          showPropertyPanel: true
        })

        this.$message.success(`已添加 ${componentConfig.displayName}`)
      } catch (error) {
        this.$message.error('添加组件失败')
        console.error('添加组件失败:', error)
      }
    },

    handleSelectComponent(componentId) {
      this.$store.commit('pageBuilder/designer/SET_SELECTED_COMPONENT', componentId)
      if (componentId) {
        // 选中组件时自动打开属性面板
        this.$store.commit('pageBuilder/designer/UPDATE_TOOLBAR_SETTINGS', {
          showPropertyPanel: true
        })
      }
    },

    handleUpdateComponent(updatedComponent) {
      this.$store.dispatch('pageBuilder/designer/updateComponent', {
        id: updatedComponent.id,
        updates: updatedComponent
      })
    },

    handleDeleteComponent(componentId) {
      this.$store.dispatch('pageBuilder/designer/deleteComponent', componentId)
      this.$store.commit('pageBuilder/designer/UPDATE_TOOLBAR_SETTINGS', {
        showPropertyPanel: false
      })
      this.$message.success('组件已删除')
    },

    handleCopyComponent(component) {
      this.$store.commit('pageBuilder/designer/SET_CLIPBOARD_COMPONENT', { ...component })
      this.$message.success('组件已复制')
    },

    handleEditComponent(component) {
      this.$store.commit('pageBuilder/designer/SET_SELECTED_COMPONENT', component.id)
      this.$store.commit('pageBuilder/designer/UPDATE_TOOLBAR_SETTINGS', {
        showPropertyPanel: true
      })
    },
    handleComponentDragStart(dragData) {
      // 设置拖拽状态
      this.$store.commit('pageBuilder/designer/SET_DRAG_STATE', {
        isDragging: true,
        dragType: 'component',
        dragData: dragData
      })
      console.log('设计器接收到拖拽开始事件:', dragData)
    },

    handleComponentDragEnd() {
      // 清除拖拽状态
      this.$store.commit('pageBuilder/designer/SET_DRAG_STATE', {
        isDragging: false,
        dragType: null,
        dragData: null
      })
      console.log('设计器接收到拖拽结束事件')
    },

    // 键盘快捷键处理
    handleKeydown(event) {
      // 键盘快捷键处理
      if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
        event.preventDefault()
        this.handleUndo()
      } else if ((event.ctrlKey && event.shiftKey && event.key === 'Z') ||
                 (event.ctrlKey && event.key === 'y')) {
        event.preventDefault()
        this.handleRedo()
      }
    }
  },
  async mounted() {
    // 设置全屏样式
    document.body.style.overflow = 'hidden'

    // 添加键盘快捷键
    document.addEventListener('keydown', this.handleKeydown)

    // 初始化页面构建器store
    try {
      await this.$store.dispatch('pageBuilder/initialize')
      console.log('页面构建器初始化完成')
    } catch (error) {
      console.error('页面构建器初始化失败:', error)
      this.$message.error('页面构建器初始化失败')
    }

    console.log('全屏设计器已加载')
  },
  beforeDestroy() {
    // 恢复页面样式
    document.body.style.overflow = ''
    document.removeEventListener('keydown', this.handleKeydown)
  }
}
</script>

<style lang="less" scoped>
.fullscreen-designer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f0f2f5;
  display: flex;
  flex-direction: column;
  z-index: 1000;

  .designer-header {
    height: 56px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;

    .header-left {
      display: flex;
      align-items: center;

      .logo-section {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: #1890ff;

        .anticon {
          margin-right: 8px;
          font-size: 18px;
        }
      }

      .page-info {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #262626;
      }
    }

    .header-center {
      display: flex;
      align-items: center;

      .toolbar-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .zoom-controls .ant-btn {
          min-width: 50px;
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
    }

    .ant-divider-vertical {
      height: 20px;
      margin: 0 12px;
    }
  }

  .designer-workspace {
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden;

    .component-panel-container {
      width: 280px;
      background: #fafafa;
      border-right: 1px solid #e8e8e8;
      flex-shrink: 0;
      transition: all 0.3s ease;

      &.panel-collapsed {
        width: 0;
        overflow: hidden;
      }
    }

    .canvas-container {
      flex: 1;
      background: #f5f5f5;
      transition: all 0.3s ease;

      &.full-width {
        margin-left: 0;
      }
    }
  }
}

// 抽屉样式覆盖
:global(.ant-drawer-content-wrapper) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15) !important;
}

:global(.ant-drawer-body) {
  padding: 16px !important;
}
</style>
