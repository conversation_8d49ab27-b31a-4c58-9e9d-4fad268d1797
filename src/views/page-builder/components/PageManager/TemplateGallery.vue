<template>
  <div class="template-gallery">
    <div class="gallery-header">
      <h2>页面模板库</h2>
      <p>选择模板快速创建页面 - 后续版本将提供更多模板</p>
      
      <div class="search-bar">
        <a-input-search
          v-model="searchText"
          placeholder="搜索模板"
          style="width: 300px"
          @search="handleSearch"
        />
      </div>
    </div>
    
    <div class="gallery-content">
      <div class="template-categories">
        <a-menu
          v-model="selectedCategory"
          mode="inline"
          :default-selected-keys="['all']"
        >
          <a-menu-item key="all">
            <a-icon type="appstore" />
            全部模板
          </a-menu-item>
          <a-menu-item key="form">
            <a-icon type="form" />
            表单模板
          </a-menu-item>
          <a-menu-item key="table">
            <a-icon type="table" />
            表格模板
          </a-menu-item>
          <a-menu-item key="dashboard">
            <a-icon type="dashboard" />
            仪表板模板
          </a-menu-item>
        </a-menu>
      </div>
      
      <div class="template-list">
        <div class="template-grid">
          <div 
            v-for="template in mockTemplates"
            :key="template.id"
            class="template-card"
            @click="selectTemplate(template)"
          >
            <div class="template-preview">
              <a-icon :type="template.icon" class="template-icon" />
            </div>
            <div class="template-info">
              <h3>{{ template.name }}</h3>
              <p>{{ template.description }}</p>
              <div class="template-meta">
                <span>{{ template.category }}</span>
                <span>{{ template.components }}个组件</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TemplateGallery',
  data() {
    return {
      searchText: '',
      selectedCategory: ['all'],
      mockTemplates: [
        {
          id: '1',
          name: '用户管理表单',
          description: '包含用户信息录入的完整表单模板',
          category: '表单模板',
          icon: 'form',
          components: 8
        },
        {
          id: '2',
          name: '数据展示表格',
          description: '标准的数据列表展示表格模板',
          category: '表格模板',
          icon: 'table',
          components: 5
        },
        {
          id: '3',
          name: '统计仪表板',
          description: '包含图表和统计信息的仪表板模板',
          category: '仪表板模板',
          icon: 'dashboard',
          components: 12
        },
        {
          id: '4',
          name: '商品管理页面',
          description: '电商商品管理的完整页面模板',
          category: '综合模板',
          icon: 'shop',
          components: 15
        }
      ]
    }
  },
  methods: {
    handleSearch(value) {
      console.log('搜索模板:', value)
    },
    selectTemplate(template) {
      this.$confirm({
        title: '使用模板',
        content: `确定要使用模板 "${template.name}" 创建新页面吗？`,
        onOk: () => {
          this.$router.push(`/page-builder/designer?template=${template.id}`)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.template-gallery {
  padding: 24px;
  
  .gallery-header {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    
    div:first-child {
      flex: 1;
    }
    
    h2 {
      margin: 0 0 8px 0;
      color: #1890ff;
    }
    
    p {
      margin: 0;
      color: #8c8c8c;
    }
    
    .search-bar {
      margin-left: 16px;
    }
  }
  
  .gallery-content {
    background: #fff;
    border-radius: 8px;
    display: flex;
    min-height: 600px;
    
    .template-categories {
      width: 200px;
      border-right: 1px solid #e8e8e8;
      
      .ant-menu {
        border-right: none;
      }
    }
    
    .template-list {
      flex: 1;
      padding: 24px;
      
      .template-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 16px;
        
        .template-card {
          border: 1px solid #e8e8e8;
          border-radius: 8px;
          padding: 16px;
          cursor: pointer;
          transition: all 0.3s;
          
          &:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
          }
          
          .template-preview {
            text-align: center;
            margin-bottom: 16px;
            
            .template-icon {
              font-size: 48px;
              color: #1890ff;
            }
          }
          
          .template-info {
            h3 {
              margin: 0 0 8px 0;
              color: #262626;
            }
            
            p {
              margin: 0 0 12px 0;
              color: #8c8c8c;
              font-size: 14px;
            }
            
            .template-meta {
              display: flex;
              justify-content: space-between;
              font-size: 12px;
              color: #bfbfbf;
            }
          }
        }
      }
    }
  }
}
</style>
