<template>
  <div class="page-list">
    <div class="page-list-header">
      <h2>页面管理</h2>
      <p>管理所有通过页面构建器创建的页面 - 任务4.1将实现完整功能</p>
      
      <div class="header-actions">
        <a-button type="primary" @click="createPage">
          <a-icon type="plus" />
          新建页面
        </a-button>
      </div>
    </div>
    
    <div class="page-list-content">
      <a-table
        :columns="columns"
        :data-source="mockData"
        :pagination="false"
        row-key="id"
      >
        <template slot="actions" slot-scope="text, record">
          <a-button size="small" @click="editPage(record)">编辑</a-button>
          <a-button size="small" @click="previewPage(record)">预览</a-button>
          <a-button size="small" type="danger" @click="deletePage(record)">删除</a-button>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PageList',
  data() {
    return {
      columns: [
        { title: '页面名称', dataIndex: 'name', key: 'name' },
        { title: '描述', dataIndex: 'description', key: 'description' },
        { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
        { title: '状态', dataIndex: 'status', key: 'status' },
        { title: '操作', key: 'actions', scopedSlots: { customRender: 'actions' } }
      ],
      mockData: [
        {
          id: '1',
          name: '示例页面1',
          description: '这是一个示例页面',
          createTime: '2025-01-28 14:30:00',
          status: '草稿'
        },
        {
          id: '2',
          name: '示例页面2',
          description: '另一个示例页面',
          createTime: '2025-01-28 15:00:00',
          status: '已发布'
        }
      ]
    }
  },
  methods: {
    createPage() {
      this.$router.push('/page-builder/designer')
    },
    editPage(record) {
      this.$router.push(`/page-builder/designer?id=${record.id}`)
    },
    previewPage(record) {
      this.$message.info(`预览页面: ${record.name}`)
    },
    deletePage(record) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除页面 "${record.name}" 吗？`,
        onOk: () => {
          this.$message.success('删除成功')
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.page-list {
  padding: 24px;
  
  .page-list-header {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    
    div:first-child {
      flex: 1;
    }
    
    h2 {
      margin: 0 0 8px 0;
      color: #1890ff;
    }
    
    p {
      margin: 0;
      color: #8c8c8c;
    }
    
    .header-actions {
      margin-left: 16px;
    }
  }
  
  .page-list-content {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
  }
}
</style>
