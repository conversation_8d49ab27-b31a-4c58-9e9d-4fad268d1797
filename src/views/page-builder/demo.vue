<template>
  <div class="designer-demo">
    <div class="demo-header">
      <h1>YYL页面构建器 - 设计器演示</h1>
      <div class="demo-actions">
        <a-button @click="addSampleComponents" type="primary">
          <a-icon type="plus" />
          添加示例组件
        </a-button>
        <a-button @click="clearCanvas">
          <a-icon type="clear" />
          清空画布
        </a-button>
        <a-button @click="showInfo">
          <a-icon type="info-circle" />
          功能说明
        </a-button>
      </div>
    </div>

    <!-- 设计器组件 -->
    <PageDesigner ref="designer" />

    <!-- 功能说明弹窗 -->
    <a-modal
      title="YYL页面构建器功能说明"
      :visible="infoVisible"
      @ok="infoVisible = false"
      @cancel="infoVisible = false"
      width="600px"
    >
      <div class="info-content">
        <h3>🎯 主要功能</h3>
        <ul>
          <li><strong>拖拽设计</strong>：从左侧组件面板拖拽YYL组件到画布</li>
          <li><strong>属性编辑</strong>：选中组件后在右侧面板编辑属性</li>
          <li><strong>工具栏操作</strong>：撤销/重做、复制/粘贴、缩放控制</li>
          <li><strong>键盘快捷键</strong>：Ctrl+Z撤销、Ctrl+C复制、Delete删除</li>
        </ul>

        <h3>🧩 支持的YYL组件</h3>
        <ul>
          <li><strong>业务组件</strong>：YYLButton（带权限控制的按钮）</li>
          <li><strong>表单组件</strong>：YYLForm（动态表单组件）</li>
          <li><strong>表格组件</strong>：YYLTable（动态表格组件）</li>
          <li><strong>通用组件</strong>：其他YYL组件库中的组件</li>
        </ul>

        <h3>⌨️ 快捷键</h3>
        <ul>
          <li><kbd>Ctrl + Z</kbd>：撤销操作</li>
          <li><kbd>Ctrl + Y</kbd>：重做操作</li>
          <li><kbd>Ctrl + C</kbd>：复制选中组件</li>
          <li><kbd>Ctrl + V</kbd>：粘贴组件</li>
          <li><kbd>Delete</kbd>：删除选中组件</li>
        </ul>

        <h3>🎨 界面布局</h3>
        <ul>
          <li><strong>左侧面板</strong>：YYL组件库，支持搜索和分类浏览</li>
          <li><strong>中间画布</strong>：页面设计区域，支持网格和标尺</li>
          <li><strong>右侧面板</strong>：属性配置，动态显示选中组件的属性</li>
          <li><strong>顶部工具栏</strong>：常用操作和设置</li>
        </ul>
      </div>
    </a-modal>
  </div>
</template>

<script>
import PageDesigner from './components/Designer/index.vue'

export default {
  name: 'DesignerDemo',
  components: {
    PageDesigner
  },
  data() {
    return {
      infoVisible: false
    }
  },
  methods: {
    // 添加示例组件
    addSampleComponents() {
      const designer = this.$refs.designer
      if (!designer) return

      // 添加一个YYL按钮
      designer.handleAddComponent({
        componentType: 'YYLButton',
        componentConfig: {
          displayName: 'YYL按钮',
          category: 'business',
          icon: 'button',
          defaultProps: {
            text: '示例按钮',
            type: 'primary',
            size: 'default',
            menuId: 'DEMO_BUTTON',
            loading: false,
            disabled: false
          },
          style: {
            margin: '8px',
            display: 'inline-block'
          }
        },
        position: { x: 100, y: 100 }
      })

      // 添加一个YYL表单
      setTimeout(() => {
        designer.handleAddComponent({
          componentType: 'YYLForm',
          componentConfig: {
            displayName: 'YYL表单',
            category: 'form',
            icon: 'form',
            defaultProps: {
              formFields: [],
              layout: 'horizontal',
              labelCol: { span: 6 },
              wrapperCol: { span: 18 }
            },
            style: {
              margin: '16px',
              width: '400px'
            }
          },
          position: { x: 100, y: 200 }
        })
      }, 500)

      // 添加一个YYL表格
      setTimeout(() => {
        designer.handleAddComponent({
          componentType: 'YYLTable',
          componentConfig: {
            displayName: 'YYL表格',
            category: 'table',
            icon: 'table',
            defaultProps: {
              columns: [],
              tableData: [],
              loading: false,
              pagination: { current: 1, pageSize: 10, total: 0 }
            },
            style: {
              margin: '16px',
              width: '500px'
            }
          },
          position: { x: 100, y: 350 }
        })
      }, 1000)

      this.$message.success('已添加示例组件，可以点击选中并在右侧编辑属性')
    },

    // 清空画布
    clearCanvas() {
      const designer = this.$refs.designer
      if (designer) {
        designer.handleClearCanvas()
      }
    },

    // 显示功能说明
    showInfo() {
      this.infoVisible = true
    }
  },

  mounted() {
    console.log('YYL页面构建器演示页面已加载')

    // 显示欢迎提示
    this.$notification.open({
      message: '欢迎使用YYL页面构建器',
      description: '点击"添加示例组件"开始体验，或从左侧面板拖拽组件到画布。',
      icon: this.$createElement('a-icon', {
        props: { type: 'build' },
        style: { color: '#1890ff' }
      }),
      duration: 5
    })
  }
}
</script>

<style lang="less" scoped>
.designer-demo {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;

  .demo-header {
    background: #fff;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h1 {
      margin: 0;
      color: #1890ff;
      font-size: 20px;
    }

    .demo-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .info-content {
    h3 {
      color: #262626;
      margin: 16px 0 8px 0;

      &:first-child {
        margin-top: 0;
      }
    }

    ul {
      margin-bottom: 16px;

      li {
        margin-bottom: 4px;
        line-height: 1.6;

        strong {
          color: #1890ff;
        }

        kbd {
          background: #f5f5f5;
          border: 1px solid #d9d9d9;
          border-radius: 3px;
          padding: 2px 6px;
          font-family: monospace;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
