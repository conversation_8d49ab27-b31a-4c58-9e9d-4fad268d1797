# YYL页面构建器使用指南

## 🚀 快速开始

### 访问设计器
```
# 主入口
http://localhost:8080/#/page-builder

# 直接访问设计器
http://localhost:8080/#/page-builder/designer

# 演示页面（推荐）
http://localhost:8080/#/page-builder/demo
```

### 基本操作流程
1. **选择组件** - 从左侧组件面板选择YYL组件
2. **拖拽到画布** - 将组件拖拽到中间的画布区域
3. **配置属性** - 在右侧属性面板编辑组件属性
4. **保存页面** - 使用工具栏的保存功能

## 🎯 界面布局

### 工具栏（顶部）
- **撤销/重做** - Ctrl+Z / Ctrl+Y
- **复制/粘贴** - Ctrl+C / Ctrl+V
- **删除** - Delete键
- **缩放控制** - 放大/缩小/重置
- **预览/保存** - 页面预览和保存功能

### 组件面板（左侧）
- **业务组件** - YYLButton等带权限控制的组件
- **表单组件** - YYLForm等表单相关组件
- **表格组件** - YYLTable等表格相关组件
- **通用组件** - 其他YYL组件库组件
- **搜索功能** - 快速查找所需组件

### 画布区域（中间）
- **网格背景** - 辅助对齐的网格线
- **标尺显示** - 精确定位的标尺
- **组件渲染** - 实时预览组件效果
- **选中状态** - 蓝色边框显示选中组件

### 属性面板（右侧）
- **基础属性** - 组件的props配置
- **样式配置** - 宽度、高度、边距等
- **位置配置** - X、Y坐标精确定位
- **实时预览** - 属性修改立即生效

## ⌨️ 快捷键

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl + Z` | 撤销 | 撤销上一步操作 |
| `Ctrl + Y` | 重做 | 重做被撤销的操作 |
| `Ctrl + C` | 复制 | 复制选中的组件 |
| `Ctrl + V` | 粘贴 | 粘贴复制的组件 |
| `Delete` | 删除 | 删除选中的组件 |

## 🧩 支持的YYL组件

### YYLButton（业务组件）
```javascript
// 主要属性
{
  text: '按钮文本',
  menuId: '权限ID',
  type: 'primary|default|dashed|danger|link',
  size: 'small|default|large',
  icon: '图标名称',
  loading: false,
  disabled: false
}
```

### YYLForm（表单组件）
```javascript
// 主要属性
{
  formFields: [], // 表单字段配置
  layout: 'horizontal|vertical|inline',
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
  disabled: false
}
```

### YYLTable（表格组件）
```javascript
// 主要属性
{
  columns: [], // 表格列配置
  tableData: [], // 表格数据
  loading: false,
  pagination: { current: 1, pageSize: 10, total: 0 },
  rowSelection: null,
  scrollX: 0
}
```

## 🎨 设计技巧

### 组件布局
1. **使用网格** - 开启网格辅助对齐
2. **合理间距** - 设置适当的margin和padding
3. **响应式设计** - 考虑不同屏幕尺寸

### 属性配置
1. **权限设置** - 为YYLButton等组件配置menuId
2. **样式统一** - 保持组件样式的一致性
3. **数据绑定** - 合理配置组件的数据源

### 交互优化
1. **快捷键使用** - 熟练使用键盘快捷键提高效率
2. **批量操作** - 使用复制粘贴快速创建相似组件
3. **历史记录** - 利用撤销重做功能安全试错

## 🔧 故障排除

### 常见问题

**Q: 组件拖拽不生效？**
A: 确保从组件面板正确拖拽，拖拽时会显示蓝色提示框。

**Q: 属性修改不生效？**
A: 检查属性值格式是否正确，复杂属性需要使用JSON格式。

**Q: 组件显示异常？**
A: 刷新页面重新加载，或检查组件配置是否正确。

**Q: 快捷键不工作？**
A: 确保焦点在设计器区域内，某些浏览器可能需要点击画布激活。

### 性能优化
- 避免在画布上放置过多组件（建议<50个）
- 复杂表格数据建议使用分页
- 定期保存设计内容避免丢失

## 📝 开发说明

### 技术架构
- **Vue 2.5.22** - 主框架
- **Ant Design Vue 1.7.8** - UI组件库
- **现有YYL组件库** - 业务组件基础

### 文件结构
```
src/views/page-builder/
├── components/Designer/    # 设计器核心组件
├── configs/               # 组件配置文件
├── utils/                # 工具函数
├── styles/               # 样式文件
└── demo.vue             # 演示页面
```

### 扩展开发
如需添加新的YYL组件支持：
1. 在 `configs/yyl-components.js` 中添加组件配置
2. 在 `Canvas.vue` 中添加组件预览渲染
3. 在 `PropertyPanel.vue` 中添加专用属性编辑器（如需要）

## 📞 技术支持

如遇到问题或需要功能扩展，请：
1. 查看控制台错误信息
2. 检查组件配置是否正确
3. 参考现有组件的实现方式
4. 联系开发团队获取支持
