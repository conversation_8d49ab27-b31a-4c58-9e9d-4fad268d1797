/**
 * YYL组件扫描器测试脚本
 * 用于验证组件扫描器的功能
 */

import yylComponentScanner from './utils/componentScanner'
import { YYL_COMPONENT_CONFIGS } from './configs/yyl-components'

// 测试组件扫描器
export async function testComponentScanner() {
  console.log('=== YYL组件扫描器测试开始 ===')
  
  try {
    // 测试扫描功能
    console.log('1. 测试组件扫描功能...')
    const components = await yylComponentScanner.scanYYLComponents()
    
    console.log(`✓ 扫描完成，发现 ${components.size} 个组件`)
    
    // 测试按分类获取组件
    console.log('2. 测试按分类获取组件...')
    const categories = ['business', 'form', 'table', 'components']
    
    categories.forEach(category => {
      const categoryComponents = yylComponentScanner.getComponentsByCategory(category)
      console.log(`✓ ${category} 分类: ${categoryComponents.length} 个组件`)
      
      categoryComponents.forEach(component => {
        console.log(`  - ${component.displayName} (${component.name})`)
      })
    })
    
    // 测试获取特定组件
    console.log('3. 测试获取特定组件...')
    const testComponents = ['YYLButton', 'YYLForm', 'YYLTable']
    
    testComponents.forEach(componentName => {
      const component = yylComponentScanner.getComponentByName(componentName)
      if (component) {
        console.log(`✓ 找到组件: ${component.displayName}`)
        console.log(`  - 属性数量: ${Object.keys(component.props).length}`)
        console.log(`  - 事件数量: ${component.events.length}`)
        console.log(`  - 分类: ${component.category}`)
      } else {
        console.log(`✗ 未找到组件: ${componentName}`)
      }
    })
    
    // 测试组件配置
    console.log('4. 测试组件配置...')
    const configComponents = Object.keys(YYL_COMPONENT_CONFIGS)
    console.log(`✓ 配置文件中有 ${configComponents.length} 个组件配置`)
    
    configComponents.forEach(componentName => {
      const config = YYL_COMPONENT_CONFIGS[componentName]
      console.log(`  - ${config.displayName}: ${config.category} 分类`)
    })
    
    console.log('=== 测试完成 ===')
    return {
      success: true,
      componentCount: components.size,
      categories: categories.map(cat => ({
        name: cat,
        count: yylComponentScanner.getComponentsByCategory(cat).length
      })),
      configCount: configComponents.length
    }
    
  } catch (error) {
    console.error('=== 测试失败 ===')
    console.error('错误信息:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数挂载到window对象
  window.testYYLScanner = testComponentScanner
}

export default testComponentScanner
