<template>
  <div class="store-test">
    <a-card title="页面构建器 - Vuex状态管理测试">
      <div class="test-section">
        <h3>初始化状态</h3>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic title="页面构建器初始化" :value="pageBuilderInitialized ? '已初始化' : '未初始化'" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="设计器初始化" :value="designerInitialized ? '已初始化' : '未初始化'" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="组件库加载" :value="componentsLoaded ? '已加载' : '未加载'" />
          </a-col>
        </a-row>

        <div style="margin-top: 16px;">
          <a-button @click="initializePageBuilder" type="primary" :loading="initializing">
            初始化页面构建器
          </a-button>
          <a-button @click="resetPageBuilder" style="margin-left: 8px;">
            重置状态
          </a-button>
        </div>
      </div>

      <a-divider />

      <div class="test-section">
        <h3>组件库状态</h3>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总组件数" :value="totalComponents" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="业务组件" :value="businessComponents" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="表单组件" :value="formComponents" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="表格组件" :value="tableComponents" />
          </a-col>
        </a-row>

        <div style="margin-top: 16px;">
          <a-button @click="loadComponents" :loading="componentsLoading">
            重新加载组件
          </a-button>
          <a-button @click="searchComponents" style="margin-left: 8px;">
            搜索测试
          </a-button>
        </div>
      </div>

      <a-divider />

      <div class="test-section">
        <h3>设计器状态</h3>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="画布组件数" :value="canvasComponents" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="选中组件" :value="selectedComponentId || '无'" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="历史记录" :value="historyLength" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="缩放级别" :value="zoomLevel" :precision="1" suffix="x" />
          </a-col>
        </a-row>

        <div style="margin-top: 16px;">
          <a-button @click="addTestComponent" type="primary">
            添加测试组件
          </a-button>
          <a-button @click="clearCanvas" style="margin-left: 8px;">
            清空画布
          </a-button>
          <a-button @click="testUndo" :disabled="!canUndo" style="margin-left: 8px;">
            撤销
          </a-button>
          <a-button @click="testRedo" :disabled="!canRedo" style="margin-left: 8px;">
            重做
          </a-button>
        </div>
      </div>

      <a-divider />

      <div class="test-section">
        <h3>页面状态</h3>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic title="页面总数" :value="totalPages" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="当前页面" :value="currentPageName" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="最近页面" :value="recentPagesCount" />
          </a-col>
        </a-row>

        <div style="margin-top: 16px;">
          <a-button @click="createTestPage" type="primary">
            创建测试页面
          </a-button>
          <a-button @click="loadPageList" style="margin-left: 8px;">
            加载页面列表
          </a-button>
        </div>
      </div>

      <a-divider />

      <div class="test-section">
        <h3>错误日志</h3>
        <div v-if="errors.length === 0" style="color: #52c41a;">
          ✅ 暂无错误
        </div>
        <div v-else>
          <a-alert
            v-for="(error, index) in errors"
            :key="index"
            :message="error.message"
            type="error"
            style="margin-bottom: 8px;"
            show-icon
          />
        </div>

        <a-button @click="clearErrors" v-if="errors.length > 0" style="margin-top: 8px;">
          清除错误
        </a-button>
      </div>
    </a-card>
  </div>
</template>

<script>
export default {
  name: 'StoreTest',
  data() {
    return {
      initializing: false,
      componentsLoading: false,
      errors: []
    }
  },

  computed: {
    // 页面构建器状态
    pageBuilderInitialized() {
      return this.$store.state.pageBuilder && this.$store.state.pageBuilder.initialized || false
    },

    // 设计器状态
    designerInitialized() {
      return this.$store.state.pageBuilder &&
             this.$store.state.pageBuilder.designer &&
             this.$store.state.pageBuilder.designer.initialized || false
    },
    canvasComponents() {
      const components = this.$store.getters['pageBuilder/designer/allComponents']
      return components ? components.length : 0
    },
    selectedComponentId() {
      return this.$store.state.pageBuilder &&
             this.$store.state.pageBuilder.designer &&
             this.$store.state.pageBuilder.designer.selectedComponentId
    },
    historyLength() {
      return this.$store.state.pageBuilder &&
             this.$store.state.pageBuilder.designer &&
             this.$store.state.pageBuilder.designer.history &&
             this.$store.state.pageBuilder.designer.history.length || 0
    },
    zoomLevel() {
      return this.$store.state.pageBuilder &&
             this.$store.state.pageBuilder.designer &&
             this.$store.state.pageBuilder.designer.canvasSettings &&
             this.$store.state.pageBuilder.designer.canvasSettings.zoomLevel || 1
    },
    canUndo() {
      try {
        return this.$store.getters['pageBuilder/designer/canUndo'] || false
      } catch (e) {
        return false
      }
    },
    canRedo() {
      try {
        return this.$store.getters['pageBuilder/designer/canRedo'] || false
      } catch (e) {
        return false
      }
    },

    // 组件库状态
    componentsLoaded() {
      try {
        return this.$store.getters['pageBuilder/components/isLoaded'] || false
      } catch (e) {
        return false
      }
    },
    totalComponents() {
      try {
        return this.$store.getters['pageBuilder/components/totalComponents'] || 0
      } catch (e) {
        return 0
      }
    },
    businessComponents() {
      try {
        const components = this.$store.getters['pageBuilder/components/componentsByCategory']('business')
        return components ? components.length : 0
      } catch (e) {
        return 0
      }
    },
    formComponents() {
      try {
        const components = this.$store.getters['pageBuilder/components/componentsByCategory']('form')
        return components ? components.length : 0
      } catch (e) {
        return 0
      }
    },
    tableComponents() {
      try {
        const components = this.$store.getters['pageBuilder/components/componentsByCategory']('table')
        return components ? components.length : 0
      } catch (e) {
        return 0
      }
    },

    // 页面状态
    totalPages() {
      try {
        const pages = this.$store.getters['pageBuilder/pages/allPages']
        return pages ? pages.length : 0
      } catch (e) {
        return 0
      }
    },
    currentPageName() {
      try {
        const currentPage = this.$store.getters['pageBuilder/pages/getCurrentPage']
        return currentPage && currentPage.name || '无'
      } catch (e) {
        return '无'
      }
    },
    recentPagesCount() {
      try {
        const recentPages = this.$store.getters['pageBuilder/pages/getRecentPages']
        return recentPages ? recentPages.length : 0
      } catch (e) {
        return 0
      }
    }
  },

  methods: {
    // 初始化页面构建器
    async initializePageBuilder() {
      try {
        this.initializing = true
        await this.$store.dispatch('pageBuilder/initialize')
        this.$message.success('页面构建器初始化成功')
      } catch (error) {
        this.errors.push({ message: `初始化失败: ${error.message}` })
        this.$message.error('初始化失败')
      } finally {
        this.initializing = false
      }
    },

    // 重置页面构建器
    async resetPageBuilder() {
      try {
        await this.$store.dispatch('pageBuilder/resetAll')
        this.$message.success('状态重置成功')
      } catch (error) {
        this.errors.push({ message: `重置失败: ${error.message}` })
        this.$message.error('重置失败')
      }
    },

    // 加载组件
    async loadComponents() {
      try {
        this.componentsLoading = true
        await this.$store.dispatch('pageBuilder/components/loadYYLComponents')
        this.$message.success('组件加载成功')
      } catch (error) {
        this.errors.push({ message: `组件加载失败: ${error.message}` })
        this.$message.error('组件加载失败')
      } finally {
        this.componentsLoading = false
      }
    },

    // 搜索组件测试
    searchComponents() {
      this.$store.dispatch('pageBuilder/components/searchComponents', 'Button')
      this.$message.info('搜索测试完成')
    },

    // 添加测试组件
    async addTestComponent() {
      try {
        await this.$store.dispatch('pageBuilder/designer/addComponent', {
          componentType: 'TestComponent',
          componentConfig: {
            displayName: '测试组件',
            defaultProps: { text: '测试' },
            style: { width: '100px', height: '50px' }
          },
          position: { x: Math.random() * 200, y: Math.random() * 200 }
        })
        this.$message.success('测试组件添加成功')
      } catch (error) {
        this.errors.push({ message: `添加组件失败: ${error.message}` })
        this.$message.error('添加组件失败')
      }
    },

    // 清空画布
    clearCanvas() {
      this.$store.dispatch('pageBuilder/designer/clearCanvas')
      this.$message.success('画布已清空')
    },

    // 测试撤销
    testUndo() {
      this.$store.dispatch('pageBuilder/designer/undo')
      this.$message.info('执行撤销')
    },

    // 测试重做
    testRedo() {
      this.$store.dispatch('pageBuilder/designer/redo')
      this.$message.info('执行重做')
    },

    // 创建测试页面
    async createTestPage() {
      try {
        await this.$store.dispatch('pageBuilder/pages/createPage', {
          name: `测试页面_${Date.now()}`,
          description: '这是一个测试页面'
        })
        this.$message.success('测试页面创建成功')
      } catch (error) {
        this.errors.push({ message: `创建页面失败: ${error.message}` })
        this.$message.error('创建页面失败')
      }
    },

    // 加载页面列表
    async loadPageList() {
      try {
        await this.$store.dispatch('pageBuilder/pages/loadPageList')
        this.$message.success('页面列表加载成功')
      } catch (error) {
        this.errors.push({ message: `加载页面列表失败: ${error.message}` })
        this.$message.error('加载页面列表失败')
      }
    },

    // 清除错误
    clearErrors() {
      this.errors = []
    }
  }
}
</script>

<style lang="less" scoped>
.store-test {
  padding: 20px;

  .test-section {
    margin-bottom: 24px;

    h3 {
      margin-bottom: 16px;
      color: #1890ff;
    }
  }
}
</style>
