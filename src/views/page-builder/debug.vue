<template>
  <div class="debug-page">
    <h2>页面构建器调试页面</h2>
    <p>这个页面用于测试页面构建器的功能。</p>

    <a-tabs default-active-key="scanner">
      <a-tab-pane key="scanner" tab="组件扫描器测试">
        <div class="scanner-test">
          <div class="test-actions">
            <a-button type="primary" @click="testScanner" :loading="scannerLoading">
              扫描YYL组件
            </a-button>
            <a-button @click="runQuickTest" :loading="quickTestLoading">
              快速测试
            </a-button>
            <a-button @click="clearResults">清空结果</a-button>
          </div>

          <div v-if="quickTestResult" class="quick-test-result">
            <a-alert
              :type="quickTestResult.success ? 'success' : 'error'"
              :message="quickTestResult.success ? '测试通过' : '测试失败'"
              :description="quickTestResult.success ?
                `发现 ${quickTestResult.componentCount} 个组件，配置 ${quickTestResult.configCount} 个组件` :
                quickTestResult.error"
              show-icon
            />
          </div>

          <div v-if="scannerResults.size > 0" class="scanner-results">
            <h3>扫描结果 (共{{ scannerResults.size }}个组件)</h3>

            <a-collapse>
              <a-collapse-panel
                v-for="[name, component] in scannerResults"
                :key="name"
                :header="`${component.displayName} (${component.name})`"
              >
                <div class="component-info">
                  <p><strong>分类:</strong> {{ component.category }}</p>
                  <p><strong>描述:</strong> {{ component.description }}</p>
                  <p><strong>图标:</strong> <a-icon :type="component.icon" /> {{ component.icon }}</p>
                  <p><strong>文件路径:</strong> {{ component.filePath }}</p>

                  <h4>属性 ({{ Object.keys(component.props).length }}个)</h4>
                  <a-table
                    :columns="propColumns"
                    :data-source="Object.entries(component.props).map(([key, value]) => ({key, ...value}))"
                    size="small"
                    :pagination="false"
                  />

                  <h4>事件 ({{ component.events.length }}个)</h4>
                  <a-table
                    :columns="eventColumns"
                    :data-source="component.events"
                    size="small"
                    :pagination="false"
                  />

                  <h4>默认属性</h4>
                  <pre>{{ JSON.stringify(component.defaultProps, null, 2) }}</pre>
                </div>
              </a-collapse-panel>
            </a-collapse>
          </div>

          <div v-if="scannerError" class="error-message">
            <a-alert type="error" :message="scannerError" show-icon />
          </div>
        </div>
      </a-tab-pane>

      <a-tab-pane key="config" tab="组件配置测试">
        <div class="config-test">
          <h3>YYL组件配置</h3>
          <a-select
            v-model="selectedComponent"
            placeholder="选择组件"
            style="width: 200px; margin-bottom: 16px;"
          >
            <a-select-option
              v-for="(config, name) in componentConfigs"
              :key="name"
              :value="name"
            >
              {{ config.displayName }}
            </a-select-option>
          </a-select>

          <div v-if="selectedComponentConfig" class="component-config">
            <h4>{{ selectedComponentConfig.displayName }}</h4>
            <p>{{ selectedComponentConfig.description }}</p>

            <a-tabs size="small">
              <a-tab-pane key="props" tab="属性配置">
                <pre>{{ JSON.stringify(selectedComponentConfig.propSchema, null, 2) }}</pre>
              </a-tab-pane>
              <a-tab-pane key="events" tab="事件配置">
                <pre>{{ JSON.stringify(selectedComponentConfig.events, null, 2) }}</pre>
              </a-tab-pane>
              <a-tab-pane key="defaults" tab="默认值">
                <pre>{{ JSON.stringify(selectedComponentConfig.defaultProps, null, 2) }}</pre>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </a-tab-pane>

      <a-tab-pane key="system" tab="系统信息">
        <div class="system-info">
          <h3>当前路由信息</h3>
          <pre>{{ routeInfo }}</pre>

          <h3>菜单数据</h3>
          <pre>{{ menuInfo }}</pre>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { YYL_COMPONENT_CONFIGS } from './configs/yyl-components'
import yylComponentScanner from './utils/componentScanner'

export default {
  name: 'PageBuilderDebug',
  data() {
    return {
      scannerLoading: false,
      quickTestLoading: false,
      scannerResults: new Map(),
      scannerError: '',
      quickTestResult: null,
      selectedComponent: '',
      componentConfigs: YYL_COMPONENT_CONFIGS,
      propColumns: [
        { title: '属性名', dataIndex: 'key', key: 'key' },
        { title: '类型', dataIndex: 'type', key: 'type' },
        { title: '必需', dataIndex: 'required', key: 'required' },
        { title: '默认值', dataIndex: 'default', key: 'default' },
        { title: '描述', dataIndex: 'description', key: 'description' }
      ],
      eventColumns: [
        { title: '事件名', dataIndex: 'name', key: 'name' },
        { title: '描述', dataIndex: 'description', key: 'description' },
        { title: '参数', dataIndex: 'params', key: 'params' }
      ]
    }
  },
  computed: {
    ...mapState({
      menuData: state => state.user.menuData
    }),

    selectedComponentConfig() {
      return this.selectedComponent ? this.componentConfigs[this.selectedComponent] : null
    },

    routeInfo() {
      try {
        // 安全地序列化路由信息，避免循环引用
        const safeRoute = {
          path: this.$route.path,
          name: this.$route.name,
          params: this.$route.params,
          query: this.$route.query,
          hash: this.$route.hash,
          fullPath: this.$route.fullPath,
          meta: this.$route.meta
        }
        return JSON.stringify(safeRoute, null, 2)
      } catch (error) {
        return `路由信息序列化失败: ${error.message}`
      }
    },

    menuInfo() {
      try {
        // 安全地序列化菜单数据
        if (!this.menuData) {
          return '菜单数据未加载'
        }

        // 创建一个简化的菜单数据副本
        const safeMenuData = this.createSafeObject(this.menuData)
        return JSON.stringify(safeMenuData, null, 2)
      } catch (error) {
        return `菜单数据序列化失败: ${error.message}`
      }
    }
  },
  methods: {
    async testScanner() {
      this.scannerLoading = true
      this.scannerError = ''

      try {
        console.log('开始测试YYL组件扫描器...')
        const results = await yylComponentScanner.scanYYLComponents()
        this.scannerResults = results

        this.$message.success(`扫描完成，发现 ${results.size} 个组件`)
      } catch (error) {
        console.error('扫描器测试失败:', error)
        this.scannerError = error.message
        this.$message.error('扫描失败: ' + error.message)
      } finally {
        this.scannerLoading = false
      }
    },

    clearResults() {
      this.scannerResults.clear()
      this.scannerError = ''
      this.quickTestResult = null
      this.$message.info('结果已清空')
    },

    async runQuickTest() {
      this.quickTestLoading = true

      try {
        console.log('运行快速测试...')

        // 简单测试：尝试扫描几个已知的组件
        const testResult = {
          success: true,
          componentCount: 0,
          configCount: Object.keys(this.componentConfigs).length,
          error: null
        }

        // 测试组件配置是否正确加载
        if (testResult.configCount === 0) {
          throw new Error('组件配置未正确加载')
        }

        // 测试扫描器是否能正常工作
        try {
          const components = await yylComponentScanner.scanYYLComponents()
          testResult.componentCount = components.size

          if (components.size === 0) {
            throw new Error('未扫描到任何组件')
          }
        } catch (scanError) {
          throw new Error(`组件扫描失败: ${scanError.message}`)
        }

        this.quickTestResult = testResult
        this.$message.success('快速测试通过')

      } catch (error) {
        console.error('快速测试失败:', error)
        this.quickTestResult = {
          success: false,
          error: error.message
        }
        this.$message.error('快速测试失败')
      } finally {
        this.quickTestLoading = false
      }
    },

    /**
     * 创建安全的对象副本，避免循环引用
     * @param {*} obj 要序列化的对象
     * @param {Set} visited 已访问的对象集合
     * @returns {*} 安全的对象副本
     */
    createSafeObject(obj, visited = new Set()) {
      // 处理基本类型
      if (obj === null || typeof obj !== 'object') {
        return obj
      }

      // 检查循环引用
      if (visited.has(obj)) {
        return '[Circular Reference]'
      }

      visited.add(obj)

      try {
        // 处理数组
        if (Array.isArray(obj)) {
          return obj.map(item => this.createSafeObject(item, visited))
        }

        // 处理普通对象
        const safeObj = {}
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            try {
              // 跳过一些可能导致问题的属性
              if (key.startsWith('_') || key.startsWith('$') || typeof obj[key] === 'function') {
                continue
              }

              safeObj[key] = this.createSafeObject(obj[key], visited)
            } catch (error) {
              safeObj[key] = `[Error: ${error.message}]`
            }
          }
        }

        return safeObj
      } finally {
        visited.delete(obj)
      }
    }
  },

  mounted() {
    console.log('页面构建器调试页面已加载')
    console.log('YYL组件配置:', this.componentConfigs)
  }
}
</script>

<style lang="less" scoped>
.debug-page {
  padding: 24px;

  .test-actions {
    margin-bottom: 16px;

    .ant-btn {
      margin-right: 8px;
    }
  }

  .scanner-results {
    margin-top: 16px;

    .component-info {
      h4 {
        margin-top: 16px;
        margin-bottom: 8px;
        color: #1890ff;
      }

      pre {
        background: #f5f5f5;
        padding: 12px;
        border-radius: 4px;
        overflow: auto;
        max-height: 200px;
        font-size: 12px;
      }
    }
  }

  .error-message {
    margin-top: 16px;
  }

  .config-test {
    .component-config {
      margin-top: 16px;

      h4 {
        color: #1890ff;
        margin-bottom: 8px;
      }

      pre {
        background: #f5f5f5;
        padding: 12px;
        border-radius: 4px;
        overflow: auto;
        max-height: 300px;
        font-size: 12px;
      }
    }
  }

  .system-info {
    pre {
      background: #f5f5f5;
      padding: 16px;
      border-radius: 4px;
      overflow: auto;
      max-height: 400px;
      font-size: 12px;
    }
  }
}
</style>
