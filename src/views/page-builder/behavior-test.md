# 菜单点击行为测试文档

## 测试目标
验证只有"页面设计器"菜单项在新窗口全屏打开，其他页面保持正常的路由跳转行为。

## 测试配置

### 页面构建器菜单配置
```javascript
// src/store/modules/user.js - setPageBuilderObj()
{
  Id: 'PAGE_BUILDER_MAIN',
  Name: "页面构建器",
  Path: "page-builder/index",
  Icon: 'build',
  children: [
    {
      Id: 'PAGE_BUILDER_DESIGNER',
      Name: "页面设计器",
      Path: "page-builder/fullscreen",
      OpenInNewTab: true  // ✅ 只有这个有新窗口标记
    },
    {
      Id: 'PAGE_BUILDER_PAGES',
      Name: "页面管理",
      Path: "page-builder/components/PageManager/PageList"
      // ❌ 没有OpenInNewTab标记，正常路由跳转
    },
    {
      Id: 'PAGE_BUILDER_TEMPLATES',
      Name: "模板库", 
      Path: "page-builder/components/PageManager/TemplateGallery"
      // ❌ 没有OpenInNewTab标记，正常路由跳转
    }
  ]
}
```

## 测试步骤

### 1. 测试页面设计器（应该新窗口打开）
- [ ] 点击左侧菜单："页面构建器" > "页面设计器"
- [ ] 验证：应该在新标签页/窗口中打开
- [ ] 验证：新窗口没有左侧菜单
- [ ] 验证：新窗口完全占据浏览器空间
- [ ] 验证：设计器功能正常工作

### 2. 测试页面管理（应该正常路由跳转）
- [ ] 点击左侧菜单："页面构建器" > "页面管理"
- [ ] 验证：在当前标签页中跳转
- [ ] 验证：保留左侧菜单
- [ ] 验证：正常的页面布局

### 3. 测试模板库（应该正常路由跳转）
- [ ] 点击左侧菜单："页面构建器" > "模板库"
- [ ] 验证：在当前标签页中跳转
- [ ] 验证：保留左侧菜单
- [ ] 验证：正常的页面布局

### 4. 测试其他系统菜单（应该不受影响）
- [ ] 点击其他系统菜单项
- [ ] 验证：所有其他菜单保持原有行为
- [ ] 验证：没有意外的新窗口打开

## 技术实现

### 菜单点击处理逻辑
```javascript
// src/components/menu/index.js - renderMenuItem()
renderMenuItem(menu) {
  // 检查是否需要在新标签页打开
  const shouldOpenInNewTab = menu.OpenInNewTab || menu.meta.openInNewTab
  
  if (shouldOpenInNewTab) {
    // 只有标记了OpenInNewTab的菜单项才会新窗口打开
    tag = 'a'
    const fullPath = `/#/${menu.path}`
    attrs = {
      href: fullPath,
      target: '_blank',
      onClick: (e) => {
        e.preventDefault()
        window.open(fullPath, '_blank', 'width=1400,height=900,scrollbars=no,resizable=yes')
      }
    }
  } else {
    // 其他菜单项保持正常的router-link行为
    tag = 'router-link'
    props = { to: { name: menu.name, query: { ...param } } }
  }
}
```

### 全屏样式控制
```javascript
// src/views/page-builder/fullscreen.vue
mounted() {
  // 只在全屏页面添加特殊样式类
  document.body.classList.add('fullscreen-mode')
},
beforeDestroy() {
  // 离开时移除样式类，不影响其他页面
  document.body.classList.remove('fullscreen-mode')
}
```

## 预期结果

| 菜单项 | 点击行为 | 窗口状态 | 左侧菜单 | 布局 |
|--------|----------|----------|----------|------|
| 页面设计器 | 新窗口打开 | 全屏 | 隐藏 | 设计器专用布局 |
| 页面管理 | 当前页跳转 | 正常 | 显示 | 系统默认布局 |
| 模板库 | 当前页跳转 | 正常 | 显示 | 系统默认布局 |
| 其他菜单 | 当前页跳转 | 正常 | 显示 | 系统默认布局 |

## 注意事项

1. **浏览器弹窗阻止**：某些浏览器可能阻止弹窗，需要用户允许
2. **窗口大小**：新窗口设置为1400x900，适合设计器使用
3. **样式隔离**：全屏样式只在设计器页面生效，不影响其他页面
4. **路由配置**：全屏设计器使用独立路由`/page-builder/fullscreen`

## 故障排除

### 如果新窗口没有打开
1. 检查浏览器是否阻止了弹窗
2. 检查菜单配置中的`OpenInNewTab`标记
3. 检查控制台是否有JavaScript错误

### 如果其他页面也在新窗口打开
1. 检查菜单配置，确保只有页面设计器有`OpenInNewTab: true`
2. 检查菜单渲染逻辑中的条件判断

### 如果全屏样式影响了其他页面
1. 检查CSS选择器是否使用了`body.fullscreen-mode`前缀
2. 确认页面离开时正确移除了`fullscreen-mode`类

## 测试完成标准

- ✅ 页面设计器在新窗口全屏打开
- ✅ 其他页面构建器菜单项正常跳转
- ✅ 系统其他菜单不受影响
- ✅ 全屏样式不影响其他页面
- ✅ 新窗口功能完全正常
