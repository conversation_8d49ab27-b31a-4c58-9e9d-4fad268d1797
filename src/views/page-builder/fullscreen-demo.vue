<template>
  <div class="fullscreen-demo">
    <div class="demo-header">
      <h1>全屏页面设计器演示</h1>
      <div class="demo-actions">
        <a-button @click="addSampleComponents" type="primary">
          <a-icon type="plus" />
          添加示例组件
        </a-button>
        <a-button @click="showLayoutInfo">
          <a-icon type="info-circle" />
          布局说明
        </a-button>
      </div>
    </div>

    <!-- 全屏设计器 -->
    <FullscreenDesigner ref="designer" />

    <!-- 布局说明弹窗 -->
    <a-modal
      title="全屏设计器布局说明"
      :visible="infoVisible"
      @ok="infoVisible = false"
      @cancel="infoVisible = false"
      width="700px"
    >
      <div class="layout-info">
        <h3>🎯 设计理念</h3>
        <p>全屏设计器专为专业的页面设计工作而优化，提供最大的设计空间和最佳的用户体验。</p>

        <h3>🖥️ 界面布局</h3>
        <div class="layout-features">
          <div class="feature-item">
            <h4><a-icon type="desktop" /> 全屏无干扰</h4>
            <p>移除左侧菜单，充分利用屏幕空间，专注于页面设计工作。</p>
          </div>
          
          <div class="feature-item">
            <h4><a-icon type="appstore" /> 可收起组件面板</h4>
            <p>左侧组件面板可以收起，为画布提供更大的设计空间。</p>
          </div>
          
          <div class="feature-item">
            <h4><a-icon type="setting" /> 右侧抽屉属性面板</h4>
            <p>属性面板采用抽屉式设计，不占用固定空间，按需显示。</p>
          </div>
          
          <div class="feature-item">
            <h4><a-icon type="build" /> 强化工具栏</h4>
            <p>顶部工具栏集成所有常用功能，操作更加便捷高效。</p>
          </div>
        </div>

        <h3>⚡ 核心优势</h3>
        <ul>
          <li><strong>更大画布空间</strong> - 中间画布区域占据更多屏幕空间</li>
          <li><strong>智能面板管理</strong> - 组件面板可收起，属性面板按需显示</li>
          <li><strong>专业设计体验</strong> - 类似专业设计软件的界面布局</li>
          <li><strong>无干扰设计</strong> - 移除不必要的界面元素，专注设计</li>
          <li><strong>新窗口独立</strong> - 在独立窗口中运行，不影响主系统</li>
        </ul>

        <h3>🎮 操作指南</h3>
        <div class="operation-guide">
          <div class="guide-item">
            <strong>组件面板控制：</strong>
            <p>点击工具栏的"组件"按钮可以显示/隐藏左侧组件面板</p>
          </div>
          
          <div class="guide-item">
            <strong>属性面板控制：</strong>
            <p>点击工具栏的"属性"按钮或选中组件时自动显示右侧属性面板</p>
          </div>
          
          <div class="guide-item">
            <strong>画布操作：</strong>
            <p>支持网格、标尺、缩放等辅助功能，提供精确的设计控制</p>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import FullscreenDesigner from './components/Designer/FullscreenDesigner.vue'

export default {
  name: 'FullscreenDemo',
  components: {
    FullscreenDesigner
  },
  data() {
    return {
      infoVisible: false
    }
  },
  methods: {
    // 添加示例组件
    addSampleComponents() {
      const designer = this.$refs.designer
      if (!designer) return

      // 添加示例组件
      const sampleComponents = [
        {
          componentType: 'YYLButton',
          componentConfig: {
            displayName: 'YYL按钮',
            category: 'business',
            icon: 'button',
            defaultProps: {
              text: '主要按钮',
              type: 'primary',
              size: 'default'
            },
            style: { margin: '8px' }
          },
          position: { x: 150, y: 100 }
        },
        {
          componentType: 'YYLForm',
          componentConfig: {
            displayName: 'YYL表单',
            category: 'form',
            icon: 'form',
            defaultProps: {
              layout: 'horizontal'
            },
            style: { margin: '16px', width: '400px' }
          },
          position: { x: 150, y: 200 }
        },
        {
          componentType: 'YYLTable',
          componentConfig: {
            displayName: 'YYL表格',
            category: 'table',
            icon: 'table',
            defaultProps: {
              loading: false
            },
            style: { margin: '16px', width: '500px' }
          },
          position: { x: 150, y: 350 }
        }
      ]

      sampleComponents.forEach((comp, index) => {
        setTimeout(() => {
          designer.handleAddComponent(comp)
        }, index * 500)
      })

      this.$message.success('正在添加示例组件...')
    },

    // 显示布局说明
    showLayoutInfo() {
      this.infoVisible = true
    }
  },

  mounted() {
    console.log('全屏设计器演示页面已加载')
    
    // 显示欢迎提示
    this.$notification.open({
      message: '全屏页面设计器',
      description: '体验专业级的页面设计工具，享受更大的设计空间和更好的用户体验。',
      icon: this.$createElement('a-icon', {
        props: { type: 'desktop' },
        style: { color: '#1890ff' }
      }),
      duration: 4
    })
  }
}
</script>

<style lang="less" scoped>
.fullscreen-demo {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;

  .demo-header {
    background: #fff;
    padding: 12px 24px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;

    h1 {
      margin: 0;
      color: #1890ff;
      font-size: 18px;
    }

    .demo-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .layout-info {
    h3 {
      color: #262626;
      margin: 20px 0 12px 0;
      
      &:first-child {
        margin-top: 0;
      }

      .anticon {
        color: #1890ff;
        margin-right: 8px;
      }
    }

    p {
      color: #595959;
      line-height: 1.6;
      margin-bottom: 16px;
    }

    .layout-features {
      .feature-item {
        margin-bottom: 16px;
        padding: 12px;
        background: #f9f9f9;
        border-radius: 6px;

        h4 {
          margin: 0 0 8px 0;
          color: #262626;
          font-size: 14px;

          .anticon {
            color: #1890ff;
            margin-right: 6px;
          }
        }

        p {
          margin: 0;
          font-size: 13px;
          color: #8c8c8c;
        }
      }
    }

    ul {
      margin-bottom: 20px;
      
      li {
        margin-bottom: 6px;
        line-height: 1.6;
        color: #595959;
        
        strong {
          color: #262626;
        }
      }
    }

    .operation-guide {
      .guide-item {
        margin-bottom: 12px;
        padding: 8px 12px;
        background: #e6f7ff;
        border-left: 3px solid #1890ff;
        border-radius: 4px;

        strong {
          color: #1890ff;
          display: block;
          margin-bottom: 4px;
        }

        p {
          margin: 0;
          font-size: 13px;
          color: #595959;
        }
      }
    }
  }
}
</style>
