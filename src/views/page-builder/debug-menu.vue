<template>
  <div class="debug-menu">
    <a-card title="菜单点击调试">
      <p>此页面用于调试菜单点击行为</p>
      
      <a-button @click="testNewWindowOpen" type="primary">
        测试新窗口打开
      </a-button>
      
      <a-button @click="checkMenuConfig" style="margin-left: 8px;">
        检查菜单配置
      </a-button>
      
      <div v-if="debugInfo" style="margin-top: 20px;">
        <h3>调试信息：</h3>
        <pre>{{ debugInfo }}</pre>
      </div>
    </a-card>
  </div>
</template>

<script>
export default {
  name: 'DebugMenu',
  data() {
    return {
      debugInfo: null
    }
  },
  methods: {
    testNewWindowOpen() {
      const fullPath = '/#/page-builder/fullscreen'
      const newWindow = window.open(fullPath, '_blank', 'width=1400,height=900,scrollbars=no,resizable=yes')
      
      if (newWindow) {
        this.debugInfo = '新窗口打开成功'
        this.$message.success('新窗口打开成功')
      } else {
        this.debugInfo = '新窗口打开失败，可能被浏览器阻止'
        this.$message.error('新窗口打开失败')
      }
    },
    
    checkMenuConfig() {
      // 检查菜单配置
      const menuList = this.$store.state.user.menuList
      this.debugInfo = JSON.stringify(menuList, null, 2)
    }
  }
}
</script>

<style lang="less" scoped>
.debug-menu {
  padding: 20px;
  
  pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    max-height: 400px;
    overflow-y: auto;
  }
}
</style>
