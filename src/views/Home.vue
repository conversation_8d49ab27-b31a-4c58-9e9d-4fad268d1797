<template>
  <div class="body">
    <a-row :gutter="8" class="titleRow">
      <a-col :md="7" class="row"></a-col>
      <a-col :md="10" class="row">
        <div style="position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%)">
          <img :src="backgroundUrl" width="334px" height="232px" />
        </div>
      </a-col>
      <a-col :md="7" class="row"></a-col>
    </a-row>
  </div>
</template>

<script>
export default {
  name: 'Home',
  mixins: [],
  components: {},
  props: {},
  data() {
    return {
      backgroundUrl: require('@/assets/image/indexbackground.png'),
    }
  },
  created() {},
  mounted() {},
  methods: {},
  beforeDestroy() {},
}
</script>

<style lang="less" scoped>


.body {
  padding-top: 1px;
  text-align: center;
}
.row {
  background-color: white;
  padding: 5px 5px;
  height: calc(100vh - 180px);
}
</style>
