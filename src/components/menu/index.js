import Menu from 'ant-design-vue/es/menu'
import Icon from 'ant-design-vue/es/icon'

const { Item, SubMenu } = Menu

export default {
  name: 'SMenu',
  props: {
    menu: {
      type: Array,
      required: true
    },
    theme: {
      type: String,
      required: false,
      default: 'dark'
    },
    mode: {
      type: String,
      required: false,
      default: 'inline'
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {
      openKeys: [],
      selectedKeys: [],
      cachedOpenKeys: []
    }
  },
  computed: {
    rootSubmenuKeys: vm => {
      const keys = []
      vm.menu.forEach(item => keys.push(item.path))
      return keys
    },
    allMenus() {
      return this.$store.state.user.menuList
    },
    isShowHeadTab() {
      return this.$store.state.config.isShowHeadTab
    },
    pathList() {
      return this.$store.state.config.samePathList
    }
  },
  mounted() {
    this.updateMenu()
  },
  watch: {
    collapsed(val) {
      if (val) {
        this.cachedOpenKeys = this.openKeys.concat()
        this.openKeys = []
      } else {
        this.openKeys = this.cachedOpenKeys
      }
    },
    $route: function() {
      this.updateMenu()
    }
  },
  methods: {
    /**
     * 顶部tab切换时设置左侧menu数据
     * @param {*} menuList
     */
    setMenu(menuList, rootPath) {
      //展开第一个menu
      let openKeys = ['']
      openKeys.push(menuList[0].path)
      for (const i in menuList[0].children || []) {
        let item = menuList[0].children[i]
        openKeys.push(item.path)
      }
      this.onOpenChange(openKeys)

      //选中第一个menu的第一个item
      let sKey = (menuList[0].children || []).length ? menuList[0].children[0].path : menuList[0].path
      let pKey = menuList[0].path
      this.selectedKeys = []
      this.selectedKeys.push(sKey)
      this.$emit('select', { key: sKey, pKey: pKey, rootPath: rootPath })
    },

    // select menu item
    onOpenChange(openKeys) {
      // 在水平模式下时执行，并且不再执行后续
      if (this.mode === 'horizontal') {
        this.openKeys = openKeys
        return
      }
      // 非水平模式时
      const latestOpenKey = openKeys.find(key => !this.openKeys.includes(key))
      if (!this.rootSubmenuKeys.includes(latestOpenKey)) {
        this.openKeys = openKeys
      } else {
        this.openKeys = latestOpenKey ? [latestOpenKey] : []
      }
    },
    updateMenu() {
      const routes = this.isShowHeadTab ? this.findAllParent(this.$route, this.allMenus) : this.$route.matched.concat()
      // console.log('SideMenu isShowHeadTab - '+this.isShowHeadTab,routes)
      const { hidden } = this.$route.meta
      if (routes.length >= 3 && hidden) {
        //删除最后一条数据
        routes.pop()
        //设置当前选中为删除后的最后一条
        this.selectedKeys = [routes[routes.length - 1].path]
      } else if (routes.length) {
        //设置当前中为最后一条数据的path
        this.selectedKeys = [routes.pop().path]
      }
      const openKeys = []
      if (this.mode === 'inline') {
        routes.forEach(item => {
          openKeys.push(item.path)
        })
      }
      //update-begin-author:taoyan date:20190510 for:online表单菜单点击展开的一级目录不对
      if (!this.selectedKeys || (this.selectedKeys.length && this.selectedKeys[0].indexOf(':') < 0)) {
        this.collapsed ? (this.cachedOpenKeys = openKeys) : (this.openKeys = openKeys)
      }
      //update-end-author:taoyan date:20190510 for:online表单菜单点击展开的一级目录不对
    },

    // render
    renderItem(menu) {
      if (!menu.hidden) {
        return menu.children && !menu.hideChildrenInMenu ? this.renderSubMenu(menu) : this.renderMenuItem(menu)
      }
      return null
    },
    /**
     * 二级，左边tab的点击跳转在这里
     * @param {*} menu
     */
    renderMenuItem(menu) {
      const target = menu.meta.target || null
      //左边tab跳转
      const tag = (target && 'a') || 'router-link'
      let param = menu.meta.configParams || {}
      // let newName =
      let props = { to: { name: menu.name, query: { ...param } } }
      if (menu.route && menu.route === '0') {
        props = { to: { path: menu.path, params: { ...param } } }
      }

      const attrs = { href: menu.path, target: menu.meta.target }
      if (menu.children && menu.hideChildrenInMenu) {
        // 把有子菜单的 并且 父菜单是要隐藏子菜单的
        // 都给子菜单增加一个 hidden 属性
        // 用来给刷新页面时， selectedKeys 做控制用
        menu.children.forEach(item => {
          item.meta = Object.assign(item.meta, { hidden: true })
        })
      }
      //医贸商品的相关界面path相同，所以改为唯一键Id
      let mKey = menu.path
      let item = this.pathList.find(x => {
        return mKey.startsWith(x)
      })
      if (item) {
        mKey = menu.Id
      }
      return (
        <Item {...{ key: mKey }}>
          <tag {...{ props, attrs }}>
            {this.renderIcon(menu.meta.icon)}
            <span>{menu.meta.title}</span>
          </tag>
        </Item>
      )
    },
    /**
     * 一级
     * @param {} menu
     */
    renderSubMenu(menu) {
      const itemArr = []
      if (!menu.hideChildrenInMenu) {
        menu.children.forEach(item => itemArr.push(this.renderItem(item)))
      }
      return (
        <SubMenu {...{ key: menu.path }}>
          <span slot="title">
            {this.renderIcon(menu.meta.icon)}
            <span>{menu.meta.title}</span>
          </span>
          {itemArr}
        </SubMenu>
      )
    },
    renderIcon(icon) {
      if (icon === 'none' || icon === undefined) {
        return null
      }
      const props = {}
      typeof icon === 'object' ? (props.component = icon) : (props.type = icon)
      return <Icon {...{ props }} />
    },
    findAllParent(node, tree) {
      let tabAppName = node.query.appName || ''
      let menuTitle = node.query.title || node.meta.title || ''
      let topTabId = window.localStorage.getItem('TopTabId')
      // console.log('findAllParent tabAppName = ' + tabAppName + ' /menuTitle = ' + menuTitle + ' /topTabId = ' + topTabId)
      let allParents = []
      //找到当前记录的顶部点击的tab的menu数据
      let topTabObj = topTabId ? tree.find(it => it.id === topTabId) : tree[0]
      //当前点击的tab的Children menu数据
      let secondLevelChildren = (topTabObj && topTabObj.children) || []
      for (let j = 0; j < secondLevelChildren.length; j++) {
        if (secondLevelChildren[j].children) {
          let Jchildren = secondLevelChildren[j].children
          for (let q = 0; q < Jchildren.length; q++) {
            // if (node.path === Jchildren[q].path) {
            let bool = tabAppName && menuTitle && Jchildren[q].meta.title && menuTitle == Jchildren[q].meta.title
            if (bool) {
              allParents.push(topTabObj)
              allParents.push(secondLevelChildren[j])
              allParents.push(Jchildren[q])
              allParents.push(node)
            } else if (node.path === Jchildren[q].path) {
              allParents.push(topTabObj)
              allParents.push(secondLevelChildren[j])
              allParents.push(Jchildren[q])
              allParents.push(node)
            }
          }
        }
      }
      return allParents
    }
  },

  render() {
    const { mode, theme, menu } = this
    const props = {
      mode: mode,
      theme: theme,
      openKeys: this.openKeys
    }
    const on = {
      click: obj => {
        this.selectedKeys = [obj.key]
        this.$emit('select', obj)
      },
      openChange: this.onOpenChange
    }

    const menuTree = menu.map(item => {
      /* if (item.hidden) {
        return null;
      } */
      return this.renderItem(item)
    })
    // {...{ props, on: on }}
    return (
      <Menu vModel={this.selectedKeys} {...{ props, on: on }}>
        {menuTree}
      </Menu>
    )
  }
}

