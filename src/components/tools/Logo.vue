<template>
  <div class="logo">
    <router-link :to="{ path: '/Home' }">
      <img style="width: 38px;height: 38px;" :src="imgUrl" :alt="title" />
      <span style="padding-left:10px;color:#fff;">{{ title }}</span>
    </router-link>
  </div>
</template>

<script>
export default {
  name: 'Logo',
  props: {
    title: {
      type: String,
      default: window._CONFIG['systemName'],
      required: false
    },
    showTitle: {
      type: Boolean,
      default: true,
      required: false
    },
    imgUrl: {
      type: String,
      default: require('@/assets/image/logo.png'),
      required: false
    }
  }
}
</script>
<style lang="scss" scoped>
/*缩小首页布 局顶部的高度*/
$height: 59px;
.sider {
  box-shadow: none !important;
  .logo {
    height: $height !important;
    line-height: $height !important;
    box-shadow: none !important;
    transition: background 300ms;

    a {
      color: white;
      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  &.light .logo {
    background-color: #1890ff;
  }
}
</style>
