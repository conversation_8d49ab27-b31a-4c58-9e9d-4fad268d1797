<template>
  <div class="user-wrapper" :class="theme">
    <a-dropdown>
      <span class="action action-full ant-dropdown-link user-dropdown-menu">
        <a-avatar class="avatar" size="small" :src="getAvatar()" />
        <span v-if="isDesktop()">{{ nickname() }}</span>
      </span>
      <a-menu slot="overlay" class="user-dropdown-menu-wrapper">
        <a-menu-item key="2" @click="updatePassword">
          <a-icon type="setting" />
          <span>密码修改</span>
        </a-menu-item>
      </a-menu>
    </a-dropdown>

    <span class="action">
      <a class="logout_title" href="javascript:;" @click="handleLogout">
        <a-icon type="logout" />
        <span v-if="isDesktop()">&nbsp;退出登录</span>
      </a>
    </span>
    <UserPassword ref="userPassword" />
    <!-- 版本号 -->
    <VersionModal ref="VersionModal" />
  </div>
</template>

<script>
import UserPassword from './UserPassword'
import VersionModal from './VersionModal'
import { mapActions, mapGetters } from 'vuex'
import { mixinDevice } from '@/utils/mixin.js'

export default {
  name: 'UserMenu',
  mixins: [mixinDevice],
  components: {
    UserPassword,
    VersionModal,
  },

  props: {
    theme: {
      type: String,
      required: false,
      default: 'dark'
    }
  },

  data() {
    return {}
  },

  created() {
    // 版本号
    let that = this
    this.$root.$on('versionModal', function () {
      that.versionModalShow()
    })
  },

  methods: {
    ...mapActions(['Logout']),
    ...mapGetters(['nickname', 'avatar', 'userInfo', 'username']),

    getAvatar() {
      return this.avatar()
    },

    handleLogout() {
      const that = this
      this.$confirm({
        title: '提示',
        content: '真的要注销登录吗 ?',
        onOk() {
          return that
            .Logout({})
            .then(() => {
              window.location.href = '/'
            })
            .catch(err => {
              that.$message.error({
                title: '错误',
                description: err.message
              })
            })
        },
        onCancel() { }
      })
    },
    versionModalShow() {
      this.$refs.VersionModal.show()
    },
    updatePassword() {
      let username = this.username()
      this.$refs.userPassword.show(username)
    }
  }
}
</script>

<style scoped>
.logout_title {
  color: inherit;
  text-decoration: none;
}
</style>
