<template>
  <a-modal
    :title="title"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    :closable="isNormalPassword"
    :maskClosable="isNormalPassword"
    @cancel="handleCancel"
    :class="isNormalPassword? '': 'forbiddenClose'"
    cancelText="关闭"
  >
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="formRef" :model="form" :rules="validatorRules">
        <!-- <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="旧密码"
        >
          <a-input
            type="password"
            placeholder="请输入旧密码"
            v-decorator="[ 'oldPassword', validatorRules.oldpassword]"
          />
        </a-form-item> -->

        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="新密码" prop="password">
          <a-input type="password" placeholder="密码必须是8-16位字母、数字、特殊字符组合" v-model="form.password" />
        </a-form-model-item>

        <a-form-model-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="确认新密码" prop="NewPassword">
          <a-input
            type="password"
            @blur="handleConfirmBlur"
            placeholder="请再次输入新密码"
            v-model="form.NewPassword"
          />
        </a-form-model-item>
        <a-form-model-item label="验证码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="Code">
          <a-row :gutter="16">
            <a-col class="gutter-row" :span="16">
              <a-input
                type="text"
                :maxLength="6"
                @blur="codeBlur"
                placeholder="请输入验证码"
                v-model.trim="form.Code"
              >
              </a-input>
            </a-col>
            <a-col class="gutter-row" :span="8">
              <a-button
                class="getCaptcha"
                tabindex="-1"
                :disabled="state.smsSendBtn"
                @click.stop.prevent="getCaptcha"
                v-text="(!state.smsSendBtn && '获取验证码') || state.time + ' s'"
              ></a-button>
            </a-col>
          </a-row>
        </a-form-model-item>
      </a-form-model>
    </a-spin>

    <!-- 图形验证码 -->
    <CaptchaImgModal ref="iCaptchaImgModal" @ok="sendCode" />
  </a-modal>
</template>

<script>
import Vue from 'vue'
import { postAction } from '@/api/manage'
import { sendSMSCode, resetPassword } from '@/api/login'
import { USER_PASSWORDLEVEL } from '@/store/mutation-types'
import { mapActions, mapGetters } from 'vuex'

export default {
  name: 'UserPassword',
  components: {},
  data() {
    return {
      title: '修改密码',
      modalWidth: 800,
      visible: false,
      isNormalPassword: true, // 当前用户使用密码是否符合要求
      confirmLoading: false,
      state: {
        time: 60,
        loginBtn: false,
        // login type: 0 email, 1 username, 2 telephone
        loginType: 0,
        smsSendBtn: false,
      },
      validatorRules: {
        // oldpassword: {
        //   rules: [{
        //     required: true, message: '请输入旧密码!',
        //   }],
        // },
        password: [
          {
            required: true,
            // message: '请输入新密码',
            trigger: 'change' ,
            validator: (rule, val, callback) => {
              this.checkPassword(rule, val, callback, this.form.NewPassword, 1)
            },
          }
        ],
        NewPassword: [
          {
            required: true,
            // message: '请确认新密码!',
            trigger: 'change' ,
            validator: (rule, val, callback) => {
              this.checkPassword(rule, val, callback, this.form.password, 2)
            },
          }
        ],
        Code: [
          {
            required: true,
            trigger: 'change' ,
            validator: (rule, val, callback) => {
              this.checkCode(rule, val, callback)
            },
          }
        ]
      },
      confirmDirty: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },

      form: {
        password:'',
        NewPassword:'',
        Code: ''
      },
      userName: '',
    }
  },
  mounted() {
    this.checkPassWordNormal()
  },
  methods: {
    ...mapGetters(['username']),
    ...mapActions(['Logout']),
    // 判断用户密码强度是否满足要求 passwordLevel大于3才是满足要求的
    checkPassWordNormal() {
      this.userName = this.username()
      let passwordLevel = Vue.ls.get(USER_PASSWORDLEVEL)
      if (passwordLevel && passwordLevel >= 3) {
         // 用户强密码
        this.visible = false
        this.isNormalPassword = true
      } else if (passwordLevel) {
        // 用户弱密码
        this.visible = true
        this.isNormalPassword = false
      } else {
         // 用户之前登录需要强制退出登录
         this.LogoutToLogin()
      }
    },
    // 退出并跳转登录
    LogoutToLogin() {
      this.Logout({})
      .then(() => {
      window.location.href = '/'
      })
      .catch(err => {
      that.$message.error({
      title: '错误',
      description: err.message
      })
      })
    },
    // 更新密码强度
    updatePassWordNormal() {
      Vue.ls.set(USER_PASSWORDLEVEL, 99)
      this.isNormalPassword = true
    },
    /**
     * 这个userName其实是手机号
     */
    show(uname) {
      if (!uname) {
        this.$message.warning('当前系统无登陆用户!')
        return
      }
      if (!/^1[3456789]\d{9}$/.test(uname)) {
        this.$message.error('非手机号暂不支持修改密码！')
        return
      }
      this.userName = uname
      this.visible = true
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.$refs.formRef.resetFields()
      this.visible = false
      this.disableSubmit = false
      this.selectedRole = []
    },
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.formRef.validate((val) => {
        if (val) {
          that.confirmLoading = true
          let params = Object.assign({ Phone: this.userName }, this.form)
          params.PasswordAgain = params.NewPassword
          console.log('修改密码提交数据', params)
          resetPassword(params)
            .then((res) => {
              if (res.IsSuccess) {
                console.log(res)
                that.$message.success(res.Msg ? res.Msg : '操作成功')
                this.updatePassWordNormal()
                that.close()
                // 新添加退出登录逻辑
                setTimeout(() => {
                  that.LogoutToLogin()
                }, 300)
                
              } else {
                that.$message.warning(res.Msg)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    /**
     * 验证密码
     * @param {*} rule
     * @param {*} val
     * @param {*} callback
     * @param {* 对比的值、主要用于验证两次录入的密码是否一致} dbValue
     * @returns
     */
    checkPassword(rule, val, callback, dbValue, type) {
      let regexStr = `^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[!@#$%^&*()_+\\-=\\[\\]{};'~:\"\\\\|,.<>\\/?]).{8,16}$`
      const regex = new RegExp(regexStr)
      if (val) {
        if (val.length < 8 || val.length > 16) {
          callback(new Error('密码长度必须满足8-16位'))
        } else if (!regex.test(val)) {
          callback(new Error('密码必须包含数字、字母、特殊字符'))
        } else if (dbValue && val != dbValue) {
          callback(new Error('两次输入的密码不一致'))
        }else {
          this.$refs.formRef.clearValidate(['password', 'NewPassword'])
        }
      } else if (rule.required) {
        if (type === 1) {
          callback(new Error('请输入新密码'))
        } else if (type === 2) {
          callback(new Error('请确认新密码'))
        }
      }
      callback()
    },
    checkCode(rule, val, callback) {
      if (val) {
        if (val.length >6) {
          callback(new Error('验证码最长位6位'))
        } else {
          this.$refs.formRef.clearValidate(['Code'])
        }
      } else if (rule.required) {
        callback(new Error('请输入验证码'))
      }
      callback()
    },
    // 验证码输入框失去焦点事件
    codeBlur() {
      if(this.form.Code) {
        this.$refs.formRef.clearValidate(['Code'])
      }
    },
    handleConfirmBlur(e) {
      const value = e.target.value
      this.confirmDirty = this.confirmDirty || !!value
    },
    // 获取验证码
    getCaptcha(e) {
      e.preventDefault()
      let UserName = this.userName
      if (!/^1[3456789]\d{9}$/.test(UserName)) {
        this.$message.error('非手机号暂不支持修改密码！')
        return
      }
      console.log('UserName = ' + UserName)
      if (UserName && this.$refs.iCaptchaImgModal) {
        this.$refs.iCaptchaImgModal.show()
      }
    },
    sendCode(captchaData) {
      const { state } = this

      state.smsSendBtn = true

      const interval = window.setInterval(() => {
        if (state.time-- <= 0) {
          state.time = 60
          state.smsSendBtn = false
          window.clearInterval(interval)
        }
      }, 1000)

      const hide = this.$message.loading('验证码发送中..', 0)
      let params = {
        PhoneNumber: this.userName,
        CaptchaResult: captchaData.Code,
        CaptchaId: captchaData.captchaId,
        AppId: window._CONFIG['SMSAppId'],
      }
      sendSMSCode(params)
        .then((res) => {
          setTimeout(hide, 1)
          if (res.IsSuccess) {
            this.$notification['success']({
              message: '提示',
              description: '验证码获取成功，请注意查收',
              duration: 8,
            })
          } else {
            state.time = 60
            state.smsSendBtn = false
            this.$notification['error']({
              message: '提示',
              description: res.Msg,
              duration: 8,
            })
          }
        })
        .catch((err) => {
          setTimeout(hide, 1)
          clearInterval(interval)
          state.time = 60
          state.smsSendBtn = false
        })
    },
  },
}
</script>

<style scoped lang="scss">
.forbiddenClose{
  ::v-deep .ant-modal-footer{
  &div button:first-child{
    display: none
  } 
}
}
</style>

