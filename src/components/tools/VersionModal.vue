<template>
  <a-modal :title="title" :width="modalWidth" :visible="visible" :maskClosable="false" :closable="false" @ok="handleOk">
    <div :style="{
        width: '100%',
      }">
      <div>{{text1}}</div>
      <div>若刷新后还有缓存，请使用<span style="color:red;">Ctrl+F5</span>强制刷新</div>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleOk" type="primary">刷新</a-button>
    </a-row>

  </a-modal>
</template>

<script>
import { EventBus } from '@/utils/eventBus';
export default {
  name: 'VersionModal',
  components: {},
  data() {
    return {
      title: '版本更新提示',
      text1: '当前发布了新版本，请刷新后使用系统',
      modalWidth: 500,
      visible: false,
    }
  },
  created() {
    EventBus.$on('versionModal', this.show);
  },
  methods: {
    show() {
      if (this.$root.versionModal == true) {
        return
      }
      this.$root.versionModal = true
      this.visible = true
    },
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.$root.versionModal = false
      this.visible = false
    },
    handleOk() {
      this.close()
      location.reload(true);
    },
  },
  beforeDestroy() {
    EventBus.$off('versionModal', this.show);
  },
}
</script>

<style scoped></style>

