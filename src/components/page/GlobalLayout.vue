<template>
  <a-layout class="layout" :class="[device]">
    <template v-if="layoutMode === 'sidemenu'">
      <SideMenu
        mode="inline"
        ref="sideMenu"
        :menus="curTabMenuList"
        @menuSelect="myMenuSelect"
        :theme="navTheme"
        :collapsed="collapsed"
        :collapsible="true"
      />
    </template>
    <a-layout
      :class="[layoutMode, `content-width-${contentWidth}`]"
      :style="{ paddingLeft: fixSiderbar && isDesktop() ? `${sidebarOpened ? 200 : 80}px` : '0' }"
    >
      <!-- 顶部显示登录信息的部分 -->
      <GlobalHeader
        :mode="layoutMode"
        :menus="menus"
        :theme="navTheme"
        :collapsed="collapsed"
        :device="device"
        @toggle="toggle"
        @onTabChange="onTabChange"
        :defaultSelectedKeys="defaultSelectedKeys"
      />

      <a-layout-content>
        <slot></slot>
      </a-layout-content>

      <!-- 底部版权显示部分 -->
      <!-- <a-layout-footer style="padding: 0px">
        <GlobalFooter />
      </a-layout-footer> -->
    </a-layout>
  </a-layout>
</template>

<script>
import SideMenu from '@/components/menu/SideMenu'
import GlobalHeader from '@/components/page/GlobalHeader'
import GlobalFooter from '@/components/page/GlobalFooter'
import { triggerWindowResizeEvent } from '@/utils/util'
import { mapState, mapActions } from 'vuex'
import { mixin, mixinDevice } from '@/utils/mixin.js'
//import SettingDrawer from '@/components/setting/SettingDrawer'

export default {
  name: 'GlobalLayout',
  components: {
    SideMenu,
    GlobalHeader,
    GlobalFooter,
  },
  props: {
    refreshMenu: {
      type: String,
      default: null,
    },
  },
  mixins: [mixin, mixinDevice],
  data() {
    return {
      collapsed: false,
      activeMenu: {},
      menus: [],
      curTabMenuList: [],
      defaultSelectedKeys: 0,
    }
  },
  computed: {
    ...mapState({
      // 主路由
      mainRouters: (state) => state.permission.addRouters,
      // 后台菜单
      permissionMenuList: (state) => state.user.menuList,
    }),
    isShowHeadTab() {
      return this.$store.state.config.isShowHeadTab
    },
  },
  watch: {
    sidebarOpened(val) {
      this.collapsed = !val
    },
  },
  created() {
    this.getMenusInfo()
    // 使用$root属性挂载该方法
    let _this = this
    _this.$root.$on('getMenusListData', function (type) {
      _this.getMenusListData(type)
    })
  },
  methods: {
    ...mapActions(['setSidebar']),
    getMenusInfo() {
      this.menus = this.getMenusList()
      if (this.isShowHeadTab) {
        let topTabId = window.localStorage.getItem('TopTabId')
        let curTab = {}
        if (topTabId) {
          let tabs = this.menus.filter((x) => {
            return x.id == topTabId
          })
          curTab = tabs && tabs.length > 0 ? tabs[0] : {}
          this.curTabMenuList = curTab.children || []
        } else {
          curTab = this.menus[0]
          this.curTabMenuList = this.menus[0].children || []
        }
        if (this.refreshMenu) {
          let path = this.findMenuPath(this.refreshMenu, this.curTabMenuList)
          if (path && this.curTabMenuList && this.curTabMenuList.length > 0) {
            this.onTabChange(this.curTabMenuList, curTab.path || '')
            if (topTabId) {
              this.defaultSelectedKeys = this.menus.findIndex((t) => t.id == topTabId)
            } else {
              this.defaultSelectedKeys = this.menus.findIndex((t) => t.path == path)
            }
          } else {
            this.onTabChange(this.menus[0].children, this.menus[0].path || '')
          }
        } else {
          this.onTabChange(this.menus[0].children, this.menus[0].path || '')
        }
        // if (path && this.menus && this.menus.length) {
        //   let curMenus = this.menus.filter(t => t.path == path)
        //   if (curMenus && curMenus.length && curMenus[0].children && curMenus[0].children.length) {
        //     this.onTabChange(curMenus[0].children,curMenus[0].path)
        //     this.defaultSelectedKeys = this.menus.findIndex(t => t.path == path)
        //   }
        // }
      } else {
        // 过滤菜单里面的首页
        if (this.menus.length > 1) {
          let homeindex = this.menus.findIndex((item) => {
            return item.name === 'Home'
          })
          if (homeindex > -1) {
            this.menus.splice(homeindex, 1)
          }
        }
        this.curTabMenuList = this.menus
      }
    },
    getMenusListData(type) {
      this.getMenusInfo()
    },
    findMenuPath(key, menuList) {
      if (!key || !menuList) {
        return ''
      }
      let path = key
      let index = key.indexOf('?')
      if (index != -1) {
        path = key.substring(0, index)
      }
      let newMenu = []
      menuList.forEach((it) => {
        newMenu.push({ ...it, stringifyChildren: (it.children && JSON.stringify(it.children)) || '' })
      })
      let menu = (newMenu.length && newMenu.find((it) => it.stringifyChildren.indexOf(path) > -1)) || null
      return menu ? menu.path : ''
    },
    getMenusList() {
      let list = []
      if (this.permissionMenuList && this.permissionMenuList.length > 0) {
        for (const i in this.permissionMenuList) {
          let newMenuItem = JSON.parse(JSON.stringify(this.permissionMenuList[i]))
          let twoItem = this.permissionMenuList[i].children
          if (twoItem && twoItem.children) {
            for (const j in twoItem.children) {
              let threeItem = twoItem.children[i]
              let newList = []
              for (const k in threeItem.children) {
                let item = threeItem.children[k]
                if (item.sort != -1) {
                  newList.push(item)
                }
              }
              newMenuItem.children[j].children = newList
            }
          }

          list.push(newMenuItem)
        }
      }
      return list
    },
    onTabChange(curMenuList, rootPath) {
      this.curTabMenuList = curMenuList || []
      if (this.$refs.sideMenu) {
        this.$refs.sideMenu.setMenuList(curMenuList, rootPath)
      }
    },
    toggle() {
      this.collapsed = !this.collapsed
      this.setSidebar(!this.collapsed)
      triggerWindowResizeEvent()
    },
    menuSelect() {
      if (!this.isDesktop()) {
        this.collapsed = false
      }
    },
    //update-begin-author:taoyan date:20190430 for:动态路由title显示配置的菜单title而不是其对应路由的title
    myMenuSelect(value) {
      //此处触发动态路由被点击事件
      this.findMenuBykey(this.menus, value.key)
      this.$emit(
        'dynamicRouterShow',
        value.key,
        this.activeMenu && this.activeMenu.meta ? this.activeMenu.meta.title : '',
        value.pKey,
        this.curTabMenuList,
        value.rootPath
      )
    },
    findMenuBykey(menus, key) {
      for (let i of menus) {
        if (i.path == key) {
          this.activeMenu = { ...i }
          break
        } else if (i.children && i.children.length > 0) {
          this.findMenuBykey(i.children, key)
        }
      }
    },
    //update-end-author:taoyan date:20190430 for:动态路由title显示配置的菜单title而不是其对应路由的title
  },
}
</script>

<style lang="scss">
body {
  // 打开滚动条固定显示
  overflow-y: scroll;

  &.colorWeak {
    filter: invert(80%);
  }
}

.layout {
  min-height: 100vh !important;
  overflow-x: hidden;

  &.mobile {
    .ant-layout-content {
      .content {
        margin: 24px 0 0;
      }
    }

    /**
       * ant-table-wrapper
       * 覆盖的表格手机模式样式，如果想修改在手机上表格最低宽度，可以在这里改动
       */
    .ant-table-wrapper {
      .ant-table-content {
        overflow-y: auto;
      }

      .ant-table-body {
        min-width: 800px;
      }
    }

    .sidemenu {
      .ant-header-fixedHeader {
        &.ant-header-side-opened,
        &.ant-header-side-closed {
          width: 100%;
        }
      }
    }

    .topmenu {
      /* 必须为 topmenu  才能启用流式布局 */
      &.content-width-Fluid {
        .header-index-wide {
          margin-left: 0;
        }
      }
    }

    .header,
    .top-nav-header-index {
      .user-wrapper .action {
        padding: 0 12px;
      }
    }
  }

  &.ant-layout-has-sider {
    flex-direction: row;
  }

  .trigger {
    font-size: 22px;
    line-height: 42px;
    padding: 0 18px;
    cursor: pointer;
    transition: color 300ms, background 300ms;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  .topmenu {
    .ant-header-fixedHeader {
      position: fixed;
      top: 0;
      right: 0;
      z-index: 9;
      width: 100%;
      transition: width 0.2s;

      &.ant-header-side-opened {
        width: 100%;
      }

      &.ant-header-side-closed {
        width: 100%;
      }
    }

    /* 必须为 topmenu  才能启用流式布局 */
    &.content-width-Fluid {
      .header-index-wide {
        max-width: unset;
        margin-left: 24px;
      }

      .page-header-index-wide {
        max-width: unset;
      }
    }
  }

  .sidemenu {
    .ant-header-fixedHeader {
      position: fixed;
      top: 0;
      right: 0;
      z-index: 9;
      width: 100%;
      transition: width 0.2s;

      &.ant-header-side-opened {
        width: calc(100% - 200px);
      }

      &.ant-header-side-closed {
        width: calc(100% - 80px);
      }
    }
  }

  .header {
    height: 64px;
    padding: 0 12px 0 0;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    position: relative;
  }

  .header,
  .top-nav-header-index {
    .user-wrapper {
      float: right;
      height: 100%;

      .action {
        cursor: pointer;
        padding: 0 14px;
        display: inline-block;
        transition: all 0.3s;

        height: 70%;
        line-height: 46px;

        &.action-full {
          height: 100%;
        }

        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }

        .avatar {
          margin: 20px 10px 20px 0;
          color: #1890ff;
          background: hsla(0, 0%, 100%, 0.85);
          vertical-align: middle;
        }

        .icon {
          font-size: 16px;
          padding: 4px;
        }
      }
    }

    &.dark {
      .user-wrapper {
        .action {
          color: black;

          &:hover {
            background: rgba(0, 0, 0, 0.05);
          }
        }
      }
    }
  }

  &.mobile {
    .top-nav-header-index {
      .header-index-wide {
        .header-index-left {
          .trigger {
            color: rgba(255, 255, 255, 0.85);
            padding: 0 12px;
          }

          .logo.top-nav-header {
            text-align: center;
            width: 56px;
            line-height: 58px;
          }
        }
      }

      .user-wrapper .action .avatar {
        margin: 20px 0;
      }

      &.light {
        .header-index-wide {
          .header-index-left {
            .trigger {
              color: rgba(0, 0, 0, 0.65);
            }
          }
        }

        //
      }
    }
  }

  &.tablet {
    // overflow: hidden; text-overflow:ellipsis; white-space: nowrap;
    .top-nav-header-index {
      .header-index-wide {
        .header-index-left {
          .logo > a {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .top-nav-header-index {
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    position: relative;
    transition: background 0.3s, width 0.2s;

    .header-index-wide {
      width: 100%;
      margin: auto;
      padding: 0 20px 0 0;
      display: flex;
      height: 59px;

      .ant-menu.ant-menu-horizontal {
        border: none;
        height: 64px;
        line-height: 64px;
      }

      .header-index-left {
        flex: 1 1;
        display: flex;

        .logo.top-nav-header {
          width: 165px;
          height: 64px;
          position: relative;
          line-height: 64px;
          transition: all 0.3s;
          overflow: hidden;

          img {
            display: inline-block;
            vertical-align: middle;
            height: 32px;
          }

          h1 {
            color: #fff;
            display: inline-block;
            vertical-align: top;
            font-size: 16px;
            margin: 0 0 0 12px;
            font-weight: 400;
          }
        }
      }

      .header-index-right {
        float: right;
        height: 59px;
        overflow: hidden;

        .action:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
    }

    &.light {
      background-color: #fff;

      .header-index-wide {
        .header-index-left {
          .logo {
            h1 {
              color: #002140;
            }
          }
        }
      }
    }

    &.dark {
      .user-wrapper {
        .action {
          color: white;

          &:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        }
      }

      .header-index-wide .header-index-left .trigger:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  // 内容区
  .layout-content {
    margin: 24px 24px 0px;
    height: 64px;
    padding: 0 12px 0 0;
  }
}

.topmenu {
  .page-header-index-wide {
    margin: 0 auto;
    width: 100%;
  }
}

// drawer-sider 自定义
.ant-drawer.drawer-sider {
  .sider {
    box-shadow: none;
  }

  &.dark {
    .ant-drawer-content {
      background-color: rgb(0, 21, 41);
    }
  }

  &.light {
    box-shadow: none;

    .ant-drawer-content {
      background-color: #fff;
    }
  }

  .ant-drawer-body {
    padding: 0;
  }
}

// 菜单样式
.sider {
  box-shadow: 2px 116px 6px 0 rgba(0, 21, 41, 0.35);
  position: relative;
  z-index: 10;

  &.ant-fixed-sidemenu {
    position: fixed;
    height: 100%;
  }

  .logo {
    height: 64px;
    position: relative;
    line-height: 64px;
    padding-left: 22px;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    background: #001529;
    overflow: hidden;

    img,
    h1 {
      display: inline-block;
      vertical-align: middle;
    }

    img {
      height: 32px;
    }

    h1 {
      color: #fff;
      font-size: 18px;
      margin: 0 0 0 8px;
      font-family: 'Chinese Quote', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
        'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol';
      font-weight: 600;
    }
  }

  &.light {
    background-color: #fff;
    box-shadow: 2px 116px 8px 0 rgba(29, 35, 41, 0.05);

    .logo {
      background: #fff;
      box-shadow: 1px 1px 0 0 #e8e8e8;

      h1 {
        color: unset;
      }
    }

    .ant-menu-light {
      border-right-color: transparent;
    }
  }
}

// 外置的样式控制
.user-dropdown-menu-wrapper.ant-dropdown-menu {
  padding: 4px 0;

  .ant-dropdown-menu-item {
    width: 160px;
  }

  .ant-dropdown-menu-item > .anticon:first-child,
  .ant-dropdown-menu-item > a > .anticon:first-child,
  .ant-dropdown-menu-submenu-title > .anticon:first-child .ant-dropdown-menu-submenu-title > a > .anticon:first-child {
    min-width: 12px;
    margin-right: 8px;
  }
}

// 数据列表 样式
.table-alert {
  margin-bottom: 16px;
}

.table-page-search-wrapper {
  .ant-form-inline {
    .ant-form-item {
      display: flex;
      margin-bottom: 24px;
      margin-right: 0;

      .ant-form-item-control-wrapper {
        flex: 1 1;
        display: inline-block;
        vertical-align: middle;
      }

      > .ant-form-item-label {
        line-height: 32px;
        padding-right: 8px;
        width: auto;
      }

      .ant-form-item-control {
        height: 32px;
        line-height: 32px;
      }
    }
  }

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 0;
    white-space: nowrap;
  }
}

.content {
  .table-operator {
    margin-bottom: 18px;

    button {
      margin-right: 8px;
    }
  }
}
</style>
