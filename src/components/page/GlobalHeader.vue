<template>
  <a-layout-header
    v-if="!headerBarFixed"
    :class="[
      fixedHeader && 'ant-header-fixedHeader',
      sidebarOpened ? 'ant-header-side-opened' : 'ant-header-side-closed',
    ]"
    :style="{ padding: '0' }"
  >
    <!-- 顶部导航栏模式 v-else-->
    <div :class="['top-nav-header-index', theme]">
      <div class="header-index-wide">
        <div class="header-index-left" :style="topMenuStyle.headerIndexLeft">
          <a-icon class="trigger" :type="collapsed ? 'menu-fold' : 'menu-unfold'" @click.native="toggle"></a-icon>
          <div :style="topMenuStyle.topSmenuStyle" v-if="isShowHeadTab">
            <a-menu
              theme="dark"
              mode="horizontal"
              :default-selected-keys="[defaultSelectedKeys]"
              :style="{ lineHeight: '59px' }"
              @click="onTabMebuClick"
            >
              <a-menu-item v-for="(item, index) in menus" :key="index" style="width: 100px; text-align: center">
                {{ item.meta.title }}
              </a-menu-item>
            </a-menu>
          </div>
          <div v-if="env != 'production'" style="color: red; font-weight: bold">
            当前系统环境为-【{{ getToast() }}】
          </div>
        </div>
        <user-menu class="header-index-right" :theme="theme" :style="topMenuStyle.headerIndexRight" />
      </div>
    </div>
  </a-layout-header>
</template>

<script>
import UserMenu from '../tools/UserMenu'
import SMenu from '../menu/'
import Logo from '../tools/Logo'
import { mixin } from '@/utils/mixin.js'

export default {
  name: 'GlobalHeader',
  components: {
    UserMenu,
    SMenu,
    Logo,
  },
  mixins: [mixin],
  props: {
    defaultSelectedKeys: {
      type: Number,
      default: 0,
    },
    mode: {
      type: String,
      // sidemenu, topmenu
      default: 'topmenu',
    },
    menus: {
      type: Array,
      required: true,
    },
    theme: {
      type: String,
      required: false,
      default: 'dark',
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false,
    },
    device: {
      type: String,
      required: false,
      default: 'desktop',
    },
  },
  data() {
    return {
      headerBarFixed: false,
      //update-begin--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
      topMenuStyle: {
        headerIndexLeft: {},
        topNavHeader: {},
        headerIndexRight: {},
        topSmenuStyle: {},
      },
      env: process.env.VUE_APP_PREVIEW.trim(),
    }
  },
  computed: {
    isShowHeadTab() {
      return this.$store.state.config.isShowHeadTab
    },
  },
  watch: {
    /** 监听设备变化 */
    device() {
      if (this.mode === 'topmenu') {
        this.buildTopMenuStyle()
      }
    },
    /** 监听导航栏模式变化 */
    mode(newVal) {
      if (newVal === 'topmenu') {
        this.buildTopMenuStyle()
      }
    },
  },
  //update-end--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
  mounted() {
    window.addEventListener('scroll', this.handleScroll)
    //update-begin--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
    if (this.mode === 'topmenu') {
      this.buildTopMenuStyle()
    }
    //update-end--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
  },
  methods: {
    getToast() {
      let toast = ''
      switch (this.env) {
        case 'development':
        case 'test':
          toast = '测试'
          break
        case 'production':
          toast = '生产'
          break
        case 'stage':
          toast = '预发布'
          break
      }
      return toast
    },
    onTabMebuClick(e) {
      let key = e.key
      let tab = this.menus[key]
      if (tab.path) {
        //在应用管理配置path后面跟'_xxx',xxx是应用id 比如 zbb_1,用于参数applicationId
        let value = null
        if (tab.path.indexOf('_') > -1) {
          value = tab.path.split('_')[1]
        }
        //记录当前点击的顶部tab的id
        this.$store.dispatch('setCurTabIsChecked', tab.id)
        window.localStorage.setItem('TopTabId', tab.id)
        window.localStorage.setItem('TopTabItem', JSON.stringify(tab))
        if (tab.path.startsWith('/zbb') || tab.path.startsWith('/hyyx')) {
          window.localStorage.setItem('HYSYYLAppId', value)
        } else {
          window.localStorage.setItem('HYSYYLAppId', '')
        }
      }
      if (tab && tab.children) {
        this.$emit('onTabChange', tab.children, tab.path)
      }
    },
    handleScroll() {
      if (this.autoHideHeader) {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
        if (scrollTop > 100) {
          this.headerBarFixed = true
        } else {
          this.headerBarFixed = false
        }
      } else {
        this.headerBarFixed = false
      }
    },
    toggle() {
      this.$emit('toggle')
    },
    //update-begin--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
    buildTopMenuStyle() {
      if (this.mode === 'topmenu') {
        if (this.device === 'mobile') {
          // 手机端需要清空样式，否则显示会错乱
          this.topMenuStyle.topNavHeader = {}
          this.topMenuStyle.topSmenuStyle = {}
          this.topMenuStyle.headerIndexRight = {}
          this.topMenuStyle.headerIndexLeft = {}
        } else {
          let rightWidth = '360px'
          this.topMenuStyle.topNavHeader = { 'min-width': '165px' }
          this.topMenuStyle.topSmenuStyle = { width: 'calc(100% - 165px)' }
          this.topMenuStyle.headerIndexRight = { 'min-width': rightWidth }
          this.topMenuStyle.headerIndexLeft = {
            width: `calc(100% - ${rightWidth})`,
          }
        }
      }
    },
    //update-begin--author:sunjianlei---date:20190508------for: 顶部导航栏过长时显示更多按钮-----
    setState(val, txt, fun) {
      this.$refs.userMenu.setState(val, txt, fun)
    },
    changeState(val, txt, fun) {
      this.$refs.taskToolBar.updateState(val, txt, fun)
    },
  },
}
</script>

<style lang="scss" scoped>
/* update_begin author:scott date:20190220 for: 缩小首页布局顶部的高度*/

$height: 59px;

.layout {
  .top-nav-header-index {
    .header-index-wide {
      // margin-left: 10px;

      .ant-menu.ant-menu-horizontal {
        height: $height;
        line-height: $height;
      }
    }
    .trigger {
      line-height: 64px;
      color: rgba(255, 255, 255, 0.85);
      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }

  .header {
    z-index: 2;
    color: white;
    height: $height;
    background-color: #1890ff;
    transition: background 300ms;

    /* dark 样式 */
    &.dark {
      color: #000000;
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
      background-color: white !important;
    }
  }

  .header,
  .top-nav-header-index {
    &.dark .trigger:hover {
      background: rgba(0, 0, 0, 0.05);
    }
  }
}

.ant-layout-header {
  height: $height;
  line-height: $height;
}
/* update_end author:scott date:20190220 for: 缩小首页布局顶部的高度*/
</style>
