<template>
  <a-cascader
    style="width: 100%"
    :value="defaultValue"
    :options="dataList"
    @change="handleInput"
    :loadData="loadData"
    :disabled="disabled"
    placeholder="请选择省/市/区"
    :changeOnSelect="changeOnSelect"
  />
</template>

<script>
import { getAction } from '@/api/manage'

export default {
  name: 'SelectAreaView',
  props: {
    placeholder: String,
    triggerChange: <PERSON><PERSON><PERSON>,
    disabled: Boolean,
    httpHead: {
      type: String,
      default: 'P31100'
    },
    changeOnSelect: {
      type: Boolean,
      default: true
    },
    level: {
      type: Number,
      default: 3
    },
    isListValue: {
      type: <PERSON>olean,
      default: true
    },
    value: {
      type: [String, Array]
    }, //选中值
    defaultCodes: [Array, String], //选中值，如：['北京市', '北京市', '西城区']
    selectCode: { type: Boolean, default: false }, //选择code，否则选择名称
    code: { type: String, default: null } //指定code的子集
  },
  model: {
    prop: 'defaultCodes',
    event: 'change'
  },
  data() {
    return {
      /* options: [
           {
             value: 'zhejiang',
             label: 'Zhejiang',
             isLeaf: false,
           },
           {
             value: 'jiangsu',
             label: 'Jiangsu',
             isLeaf: false,
           },
         ], */
      defaultValue: [], //['江苏省', '南京市', '江宁区'],
      dataList: [],
      url: '/{v}/AdministrativeArea/ListByCode'
    }
  },
  watch: {
    // defaultCodes(val, oldval) {
    //   if (!(val && val.length)) {
    //     this.defaultValue = []
    //   } else {
    //     console.log('watch dcodes', val)
    //     // const valArr = this.getDefaultValueByCode(val)
    //     // if (this.defaultValue.join(',') !== valArr.join(',')) {
    //     //   this.defaultValue = valArr
    //     //   this.initData(1)
    //     // }
    //   }
    // },
    value(val, oldval) {
      if (!(val && val.length)) {
        this.defaultValue = []
      }
    }
  },
  created() {
    //获取数据
    let level = 1
    if (this.code && this.code.length == 2) {
      level = 2
    }
    if (this.code && this.code.length == 4) {
      level = 3
    }
    this.initData(level)
  },
  methods: {
    changeData(data) {
      return data.map(q => {
        return {
          id: q.Id,
          level: q.Level,
          code: q.Code,
          value: this.selectCode ? q.Code : q.Level == 1 ? q.Province : q.Level == 2 ? q.City : q.County,
          label: q.Level == 1 ? q.Province : q.Level == 2 ? q.City : q.County,
          isLeaf: q.Level >= this.level ? true : false,
          source: q
        }
      })
    },
    getDefaultValueByCode(areaCode) {
      if (!areaCode) return []
      let tempArray = new Array()
      for (let i = 0; i < 3; i++) {
        if (areaCode.length >= 2 * i) {
          tempArray.push(areaCode.substring(0, 2 * i + 2))
        }
      }
      return tempArray
    },
    initData(level) {
      let params = { level: level, code: this.code || '' }
      getAction(`${this.url}?level=${level}&code=${this.code || ''}`, params, this.httpHead).then(res => {
        if (res.IsSuccess) {
          this.dataList = this.changeData(res.Data)
          if (typeof this.defaultCodes === 'string') {
            this.$set(this, 'defaultValue', this.getDefaultValueByCode(this.defaultCodes))
          }
          if (this.defaultCodes) {
            const codeVal = [...this.defaultCodes]
            if (this.defaultCodes === 'string') {
              codeVal = this.getDefaultValueByCode(this.defaultCodes)
            }

            // const codeVal =
            //   typeof this.defaultCodes === 'string'
            //     ? this.getDefaultValueByCode(this.defaultCodes)
            //     : [...this.defaultCodes]
            if (codeVal.length >= params.level) {
              let tempValue = this.getCodeByLevel(params.level)
              var selectedOptions = this.dataList.filter(q => q.level == params.level && q.code == tempValue)
              this.loadData(selectedOptions)
            }
          }
        }
      })
    },
    loadData(selectedOptions) {
      this.setDefaultValue(selectedOptions)
      const targetOption = selectedOptions[selectedOptions.length - 1]
      if (!targetOption || targetOption.level >= this.level) {
        return
      }
      targetOption.loading = true
      let params = { level: targetOption.level + 1, code: targetOption.code }
      getAction(this.url, params, this.httpHead).then(res => {
        if (res.IsSuccess) {
          targetOption.loading = false
          targetOption.children = this.changeData(res.Data)
          this.dataList = [...this.dataList]
          this.$forceUpdate()
          //return;
          if (this.defaultCodes && this.defaultCodes.length >= params.level && targetOption.children.length > 0) {
            let tempValue = this.getCodeByLevel(params.level)
            var selectedOptions = targetOption.children.filter(q => q.level == params.level && q.code == tempValue)
            this.loadData(selectedOptions)
          }
        }
      })
    },
    getCodeByLevel(level) {
      if (this.isListValue) {
        let tempValue = ''
        for (var i = 0; i < level; i++) {
          tempValue += this.defaultCodes[i]
        }
        return tempValue
      }
      return this.defaultValue[level - 1]
    },
    setDefaultValue(selectedOptions) {
      if (typeof selectedOptions != 'undefined' && typeof selectedOptions[0] != 'undefined') {
        let selectKey = this.selectCode ? 'code' : 'label'
        if (!this.defaultValue.includes(selectedOptions[0][selectKey])) {
          this.defaultValue.push(selectedOptions[0][selectKey])
          this.$forceUpdate()
        }
      }
    },
    handleInput(val, node) {
      if (val && val.length) {
        this.defaultValue = val
      }
      let changeVal = this.isListValue ? val : val.length > 0 ? val[val.length - 1] : ''
      this.$emit('change', changeVal, node)
    }
  }
}
</script>

<style scoped></style>
