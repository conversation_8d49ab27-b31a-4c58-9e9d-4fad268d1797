<template>
  <a-modal
    ref="mapModal"
    :maskClosable="false"
    :title="readonly ? '查看地图' : '请点击地图选择位置（坐标将实时更新）'"
    :width="900"
    :visible="visibleMap"
    @ok="handleOkMap"
    @cancel="handleCancelMap"
    cancelText="关闭"
  >
    <!-- <MapView ref="mapView" :center="model.address" @transferPoint="getPoint">
    </MapView> -->
    <a-spin :spinning="mapspinning">
      <a-form-model :model="model" v-if="visibleMap && !readonly">
        <a-form-model-item>
          <a-input-search
            v-model="model.DetailAddress"
            enter-button
            @search="handleDetailAddressSearch"
            @change="handleDetailAddressChange"
          />
        </a-form-model-item>
      </a-form-model>
      <a-row v-if="visibleMap" :gutter="20">
        <a-col :md="8" v-if="!readonly">
          <div id="r-result" style="height: 400px; overflow: auto;" class="scroll-style">
            <div>请在输入框输入关键字查询数据</div>
          </div>
        </a-col>
        <a-col :md="readonly ? 24 : 16">
          <div style="width: 100%; height: 400px;" id="allmap"></div>
        </a-col>
      </a-row>
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancelMap">
        关闭
      </a-button>
      <a-button @click="handleOkMap" type="primary" v-if="!readonly">确定</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import { getAction } from '@/api/manage'

export default {
  name: 'BMapView',
  props: {
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visibleMap: false,
      mapspinning: false,
      model: {
        mobile: this.phone || '',
        captcha: '',
        AdminName: '',
        OrgName: '',
        AreaId: '',
        AreaCode: '',
        AreaFullName: '',
        DetailAddress: '',
        Logo: '',
        LoginCreateDrugstoreFiles: []
      },
      _map: undefined,
      mapInfo: {
        province: '', //省
        city: '', //市
        district: '', //区县
        street: '', //街道
        streetNumber: '', //街道号码
        lat: null, //纬度
        lng: null //经度
      },
      url: {
        adminAreas: '/AdministrativeArea/List'
      }
    }
  },
  created() {},
  methods: {
    // 地图弹框
    handleCancelMap() {
      // this.$refs.mapView.reset();
      this.visibleMap = false
    },
    async handleOkMap() {
      if (this.mapInfo['province'] || this.mapInfo['city'] || this.mapInfo['district']) {
        await this.getAreaId()
      }

      if (this.mapInfo['areaCode']) this.model['AreaCode'] = this.mapInfo['areaCode']
      this.model['Longitude'] = this.mapInfo['lng']
      this.model['Latitude'] = this.mapInfo['lat']
      // console.log(this.mapInfo)
      // console.log(this.model)
      // return;
      if (this.mapInfo['province'] || this.mapInfo['city'] || this.mapInfo['district']) {
        this.model['AreaFullName'] =
          (this.mapInfo['province'] || '') + '' + (this.mapInfo['city'] || '') + '' + (this.mapInfo['district'] || '')
      } else {
        this.model['AreaFullName'] = this.model['AreaFullName'] || ''
      }

      if (!this.model['DetailAddress']) {
        if (this.mapInfo['street'] || this.mapInfo['streetNumber']) {
          this.model['DetailAddress'] = this.mapInfo['street'] + this.mapInfo['streetNumber']
        } else {
          this.model['DetailAddress'] = ''
        }
      }

      // console.log(this.model);
      this.$emit('ok', this.model)
      this.visibleMap = false
    },
    /**
     * @description: 初始化地图
     */
    initMap(eData) {
      let t = this

      this.model = Object.assign(this.model, eData)
      // console.log(this.model)

      t.visibleMap = true
      t.mapspinning = true
      // let eData = {
      //   city: this.model.AreaFullName,
      //   address: this.model.DetailAddress,
      //   longitudes: this.model.Longitude,
      //   latitudes: this.model.Latitude,
      // };
      // if (!eData.address) {
      //   this.$message.error("请先填写正确的地址。");
      //   return false;
      // }
      // 渲染地图
      setTimeout(() => {
        // 百度地图API功能
        var map = new BMap.Map('allmap') // 创建Map实例
        t._map = map
        //添加地图类型控件
        map.addControl(
          new BMap.MapTypeControl({
            mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP]
          })
        )
        if (!this.model.AreaFullName && !this.model.DetailAddress) {
          var point = new BMap.Point(116.331398, 39.897445)
          map.centerAndZoom(point, 11)
          // map.centerAndZoom("成都市", 15);
        }
        map.enableScrollWheelZoom(true) //开启鼠标滚轮缩放
        // 添加带有定位的导航控件
        var navigationControl = new BMap.NavigationControl({
          // 靠左上角位置
          anchor: BMAP_ANCHOR_TOP_LEFT,
          // LARGE类型s
          type: BMAP_NAVIGATION_CONTROL_LARGE,
          // 启用显示定位
          enableGeolocation: true
        })
        map.addControl(navigationControl)

        if (!this.model.AreaFullName && !this.model.DetailAddress) {
          //浏览器定位
          var geolocation = new BMap.Geolocation()
          geolocation.getCurrentPosition(
            function(r) {
              t.mapspinning = false
              if (this.getStatus() == BMAP_STATUS_SUCCESS) {
                var mk = new BMap.Marker(r.point)
                map.addOverlay(mk)
                map.panTo(r.point)
                // console.log("您的位置：" + r.point.lng + "," + r.point.lat);
                t.getAddress(r.point, () => {
                  t.handleDetailAddressSearch()
                })
              } else {
                alert('failed' + this.getStatus())
              }
            },
            { enableHighAccuracy: true }
          )
        } else {
          t.mapspinning = false
        }
        //点击
        if (!this.readonly) {
          map.addEventListener('click', function(e) {
            t.getAddress(e.point, () => {
              t.handleDetailAddressSearch()
            })
          })
        }

        this.changeAddress(true)
      }, 200)
    },
    /**
     * @description: 获取地址
     * @param {pointer} 经纬度
     * @return {result}
     */
    getAddress(pointer, call, title = null) {
      let t = this
      t._map.clearOverlays()
      t._map.centerAndZoom(new BMap.Point(pointer.lng, pointer.lat), 16)
      var marker = new BMap.Marker(pointer)
      t._map.addOverlay(marker)
      // console.log(title);
      // console.log(pointer)
      new BMap.Geocoder().getLocation(pointer, function(poi) {
        // console.log(poi);
        t.mapInfo.province = poi.addressComponents.province
        t.mapInfo.city = poi.addressComponents.city
        t.mapInfo.district = poi.addressComponents.district
        t.mapInfo.street = poi.addressComponents.street
        t.mapInfo.streetNumber = poi.addressComponents.streetNumber
        if (poi.surroundingPois && poi.surroundingPois.length) {
          t.mapInfo.street = poi.addressComponents.street
          t.mapInfo.streetNumber = poi.addressComponents.streetNumber + '' + poi.surroundingPois[0].title
        } else {
          t.mapInfo.street = poi.addressComponents.street || ''
          t.mapInfo.streetNumber = poi.addressComponents.streetNumber || ''
        }

        t.mapInfo.lat = poi.point.lat
        t.mapInfo.lng = poi.point.lng
        //详细地址
        t.model.AreaFullName = t.mapInfo.province + t.mapInfo.city + t.mapInfo.district
        //详细地址
        if (t.mapInfo.street || t.mapInfo.streetNumber) {
          t.model.DetailAddress = title ? title : t.mapInfo.street + t.mapInfo.streetNumber
        } else {
          t.model.DetailAddress = ''
        }

        t.model.Province = t.mapInfo.province
        t.model.City = t.mapInfo.city
        t.model.County = t.mapInfo.district
        // console.log(t.model.OrgAddress )
        marker.setLabel(
          new BMap.Label(t.model.AreaFullName + '' + t.model.DetailAddress, {
            offset: new BMap.Size(10, -10)
          })
        )
        call && call(poi)
      })
    },
    /**
     * @description: 地址在地图上展现
     */
    changeAddress(v) {
      let t = this

      if (v && t.model.Latitude && t.model.Longitude) {
        let point = {
          lng: t.model.Longitude,
          lat: t.model.Latitude
        }
        t.model.Latitude = point.lat
        t.model.Longitude = point.lng
        t.mapInfo['province'] = t.model['Province']
        t.mapInfo['city'] = t.model['City']
        t.mapInfo['district'] = t.model['County']
        t.mapInfo['lng'] = t.model['Longitude']
        t.mapInfo['lat'] = t.model['Latitude']
        t.model.AreaFullName = t.model.AreaFullName
          ? t.model.AreaFullName
          : t.model.Province + '' + t.model.City + '' + t.model.County
        t.model.DetailAddress = t.model.DetailAddress
        t._map.clearOverlays()
        t._map.centerAndZoom(new BMap.Point(point.lng, point.lat), 16)
        var marker = new BMap.Marker(point)
        t._map.addOverlay(marker)
        marker.setLabel(
          new BMap.Label(t.model.AreaFullName + '' + t.model.DetailAddress, {
            offset: new BMap.Size(10, -10)
          })
        )
        return
      }
      // 创建地址解析器实例
      var myGeo = new BMap.Geocoder()
      myGeo.getPoint(
        t.model.AreaFullName + '' + t.model.DetailAddress,
        function(point) {
          if (point) {
            t.getAddress(point)
          } else {
            alert('您选择地址没有解析到结果!')
          }
        },
        t.model.AreaFullName || '北京市'
      )
    },
    /**
     * @description: 获取区域Id
     */
    async getAreaId() {
      const _this = this
      if (!_this.mapInfo['province'] || !_this.mapInfo['city'] || !_this.mapInfo['district']) {
        _this.$message.warning('请先输入地址（若已输入，请检查地址省市区是否输入正确？例如：XX省XX市XX区）')
        return
      }
      // if (_this.mapInfo["district"]) {
      //   params = {
      //     level: 3,
      //     KeyWord: _this.mapInfo["district"],
      //   };
      // }
      let params = {
        Level: _this.mapInfo['district'] ? 3 : _this.mapInfo['city'] ? 2 : 1,
        KeyWord: _this.mapInfo['district'] || _this.mapInfo['city'] || _this.mapInfo['province']
      }
      let res = await getAction(_this.url.adminAreas, params)
      if (!res.IsSuccess) {
        _this.$message.warning(res.Msg)
        return
      }
      if (res.Data && res.Data.length) {
        let cur = res.Data.filter(
          tag =>
            tag.Province == _this.mapInfo['province'] &&
            tag.City == _this.mapInfo['city'] &&
            tag.County == _this.mapInfo['district']
        )
        if (cur.length) {
          _this.mapInfo['areaCode'] = cur[0].Code
          _this.model['AreaId'] = cur[0].Id
        } else {
          _this.$message.warning('系统中未找到该区域')
        }
      } else {
        _this.$message.warning('系统中未找到该区域')
      }
    },
    // 搜索 输入框 change
    handleDetailAddressChange() {
      var addr_res = document.getElementById('r-result')
      addr_res.innerHTML = ''
    },
    // 搜索地图
    handleDetailAddressSearch() {
      let t = this
      // var local = new BMap.LocalSearch(this._map, {
      //   renderOptions: {map: this._map, panel: "r-result"},
      //   onInfoHtmlSet:function(res){
      //     console.log(res)
      //     t.getAddress(res.point);
      //   },
      // });
      // local.search(this.model.DetailAddress);
      // local.setPageCapacity(5);

      var addr_res = document.getElementById('r-result')
      addr_res.innerHTML = ''
      var ls = new BMap.LocalSearch(this._map)
      ls.search(this.model.DetailAddress)
      ls.setSearchCompleteCallback(function(rs) {
        if (ls.getStatus() == BMAP_STATUS_SUCCESS) {
          let divHtml = ''
          for (var i = 0; i < rs.getCurrentNumPois(); i++) {
            var poi = rs.getPoi(i)
            // console.log(rs.getPoi(i))
            divHtml +=
              "<div style='padding:10px 0;border-bottom:1px solid #f1f1f1;line-height:1.5;font-size:12px' onclick=\"deliver_addr('" +
              poi.title +
              "','" +
              poi.address +
              "','" +
              poi.point.lng +
              "','" +
              poi.point.lat +
              '\')">'
            divHtml += "<div style='font-weight: 600;font-size:14px'>" + poi.title + '</div>'
            divHtml += '<div>地址：' + poi.address + '</div>'
            divHtml += '</div>'
          }
          addr_res.innerHTML = divHtml
        } else {
          addr_res.innerHTML = '<div>未查询到数据</div>'
        }
      })
      window.deliver_addr = function(title, address, lng, lat) {
        // console.log(title, address, lng, lat);
        t.getAddress(new BMap.Point(lng, lat), null, title)
      }
    }
  }
}
</script>

<style scoped></style>
