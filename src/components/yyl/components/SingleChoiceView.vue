<!--技术中心-普通调用接口单选view-->
<template>
  <a-radio-group v-if="tagType == 'radio'" @change="handleInput" :value="value" :disabled="disabled">
    <a-radio v-for="(item, key) in dataList" :key="key" :value="item.ItemValue">{{ item.ItemName }}</a-radio>
  </a-radio-group>
  
  <a-select
    v-else-if="tagType == 'select'"
    :placeholder="placeholder ? placeholder : '请选择'"
    :disabled="disabled"
    :value="value"
    @change="handleInput"
    :style="{ width: width }"
    :allowClear="allowClear"
    @focus="onFocus"
  >
    <template v-for="item in dataList">
      <a-select-option :key="item.ItemValue" :value="item.ItemValue" v-if="setNotOption(item.ItemValue) == -1">
        <span style="display: inline-block; width: 100%" :title="item.ItemName ? item.ItemName : item.ItemDescription">{{
          item.ItemName
        }}</span>
      </a-select-option>
    </template>
  </a-select>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'SingleChoiceView',
  props: {
    //http请求类型
    httpType: {
      type: String,
      default: 'GET',
    },
    //请求参数
    httpParams: {
      type: Object,
      default() {
        return {}
      },
    },
    Url: {
      type: String,
      default: '',
    },
    dataKey: {
      type: Object,
      default() {
        //name 显示内容的字段名称  value 实际的值的字段名称  nameList显示的其他字段名称（比如显示 名称/编码 这种情况就把Code放在nameList中）
        return { name: 'Name', value: 'Id', nameList: [] }
      },
    },
    placeholder: String, //提示语
    disabled: Boolean, //是否禁用
    value: {
      Type: String,
      Default: '',
    }, //选中值
    httpHead: {
      type: String,
      default: 'P36001',
    },
    type: String, //展示类型 radio 和 select
    width: { type: String, default: '100%' }, //宽度
    notOption: {
      // 过滤不显示 选项值
      type: Array,
      default: () => []
    },
    allowClear: {
      type: Boolean,
      default : true
    },
    reloadDataOnFocus: { // 是否每次聚焦都重新请求数据
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  data() {
    return {
      url: this.Url,
      dataList: [],
      tagType: '',
      loading: false
    }
  },
  created() {
    if (!this.type || this.type === 'list') {
      this.tagType = 'select'
    } else {
      this.tagType = this.type
    }
  },
  mounted() {
    !this.reloadDataOnFocus && this.getData()
  },
  methods: {
    onFocus() {
      if (!this.reloadDataOnFocus) return
      this.getData()
    },
    setNotOption(value) {
      let index = -1
      if (this.notOption.length) {
        index = this.notOption.findIndex(t => t == value)
      }
      return index
    },
    setUrl(url) {
      this.url = url
      this.getData()
      console.log('获取数据' + this.url)
    },
    resetHttpParams(params) {
      if (!params) return
      Object.keys(params).forEach((key) => {
        this.httpParams[key] = params[key]
      })
      this.getData()
    },
    getData(key, val) {
      let that = this
      if (!this.url) return

      this.loading = true
      let obj = null
      if (key) {
        this.httpParams[key] = val
      }
      if (!that.httpParams.PageIndex) {
        that.httpParams.PageIndex = 1
      }
      if (!that.httpParams.PageSize || that.httpParams.PageSize < 150) {
        that.httpParams.PageSize = 150
      }
      if (this.httpType == 'GET') {
        obj = getAction(this.url, this.httpParams, that.httpHead)
      } else if (this.httpType == 'POST') {
        obj = postAction(this.url, this.httpParams)
      }
      if (!obj) {
        return
      }
      obj.then((res) => {
        if (res.IsSuccess) {
          this.dataList = res.Data.map(item => {
            let otherName = ''
            if (this.dataKey['nameList'] && this.dataKey['nameList'].length > 0) {
              this.dataKey['nameList'].forEach((x) => {
                otherName += item[x] + '/'
              })
            }
            return {
              ItemName: item[this.dataKey['name']] + (otherName ? '/' + otherName.substring(0, otherName.length - 1) : ''),
              ItemValue: item[this.dataKey['value']],
              Item: item,
            }
          })

          this.loading = false
          this.$emit('dataLoaded', this.dataList)
        }else{
          this.$emit('dataLoaded', res)
        }
      })
    },
    // dropdownVisibleChange(open) {
    //   if (!open && this.reloadDataOnFocus) {
    //     this.dataList = []
    //   }
    // },
    handleInput(e, opt) {
      let val, txt, item
      if (this.tagType == 'radio') {
        val = e.target.value
        txt =
          this.dataList &&
          this.dataList.length &&
          this.dataList.filter((v) => v.ItemValue == val) &&
          this.dataList.filter((v) => v.ItemValue == val).length
            ? this.dataList.filter((v) => v.ItemValue == val)[0].ItemName
            : ''
      } else {
        if (e) {
          val = e
          txt = opt.componentOptions.children[0].data ? opt.componentOptions.children[0].data.attrs.title : '' //获取文本
          item = this.getItem(val)
          // console.log(item)
        } else {
          val = ''
          txt = ''
          item = null
        }
      }
      this.$emit('change', val, txt, item)
    },
    getItem(val) {
      let item = null
      if (this.dataList) {
        this.dataList.forEach((x) => {
          if (val == x.ItemValue) {
            item = x.Item
          }
        })
      }
      return item
    },
    setCurrentDictOptions(list) {
      this.dataList = list
    },
    getCurrentDictOptions() {
      return this.dataList
    },
  },
}
</script>

<style scoped></style>
