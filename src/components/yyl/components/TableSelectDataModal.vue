<template>
  <a-modal :title="title" :width="width" :visible="visible" :confirmLoading="confirmLoading" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">
    <!-- 选择弹窗 -->
    <div :style="{
        width: '100%',
        background: '#fff',
      }">
      <a-spin :spinning="confirmLoading">
        <a-tabs v-if="selectTabData.length > 0 && selectTabData.every(item=>item.tabTitle !== '')" :default-active-key="0" v-model="tabActiveKey" @change="changeTab">
          <a-tab-pane v-for="(item,index) in selectTabData" :key="index" :tab="item.tabTitle">
          </a-tab-pane>
        </a-tabs>
        <div>
          <!-- 搜索 -->
          <SimpleSearchArea ref="SimpleSearchArea" v-if="selectTabData.length > 0?selectTabData[tabActiveKey].selectTableData.searchInput.length>0:selectTableData.searchInput.length>0" :queryCol="8" :searchInput="selectTabData.length > 0?selectTabData[tabActiveKey].selectTableData.searchInput:selectTableData.searchInput" @searchQuery="searchQuery" />
          <p style="text-align: right">
            <a-button type="primary" v-if="showChooseAll" @click="clickAll">全选当页数据</a-button>
          </p>
          <a-row :gutter="10">
            <a-col :md="24">
              <!-- 数据列表 -->
              <a-table :bordered="false" ref="table" :rowKey="tableRecordKey ? tableRecordKey : (record, index) => index" size="middle" :columns="columns" :dataSource="dataSource" :rowSelection="rowSelectionInfo" :pagination="ipagination" :loading="loading" @change="handleTableChange" :scroll="{ y: '200px', x: 900 }">
                <!-- 字符串超长截取省略号显示-->
                <span slot="component" slot-scope="text">
                  <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                  <j-ellipsis :value="text" v-else />
                </span>
                <!-- 价格展示 -->
                <template slot="price" slot-scope="text">
                  <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                </template>
                <!-- 是否活动 -->
                <template slot="IsActivity" slot-scope="text">
                  <span>{{ text ? '是' : '否' }}</span>
                </template>
                <span slot="action" slot-scope="text, record,index,column">
                  <!-- 已绑定不能选择 -->
                  <template v-if="column.showKey && column.showKey ==='IsBound'">
                    <a :disabled="record[column.showKey]?true:false" @click="record[column.showKey]?null: clickRowData(record,index)">{{record[column.showKey]?'已绑定' : '选择'}}</a>
                  </template>
                  <a :disabled="(record.chooseText == '已选择' || record.chooseText == '列表已选择')?true:false" v-else @click="clickRowData(record,index)">{{record.chooseText || '选择'}}</a>
                </span>
                <!-- 促销时间 -->
                <span slot="AgreementStartTimeVal" slot-scope="text,record">
                  <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
                </span>
              </a-table>
            </a-col>
            <a-col :md="24" v-if="!isToSelectClose">
              <a-card>
                <span slot="title">已选{{name}} <span style="color: red">（共{{ dataSource2.length }}个）</span></span>
                <a-table :bordered="false" ref="table" :rowKey="tableRecordKey ? tableRecordKey : (record, index) => index" size="middle" :columns="columns" :dataSource="dataSource2" :pagination="false" :scroll="{ y: '450px', x: 900 }">
                  <!-- 字符串超长截取省略号显示-->
                  <span slot="component" slot-scope="text">
                    <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                    <j-ellipsis :value="text" v-else />
                  </span>
                  <!-- 价格展示 -->
                  <template slot="price" slot-scope="text">
                    <j-ellipsis :value="text || text==0?(text != 0?'¥'+text:'0'):'--'" />
                  </template>
                  <!-- 是否活动 -->
                  <template slot="IsActivity" slot-scope="text">
                    <span>{{ text ? '是' : '否' }}</span>
                  </template>
                  <!-- 促销时间 -->
                  <span slot="AgreementStartTimeVal" slot-scope="text,record">
                    <j-ellipsis :value="`${text || ''}~${record['AgreementEndTime'] || ''}`" :length="40" />
                  </span>
                  <span slot="action" slot-scope="text, record">
                    <a @click="deleteData(record)">删除</a>
                  </span>
                </a-table>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </a-spin>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button type="primary" @click="handleOk('close')" v-if="!isToSelectClose"> 保存 </a-button>
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel"> 取消 </a-button>
    </a-row>

  </a-modal>
</template>

<script>
import moment from 'moment'
import { getAction, putAction, postAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'TableSelectDataModal',
  mixins: [ListMixin],
  components: {
    JEllipsis,
  },
  props: {
    type: {
      type: String,
      default: 'checkbox',//checkbox 多选 radio 单选
    },
    width: {
      type: Number,
      default: 900,
    },
    // 有tab标签时候使用 数组类型
    selectTabData: {
      type: Array,
      default: () => {
        return [
          // {
          //   tabTitle: '',//tab标签名称
          //   selectTableData: {
          //     searchInput: [

          //     ],
          //     title: '',
          //     name: '',
          //     recordKey: '',//唯一值key 必要
          //     httpHead: '',
          //     isInitData: false,
          //     queryParam: {

          //     },
          //     url: {
          //       list: '',
          //       listType: '',
          //     },
          //     columns: [

          //     ],
          //   },
          // },
        ]
      }
    },
    // 普通选择框 对象类型
    selectTableData: {
      type: Object,
      default: () => {
        return {
          // searchInput: [

          // ],
          // title: '',//选择框名字
          // name: '',//name名称
          // recordKey: '',//唯一值key 必要
          // httpHead: '',
          // isInitData: false,
          // url: {
          //   list: '',
          //   listType: 'Get',
          // },
          // columns: [

          // ],
        }
      }
    },
    // 是否显示已选的列表数据
    showSelectedData: {
      type: Boolean,
      default: false
    },
    // 是否选择后直接关闭弹窗
    isToSelectClose: {
      type: Boolean,
      default: false
    },
    // 是否是Checkbook模式
    isCheckbook: {
      type: Boolean,
      default: false
    },
    // 是否显示全选当页按钮
    showChooseAll: {
      type: Boolean,
      default: false
    },
    // 隐藏recordKey提示
    hideRecordKeyMsg: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      title: '选择',
      name: '',
      visible: false,
      dateFormat: 'YYYY-MM-DD',
      dateTimeFormat: 'YYYY-MM-DD hh:mm:ss',
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ["10", "30"],
        showTotal: (total, range) => {
          return range[0] + "-" + range[1] + " 共" + total + "条";
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
      },
      rowKey: '',
      tabActiveKey: 0,
      selectData: [],
      dataSource2: [],
      initCount: null,
      confirmLoading: false,
      // 表头
      columns: [],
      queryParam: {},
      queryParamInit: {},//初始化查询条件
      selectedRows: [],
      selectedRowKeys: [],
      selectedShowRowKeys: [],
      tableRecordKey: '',
      isInit: null,
      queryCol: 6,
      httpHead: '',
      isInitData: false,
      url: {
        list: '',
      },
    }
  },
  computed: {
    rowSelectionInfo() {
      if (this.isCheckbook) {
        return {
          selectedRowKeys: this.selectedRowKeys, onChange: this.onSelectChange, getCheckboxProps: record => ({
            props: {
              disabled: record.checkbookDisabled,
            },
          })
        }
      } else {
        return null
      }
    },
  },
  mounted() {

  },
  created() {
    if (this.selectTabData.length > 0) {
      this.tableRecordKey = this.selectTabData[this.tabActiveKey].selectTableData.recordKey
    } else {
      this.tableRecordKey = this.selectTableData.recordKey
    }
  },
  methods: {
    moment,
    show(selectData, queryParam) {
      this.visible = true
      this.dataSource2 = []
      if (this.isCheckbook) {
        selectData = selectData ? Object.assign([], selectData) : []
        this.selectedShowRowKeys = selectData.map((item, index) => {
          return item[this.tableRecordKey]
        })
      }
      if (queryParam) {
        this.queryParam = queryParam;
        this.queryParamInit = queryParam;
      }
      this.selectData = selectData || []
      // Tab标签模式
      if (this.selectTabData.length > 0) {
        this.tabActiveKey = 0
        this.title = this.selectTabData[this.tabActiveKey].selectTableData.title
        this.name = this.selectTabData[this.tabActiveKey].selectTableData.name
        this.columns = this.selectTabData[this.tabActiveKey].selectTableData.columns
        this.httpHead = this.selectTabData[this.tabActiveKey].selectTableData.httpHead
        this.url = this.selectTabData[this.tabActiveKey].selectTableData.url
        this.isInitData = this.selectTabData[this.tabActiveKey].selectTableData.isInitData
        this.queryParam = this.selectTabData[this.tabActiveKey].selectTableData.queryParam;
        this.queryParamInit = this.selectTabData[this.tabActiveKey].selectTableData.queryParam;
      }
      // 普通选择模式
      if (Object.keys(this.selectTableData).length > 0) {
        this.title = this.selectTableData.title
        this.name = this.selectTableData.name
        this.columns = this.selectTableData.columns
        this.httpHead = this.selectTableData.httpHead
        this.url = this.selectTableData.url
        this.isInitData = this.selectTableData.isInitData ? this.selectTableData.isInitData : true
      }

      if (this.selectData.length > 0) {
        let rIndex = -1
        let pageIndex = this.ipagination.current
        this.selectData.map((item, index) => {
          rIndex++
          if (rIndex >= this.ipagination.pageSize) {
            rIndex = 0
            pageIndex++
          }
          item['record_key'] = this.tableRecordKey ? item[this.tableRecordKey] : (item['record_key'] ? item['record_key'] : this.setRecordKey(rIndex, pageIndex))
        })
      }

      if (!this.tableRecordKey && !this.hideRecordKeyMsg) {
        this.$message.warning('数据选择框recordKey唯一键必须有效配置,非实时数据模式可以不配置,当isCheckbook为true时必须配置,隐藏该提示请配置hideRecordKeyMsg为true')
      }

      // console.log('selectData', this.selectData)
      this.isInit = true
      this.loadData(1)
    },
    loadAfter(data, count) {
      let dataSource2KeyArray = []
      if (this.isCheckbook) {
        data.map(item => {
          item['record_key'] = item[this.tableRecordKey]
          let haveData = this.selectedShowRowKeys.find(r => {
            return r == item['record_key']
          })
          if (haveData) {
            item.checkbookDisabled = true
          }
        })
      } else {
        data.map((item, index) => {
          if (this.selectTabData.length > 0) {
            item['record_key'] = this.tableRecordKey ? item[this.tableRecordKey] : this.setRecordKey(index)
          } else {
            // 没写recordKey的情况下自己赋值recordKey的值
            item['record_key'] = this.tableRecordKey ? item[this.tableRecordKey] : this.setRecordKey(index)
          }
          dataSource2KeyArray = this.dataSource2.map((item) => {
            return item['record_key']
          })
          if (dataSource2KeyArray.indexOf(item['record_key']) > -1) {
            item.chooseText = '已选择'
          } else {
            item.chooseText = '选择'
          }
          if (this.showSelectedData && this.selectData.length > 0) {
            this.selectData.map(rItem => {
              if (rItem['record_key'] == item['record_key']) {
                item.chooseText = '列表已选择'
              }
            })
          }
        })
      }
      if (this.isInit) {
        this.initCount = count
      }

      this.dataSource = data
    },
    setRecordKey(index, pageIndex) {
      return ('table' + index.toString() + (pageIndex ? pageIndex : this.ipagination.current))
    },
    changeTab() {
      if (this.selectTabData.length > 0) {
        this.title = this.selectTabData[this.tabActiveKey].selectTableData.title
        this.name = this.selectTabData[this.tabActiveKey].selectTableData.name
        this.columns = this.selectTabData[this.tabActiveKey].selectTableData.columns
        this.httpHead = this.selectTabData[this.tabActiveKey].selectTableData.httpHead
        this.url = this.selectTabData[this.tabActiveKey].selectTableData.url
        this.isInitData = this.selectTabData[this.tabActiveKey].selectTableData.isInitData
        this.queryParam = this.selectTabData[this.tabActiveKey].selectTableData.queryParam || {};
        this.queryParamInit = this.selectTabData[this.tabActiveKey].selectTableData.queryParam || {};
        if (this.$refs.SimpleSearchArea) {
          this.$refs.SimpleSearchArea.queryParam = {}
        }
      }
      this.dataSource2 = []
      this.isInit = true
      this.loadData(1)
    },
    showHaveData(selectData) {
      this.selectData = selectData || []
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = Object.assign([], selectedRowKeys)
      selectedRows.map((item) => {
        item['record_key'] = item[this.tableRecordKey]
        let haveData = this.selectedRows.find(r => {
          return item['record_key'] == r['record_key']
        })
        if (!haveData) {
          this.selectedRows.push(item)
        }
      })
      let array = []
      selectedRowKeys.map(item => {
        let haveData = this.selectedRows.find(r => {
          return item == r['record_key']
        })
        if (haveData) {
          array.push(haveData)
        }
      })

      this.selectedRows = array
      this.dataSource2 = this.selectedRows
    },
    searchQuery(queryParam, type) {
      if (type == 'searchReset') {
        this.queryParam = this.queryParamInit;
      } else {
        this.queryParam = Object.assign(queryParam, this.queryParamInit);
      }

      this.loadData(1)
    },
    searchReset() {
      this.queryParam = {}
      this.loadData(1)
    },
    // 选择数据
    clickRowData(record, rowIndex) {
      record['record_key'] = record['record_key']
      let obj = this.dataSource2.find((v) => {
        return record['record_key'] == v['record_key']
      })
      if (obj) {
        this.$message.warning('已选择该' + this.name)
        return
      }
      let haveTableSelet = this.selectData.find((v) => {
        return record['record_key'] == v['record_key']
      })
      if (haveTableSelet) {
        this.$warning({
          title: '温馨提示',
          content: '列表中已选择该' + this.name,
          okText: '确定',
        })
        return
      } else {
        if (this.type == 'radio') {
          this.dataSource.map(item => {
            item.chooseText = ''
          })
        }
        // record.chooseText = '已选择'
        this.$set(record, 'chooseText', '已选择')
      }
      // checkbox 多选 radio 单选
      if (this.type == 'radio') {
        this.dataSource2 = [record]
      } else {
        this.dataSource2.push(record)
      }
      // 选择完后直接关闭弹窗
      if (this.isToSelectClose) {
        this.handleOk('close')
      }
    },
    // 全选当前页的所有数据
    clickAll() {
      if (this.type == 'radio') {
        this.$message.warning('单选模式下不能全选')
        return
      }
      this.dataSource.map(item => {
        let haveData = this.dataSource2.find(r => {
          return item['record_key'] === r['record_key']
        })
        if (!haveData && item.chooseText !== '列表已选择') {
          item.chooseText = '已选择'
          this.dataSource2.push(item)
        }
      })
    },
    // 删除
    deleteData(record) {
      for (var i in this.dataSource2) {
        if (this.dataSource2[i]['record_key'] == record['record_key']) {
          this.dataSource2.splice(i, 1)
        }
      }
      let item = this.dataSource.find((item) => {
        return item['record_key'] == record['record_key']
      })
      if (item) {
        item.chooseText = '选择'
      }
    },
    // 确定
    handleOk(type) {
      if (!(this.dataSource2 || []).length) {
        this.$message.warning('请先选择' + this.name + '!')
        return
      }
      if (this.selectTabData.length > 0) {
        this.dataSource2.map(item => {
          item['tabActiveKey'] = this.tabActiveKey
        })
      }
      this.$emit('chooseData', this.dataSource2, this.type)
      this.dataSource2 = []
      if (type == 'close') {
        this.close()
      }

    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.visible = false
      this.queryParam = {}
      this.selectedRowKeys = []
      this.selectionRows = []
    },
  },
}
</script>
