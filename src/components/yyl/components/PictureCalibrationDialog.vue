<!--图片校准-弹窗-->
<template>
  <a-modal
    title="图片校准"
    :width="1200"
    :visible="visible"
    :confirmLoading="confironLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    cancelText="关闭"
    :destroyOnClose="true"
  >
    <a-spin :spinning="loading">
      <div>
        <a-row :getter="10">
          <a-col :md="14">
            <div style="max-width:600px;">
              <div class="title">
                原图
                <span style="padding-left:20px;font-size:14px;color:red;">(按顺序点击4个点，可实现校准)</span>
              </div>
              <div class="pic-block text-center">
                <canvas id="canvasInput" @click="onCanvasInputClick" ref="canvasInput"></canvas>
              </div>
            </div>
          </a-col>
          <a-col :md="10">
            <div style="max-width:460px;">
              <div class="title">校准预览</div>
              <div class="pic-block text-center" v-if="!loading">
                <canvas id="canvasOutput" ref="canvasOutput"></canvas>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-col :md="12" style="text-align: center;">
        <img src="@assets/image/icon_left.png" class="rotate-img" @click="onRotateClick('left')">
        <img src="@assets/image/icon_right.png" class="rotate-img" @click="onRotateClick('right')">
      </a-col>
      <a-col :md="12">
        <a-button @click="handleCancel">取消</a-button>
        <a-button
          @click="handleOk"
          type="primary"
          :loading="confironLoading"
          v-show="replaceType==1 || replaceType==2"
        >替换</a-button>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin.js'
export default {
  name: 'PictureCalibrationDialog',
  mixins: [ListMixin],
  components: {},
  // props: {
  //   // 存储桶
  //   bName: {
  //     type: String,
  //     default: window._CONFIG.AliOssConfig['AuthBucketName']
  //   },
  //   // 文件夹
  //   dir: {
  //     type: String,
  //     default: window._CONFIG.AliOssConfig['AuthDir']
  //   }
  // },
  data() {
    return {
      oldUrl: '', //原图
      loading: false,
      confironLoading: false,
      visible: false,

      rectPoint: [],
      lastImage: null,
      canvas: null, //原图canvas组件
      ctx: null,
      zoom: 1,
      bName: window._CONFIG.AliOssConfig['AuthBucketName'],
      dir: window._CONFIG.AliOssConfig['AuthDir'],
      startNum: 50,

      imgW: 0,
      imgH: 0,

      step: 0,
      replaceType: 0 //替换类型 1 替换时保存canvasInput 2替换时保存canvasOutput
    }
  },
  mounted() {},
  methods: {
    show(url) {
      console.log('bName = ' + this.bName + ' dir = ' + this.dir)
      this.oldUrl = url
      this.lastImage = null
      this.rectPoint = []
      this.loading = true
      this.confironLoading = false
      this.visible = true
      if (this.oldUrl) {
        this.$nextTick(function() {
          this.canvas = this.$refs.canvasInput
          this.ctx = this.canvas.getContext('2d')
          this.getImage(this.oldUrl, url => {
            this.onCanvasInputChange(url)
          })
        })
      }
    },
    /**
     * 开始绘制校准图
     */
    startDraw() {
      itemExtract(cv.imread('canvasInput'), 'canvasOutput', this.rectPoint)
      this.replaceType = 2
    },
    /**
     * 原图change事件
     */
    onCanvasInputChange(imgUrl) {
      // let imgUrl = URL.createObjectURL(this.oldUrl);
      let that = this
      if (imgUrl) {
        this.lastImage = new Image()
        this.lastImage.crossOrigin = 'anonymous'
        this.lastImage.onload = function() {
          that.imgW = that.lastImage.width
          that.imgH = that.lastImage.height

          that.rotateAndDrawImage()

          that.rectPoint = []

          that.loading = false
        }
        this.lastImage.src = imgUrl
      } else {
        this.loading = false
      }
    },
    /**
     * 旋转图片
     */
    onRotateClick(r) {
      this.rotateImg(r)
    },
    /**
     * 原图点击事件
     */
    onCanvasInputClick(e) {
      let that = this
      let zoom = this.canvas.zoom
      let x = e.layerX / zoom
      let y = e.layerY / zoom - this.startNum / zoom
      //'layerX = ' + e.layerX + ' layerY = ' + e.layerY +

      let pointX = x
      let pointY = y

      switch (this.step) {
        case 1:
          pointX = y
          pointY = -x //-y
          break
        case 2:
          pointX = -x
          pointY = -y
          break
        case 3:
          pointX = -y
          pointY = x
          break
      }

      console.log('step = ' + this.step + ' zoom = ' + zoom + ' x = ' + pointX + ' y = ' + pointY)

      this.rectPoint.push({ x: x, y: y })

      this.ctx.beginPath()
      //画圆
      this.ctx.arc(pointX, pointY, 10 / zoom, 0, Math.PI * 2, true)
      this.ctx.fillStyle = 'red'
      this.ctx.fill() //实心圆

      this.ctx.fillStyle = 'white'
      let size = 10 / zoom
      size = size < 10 ? 12 : size > 40 ? size + 30 : size + 10
      this.ctx.font = size + 'px 宋体' //22
      this.ctx.fillText(this.rectPoint.length, pointX - 5 / zoom, pointY + 5 / zoom)

      this.ctx.closePath()

      if (this.rectPoint.length >= 4) {
        that.rotateAndDrawImage()
        that.startDraw()
        that.rectPoint = []
      }
    },

    getImage(url, callback) {
      var xhr = new XMLHttpRequest()
      xhr.open('get', url, true)
      // 设置请求头（这一步得设置不然oss图片还是跨域）
      xhr.setRequestHeader('Cache-Control', 'no-cache')
      xhr.setRequestHeader('Access-Control-Allow-Origin', '*')
      xhr.responseType = 'blob'
      xhr.onload = function() {
        if (this.status == 200) {
          let imgUrl = URL.createObjectURL(this.response)
          callback && callback(imgUrl)
        } else {
          this.loading = false
        }
      }
      xhr.send()
    },
    /**
     * 保存校准后的图片
     * @param callback 回调
     */
    saveNewImage(callback) {
      let that = this
      let canvas =
        this.replaceType == 1 ? this.$refs.canvasInput : this.replaceType == 2 ? this.$refs.canvasOutput : null
      if (!canvas) {
        return
      }
      //canvas转换为blob
      canvas.toBlob(function(blob) {
        console.log('that.bName = ' + that.bName + ' that.dir = ' + that.dir, blob)
        //上传blob到oss
        that.uploadBlob(
          blob,
          realurl => {
            callback && callback(realurl)
          },
          that.bName,
          that.dir
        )
      })
    },
    /**
     * 旋转照片,计算当前step
     * @param direction 旋转方向
     * 备注：翻转功能未实现
     */
    rotateImg(direction) {
      let that = this
      //最小与最大旋转方向，图片旋转4次后回到原方向
      const min_step = 0
      const max_step = 3
      if (this.step == null) {
        this.step = min_step
      }
      if (direction == 'right') {
        this.step++ //旋转到原位置，即超过最大值
        this.step > max_step && (this.step = min_step)
      } else if (direction == 'left') {
        this.step--
        this.step < min_step && (this.step = max_step)
      } else if (direction == 'lr') {
        if (this.step != 1 && this.step != 3) {
          this.step = 3
        } else {
          if (this.step == 3) {
            this.step = 0
          } else {
            this.step = 3
          }
        }
      } else if (direction == 'ud') {
        if (this.step != 2 && this.step != 0) {
          this.step = 2
        } else {
          if (this.step == 2) {
            this.step = 0
          } else {
            this.step = 2
          }
        }
      }

      this.rectPoint = []
      this.rotateAndDrawImage()
    },
    /**
     * 计算旋转角度和画图
     */
    rotateAndDrawImage() {
      let degree = (this.step * 90 * Math.PI) / 180
      let w = this.imgW
      let h = this.imgH
      console.log('step = ' + this.step)
      if (this.step > 0 && this.step < 4) {
        this.replaceType = 1
      }
      switch (this.step) {
        case 0:
          this.drawImage(w, h, degree)
          break
        case 1:
          w = this.imgH
          h = this.imgW
          this.drawImage(w, h, degree, 0, -this.imgH - this.startNum)
          break
        case 2:
          this.drawImage(w, h, degree, -this.imgW - this.startNum, -this.imgH - this.startNum)
          break
        case 3:
          w = this.imgH
          h = this.imgW
          this.drawImage(w, h, degree, -this.imgW - this.startNum, 0)
          break
      }
    },
    /**
     * 画图
     * @param img 图片
     * @param imgw  图片宽度
     * @param imgh  图片高度
     * @param x 在画布上放置图像的 x 坐标位置
     * @param y 在画布上放置图像的 y 坐标位置
     *
     */
    drawImage(imgw, imgh, degree, x, y) {
      this.canvas.width = imgw + this.startNum * 2
      this.canvas.height = imgh + this.startNum * 2
      this.canvas.zoom = this.canvas.clientWidth / this.canvas.width
      if (degree) {
        this.ctx.rotate(degree)
      }
      if (this.lastImage) {
        this.ctx.clearRect(0, 0, imgw + this.startNum * 2, imgh + this.startNum * 2)
      }

      console.log(
        'degree=' + degree + ' x=' + x + ' y=' + y + ' imgw=' + imgw + ' imgh=' + imgh + ' zoom=' + this.canvas.zoom
      )
      this.ctx.drawImage(this.lastImage, x || this.startNum, y || this.startNum)
    },
    /**
     * 确定
     */
    handleOk() {
      this.confironLoading = true
      this.saveNewImage(url => {
        if (url) {
          this.$emit('ok', url)
          this.close()
        } else {
          this.$message.warning('操作失败！')
        }
        this.confironLoading = false
      })
    },
    /***
     * 取消
     * */
    handleCancel() {
      this.close()
    },

    close() {
      this.visible = false
      this.step = 0
      this.replaceType = 0
    }
  }
}
</script>

<style scoped>
.title {
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 20px;
}
.pic-block > canvas {
  width: 100%;
}
.text-center {
  text-align: center !important;
  border: 1px solid #e9e9e9;
}

.rotate-img {
  width: 20px;
  margin-right: 10px;
}
</style>

