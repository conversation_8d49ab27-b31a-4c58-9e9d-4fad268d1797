<template>
  <div class="imgDiv">
    <!-- {{ imgIndex }} -->
    <!--  {
          zoomIn: true,
          zoomOut: true,
          reset: true,
          rotateLeft: true,
          rotateRight: true,
          flipHorizontal: true,
          flipVertical: true,
        } -->

    <viewer
      :images="imgUrlList"
      v-viewer="{
        inline: true,
        fullscreen: false,
        navbar: false,
        title: false,
        button: false,
        toolbar: true,
        view: onViewed,
      }"
      style="background-color: #999; width: 100%; height: 100%"
    >
      <img
        v-for="(src, index) in imgUrlList"
        :src="src"
        :key="index"
        style="width: 100%; height: 100%; margin: auto; display: none"
        :id="`img${index}`"
        class="img-style"
      />
    </viewer>
  </div>
</template>

<script>
export default {
  name: 'ImageViewer',
  props: {
    imgList: {
      type: Array,
      default: () => {
        return []
      },
    },
    urlKey: {
      type: String,
      default: 'FileUrl',
    },
    title: {
      type: String,
      default: '图片',
    },
    imgIndex: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      curIndex: 0,
      imgUrlList: [],
    }
  },
  created() {},
  mounted() {
    this.curIndex = this.imgIndex
    this.parseImgList()
  },
  methods: {
    onViewed(e) {
      // console.log('curIndex       ',e.detail.index)
      // console.log('curIndex       ',document.getElementById(`img${e.detail.index}`))
      document.querySelector('.img-style').style.display = 'none'
      this.$nextTick(() => {
        document.getElementById(`img${e.detail.index}`).style.display = 'inline-block'
      })
    },
    parseImgList() {
      if (this.imgList.length > 0) {
        let fristList = []
        this.imgList.forEach((item, i, arr) => {
          if (typeof item == 'Object' || typeof item == 'object') {
            if (item[this.urlKey]) {
              if (i == this.imgIndex) {
                fristList.push(item[this.urlKey])
              } else {
                this.imgUrlList.push(item[this.urlKey])
              }
            }
          } else if (typeof item == 'string') {
            if (i == this.imgIndex) {
              fristList.push(item)
            } else {
              this.imgUrlList.push(item)
            }
          }
        })
        this.imgUrlList = [].concat(fristList, this.imgUrlList)
        console.log('imgUrlList', this.imgUrlList)
        this.$nextTick(() => {
          setTimeout(() => {
            if (document.getElementById('img0') && document.getElementById('img0').style) {
              document.getElementById('img0').style.display = 'inline-block'
            }
          }, 300)
        })
      }
    },
    toLeft() {
      console.log(this.imgIndex)
      if (this.curIndex == 0) {
        this.$message.warning('已经是首张' + this.title + '了')
        return
      }
      this.curIndex--
    },
    toRight() {
      if (this.curIndex >= this.imgList.length - 1) {
        this.$message.warning('已经是最后一张' + this.title + '了')
        return
      }
      this.curIndex++
    },
    previewChange() {
      // 获取遮罩层dom
      let domImageMask = document.querySelector('.a-image-viewer__mask')
      if (!domImageMask) {
        return
      }
      document.querySelector('.a-image-viewer__close').click()
    },
  },
}
</script>

<style lang="less" scoped>
.imgDiv {
  width: 100%;
  text-align: center;
  overflow: hidden;
  vertical-align: middle;
  height: 450px;
  // padding: 50px;
  position: relative;
  margin: 16px 0;
  // div {
  //
  // background-size: contain;
  // background-position: center;
  // background-repeat: no-repeat;
  // width: 100%;
  // height: 250px;
  // margin: 0 auto; /*水平居中*/
  // position: relative;
  // top: 50%; /*偏移*/
  // margin-top: -150px;
  // }
}
</style>
