<!--
 * @Description: 通过 SystemTool/GetEnumsByName?enumName= 接口获取枚举数据的 下拉单选/radio
 * @Version: 1.0
 * @Author: 继承
 * @Date: 2025-02-10 11:15:32
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-03-26 10:03:53
-->

<template>
  <a-radio-group v-if="tagType == 'radio'" @change="handleInput" :value="value" :disabled="disabled">
    <template v-for="(item, key) in dataList">
      <a-radio :key="key" :value="
          dictCode == 'EnumCustomerSource' ? (item.ItemName ? item.ItemName : item.ItemDescription) : item.ItemValue
        " v-if="setOption(item.ItemValue)">{{ item.ItemName ? item.ItemName : item.ItemDescription }}</a-radio>
    </template>
  </a-radio-group>

  <a-select v-else-if="tagType == 'select'" :placeholder="placeholder ? placeholder : '请选择'" :disabled="disabled" :value="value" @change="handleInput" :style="{ width: width }" :allowClear="allowClear">
    <!-- <a-select-option value>{{ placeholder ? placeholder : '请选择' }}</a-select-option> -->
    <template v-for="(item, key) in dataList">
      <a-select-option :key="key" :value="isNumber?(item.ItemValue?parseInt(item.ItemValue):''):item.ItemValue" v-if="setOption(item.ItemValue)">
        <span style="display: inline-block; width: 100%" :title="item.ItemName ? item.ItemName : item.ItemDescription">{{ item.ItemName ? item.ItemName : item.ItemDescription }}</span>
      </a-select-option>
    </template>
  </a-select>
</template>

<script>
import { getAction } from '@/api/manage'
export default {
  name: 'EnumSingleChoiceView',
  props: {
    httpHead: String,
    dictCode: String, //枚举接口请求枚举关键字
    placeholder: String, //提示语
    disabled: Boolean, //是否禁用
    // model是否绑定文字，默认不绑定
    isTextValue: {
      type: Boolean,
      default: false
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    value: {
      Type: String,
      Default: undefined
    }, //选中值
    tagType: {
      type: String,
      default: () => {
        return 'select'
      }
    }, //展示类型 radio 和 select
    notOption: {
      // 过滤不显示 选项值
      type: Array,
      default: () => []
    },
    onlyOption: {
      // 过滤只显示 选项值
      type: Array,
      default: () => []
    },
    // 枚举值是否是数字
    isNumber: {
      type: Boolean,
      default: false
    },
    width: { type: String, default: '120px' } //宽度
  },
  data() {
    return {
      dataList: []
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  created() {
    //获取字典数据
    this.initDictData()
  },
  methods: {
    initDictData() {
      //根据字典Code, 初始化字典数组
      getAction(`/{v}/SystemTool/GetEnumsByName?enumName=${this.dictCode}`, this.httpHead).then(res => {
        if (res.IsSuccess) {
          // console.log(res.Data);
          this.dataList = res.Data
          this.$emit(
            'loaded',
            this.dataList.map(m => {
              return {
                value: m.ItemValue,
                text: m.ItemName ? m.ItemName : m.ItemDescription
              }
            })
          )
        }
      })
    },
    handleInput(e, opt) {
      let val, txt
      if (this.tagType == 'radio') {
        val = e.target.value
        let cur = this.dataList.filter(t => t.ItemValue == val)
        txt = cur && cur.length ? cur[0].ItemName || cur[0].ItemDescription : ''
      } else {
        // if (e) {
        val = e
        txt = opt && opt.componentOptions.children[0].data ? opt.componentOptions.children[0].data.attrs.title : '' //获取文本
        // } else {
        //   val = ''
        //   txt = ''
        // }
      }
      let emitVal = this.isTextValue ? txt : val
      this.$emit('change', emitVal, txt)
    },
    setCurrentDictOptions(dataList) {
      this.dataList = dataList
    },
    getCurrentDictOptions() {
      return this.dataList
    },
    setOption(value) {
      let index = true
      // 不展示
      if (this.notOption.length) {
        index = this.notOption.findIndex(t => t == value)
        if (index >= 0) {
          return false
        } else {
          return true
        }
      }
      // 只展示
      if (this.onlyOption.length) {
        index = this.onlyOption.findIndex(t => t == value)
        if (index >= 0) {
          return true
        } else {
          return false
        }
      }
      return index
    }
  }
}
</script>

<style scoped></style>
