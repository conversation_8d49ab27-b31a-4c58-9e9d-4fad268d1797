<template>
  <div class="clearfix">
    <a-upload
      :action="action"
      :listType="listType"
      :data="{ isup: 1 }"
      :multiple="false"
      :fileList="fileList"
      :headers="uploadGetHeaders()"
      :beforeUpload="beforeUpload"
      @preview="handlePreview"
      @change="handleChange"
      :remove="handleRemove"
    >
      <div v-show="listType !== 'text'" v-if="fileList.length < 10">
        <a-icon :type="loading ? 'loading' : 'plus'" />
        <div class="ant-upload-text">{{ showText }}</div>
      </div>
      <div v-show="listType === 'text'" v-if="fileList.length < 10">
        <a-button>
          <a-icon :type="loading ? 'loading' : 'plus'" />
          {{ showText }}
        </a-button>
      </div>
    </a-upload>
    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancelImage">
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
    <!--<a-input type="hidden" placeholder="请输入横幅图片（多个以,分隔）" v-decorator="['banners', {}]" />-->
  </div>
</template>
<script>
import Vue from 'vue'
import { ACCESS_TOKEN } from '@/store/mutation-types'
function getBase64(img, callback) {
  const reader = new FileReader()
  reader.addEventListener('load', () => callback(reader.result))
  reader.readAsDataURL(img)
}
export default {
  props: {
    showText: { type: String, default: '上传' },
    listType: { type: String, default: 'picture-card' }, //text picture-card picture
    action: { type: String, default: window._CONFIG['uploadURL'] },
    fileType: { type: String, default: '' }
  },
  data() {
    return {
      loading: false,
      //上传相关
      previewVisible: false,
      previewImage: '',
      fileList: [],
      fileUrls: [],
      fileOk:false
      // action: window._CONFIG['uploadURL'],
    }
  },
  // 计算属性
  computed: {
    accessToken() {
      return Vue.ls.get(ACCESS_TOKEN)
    }
  },
  created() {
    if (this.imageUrl) {
      this.uploadUrl = this.imageUrl
    }
  },
  methods: {
    beforeUpload(file) {
      let fileOk = false
      if (this.fileType === '') {
        const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
        if (!isJPG) {
          this.$message.error('只能上传 JPG/PNG 文件!')
        }
        const isLt2M = file.size / 1024 / 1024 < 2
        if (!isLt2M) {
          this.$message.error('图片不能大于 2MB!')
        }
        fileOk =  isJPG && isLt2M
      }else if (this.fileType === 'all') {
        fileOk =  true
      } else {
        const isType = this.fileType === file.type
        if (!isType) {
          this.$message.error('You can only upload ' + this.fileType + ' file!')
        }
        fileOk = isType
      }
      this.fileOk = fileOk;
      return fileOk
    },
    uploadGetHeaders() {
      let headers = {}
      headers['Authorization'] = 'Bearer ' + this.accessToken
      return headers
    },

    init(fileList) {
      this.fileList = fileList
    },
    getFileList() {
      let fileList = this.fileList
      let reList = []
      fileList.forEach((file, i) => {
        if (file.status == 'done') {
          reList.push({ id: file.uid, image: file.url, sortNo: 0 })
        }
      })
      return reList
    },

    //上传事件
    handleCancelImage() {
      this.previewVisible = false
    },
    handlePreview(file) {
      this.previewImage = file.url || file.thumbUrl
      this.previewVisible = true
    },
    handleChange({ fileList }) {
      console.log('fileList', fileList)
      if(!this.fileOk) return;
      this.fileUrls = []
      fileList.forEach((file, i) => {
        if (file.status == 'done') {
          let uploadUrl = ''
          if (file.url && file.url.length > 5) {
            uploadUrl = file.url
          } else if (file.response && file.response.IsSuccess && file.response.Data) {
            uploadUrl = file.response.Data
          }
          if (uploadUrl) {
            let foundItem = this.fileUrls.filter(url => {
              return url == uploadUrl
            })
            if (!foundItem || foundItem.length == 0) {
              this.fileUrls.push(uploadUrl)
              file.url = uploadUrl
            }
          }
        }
      })
      this.fileList = fileList
      //const fileUrl = this.fileUrls.map((item) => item).join(',')
    },
    handleRemove(file) {
      //删除，已在change中处理
      /*        for(var i=0;i<this.fileUrls.length;i++){
                if(this.fileUrls[i].url==file.url){
                  this.fileUrls.splice(i,1);
                  break;
                }
              }
              const fileUrl = this.fileUrls.map((item) => item).join(',')
              this.$nextTick(() => {
                this.form.setFieldsValue({ "banners": fileUrl });
              });*/
      return true
    }
  }
}
</script>
<style>
.avatar-uploader > .ant-upload {
  width: 100px;
  height: 100px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
.uploadImg {
  max-width: 100px;
  max-height: 100px;
}
</style>
