<!-- 资质列表 -->
<template>
  <div v-if="rowData.tabList && rowData.tabList.length > 0">
    <div class="a-tag-box">
      <a-row :gutter="20">
        <!-- 左边-资质列表 -->
        <a-col :span="3" v-if="showTitleCol">
          <div class="a-tag-box-type">
            <div v-for="(item, tIndex) in rowData.tabList" :key="item.Title" @click="onTabClick(item, tIndex)">
              <span
                :class="{
                  active: !item.hasWarning,
                  warning: item.hasWarning,
                  warningActive: item.hasWarning
                }"
                style="margin-bottom: 16px">
                {{ (item.Title?item.Title:item.Name) + '(' + item.Sum + ')' }}
              </span>
            </div>
          </div>
        </a-col>
        <a-col :span="showTitleCol?21:24">
          <div class="preview-box">
            <div class="container">
              <!-- PDF展示 -->
              <div class="pdf-box" v-if="rowData.tabList[tabIndex].Files[imgIndex].FileUrl.indexOf('.pdf' || '.PDF') > -1">
                <pdf
                  ref="pdf"
                  :rotate="pageRotate"
                  :src="getPdfUrl(rowData.tabList[tabIndex].Files[imgIndex].FileUrl)"
                  v-for="(i,index) in numPages"
                  :page="i"
                  :key="index"></pdf>
                <div class="pdf-operator">
                  <div class="pdf-operator-ctn">
                    <div @click="operator('nsz')">
                      <a-icon style="font-size:30px" type="redo" />
                      <span>顺时针</span>
                    </div>
                    <div @click="operator('ssz')">
                      <a-icon style="font-size:30px" type="undo" />
                      <span>逆时针</span>
                    </div>
                    <div @click="operator('yl',rowData.tabList[tabIndex].Files[imgIndex].FileUrl)">
                      <a-icon style="font-size:30px" type="fullscreen" />
                      <span>预览</span>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 图片展示 -->
              <div v-else class="container-box">
                <a-carousel @afterChange="previewChange" dotPosition="bottom" :autoplay="false" height="58vh" style="background: rgba(0, 0, 0, 0.4)">
                  <viewer
                    :images="rowData.tabList[tabIndex].Files"
                    v-viewer="{
                      inline: false,
                      fullscreen: false,
                      navbar: false,
                      title: false,
                      button: false
                    }">
                    <!-- style="display: none;" -->
                    <div style="height: 58vh; margin: auto; padding: 20px">
                      <img
                        :src="
                          rowData.tabList[tabIndex].Files && rowData.tabList[tabIndex].Files.length > 0
                            ? rowData.tabList[tabIndex].Files[imgIndex].FileUrl
                            : ''
                        "
                        style="width: 100%;height: 100%; margin: auto" />
                    </div>
                  </viewer>
                </a-carousel>
              </div>
              <!-- 图片分页 -->
              <div class="container-page" v-if="rowData.tabList[tabIndex].Files.length > 0">
                <span v-for="(file, index) in rowData.tabList[tabIndex].Files" :key="index" :class="{ 'page-active': rowData.tabList[tabIndex].Files[imgIndex].FileUrl === file.FileUrl }"></span>
              </div>
              <!-- 上一页 -->
              <div class="page-prev" @click="onChangeImageClick('prev')" v-if="tabIndex > 0 || (tabIndex === 0 && imgIndex > 0)">
                <a-icon type="left" />
              </div>
              <!-- 下一页 -->
              <div
                class="page-next"
                @click="onChangeImageClick('next')"
                v-if="
                  tabIndex < rowData.tabList.length - 1 ||
                    (tabIndex === rowData.tabList.length - 1 && imgIndex < rowData.tabList[tabIndex].Files.length - 1)
                ">
                <a-icon type="right" />
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
// import pdf from "vue-pdf"
import pdf from 'vue-pdf-signature'
import CMapReaderFactory from "vue-pdf-signature/src/CMapReaderFactory.js";
import moment from 'moment'
// 引入api请求配置
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'QualificationsListComponents',
  props: {
    // 监听父组件传递的row对象
    rowData: {
      type: Object,
      default: () => { }
    },
    // 是否可编辑
    isEdit: {
      type: Boolean,
      default: false
    },
    // 是否需要左边的title选项
    showTitleCol: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      tabIndex: 0, //当前tab的index
      imgIndex: 0, //当前tab下File的当前显示index
      pageRotate: 0,
      numPages: 1,
      rowDataInfo: this.rowData,
    }
  },
  components: {
    pdf, CMapReaderFactory,
  },
  watch: {
    rowData(val) {
      this.rowDataInfo = val
      this.fristPdf()
    }
  },
  computed: {},
  created() {
    this.initImages()
  },
  methods: {
    moment,
    initImages() {
      this.tabIndex = 0
      this.imgIndex = 0
    },
    fristPdf() {
      // 首张是pdf获取页码
      let tabIndex = 0
      let imgIndex = 0
      if (this.rowDataInfo.tabList[tabIndex] && this.rowDataInfo.tabList[tabIndex].Files[imgIndex] && this.rowDataInfo.tabList[tabIndex].Files.length > 0) {
        if (this.rowDataInfo.tabList[tabIndex].Files[imgIndex].FileUrl.indexOf('.pdf' || '.PDF') > -1) {
          this.getNumPages(this.rowDataInfo.tabList[tabIndex].Files[imgIndex].FileUrl)
        }
      }
    },
    getPdfUrl(pdfUrl) {
      if (pdfUrl && pdfUrl.indexOf('.pdf' || '.PDF') >= 0) {
        return pdf.createLoadingTask({ url: pdfUrl, CMapReaderFactory });
      } else {
        return pdfUrl
      }
    },
    operator(type, url) {
      if (type == 'nsz') {
        if (this.pageRotate == 360) {
          this.pageRotate = 0
        }
        this.pageRotate = this.pageRotate + 90
      } else if (type == 'ssz') {
        if (this.pageRotate == 0) {
          this.pageRotate = 360
        }
        this.pageRotate = this.pageRotate - 90
      } else if (type == 'yl') {
        window.open(url)
      }
    },
    // 获取pdf的页码
    getNumPages(pdfUrl) {
      if (pdfUrl.indexOf('.pdf' || '.PDF') == -1) {
        return
      }
      let loadingTask = pdf.createLoadingTask(pdfUrl);
      loadingTask.promise
        .then((pdf) => {
          this.numPages = pdf.numPages;
          console.log('页码是', pdf.numPages)
        })
        .catch((err) => {
          console.error('pdf 加载失败', err);
        });
    },

    /**
     * 左边tab项点击事件
     */
    onTabClick(tab, tIndex) {
      this.tabIndex = tIndex
      this.$forceUpdate()
    },
    /*
     * 大图预览
     */
    previewChange() {
      // 获取遮罩层dom
      let domImageMask = document.querySelector('.a-image-viewer__mask')
      if (!domImageMask) {
        return
      }
      document.querySelector('.a-image-viewer__close').click()
    },

    /*
     * 图片分页器
     * type = prev 上一页
     * type = next 下一页
     */
    onChangeImageClick(type) {
      if (!this.rowData.tabList || this.rowData.tabList.length === 0) {
        return
      }

      this.previewChange()
      //只有一个tab
      if (this.rowData.tabList.length === 1) {
        if (type === 'prev') {
          if (this.imgIndex > 0) {
            this.imgIndex = this.imgIndex - 1
          }
        } else if (type === 'next') {
          if (this.imgIndex < this.rowData.tabList[this.tabIndex].Files.length - 1) {
            this.imgIndex = this.imgIndex + 1
          }
        }
      } else {
        //多个tab
        if (type === 'prev') {
          if (this.imgIndex > 0) {
            this.imgIndex = this.imgIndex - 1
          } else {
            if (this.tabIndex > 0) {
              this.tabIndex = this.tabIndex - 1
              this.imgIndex = this.rowData.tabList[this.tabIndex].Files.length - 1
            }
          }
        } else if (type === 'next') {
          if (this.imgIndex < this.rowData.tabList[this.tabIndex].Files.length - 1) {
            this.imgIndex = this.imgIndex + 1
          } else {
            if (this.tabIndex < this.rowData.tabList.length - 1) {
              this.tabIndex = this.tabIndex + 1
              this.imgIndex = 0
            }
          }
        }
      }
      // this.$forceUpdate()
      console.log('tabIndex = ' + this.tabIndex + ' / imgIndex = ' + this.imgIndex)
    }
  }
}
</script>

<style lang="scss" scoped>
.pdf-box {
  height: 60vh;
}
.pdf-box:hover .pdf-operator-ctn {
  display: flex;
}
.pdf-operator {
  width: 100%;
  // width: inherit;
  position: fixed;
  bottom: 17%;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pdf-operator-ctn {
  width: 23%;
  background: #ddd;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  // margin-right: 4%;
  border-radius: 10px;
  display: none;
}
.pdf-operator-ctn div {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
::v-deep .a-tag-header {
  .label-font {
    position: relative;
    padding-left: 10px;
    font-weight: bold;
  }
  .label-font:after {
    position: absolute;
    width: 5px;
    height: 100%;
    background-color: #409eff;
    left: 0;
    top: 0;
    content: '';
  }
}
::v-deep .a-tag-box {
  margin: 0 8px 8px 0;
  .a-tag-box-type {
    span {
      display: block;
      text-align: center;
      padding: 10px 5px;
      border: 1px solid #ecf5ff;
      background-color: #ecf5ff;
      border-radius: 4px;
      margin-bottom: 10px;
      color: #409eff;
      cursor: pointer;
    }
    span:last-child {
      margin-bottom: 0;
    }
    .active {
      background-color: #409eff !important;
      color: #ffffff !important;
      font-weight: bold;
    }
    .warning {
      color: red !important;
      background-color: #fff0f0 !important;
      border: 1px solid #fff0f0 !important;
    }
    .warningActive {
      color: #ffffff !important;
      background-color: #f56c6c !important;
      border: 1px solid #f56c6c !important;
    }
  }
  .preview-box {
    margin: 0 0 10px 0;
  }
  .a-carousel__item h3 {
    color: #475669;
    font-size: 14px;
    opacity: 0.75;
    line-height: 150px;
    margin: 0;
  }
  .flex-box {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .text-center {
    text-align: center;
  }
}
.a-icon-circle-close {
  color: #000000 !important;
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 16px;
}
.container {
  height: 60vh;
  position: relative;
  // overflow: hidden;
  .page-prev {
    position: absolute;
    top: 28vh;
    left: 5px;
    z-index: 999;
    border-radius: 100%;
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 30px;
    box-shadow: 0 0 10px #ccc;
    background: #ffffff;
    cursor: pointer;
  }
  .page-next {
    position: absolute;
    top: 28vh;
    right: 5px;
    z-index: 999;
    border-radius: 100%;
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 30px;
    box-shadow: 0 0 10px #ccc;
    background: #ffffff;
    cursor: pointer;
  }
  .container-box {
    height: 58vh;
  }
  .container-page {
    text-align: center;
    span {
      display: inline-block;
      background: #dcdfe6;
      color: #dcdfe6;
      margin: 0 6px;
      height: 4px;
      width: 25px;
    }
    .page-active {
      background: #409eff;
      color: #409eff;
    }
  }
}
</style>
