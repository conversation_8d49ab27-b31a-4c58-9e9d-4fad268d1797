<template>
  <div :class="fileBreak == 'row' ? 'rflex' : 'rflex-break'" :style="fileBreak == 'row' ? rootSize : ''">
    <div class="img-item" :style="{ marginBottom: fileBreak == 'break' ? '10px' : '0' }" v-for="(imgUrl, index) in max == 'maxNum'
        ? images.concat([''])
        : images.length < max && add
        ? images.concat([''])
        : images" :key="index">
      <a-spin :spinning="upload['loading' + index] || false">
        <div @click.stop="!imgUrl ? selectImage(index) : ''" :style="background" v-if="readOnly ? (imgUrl ? true : false) : true" class="iFile" @mouseover="iconShow = true" @mouseleave="iconShow = false">
          <div v-if="!imgUrl" style="line-height: 1">
            <a-icon type="plus" />
            <p style="color: #858b9c; font-size: 12px; margin: 0; line-height: 2">{{ uploadTextData || '上传图片' }}</p>
          </div>
          <div v-else class="level-pdiv">
            <div class="level-img">
              <!-- pdf图标 -->
              <img v-if="fileTypeData == 3 || imgUrl.indexOf('.pdf' || '.PDF') > -1" src="@/assets/image/pdficon.png" alt="" style="width: 80px" />
              <img v-else :src="imgUrl" :style="imageSize" />
            </div>
            <div class="level-option">
              <a-icon type="eye" @click.stop="handlePreview(index)" style="color: #fff" />
              <a-icon type="delete" style="margin-left: 10px; color: #fff" @click.stop="onClearImage(index)" v-if="!readOnly" />
            </div>
          </div>

          <div v-if="imageHints && imageHints.length > 0 && index < imageHints.length" class="toast-div" :style="{ color: imgUrl ? 'white' : '' }">
            <span>{{ imageHints[index] }}</span>
          </div>
        </div>
      </a-spin>
    </div>
    <!-- 只读模式并且没有图片的情况 -->
    <div v-if="readOnly && images.length == 0">
      <div class="iFile">
        <div style="line-height: 1">
          <a-icon type="plus" />
          <p style="color: #858b9c; font-size: 12px; margin: 0; line-height: 2">{{ uploadTextData || '上传图片' }}</p>
        </div>
      </div>
    </div>
    <!-- 大图 -->
    <a-modal :visible="previewVisible" @cancel="handleCancel" :width="preModalWidth" v-if="previewVisible" :footer="null" :maskClosable="true">
      <div class="imgDiv" :style="{height:preImgHeight+'px'}">
        <viewer :images="previewImageArr" v-viewer="{
            inline: true,
            fullscreen: false,
            navbar: false,
            title: false,
            button: false,
            toolbar: {
              zoomIn: true,
              zoomOut: true,
              reset: true,
              rotateLeft: true,
              rotateRight: true,
              flipHorizontal: true,
              flipVertical: true,
            },
          }" style="background-color: #999; width: 100%; height: 100%">
          <img v-for="(src, index) in previewImageArr" :src="src" :key="index" style="display: none; width: 100%; height: 100%" :id="`img${index}`" />
        </viewer>
      </div>
    </a-modal>
    <!-- 文件选择弹框 -->
    <a-modal title="选择上传方式" :visible="imgVisible" @cancel="imgVisible = false" :width="600" v-if="chooseFileType" :footer="null" :maskClosable="true">
      <div class="fileTypeBox">
        <div v-for="(item,index) in fileTypeArray" :key="index" @click="choosefileTypeItem(item)">{{item.title}}</div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin.js'
export default {
  name: 'MultiUpload',
  mixins: [ListMixin],
  props: {
    // 最大图片数量
    max: {
      type: [Number, String],
      default: 2,
    },
    // 图片集合
    images: {
      type: Array,
      default: function () {
        return []
      },
    },
    // 图片备注集合
    imageHints: {
      type: Array,
      default: function () {
        return []
      },
    },
    /**
     * 是否显示添加
     */
    add: {
      type: Boolean,
      default: true,
    },
    // 存储桶
    bName: {
      type: String,
      default: window._CONFIG.AliOssConfig['BaseBucketName'],
    },
    // 文件夹
    dir: {
      type: String,
      default: window._CONFIG.AliOssConfig['CommonDir'],
    },
    // 文件大小限制，单位M，默认20M
    fileSize: {
      type: Number,
      default: 20,
    },
    // pdf文件大小限制，单位M，默认50M
    fileSizePdf: {
      type: Number,
      default: 50,
    },
    //文件类型 1 图片 2 所有文件 3 pdf 4 'image/jpeg,image/jpg,image/png,image/bmp'
    fileType: {
      type: Number,
      default: 4,
    },
    // 文件是否换行 row不换行 break 换行
    fileBreak: {
      type: String,
      default: 'row',
    },
    // 提示文本
    uploadText: {
      type: String,
      default: '上传图片',
    },
    // 只读
    readOnly: {
      type: Boolean,
      default: false,
    },
    //上传之前执行函数 例如test(()=>{})
    beforeCall: {
      type: Function,
      default: undefined,
    },
    width: {
      type: Number,
      default: 120,
    },
    // 图片文件在数组中的索引
    aIndex: {
      type: Number,
      default: -1,
    },
    // 是否显示文件选择框
    chooseFileType: {
      type: Boolean,
      default: null,
    },
    // 预览的弹框宽度
    preModalWidth: {
      type: Number,
      default: 750,
    },
    // 预览的图片高度
    preImgHeight: {
      type: Number,
      default: 550,
    },
  },
  watch: {
    fileType(val) {
      this.fileTypeData = val
    },
    uploadText(val) {
      this.uploadTextData = val
    },
  },
  data() {
    return {
      iconShow: false,
      imgVisible: false,
      previewVisible: false,
      fileTypeData: this.fileType,
      uploadTextData: this.uploadText,
      previewImageArr: [],
      fileTypeArray: [
        { title: '上传图片', type: 4 },
        { title: '上传PDF', type: 3 },
      ],
      newUrl: '',
      loading: false,

      curImgUrl: '', //当前
      curImgIndex: 0,
      curIndex: 0,
      upload: {},
    }
  },
  computed: {
    background() {
      return {
        borderColor: 'rgba(0,0,0,0.2)',
        width: (this.width || 120) + 'px',
      }
    },
    imageSize() {
      return {
        width: (this.width || 120) + 'px',
        height: (this.width || 120) + 'px',
      }
    },
    rootSize() {
      return {
        height: this.width ? this.width + 10 + 'px' : '130px',
      }
    },
    rules() {
      return {
        CredentialsBumber: [{ required: false, message: '请输入!' }],
        EndValidityDate: [{ required: false, message: '请选择!' }],
      }
    },
  },
  methods: {
    /**
     * 选择图片
     * @param index 当前点击的索引
     */
    selectImage(index, type) {
      this.curIndex = index
      let fileTypeText = ''
      if (this.fileTypeData == 1) {
        fileTypeText = ''
      } else if (this.fileTypeData == 2) {
        fileTypeText = '*'
      } else if (this.fileTypeData == 3) {
        fileTypeText = '.pdf'
      } else if (this.fileTypeData == 4) {
        fileTypeText = 'image/jpeg,image/jpg,image/png,image/bmp'
      }
      if (this.images.length === 0 && this.chooseFileType && type !== 'choose') {
        this.imgVisible = true
        return
      }
      if (this.readOnly) {
        return
      }
      // this.upload['loading' + this.curIndex] = true
      this.$forceUpdate()
      if (this.beforeCall) {
        this.beforeCall(() => {
          this._file(this.fileCallBack, fileTypeText, this.bName, this.dir, this.handleBeforeUpload)
        })
      } else {
        this._file(this.fileCallBack, fileTypeText, this.bName, this.dir, this.handleBeforeUpload)
      }
    },
    choosefileTypeItem(item) {
      this.imgVisible = false
      this.fileTypeData = item.type
      this.uploadTextData = item.title
      this.selectImage(this.curIndex, 'choose')
      this.$emit('choosefileTypeItem', item)
    },
    fileCallBack(bool, realurl, b64) {
      this.upload['loading' + this.curIndex] = false
      if (bool && realurl) {
        this.emitChange(realurl, this.curIndex)
      } else {
        this.$forceUpdate()
        if (realurl != 'cancel') this.$message.warning(realurl)
      }
    },
    // 文件大小限制
    handleBeforeUpload(files) {
      if (this.fileTypeData == 3) {
        if (files && Array.from(files).some((res) => res.size / (1024 * 1024) > this.fileSizePdf)) {
          this.$message.warn(`pdf文件大小不能超过${this.fileSizePdf}M`)
          return false
        }
      } else {
        if (files && Array.from(files).some((res) => res.size / (1024 * 1024) > this.fileSize)) {
          this.$message.warn(`文件大小不能超过${this.fileSize}M`)
          return false
        }
      }
      return true
    },
    emitChange(value, index) {
      this.images[index] = value
      this.$forceUpdate()
      this.$emit('change', this.images)
    },
    /**
     * 删除当前图片
     * @param index 当前点击的索引
     */
    onClearImage(index) {
      if (this.images[index]) {
        this.images.splice(index, 1)
        this.$emit('change', this.images)
        this.$forceUpdate()
      }
      if (this.images.length === 0 && this.chooseFileType !== null) {
        this.uploadTextData = '上传文件'
      }
      // this.$emit('clear')
    },
    handlePreview(index) {
      if (!this.images || index >= this.images.lenght) {
        return
      }
      // 如果是pdf模式
      if (this.fileTypeData == 3 || this.images[index].indexOf('.pdf' || '.PDF') > -1) {
        window.open(this.images[index])
        return
      }
      this.previewImageArr = [this.images[index]]
      this.previewVisible = true
      this.$nextTick(() => {
        setTimeout(() => {
          document.getElementById('img0').style.display = 'inline-block'
        }, 300)
      })
    },

    showImg(src) {
      let a = document.createElement('a')
      a.href = src
      a.target = '_blank'
      a.click()
    },
    handleCancel() {
      this.previewVisible = false
    },
    // 确定
    handleOk() {
      this.handleCancel()
    },
  },
}
</script>
<style lang="less" scoped>
/deep/.qualification-check {
  margin-left: 16px;
  span:last-child {
    padding-right: 0;
  }
}
/deep/.qualification-date {
  width: calc(100% - 72px);
}
.imgDiv {
  width: 100%;
  text-align: center;
  overflow: hidden;
  vertical-align: middle;
  height: 450px;
  // padding: 50px;
  position: relative;
  margin: 16px 0;
  div {
    // width: 100%;
    // height: 100%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 250px;
    margin: 0 auto; /*水平居中*/
    position: relative;
    // top: 50%; /*偏移*/
    // margin-top: -150px;
  }
}
.rflex {
  display: flex;
  width: 100%;
  height: 300px;
  flex-direction: row;
  overflow-x: auto;
}
.rflex-break {
  display: flex;
  width: 100%;
  height: 260px;
  flex-direction: row;
  overflow-x: auto;
  flex-wrap: wrap;
}
.img-item {
  /* background-color: rebeccapurple; */
  white-space: nowrap;
  margin-right: 10px;
}
.level-pdiv {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}
.level-option {
  position: absolute;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  z-index: 10;
}
.level-img {
  position: absolute;
  z-index: 2;
  width: 100%;
}
/* 图片 */
.iFile {
  position: relative;
  border: 1px dashed rgba(0, 0, 0, 0.2);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 120px;
  height: 120px;
  /* line-height: 120px; */
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;

  background-color: #f9fafb;
}

.thumbBody {
  position: relative;
  overflow: hidden;
}

.thumb {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

.thumb > img {
  transition: all 0.3s;
  max-width: 100%;
}

.thumb.round {
  padding: 4px;
  border: 1px solid transparent;
  background-color: #fff;
  border-radius: 50%;
}

.iFile .remove {
  position: absolute;
  top: -8px;
  right: -8px;
  line-height: 1;
}
.picture_jz_btn {
  position: absolute;
  top: 50px;
  right: 50px;
  z-index: 99;
}
.toast-div {
  position: absolute;
  z-index: 10;
  bottom: 0;
  font-size: 12px;
}
.fileTypeBox {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.fileTypeBox div {
  width: 40%;
  height: 100px;
  border: 1px solid #ddd;
  color: rgba(0, 0, 0, 0.85);
  border-radius: 10px;
  line-height: 100px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
}
.fileTypeBox div:hover {
  background: #ddd;
  color: #1890ff;
}
</style>
