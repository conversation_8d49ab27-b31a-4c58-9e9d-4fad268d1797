<!--树形组件-->
<template>
  <div>
    <a-input-search style="margin-bottom: 8px;" placeholder="请输入" @change="onSearch" v-if="showSearch" />
    <template v-if="DataList && DataList.length">
      <a-tree :tree-data="DataList" @select="change">
        <!-- :defaultSelectedKeys="[DataList[0].key]" -->
        <template slot="title" slot-scope="{ title }">
          <span v-if="title.indexOf(searchValue) > -1">
            {{ title.substr(0, title.indexOf(searchValue)) }}
            <span style="color: #f50;">{{ searchValue }}</span>
            {{ title.substr(title.indexOf(searchValue) + searchValue.length) }}
          </span>
          <span v-else>{{ title }}</span>
        </template>
      </a-tree>
    </template>
    <div v-else style="font-size: 14px; color: #777;">搜索无数据</div>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'TreeView',
  props: {
    httpType: {
      type: String,
      default: 'GET'
    },
    showSearch: {
      type: Boolean,
      default: false
    },
    list: {
      type: Object,
      default() {
        return null
      }
    },
    httpParams: {
      type: Object,
      default() {
        return {}
      }
    },
    Url: {
      type: String,
      default: ''
    },
    httpHead: {
      type: String,
      default: ''
    },
    dataKey: {
      type: Object,
      default() {
        return {
          value: 'Code',
          label: 'Name',
          children: 'Childrens',
          nameList: []
        }
      }
    }
  },
  data() {
    return {
      url: this.Url,
      DataList: [],
      selected: {
        key: '',
        id: '',
        title: ''
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData(keyWord = '') {
      if (!this.url) return
      let obj = null
      // console.log('httpType',this.httpType)
      this.httpParams.KeyWord = keyWord
      if (this.httpType == 'GET') {
        obj = getAction(this.url, this.httpParams, this.httpHead)
      } else if (this.httpType == 'POST') {
        obj = postAction(this.url, this.httpParams, this.httpHead)
      }
      if (!obj) {
        return
      }
      obj.then(res => {
        if (res.IsSuccess) {
          const data = res.Data
          let list = this.setList(data)
          if (this.list) {
            this.DataList = [this.list].concat(list)
          } else {
            this.DataList = [].concat(list)
          }
          // if (this.DataList && this.DataList.length) {
          //   this.selected = {
          //     key: this.DataList[0].key,
          //     title: this.DataList[0].title,
          //   };
          // }
          this.$emit('loadEnd')
        }
      })
    },
    setList(arr) {
      let data = []
      if (arr && arr.length) {
        arr.map(item => {
          let childrenList = item[this.dataKey['children']]
          let arrItem = {
            key: item[this.dataKey['value']],
            id: item['Id'],
            title: item[this.dataKey['label']]
          }
          if (childrenList && childrenList.length) {
            arrItem.children = this.setList(childrenList)
          }
          data.push(arrItem)
        })
      }

      return data
    },
    change(selectedKeys, e) {
      this.selected = e.node.dataRef
      this.$emit('change', this.selected)
    },
    onSearch(e) {
      const value = e.target.value
      this.getData(value)
    }
  },
  watch: {}
}
</script>

<style></style>
