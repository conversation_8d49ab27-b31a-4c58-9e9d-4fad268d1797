<!--
 * @Author: LP
 * @Description: 上传按钮
 * @Date: 2024-01-11 11:40:31
-->
<template>
  <a-button :type="btnType" :loading="uploading" @click.stop="selectImage">{{ text }}</a-button>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin.js'
export default {
  name: 'UploadButton',
  mixins: [ListMixin],
  props: {
    /**
     * 按钮文本
     */
    text: {
      type: String,
      default: () => {
        return '上传图片'
      },
    },
    /**
     * 按钮类型
     */
    btnType: {
      type: String,
      default: () => {
        return 'primary'
      },
    },

    // 存储桶
    bName: {
      type: String,
      default: () => window._CONFIG.AliOssConfig['BaseBucketName'],
    },
    // 文件夹
    dir: {
      type: String,
      default: () => window._CONFIG.AliOssConfig['CommonDir'],
    },
    // 文件大小限制，单位M，默认20M
    fileSize: {
      type: Number,
      default: 20,
    },
    // pdf文件大小限制，单位M，默认50M
    fileSizePdf: {
      type: Number,
      default: 50,
    },
    //文件类型 1 图片 2 所有文件 3 pdf 4 'image/jpeg,image/jpg,image/png,image/bmp'
    fileType: {
      type: Number,
      default: 4,
    },
  },
  watch: {
    fileType(val) {
      this.fileTypeData = val
    },
  },
  data() {
    return {
      uploading: false,
      fileTypeData: this.fileType,
    }
  },
  computed: {},
  methods: {
    /**
     * 选择图片
     */
    selectImage() {
      let fileTypeText = ''
      if (this.fileTypeData == 1) {
        fileTypeText = ''
      } else if (this.fileTypeData == 2) {
        fileTypeText = '*'
      } else if (this.fileTypeData == 3) {
        fileTypeText = '.pdf'
      } else if (this.fileTypeData == 4) {
        fileTypeText = 'image/jpeg,image/jpg,image/png,image/bmp'
      }
      console.log('当前使用的桶名 this.bName:', this.bName)
      console.log('全局配置中的桶名:', window._CONFIG.AliOssConfig['BaseBucketName'])
      console.log('props 中的默认桶名:', window._CONFIG.AliOssConfig['BaseBucketName'])
      if (this.beforeCall) {
        this.beforeCall(() => {
          this._file(this.fileCallBack, fileTypeText, this.bName, this.dir, this.handleBeforeUpload)
        })
      } else {
        this._file(this.fileCallBack, fileTypeText, this.bName, this.dir, this.handleBeforeUpload)
      }
    },
    /**
     * 文件上传回调
     * @param {* 是否成功} bool
     * @param {* 文件路径} realurl
     * @param {* base64文件} b64
     */
    fileCallBack(bool, realurl, b64) {
      if (bool && realurl) {
        this.$emit('uploadOK', realurl)
      } else {
        if (realurl != 'cancel') {
          this.$message.warning(realurl)
        }
      }
      this.uploading = false
    },
    /**
     * 上传之前-文件大小验证
     * @param {*} files
     */
    handleBeforeUpload(files) {
      this.uploading = true
      if (this.fileTypeData == 3) {
        if (files && Array.from(files).some((res) => res.size / (1024 * 1024) > this.fileSizePdf)) {
          this.$message.warn(`pdf文件大小不能超过${this.fileSizePdf}M`)
          this.uploading = false
          return false
        }
      } else {
        if (files && Array.from(files).some((res) => res.size / (1024 * 1024) > this.fileSize)) {
          this.$message.warn(`文件大小不能超过${this.fileSize}M`)
          this.uploading = false
          return false
        }
      }
      return true
    },
  },
}
</script>
<style lang="less" scoped>
</style>
