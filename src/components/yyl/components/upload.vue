<template>
  <div
    @click.stop="!bg ? selectImage() : ''"
    :style="background"
    class="iFile"
    @mouseover="iconShow = true"
    @mouseleave="iconShow = false"
    v-if="type != 'button'"
  >
    <slot>
      <!-- <div v-if="!bg"><a-icon type="plus" />上传</div> -->
      <div
        v-if="!bg"
        style="line-height: 1;"
      >
        <img
          src="@/assets/image/icon-upload.png"
          alt=""
          style="width: 40px;"
        />
        <p style="color: #858b9c; font-size: 12px; margin: 0; line-height: 2;">
          上传图片
        </p>
      </div>
      <div
        v-if="iconShow && bg"
        style="
          background: rgba(0, 0, 0, 0.5);
          postion: absoulte;
          width: 100%;
          height: 100%;
          z-index: 1;
        "
      >
        <a-icon
          type="eye"
          @click.stop="handlePreview"
          style="color: #fff;"
        />
        <a-icon
          type="delete"
          style="margin-left: 10px; color: #fff;"
          @click.stop="$emit('clear')"
          v-if="!readonly"
        />
      </div>
    </slot>
    <div
      class="remove"
      v-if="clearAble && bg"
      @click.stop="$emit('clear')"
    >
      <a-icon type="close-circle" />
    </div>

    <a-modal
      :visible="previewVisible"
      @cancel="handleCancel"
      :width="700"
      v-if="previewVisible"
      :maskClosable="isFormModel ? false : true"
    >
      <div
        class="picture_jz_btn"
        v-if="showPicBtn"
      >
        <a-button
          @click="onPictureCalibrationClick"
          type="danger"
          shape="round"
        >图片校准</a-button>
      </div>
      <div class="imgDiv">
        <a-alert
          v-if="previewImage && isPdf(previewImage)"
          message="提示"
          type="warning"
          show-icon
          class="error-image-tip"
        >
          <span slot="description">客户上传格式为PDF文件（<a
              :href="previewImage"
              target="_blank"
            >点此查看并下载</a>）
          </span>
        </a-alert>
        <!-- <div alt="example" style="width: 100%;" :id="`img_${showId}`"></div> -->
        <viewer
          :images="previewImageArr"
          v-viewer="{
            inline: true,
            fullscreen: false,
            navbar: false,
            title: false,
            button: false,
            toolbar: {
              zoomIn: true,
              zoomOut: true,
              reset: true,
              rotateLeft: true,
              rotateRight: true,
              flipHorizontal: true,
              flipVertical: true
            }
          }"
          ref="viewer"
          style="background-color: #999; width: 100%; height: 100%;"
        >
          <img
            v-for="(src, index) in previewImageArr"
            :src="src"
            :key="index"
            style="display: none; width: 100%; height: 100%;"
            :id="`img${index}`"
          />
        </viewer>
      </div>
      <!-- 填写证件号  有效期 -->
      <div v-if="isFormModel">
        <a-form-model
          ref="form"
          :rules="rules"
          :model="model"
          layout="vertical"
        >
          <a-form-model-item
            label="证件号："
            prop="OldLicenseNumber"
          >
            <a-input
              placeholder="请输入"
              v-model="model.OldLicenseNumber"
              :maxLength="100"
            />
          </a-form-model-item>
          <a-form-model-item
            label=""
            prop="LicenseNumber"
          >
            <a-input
              placeholder="请再次输入证件号，以确认无误"
              v-model="model.LicenseNumber"
              :maxLength="100"
            />
          </a-form-model-item>
          <template v-if="isFormModelDate">
            <a-form-model-item
              label="有效期至："
              prop="BeginValidDate"
            >
              <!-- <a-date-picker
              @change="onStartTimeChange"
              v-model="validTime.BeginValidDate"
              style="width: 35%;"
              placeholder="开始日期"
            >
              <a-icon slot="suffixIcon" type="" />
            </a-date-picker>
            <span
              style="
                width: calc(100% - 92%);
                display: inline-block;
                text-align: center;
              "
              >~</span
            > -->
              <a-date-picker
                @change="onEndTimeChange"
                v-model="validTime.EndValidDate"
                style="width: 35%;"
                placeholder="结束日期"
              >
                <a-icon
                  slot="suffixIcon"
                  type=""
                />
              </a-date-picker>
              <a-checkbox
                v-model="LongTimeCheck"
                @change="onValidDateChange"
                style="margin-left: 16px;"
              >
                长期
              </a-checkbox>
            </a-form-model-item>
          </template>

          <template v-if="isFormModelAddress">
            <a-form-model-item
              label="注册地区："
              prop="AreaId"
            >
              <SelectCityAreas
                style="width: 100%;"
                ref="iSelectCityAreas"
                v-if="isSelectCityAreas"
                :defaultCodes="cityAreas"
                @change="(val, node) => cityAreasChange(val, node)"
              >
              </SelectCityAreas>
            </a-form-model-item>
            <a-form-model-item
              label="注册地址："
              prop="DetailAddress"
            >
              <a-input
                placeholder="请点击右边图标进行选择"
                v-model="model.DetailAddress"
                :maxLength="300"
                @change="
                  model.Latitude = ''
                  model.Longitude = ''
                "
              >
                <template slot="suffix">
                  <img
                    src="@/assets/image/dingwei-icon.png"
                    alt=""
                    @click.stop="
                      $refs.iBMapView.initMap({
                        ...model,
                        isShipping: false
                      })
                    "
                    style="width: 24px; cursor: pointer;"
                  />
                </template>
              </a-input>
            </a-form-model-item>
            <a-form-model-item
              label="坐标"
              v-show="false"
            >
              <a-input-group :compact="true">
                <a-input
                  disabled
                  :style="{ width: 'calc(50% - 50px)' }"
                  placeholder="经度"
                  v-model="model.Longitude"
                />
                <a-input
                  disabled
                  :style="{ width: 'calc(50% - 50px)' }"
                  placeholder="纬度"
                  v-model="model.Latitude"
                />
              </a-input-group>
            </a-form-model-item>
            <!-- 地图 -->
            <BMapView
              ref="iBMapView"
              @ok="handleOkMap"
            />
            <!--  -->
          </template>
        </a-form-model>
      </div>
      <!-- 填写证件号  有效期 -->
      <div
        class="tools"
        slot="footer"
        style="text-align: left;"
      >
        <a-row
          type="flex"
          :gutter="10"
          align="middle"
        >
          <a-col :md="12">
            <!-- <span @click="picture_rotate" style="cursor: pointer;">
              <a-icon type="redo" /> 旋转</span
            > -->
            <span
              style="margin-left: 20px; cursor: pointer;"
              @click="showImg(previewImage)"
            >
              <a-icon type="file-image" /> 大图
            </span>
          </a-col>
          <a-col
            :md="12"
            style="text-align: right;"
            v-if="isFormModel"
          >
            <a-button
              @click="handleOk"
              type="primary"
            >确定</a-button>
          </a-col>
        </a-row>
      </div>
      <PictureCalibrationDialog
        ref="pictureCalibrationDialog"
        @ok="onPictureCalibrationCallback"
        :bName="bName"
        :dir="dir"
      />
    </a-modal>
  </div>
  <div v-else>
    <a-button @click.stop="selectImage()">
      <a-icon :type="loading ? 'loading' : 'upload'" />
      上传
    </a-button>
  </div>
</template>

<script>
import moment from 'moment'
import { ListMixin } from '@/mixins/ListMixin.js'
export default {
  name: 'upload',
  mixins: [ListMixin],
  props: {
    type: {
      type: String,
      default: ''
    },
    fileType: {
      type: String,
      default: '*'
    },
    dirName: {
      type: String,
      default: ''
    },
    gray: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    onlycamera: {
      type: Boolean,
      default: false
    },
    isFormModel: {
      type: Boolean,
      default: false
    },
    isFormModelDate: {
      type: Boolean,
      default: true
    },
    // 文件大小限制，单位M，默认20M
    fileSize: {
      type: Number,
      default: 20
    },
    isFormModelAddress: {
      type: Boolean,
      default: false
    },
    FormModelContent: {
      type: Object,
      default: null
    },
    clearAble: {
      type: Boolean,
      default: false
    },
    bg: '',
    beforeCall: {
      type: Function,
      default: undefined
    },
    width: {
      type: String,
      default: '120px'
    },
    // 存储桶
    bName: {
      type: String,
      default: window._CONFIG.AliOssConfig['BaseBucketName']
    },
    // 文件夹
    dir: {
      type: String,
      default: window._CONFIG.AliOssConfig['CommonDir']
    },
    showId: { type: [String, Number], default: 0 }, // 预览大图的当前数据源id，保证imgId唯一性
    // 图片文件存储属性名称
    aName: {
      type: String,
      default: ''
    },
    // 图片文件在数组中的索引
    aIndex: {
      type: Number,
      default: -1
    },
    //是否显示图片校验
    showPicBtn: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      base64: '',
      iconShow: false,
      previewVisible: false,
      previewImage: '',
      previewImageArr: [],
      angle: 0,
      dateFormat: 'YYYY-MM-DD',
      model: {
        OldLicenseNumber: '',
        LicenseNumber: '',
        BeginValidDate: null,
        EndValidDate: null,
        AreaId: null,
        AreaCode: null,
        AreaFullName: '',
        DetailAddress: '',
        Longitude: null,
        Latitude: null
      },
      LongTimeCheck: false,
      validTime: {
        BeginValidDate: null,
        EndValidDate: null
      },
      isSelectCityAreas: true,
      cityAreas: [],
      newUrl: ''
    }
  },
  computed: {
    background() {
      if (this.bg) {
        return {
          borderColor: this.bg || this.gray ? 'rgba(0,0,0,0.1)' : 'rgba(0,0,0,0.2)',
          backgroundImage: 'url("' + (this.bg || this.base64) + '")',
          width: this.width || '120px'
        }
      } else {
        return {
          borderColor: this.bg || this.gray ? 'rgba(0,0,0,0.1)' : 'rgba(0,0,0,0.2)',
          width: this.width || '120px'
        }
      }
    },
    rules() {
      return {
        BeginValidDate: [
          {
            required: this.audit && !this.model.IsRealName ? false : true,
            validator: (rule, value, callback) => {
              if (!this.validTime.BeginValidDate || !this.validTime.EndValidDate) {
                if (this.audit && !this.model.IsRealName) {
                  callback()
                } else {
                  callback(new Error('请选择!'))
                }
              } else {
                callback()
              }
            }
          }
        ],
        OldLicenseNumber: [
          {
            required: this.audit && !this.model.IsRealName ? false : true,
            message: '请输入证件号!'
          }
        ],
        LicenseNumber: [
          {
            required: this.audit && !this.model.IsRealName ? false : true,
            validator: (rule, value, callback) => {
              if (!value) {
                if (this.audit && !this.model.IsRealName) {
                  callback()
                } else {
                  callback(new Error('请再次输入证件号，以确认无误!'))
                }
              } else {
                if (value != this.model.OldLicenseNumber) {
                  callback(new Error('两次输入的证件号不一致，请确认!'))
                } else {
                  callback()
                }
              }
            }
          }
        ]
      }
    }
  },
  methods: {
    moment,
    // //初始化预览组件
    // inited(viewer) {
    //   this.$viewer = viewer;
    // },
    // 是否是PDF文件
    isPdf(url) {
      return url.split('?')[0].length > 0 && url.split('?')[0].endsWith('.pdf')
    },
    selectImage() {
      if (this.readonly) {
        return
      }
      if (this.beforeCall) {
        this.beforeCall(() => {
          this._file(
            (bool, realurl, b64) => {
              if (bool) {
                if (b64) {
                  this.base64 = b64
                } else {
                  this.base64 = ''
                }
                this.$emit('change', realurl)
              }
            },
            this.type != 'button' ? '' : '*',
            this.bName,
            this.dir,
            this.handleBeforeUpload
          )
        })
      } else {
        this._file(
          (bool, realurl, b64) => {
            if (bool) {
              if (b64) {
                this.base64 = b64
              } else {
                this.base64 = ''
              }
              this.$emit('change', realurl)
            }
          },
          this.type != 'button' ? '' : this.fileType,
          this.bName,
          this.dir,
          this.handleBeforeUpload
        )
      }
    },
    // 文件大小限制
    handleBeforeUpload(files) {
      if (files && Array.from(files).some(res => res.size / (1024 * 1024) > this.fileSize)) {
        this.$message.warn(`文件大小不能超过${this.fileSize}M`)
        return false
      }
      return true
    },
    handlePreview() {
      if (!this.bg) {
        return
      }
      this.previewImage = this.bg
      this.previewImageArr = [this.bg]
      this.previewVisible = true
      this.$nextTick(() => {
        // console.log(document.getElementById('img0'))
        setTimeout(() => {
          document.getElementById('img0').style.display = 'inline-block'
        }, 300)

        //   // console.log(document.getElementById("img_" + this.showId))
        //   document.getElementById(
        //     "img_" + this.showId
        //   ).style.backgroundImage = `url(${this.previewImage})`;
      })
      // 证件号 有效期
      // console.log(this.isFormModel);
      // console.log(this.FormModelContent);

      if (this.isFormModel && this.FormModelContent) {
        this.model = {
          OldLicenseNumber: this.FormModelContent.OldLicenseNumber,
          LicenseNumber: this.FormModelContent.LicenseNumber,
          BeginValidDate: this.FormModelContent.BeginValidDate,
          EndValidDate: this.FormModelContent.EndValidDate
        }
        console.log(this.model.BeginValidDate)

        if (this.isFormModelAddress) {
          this.model = Object.assign(
            {
              AreaId: this.FormModelContent.AreaId,
              AreaCode: this.FormModelContent.AreaCode,
              Longitude: this.FormModelContent.Longitude,
              Latitude: this.FormModelContent.Latitude,
              AreaFullName: this.FormModelContent.AreaFullName,
              DetailAddress: this.FormModelContent.DetailAddress
            },
            this.model
          )
          this.cityAreas = this.getDefaultValueByCode(this.model['AreaCode'])

          this.isSelectCityAreas = false
          this.$nextTick(() => {
            this.isSelectCityAreas = true
          })
        }
        this.LongTimeCheck = this.FormModelContent.LongTimeCheck
        this.validTime = this.FormModelContent.validTime
        console.log(this.validTime)
      }
    },
    handleCancel() {
      if (this.newUrl) {
        if (this.aIndex > -1) {
          this.$emit('change', this.newUrl, this.bIndex, this.bName)
        } else {
          this.$emit('change', this.newUrl, this.bName)
        }
      }
      this.previewVisible = false
      // this.picture_rotate({ isReset: true });
      this.model = {
        OldLicenseNumber: '',
        LicenseNumber: '',
        BeginValidDate: null,
        EndValidDate: null,
        AreaId: null,
        AreaCode: null,
        AreaFullName: '',
        DetailAddress: '',
        Longitude: null,
        Latitude: null
      }
      this.LongTimeCheck = false
      this.validTime = {
        BeginValidDate: null,
        EndValidDate: null
      }
      this.cityAreas = []
      this.isSelectCityAreas = true
    },
    /**
     * 图片旋转
     */
    picture_rotate({ isReset = false }) {
      let newAngle = this.angle
      var el = document.getElementById('img_' + this.showId)
      if (isReset) {
        newAngle = 0
      } else {
        newAngle += 90
      }
      // el.setAttribute("style", `transform:rotate(${newAngle}deg)`);
      el.style.transform = `rotate(${newAngle}deg)`
      if ((newAngle / 90) % 2 == 0) {
        el.style.width = '100%'
        el.style.height = '350px'
        el.style.top = '50%'
        el.style.marginTop = '-150px'
      } else {
        el.style.width = 'calc(550px / 1.5)'
        el.style.height = '100%'
        el.style.top = 0
        el.style.marginTop = 0
      }
      this.angle = newAngle
    },
    showImg(src) {
      let a = document.createElement('a')
      a.href = src
      a.target = '_blank'
      a.click()
    },
    /**
     * @description: 选择有效期
     * @param {*} dates
     * @param {*} dateStrings
     * @return {*}
     */
    onStartTimeChange(dates, dateStrings) {
      if (dates) {
        this.validTime.BeginValidDate = moment(dates)
        this.model.BeginValidDate = moment(dates).format(this.dateFormat)
      } else {
        this.validTime.BeginValidDate = null
        this.model.BeginValidDate = null
      }
      this.model.EndValidDate = this.LongTimeCheck ? '2999-12-31' : null
      this.validTime.EndValidDate = this.LongTimeCheck ? moment('2999-12-31') : null
    },
    onEndTimeChange(dates, dateStrings) {
      if (dates) {
        /* if (!this.retrunDate(moment(dates).format(this.dateFormat))) {
          this.validTime.EndValidDate = null;
          this.model.EndValidDate = null;
        } else {
          this.validTime.EndValidDate = moment(dates);
          this.model.EndValidDate = moment(dates).format(this.dateFormat);
        } */
        let aDayAgo = moment(new Date(dates).getTime() - 1 * 24 * 60 * 60 * 1000)
        this.validTime.BeginValidDate = moment(aDayAgo)
        this.validTime.EndValidDate = moment(dates)
        this.model.BeginValidDate = moment(aDayAgo).format(this.dateFormat)
        this.model.EndValidDate = moment(dates).format(this.dateFormat)
      } else {
        this.validTime.EndValidDate = null
        this.model.EndValidDate = null
      }
      if (this.model.EndValidDate == '2999-12-31') {
        this.LongTimeCheck = true
      } else {
        this.LongTimeCheck = false
      }
    },
    retrunDate(endDate) {
      var beginDate = this.model.BeginValidDate
      if (!beginDate || !endDate) {
        return true
      }
      var d1 = new Date(beginDate.replace(/\-/g, '/'))
      var d2 = new Date(endDate.replace(/\-/g, '/'))

      if (d1 >= d2) {
        this.$message.error('结束日期不能小于开始日期！')
        return false
      } else {
        return true
      }
    },
    /**
     * @description:长期
     * @param {*}
     * @return {*}
     */

    onValidDateChange(e) {
      let checked = e.target.checked
      if (checked) {
        let aDayAgo = moment(new Date('2999-12-31').getTime() - 1 * 24 * 60 * 60 * 1000)
        this.validTime.BeginValidDate = moment(aDayAgo)
        this.model.BeginValidDate = moment(aDayAgo).format(this.dateFormat)
        this.validTime.EndValidDate = moment('2999-12-31')
        this.model.EndValidDate = '2999-12-31'
      } else {
        this.validTime.EndValidDate = null
        this.model.EndValidDate = null
      }
    },
    // 确定
    handleOk() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          // console.log(this.model);
          let emitData = {
            model: this.model,
            validTime: this.validTime,
            LongTimeCheck: this.LongTimeCheck,
            cityAreas: this.cityAreas
          }
          emitData.model.BeginValidDate = moment(this.model.BeginValidDate)
          emitData.model.EndValidDate = moment(this.model.EndValidDate)
          this.$emit('ok', { ...emitData })
          this.handleCancel()
        }
      })
    },
    //选择地址区域
    cityAreasChange(val, node) {
      if (!val) {
        return
      }
      this.cityAreas = val
      this.model.AreaFullName = ''
      this.model.Latitude = ''
      this.model.Longitude = ''
      if (typeof node != 'undefined') {
        for (var i = 0; i < node.length; i++) {
          this.model.AreaFullName += node[i].label
        }
        this.model.AreaId = node[node.length - 1].id
        this.model.AreaCode = node[node.length - 1].code
      } else {
        this.model.AreaId = null
        this.model.AreaCode = null
      }
    },
    getDefaultValueByCode(areaCode) {
      if (!areaCode) return
      let tempArray = new Array()
      for (let i = 0; i < 3; i++) {
        if (areaCode.length >= 2 * i) {
          tempArray.push(areaCode.substring(2 * i, 2 * i + 2))
        }
      }
      return tempArray
    },
    onPictureCalibrationClick() {
      if (this.$refs.pictureCalibrationDialog) {
        this.$refs.pictureCalibrationDialog.show(this.bg)
      }
    },
    onPictureCalibrationCallback(url) {
      this.newUrl = url
      this.previewImage = url
      this.previewImageArr = [url]
    },
    async handleOkMap(mapInfo) {
      // console.log(mapInfo)
      this.$refs.form.clearValidate()
      this.model['AreaId'] = mapInfo['AreaId']
      this.model['AreaCode'] = mapInfo['AreaCode']
      this.model['Longitude'] = mapInfo['Longitude']
      this.model['Latitude'] = mapInfo['Latitude']
      this.model['AreaFullName'] = mapInfo['AreaFullName']
      this.model['DetailAddress'] = mapInfo['DetailAddress']
      this.cityAreas = this.getDefaultValueByCode(mapInfo['AreaCode'])
      // console.log(this.cityAreas)

      this.isSelectCityAreas = false
      this.$nextTick(() => {
        this.isSelectCityAreas = true
      })
    }
  }
}
</script>
<style lang="less" scoped>
.imgDiv {
  width: 100%;
  text-align: center;
  overflow: hidden;
  vertical-align: middle;
  height: 450px;
  // padding: 50px;
  position: relative;
  margin: 16px 0;
  div {
    // width: 100%;
    // height: 100%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 250px;
    margin: 0 auto; /*水平居中*/
    position: relative;
    top: 50%; /*偏移*/
    margin-top: -150px;
  }
}
</style>
<style>
/* 图片 */
.iFile {
  position: relative;
  border: 1px dashed rgba(0, 0, 0, 0.2);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 120px;
  height: 120px;
  /* line-height: 120px; */
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;

  background-color: #f9fafb;
}

.thumbBody {
  position: relative;
  overflow: hidden;
}

.thumb {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

.thumb > img {
  transition: all 0.3s;
  max-width: 100%;
}

.thumb.round {
  padding: 4px;
  border: 1px solid transparent;
  background-color: #fff;
  border-radius: 50%;
}

.iFile .remove {
  position: absolute;
  top: -8px;
  right: -8px;
  line-height: 1;
}
.picture_jz_btn {
  position: absolute;
  top: 50px;
  right: 50px;
  z-index: 99;
}
.imgDiv .error-image-tip {
  width: 400px !important;
  z-index: 99;
  height: auto !important;
  text-align: left !important;
  margin: -20px 0 0 -200px !important;
  position: absolute !important;
  left: 50%;
  top: 50% !important;
}
</style>
