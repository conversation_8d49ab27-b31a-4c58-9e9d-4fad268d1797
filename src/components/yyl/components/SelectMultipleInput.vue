<template>
  <a-select style="width: 100%" label-in-value :filter-option="false" v-model="inputValue" :maxTagCount="maxTagCount" :disabled="disabled" :not-found-content="fetching ? undefined : null" mode="multiple" :placeholder="placeholder" @focus="focusInput" @search='searchInput' @change="changeInput">
    <a-spin v-if="fetching" slot="notFoundContent" size="small" />
    <a-select-option :value="item[dataKey.value]" v-for="(item,index) in list" :key="index">{{item[dataKey.name]}}</a-select-option>
  </a-select>
</template>

<script>
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: 'SelectMultipleInput',
  props: {
    //http请求类型
    httpType: {
      type: String,
      default: "GET",
    },
    //请求参数
    httpParams: {
      type: Object,
      default() {
        return {
          PageIndex: 1,
          PageSize: 20,
        };
      },
    },
    url: {
      type: String,
      default: "",
    },
    value: {
      type: Array,
      default() {
        return []
      }
    },
    dataKey: {
      type: Object,
      default() {
        //name 显示内容的字段名称  value 实际的值的字段名称  nameList显示的其他字段名称（比如显示 名称/编码 这种情况就把Code放在nameList中）
        return { name: "Name", value: "Id", nameList: [] };
      },
    },
    keyWord: {
      Type: String,
      Default: "KeyWord",
    },
    httpHead: {
      type: String,
      default: "",
    },
    placeholder: String, //提示语
    disabled: Boolean, //是否禁用
    // 最多显示多少个tag 多了隐藏
    maxTagCount: {
      type: Number,
      default: undefined,
    },
  },
  data() {
    return {
      inputValue: [],
      fetching: false,
      list: [],
    }
  },
  watch: {
    value(newName, oldName) {
      this.inputValue = newName
    },
  },
  created() {
    this.inputValue = this.value;
    this.getInfo()
  },
  methods: {
    getInfo(keyWord, type) {
      let formData = this.httpParams
      if (keyWord) {
        formData[this.keyWord] = keyWord
      } else {
        delete formData[this.keyWord]
      }
      this.fetching = true;
      let obj = {}
      if (this.httpType == "GET") {
        obj = getAction(this.url, formData, this.httpHead);
      } else if (this.httpType == "POST") {
        obj = postAction(this.url, formData, this.httpHead);
      }
      obj.then((res) => {
        if (res.IsSuccess) {
          this.list = res.Data
          if (type == 'focus') {
            this.$emit('loadOk', res.Data)
          }
        } else {
          this.$message.warning(res.Msg);
        }
      }).finally(() => {
        this.fetching = false;
      });
    },
    focusInput() {
      setTimeout(() => {
        this.getInfo(null, 'focus')
      }, 500)
    },
    searchInput(keyWord) {
      setTimeout(() => {
        console.log(keyWord)
        this.getInfo(keyWord)
      }, 1000)
    },
    changeInput(e) {
      let key = []
      let label = []
      e.map((item) => {
        key.push(item.key)
        label.push(item.label.replace(/\n/g, ""))
      })
      this.$emit('change', key, label, e)
    },
  },
}
</script>

<style>
</style>