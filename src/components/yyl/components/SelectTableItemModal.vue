<!-- 选择通用 modal-->
<!-- 引用示例 columnsModal必填-->
<!-- 
  <SelectTableItemModal
    ref="SelectTableItemModalRef"
    listType="POST"
    modalTitle="选择客户"
    :httpHeadList="httpHead"
    :listUrl="url.list"
    :columns="columnsModal"
    :queryList="modalQuery"
  /> -->
<!-- 筛选条件示例 -->
<!-- 
computed: {
  modalQuery() {
    return [
      {
        type: 'input',
        label: '客户名称',
        dataIndex: 'Name',
      },
      {
        type: 'SingleChoice',
        label: '客户类型',
        httpParams: { PageIndex: 1, PageSize: 100, IsValid: true },
        url: this.url.getCustomerTypeList,
        dataIndex: 'CustomerType',
      },
      {
        type: 'SelectCity',
        label: '注册地区',
        dataIndex: 'AreaCode',
      },
      {
        type: 'select',
        label: '是否有效',
        options: [
          {
            value: 'true',
            label: '有效',
          },
          {
            value: 'false',
            label: '无效',
          },
        ],
        dataIndex: 'IsValid',
      },
      {
        type: 'EnumSingle',
        label: '首营审核状态',
        dictCode: 'EnumCustomerQualificationAuditStatus',
        dataIndex: 'FirstCampAuditStatus',
      },
      {
        type: 'SingleChoice',
        label: '客户类型',
        httpParams: { PageIndex: 1, PageSize: 100, IsValid: true },
        url: this.url.getCustomerTypeList,
        dataIndex: 'CustomerType',
      },
      {
        type: 'rangePicker',
        label: '创建时间',
        startKey: 'BeginCreateTime',
        endKey: 'EndCreateTime',
        dataIndex: 'rangeDate',
      },
      {
        type: 'SelectTree',
        label: '客户标签',
        filterHandle: this.labelFilterHandle,
        httpParams: { PageIndex: 1, PageSize: 100, IsValid: true },
        url: this.url.getCustomerGroupLabelList,
        isMultiple: true,
        dataIndex: 'CustomerLabelIds',
      },
    ]
  },
}, -->
<template>
  <a-modal
    :title="modalTitle"
    :width="modalWidth"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    @close="handleCancel"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <div>
      <!-- 搜索条件 -->
      <a-card v-if="queryList.length > 0" :bodyStyle="{ padding: '16px 16px 0 16px' }" style="margin-bottom: 16px">
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="10">
              <a-col v-for="(queryItem, index) in queryList" :key="queryItem.dataIndex || index" :md="queryCol">
                <a-form-item :label="queryItem.label" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <!-- 输入框 -->
                  <a-input
                    v-if="queryItem.type === 'input' || !queryItem.type"
                    :placeholder="queryItem.placeholder || '请输入'"
                    v-model="queryParam[queryItem.dataIndex]"
                    :maxLength="50"
                  ></a-input>
                  <!-- 单选框， 带选项数据 -->
                  <a-select
                    v-if="queryItem.type === 'select'"
                    v-model="queryParam[queryItem.dataIndex]"
                    :placeholder="queryItem.placeholder || '请选择'"
                    :mode="queryItem.isMultiple ? 'multiple' : ''"
                  >
                    <a-select-option value>请选择</a-select-option>
                    <a-select-option v-for="it in queryItem.options" :key="it.value">{{ it.label }}</a-select-option>
                  </a-select>
                  <!-- 单选框 -->
                  <SingleChoiceView
                    v-if="queryItem.type === 'SingleChoice' && !queryItem.showSearch"
                    style="width: 100%"
                    :placeholder="queryItem.placeholder || '请选择'"
                    :keyWord="queryItem.keyWord || 'KeyWord'"
                    :showSearch="queryItem.showSearch || false"
                    :httpParams="queryItem.httpParams || {}"
                    :httpHead="queryItem.httpHead || 'P36001'"
                    :dataKey="queryItem.dataKey || { name: 'Name', value: 'Id' }"
                    :Url="queryItem.url"
                    v-model="queryParam[queryItem.dataIndex]"
                  />
                  <!-- 单选框 带搜索 -->
                  <SingleChoiceSearchView
                    v-if="queryItem.type === 'SingleChoice' && queryItem.showSearch"
                    style="width: 100%"
                    :placeholder="queryItem.placeholder || '请选择'"
                    :keyWord="queryItem.keyWord || 'KeyWord'"
                    :showSearch="queryItem.showSearch || false"
                    :httpParams="queryItem.httpParams || {}"
                    :httpHead="queryItem.httpHead || 'P36001'"
                    :dataKey="queryItem.dataKey || { name: 'Name', value: 'Id' }"
                    :Url="queryItem.url"
                    v-model="queryParam[queryItem.dataIndex]"
                  />
                  <!-- 多选 -->
                  <MultipleChoiceView
                    v-if="queryItem.type === 'MultipleChoice' && !queryItem.showSearch"
                    :placeholder="queryItem.placeholder || '请选择'"
                    :httpParams="queryItem.httpParams || {}"
                    :httpHead="queryItem.httpHead || 'P36001'"
                    :dataKey="queryItem.dataKey || { name: 'Name', value: 'Id' }"
                    :Url="queryItem.url"
                    v-model="queryParam[queryItem.dataIndex]"
                  />
                  <!-- 多选带搜索 -->
                  <MultipleChoiceSearchView
                    v-if="queryItem.type === 'MultipleChoice' && queryItem.showSearch"
                    style="width: 100%"
                    :placeholder="queryItem.placeholder || '请选择'"
                    :keyWord="queryItem.keyWord || 'KeyWord'"
                    :httpParams="queryItem.httpParams || {}"
                    :httpHead="queryItem.httpHead || 'P36001'"
                    :dataKey="queryItem.dataKey || { name: 'Name', value: 'Id' }"
                    :Url="queryItem.url"
                    v-model="queryParam[queryItem.dataIndex]"
                  />
                  <!-- 单选枚举 -->
                  <EnumSingleChoiceView
                    v-if="queryItem.type === 'EnumSingle'"
                    style="width: 100%"
                    :placeholder="queryItem.placeholder || '请选择'"
                    :dictCode="queryItem.dictCode || ''"
                    :httpHead="'P36001'"
                    v-model="queryParam[queryItem.dataIndex]"
                  />
                  <!-- 多选枚举 -->
                  <EnumMultipleChoiceView
                    v-if="queryItem.type === 'EnumMultiple'"
                    style="width: 100%"
                    :placeholder="queryItem.placeholder || '请选择'"
                    :dictCode="queryItem.dictCode || ''"
                    :httpHead="'P36001'"
                    v-model="queryParam[queryItem.dataIndex]"
                  />
                  <!-- 省市区 -->
                  <SelectCityAreas
                    v-if="queryItem.type === 'SelectCity'"
                    style="width: 100%"
                    :selectCode="true"
                    :isListValue="false"
                    v-model="queryParam[queryItem.dataIndex]"
                  ></SelectCityAreas>
                  <!-- 树形选择器 -->
                  <SelectTreeViewModel
                    v-if="queryItem.type === 'SelectTree'"
                    style="width: 100%"
                    :placeholder="queryItem.placeholder || '请选择'"
                    class="multi-tree-query-select"
                    :multiple="queryItem.isMultiple || false"
                    :Url="queryItem.url"
                    :filterHandle="queryItem.filterHandle || null"
                    :httpParams="queryItem.httpParams || {}"
                    v-model="queryParam[queryItem.dataIndex]"
                    :httpHead="queryItem.httpHead || 'P36001'"
                    :dataKey="
                      queryItem.dataKey || {
                        value: 'Id',
                        label: 'Name',
                        children: 'Children'
                      }
                    "
                  />
                  <!-- 日期范围 -->
                  <a-range-picker
                    v-if="queryItem.type === 'rangePicker'"
                    @change="
                      (dates, dateStrings) => onTimeChange(dates, dateStrings, queryItem.startKey, queryItem.endKey)
                    "
                    v-model="dateQuery['rangeDate' + index]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="queryCol * (3 - (queryList.length % 3))" style="margin-bottom: 16px">
                <span class="table-page-search-submit-buttons">
                  <a-button @click="searchReset" icon="reload">重置</a-button>
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card
        :bodyStyle="{ padding: queryList.length === 0 ? '0' : '16px' }"
        :headStyle="{ padding: queryList.length === 0 ? '0' : '0 16px ' }"
        :title="queryList.length === 0 ? '' : '查询结果'"
        :bordered="queryList.length > 0"
      >
        <a-row>
          <a-col :span="24">
            <!-- 数据列表 -->
            <a-table
              :bordered="true"
              ref="table"
              :rowKey="rowKey"
              size="middle"
              :columns="columns"
              :dataSource="dataSource"
              :pagination="isPagination ? ipagination : false"
              :loading="loading"
              :rowSelection="
                isViewOnly
                  ? null
                  : {
                      selectedRowKeys: selectedRowKeys,
                      onChange: onSelectChange,
                      fixed: true,
                      type: selectType,
                      getCheckboxProps: getCheckboxProps
                    }
              "
              @change="handleTableChange"
              :scroll="{ y: dataSource.length === 0 ? false : 350, x: '100%' }"
            >
              <span slot="component" slot-scope="text">
                <j-ellipsis :value="'' + text" v-if="typeof text == 'number'" />
                <j-ellipsis :value="text" v-else />
              </span>
              <!-- 合并为规格/单位/生产厂商 -->
              <span slot="PackingSpecification" slot-scope="text, record">
                <j-ellipsis
                  :value="
                    '' + (text || '') + ' / ' + (record.PackageUnit || '') + ' / ' + (record.BrandManufacturer || '')
                  "
                />
              </span>
            </a-table>
            <!--/ 数据列表 -->
          </a-col>
        </a-row>
      </a-card>
    </div>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel"> 取消 </a-button>
      <a-button
        :disabled="confirmLoading"
        :style="{ marginLeft: '8px' }"
        v-if="!isViewOnly"
        :loading="confirmLoading"
        @click="handleOk"
        type="primary"
        >确定</a-button
      >
    </a-row>
  </a-modal>
</template>
<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'SelectTableItemModal',
  mixins: [ListMixin],
  components: { JEllipsis },
  props: {
    //弹窗标题
    modalTitle: {
      type: String,
      default: '选择'
    },
    // 弹窗宽度
    modalWidth: {
      type: [String, Number],
      default: 1100
    },
    // 搜索条件  组件类型集合 ['input','select','SingleChoice','MultipleChoice','EnumSingle','EnumMultiple', 'SelectCity','SelectTree', 'rangePicker']
    queryList: {
      type: Array,
      default() {
        return []
      }
    },
    // 表格rowKey
    rowKey: {
      type: String,
      default: 'Id'
    },
    // 仅查看数据，不选择
    isViewOnly: {
      type: Boolean,
      default: false
    },
    // 列表项
    columns: {
      type: Array,
      required: true
    },
    // 是否分页
    isPagination: {
      type: Boolean,
      default: true
    },
    // 列表请求接口
    listUrl: String,
    // 接口请求方式
    listType: {
      type: String,
      default: 'GET'
    },
    // 列表请求api端口
    httpHeadList: String,
    // 默认搜索条件，重置不会被清空
    defaultQueryParam: {
      type: Object,
      default() {
        return {}
      }
    },
    // 是否打开弹窗即请求数据
    isLoadData: {
      type: Boolean,
      default: true
    },
    // 选择类型  radio:单选  checkbox：多选
    selectType: {
      type: String,
      default: 'checkbox'
    },
    // 选择框的默认属性配置
    getCheckboxProps: {
      type: Function,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 8 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 16 },
        sm: { span: 19 }
      },
      queryParam: {},
      dateQuery: {},
      queryCol: 8,
      confirmLoading: false,
      url: {
        list: '',
        listType: ''
      }
    }
  },
  computed: {},
  mounted() {},
  created() {},
  methods: {
    show() {
      this.url.list = this.listUrl
      this.url.listType = this.listType
      this.httpHead = this.httpHeadList
      this.queryParam = { ...this.defaultQueryParam }
      if (this.isLoadData) {
        this.loadData(1)
      }
      this.visible = true
    },
    // 确定
    handleOk() {
      if (this.selectionRows.length === 0) {
        this.$message.warning('至少选择一条数据！')
        return
      }
      this.$emit('ok', this.selectionRows)
      this.close()
    },
    searchReset() {
      this.dateQuery = {}
      this.queryParam = { ...this.defaultQueryParam }
      this.loadData(1)
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.queryParam = {}
      this.dateQuery = {}
      this.url.list = ''
      this.onClearSelected()
      this.confirmLoading = false
      this.visible = false
    }
  }
}
</script>
<style lang="less" scoped>
.table-page-search-submit-buttons {
  float: right;
  overflow: hidden;
  .ant-btn + .ant-btn {
    margin-left: 8px;
  }
}
.multi-tree-query-select {
  /deep/.ant-select-selection--multiple {
    height: 30px;
    overflow: auto;
  }
}
</style>
