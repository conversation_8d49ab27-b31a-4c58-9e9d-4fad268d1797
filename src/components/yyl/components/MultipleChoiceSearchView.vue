<!--技术中心-普通调用接口多选支持搜索view-->
<template>
  <a-select
    mode="multiple"
    :placeholder="placeholder ? placeholder : '请搜索选择'"
    :disabled="disabled"
    :value="value"
    :style="{ width: width }"
    :allowClear="true"
    :filter-option="false"
    :maxTagCount="maxTagCount || 100"
    :maxTagTextLength="maxTagCount ? 6 : 100"
    @search="handleSearch"
    @change="handleChange"
  >
    <a-spin v-if="fetching" slot="notFoundContent" size="small" />
    <a-select-option
      v-for="(item, key) in dataList"
      :key="key"
      :value="item.ItemValue"
      :disabled="item.IsValid === false"
    >
      {{ item.ItemName }}
    </a-select-option>
  </a-select>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import debounce from 'lodash/debounce'

export default {
  name: 'MultipleChoiceSearchView',
  props: {
    //http请求类型
    httpType: {
      type: String,
      default: 'GET',
    },
    //请求参数
    httpParams: {
      type: Object,
      default() {
        return {
          pageIndex: 1,
          pageSize: 50,
        }
      },
    },
    // 最多显示多少个tag
    maxTagCount: {
      type: Number,
      default: null,
    },
    Url: {
      type: String,
      default: '',
    },
    dataKey: {
      type: Object,
      default() {
        return { name: 'Name', value: 'Id', nameList: [] }
      },
    },
    placeholder: String, //提示语
    disabled: Boolean, //是否禁用
    value: {
      Type: Array,
      Default: [],
    }, //选中值
    keyWord: {
      Type: String,
      Default: 'KeyWord',
    }, //选中值文字
    width: { type: String, default: '120px' }, //宽度
    httpHead: { type: String, default: 'P36001' }, //宽度
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  data() {
    this.getData = debounce(this.getData, 800)
    return {
      fetching: false,
      dataList: [],
      allDataList: [],
    }
  },
  created() {},
  mounted() {
    this.getData()
  },
  methods: {
    handleSearch(value) {
      this.getData(value)
    },
    reloadData(params) {
      this.getData('', params)
    },
    getData(keyWord = '', params) {
      let that = this
      if (!this.Url) return

      that.fetching = true
      let obj = null
      that.httpParams.KeyWord = keyWord || ''
      if (keyWord) {
        that.httpParams[this.keyWord] = keyWord
      }
      if (!keyWord && !params && this.allDataList && this.allDataList.length > 0) {
        this.dataList = [].concat(this.allDataList)
        return
      }
      let requestParams = { ...that.httpParams }
      if (params) {
        requestParams = { ...that.httpParams, ...params }
      }
      if (that.httpType == 'GET') {
        obj = getAction(that.Url, requestParams, this.httpHead)
      } else if (that.httpType == 'POST') {
        obj = postAction(that.Url, requestParams, this.httpHead)
      }
      if (!obj) {
        that.fetching = false
        return
      }
      obj
        .then((res) => {
          if (res.IsSuccess) {
            const data = res.Data
            let list = that.parseDataList(data)
            that.dataList = list
            if (!keyWord) {
              this.allDataList = [].concat(list)
            }

            this.$emit('dataLoaded', this.dataList)
          } else {
            that.dataList = []
          }
        })
        .finally(() => {
          that.fetching = false
        })
    },
    parseDataList(list) {
      let arrayList = []
      list.map((item) => {
        let otherName = ''
        if (this.dataKey['nameList'] && this.dataKey['nameList'].length > 0) {
          this.dataKey['nameList'].forEach((x) => {
            otherName += item[x] + '/'
          })
        }
        arrayList.push({
          ItemName: item[this.dataKey['name']] + (otherName ? '/' + otherName.substring(0, otherName.length - 1) : ''),
          ItemValue: item[this.dataKey['value']],
          Item: item,
          IsValid: item.IsValid,
        })
      })
      return arrayList
    },
    handleChange(value) {
      let contents = []
      if (value) {
        contents = this.getContents(value)
      }
      this.getData()
      this.$emit('change', value, contents)
    },
    getContents(vals) {
      let items = []
      if (this.dataList) {
        this.dataList.forEach((x) => {
          if (vals.findIndex((t) => t == x.ItemValue) > -1) {
            items.push(x.Item)
          }
        })
      }
      return items
    },
    setCurrentDictOptions(list) {
      this.dataList = list
    },
    getCurrentDictOptions() {
      return this.dataList
    },
  },
}
</script>

<style scoped></style>
