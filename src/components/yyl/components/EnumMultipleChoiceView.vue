<!--技术中心-枚举多选view-->
<template>
  <a-checkbox-group v-if="tagType == 'checkbox'" @change="handleChange" :value="enumValue" :disabled="disabled">
    <template v-for="(item, key) in dataList">
      <a-checkbox :key="key" :disabled="disabledItems.includes(item.ItemValue)" :value="
          dictCode == 'EnumCustomerSource' ? (item.ItemName ? item.ItemName : item.ItemDescription) : item.ItemValue
        " v-if="setNotOption(item.ItemValue) == -1">{{ item.ItemName ? item.ItemName : item.ItemDescription }}</a-checkbox>
    </template>
  </a-checkbox-group>
  <a-select v-else mode="multiple" :maxTagCount="maxTagCount" :disabled="disabled" :value="enumValue" @change="handleChange" :style="{ width: width }" :placeholder="placeholder ? placeholder : '请选择'" :allowClear="true">
    <a-select-option v-for="(item, key) in dataList" :key="key" :value="item.ItemValue">
      <span style="display: inline-block;width: 100%" :title="item.ItemName ? item.ItemName : item.ItemDescription">{{
        item.ItemName ? item.ItemName : item.ItemDescription
      }}</span>
    </a-select-option>
  </a-select>
</template>

<script>
import { getAction } from '@/api/manage'
export default {
  name: 'EnumMultipleChoiceView',
  props: {
    httpHead: String,
    dictCode: String, //枚举接口请求枚举关键字
    placeholder: String, //提示语
    disabled: Boolean, //是否禁用
    tagType: {
      type: String,
      default: () => {
        return 'select'
      }
    }, //展示类型 checkbox 和 select
    value: { Type: Array, Default: [] }, //选中值-数组
    disabledItems: {
      type: Array,
      default() {
        return []
      }
    },
    notOption: {
      // 过滤不显示 选项值
      type: Array,
      default: () => []
    },
    width: { type: String, default: '120px' }, //宽度
    // 最多显示多少个tag 多了隐藏
    maxTagCount: {
      type: Number,
      default: undefined,
    },
  },
  data() {
    return {
      dataList: [],
      enumValue: [],
    }
  },
  watch: {
    value: {
      handler(newData, oldData) {
        if(newData && typeof(newData) == 'string'){
          newData = newData.split(',')
        }
        this.enumValue = newData || []
        if (this.enumValue.length > 0) {
          this.enumValue = this.enumValue.map(item => {
            return item.toString()
          })
        }
      },
      immediate: true,//第一次声明了就监听
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  created() {
    //获取字典数据
    this.initDictData()
  },
  methods: {
    initDictData() {
      //根据字典Code, 初始化字典数组
      getAction(`/{v}/SystemTool/GetEnumsByName?enumName=${this.dictCode}`, this.httpHead).then(res => {
        if (res.IsSuccess) {
          this.dataList = res.Data
          this.$emit(
            'loaded',
            this.dataList.map(m => {
              return { value: m.ItemValue, text: m.ItemName ? m.ItemName : m.ItemDescription }
            })
          )
        }
      })
    },
    handleChange(value) {
      const selectedNodes = this.dataList.filter(t => value.includes(t.ItemValue))
      this.$emit('change', value, selectedNodes)
    },
    setCurrentDictOptions(dataList) {
      this.dataList = dataList
    },
    getCurrentDictOptions() {
      return this.dataList
    },
    setNotOption(value) {
      let index = -1
      if (this.notOption.length) {
        index = this.notOption.findIndex(t => t == value)
      }
      return index
    }
  }
}
</script>

<style scoped></style>
