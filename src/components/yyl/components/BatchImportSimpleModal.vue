<!-- 导入搜索置顶品种 modal-->
<template>
  <a-modal :title="modalTitle" :width="diyStyle.width" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :destroyOnClose="true">
    <div>
      <template>
        <div v-if="isShowTopDownloadTempFile" style="font-size: 16px; font-weight: 600;margin-bottom:15px">上传文件
          <a :href="downloadUrl" style="margin-left:8px;">
            <a-button type="primary">下载导入模板</a-button>
          </a>
        </div>
        <!-- 上面的区域 -->
        <slot style="margin-bottom:5px;" name="topView"></slot>
        <div style="margin-bottom:5px;"><span style="color:red">*</span> 导入文档：</div>
        <a-upload-dragger name="file" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="onUploadChange">
          <p class="ant-upload-drag-icon">
            <a-icon type="plus" />
          </p>
          <p class="ant-upload-hint">{{importConfigData.tips}}</p>
        </a-upload-dragger>
      </template>
      <div style="font-size: 16px; font-weight: 600;margin:30px 0 15px 0">导入预览 <span style="color:red;font-size:14px;font-weight: 400;">{{importConfigData.previewTips}}</span></div>
      <!-- 搜索 ref="searchView"一定不能修改，因为配合TableView的话 里面来找searchView-->
      <SimpleSearchView ref="searchView" :searchParamsList="searchParamsList" @search="searchQuery" />
      <!-- 列表 -->
      <TableNewView ref="tableView" :table="table" :columns="table.curStatus == 1?tableColumns.filter(item=>item.title !== '异常原因'):tableColumns" :tabDataList="tabDataList" @onTabChange="onTabChange" :dataList="dataSource" @operate="operate">
        <span slot="commonContent" slot-scope="{text, record}" @click="onAbnormalClick(record)">
          <a v-if="record[importConfigData.ErrorShowKey]">
            <a-icon type="question-circle" />
          </a>
          <j-ellipsis :value="text" :style="{ color: record[importConfigData.ErrorShowKey] ? 'red' : '' }" />
        </span>
      </TableNewView>

    </div>
    <a-row slot="footer">
      <div style="display: flex;align-items: center;justify-content: space-between;">
        <div>
          <!-- 下面的区域 -->
          <slot name="bottomView"></slot>
        </div>
        <div>
          <a-button v-for="(item,index) in importConfigData.bottomBtn" :loading="confirmLoading" :type="item.type" :key="index" @click="bottomBtnClick(item)">{{item.name}}</a-button>
        </div>
      </div>
    </a-row>

    <a-modal :visible="errVisibile" title="提示" :closable="false" @close="onKnowClick" :maskClosable="true" :destroyOnClose="true">
      <div>
        <div style="margin-bottom:10px;">{{ ToastModel.Title }}</div>
        <div v-for="(item, index) in ToastModel.Content" :key="index" style="color:red;">
          <span v-if="item">{{ item }};</span>
        </div>
      </div>
      <a-row :style="{ textAlign: 'right' }" slot="footer">
        <a-button @click="onKnowClick" type="primary">知道了</a-button>
      </a-row>
    </a-modal>
  </a-modal>
</template>

<script>
import { putAction, postAction, apiHead, deleteAction, getAction } from '@/api/manage'
import { ListMixin } from '@/mixins/ListMixin'
import { re } from 'mathjs'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')

export default {
  name: 'BatchImportSimpleModal',
  mixins: [ListMixin],
  components: { JEllipsis },
  props: {
    modalTitle: {
      type: String,
      default: '批量导入'
    },
    searchParamsList: {
      type: Array,
      default: () => []
    },
    // 列
    tableColumns: {
      type: Array,
      default: () => []
    },
    importHttpHead: {
      type: String,
      default: ''
    },
    importUrlType: {
      type: String,
      default: 'GET'
    },
    importUrl: {
      type: String,
      default: ''
    },
    importConfig: {
      type: Object,
      default: () => {
        return this.importDefConfig
      }
    },
    topViewData: {
      type: Object,
      default: null
    },
    diyStyle: { // 自定义样式
      type: Object,
      default: () => {
        return {
          width: 1100
        }
      }
    },
    okBefore: { // 确认保存前的前置事件 必须返回一个状态值，确保正常保存逻辑后续执行
      default: null
    },
    bordered: {
      type: Boolean,
      default: null,
    },
    dataSourceData: {
      type: Array,
      default: null
    },
  },
  data() {
    return {
      visible: false,
      errVisibile: false,
      ToastModel: {
        Title: '',
        Content: []
      },
      importDefConfig: {
        tips: '点击或将文档拖拽到这里上传',
        previewTips: '(重新导入将覆盖本次已导入数据，不可恢复)',
        tips: '点击或将文档拖拽到这里上传',
        importTitle: '数据',//导入的时候的基本数据类型名称 如：商品 客户
        templateFileName: '',//下载模板名字
        ErrorShowKey: 'ErrorShow',//异常信息字段名称
        saveKey: '',
        saveUrlType: 'GET',//保存接口请求方式
        saveUrl: '',//保存接口
        listQueryKey: '',//列表的时候需要用到的字段
        batchRemoveId: '',//批量移除id
        listRemoveType: 'DELETE',//列表删除接口请求方式
        listRemoveUrl: '',//列表删除 接口
        listRemoveKey: '',//列表删除 参数 key
        listHttpHead: '',
        listUrl: '',//列表的请求接口
        listUrlType: 'GET',//列表接口请求方式
        listPageSize: null,
        queryParamStatusKey: 'Status',//列表查询 异常 正常 key
        errorMessage: '',//有异常数据时 保存提示文字
        noLoadData: true,//是否默认弹出时候不加载数据
        importResKey: '',//导出完成后返回的key 列表的时候用到 如：BatchId
        dataSourceListKey: '',
        clearUrlDataType: 'DELETE',//清空临时表接口请求方式
        clearUrl: '',//清空临时表接口
        clearSaveKey: '',//清空临时表接口保存时候的参数
        haveDataSourceKey: '',//验证重复的时候比较时候的key
        onlySaveOneData: null,//是否只能保存一条数据
        bottomBtn: [
          { name: '取消', type: '', funType: 'handleCancel', },
          // { name: '模板下载', type: 'primary', funType: 'downloadTempFile', },
          { name: '保存', type: 'primary', funType: 'handleOk', }
        ],
      },
      tabDataList: [
        {
          name: '正常',
          value: 1,
          count: 0,
        },
        {
          name: '异常',
          value: 2,
          count: 0,
        },
      ],
      table: {
        curStatus: 1,
        tabStatusKey: this.importConfig.queryParamStatusKey ? this.importConfig.queryParamStatusKey : 'Status',
        operateBtns: [
          { name: '批量移除', type: 'primary', icon: '', key: 'batchDel', id: this.importConfig.batchDelId || '' },
        ],
        rowKey: 'Id',
        customSlot: ['commonContent'],
        selectType: 'checkbox',
      },
      columns: [],
      preId: '',//上一次的Id
      importQueryParamData: [],//导入的所有次数的查询数据
      confirmLoading: false,
      queryParam: {},
      initQueryParam: {},
      dataSource: [],
      haveDataSource: [],
      haveListDataSub: null,
      selectedRowKeys: [],
      uploadResData: {},
      haveImport: false,//是否导入过文件
      importFileName: '', //导入文件名称
      queryCol: 6,
      isInitData: false,
      httpHead: this.importConfig.listHttpHead || '',
      url: {
        listType: this.importConfig.listUrlType || 'GET',
        list: this.importConfig.listUrl || '',
      }
    }
  },
  computed: {
    importExcelUrl: function () {
      return apiHead(this.importHttpHead) + `${this.importUrl.indexOf('{v}') > -1 ? this.importUrl.replace(/{v}/, 'v1') : this.importUrl}`
    },
    downloadUrl() {
      return apiHead('P36100') + `/${this.importConfigData.templateFileName}.xlsx`
    },
    importConfigData() {
      return Object.assign(this.importDefConfig, (this.importConfig || {}));
    },
    isShowTopDownloadTempFile() {
      let haveBottomDown = this.importConfigData.bottomBtn.find(item => {
        return item.funType == 'downloadTempFile' || item.name == '模板下载'
      })
      if (haveBottomDown) {
        return false
      } else {
        return true
      }
    },
  },
  mounted() { },
  created() { },
  methods: {
    importExcelAfter(fileName) {
      this.importFileName = fileName
    },
    show(haveDataSource, queryParam) {
      this.init()
      this.haveImport = false
      this.initQueryParam = queryParam
      this.haveDataSource = haveDataSource
      this.visible = true
      if (this.dataSourceData) {
        this.dataSource = this.dataSourceData
      }
      if (!this.importConfigData.noLoadData) {
        this.setPageSizeNum()
        this.loadData(1)
      }
    },
    init() {
      this.dataSource = []
      this.queryParam = {}
      this.table.curStatus = 1
      this.tabDataList[0].count = 0
      this.tabDataList[1].count = 0
      this.ipagination.total = 0
      this.importQueryParamData = []
      if (this.bordered == true) {
        this.table.bordered = true
      } else {
        this.table.bordered = false
      }
    },
    setPageSizeNum() {
      if (this.importConfigData.listPageSize) {
        this.pageSizeNum = this.importConfigData.listPageSize
        this.tableChangeLoadData = false
      }
    },
    loadAfter(data) {
      this.dataSource = this.importConfigData.dataSourceListKey ? data[this.importConfigData.dataSourceListKey] : data
      if (data.StatuCount && data.StatuCount.length > 0) {
        let successStatus = data.StatuCount.find(item => item.Status == 1)
        let errorStatus = data.StatuCount.find(item => item.Status == 2)
        if (successStatus) this.tabDataList[0].count = successStatus.Count
        if (errorStatus) this.tabDataList[1].count = errorStatus.Count
      }
      this.$emit('importLoadAfter', data)
      // 如果移除的时候没有数据了,就加载上一页的数据
      if (this.ipagination.current > 1 && this.dataSource.length == 0) {
        this.ipagination.current = this.ipagination.current - 1
        this.loadData()
      }

      if (this.haveListDataSub && this.haveDataSource && this.haveDataSource.length > 0) {
        if (!this.checkListSameData()) {
          return
        }
        this.saveData()
      }

    },
    searchQuery() {
      if (this.haveImport == true) {
        this.loadData(1)
      }
    },
    onTabChange() {
      this.selectedRowKeys = []
    },
    // 列表操作
    operate(record, type) {
      if (type == 'batchDel') {

        this.onMoreDeleteClick()
      } else if (type == '移除') {
        let that = this
        this.$confirm({
          title: '提示',
          content: '确认移除？',
          onOk() {
            that.onRemoveGoodsClick(record)
          },
          onCancel() { }
        })
      }
    },

    // 确定
    handleOk() {
      let flag = true
      if (this.okBefore) {
        flag = this.okBefore()
      }
      if (!flag) {
        return false
      }
      if (this.tabDataList[1].count > 0) {
        const text = this.importConfigData.errorMessage || '导入的文档存在异常，请处理后再保存！'
        this.$message.error(text)
      } else {
        if (this.tabDataList[0].count > 0) {
          if (this.importConfigData.onlySaveOneData == true && this.dataSource.length > 1) {
            this.$message.error('该模式只能选择一条数据，请移除后再保存')
            return
          }
          // 判断列表上是否有该数据
          if (this.haveDataSource && this.haveDataSource.length > 0) {
            if (this.table.curStatus == 2) {
              this.haveListDataSub = true
              this.table.curStatus = 1
              this.loadData(1)
              return
            } else {
              if (!this.checkListSameData()) {
                return
              }
            }
          }
          this.saveData()
        } else {
          this.$message.warning('请选择导入的数据！')
        }
      }
    },
    checkListSameData() {
      let sameArray = []
      this.haveListDataSub = null
      this.dataSource.map(item => {
        let haveData = this.haveDataSource.find(rItem => {
          return item[this.importConfigData.haveDataSourceKey] == rItem[this.importConfigData.haveDataSourceKey]
        })
        if (haveData) {
          sameArray.push(haveData)
        }
      })
      if (sameArray.length > 0) {
        let text = ''
        let sameArraytext = sameArray.map(item => item[this.importConfigData.haveDataSourceKey]).join(',')
        text = '导入' + this.importConfigData.importTitle + '[' + sameArraytext + ']在列表中重复，请移除后再保存'
        this.$message.error(text)
        return false
      } else {
        return true
      }
    },
    // 保存
    saveData() {
      if (!this.importConfigData.saveUrl) {
        return
      }
      let params = {
        [this.importConfigData.saveKey]: this.uploadResData[this.importConfigData.saveKey]
      }
      if (this.topViewData) {
        params = {
          ...this.initQueryParam,
          ...this.topViewData,
          ...params,
        }
      } else if (this.initQueryParam) {
        params = {
          ...this.initQueryParam,
          ...params,
        }
      }
      let obj = {}
      if (this.importConfigData.saveUrl && this.importConfigData.saveUrlType == 'POST') {
        obj = postAction(this.importConfigData.saveUrl, params, this.importConfigData.listHttpHead)
      } else if (this.importConfigData.saveUrl && this.importConfigData.saveUrlType == 'GET') {
        obj = getAction(this.importConfigData.saveUrl, params, this.importConfigData.listHttpHead)
      } else if (this.importConfigData.saveUrl && this.importConfigData.saveUrlType == 'PUT') {
        obj = putAction(this.importConfigData.saveUrl, params, this.importConfigData.listHttpHead)
      }
      this.confirmLoading = true
      obj.then(res => {
        if (res.IsSuccess) {
          let data = []
          if (typeof (res.Data) == 'boolean') {
            this.table.curStatus = 1  // 保存时始终使用正常状态的数据
            this.loadData(1)  // 先加载数据
            // 监听 loadAfter 事件来处理数据
            this.$once('importLoadAfter', (loadedData) => {
              data = this.dataSource.filter(item => !item[this.importConfigData.ErrorShowKey])
              this.$emit('ok', data)
              this.close()
            })
            return // 提前返回，避免执行后续代码
          } else {
            data = res.Data
            this.$emit('ok', data)
            this.close()
          }
        } else {
          this.$message.warning(res.Msg)
        }
      }).finally(() => {
        this.confirmLoading = false
      })
    },
    // 移除和批量移除
    deleteGoods(ids) {
      if (!this.importConfigData.listRemoveUrl) {
        return
      }
      let params = {
        [this.importConfigData.listRemoveKey]: ids
      }
      let obj = {}
      if (this.importConfigData.listRemoveType && this.importConfigData.listRemoveType == 'POST') {
        obj = postAction(this.importConfigData.listRemoveUrl, this.importConfigData.listRemoveKey ? params : ids, this.importConfigData.listHttpHead)
      } else if (this.importConfigData.listRemoveType && this.importConfigData.listRemoveType == 'DELETE') {
        obj = deleteAction(this.importConfigData.listRemoveUrl, this.importConfigData.listRemoveKey ? params : ids, this.importConfigData.listHttpHead)
      }
      obj.then(res => {
        if (res.IsSuccess) {
          this.loadData()
          this.selectedRowKeys = []
        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
    // 清空临时表
    clearTemporaryTable(id) {
      if (!this.importConfigData.clearUrl) {
        return
      }
      if (!id) {
        return
      }
      let params = {
        [this.importConfigData.clearSaveKey]: id
      }
      let obj = {}
      if (this.importConfigData.clearUrlType && this.importConfigData.clearUrlType == 'POST') {
        obj = postAction(this.importConfigData.clearUrl, this.importConfigData.clearSaveKey ? params : id, this.importConfigData.listHttpHead)
      } else if (this.importConfigData.clearUrlType && this.importConfigData.clearUrlType == 'DELETE') {
        let url = ''
        if (this.importConfigData.clearSaveKey) {
          url = this.importConfigData.clearUrl + `?${this.importConfigData.clearSaveKey}=${id}`
        } else {
          url = this.importConfigData.clearUrl
        }
        obj = deleteAction(url, this.importConfigData.clearSaveKey ? params : id, this.importConfigData.listHttpHead)
      } else if (this.importConfigData.clearUrlType && this.importConfigData.clearUrlType == 'PUT') {
        obj = putAction(this.importConfigData.clearUrl, this.importConfigData.clearSaveKey ? params : id, this.importConfigData.listHttpHead)
      }
      obj.then(res => {
        if (res.IsSuccess) {
          // this.loadData()
        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    bottomBtnClick(item) {
      switch (item.funType) {
        case 'handleCancel':
          this.handleCancel()
          break;
        case 'handleOk':
          this.handleOk()
          break;
        case 'downloadTempFile':
          if (item.url) {
            // this.downUrlFun(item)
            this.$emit('downloadTempFileDiy', item)
          } else {
            window.location.href = this.downloadUrl
          }
          break;
        default:
          break;
      }
    },
    downUrlFun(item) {
      let params = item.params || {}
      let obj = {}
      if (item.urlType && item.urlType == 'POST') {
        obj = postAction(item.url, params, item.httpHead)
      } else {
        obj = getAction(item.url, params, item.httpHead)
      }
      obj.then(res => {
        if (res.IsSuccess) {

        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
      this.importFileName = ''
      let id = undefined
      if (this.importConfigData.clearUrl) {
        if (this.importConfigData.clearSaveKey) {
          id = this.queryParam[this.importConfigData.clearSaveKey]
        }
        this.clearTemporaryTable(id)
      }
    },
    onRemoveGoodsClick(record) {
      let ids = []
      ids.push(record.Id)
      this.deleteGoods(ids)
    },
    onMoreDeleteClick() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择数据!')
        return
      }
      this.deleteGoods(this.selectedRowKeys)
    },
    /**
     * 异常提示点击事件
     */
    onAbnormalClick(record) {
      if (!record[this.importConfigData.ErrorShowKey]) {
        return
      }
      this.ToastModel.Title = '存在以下异常，将导致保存失败：'
      this.ToastModel.Content = record[this.importConfigData.ErrorShowKey] ? record[this.importConfigData.ErrorShowKey].split(';') : []

      this.errVisibile = true
    },
    onKnowClick() {
      this.ToastModel = {
        Title: '',
        Content: []
      }
      this.errVisibile = false
    },
    // 处理文件上传状态变化的方法
    onUploadChange(info) {
      const status = info.file.status
      // 当文件上传完成时
      if (status === 'done') {
        if (info.file.response.IsSuccess) {
          // 获取上传返回的数据
          let resData = info.file.response.Data
          this.uploadResData = resData || {}
          let queryParam = {}
          // 根据配置设置导入结果的查询参数
          if (this.importConfigData.importResKey) {
            queryParam[this.importConfigData.importResKey] = this.importConfigData.listQueryKey ? resData[this.importConfigData.listQueryKey] : resData
          }
          this.haveImport = true
          this.queryParam = queryParam
          this.importQueryParamData.push(this.queryParam)
          console.log('导入的数据', this.importQueryParamData, this.queryParam[this.importConfigData.clearSaveKey])
          // 如果有多次导入且配置了清理临时表的key
          if (this.importQueryParamData.length > 1 && this.importConfigData.clearSaveKey) {
            // 查找当前导入数据的索引
            let thisIndex = this.importQueryParamData.findIndex(item => {
              return item[this.importConfigData.clearSaveKey] == this.queryParam[this.importConfigData.clearSaveKey]
            })
            // 获取上一次导入数据的索引
            let preIndex = thisIndex - 1 > 0 ? thisIndex - 1 : 0
            let preId = this.importQueryParamData[preIndex][this.importConfigData.clearSaveKey]
            // 清除上一次的临时表数据
            this.clearTemporaryTable(preId)
          }
          // 设置分页大小
          this.setPageSizeNum()
          // 加载数据列表
          this.loadData(1)
        } else if (!info.file.response.IsSuccess && info.file.response.Msg) {
          // 上传失败时显示错误信息
          this.$message.warning(info.file.response.Msg)
          this.init()
        }
      }
    },
    searchReset() {
      this.queryParam = {}
      this.loadData(1)
    },
  }
}
</script>
<style scoped>
.excel-div {
  position: relative;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background: #fafafa;
}
.icon-close {
  position: absolute;
  top: 10px;
  right: 10px;
}
.ant-result {
  padding: 5px 4px;
}
</style>
