<!--图形验证码 弹框-->
<template>
  <a-modal :visible="visible" @ok="handleOk" :width="500" @cancel="handleCancel" cancelText="关闭">
    <a-form-model ref="form" :rules="rules" :model="model">
      <a-form-model-item label="图形验证码" prop="Code">
        <a-row :gutter="10">
          <a-col :span="14">
            <a-input autocomplete="false" placeholder="图形验证码" :maxLength="6" v-model="model.Code"> </a-input>
          </a-col>
          <a-col :span="10">
            <a-spin :spinning="loading" v-if="!model.captchaImg"> </a-spin>
            <img
              v-else
              :src="'data:image/png;base64,' + model.captchaImg"
              height="40"
              @click="getCaptchaImg"
              style="cursor: pointer"
              title="点击更换验证码"
            />
          </a-col>
        </a-row>
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>
<script>
import moment from 'moment'
import { getAction } from '@/api/manage'
import { getImageCodeBySMS } from '@/api/login'
export default {
  name: 'CaptchaImgModal',
  data() {
    return {
      moment,
      visible: false,
      loading: false,
      model: {
        captchaImg: null,
        captchaId: '',
        ExpireTime: '',
        Code: '',
      },
      url: {},
    }
  },
  computed: {
    rules() {
      return {
        Code: [{ required: true, message: '请输入!', trigger: 'change' }],
      }
    },
  },
  methods: {
    show() {
      this.getCaptchaImg()
      this.visible = true
      this.model.captchaImg = null
      this.model.captchaId = ''
      this.model.ExpireTime = ''
      this.model.Code = ''
    },
    handleCancel() {
      this.visible = false
      this.model.captchaImg = null
      this.model.captchaId = ''
      this.model.ExpireTime = ''
      this.model.Code = ''
    },
    handleOk() {
      this.$refs.form.validate((err) => {
        if (err) {
          if (this.model.ExpireTime && this.returnDateValidator(this.model.ExpireTime)) {
            this.$emit('ok', this.model)
            this.visible = false
          } else {
            this.$notification['error']({
              message: '提示',
              description: '验证码已失效',
              duration: 8,
            })
          }
        }
      })
    },
    // 图形验证码
    getCaptchaImg() {
      let that = this
      that.loading = true
      getImageCodeBySMS()
        .then((res) => {
          if (res.IsSuccess) {
            that.model.captchaId = res.Data.CaptchaId
            that.model.captchaImg = res.Data.CaptchaBase64
            that.model.ExpireTime = res.Data.ExpireTime
          } else {
            that.$notification['error']({
              message: '提示',
              description: res.Msg,
              duration: 8,
            })
          }
        })
        .finally(() => {
          that.loading = false
        })
    },
    //验证码是否已失效
    returnDateValidator(endDate) {
      let validator = false
      let cur = moment(new Date()).format('YYYY-MM-DD HH:mm:ss').replace(/\-/g, '\/')
      endDate = endDate.replace(/\-/g, '\/')
      if (endDate >= cur) {
        validator = true
      } else {
        validator = false
      }
      return validator
    },
  },
}
</script>
<style scoped></style>
