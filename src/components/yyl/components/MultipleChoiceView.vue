<!--技术中心-普通调用接口多选view-->
<template>
  <div>
    <a-checkbox-group v-if="tagType == 'checkbox'" @change="handleChange" :value="value" :disabled="disabled">
      <a-checkbox v-for="(item, key) in dataList" :key="key" :disabled="disabledItems.includes(item.ItemValue)" :value="item.ItemValue">{{ item.ItemName }}</a-checkbox>
    </a-checkbox-group>
    <a-select mode="multiple" :placeholder="placeholder ? placeholder : '请选择'" :disabled="disabled" :open="open" :value="value" @change="handleChange" :style="{ width: width }" :allowClear="true" :maxTagCount="maxTagCount" v-else>
      <a-select-option v-for="(item, key) in dataList" :key="key" :value="item.ItemValue">
        <span style="display: inline-block; width: 100%;" :title="item.ItemName ? item.ItemName : item.ItemDescription">{{ item.ItemName }}</span>
      </a-select-option>
    </a-select>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'MultipleChoiceView',
  props: {
    //显示样式
    tagType: {
      type: String,
      default: ''
    },
    //http请求类型
    httpType: {
      type: String,
      default: 'GET'
    },
    httpHead: {
      type: String,
      default: 'Base'
    },
    //请求参数
    httpParams: {
      type: Object,
      default() {
        return {}
      }
    },
    Url: {
      type: String,
      default: ''
    },
    dataKey: {
      type: Object,
      default() {
        return { name: 'Name', value: 'Id' }
      }
    },
    open: {
      type: Boolean,
      default: undefined,
    },
    localDataList: {
      type: Array,
      default() {
        return []
      }
    },
    disabledItems: {
      type: Array,
      default() {
        return []
      }
    },
    maxTagCount: Number, //最多显示多少个 tag
    placeholder: String, //提示语
    disabled: Boolean, //是否禁用
    value: {
      Type: Array,
      Default: []
    }, //选中值
    width: { type: String, default: '100%' } //宽度
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  data() {
    return {
      dataList: []
    }
  },
  watch: {
    localDataList: {
      handler(newData, oldData) {
        let list = this.parseDataList(newData)
        this.dataList = [].concat(list)
      },
      immediate: true,//第一次声明了就监听
    }
  },
  created() { },
  mounted() {
    this.getData()
  },
  methods: {
    getData(keyWord = '') {
      let that = this
      if (!this.Url) return
      if (this.localDataList.length > 0) return
      let obj = null
      // this.httpParams.Name = keyWord
      if (this.httpType == 'GET') {
        obj = getAction(that.Url, that.httpParams, that.httpHead)
      } else if (that.httpType == 'POST') {
        obj = postAction(that.Url, that.httpParams, that.httpHead)
      }
      if (!obj) {
        return
      }
      obj.then(res => {
        if (res.IsSuccess) {
          const data = res.Data
          let list = that.parseDataList(data)
          that.dataList = [].concat(list)
        }
      })
    },
    parseDataList(list) {
      let arrayList = []
      list.map(item => {
        arrayList.push({
          ItemName: item[this.dataKey['name']],
          ItemValue: item[this.dataKey['value']],
          Item: item
        })
      })
      return arrayList
    },
    handleChange(value) {
      let contents = []
      if (value && value.length) {
        contents = this.getContents(value)
      }
      this.$emit('change', value, contents)
    },
    getContents(vals) {
      let items = []
      if (this.dataList) {
        this.dataList.forEach(x => {
          if (vals.findIndex(t => t == x.ItemValue) > -1) {
            items.push(x.Item ? x.Item : x)
          }
        })
      }
      return items
    },
    setCurrentDictOptions(list) {
      this.dataList = list
    },
    getCurrentDictOptions() {
      return this.dataList
    }
  }
}
</script>

<style scoped>
</style>
