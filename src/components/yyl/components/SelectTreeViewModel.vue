<!-- 
  * 与SelectTreeView的区别：
  *  1. 可以使用v-model双向绑定，使用时无需绑定change事件手动改变绑定的value
  *  2. 使用时change事件的返回参数结构和顺序有变化，原来第一个参数是当前选中的节点记录，第二个参数是dataRef，
  *     现在第一个参数是选中的value值，第二个是当前选中的节点记录，第三个参数是dataRef
-->
<template>
  <div>
    <a-tree-select :laceholder="DataValue" :show-search="showSearch" style="width: 100%;" :placeholder="placeholder" :value="value" :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }" allowClear treeDefaultExpandAll :disabled="disabled" :treeData="DataList" @change="change" @search="handleSearch" :replaceFields="replaceFields" :multiple="multiple" treeNodeFilterProp="label"></a-tree-select>
    <!--@search="fetchUser"-->
  </div>
</template>

<script>
import moment from 'moment'
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'SelectTreeViewModel',
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
    httpType: {
      type: String,
      default: 'GET'
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    value: {
      Type: String,
      Default: ''
    }, //选中值
    list: {
      type: Object,
      default() {
        return null
      }
    },
    httpParams: {
      type: Object,
      default() {
        return {}
      }
    },
    selectable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    type: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isAllName: {
      type: Boolean,
      default: false
    },
    DataValue: {
      type: String,
      default: 'Operating'
    },
    Url: {
      type: String,
      default: ''
    },
    httpHead: {
      type: String,
      default: ''
    },
    Level: {
      type: Number,
      default: 2
    },
    filterHandle: {
      type: Function
    },
    replaceFields: {
      type: Object,
      default() {
        return { children: 'children', title: 'title', key: 'key', value: 'value' }
      }
    },
    dataKey: {
      type: Object,
      default() {
        return {
          value: 'Id',
          label: 'Name',
          selectableKey: '',
          disabledKey: '',
          children: 'ChildrenList',
          nameList: []
        }
      }
    },
    select: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  data() {
    // this.fetchUser = debounce(this.onSearch, 800);
    return {
      url: this.Url,
      DataList: [],
      selectItem: { value: '', label: '' }
    }
  },
  mounted() {
    this.selectItem = this.select
    this.getData()
  },
  methods: {
    moment,
    init(value) {
      this.selectItem.value = value
      // console.log(value)
    },
    getData(keyWord = '') {
      if (!this.url) return
      let obj = null
      // console.log('httpType',this.httpType)
      this.httpParams.Name = keyWord
      if (this.httpType == 'GET') {
        obj = getAction(this.url, this.httpParams, this.httpHead)
      } else if (this.httpType == 'POST') {
        obj = postAction(this.url, this.httpParams, this.httpHead)
      }
      if (!obj) {
        return
      }
      obj.then(res => {
        if (res.IsSuccess) {
          const data = (typeof this.filterHandle === 'function' && this.filterHandle(res.Data)) || res.Data || []
          let list = this.setList(data)
          const defaultList = (this.list && [...this.list]) || []
          this.DataList = defaultList.concat(list)
          this.$emit('loadEnd')
          if (this.selectItem.value) {
            this.filterNotId()
          }
        }
      })
    },
    filterNotId() {
      if (!this.multiple) return
      this.selectItem.value.map((t, i) => {
        if (this.DataList.findIndex(v => v.value == t) < 0) {
          this.selectItem.value.splice(i, 1)
        }
      })
    },
    setList(arr) {
      let data = []
      if (this.type && this.type == 'attr') {
        arr.map(item => {
          let childrenList = item[this.dataKey['children']]
          let NotBasicAttributeDtos = item['NotBasicAttributeDtos']
          let otherName = ''
          if (this.dataKey['nameList'] && this.dataKey['nameList'].length > 0) {
            this.dataKey['nameList'].forEach(x => {
              if (x === 'IsKey') {
                let isKey = item[x] ? '关键字段' : ''
                if (isKey) {
                  otherName += isKey ? isKey + '/' : ''
                }
              } else {
                otherName += item[x] + '/'
              }
            })
          }
          let arrItem = {
            value: item[this.dataKey['value']],
            label: item.GroupName
              ? `${item[this.dataKey['label']] || item[this.dataKey['pLabel']]}${otherName ? '/' + otherName.substring(0, otherName.length - 1) : ''
              }`
              : `${item[this.dataKey['label']] || item[this.dataKey['pLabel']]}${otherName ? '/' + otherName.substring(0, otherName.length - 1) : ''
              }`,
            selectable: !item.GroupName ? true : false
          }
          if (childrenList && childrenList.length) {
            arrItem.children = this.setList(childrenList)
          }
          if (NotBasicAttributeDtos && NotBasicAttributeDtos.length) {
            arrItem.children = (arrItem.children || []).concat(this.setList(NotBasicAttributeDtos))
          }
          data.push(arrItem)
        })
      } else {
        arr.map(item => {
          let childrenList = item[this.dataKey['children']]
          let otherName = ''
          if (this.dataKey['nameList'] && this.dataKey['nameList'].length > 0) {
            this.dataKey['nameList'].forEach(x => {
              otherName += item[x] + '/'
            })
          }
          let lastIndex = this.isAllName ? item.FullDepartmentName.lastIndexOf('-') : -1
          let subLabel =
            this.isAllName && lastIndex >= 0 ? '-' + item[this.dataKey['label']] : item[this.dataKey['label']]
          let full = this.isAllName ? item.FullDepartmentName.replace(subLabel, '') : ''
          let arrItem = {
            value: item[this.dataKey['value']],
            label: this.isAllName
              ? (item[this.dataKey['label']] || item[this.dataKey['pLabel']]) + (full ? ' 【 ' + full + '】' : '')
              : (item[this.dataKey['label']] || item[this.dataKey['pLabel']]) +
              '' +
              (otherName ? '/' + otherName.substring(0, otherName.length - 1) : ''),
            selectable: this.dataKey['selectableKey'] ? (item[this.dataKey['selectableKey']] ? true : false) : true,
            disabled:
              typeof item.disabled !== 'undefined'
                ? item.disabled
                : this.dataKey['disabledKey']
                  ? item[this.dataKey['disabledKey']]
                    ? false
                    : true
                  : false,
            item: item
          }
          if (childrenList && childrenList.length) {
            arrItem.children = this.setList(childrenList)
          }
          data.push(arrItem)
        })
      }
      return data
    },
    change(value, label, extra) {
      let dataRef = {} // node上信息
      let parent = { value: '', label: '' }
      try {
        parent = extra.triggerNode.$parent.$options.propsData
        dataRef = extra.triggerNode.dataRef
      } catch { }
      this.selectItem = {
        value: value,
        label: label ? label[0] : label,
        pValue: parent.value ? parent.value : '',
        pLabel: parent.label ? parent.label : ''
      }
      this.$emit('change', this.selectItem.value, this.selectItem, dataRef)
    },
    handleSearch(value) {
      this.getData(value)
    },
    onSearch(value) {
      this.getData(value)
    },
    setUrl(url) {
      this.url = url
      this.getData()
    }
  },
  watch: {}
}
</script>

<style></style>
