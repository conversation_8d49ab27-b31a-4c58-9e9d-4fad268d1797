<template>
  <a-spin :spinning="loading">
    <!-- :style="{  width: w + 'px',height:h+'px'}" -->
    <img :src="srcUrl ? srcUrl : defaultImg" class="chatheadimg" />
  </a-spin>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin.js'
export default {
  name: 'IdImage',
  mixins: [ListMixin],
  components: {
    // Viewer
  },
  props: {
    id: { type: String }, //填了默认图片是防止报错
    defaultImg: { type: String }
  },
  data() {
    return {
      srcUrl:'',
      loading: false
    }
  },
  mounted() {
    let that = this
    if (that.id) {
      if (that.id.startsWith('http')) {
        that.srcUrl = that.id
      } else {
        that.loading = true
        that.getFileTempUrl(that.id, res => {
          that.loading = false
          if (res) {
            that.srcUrl = res.TempUrl
          }
        })
      }
    }
  },
  methods: {}
}
</script>

<style></style>
