<!-- 多类型导出弹窗 -->
<template>
  <a-modal :title="modalTitle" :width="width" :visible="visible" :confirmLoading="confirmLoading" @ok="handleOk" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" cancelText="关闭" :destroyOnClose="true">

    <a-spin :spinning="confirmLoading">
      <a-form-model-item prop="recordList">
        <a-checkbox-group v-model="checkKeys">
          <a-row>
            <a-col style="margin-bottom: 8px;" :span="8" v-for="(item,index) in recordArray" :key="index">
              <a-checkbox :value="item.value">
                {{item.name}}
              </a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>

      </a-form-model-item>
    </a-spin>

    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" @click="handleCancel">取消</a-button>
      <a-button :disabled="confirmLoading" :style="{ marginRight: '8px' }" @click="chooseAll" type="primary">{{isAll?'反选':'全选'}}</a-button>
      <a-button :disabled="confirmLoading" @click="handleOk" type="primary">确定</a-button>
    </a-row>
  </a-modal>
</template>

<script>
import moment from "moment";
import { ListMixin } from '@/mixins/ListMixin'
import { getAction, putAction, postAction } from "@/api/manage";
export default {
  name: "ExportMultiModal",
  components: {},
  mixins: [ListMixin],
  props: {
    width: {
      type: Number,
      default: 800,
    },
    // 导出的标题
    modalTitle: {
      type: String,
      default: '导出',
    },
    // 导出的选项
    recordArray: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 导出参数
    exportParams: {
      type: Object,
      default: () => {
        return {
          name: '',
          methods: 'POST',
          url: '',
          httpHead: '',
        }
      }
    },
    // 导出的时候传参的key
    exportKey: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      visible: false,
      dateFormat: "YYYY-MM-DD",
      dateTimeFormat: "YYYY-MM-DD hh:mm:ss",
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 20 },
      },
      isAll: false,//是否全选
      queryParam: {},
      checkKeys: [],
      confirmLoading: false,
    };
  },
  mounted() { },
  created() { },
  methods: {
    moment,
    show(queryParam) {
      this.model = {};
      this.checkKeys = []
      this.isAll = false
      this.queryParam = Object.assign({}, queryParam)
      this.visible = true;
    },
    chooseAll() {
      this.isAll = !this.isAll
      let allKeys = []
      if (this.isAll) {
        allKeys = this.recordArray.map(item => {
          return item.value
        })
      } else {
        allKeys = []
      }
      this.checkKeys = allKeys
    },
    // 确定
    handleOk() {
      if (this.checkKeys.length == 0) {
        this.$message.warning('请选择导出类型!')
        return
      }
      let array = []
      this.checkKeys.map(item => {
        if (typeof (item) == 'string') {
          array = array.concat(item.split(','))
        }
      })
      let keys = this.checkKeys.concat(array)
      let keysList = []
      keys.map(item => {
        if (isNaN(Number(item)) == false) {
          keysList.push(Number(item))
        }
      })
      this.queryParam[this.exportKey] = keysList
      this.handleExportXls(
        this.exportParams.name || '导出记录',
        this.exportParams.methods,
        this.exportParams.url,
        null,
        this.exportParams.httpHead,
        this.queryParam
      )
      // 关闭
      this.close()
    },
    // 关闭
    handleCancel() {
      this.close();
    },
    close() {
      this.$emit("close");
      this.visible = false;
    },
  },
};
</script>
