<template>
  <div>
    <a-tree-select
      :placeholder="placeholder"
      :laceholder="DataValue"
      :show-search="showSearch"
      style="width: 100%;"
      v-model="selectItem.value"
      :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
      allowClear
      treeDefaultExpandAll
      :disabled="disabled"
      :treeData="DataList"
      @change="change"
      @search="handleSearch"
      :multiple="multiple"
      treeNodeFilterProp="label"
    ></a-tree-select>
    <!--@search="fetchUser"-->
  </div>
</template>

<script>
import moment from "moment";
import { getAction, postAction } from "@/api/manage";

export default {
  name: "SelectTreeView",
  props: {
    multiple: {
      type: Boolean,
      default: false,
    },
    httpType: {
      type: String,
      default: "GET",
    },
    showSearch: {
      type: Boolean,
      default: true,
    },
    list: {
      type: Object,
      default() {
        return null;
      },
    },
    httpParams: {
      type: Object,
      default() {
        return {};
      },
    },
    selectable: {
      type: Boolean,
      default: true,
    },
    type: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isAllName: {
      type: Boolean,
      default: false,
    },
    DataValue: {
      type: String,
      default: "Operating",
    },
    Url: {
      type: String,
      default: "",
    },
    httpHead: {
      type: String,
      default: "",
    },
    Level: {
      type: Number,
      default: 2,
    },
    placeholder:{
      type: String,
      default: "请选择",
    },
    dataKey: {
      type: Object,
      default() {
        return {
          value: "Id",
          label: "Name",
          selectableKey: "",
          disabledKey: "",
          children: "ChildrenList",
          nameList: [],
        };
      },
    },
    select: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    // this.fetchUser = debounce(this.onSearch, 800);
    return {
      url: this.Url,
      DataList: [],
      selectItem: { value: "", label: "" },
    };
  },
  mounted() {
    this.selectItem = this.select;
    this.getData();
  },
  methods: {
    moment,
    init(value) {
      this.selectItem.value = value;
      // console.log(value)
    },
    getData(keyWord = "") {
      if (!this.url) return;
      let obj = null;
      // console.log('httpType',this.httpType)
      this.httpParams.Name = keyWord;
      if (this.httpType == "GET") {
        obj = getAction(this.url, this.httpParams, this.httpHead);
      } else if (this.httpType == "POST") {
        obj = postAction(this.url, this.httpParams, this.httpHead);
      }
      if (!obj) {
        return;
      }
      obj.then((res) => {
        if (res.IsSuccess) {
          const data = res.Data || [];
          let list = this.setList(data);
          if (this.list) {
            this.DataList = [this.list].concat(list);
          } else {
            this.DataList = [].concat(list);
          }
          this.$emit("loadEnd");
          if (this.selectItem.value) {
            this.filterNotId();
          }
        }
      });
    },
    filterNotId() {
      if (!this.multiple) return;
      this.selectItem.value.map((t, i) => {
        if (this.DataList.findIndex((v) => v.value == t) < 0) {
          this.selectItem.value.splice(i, 1);
        }
      });
    },
    setList(arr) {
      let data = [];
      if (this.type && this.type == "attr") {
        arr.map((item) => {
          let childrenList = item[this.dataKey["children"]];
          let NotBasicAttributeDtos = item["NotBasicAttributeDtos"];
          let otherName = "";
          if (this.dataKey["nameList"] && this.dataKey["nameList"].length > 0) {
            this.dataKey["nameList"].forEach((x) => {
              if (x === "IsKey") {
                let isKey = item[x] ? "关键字段" : "";
                if (isKey) {
                  otherName += isKey ? isKey + "/" : "";
                }
              } else {
                otherName += item[x] + "/";
              }
            });
          }
          let arrItem = {
            value: item[this.dataKey["value"]],
            label: item.GroupName
              ? `${
                  item[this.dataKey["label"]] || item[this.dataKey["pLabel"]]
                }${
                  otherName
                    ? "/" + otherName.substring(0, otherName.length - 1)
                    : ""
                }`
              : `${
                  item[this.dataKey["label"]] || item[this.dataKey["pLabel"]]
                }${
                  otherName
                    ? "/" + otherName.substring(0, otherName.length - 1)
                    : ""
                }`,
            selectable: !item.GroupName ? true : false,
          };
          if (childrenList && childrenList.length) {
            arrItem.children = this.setList(childrenList);
          }
          if (NotBasicAttributeDtos && NotBasicAttributeDtos.length) {
            arrItem.children = (arrItem.children || []).concat(
              this.setList(NotBasicAttributeDtos)
            );
          }
          data.push(arrItem);
        });
      } else {
        arr.map((item) => {
          let childrenList = item[this.dataKey["children"]];
          let otherName = "";
          if (this.dataKey["nameList"] && this.dataKey["nameList"].length > 0) {
            this.dataKey["nameList"].forEach((x) => {
              otherName += item[x] + "/";
            });
          }
          let lastIndex = this.isAllName ? item.FullDepartmentName.lastIndexOf('-') : -1;
          let subLabel = this.isAllName && lastIndex >= 0 ? '-' + item[this.dataKey["label"]] : item[this.dataKey["label"]];
          let full  = this.isAllName ? item.FullDepartmentName.replace(subLabel,'') : '';
          let arrItem = {
            value: item[this.dataKey["value"]],
            label:this.isAllName ? (item[this.dataKey["label"]] || item[this.dataKey["pLabel"]]) + (full?' 【 ' + full + '】':'') : 
              (item[this.dataKey["label"]] || item[this.dataKey["pLabel"]]) +
              "" +
              (otherName
                ? "/" + otherName.substring(0, otherName.length - 1)
                : ""),
            selectable: this.dataKey["selectableKey"]
              ? item[this.dataKey["selectableKey"]]
                ? true
                : false
              : true,
            disabled: this.dataKey["disabledKey"]
              ? item[this.dataKey["disabledKey"]]
                ? false
                : true
              : false,
            item: item,
          };
          if (childrenList && childrenList.length) {
            arrItem.children = this.setList(childrenList);
          }
          data.push(arrItem);
        });
      }
      return data;
    },
    change(value, label, extra) {
      let dataRef = {}; // node上信息
      let parent = { value: "", label: "" };
      try {
        parent = extra.triggerNode.$parent.$options.propsData;
        dataRef = extra.triggerNode.dataRef;
      } catch {}
      this.selectItem = {
        value: value,
        label: label.join(""),
        pValue: parent.value ? parent.value : "",
        pLabel: parent.label ? parent.label : "",
      };
      this.$emit("change", this.selectItem, dataRef);
    },
    handleSearch(value) {
      this.getData(value);
    },
    onSearch(value) {
      this.getData(value);
    },
    setUrl(url) {
      this.url = url;
      this.getData();
    },
  },
  watch: {},
};
</script>

<style></style>
