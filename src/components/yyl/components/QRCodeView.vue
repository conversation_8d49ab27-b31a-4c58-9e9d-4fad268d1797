<!-- 二维码生成器 -->
<template>
  <div class="QRCode-wrap">
    <div class="img-cont" :style="{ width: contentW + 'px', height: contentH + 'px' }" ref="qrCodeUrl" id="qrCode" />
  </div>
</template>
<script>
import QRCode from 'qrcodejs2'
export default {
  name: 'QRCodeView',
  props: {
    // 二维码内容
    contentUrl: {
      type: String,
      required: true
    },
    // 二维码宽度
    contentW: {
      type: Number,
      default: 120
    },
    // 二维码高度
    contentH: {
      type: Number,
      default: 120
    },
    // 二维码深色颜色
    colorDark: {
      type: String,
      default: '#000000'
    },
    // 二维码浅色颜色
    colorLight: {
      type: String,
      default: '#ffffff'
    },
    // 二维码容错级别  L  M  Q  H
    level: {
      type: String,
      default: 'L'
    }
  },
  data() {
    return {
      MCImage: ''
    }
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.getQRCode()
    })
  },
  methods: {
    getQRCode() {
      const _that = this
      this.MCImage = new QRCode(_that.$refs.qrCodeUrl, {
        text: _that.contentUrl,
        width: _that.contentW,
        height: _that.contentH,
        colorDark: _that.colorDark,
        colorLight: _that.colorLight,
        correctLevel: QRCode.CorrectLevel[_that.level]
      })
    },
    /***
     * 下载二维码图片
     * @param name 图片名称
     */
    downLoadImage(name) {
      let qrCodeCanvas = document.getElementById('qrCode').getElementsByTagName('canvas')
      let a = document.createElement('a')
      a.href = qrCodeCanvas[0].toDataURL('image/url')
      a.download = name + `二维码.png`
      a.click()
    }
  }
}
</script>
<style lang="less" scoped>
.img-cont {
  display: inline-block;
  object-fit: contain;
}
.QRCode-wrap {
  display: inline-block;
  text-align: center;
}
</style>
