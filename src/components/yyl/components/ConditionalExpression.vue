<template>
  <div>
    <a-form-model ref="form" :rules="rules" :model="model" layout="vertical">
      <a-row :gutter="10" v-if="showTable">
        <a-col :md="inputCol">
          <a-form-model-item :label="`${Blabel}：`" required>
            <a-table
              bordered
              :columns="tableColumns"
              :dataSource="tableData"
              rowKey="Id"
              :pagination="false"
              :scroll="{ x: false, y: '350px' }"
            >
              <!-- ( -->
              <template slot="LeftBracket" slot-scope="text, record">
                <a-select
                  placeholder="请选择"
                  v-model="record.LeftBracket"
                  style="width: 100%;"
                  @change="changeLeftBracket(record)"
                >
                  <a-select-option :value="0">无</a-select-option>
                  <a-select-option value="(">(</a-select-option>
                  <a-select-option value="((">((</a-select-option>
                  <a-select-option value="(((">(((</a-select-option>
                  <a-select-option value="((((">((((</a-select-option>
                  <a-select-option value="(((((">(((((</a-select-option>
                </a-select>
              </template>
              <!-- ) -->
              <template slot="RightBracket" slot-scope="text, record">
                <a-select
                  placeholder="请选择"
                  v-model="record.RightBracket"
                  style="width: 100%;"
                  @change="changeRightBracket(record)"
                >
                  <a-select-option :value="0">无</a-select-option>
                  <a-select-option value=")">)</a-select-option>
                  <a-select-option value="))">))</a-select-option>
                  <a-select-option value=")))">)))</a-select-option>
                  <a-select-option value="))))">))))</a-select-option>
                  <a-select-option value=")))))">)))))</a-select-option>
                </a-select>
              </template>

              <!-- 统计指标 -->
              <template slot="StatisticsType" slot-scope="text, record">
                <EnumSingleChoiceView
                  style="width: 100%;"
                  placeholder="请选择"
                  dictCode="EnumStatisticsType"
                  v-model="record.StatisticsType"
                  @change="(value, text) => changeStatisticsType(value, text, record)"
                />
              </template>
              <!-- 比较方法 -->
              <template slot="CompareMethodType" slot-scope="text, record">
                <EnumSingleChoiceView
                  style="width: 100%;"
                  placeholder="请选择"
                  dictCode="EnumCompareMethodType"
                  v-model="record.CompareMethodType"
                  @change="(value, text) => changeComparisonMethod(value, text, record)"
                />
              </template>
              <!-- 值 -->
              <template slot="CompareValue" slot-scope="text, record">
                <a-input-number
                  placeholder="请输入"
                  style="width: 100%;"
                  v-model="record.CompareValue"
                  @change="changeCompareValue(record)"
                />
              </template>
              <!-- 逻辑 -->
              <template slot="LogicSymbol" slot-scope="text, record, index">
                <EnumSingleChoiceView
                  style="width: 100%;"
                  placeholder="请选择"
                  dictCode="EnumLogicSymbol"
                  v-model="record.LogicSymbol"
                  :disabled="index == tableData.length - 1"
                  @change="(value, text) => changeLogicSymbol(value, text, record)"
                />
              </template>
              <!-- 操作 -->
              <template slot="operation" slot-scope="text, record, index">
                <a @click="handleAddAboveOrBelow(record, index, 'Above')">
                  <a-icon type="plus" />
                  上方
                </a>
                <a @click="handleAddAboveOrBelow(record, index, 'Below')" style="margin-left: 10px;">
                  <a-icon type="plus" />
                  下方
                </a>
                <a @click="removeExpressionItem(index, record)" style="margin-left: 10px; color: red;">
                  <a-icon type="delete" />
                  删除
                </a>
              </template>
            </a-table>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="10" v-show="showConditionExpression">
        <a-col :md="inputCol">
          <a-form-model-item :label="`${Blabel}：`">
            <div
              v-html="model.ConditionExpression"
              id="ConditionExpression"
              style="
                width: 100%;
                min-height: 100px;
                border: 1px solid rgba(0, 0, 0, 0.1);
                background: rgba(0, 0, 0, 0.05);
                border-radius: 2px;
              "
            ></div>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="10">
        <a-col :md="inputCol">
          <a-form-model-item :label="`${Mlabel}：`" :required="showTable ? false : true">
            <div
              v-html="model.ConditionDescription"
              id="ConditionDescription"
              style="
                width: 100%;
                min-height: 100px;
                border: 1px solid rgba(0, 0, 0, 0.1);
                background: rgba(0, 0, 0, 0.05);
                border-radius: 2px;
              "
            ></div>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="10" v-if="FieldDescription">
        <a-col :md="inputCol">
          <a-form-model-item label="统计指标说明：">
            <a-table
              :bordered="true"
              :columns="columns"
              size="middle"
              rowKey="Id"
              :pagination="ipagination"
              :dataSource="dataSource"
              :loading="loading"
              @change="handleTableChange"
              :scroll="{ x: false, y: '200px' }"
            >
            </a-table>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>
<script>
import moment from 'moment'
import { EditMixin } from '@/mixins/EditMixin'
import { ListMixin } from '@/mixins/ListMixin'
import { deleteAction, downFile, getAction, postAction } from '@/api/manage'
const columns = [
  {
    title: '统计指标名称',
    dataIndex: 'Expression',
    key: 'Expression',
    ellipsis: true
  },
  {
    title: '统计指标描述',
    dataIndex: 'Description',
    key: 'Description',
    ellipsis: true
  }
]
export default {
  name: 'ConditionalExpression',
  mixins: [EditMixin, ListMixin],
  props: {
    FieldDescription: {
      type: Boolean,
      default: true
    },
    //是否条件表达式
    showTable: {
      type: Boolean,
      default: true
    },
    //是否显示表达式
    showConditionExpression: {
      type: Boolean,
      default: true
    },

    ForType: {
      type: String,
      default: ''
    },
    userType: {
      type: String,
      default: ''
    },
    Blabel: {
      type: String,
      default: '生效条件表达式'
    },
    Mlabel: {
      type: String,
      default: '生效条件描述'
    }
  },
  // components: {
  //   SingleChoiceView,
  //   DictSingleChoiceView
  // },
  data() {
    return {
      moment,
      dictDataList: [],
      scene: this.userType,
      model: {},
      dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',
      visiblekey: false,
      labelCol: { xs: { span: 24 }, sm: { span: 4 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 18 } },
      inputCol: 24,
      form: this.$form.createForm(this),
      dataKey: {
        name: 'Name',
        value: 'Id'
      },
      //规则配置表格
      tableColumns: [
        {
          title: '(',
          dataIndex: 'LeftBracket',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'LeftBracket' }
        },

        {
          title: '统计指标',
          dataIndex: 'StatisticsType',
          width: 180,
          align: 'center',
          ellipsis: true,
          scopedSlots: { customRender: 'StatisticsType' }
        },
        {
          title: '比较方法',
          dataIndex: 'CompareMethodType',
          width: 130,
          align: 'center',
          scopedSlots: { customRender: 'CompareMethodType' }
        },
        {
          title: '门槛值',
          dataIndex: 'CompareValue',
          width: 120,
          align: 'center',
          ellipsis: true,
          scopedSlots: { customRender: 'CompareValue' }
        },
        {
          title: ')',
          dataIndex: 'RightBracket',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'RightBracket' }
        },
        {
          title: '逻辑',
          dataIndex: 'LogicSymbol',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'LogicSymbol' }
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'center',
          scopedSlots: { customRender: 'operation' }
        }
      ],
      emptyItem: [
        {
          LeftBracket: '',
          StatisticsType: '',
          StatisticsTypeStr: '',
          CompareMethodType: '',
          CompareMethodTypeStr: '',
          CompareValue: '',
          RightBracket: '',
          LogicSymbol: '',
          LogicSymbolStr: '',
          Sort: 0,
          Id: this.GUID()
        }
      ],
      tableData: [
        {
          LeftBracket: '',
          StatisticsType: '',
          StatisticsTypeStr: '',
          CompareMethodType: '',
          CompareMethodTypeStr: '',
          CompareValue: '',
          RightBracket: '',
          LogicSymbol: '',
          LogicSymbolStr: '',
          Sort: 0,
          Id: this.GUID()
        }
      ],

      // 统计指标说明
      columns: columns,
      loading: false,

      url: {
        // zdUrl: this.userType ? 'businessdata/ExpressionColumnMapping?scene=' + this.userType + '&forType=' : '',
        // list: 'businessdata/ExpressionColumnMapping', //"/businessdata/RiskExpression/List",
        // update: '/businessdata/RiskRulesAlertLevel/UpdateUpgradeAlertAmount'
      }
    }
  },
  computed: {
    rules() {
      return {}
    }
  },
  created() {},
  methods: {
    // setScene(scene) {
    //   this.scene = scene
    //   this.url.zdUrl = this.url.list + '?scene=' + this.scene + '&forType=' + this.ForType
    //   if (this.$refs.singleChoiceView) {
    //     this.tableData = JSON.parse(JSON.stringify(this.emptyItem))
    //     this.model = {}
    //     this.$refs.singleChoiceView.setUrl(this.url.zdUrl)
    //   }
    // },
    // 统计指标说明
    // async loadData(arg) {
    //   if (!this.FieldDescription) return
    //   if (arg === 1) {
    //     this.ipagination.current = 1
    //   }
    //   this.loading = true
    //   let params = this.getQueryParams()
    //   params.scene = this.scene
    //   params.ForType = this.ForType
    //   let departs = await getAction(this.url.list, params)
    //   this.loading = false
    //   if (departs.IsSuccess) {
    //     this.dataSource = departs.Data
    //     this.ipagination.total = departs.Count ? departs.Count : 0
    //   }
    // },
    init(record) {
      // if (record.Id) {
      //   this.visiblekey = true
      // } else {
      //   this.visiblekey = false
      // }
      this.form.resetFields()
      this.model = Object.assign({
        ConditionExpression: '',
        ConditionDescription: ''
      })
      record.map(v => {
        v.Id = this.GUID()
      })
      this.tableData = record
      this.$nextTick(() => {
        this.resetExpressionData()
      })
    },

    /**
     * @description: 上方添加 | 下方添加
     * @param {record|index|code}单条数据|下标 | Below -- 下方添加 | 上方添加
     * @return {*}
     */
    handleAddAboveOrBelow(record, index, code) {
      if (!record.StatisticsType) {
        this.$message.warning('统计指标不能为空')
        return
      }
      if (!record.CompareMethodType) {
        this.$message.warning('比较方法不能为空')
        return
      }
      if (!record.CompareValue) {
        this.$message.warning('门槛值不能为空')
        return
      }
      let item = {
        LeftBracket: '',
        StatisticsType: '',
        StatisticsTypeStr: '',
        CompareMethodType: '',
        CompareMethodTypeStr: '',
        CompareValue: '',
        RightBracket: '',
        LogicSymbol: '',
        LogicSymbolStr: '',
        Sort: 0,
        Id: this.GUID()
      }
      if (code == 'Below') {
        record.LogicSymbol = '1'
        record.LogicSymbolStr = '并且'
        this.tableData.splice(index + 1, 0, item)
      } else {
        item.LogicSymbol = '1'
        item.LogicSymbolStr = '并且'
        this.tableData.splice(index, 0, item)
      }
      this.resetExpressionData()
    },
    /**
     * @description: 删除
     * @param {*} index
     * @return {*}
     */
    removeExpressionItem(index) {
      if (this.tableData.length == 1) {
        //只有一个的时候重置初始数据
        this.tableData = JSON.parse(JSON.stringify(this.emptyItem))
        this.model.ConditionExpression = ''
        this.model.ConditionDescription = ''
        return
      }
      if (index != 0) {
        this.tableData[index - 1].LogicSymbol = ''
        this.tableData[index - 1].LogicSymbolStr = ''
      }
      this.tableData.splice(index, 1)
      this.resetExpressionData()
    },
    resetExpressionData() {
      if (this.tableData.length > 0) {
        this.productExpression()
      } else {
        this.model.ConditionExpression = ''
        this.model.ConditionDescription = ''
      }
    },
    /**
     * @description: (   改变 生成表达式
     * @param {*} 单条数据
     */
    changeLeftBracket(item) {
      item.LeftBracket == 0 ? (item.LeftBracket = undefined) : item.LeftBracket
      this.resetExpressionData()
    },
    /**
     * @description: 非  改变 生成表达式
     * @param {*} 单条数据
     */
    changeWrong(item) {
      this.resetExpressionData()
    },
    /**
     * @description: 统计指标  改变 生成表达式
     * @param {value|text|item} 值,选中文字, 单条table数据
     */
    changeStatisticsType(value, text, item) {
      item.StatisticsTypeStr = text
      item.StatisticsType = value
      this.resetExpressionData()
    },

    /**
     * @description: 比较方法  改变 生成表达式
     * @param {value|opt|item} 值,选中,单条数据
     */
    changeComparisonMethod(value, text, item) {
      item.CompareMethodType = value
      item.CompareMethodTypeStr = text
      this.resetExpressionData()
    },
    /**
     * @description: 值  改变 生成表达式
     * @param {item} 单条数据
     */
    changeCompareValue(item) {
      this.resetExpressionData()
    },
    /**
     * @description:逻辑  改变 生成表达式
     * @param {*} 单条数据
     */
    changeLogicSymbol(value, text, item) {
      item.LogicSymbol = value
      item.LogicSymbolStr = text
      item.LogicSymbol < 1 ? (item.LogicSymbol = undefined) : item.LogicSymbol
      this.resetExpressionData()
    },
    /**
     * @description: )  改变 生成表达式
     * @param {*} 单条数据
     */
    changeRightBracket(item) {
      item.RightBracket == 0 ? (item.RightBracket = undefined) : item.RightBracket
      this.resetExpressionData()
    },
    /**
     * @description: 生成表达式
     */
    productExpression() {
      var EngStr = ''
      var CnStr = ''
      this.model.ConditionExpression = ''
      this.model.ConditionDescription = ''
      var cn_temp = ''
      var en_temp = ''
      var CnExpressionCompareTypeStr = ''
      this.tableData.forEach((item, key) => {
        var cnHtml = ''
        var enHtml = ''
        var CompareTypeStr = ''
        var nr = ''

        if (
          item.CompareMethodType &&
          (item.CompareMethodType == 5 || item.CompareMethodType == 6) &&
          !item.CompareValue
        ) {
          //  等于|不等于   没有值时

          enHtml += `( ${item.StatisticsType}<b style="color:red;font-weight: 400;font-style: normal;"> ${
            item.CompareMethodType
          } </b>null ${item.CompareMethodType == 5 ? 'or' : 'and'} ${
            item.StatisticsType
          }<b style="color:red;font-weight: 400;font-style: normal;"> ${item.CompareMethodType} </b>${0} )`
          cnHtml += `${item.StatisticsTypeStr}<b style="color:red;font-weight: 400;font-style: normal;"> ${item.CompareMethodTypeStr} 空</b>`
        } else {
          if (item.StatisticsType) {
            enHtml += `${item.StatisticsType}`
            cnHtml += `${item.StatisticsTypeStr}`
          }
          if (item.CompareMethodType) {
            enHtml += `<b style="color:red;font-weight: 400;font-style: normal;"> ${item.CompareMethodType} </b>`
            cnHtml += `<b style="color:red;font-weight: 400;font-style: normal;"> ${item.CompareMethodTypeStr} </b>`
          }
          if (item.CompareValue) {
            enHtml += `<span style="color:orange;font-weight: 400;font-style: normal;"> ${item.CompareValue} </span>`
            cnHtml += `<span style="color:orange;font-weight: 400;font-style: normal;">${item.CompareValue}</span>`
          }
        }
        //英文
        en_temp += `<b style="color:blue;font-weight: 700;font-style: normal;">${item.LeftBracket ||
          ''} </b>${enHtml}<b style="color:blue;font-weight: 700;font-style: normal;">
        ${item.RightBracket || ''}</b>`
        // 中文
        cn_temp += `<b style="color:blue;font-weight: 700;font-style: normal;">${item.LeftBracket || ''} </b>
            ${cnHtml}
        <b style="color:blue;font-weight: 700;font-style: normal;">
          ${item.RightBracket || ''}</b>`
        if (key < this.tableData.length - 1) {
          cn_temp += item.LogicSymbol
            ? `<b style='color:blue;font-weight: 400;font-style: normal;'> ${item.LogicSymbolStr} </b>`
            : ' '
          en_temp += item.LogicSymbol
            ? `<b style='color:blue;font-weight: 400;font-style: normal;'> ${item.LogicSymbol} </b>`
            : ' '
        }
      })
      this.model.ConditionExpression = en_temp
      this.model.ConditionDescription = cn_temp
      this.$forceUpdate()
    },

    /**
     * @description: 获取值
     * @param {*} call
     * @return {*}
     */
    handleOk(call) {
      // if (!this.model.ConditionDescription) {
      //   call(null)
      // } else {
      // 触发表单验证
      this.$refs.form.validate((err, values) => {
        if (err) {
          this.returnTableValidator()
          if (this.returnTableValidator()) {
            this.$message.warning('未达标品种条件表达式不正确，请检查数据是否有空值！')
            return
          }
          if (this.returnExpressionValidator()) {
            this.$message.warning('未达标品种条件表达式不正确，请检查括号是否正确匹配！')
            return
          }
          let EditJsonData = JSON.stringify(this.tableData)
          // let Description = document.getElementById("ConditionDescription").innerText;
          let Description = this.model.ConditionDescription
          let Expression = document.getElementById('ConditionExpression').innerText
          call &&
            call({
              EditJsonData,
              Description,
              Expression,
              List: this.tableData
            })
        }
      })
      // }
    },
    /**
     * @description: 验证 表格数据是否有空值
     * @return {*}
     */
    returnTableValidator() {
      let validator = []
      this.tableData.map((item, index) => {
        if (!item.StatisticsType || !item.CompareMethodType || !item.CompareValue) {
          validator.push(true)
        } else {
          validator.push(false)
        }
      })
      return validator.findIndex(v => v == true) > -1 ? true : false
    },
    /**
     * @description: 验证 表达式是否正确
     * @return {*}
     */
    returnExpressionValidator() {
      let validator = false
      let ConditionExpression = document.getElementById('ConditionExpression').innerText
      if (this.getStrCount(ConditionExpression, '(') != this.getStrCount(ConditionExpression, ')')) {
        validator = true
      }
      return validator
    },
    // 字符串内 某字符 个数
    getStrCount(scrstr, armstr) {
      //scrstr 源字符串 armstr 特殊字符
      var count = 0
      while (scrstr.indexOf(armstr) != -1) {
        scrstr = scrstr.replace(armstr, '')
        count++
      }
      return count
    }
  }
}
</script>
