<!--
 * @Author: HL
 * @Date: 2021-07-24 15:25:28
 * @LastEditTime: 2024-08-22 10:52:03
 * @LastEditors: alpin
 * @Description: 输入框
 * @FilePath: \YMallScmApps\src\components\yyl\components\SingleInputSearchView.vue
-->
<template>
  <div class="search-box" ref="shows" style="position:relative;">
    <a-input v-model="inputValue" @click="(dataList || []).length?(noShowList?'':isListShow = true):''" @change="handleChange" :placeholder="placeholder" :disabled="disabled" :style="{ width: width }" allowClear>
      <a-icon v-if="showSearchIcon" slot="suffix" type="search" style="color: rgba(0,0,0,.45);cursor: pointer;" @click="searchInput" />
    </a-input>
    <ul class="scroll-style" ref="container" @scroll="handleScroll" :class="isAbsolute?'ul-absolute':''" v-if="isListShow">
      <a-spin :spinning="fetching" tip="加载中">
        <li v-for="(item, key) in (dataList || [])" :key="key" @click="handleInput(item.ItemValue,item.ItemName)" :class="inputId == item.ItemValue?'list-actived':'list-li'">{{ item.ItemName }}</li>
      </a-spin>
    </ul>
  </div>

</template>

<script>
import { getAction, postAction } from "@/api/manage";
import debounce from 'lodash/debounce'
export default {
  name: "SingleInputSearchView",
  props: {
    //http请求类型
    httpType: {
      type: String,
      default: "GET",
    },
    //请求参数
    httpParams: {
      type: Object,
      default() {
        return {
          PageIndex: 1,
          PageSize: 20,
        };
      },
    },
    Url: {
      type: String,
      default: "",
    },
    dataKey: {
      type: Object,
      default() {
        //name 显示内容的字段名称  value 实际的值的字段名称  nameList显示的其他字段名称（比如显示 名称/编码 这种情况就把Code放在nameList中）
        return { name: "Name", value: "Id", nameList: [] };
      },
    },
    httpHead: {
      type: String,
      default: "",
    },
    placeholder: String, //提示语
    disabled: Boolean, //是否禁用
    value: {
      Type: String,
      Default: "",
    },
    //选中值Id
    name: {
      Type: String,
      Default: "",
    },
    //选中值文字
    keyWord: {
      Type: String,
      Default: "KeyWord",
    },
    //选中值文字
    width: { type: String, default: "120px" }, //宽度
    // 样式是否绝对定位
    isAbsolute: {
      type: Boolean,
      default: false
    },
    // 是否分页模式
    paging: {
      type: Boolean,
      default: false
    },
    isAuto: {
      type: Boolean,
      default: true
    },
    // 是否获取加载的数据
    isGetData: {
      type: Boolean,
      default: false
    },
    // 是否本地模糊搜索
    isLocalSearch: {
      type: Boolean,
      default: false
    },
    // 是否显示列表
    noShowList: {
      type: Boolean,
      default: false
    },
    showSearchIcon: {
      type: Boolean,
      default: false
    }
  },
  data() {
    this.getData = debounce(this.getData, 800)
    return {
      fetching: false,
      isListShow: false,
      dataList: [],
      originDataList: [],//源dataList数据
      inputValue: '',
      isAll: false,
      inputId: null,
    };
  },
  created() {
    this.clickOrtherDiv()
  },

  mounted() {
    this.inputValue = this.name;
    this.inputId = this.value;
    if (this.isAuto) {
      this.getData(this.inputValue);
    }
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  watch: {
    name(newName, oldName) {
      this.inputValue = newName
    },
    value(newName) {
      this.inputId = newName;
    }
  },
  methods: {
    handleChange(e) {
      if (this.noShowList) {
        return
      }
      let value = e.target.value
      if (!value) {
        this.isListShow = false
        this.isAll = false
        if (this.paging) {
          this.httpParams.pageIndex = 1
          this.httpParams.Name = '';
          this.httpParams[this.keyWord] = '';
        }
        this.$emit('clear')
        this.getData(this.inputValue)
        return
      }
      if (this.paging) {
        this.httpParams.pageIndex = 1
      }
      // 是否本地模糊搜索
      if (this.isLocalSearch) {
        this.dataList = this.originDataList.filter(item => item.ItemName.includes(this.inputValue));
      } else {
        // this.$emit('clear')
        this.getData(this.inputValue)
      }
      if (this.noShowList) {
        this.isListShow = false
      } else {
        this.isListShow = true;
      }
    },
    searchInput() {
      this.$emit('search', this.inputValue)
    },
    handleScroll() {
      if (!this.paging) {
        return
      }
      const container = this.$refs.container
      const scrollHeight = container.scrollHeight
      const scrollTop = container.scrollTop
      const clientHeight = container.clientHeight
      // console.log(scrollHeight, scrollTop, clientHeight)
      if (scrollTop + clientHeight >= scrollHeight && !this.fetching && this.dataList.length > 0) {
        console.log('触底')
        this.loadMore()
      }
    },
    loadMore() {
      if (this.isAll) {
        return
      }
      this.httpParams.pageIndex++
      this.getData(this.inputValue)
    },
    // 点击div以外区域隐藏
    clickOrtherDiv() {
      document.addEventListener("click", e => {
        if (this.$refs.shows) {
          let self = this.$refs.shows.contains(e.target);
          if (!self) {
            this.isListShow = false;
          }
        }
      })
    },
    getData(keyWord = "", callback) {
      let that = this;
      if (!this.Url) return;
      let obj = null;
      that.httpParams.Name = keyWord;
      that.httpParams[this.keyWord] = keyWord;
      // that.httpParams.pageIndex = 1
      // that.httpParams.pageSize = 50
      that.fetching = true;
      if (that.httpType == "GET") {
        obj = getAction(that.Url, that.httpParams, that.httpHead);
      } else if (that.httpType == "POST") {
        obj = postAction(that.Url, that.httpParams, that.httpHead);
      }
      if (!obj) {
        that.fetching = false;
        return;
      }
      obj.then((res) => {
        if (res.IsSuccess) {
          const data = res.Data;
          let list = that.parseDataList(data);
          if (this.paging) {
            // 分页模式
            if (that.httpParams.pageIndex == 1) {
              that.dataList = list
            } else {
              if (list.length < that.httpParams.pageSize) {
                that.isAll = true
                that.fetching = false;
                return
              }
              that.dataList = that.dataList.concat(list)
            }
          } else {
            // 普通模式
            that.dataList = [
              {
                ItemName: "请选择",
                ItemValue: "",
                Item: null,
              },
            ].concat(list);
          }
          callback && callback();
          if (this.isGetData) {
            this.$emit('getData', res.Data)
          }
        } else {
          that.dataList = [];
        }
        // 源数据赋值
        if (that.isLocalSearch) {
          that.originDataList = JSON.parse(JSON.stringify(that.dataList)) || []
        }

        // console.log('datalist = ', that.dataList)
        that.fetching = false;
      });
    },
    parseDataList(list) {
      let arrayList = [];
      list.map((item) => {
        let otherName = "";
        if (this.dataKey["nameList"] && this.dataKey["nameList"].length > 0) {
          this.dataKey["nameList"].forEach((x) => {
            otherName += item[x] ? item[x] + "/" : "";
          });
        }
        if (this.dataKey["name"].indexOf('.') > 0) {
          // 数据双层嵌套
          let itemNameArray = this.dataKey["name"].split('.') || []
          let itemValueArray = this.dataKey["value"].split('.') || []
          arrayList.push({
            ItemName:
              item[itemNameArray[0]][itemNameArray[1]] +
              (otherName
                ? "/" + otherName.substring(0, otherName.length - 1)
                : ""),
            ItemValue: item[itemValueArray[0]][itemValueArray[1]],
            Item: item,
          });
        } else {
          // 单层数据展示
          arrayList.push({
            ItemName:
              item[this.dataKey["name"]] +
              (otherName
                ? "/" + otherName.substring(0, otherName.length - 1)
                : ""),
            ItemValue: item[this.dataKey["value"]],
            Item: item,
          });
        }
      });
      return arrayList;
    },
    clear() {
      this.inputValue = ''
    },
    handleInput(v, t) {
      let val, txt, item;
      if (v) {
        val = v;
        txt = t || ""; //获取文本
        item = this.getItem(val);
      } else {
        val = "";
        txt = "";
        item = null;
      }
      this.inputId = v;
      this.inputValue = t;
      this.isListShow = false
      this.$emit("change", val, txt, item);
      this.getData();
    },
    getItem(val) {
      let item = null;
      if (this.dataList) {
        this.dataList.forEach((x) => {
          if (val == x.ItemValue) {
            item = x.Item;
          }
        });
      }
      return item;
    },
    setCurrentDictOptions(list) {
      this.dataList = list;
    },
    getCurrentDictOptions() {
      return this.dataList;
    },
  },
};
</script>

<style scoped>
ul {
  padding: 0;
  border: 1px solid #f5f5f5;
  border-radius: 8px;
  max-height: 300px;
  /* position: absolute; */
  top: 40px;
  left: 0;
  right: 0;
  z-index: 1;
  background: #fff;
  box-shadow: 1px 5px 5px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}
/* .search-box {
  z-index: 999;
} */
.ul-absolute {
  position: absolute;
  z-index: 999;
}
.list-li {
  list-style: none;
  padding: 8px 20px;
  line-height: initial;
  cursor: pointer;
}
.list-li:hover {
  background: #e6f7ff;
}
.list-actived {
  list-style: none;
  padding: 8px 20px;
  cursor: pointer;
  background: #bae7ff;
  line-height: initial;
}
</style>
