<!--技术中心-普通调用接口单选带搜索view可手动输入 保留手动输入值-->
<template>
  <a-select
    :showSearch="showSearch"
    :placeholder="placeholder"
    :disabled="disabled"
    :value="newSoftName"
    :style="{ width: width }"
    :allowClear="true"
    :filter-option="IsInput?filterOption :false"
    :not-found-content="fetching ? undefined : null"
    @search="IsInput?false : handleSearch"
    @change="handleInput"
  >
    <a-spin
      v-if="fetching"
      slot="notFoundContent"
      size="small"
    />
    <template v-for="(item, key) in dataList">
      <a-select-option
        v-if="isVisible(item.ItemValue) == -1"
        :key="key"
        :value="item.ItemValue"
        :disabled="item.IsValid === false"
      >
        <span
          style="display: inline-block; width: 100%;"
          :title="item.ItemName ? item.ItemName : item.ItemDescription"
        >{{ item.ItemName }}</span>
      </a-select-option>
    </template>
  </a-select>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'SingleChoiceSearchAndInputView',
  props: {
    //http请求类型
    httpType: {
      type: String,
      default: 'GET'
    },
    //请求参数
    httpParams: {
      type: Object,
      default() {
        return {}
      }
    },
    Url: {
      type: String,
      default: ''
    },
    dataKey: {
      type: Object,
      default() {
        //name 显示内容的字段名称  value 实际的值的字段名称  nameList显示的其他字段名称（比如显示 名称/编码 这种情况就把Code放在nameList中）
        return { name: 'Name', value: 'Id', nameList: [] }
      }
    },
    httpHead: {
      type: String,
      default: 'P36001'
    },
    /**
     * 提示语句
     */
    placeholder: {
      type: String,
      default: '请选择'
    },
    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false
    }, //是否禁用
    value: {
      type: String,
      default: ''
    },
    /***
     * 是否支持搜索，默认支持
     */
    showSearch: {
      type: Boolean,
      default: true
    },
    /**
     * 搜索参数关键字
     */
    keyWord: {
      type: String,
      default: 'keyWord'
    },

    width: { type: String, default: '120px' }, //宽度

    /**
     * 过滤不显示 选项值
     **/
    notOption: {
      type: Array,
      default: () => []
    },
    // 直接设置选项值
    defaultDataList: {
      type: Array,
      default: () => []
    },
    //设置追加首个的值
    sortFirstName: {
      type: String,
      default: ''
    },
    IsInput: {
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  data() {
    // this.getData = debounce(this.getData, 800)
    return {
      fetching: false,
      newSoftName: this.value || '',
      dataList: [],
      allDataList: []
    }
  },
  created() {},
  mounted() {
    // console.log(this.value)

    if (this.defaultDataList.length) {
      let list = this.parseDataList(this.defaultDataList)
      this.dataList = [
        {
          ItemName: this.placeholder || '请选择',
          ItemValue: '',
          Item: null
        }
      ].concat(list)
    } else {
      this.getData()
    }
  },
  methods: {
    handleSearch(value) {
      this.getData(value)
    },
    getData(value = '') {
      let that = this
      if (!this.Url) return
      let obj = null

      that.httpParams[this.keyWord] = value
      that.httpParams.PageIndex = 1
      that.httpParams.PageSize = 100

      if (!value && this.allDataList && this.allDataList.length > 0) {
        this.dataList = [].concat(this.allDataList)
        return
      }
      that.fetching = true
      if (that.httpType == 'GET') {
        obj = getAction(that.Url, that.httpParams, that.httpHead)
      } else if (that.httpType == 'POST') {
        obj = postAction(that.Url, that.httpParams, that.httpHead)
      }
      if (!obj) {
        that.fetching = false
        return
      }
      obj
        .then(res => {
          if (res.IsSuccess) {
            const data = res.Data
            let list = that.parseDataList(data)
            // 设置追加首个的值
            if (this.sortFirstName) {
              list.unshift({
                ItemName: this.sortFirstName,
                ItemValue: this.sortFirstName,
                Item: null
              })
            }
            that.dataList = [
              {
                ItemName: that.placeholder || '请选择',
                ItemValue: '',
                Item: null
              }
            ].concat(list)

            if (!value) {
              this.allDataList = [
                {
                  ItemName: that.placeholder || '请选择',
                  ItemValue: '',
                  Item: null
                }
              ].concat(list)
            }
          } else {
            that.dataList = []
          }
          this.$emit('dataLoaded', res.Data)
          // console.log('datalist = ', that.dataList)
        })
        .finally(() => {
          that.fetching = false
        })
    },
    parseDataList(list) {
      let arrayList = []
      list.map(item => {
        let otherName = ''
        if (this.dataKey['nameList'] && this.dataKey['nameList'].length > 0) {
          this.dataKey['nameList'].forEach(x => {
            otherName += item[x] ? item[x] + '/' : ''
          })
        }
        arrayList.push({
          ItemName: item[this.dataKey['name']] + (otherName ? '/' + otherName.substring(0, otherName.length - 1) : ''),
          ItemValue: item[this.dataKey['value']],
          Item: item,
          IsValid: item.IsValid
        })
      })
      return arrayList
    },
    handleInput(e, opt) {
      let val, txt, item
      this.fetching = false
      if (e) {
        val = e
        txt = opt.componentOptions.children[0].data ? opt.componentOptions.children[0].data.attrs.title : '' //获取文本
        item = this.getItem(val)
        this.getData()
      } else {
        val = ''
        txt = ''
        item = null
        this.getData()
      }
      this.$set(this, 'newSoftName', val)
      // console.log(val, txt, item)
      this.$emit('change', val, txt, item)
    },
    getItem(val) {
      let item = null
      if (this.dataList) {
        this.dataList.forEach(x => {
          if (val == x.ItemValue) {
            item = x.Item
          }
        })
      }
      return item
    },
    setCurrentDictOptions(list) {
      this.dataList = list
    },
    getCurrentDictOptions() {
      return this.dataList
    },
    isVisible(value) {
      let index = -1
      if (this.notOption.length) {
        index = this.notOption.findIndex(t => t == value)
      }
      return index
    },
    filterOption(input, option) {
      // console.log(option.componentOptions.children[0])
      // 重要的一步，可以console.log(input)看下输入的是啥
      this.newSoftName = input // 在此前是可以手动输入，但鼠标离开后，输入的内容不能在输入框内保存，将input的值给到a-select标签的v-model绑定的newSoftName即可实现将手动输入的值赋值给input
      if (option.componentOptions.children[0].text)
        return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }
  }
}
</script>

<style scoped></style>
