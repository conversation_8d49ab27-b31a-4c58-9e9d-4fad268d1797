<!--
 * @Description: 带搜索的 select 组件（接口调用）
 * @Version: 1.0
 * @Author: LP
 * @Date: 2023-07-06 14:17:02
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-03-26 11:25:05
-->

<template>
  <a-select 
    :showSearch="showSearch" 
    :placeholder="placeholder" 
    :disabled="disabled" 
    :value="value" 
    :style="{ width: width }" 
    :allowClear="true" 
    :filter-option="false" 
    :not-found-content="fetching ? undefined : null" 
    @search="onSearch" 
    @change="handleInput"
    @focus="onFocus"
  >
    <a-spin v-if="fetching" slot="notFoundContent" size="small" />
    <template v-for="(item, key) in dataList">
      <a-select-option 
        v-if="isVisible(item.ItemValue) == -1" 
        :key="key" 
        :value="item.ItemValue" 
        :disabled="isOptionDisabled ? isOptionDisabled(item) : (item.IsValid === false)">
        <span style="display: inline-block; width: 100%" :title="item.ItemName ? item.ItemName : item.ItemDescription">{{ item.ItemName }}</span>
      </a-select-option>
    </template>
  </a-select>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'SingleChoiceSearchView',
  props: {
    //http请求类型
    httpType: {
      type: String,
      default: 'GET',
    },
    //请求参数
    httpParams: {
      type: Object,
      default() {
        return {}
      },
    },
    Url: {
      type: String,
      default: '',
    },
    dataKey: {
      type: Object,
      default() {
        //name 显示内容的字段名称  value 实际的值的字段名称  nameList显示的其他字段名称（比如显示 名称/编码 这种情况就把Code放在nameList中）
        return { name: 'Name', value: 'Id', nameList: [] }
      },
    },
    httpHead: {
      type: String,
      default: '',
    },
    /**
     * 提示语句
     */
    placeholder: {
      type: String,
      default: '请选择',
    },
    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    name: {
      type: String,
      default: '',
    },
    value: {
      type: String,
      default: undefined,
    },
    /***
     * 是否支持搜索，默认支持
     */
    showSearch: {
      type: Boolean,
      default: true,
    },
    /**
     * 搜索参数关键字
     */
    keyWord: {
      type: String,
      default: 'keyWord',
    },

    width: { type: String, default: '120px' }, //宽度

    /**
     * 过滤不显示 选项值
     **/
    notOption: {
      type: Array,
      default: () => [],
    },
    // 直接设置选项值
    defaultDataList: {
      type: Array,
      default: () => [],
    },
    //设置追加首个的值
    sortFirstName: {
      type: String,
      default: '',
    },
    /**
     * 自定义选项禁用逻辑
     */
    isOptionDisabled: {
      type: Function,
      default: null
    },
    reloadDataOnFocus: { // 是否每次聚焦都重新请求数据
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  watch: {
    value: {
      handler(val, old) {
        if (val && old != val) {
          this.loadDataAfter()
        }
      },
    },
  },
  data() {
    return {
      fetching: false,
      dataList: [],
      allDataList: [],
      seletcedItem: null,
    }
  },
  created() {
    this.onSearch = this.$lpTools.debounce(this.handleSearch, 800)
  },
  mounted() {
    if (this.defaultDataList.length) {
      let list = this.parseDataList(this.defaultDataList)
      // this.dataList = [
      //   {
      //     ItemName: this.placeholder || '请选择',
      //     ItemValue: '',
      //     Item: null,
      //   },
      // ].concat(list)

      this.dataList = [...list]
    } else {
      // 针对默认传参处理
      !this.reloadDataOnFocus && this.getData(this.httpParams[this.keyWord] || '')
    }
  },
  methods: {
    onSearch() { },
    onFocus() {
      if (!this.reloadDataOnFocus) return
      this.getData('', null, true)
    },
     /**
     * 搜索事件
     * @param value 搜索关键字
     * @param params 额外参数
     * @param callback 用于处理返回数据
     * @param isReload 是否重新请求数据
     */
     handleSearch(value, params = null, callback = null, isReload = false) {
      if (params) {
        Object.keys(params).forEach((key) => {
          this.httpParams[key] = params[key]
        })
      }
      this.getData(value, callback, isReload)
    },
    getData(value = '', callback, isLoad = false) {
      let that = this
      if (!this.Url) return
      let obj = null

      that.httpParams[this.keyWord] = value
      if (!that.httpParams.PageIndex) {
        that.httpParams.PageIndex = 1
      }
      //|| that.httpParams.PageSize < 100
      if (!that.httpParams.PageSize) {
        that.httpParams.PageSize = 100
      }

      if (!value && this.allDataList && this.allDataList.length > 0 && !isLoad) {
        this.dataList = [].concat(this.allDataList)
        that.loadDataAfter()
        return
      }
      that.fetching = true
      if (that.httpType == 'GET') {
        obj = getAction(that.Url, that.httpParams, that.httpHead)
      } else if (that.httpType == 'POST') {
        obj = postAction(that.Url, that.httpParams, that.httpHead)
      }
      if (!obj) {
        that.fetching = false
        return
      }
      obj
        .then((res) => {
          if (res.IsSuccess) {
            const data = res.Data
            let list = that.parseDataList(data)
            if (callback) {
              callback(list)
            } else {
              // 设置追加首个的值
              if (this.sortFirstName) {
                list.unshift({
                  ItemName: this.sortFirstName,
                  ItemValue: this.sortFirstName,
                  Item: null,
                })
              }
              // that.dataList = [
              //   {
              //     ItemName: that.placeholder || '请选择',
              //     ItemValue: '',
              //     Item: null,
              //   },
              // ].concat(list)
              that.dataList = [...list]

              //非搜索的情况记录数据，直接使用避免重复调用接口
              if (!value) {
                // this.allDataList = [
                //   {
                //     ItemName: that.placeholder || '请选择',
                //     ItemValue: '',
                //     Item: null,
                //   },
                // ].concat(list)
                this.allDataList = [...list]
                that.loadDataAfter()
              }
              that.$emit('dataLoaded', res.Data)
            }
          } else {
            if (!callback) {
              that.dataList = []
            }
          }
        })
        .finally(() => {
          that.fetching = false
        })
    },
    loadDataAfter() {
      if (this.value && this.dataList.length > 0) {
        const index = this.dataList.findIndex((x) => x.ItemValue == this.value)
        //如果当前数据中没有已有数据，则查询一便
        if (index == -1) {
          //传入了name
          if (this.name) {
            this.dataList.push({
              ItemName: this.name,
              ItemValue: this.value,
            })
          } else {
            if (this.seletcedItem) {
              const idx = this.dataList.findIndex((x) => x.ItemValue == this.seletcedItem.ItemValue)
              if (idx == -1) {
                this.dataList.push(this.seletcedItem)
              }
            }
          }
          // this.$forceUpdate()
        }
      }
      if (!this.value && this.seletcedItem) {
        const idx = this.dataList.findIndex((x) => x.ItemValue == this.seletcedItem.ItemValue)
        if (idx == -1) {
          this.dataList.push(this.seletcedItem)
        }
      }
    },
    parseDataList(list) {
      let arrayList = []
      list.map((item) => {
        let otherName = ''
        if (this.dataKey['nameList'] && this.dataKey['nameList'].length > 0) {
          this.dataKey['nameList'].forEach((x) => {
            otherName += item[x] ? item[x] + '/' : ''
          })
        }
        arrayList.push({
          ItemName: item[this.dataKey['name']] + (otherName ? '/' + otherName.substring(0, otherName.length - 1) : ''),
          ItemValue: item[this.dataKey['value']],
          Item: item,
          IsValid: item.IsValid,
        })
      })
      return arrayList
    },
    handleInput(e, opt) {
      let val, txt, item
      this.fetching = false
      if (e) {
        val = e
        txt = opt.componentOptions.children[0].data ? opt.componentOptions.children[0].data.attrs.title : '' //获取文本
        item = this.getItem(val)
        this.seletcedItem = {
          ItemName: txt,
          ItemValue: val,
        }
        this.getData()
      } else {
        val = ''
        txt = ''
        item = null
        this.seletcedItem = null
        this.getData()
      }
      this.$emit('change', val, txt, item)
    },
    getItem(val) {
      let item = null
      if (this.dataList) {
        this.dataList.forEach((x) => {
          if (val == x.ItemValue) {
            item = x.Item
          }
        })
      }
      return item
    },
    setCurrentDictOptions(list) {
      this.dataList = list
    },
    getCurrentDictOptions() {
      return this.dataList
    },
    isVisible(value) {
      let index = -1
      if (this.notOption.length) {
        index = this.notOption.findIndex((t) => t == value)
      }
      return index
    },
  },
}
</script>

<style scoped></style>
