<template>
  <div>
    <a-popover :placement="placement" arrowPointAtCenter autoAdjustOverflow @visibleChange="resetImgRotate" v-if="!isPdf">
      <template slot="content">
        <div class="imgDiv">
          <img v-if="src.indexOf('.pdf' || '.PDF') > -1" :id="`img_${src}`" src="@/assets/image/pdficon.png" alt="" class="img-control-view" />
          <img v-else :id="`img_${src}`" :src="src" class="img-control-view" />
          <!--  <div 
            style="width: 100%;"
            :id="`img_${showId}`"
            v-if="imgVisible"
          ></div> -->
        </div>
        <div class="tools">
          <span @click="picture_rotate">
            <a-icon type="redo" /> 旋转</span>
          <span style="margin-left: 20px;" @click="showImg(src)">
            <a-icon type="file-image" /> 大图 </span>
        </div>
      </template>
      <div class="pdf" v-if="src.indexOf('.pdf' || '.PDF') > -1">
        <img src="@/assets/image/pdficon.png" :style="{
          width: width + 'px',
          height: height + 'px',
          maxHeight: height + 'px'
        }" @click="showImg(src)" />
        <div class="eye" @click="showImg(src)">
          <a-icon style="font-size:26px;" type="eye" />
        </div>
      </div>
      
      <img v-else :src="getThumbnail(src)" :style="{
          width: width + 'px',
          height: height + 'px',
          maxHeight: height + 'px'
        }" @click="showImg(src)" />
    </a-popover>
    <div v-else :style="{ height: (height || 200 )+ 'px', position: 'relative' }">
      <iframe :src="curPdf" style="width: 100%; height: 100%;" :toolbar="0" scrolling="no" security="restricted" frameborder="no" border="0" v-if="curPdf"></iframe>
      <!-- <div
        class="lgdiv"
        @click.stop="modalVisible = true"
        v-if="PdfUrls.length"
      >
        查看更多
      </div> -->
      <div class="lgbtn" @click.stop="handleLg(curPdf)" v-if="curPdf">
        查看大图
      </div>
      <div class="qdiv" v-if="curPdfIndex > 0" @click.stop="onNextClick(1)">
        上一页
      </div>
      <div class="hdiv" v-if="curPdfIndex < PdfUrls.length - 1 && PdfUrls.length > 1" @click.stop="onNextClick(2)">
        下一页
      </div>
      <!-- <div class="qdiv" v-if="curPdfIndex > 0" @click.stop="onNextClick(1)">
        上一页
      </div>
      <div
        class="hdiv"
        v-if="curPdfIndex < PdfUrls.length - 1 && PdfUrls.length > 1"
        @click.stop="onNextClick(2)"
      >
        下一页
      </div> -->
    </div>
    <!--  -->
    <a-modal :title="''" :width="1200" :visible="modalVisible" @cancel="handleCancel" @close="handleCancel" :maskClosable="false" :destroyOnClose="true" :footer="null">
      <div :style="{ height: '700px', padding: '20px', position: 'relative' }">
        <iframe :src="curPdf" style="width: 100%; height: 100%;" :toolbar="0" scrolling="no" security="restricted"></iframe>
        <div class="qdiv" v-if="curPdfIndex > 0" @click.stop="onNextClick(1)">
          上一页
        </div>
        <div class="hdiv" v-if="curPdfIndex < PdfUrls.length - 1 && PdfUrls.length > 1" @click.stop="onNextClick(2)">
          下一页
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'ImageControl',
  props: {
    index: { type: Number, default: 0 },
    src: {
      type: String,
      default: require('@/assets/image/tempimg.svg'),
      required: true
    }, //填了默认图片是防止报错
    width: { type: Number, required: false }, //预览图高
    height: { type: Number, required: false }, //预览图高
    placement: { type: String, default: 'right', required: false }, //弹出方向
    // showId: { type: [String, Number], default: 0 }, // 预览大图的当前数据源id，保证imgId唯一性
    isPdf: { type: Boolean, default: false },
    PdfUrls: { type: Array, default: () => [] }
  },
  data() {
    return {
      site: window._CONFIG['site'],
      angle: 0,
      curPdfIndex: 0, //当前pdf的index，默认第一个
      curPdf: this.src,
      modalVisible: false,
      imgVisible: false
    }
  },
  methods: {
    onNextClick(type) {
      if (type == 1) {
        if (this.curPdfIndex > 0) {
          this.curPdfIndex = this.curPdfIndex - 1
        }
      } else if (type == 2) {
        if (this.curPdfIndex < this.PdfUrls.length - 1) {
          this.curPdfIndex = this.curPdfIndex + 1
        }
      }
      // console.log(this.curPdfIndex);
      this.curPdf = this.PdfUrls[this.curPdfIndex]
    },
    getThumbnail(src) {
      let newSrc = src
      if (src && src.indexOf(this.site) > 0) {
        newSrc = src.replace('.jpg', '_s.jpg').replace('.png', '_s.png')
      }
      return newSrc
    },
    showImg(src) {
      let a = document.createElement('a')
      a.href = src
      a.target = '_blank'
      a.click()
    },
    /**
     * 图片旋转
     */
    picture_rotate({ isReset = false }) {
      let newAngle = this.angle
      var el = document.getElementById('img_' + this.src)
      if (isReset) {
        newAngle = 0
      } else {
        newAngle += 90
      }
      // el.setAttribute("style", `transform:rotate(${newAngle}deg)`);
      el.style.transform = `rotate(${newAngle}deg)`
      /* if((newAngle / 90) % 2 == 0 ){
        el.style.width = '100%'
        el.style.height = '350px'
        el.style.top = '50%'
        el.style.marginTop = '-150px'

      }else{
        el.style.width = 'calc(550px / 1.5)'
        el.style.height = '100%'
        el.style.top = 0
        el.style.marginTop = 0
      } */
      this.angle = newAngle
    },
    // 预览关闭时，重置图片旋转角度
    resetImgRotate(visible) {
      this.imgVisible = visible
      // console.log(visible);
      if (!visible) {
        this.picture_rotate({ isReset: true })
      } else {
        /* this.$nextTick(()=>{
          setTimeout(()=>{
            document.getElementById("img_" + this.showId).style.backgroundImage = `url(${this.src})`
          },300)

        }) */
      }
    },
    // 关闭
    handleCancel() {
      this.close()
    },
    close() {
      this.$emit('close')
      this.modalVisible = false
    },
    // 查看大图pdf
    handleLg(item) {
      window.open(item)
    }
  }
}
</script>

<style scoped lang="less">
.imgDiv {
  // min-width: 500px;
  // min-height: 350px;
  // line-height: 150px;
  text-align: center;
  overflow: hidden;
  // vertical-align: middle;
  // padding: 50px;
  width: 600px;
  height: 550px;
  line-height: 550px;
  position: relative;
  .img-control-view {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
  div {
    // width: 100%;
    // height: 100%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 350px;
    margin: 0 auto; /*水平居中*/
    position: relative;
    top: 50%; /*偏移*/
    margin-top: -150px;
  }
}
.pdf {
  position: relative;
}
.eye {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0;
  background: #f1eeee;
  width: 90%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 2px;
  display: none;
}
.pdf:hover .eye {
  display: flex;
}

.tools {
  padding-top: 10px;

  > span {
    cursor: pointer;
  }
}

/deep/.ant-popover-inner {
  background-color: rgb(250, 250, 250, 0.1);
}

.lgbtn {
  position: absolute;
  right: 140px;
  top: 13px;
  background: #1890ff;
  color: #fff;
  padding: 3px 10px;
  border-radius: 20px;
  cursor: pointer;
  border: 2px solid #1890ff;
}
.lgdiv {
  position: absolute;
  right: 20px;
  bottom: 5%;
  color: #1890ff;
  border: 2px solid #1890ff;
  padding: 3px 10px;
  border-radius: 20px;
  cursor: pointer;
  &:hover {
    background: #1890ff;
    color: #fff;
  }
}
.qdiv {
  position: absolute;
  right: 316px;
  top: 13px;
  color: #1890ff;
  border: 2px solid #1890ff;
  padding: 3px 10px;
  border-radius: 20px;
  cursor: pointer;
  &:hover {
    background: #1890ff;
    color: #fff;
  }
}
.hdiv {
  position: absolute;
  right: 235px;
  top: 13px;
  color: #1890ff;
  border: 2px solid #1890ff;
  padding: 3px 10px;
  border-radius: 20px;
  cursor: pointer;
  &:hover {
    background: #1890ff;
    color: #fff;
  }
}
</style>
