<!-- 省市区 -->
<template>
  <a-modal :title="title" :width="500" :visible="visible" @cancel="handleCancel" @close="handleCancel" :footer="isInfo ? null : undefined" :maskClosable="false" cancelText="取消">
    <a-spin :spinning="loading">
      <a-tree ref="AreaTreeRef" checkable v-model="selectedRowKeysArray" :replaceFields="replaceFields" :expandedKeys.sync="expandedKeys" :checkStrictly="checkStrictly" @expand="expandHandle" @check="checkHandle" :tree-data="dataSource" />
    </a-spin>
    <a-row :style="{ textAlign: 'right' }" slot="footer">
      <a-button :style="{ marginRight: '8px' }" :loading="confirmLoading" @click="handleOk" type="primary">确定</a-button>
      <a-button @click="handleCancel"> 取消 </a-button>
    </a-row>
  </a-modal>
</template>

<script>
import { postAction, getAction } from '@/api/manage'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'SelectAreaTree',
  props: {
    /**
     * 节点前添加 Checkbox情况下 false关联 true不关联 父子节点选中状态是否关联
     */
    checkStrictly: {
      type: Boolean,
      default: false
    },
    // 1 只显示省份 2 只显示省市 3显示省市区 不传显示全部
    level: {
      type: Number,
      default: null
    },
    // 是否显示全国
    showCountry: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '选择区域'
    },
    // 是否过滤保存参数
    isFilterParam: {
      type: Boolean,
      default: true
    },
    // 过滤全国 onlyCountry 直接返回全国 onlyProvince 返回省份
    filterCountry: {
      type: String,
      default: 'onlyCountry'
    },
    // 详情模式
    isInfo: {
      type: Boolean,
      default: false
    },
    // 全国默认覆盖值
    coverCountryInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    JEllipsis
  },
  data() {
    return {
      httpHead: 'P31100',
      dataSource: [],
      selectedRowKeysArray: [],
      expandedKeys: [],
      checkedNodes: [],
      loading: false,
      confirmLoading: false,
      visible: false,
      countryData: [
        {
          AliasName: "",
          Children: [],
          Code: "10",
          Name: "全国",
          FullName: "全国",
          Id: "1000000000000000",
          Level: 0,
        }
      ],
      allProvince: [],//所有的省份
      allCity: [],//所有的市区
      halfCheckedKeys: [],
      paramsKey: [],
      replaceFields: { children: 'Children', title: 'Name', key: 'Code' },
      url: {
        list: '/{v}/AdministrativeArea/ListByCode',//获取地区逐级数据
        listAll: '/{v}/AdministrativeArea/GetAllAdministrativeAreaList',//获取地区所有数据
        listLevelArea: '/{v}/AdministrativeArea/AllList',//根据level获取地区数据 1获取所有省份 2获取所有市 3获取所有县
      }
    }
  },
  created() {

  },
  computed: {},
  methods: {
    show(areaList) {
      // console.log('显示', areaList)
      if (areaList && areaList.length > 0) {
        // 判断是否全国 兼容code是0和10回显
        if (areaList.length == 1 && (areaList[0].Code == '0' || areaList[0].Code == '10')) {
          areaList[0].Code = '10'
          areaList[0].AreaCode = '10'
        }
        this.selectedRowKeysArray = areaList.map(it => it.Code)
        this.checkedNodes = areaList || []
      }
      this.getAllTreeData()
      this.confirmLoading = false
      this.visible = true
    },
    // 获取所有的数据，先从session里面取，没取到再调接口
    getAllTreeData() {
      let params = {}
      let url = ''
      switch (this.level) {
        case 1: {
          params = {
            level: 1,
          }
          url = this.url.listLevelArea
          break;
        }
        default: {
          url = this.url.listAll
          break;
        }
      }
      // const sessionData = sessionStorage.getItem('AREA_TREE_DATA')
      // if (sessionData) {
      //   let dataSource = JSON.parse(sessionData)
      //   this.dataSource = this.setDataSource(dataSource) || []
      //   return
      // }
      this.loading = true
      getAction(url, params, 'P31100')
        .then(res => {
          if (res.IsSuccess) {
            this.dataSource = this.setDataSource(res.Data) || []
            // 默认展开全国
            if (this.showCountry) {
              this.expandedKeys = ['10']
            }
            // sessionStorage.setItem('AREA_TREE_DATA', JSON.stringify(res.Data))
          } else {
            this.$message.warning(res.Msg)
            this.dataSource = []
          }
        })
        .finally(e => {
          this.loading = false
        })
    },
    setDataSource(data) {
      if (!data) {
        data = []
      }
      // 获取所有省份的key
      // console.log('源数据', data)
      if (this.showCountry) {
        if(this.coverCountryInfo && this.coverCountryInfo.Code) {
          this.countryData[0] = {
            ...this.countryData[0],
            ...this.coverCountryInfo
          }
        }
        this.countryData[0].Children = data
        data = this.countryData
      }
      // 处理树形数据
      if (this.level != 1) {
        this.flattenTree(data)
      }
      return data
    },
    flattenTree(tree) {
      let result = {
        allProvince: [],
        allCity: [],
      };
      function traverse(node) {
        if (node.Level == 1) {
          result['allProvince'].push(node.Code);
        } else if (node.Level == 2) {
          result['allCity'].push(node.Code);
        }

        if (node.Children) {
          node.Children.forEach(child => traverse(child));
        }
      }

      tree.forEach(node => traverse(node));
      this.allProvince = result['allProvince'];
      this.allCity = result['allCity'];
      // console.log('处理后的数据', this.allProvince, this.allCity)
      return result;
    },
    // 展开回调
    expandHandle(expandedKeys) {
      this.expandedKeys = expandedKeys
    },
    // 复选框回调
    checkHandle(checkedKeys, e) {
      this.checkedNodes = e.checkedNodes.map(it => {
        return {
          Code: it.data.props.Code,
          CustomerCount: 1,
          AliasName: it.data.props.AliasName,
          Id: it.data.props.Id,
          Name: it.data.props.FullName,
          Level: it.data.props.Level
        }
      })
    },
    setSubData() {
      let chooseProvince = []
      let chooseCity = []
      this.selectedRowKeysArray.map(item => {
        let a = this.allProvince.find(rItem => {
          return item == rItem
        })
        if (a) {
          chooseProvince.push(a)
        }
        let b = this.allCity.find(rItem => {
          return item == rItem
        })
        if (b) {
          chooseCity.push(b)
        }
      })
      // 处理省和市区
      let haveIndexOfData = [] //数据中包含了省份的code 但是不等于该code的数据
      let allChooseArray = chooseProvince.concat(chooseCity)
      if (allChooseArray && allChooseArray.length > 0) {
        allChooseArray.map(item => {
          this.checkedNodes.map((rItem, index) => {
            if (rItem.Code.substring(0, item.length) === item.substring(0, item.length) && rItem.Code !== item) {
              haveIndexOfData.push(rItem)
            }
          })
        })
      }

      // 获取在array1中但不在array2中的对象
      let array1 = this.checkedNodes || []
      let array2 = haveIndexOfData
      let paramsKey = null
      paramsKey = array1.filter(item1 => !array2.find(item2 => item2.Code === item1.Code));
      if (!paramsKey) {
        this.$message.warning('您的操作太频繁了，请稍后再试!')
        return
      }

      return paramsKey
    },
    // 弹窗确定
    handleOk() {
      this.confirmLoading = true
      setTimeout(() => {
        let paramsKey = []
        if (this.level == 1 || !this.isFilterParam) {
          paramsKey = this.checkedNodes || []
        } else {
          paramsKey = this.setSubData() || []
        }
        // return
        let quanGuoData = paramsKey.find(item => {
          return item.Name === '全国'
        })
        if (this.filterCountry == 'onlyProvince' && quanGuoData) {
          paramsKey = paramsKey.filter(item => {
            return item.Name !== '全国'
          })
        } else if (this.filterCountry == 'onlyCountry' && quanGuoData) {
          paramsKey = [quanGuoData]
        }
        console.log('paramsKey', paramsKey, quanGuoData)
        this.$emit('ok', paramsKey)
        this.handleCancel()
        setTimeout(() => {
          this.confirmLoading = false
        })
      }, 50)
    },
    // 弹窗关闭
    handleCancel() {
      this.visible = false
      this.expandedKeys = []
      this.selectedRowKeysArray = []
      this.checkedNodes = []
    }
  }
}
</script>
