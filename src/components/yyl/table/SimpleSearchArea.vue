<template>
  <a-card :bodyStyle="{ padding: '12px 12px 0 12px' }" style="margin-bottom: 12px">
    <div class="table-page-search-wrapper customer-list">
      <a-form layout="inline" ref="searchForm">
        <a-row :gutter="10">
          <!-- type类型
          select 下拉筛选
          input 输入框
          timeInput 日期时间
          selectLink 链接版搜索
          selectInputLink 输入搜索链接版选择框
          selectBaseLink 自由枚举读取数据的选择
          selectTreeLink 树状读取数据的选择 -->
          <div v-for="(item, index) in searchInput" :key="index">
            <a-col :md="queryCol" v-if="isSearchMoreShow ? true : index <= 6">
              <a-form-item :label="item.name" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <!-- 输入框 -->
                <!--例子 { name: '客户名称', type: 'input', value: '', key: 'Name', defaultVal: '', placeholder: '请输入客户名称' } -->
                <a-input v-if="item.type == 'input'" :placeholder="item.placeholder ? item.placeholder : '请输入'" :defaultVal="item.defaultVal" v-model="queryParam[item.key]"></a-input>
                <!-- 选择框 -->
                <!-- 例子 { name: '售后场景',type: 'select',value: '',key: 'AuditState',defaultVal: [{ title: '待审核', value: 1 },{ title: '审核通过', value: 2 },],placeholder: '请选择'}, -->
                <a-select v-if="item.type == 'select'" :placeholder="item.placeholder ? item.placeholder : '请选择'" @change="(val) => handleChange(val, item.key)" v-model="queryParam[item.key]" :disabled="item.disabled">
                  <a-select-option value=""> 请选择 </a-select-option>
                  <a-select-option v-for="(r, rIndex) in item.defaultVal" :key="rIndex" :value="r.value">
                    {{ r.title }}
                  </a-select-option>
                </a-select>
                <!-- 时间选择框 -->
                <!--例子 { name: '创建时间', type: 'timeInput', value: '', key: ['StartTime', 'EndTime'], defaultVal: ['', ''], placeholder: ['开始日期', '结束日期'] } -->
                <a-range-picker v-if="item.type == 'timeInput'" v-model="item.rangeDate" :placeholder="item.placeholder ? item.placeholder : '请选择时间'" :defaultVal="item.defaultVal" @change="(value, options) => onTimeChange(value, options, item.key)" />
                <!-- 链接版选择框 -->
                <!-- 例子 {name: '售后场景',httpType: 'POST',httpParams: { Name: '' },keyWord: 'Name', type: 'selectLink', vModel: 'SaleAfterSceneId', defaultVal: '', placeholder: '请选择', url: '/SaleAfterV2/GetSaleAfterSceneList', httpHead: 'P31109'} -->
                <SingleChoiceSearchView v-if="item.type == 'selectLink'" style="width: 100%" placeholder="请选择" :invalidChecked="item.invalidChecked" :httpParams="item.httpParams" :httpHead="item.httpHead" :httpType="item.httpType" :keyWord="item.keyWord" :Url="item.url" :dataKey="item.dataKey" v-model="queryParam[item.vModel]" />
                <!-- 基础枚举版选择框 -->
                <!-- 例子 {name: '售后类型', type: 'selectBasicLink', vModel: 'SaleAfterType', dictCode: 'EnumSaleAfterType', defaultVal: '', placeholder: '请选择'}, -->
                <EnumSingleChoiceView v-if="item.type == 'selectBasicLink'" style="width: 100%" placeholder="请选择" :dictCode="item.dictCode" :notOption="item.notOption" :onlyOption="item.onlyOption" :isNumber="item.isNoNumber?false:true" v-model="queryParam[item.vModel]" />
                <!-- 输入搜索链接版选择框 -->
                <!--例子 { name: '售后场景3', type: 'selectInputLink', vModel: 'EmployeeId',vModelVal:'EmployeeName', defaultVal: '', placeholder: '请选择', url: '/Employee/GetEmployeesSelected', httpHead: 'P35205'} -->
                <SingleInputSearchView v-if="item.type == 'selectInputLink'" :placeholder="item.placeholder ? item.placeholder : '请选择'" :isAbsolute="true" style="margin-left: 20px" width="100%" :httpParams="{
                    pageIndex: 1,
                    pageSize: 100,
                    IsValid: true,
                  }" keyWord="keyWord" :httpHead="item.httpHead" :Url="item.url" :value="queryParam[item.vModel]" :name="queryParam[item.vModelVal]" @clear="
                    () => {
                      queryParam[item.vModel] = ''
                      queryParam[item.vModelVal] = ''
                    }
                  " @change="(val, txt, row) => changeEmployeeName(val, txt, row, item)" />
                <!-- 自由枚举读取数据的选择 -->
                <!-- 例子 {name: '售后场景2', type: 'selectBaseLink', vModel: 'SaleAfter', defaultVal: '',dataKey:{name: 'Name', value: 'Id'}, placeholder: '请选择', url: '/Application/List', httpHead: 'P36001'} -->
                <SingleChoiceView v-if="item.type == 'selectBaseLink'" style="width: 100%" :placeholder="item.placeholder ? item.placeholder : '请选择'" :Url="item.url" :httpHead="item.httpHead" v-model="queryParam[item.vModel]" :dataKey="item.dataKey ? item.dataKey : { name: 'Name', value: 'Id' }" :httpParams="item.httpParams?item.httpParams:{ pageindex: 1, pagesize: 100 }" @change="changeSingleChoice" />
                <!-- 树状读取数据的选择 -->
                <!-- 例子 {
                name: '所在部门', type: 'selectTreeLink', vModel: 'DepartmentId', defaultVal: '', disabled: false, placeholder: '请选择', dataKey: {
                value: 'Id',
                label: 'Name',
                selectableKey: 'Id',
                children: 'ChildrenDepartments'
                }, url: '/{v}/UserPermission/Department/List', httpHead: 'P36001'
                } -->
                <SelectTreeView v-if="item.type == 'selectTreeLink'" style="width: 100%" ref="selectTreeView" :placeholder="item.placeholder ? item.placeholder : '请选择'" :Url="item.url" v-model="queryParam[item.vModel]" :httpHead="item.httpHead" :disabled="item.disabled" :dataKey="item.dataKey" @change="(selectItem) => changeTreeId(selectItem, item.vModel)" />
                <!-- 树状读取数据支持双向绑定的选择 -->
                <!-- 例子 {
                name: '所在部门', type: 'selectTreeViewLink', vModel: 'DepartmentId', defaultVal: '', disabled: false, placeholder: '请选择', dataKey: {
                value: 'Id',
                label: 'Name',
                selectableKey: 'Id',
                children: 'ChildrenDepartments'
                }, url: '/{v}/UserPermission/Department/List', httpHead: 'P36001'
                } -->
                <SelectTreeViewModel v-if="item.type == 'selectTreeViewLink'" style="width: 100%" :placeholder="item.placeholder ? item.placeholder : '请选择'" :Url="item.url" v-model="queryParam[item.vModel]" :httpHead="item.httpHead" :disabled="item.disabled" :dataKey="item.dataKey" @change="(key, selectItem) => changeTreeViewId(key, selectItem)" />
                <!-- 普通多选 -->
                <SelectMultipleInput v-if="item.type == 'selectMultipleInput'" :value="item.value?item.value:[]" :maxTagCount="item.maxTagCount" :httpParams="item.httpParams" keyWord="KeyWord" :dataKey="item.dataKey ? item.dataKey : { name: 'Name', value: 'Id' }" :placeholder="item.placeholder ? item.placeholder : '请选择'" :httpHead="item.httpHead" :url="item.url" @change="(key, selectItem) => changeMultipleInput(key, selectItem,item)" />
                <!-- 枚举多选 -->
                <EnumMultipleChoiceView v-if="item.type == 'enumMultipleChoiceView'" style="width: 100%" :maxTagCount="item.maxTagCount" :placeholder="item.placeholder ? item.placeholder : '请选择'" :dictCode="item.dictCode" :httpHead="item.httpHead?item.httpHead:'P36001'" v-model="queryParam[item.vModel]" @change="(key, selectItem) => changeMultipleInput(key,selectItem,item)" />
                <!-- 其他搜索的插槽 -->
                <div v-if="sortArray.length > 0">
                  <div v-for="(rItem,index) in sortArray" :key="index">
                    <slot :name="rItem" v-if="item.type == rItem"></slot>
                  </div>
                </div>
              </a-form-item>
            </a-col>
          </div>

          <a-col :md="searchShowCol">
            <span style="float: right; overflow: hidden; margin-bottom: 12px" class="table-page-search-submitButtons">
              <a-button @click="searchReset()" icon="reload" style="margin-right: 8px">重置</a-button>
              <a-button type="primary" @click="searchQuery(null,1)" icon="search">查询</a-button>
              <a-button type="link" v-if="showUnfoldBtn" :icon="isSearchMoreShow ? 'up' : 'down'" @click=";(isSearchMoreShow = !isSearchMoreShow), (showLongSearchBtn = !showLongSearchBtn)">{{ !isSearchMoreShow ? '展 开' : '收 起' }}</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-card>
</template>

<script>
export default {
  name: 'SimpleSearchArea',
  props: {
    searchInput: {
      type: Array,
      default: () => {
        return []
      },
    },
    queryCol: {
      type: Number,
      default: 6,
    },
    sortArray: {
      type: Array,
      default: () => {
        return []
      },
    },
    // 某些情况下需要加默认值防止第一次不显示
    defQueryParam: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      labelCol: {
        xs: { span: 8 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 16 },
        sm: { span: 19 },
      },
      queryNum: 1,
      queryParam: {
        ...this.defQueryParam
      },
      rangeDate: [],
      isSearchMoreShow: false, //是否展示展开收起
      showLongSearchBtn: true, //是否向两侧对齐
      showUnfoldBtn: false, //是否展示展开按钮
    }
  },
  computed: {
    searchShowCol() {
      if (this.isSearchMoreShow) {
        if (this.showLongSearchBtn) {
          return this.queryCol * this.queryNum
        } else {
          return this.queryCol
        }
      } else {
        if (this.showLongSearchBtn) {
          return this.queryCol * this.queryNum
        } else {
          return this.queryCol
        }
      }
    },
  },
  watch: {
    queryParam: {
      handler(val) {
        this.$emit('watchQueryParam', val)
      },
      deep: true,
    },
  },
  created() {

  },
  mounted() {
    this.setInputRow()
    this.initData()
  },
  methods: {
    /**
 * 初始化查询条件默认值
 */
    initDefaultValue(isCaceh, callback) {
      this.searchInput.forEach((x) => {
        let value = isCaceh ? x.dValue : x.defaultVal
        let key = x.key || x.vModel
        if (value != undefined) {
          if (value instanceof String) {
            this.queryParam[key] = value
          } else if (value instanceof Boolean) {
            this.queryParam[key] = String(value)
          } else {
            this.queryParam[key] = value
          }
        }
      })
      console.log(this.searchInput, this.queryParam)
      this.$forceUpdate()
      callback && callback()
    },
    initData() {
      if (this.searchInput && this.searchInput.length > 0) {
        this.searchInput.forEach((item, index) => {
          item.dValue = null
          // 下面这段话不需要，时间组件v-moel申明会自动添加上该字段，添加反而会导致时间组件选值后视图无法更新
          // if (item.type == 'timeInput') {
          //   item.rangeDate = []
          // }
        })
      }
    },
    onTimeChange(value, options, key) {
      if (options.length == 2) {
        this.queryParam[key[0]] = options[0]
        this.queryParam[key[1]] = options[1]
      } else {
        this.queryParam[key[0]] = null
        this.queryParam[key[1]] = null
      }
    },
    changeEmployeeName(val, txt, row, item) {
      this.queryParam[val] = val
      this.queryParam[txt] = txt
    },
    changeSingleChoice(val, txt, item) {
      this.$emit('changeSingleChoice', val, txt, item)
    },
    changeTreeId(selectItem, key) {
      this.queryParam[key] = selectItem.value
    },
    changeTreeViewId(key, selectItem) { },
    changeMultipleInput(key, selectItem, item) {
      if (item.vModel) {
        if (item.vModelStr) {
          let keyStr = key.join(',')
          this.queryParam[item.vModel] = keyStr
        } else {
          this.queryParam[item.vModel] = key
        }
      }
    },
    setQueryParam(queryParam) {
      this.queryParam = queryParam
      this.$forceUpdate()
    },
    handleChange(value, key) {
      this.queryParam[key] = value
      this.$forceUpdate()
    },
    searchQuery(extendParams, searchType) {
      this.$emit('searchQuery', this.queryParam, 'searchQuery', extendParams, searchType)
    },
    searchReset() {
      this.queryParam = {}
      this.cacheUtil.deleteListData(this.$route.name)
      this.searchInput.forEach((item, index) => {
        item.dValue = null
        if (item.type == 'timeInput') {
          item.rangeDate = []
        }
      })
      this.$emit('searchQuery', this.queryParam, 'searchReset', null, 2)
    },
    resetQueryParam() {
      this.queryParam = {}
      this.searchInput.forEach((item, index) => {
        if (item.type == 'timeInput') {
          item.rangeDate = []
        }
      })
    },
    setInputRow() {
      let len = this.searchInput.length
      let lenNum = 4 //搜索一排放下的数量
      if (len >= 8) {
        this.showUnfoldBtn = true
        this.isSearchMoreShow = false
        this.showLongSearchBtn = false
      } else {
        this.showUnfoldBtn = false
        this.isSearchMoreShow = true
        this.showLongSearchBtn = true
      }
      if (this.queryCol == 8) {
        lenNum = 3
        if (len >= 1 && len <= lenNum) {
          this.queryNum = lenNum - len
        } else if (len > lenNum) {
          this.queryNum = lenNum - (len % lenNum)
        }
      } else {
        lenNum = 4
        if (len >= 1 && len <= lenNum) {
          this.queryNum = lenNum - len
        } else if (len > lenNum) {
          this.queryNum = lenNum - (len % lenNum)
        }
      }
      // 如果是0的话重置数量
      if (this.queryNum == 0) {
        this.queryNum = lenNum
      }
    },
  },
}
</script>

<style></style>
