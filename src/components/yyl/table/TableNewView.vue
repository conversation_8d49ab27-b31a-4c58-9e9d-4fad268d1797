<!--
 * @Author: LP
 * @Description: 公用table组件
 * @Date: 2023/07/06
-->
<template>
  <a-card
    v-if="tableData"
    :bordered="showCardBorder"
    :bodyStyle="{ padding: showCardBorder ? '12px' : '0' }"
    :headStyle="{
      padding:
        tabDataList.length > 0 ||
        (tableData.tabTitles && tableData.tabTitles.length > 0) ||
        (tableData.operateBtns && tableData.operateBtns.length > 0)
          ? '10px 16px'
          : '0 0',
    }"
  >
    <div slot="title" v-if="tabDataList.length > 0 || (tableData.tabTitles && tableData.tabTitles.length > 0)">
      <template v-if="tabDataList.length > 0">
        <a-radio-group v-model="table.curStatus" @change="onTabChange">
          <a-radio-button v-for="(item, index) in tabDataList" :value="item[table.tabValueKey || 'value']" :key="index"
            >{{ item.name != undefined ? item.name : item.Name }}
            {{
              table.tabShowCount == false ? '' : item.count != undefined ? '(' + item.count + ')' : ''
            }}</a-radio-button
          >
        </a-radio-group>
      </template>
      <template v-else-if="tableData.tabTitles && tableData.tabTitles.length > 0">
        <span
          style="margin-right: 20px"
          :style="{ color: tableData.titleColor }"
          v-for="(title, index) in tableData.tabTitles"
          :key="index"
          >{{ title }}</span
        >
      </template>
    </div>
    <div slot="extra" v-if="tableData.operateBtns && tableData.operateBtns.length > 0">
      <slot name="operateBtnBefore"></slot>
      <template v-if="tableData.operateBtns.length > 0">
        <YYLButton
          v-for="(item, index) in tableData.operateBtns"
          :key="index"
          :menuId="item.id"
          :loading="item.loading"
          :text="item.name"
          :type="item.type"
          :icon="item.icon"
          :size="item.size"
          :disabled="item.disabled === true || (item.isDisabled && item.isDisabled() === true)"
          :class="{ 'operate-btn': index == 0 }"
          @click="operate(null, item.key)"
        />
      </template>
      <slot name="operateBtnAfter"></slot>
    </div>
    <!-- 数据列表 -->
    <a-table
      :bordered="tableData.bordered == true ? true : false"
      ref="table"
      :size="tableData.size"
      :rowKey="tableData.rowKey ? tableData.rowKey : (record, index) => index"
      :class="tableData.bordered == true ? 'table' : ''"
      :columns="
        tableData.filterColumns
          ? columns.filter((item) => item.isShow == undefined || item.isShow(item) === true)
          : columns
      "
      :dataSource="dataList"
      :pagination="tableData.showPagination == false || !parent ? false : parent.ipagination"
      :loading="parent ? parent.loading : false"
      :expandedRowKeys.sync="expandedRowKeys"
      :childrenColumnName="tableData.childrenKey || 'children'"
      :rowClassName="rowClassNameFun"
      :rowSelection="
        tableData.selectType
          ? {
              selectedRowKeys: parent ? parent.selectedRowKeys : [],
              onChange: (sRowKeys, sRows) => {
                if (parent && parent.onSelectChange) {
                  parent.onSelectChange(sRowKeys, sRows, tableData.isSelectCurPage == false ? '' : tableData.rowKey)
                }
              },
              // onSelect: (record, selected, selectedRows) => {
              //   if (parent && parent.onSelect) {
              //     parent.onSelect(record, selected, dataList, tableData.rowKey, tableData.childrenKey, tableData.selectedChild)
              //   }
              // },
              type: tableData.selectType,
            }
          : null
      "
      @change="(pag, filters, sorter) => onTableChange(pag, filters, sorter)"
      :scroll="{
        y: dataList.length === 0 ? false : tableData.scrollY ? tableData.scrollY : this.$root.tableHeight,
        x: tableWidth(),
      }"
    >
      <!-- 可点击的a标签样式展示 -->
      <span slot="click" slot-scope="text, record, index, column">
        <a v-if="text" @click="operate(record, column.dataIndex, index)">{{ text }}</a>
        <span v-else>--</span>
      </span>
      <!-- 普通文本 -->
      <span slot="component" slot-scope="text, record, index, column">
        <!-- 完整展示内容 -->
        <div v-if="column.complete == true" style="word-wrap: break-word; white-space: normal">
          {{ typeof text == 'number' || text ? '' + text : text == undefined ? '' : '--' }}
        </div>
        <!-- 显示部分内容、超过省略号 -->
        <j-ellipsis
          v-else
          :value="typeof text == 'number' || text ? '' + text : text == undefined ? '' : '--'"
          :length="column.length || 20"
        />
      </span>
      <!-- 带背景色的普通文本 -->
      <div slot="bgComponent" slot-scope="text, record, index, column" class="bgComponent">
        <div class="position" :style="{ background: column.bgColor }">
          <!-- 完整展示内容 -->
          <div v-if="column.complete == true" style="word-wrap: break-word; white-space: normal">
            {{ text || '--' }}
          </div>
          <!-- 显示部分内容、超过省略号 -->
          <j-ellipsis
            v-else
            :value="text == 0 || text ? String(column.type == 'price' ? '￥' + text : text) : '--'"
            :length="column.length || 20"
          />
        </div>
      </div>
      <!-- 数字 -->
      <span slot="number" slot-scope="text, record, index, column">
        <!-- 展示可操作的a标签样式 -->
        <a v-if="column.action === true && (text >= 0 || text < 0)" @click="operate(record, column.dataIndex, index)">{{
          text
        }}</a>
        <j-ellipsis v-else :value="text >= 0 || text < 0 ? '' + text : '--'" :length="10" />
      </span>
      <!-- 根据条件是否展示内容 -->
      <template slot="isShow" slot-scope="text, record, index, column">
        <j-ellipsis v-if="column.isShow && column.isShow(record) === true" :value="(column.beforeText || '') + text" />
        <span v-else>{{ '--' }}</span>
      </template>
      <!-- 布尔值 -->
      <template slot="bool" slot-scope="text, record, index, column">
        <span v-if="[true, 'true'].includes(text)">{{ column.trueText || '是' }}</span>
        <span v-else-if="[false, 'false'].includes(text)">{{ column.falseText || '否' }}</span>
        <span v-else>{{ text || ' -- ' }}</span>
      </template>
      <!-- 状态 -->
      <span
        slot="state"
        slot-scope="text, record, index, column"
        :style="{ color: column.color ? column.color(record) : '' }"
      >
        <j-ellipsis :value="text || '--'" :length="100" />
      </span>
      <!-- 价格 -->
      <span
        slot="price"
        slot-scope="text, record, index, column"
        :style="{ color: column.color ? column.color(record) : '' }"
      >
        <j-ellipsis
          :value="
            text == undefined || text == null
              ? '--'
              : (text < 0 ? '-' : '') +
                '￥' +
                showPrice(text, column.precision || 2)
          "
        />
      </span>
      <!-- 百分比 -->
      <span slot="percent" slot-scope="text">
        <j-ellipsis :value="text || text == 0 ? text + '%' : '--'" />
      </span>
      <!-- 日期 -->
      <span slot="date" slot-scope="text, record, index, column">
        <j-ellipsis :value="text ? moment(text).format(column.format || 'YYYY-MM-DD') : ' -- '" :length="100" />
      </span>
      <!-- 文本输入框-->
      <div slot="input" slot-scope="text, record, index, column">
        <a-input
          placeholder="请输入"
          :disabled="column.disabled"
          v-model="record[column.dataIndex]"
          :maxLength="column.maxLength || 50"
          style="width: 100%"
          @blur="() => (column.onBlur ? column.onBlur(column.dataIndex, record, index) : {})"
          @pressEnter="(e) => (column.onEnter ? column.onEnter(e, column.dataIndex, record, index) : {})"
          @change="
            (e) => (column.onInputChange ? column.onInputChange(e.target.value, column.dataIndex, record, index) : {})
          "
        />
      </div>
      <!-- 数字输入框 带小数点-->
      <div slot="inputNumber" slot-scope="text, record, index, column">
        <a-input-number
          style="width: 100%"
          v-model="record[column.dataIndex]"
          placeholder="请输入"
          :min="column.min === null ? undefined : !column.min ? 0 : column.min"
          :max="column.max || 999999999"
          :size="column.size"
          :precision="column.precision >= 0 ? column.precision : 2"
          @blur="() => (column.onBlur ? column.onBlur(column.dataIndex, record, index) : {})"
          @pressEnter="(e) => (column.onEnter ? column.onEnter(e, column.dataIndex, record, index) : {})"
          @change="(e) => (column.onInputChange ? column.onInputChange(e, column.dataIndex, record, index) : {})"
        />
      </div>
      <!-- 数字输入框 数量 -->
      <div slot="inputCount" slot-scope="text, record, index, column">
        <a-input-number
          style="width: 100%"
          v-model="record[column.dataIndex]"
          placeholder="请输入"
          :step="1"
          :min="0"
          :max="column.max || 999999999"
          :size="column.size"
          :formatter="(text) => (/^\d+$/.test(text) ? text : text.slice(0, -1))"
          @blur="() => (column.onBlur ? column.onBlur(column.dataIndex, record, index) : {})"
          @pressEnter="(e) => (column.onEnter ? column.onEnter(e, column.dataIndex, record, index) : {})"
          @change="(e) => (column.onInputChange ? column.onInputChange(e, column.dataIndex, record, index) : {})"
          :precision="0"
        />
      </div>
      <!-- 是否活动 -->
      <template slot="isValid" slot-scope="text, record, index, column">
        <a-switch
          :checked-children="column.checkedTxt"
          :un-checked-children="column.unCheckedTxt"
          v-if="(!column.id || (column.id && parent.checkBtnPermissions(column.id))) && !isNoEdit(record, column)"
          v-model="record[column.dataIndex]"
          :loading="record.loading"
          @change="(checked, event) => onSetValid(record, checked, column)"
        />
        <span v-else>{{ text ? column.trueText || '有效' : column.falseText || '无效' }}</span>
      </template>
      <!-- 设置序号 -->
      <template slot="sort" slot-scope="text, record, index, column">
        <a-input-number
          v-model="record.Sort"
          v-if="record.isSortEdit && !isNoEdit(record, column)"
          @keydown.enter="onSetSort(record, index, column)"
          :min="0"
          :max="999999999"
          :precision="0"
        />
        <span v-else>{{ text || ' -- ' }}</span>
        <a v-if="!isNoEdit(record, column)" style="margin-left: 6px">
          <a-icon
            type="edit"
            title="编辑"
            v-if="!record.isSortEdit"
            @click="
              () => {
                record.isSortEdit = true
                $forceUpdate()
              }
            "
          />
          <a-icon type="enter" v-else @click="onSetSort(record, index, column)" title="确定" />
        </a>
      </template>
      <!-- 自定义插槽 -->
      <span :slot="item" v-for="(item, index) in tableData.customSlot || []" :key="index" slot-scope="text, record">
        <slot :name="item" :record="record" :text="text" :index="index"></slot>
      </span>
      <!-- 图片 -->
      <span slot="image" slot-scope="text, record, index, column">
        <ImageControl
          :height="column.imgh > 0 ? column.imgh : 30"
          :width="column.imgw > 0 ? column.imgw : 30"
          :src="text"
          v-if="text"
        />
        <span v-else>--</span>
      </span>
      <!-- 操作 -->
      <span slot="action" slot-scope="text, record, index, column">
        <span v-for="(item, i) in column.actionBtns || []" :key="i">
          <!-- 根据条件隐藏显示 -->
          <span style="margin: 0 5px" v-if="actionBtnIsShow(item, record)">
            <template v-if="item.name === '删除'">
              <a-popconfirm
                v-if="item.showPopconToast"
                :title="item.title || `确定删除该项吗?`"
                :disabled="actionBtnIsDisabled(item, record)"
                @confirm="operate(record, item.name, index)"
              >
                <a :disabled="actionBtnIsDisabled(item, record)"> {{ item.name }}</a>
              </a-popconfirm>
              <a v-else @click="operate(record, item.name, index)"> {{ item.name }}</a>
            </template>
            <template v-else>
              <a @click="operate(record, item.name, index)" :disabled="item.disabled">
                <a-icon v-if="item.icon" :type="item.icon" />
                {{ item.name }}
              </a>
            </template>
          </span>
        </span>
        <span
          class="tm"
          v-if="
            column.actionBtns &&
            column.actionBtns.filter((btn) => btn.isShow == undefined || btn.isShow(record) == true).length == 0
          "
          >-</span
        >
      </span>
      <!-- 树形操作 -->
      <span slot="actionChild" slot-scope="text, record, index, column">
        <span v-for="(item, i) in column.actionBtns || []" :key="i">
          <!-- 根据条件隐藏显示 -->
          <span style="margin: 0 5px" v-if="record.children">
            <template v-if="['删除', '移除'].includes(item.name)">
              <a-popconfirm
                :title="`确定删除该项吗?`"
                :disabled="actionBtnIsDisabled(item, record)"
                @confirm="operate(record, item.name, index)"
              >
                <a :disabled="actionBtnIsDisabled(item, record)"> {{ item.name }}</a>
              </a-popconfirm>
            </template>
            <template v-else>
              <a @click="operate(record, item.name, index)" :disabled="item.disabled">
                <a-icon v-if="item.icon" :type="item.icon" />
                {{ item.name }}
              </a>
            </template>
          </span>
        </span>
      </span>
    </a-table>
    <!-- 底部按钮 -->
    <slot name="bottomView"></slot>
  </a-card>
</template>

<script>
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import moment from 'moment'
export default {
  name: 'TableNewView',
  //   inject: ['tableH'], //未使用
  mixins: [],
  components: {
    JEllipsis,
  },
  props: {
    /**
     * table顶部左边tab状态数据 - 和 tableData.tabTitles 互斥
     */
    tabDataList: {
      type: Array,
      default: () => {
        return [
          // {
          //   name: '全部',
          //   value: 'all',
          //   count: '',
          //   key:'',//数量对应字段
          // }
        ]
      },
    },
    /**
     * 整个table列表的配置项-必填
     */
    table: {
      type: Object,
      required: true,
      default: () => {
        return this.tableDefData
      },
    },

    /**
     * 是否显示边框
     */
    showCardBorder: {
      type: Boolean,
      default: false,
    },
    /**
     * 表头
     */
    columns: {
      type: Array,
      required: true,
      default: () => {
        return [
          // {
          //   //常规设置之外、如下
          //   colors: [], //state 和 yesno状态显示的颜色值
          //   menuId: '', //列配置的菜单id
          //   trueText: '是', //isValid 无权限 时true的显示文本
          //   falseText: '否', //isValid 无权限 时false的显示文本
          //   actionBtns: [
          //     // {
          //     //   name: '详情',
          //     //   id: '0b79bc0f-c260-49ed-8034-3208b2fe0294', //按钮 id
          //     //   isShow: record => {  //是否显示的验证方法
          //     //     return true
          //     //   }
          //     // }
          //   ] //列表按钮集合
          // }
        ]
      },
    },
    /**
     * table数据
     */
    dataList: {
      type: Array,
      required: true,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      parent: null,
      expandedRowKeys: [],
      tableDefData: {
        size: 'middle', //表格大小 default默认 middle中等 small小号
        curStatus: 'all', //tab默认值，当前状态值
        tabStatusKey: '', //tab切换查询条件属性值
        tabShowCount: true, //tab是否显示数量
        tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabDataList互斥
        operateBtns: [
          //tab右上角的按钮集合
          // {
          //   name: '申请付款',
          //   type: 'primary',
          //   icon: 'plus',
          //   key: '申请付款',
          //   id: '697e3315-8aee-4e70-8f94-e4f992100212'  //按钮 id
          // }
        ], //右上角按钮集合
        bordered: false, //是否显示表格的边框
        rowKey: '', //table的roekey字段
        showPagination: true, //是否显示分页
        childrenKey: '', //table下级数据的属性名称
        selectType: '', //选择类型 radio单选 checkbox多选 空为不支持选择
        isSelectCurPage: false, //是否只是选择当前页数据 true 可以记录跨页面选择数据 false 只能记录当前页选择数据
        selectedChild: false, // 是否联动选择子集
        filterColumns: null, //是否筛选columns
      },
    }
  },
  watch: {},
  filters: {},
  computed: {
    tableData() {
      return Object.assign(this.tableDefData, this.table || {})
    },
    isNoEdit() {
      return function (record, column) {
        if (column.isNoEdit) {
          return column.isNoEdit(record) || false
        } else {
          return false
        }
      }
    },
    actionBtnIsDisabled() {
      return function (item, record) {
        if (item.disabled) {
          // 特殊条件显示隐藏
          if (item.disabled(record) === true) {
            return true
          } else {
            return false
          }
        } else {
          // 未设置条件直接显示按钮
          return false
        }
      }
    },
    actionBtnIsShow() {
      return function (item, record) {
        //是否有按钮权限优先判断
        if (this.parent && !this.parent.checkBtnPermissions(item.id)) {
          return false
        }
        if (item.isShow) {
          // 特殊条件显示隐藏
          if (item.isShow(record) === true) {
            return true
          } else {
            return false
          }
        } else {
          // 未设置条件直接显示按钮
          return true
        }
      }
    },
  },
  mounted() {
    this.findParent(this)
  },
  created() {},
  activated() {
    this.initLoadData()
  },
  methods: {
    moment,

    /**
     * 初始化 展开初始值
     * @param {*} key
     */
    initExpandedRowKeys(key) {
      this.expandedRowKeys = []
      this.expandedRowKeys.push(key)
    },
    showPrice(txt, precision) {
      const base = +String(1).padEnd(precision + 1, '0')
      return (Math.round(Number(txt < 0 ? String(txt).replace('-', '') : txt) * base) / base).toFixed(precision)
    },
    initLoadData() {
      if (!this.parent) {
        this.findParent(this)
      }
      if (this.parent.useParentActivated) {
        return
      }
      const name = this.$route.name
      let reg = /List$/
      if (reg.test(name)) {
        this.parent.loadData()
      }
    },
    // 操作
    operate(record, key, index) {
      this.$emit('operate', record, key, index)
    },
    tableLoadBefore() {
      if (
        this.tableData &&
        this.tableData.tabStatusKey &&
        this.tableData.curStatus != null &&
        this.tableData.curStatus != undefined &&
        this.parent
      ) {
        this.parent.queryParam[this.tableData.tabStatusKey] = this.tableData.curStatus
      }
    },
    /**
     * 顶部切换tab change事件
     */
    onTabChange() {
      this.$emit('onTabChange', this.tableData.curStatus)
      if (this.parent) {
        this.parent.searchQuery(null, 1)
      }
    },

    findParent(p) {
      if (p && p.$parent && p.$parent.IsListMixin != undefined) {
        this.parent = p.$parent
      } else if (p.$parent) {
        this.findParent(p.$parent)
      }
    },
    /**
     * table的change 事件
     * @param {*} pag
     * @param {*} filters
     * @param {*} sorter
     */
    onTableChange(pag, filters, sorter) {
      if (this.parent) {
        this.parent.handleTableChange(pag, filters, sorter)
      }
    },
    /**
     * 设置是否有效
     * @param {*} record
     * @param {*} checked
     */
    onSetValid(record, checked, column) {
      if (column.onChange) {
        column.onChange(record, checked)
      }
    },
    /**
     * 设置序号
     * @param {*} record
     * @param {*} index
     */
    onSetSort(record, index, column) {
      if (column.onChange) {
        column.onChange(record, index)
      }
    },

    tableWidth() {
      let w = 0
      this.columns.forEach((x) => {
        if (x.width >= 0) {
          w += x.width
        }
      })
      if (w == 0 || w < 1300) {
        w = '100%'
        if (w < 1000) {
          this.columns.forEach((item) => {
            if (item.dataIndex != 'action' && !item.fixed && item.width > 0) {
              delete item['width']
            }
          })
        }
      }

      // console.log('tableData width = ' + w)
      return w
    },
    rowClassNameFun(record, index) {
      let key = this.tableData.childrenKey || 'children'
      if (!record[key] || record[key].length === 0) {
        return 'empty-row'
      }
    },
    findCacheParent(p) {
      if (p && p.parent && p.parent.tag) {
        if (p.parent.tag.endsWith('keep-alive')) {
          return p.parent
        } else {
          return this.findCacheParent(p.parent)
        }
      }
    },
    /**
     * 删除编辑界面缓存数据
     * @param {*} pageName
     */
    clearEditPageChaceData(pageName) {
      if (!pageName) {
        console.log('pageName为空')
        return
      }
      let vnode = this.$root.cacheThis.$vnode
      if (!vnode) {
        console.log('没有找到vnode')
        return
      }
      let parent = this.findCacheParent(vnode)
      if (!parent) {
        // console.log('没有找到大的parent', this.$route)
        parent = this.findCacheParent(this.$parent.$vnode)
      }

      if (!parent) {
        console.log('没有找到parent')
        return
      }
      let cache = parent.componentInstance.cache
      let cacehKeys = parent.componentInstance.keys
      if (cache) {
        // console.log('删除这里', cache)
        let dKey = null
        Object.keys(cache).forEach((key, i, arr) => {
          if (cache[key] && cache[key].name && cache[key].name == pageName) {
            dKey = key
          }
        })
        let toast = '删除了'
        if (dKey) {
          delete cache[dKey]
          toast += 'cache /'
        }
        let index = cacehKeys.indexOf(dKey)
        if (index > -1) {
          cacehKeys.splice(index, 1)
          toast += ' 和 cacehKeys / ' + dKey + '缓存'
        } else {
          console.log('没在caceh中找到该页面直接进行重置')
          this.$root.$emit('reloadRouter')
        }
        if (toast != '删除了') {
          console.log(toast)
        }
      }
    },
  },
}
</script>

<style scoped lang="less">
.group-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
.operate-btn {
  margin-left: 10px;
}
.operate-btn:last-child {
  margin-right: 0px;
}
/deep/.table .ant-table {
  border: none;
  border-radius: 4px;
}
/deep/.ant-card-head-title {
  padding: 0 0;
}

/deep/.empty-row {
  td:first-child {
    .ant-table-row-expand-icon {
      visibility: hidden;
    }
  }
}
.tm {
  color: white;
  opacity: 0.01;
}
/deep/ .ant-table-row-cell-break-word {
  position: relative;
}
.bgComponent {
  width: 100%;
  height: 100%;
  .position {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
