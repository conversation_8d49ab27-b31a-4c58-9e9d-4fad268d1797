<!--
 * @Description: Table 组件（仅table）
 * @Version: 1.0
 * @Author: Harmoni
 * @Date: 2025-02-18 09:32:38
 * @LastEditors: haolin.hu
 * @LastEditTime: 2025-04-30 00:54:36
-->

<template>
  <a-table 
    class="custom-tailwind"
    :loading="loading" 
    :data-source="tableData" 
    size="small" 
    bordered 
    :scroll="{ x: scrollX, y: $root.tableHeight }"
    :pagination="isShowPagination ? pagination : false"
    :rowKey="rowKey"
    :row-selection="rowSelection"
    @change="pagination => getList(false, pagination)"
  >
    <template v-for="item in columns" >
      <a-table-column 
        v-if="getBooleanValue(item, 'isShow')"
        :key="item.key"
        :title="item.title"
        :data-index="item.key"
        :fixed="item.key === 'action' ? 'right' : (item.fixed || false)"
        :width="item.width"
        :ellipsis="item.ellipsis || false"
        :align="getAlign(item)"
      >
        <template slot-scope="text, record, index">
          <!-- 操作按钮 -->
          <div v-if="item.key === 'action'">
            <template v-for="action in item.actionBtns">
              <a-button
                v-if="getBooleanValue(action, 'isShow')"
                :key="action.key"
                type="link"
                v-has="action.permissionId"
                @click="handleClick(action, record, index)"
              >
                {{action.text}}
            </a-button>
            </template>
          </div>
          <!-- 可点击的文字按钮 -->
          <a-button v-else-if="item.isLink" type="link" @click="handleClick(item, record, index)">
            {{ getValue(item, record) }}
          </a-button>
          <!-- 图片 image -->
          <img
            v-else-if="item.type === 'image'"
            :src="getValue(item, record)"
            class="object-contain"
            :style="getStyle(item)"
            alt="图片"
          >
          <!-- 普通文字 -->
          <a-tooltip v-else :title="getValue(item, record)">
            <span class="ellipsis" :class="getConfig(item, 'class', record)">{{ getValue(item, record) }}</span>
          </a-tooltip>
        </template>
      </a-table-column>
    </template>
  </a-table>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import moment from 'moment'

export default {
  name: 'YYLTable',
  props: {
    dataList: { // 表格数据(由外层传入 非走接口请求数据时 传入)
      type: Array,
      default: () => []
    },
    url: { // 接口地址
      type: String,
      default: '',
    },
    httpHead: { // 示例 P36001
      type: String,
      default: '',
    },
    method: { // 请求方式
      type: String,
      default: 'GET',
    },
    columns: {
      type: Array,
      default: () => [],
      required: true
    },
    queryParams: { // 查询参数  
      type: Object,
      default: () => ({})
    },
    rowKey: {
      type: [ String, Function ],
      default: 'Id',
      required: true
    },
    rowSelection: { // 表格行选择配置
      type: Object,
      default: null
    },
    scrollX: { // 表格横向滚动
      type: [ Number, Boolean, String ], // 接受 Number/Boolean
      default: '100%'
    },
    immediateGet: { // 是否渲染时立即请求列表接口
      type: Boolean,
      default: true
    },
    pageSize: {
      type: Number,
      default: 10
    },
    isShowPagination: { // 是否显示分页
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      isDeactivated: false, // 是否执行过deactivated生命周期
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条`
      }
    }
  },
  watch: {
    dataList: {
      handler(val) {
        if(!this.url) { // 无接口地址时,直接使用value作为数据源
          this.tableData = val
          this.$emit('input', this.tableData)
        }
      },
      immediate: true
    }
  },
  created() {
    this.pagination.pageSize = this.pageSize
    this.immediateGet && this.getList(true)
  },
  activated() {
    (this.isDeactivated && this.immediateGet) && this.getList(true)
  },
  deactivated() {
    this.isDeactivated = true
  },
  methods: {
    getList(isRefresh, pageInfo) {
      if (!this.url) return

      if (isRefresh) {
        this.pagination.current = 1
      } else if (pageInfo) {
        Object.assign(this.pagination, pageInfo)
      }
      
      this.loading = true
      const { current, pageSize } = this.pagination

      const requestAction = this.method === 'POST' ? postAction : getAction
      requestAction(this.url, {
        ...this.queryParams,
        PageIndex: current,
        PageSize: pageSize
      }, this.httpHead ).then(({ IsSuccess, Data, Count, Msg }) => {
        this.loading = false
        if (!IsSuccess) return
        this.pagination.total = Count

        if (Data.length) {
          this.tableData = Data
        } else if (current > 1) {
          this.pagination.current -= 1
          this.getList()
          return
        } else {
          this.tableData = []
        }

        this.$emit('getTableData', this.tableData)
      })
    },
    getBooleanValue(item, key) { // 获取布尔值
      const matchValue = item[key]
      const defaultValueMap = {
        loading: false,
        disabled: false,
        isShow: true
      }

      if (typeof matchValue === 'function') {
        return matchValue(item)
      } else {
        return typeof matchValue === 'undefined' 
          ? defaultValueMap[key]
          : !!matchValue
      }
    },
    getStyle(item) {
      if (typeof item.style === 'object') {
        return item.style
      }
      return {}
    },
    getConfig(item, key, record) { // 获取配置值
      const config = item[key]
      if (typeof config === 'function') {
        return config(record)
      } else {
        return config
      }
    },
    getAlign(item) {
      const { key, align, type } = item
      if (['action', 'bool'].includes(key)) return 'center'
      if (type === 'amount') return 'right'
      if (align) return align
      return 'left'
    },
    getValue(item, record) { // 获取值
      const { getVal, type, key, precision, format } = item
      const val = record[key]

      if (typeof getVal === 'function') {
        return getVal(record, key)
      } 

      if (val === null || val === undefined || val === '') {
        return '--'
      }
      
      if (type === 'bool') {
        return typeof val === 'boolean'
          ? (val ? '是' : '否')
          : '--'
      } else if (type === 'amount') {
        let num = val
        if (typeof val === 'string') {
          num = Number(val)
        }

        if (isNaN(num)) return ''

        if (precision) {
          const multiplier = Math.pow(10, precision)
          const roundedNum = Math.round(num * multiplier) / multiplier
          
          if (Number.isInteger(roundedNum) && roundedNum !== 0) {
            return `¥${roundedNum.toFixed(precision)}`
          }
        }
        
        return `¥${num}`
      } else if (type === 'date' && format) {
        return moment(val).format(format)
      } else {
        return val
      }
    },
    handleClick(item, record, index) {
      const { onClick } = item
      if (!typeof onClick === 'function') return
      onClick(record, index)
    },
  }
}
</script>

<style lang="scss" scoped>
:deep(.ant-table-header) {
  background: #fafafa !important;
}
</style>