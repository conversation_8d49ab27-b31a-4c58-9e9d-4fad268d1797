<template>
  <!-- 内容 -->
  <a-row>
    <a-card
      :bordered="cardBordered"
      :bodyStyle="{ padding: cardBordered ? '12px' : '0' }"
      :headStyle="{ padding: cardBordered ? '0 16px' : '0' }"
    >
      <!-- 表格上的状态和操作 -->
      <div class="group-box" v-if="showTab">
        <div>
          <!-- 展示状态 -->
          <a-radio-group v-model="tab.status" v-if="tab.statusList.length" @change="changeTab">
            <a-radio-button :value="item.value" v-for="(item, index) in tab.statusList" :key="index"
              >{{ item.name }}{{ item.count || item.count === 0 ? '(' + item.count + ')' : '' }}</a-radio-button
            >
          </a-radio-group>
          <!-- 左边的表格标题文字 -->
          <span v-if="tab.tableTile">{{ tab.tableTile }}</span>
          <!-- 状态栏提示文字 -->
          <div v-if="tab.statusHintArray && tab.statusHintArray.length > 0">
            <span style="color: red; margin-right: 20px" v-for="(item, index) in tab.statusHintArray" :key="index">{{
              item
            }}</span>
          </div>
        </div>
        <!-- 操作按钮 -->
        <div>
          <slot name="operateBtnBefore"></slot>
          <template v-if="tab.operateBtn && tab.operateBtn.length > 0">
            <YYLButton
              v-for="(item, index) in tab.operateBtn"
              :key="index"
              :menuId="item.id"
              :loading="item.loading"
              :text="item.name"
              v-show="!item.isHide"
              :type="item.type"
              :icon="item.icon"
              :class="{ 'operate-btn': index == 0 }"
              @click="operate('', item.key)"
            />
          </template>
          <slot name="operateBtnAfter"></slot>
        </div>
      </div>
      <!-- 提示文字 -->
      <div style="margin-bottom: 10px" v-if="tab.hintArray && tab.hintArray.length > 0">
        <span style="color: red; margin-right: 20px" v-for="(item, index) in tab.hintArray" :key="index">{{
          item
        }}</span>
      </div>

      <!-- 数据列表 -->
      <!-- {{'高度：'+this.$root.tableHeight}} -->
      <a-table
        :bordered="tab.bordered"
        ref="table"
        :rowKey="tab.rowKey ? tab.rowKey : (record, index) => index"
        :class="tab.bordered == true ? 'table' : ''"
        :size="tableInfo.size"
        :columns="
          tab.filterColumns ? columns.filter((item) => item.isShow == undefined || item.isShow(item) === true) : columns
        "
        :dataSource="dataSource"
        :rowClassName="rowClassNameFun"
        :pagination="tab.hideIpagination ? false : ipagination"
        :loading="loading"
        :rowSelection="rowSelectionInfo"
        @change="handleTableChange"
        :expanded-row-keys.sync="expandedRowKeys"
        :scroll="{
          y: dataSource.length === 0 ? false : tableInfo.scrollY ? tableInfo.scrollY : this.$root.tableHeight,
          x: tableInfo.scrollX ? tableInfo.scrollX : '100%',
        }"
      >
        <!-- 序号 -->
        <span slot="Order" slot-scope="text, record, order">
          {{ getOrder(record, order) }}
        </span>

        <!-- 是否活动 -->
        <template slot="IsActivity" slot-scope="text, record">
          <a-switch
            v-if="showActivity"
            v-model="record.IsValid"
            :loading="record.loading"
            @change="(checked, event) => onSetValid(record, checked, event)"
          />
          <span v-else>{{ text ? '是' : '否' }}</span>
        </template>

        <!-- 是否有效 -->
        <template slot="IsValid" slot-scope="text, record">
          <a-switch
            v-model="record.IsValid"
            :loading="record.loading"
            @change="(checked, event) => onSetValid(record, checked, event)"
          />
        </template>

        <!-- 是否状态 -->
        <template slot="IsWhether" slot-scope="text, record">
          <template v-if="record.Type == 2">
            <j-ellipsis v-if="text == true" value="是" :length="100" />
            <j-ellipsis v-if="text == false" value="否" :length="100" />
          </template>
          <span v-if="record.Type == 1 || record.Type == 3">/</span>
        </template>

        <!-- 设置序号 -->
        <template slot="setSort" slot-scope="text, record">
          {{ text }}
          <a>
            <a-icon type="edit" @click="operate(record, 'setSort')" />
          </a>
        </template>

        <!-- 字符串超长截取省略号显示-->
        <span slot="component" slot-scope="text">
          <j-ellipsis :value="typeof text == 'number' ? '' + text : text || '--'" :length="100" />
        </span>

        <!-- 价格显示-->
        <!-- <span slot="price" slot-scope="text">
          <j-ellipsis :value="text || text == 0 ? (text != 0 ? '¥' + text : '0') : '--'" />
        </span> -->
        <!-- 价格 precision 保留机位小数 默认俩位-->
        <span slot="price" slot-scope="text, record, index, column">
          <j-ellipsis
            :value="
              text == undefined || text == null
                ? '--'
                : (text < 0 ? '-' : '') +
                  '￥' +
                  Number(text < 0 ? String(text).replace('-', '') : text).toFixed(column.precision || 2)
            "
          />
        </span>
        <span slot="Price" slot-scope="text, record, index, column">
          <j-ellipsis
            :value="
              text == undefined || text == null
                ? '--'
                : (text < 0 ? '-' : '') +
                  '￥' +
                  Number(text < 0 ? String(text).replace('-', '') : text).toFixed(column.precision || 2)
            "
          />
        </span>

        <!-- 公用插槽-->
        <span slot="commonSlot" slot-scope="text, record">
          <slot name="commonContent" :record="record" :text="text"></slot>
        </span>

        <!-- 自定义插槽 -->
        <span :slot="item" v-for="(item, index) in tab.sortArray || []" :key="index" slot-scope="text, record">
          <slot :name="item" :record="record" :text="text"></slot>
        </span>

        <!-- 状态 -->
        <span slot="status" slot-scope="text">
          <a-tag v-if="text" :color="statusVal(text)">{{ text }}</a-tag>
        </span>

        <!-- 支付状态 -->
        <span slot="payStatus" slot-scope="text">
          <a-tag v-if="text" :color="payStatus(text)">{{ text }}</a-tag>
        </span>

        <!-- 订单状态 -->
        <span slot="orderStatus" slot-scope="text">
          <a-tag v-if="text" :color="orderStatusVal(text)">{{ text }}</a-tag>
        </span>

        <!-- 日期截取 -->
        <span slot="dayTime" slot-scope="text">
          <j-ellipsis v-if="text" :value="'' + (text.substring(0, 11) || ' -- ')" :length="100" />
        </span>

        <!-- 规格/单位/生产厂商 -->
        <span slot="Specification" slot-scope="text, record">
          <j-ellipsis :value="(text || '--') + '/' + (record.Unit || '--') + '' + (record.Manufacturer || '--')" />
        </span>

        <!-- tag标签 -->
        <span slot="tag" slot-scope="text">
          <a-tag>{{ text }}</a-tag>
        </span>

        <!-- 异常情况 -->
        <span slot="errMsg" slot-scope="text">
          <div
            style="
              background: rgba(245, 108, 108, 1);
              border-radius: 4px;
              padding: 1px 4px;
              color: #ffffff;
              font-size: 13px;
            "
          >
            {{ text }}
          </div>
        </span>

        <!-- 操作 -->
        <span slot="action" slot-scope="text, record, index">
          <span v-for="(item, index) in columns[columns.length - 1].actionBtn" :key="index">
            <!-- 根据条件隐藏显示 -->
            <span style="margin: 0 5px; line-height: 1.2" v-if="actionShowBtn(item, record)">
              <a @click="operate(record, item.name)" :disabled="item.disabled">
                <a-icon v-if="item.icon" :type="item.icon" />
                {{ item.name }}
              </a>
              <!-- <a-divider type="vertical" v-if="actionShowDivider(item,record) && index !== columns[columns.length - 1].actionBtn.length - 1" /> -->
            </span>
          </span>
          <slot name="opBtns" :record="record" :index="index"></slot>
          <span
            class="tm"
            v-if="
              columns[columns.length - 1].actionBtn &&
              columns[columns.length - 1].actionBtn.filter((btn) => actionShowBtn(btn, record)).length == 0
            "
            >-</span
          >
        </span>
      </a-table>
      <!-- 自定义页码器，解耦 table 展示数据与 pageSize --> 
      <a-pagination
        v-if="dataSource.length !== 0 && !!tab.customerPager"
        style="text-align: right; margin-top: 10px"
        size="small"
        show-size-changer
        show-quick-jumper
        :total="ipagination.total"
        @change="handlePagerChange"
        @showSizeChange="handlePagerChange"
        :current="ipagination.current"
        :page-size="ipagination.pageSize"
        :show-total="total => `共 ${total} 条`"
        :page-size-options="ipagination.pageSizeOptions"
      />
      <!-- 底部按钮 -->
      <slot name="bottomBtn"></slot>
    </a-card>
  </a-row>
</template>

<script>
import { ListMixin } from '@/mixins/ListMixin'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
import { getAction, putAction, postAction } from '@/api/manage'

export default {
  name: 'SimpleTable',
  mixins: [ListMixin],
  components: {
    JEllipsis,
  },
  props: {
    tab: {
      type: Object,
      default: () => {
        return {
          status: '',
          statusList: [
            {
              name: '全部',
              value: 'all',
              count: '',
            },
          ],
          showIpagination: true,
          bordered: true, //是否显示表格的边框
          tableTile: '',
          initSortColumn: 'WarehouseInTime', //原始排序字段
          hintArray: [],
          sortArray: [],
          operateBtn: [],
        }
      },
    },
    tableInfo: {
      type: Object,
      default: () => {
        return {
          scrollY: null,
          scrollX: null,
          actionIndex: null, //操作项的位置
          size: 'middle', //表格大小 default默认 middle中等 small小号
          filterColumns: null, //是否筛选columns
        }
      },
    },
    // 表头
    columns: {
      type: Array,
      default: () => {
        return []
      },
    },
    // 返回数据的类型,用于数量和list在一个接口
    dataList: {
      type: Object,
      default: () => {
        return {}
      },
    },
    // 表头的红色提示
    tooltipArray: {
      type: Array,
      default: () => {
        return []
      },
    },
    showTab: {
      type: Boolean,
      default: false,
    },
    cardBordered: {
      type: Boolean,
      default: false,
    },
    linkHttpHead: {
      type: String,
      default: 'P33000',
    },
    //是否自动加载表格
    isTableInitData: {
      type: Boolean,
      default: true,
    },
    // 是否分页change回调
    isHandleTableChange: {
      type: Boolean,
      default: false,
    },
    expandedRowKeysArray: {
      type: Array,
      default: () => {
        return []
      },
    },
    // 列表请求方式
    linkUrlType: {
      type: String,
      default: 'GET',
    },
    // 接口信息
    linkUrl: {
      type: Object,
      default: () => {
        return {
          list: '',
        }
      },
    },
    // 外层传入table数据
    tableDatas: {
      type: Array,
      default: () => [],
    },
    // 是否setValid的时候LoadData
    onSetValidLoadData: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      description: '列表',
      tip: '',
      queryParam: {},
      loading: false,
      listResData: [],
      value: '',
      expandedRowKeys: [], //展开的项
      selectedRowKeys: [], //选中项的key
      selectedRows: [], //选中项的信息
      parent: null,
    }
  },
  filters: {},
  computed: {
    showActivity() {
      //判断是否有活动权限
      let idArray = []
      this.columns.map((item) => {
        if (item.scopedSlots && item.scopedSlots.customRender == 'IsActivity') {
          if (item.id) {
            idArray.push(item.id)
          }
        }
      })
      if (idArray.length > 0 && this.checkBtnPermissions(idArray[0])) {
        return true
      } else {
        return false
      }
    },
    actionShowBtn() {
      return function (item, record) {
        //是否有按钮权限优先判断
        if (!this.checkBtnPermissions(item.id)) {
          return false
        }
        // 普通条件显示隐藏
        if (item.rowShowBtnKey) {
          // 值是数组的情况下
          if (item.rowShowBtnKey.val.constructor === Array) {
            let recordText = record[item.rowShowBtnKey.name]
            let showIndex = item.rowShowBtnKey.val.indexOf(recordText)
            if (showIndex > -1) {
              return true
            } else {
              return false
            }
          } else {
            // 值是字符串的情况下
            if (item.rowShowBtnKey.val == record[item.rowShowBtnKey.name]) {
              return true
            } else {
              return false
            }
          }
        } else if (item.specialShowFuc) {
          // 特殊条件显示隐藏
          if (item.specialShowFuc(record) === true) {
            return true
          } else {
            return false
          }
        } else {
          // 未设置条件直接显示按钮
          return true
        }
      }
    },
    // 设置表格是否单选多选多选
    rowSelectionInfo() {
      if (this.tab.rowSelection && this.tab.rowSelection.type && !this.tab.rowSelection.getCheckboxProps) {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange,
          onSelect: this.onSelect,
          onSelectAll: this.onSelectAll,
          type: this.tab.rowSelection.type || 'checkbox',
        }
      } else if (this.tab.rowSelection && this.tab.rowSelection.getCheckboxProps) {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange,
          onSelect: this.onSelect,
          onSelectAll: this.onSelectAll,
          type: this.tab.rowSelection.type || 'checkbox',
          getCheckboxProps: this.tab.rowSelection.getCheckboxProps,
        }
      } else {
        return null
      }
    },
    actionShowDivider() {
      return function (item, record) {
        if (item.name == '详情') {
          let orderStatisArray = record.OrderStatisticalCountList || []
          for (let i = 0; i < orderStatisArray.length; ++i) {
            if (orderStatisArray[i].SurplusDeliverCount > 0) {
              return true
            } else {
              return false
            }
          }
        } else {
          return true
        }
      }
    },
    statusVal() {
      return function (val) {
        switch (val) {
          case '未提交':
            return 'pink'
          case '待审核':
            return 'orange'
          case '未补完':
          case '待复核':
            return 'cyan'
          case '审核通过':
          case '已完成':
            return 'green'
          case '审核失败':
          case '已终止':
            return 'red'
          case '资质过期':
            return 'blue'
        }
      }
    },
    orderStatusVal() {
      return function (val) {
        switch (val) {
          case '已下单':
            return 'blue'
          case '已完成':
            return 'green'
          case '已取消':
            return 'red'
          case '超时取消':
            return 'cyan'
          case '部分退款':
            return 'orange'
          case '全额退款':
            return 'pink'
          // default:return '#409EFF'
        }
      }
    },
    payStatus() {
      return function (val) {
        switch (val) {
          case '待支付':
          case '未支付':
            return 'orange'
          case '取消支付':
            return 'pink'
          case '支付失败':
            return 'red'
          case '部分支付':
            return 'cyan'
          case '支付成功':
          case '已支付':
            return 'green'
        }
      }
    },
  },
  created() {
    this.init()
  },
  activated() {
    if (this.isTableInitData) {
      this.findParent(this)
    }
  },
  methods: {
    findParent(p) {
      if (p && p.$parent && p.$parent.$refs && p.$parent.$refs.SimpleSearchArea) {
        this.parent = p.$parent
        let queryParam = this.parent.$refs.SimpleSearchArea.queryParam
        if (
          this.parent.tab &&
          this.parent.tab['statusKey'] &&
          this.parent.tab['status'] &&
          this.parent.tab['statusList'].length > 0
        ) {
          queryParam[this.parent.tab['statusKey']] = this.parent.tab['status']
        }
        this.loadDatas(null, queryParam)
      } else {
        if (p.$parent) {
          this.findParent(p.$parent)
        }
      }
    },
    init() {
      // 默认自动加载
      if (this.$route.meta.keepAlive) {
        this.isInitData = false
      } else {
        if (this.isTableInitData) {
          this.queryParam = this.$parent.queryParam || {}
          if (this.$root.indexOrderKey) {
            return
          }
          this.isInitData = true
        } else {
          this.isInitData = false
        }
      }
      // 外层传入table数据 赋值
      if ((this.tableDatas || []).length) {
        this.dataSource = this.tableDatas || []
      }
      // 自动展开数据
      if (this.expandedRowKeysArray && this.expandedRowKeysArray.length > 0) {
        this.expandedRowKeys = this.expandedRowKeysArray
      }
    },
    loadDatas(arg, queryParam, callback, type, extendParams, searchType) {
      this.queryParam = queryParam || this.$parent.queryParam || {}
      if (this.dataList && this.dataList.list) {
        this.$root.tableDataList = this.dataList
      } else {
        this.$root.tableDataList = null
      }
      if (callback) {
        this.loadData(arg, callback, null, searchType)
      } else {
        this.loadData(arg)
      }
      // 列表数量
      if (this.linkUrl.listAmount) {
        this.listAmount()
      }
    },
    loadAfter(data) {
      this.$emit('simpleTableLoadAfter', data)
    },
    // 列表数量
    listAmount() {
      if (this.dataList && this.dataList.count) {
        let count = this.dataList.count
        this.$emit('getListAmount', this.listResData[count])
        return
      }
      let params = this.queryParam
      let obj =
        this.linkUrl.listAmountType == 'POST'
          ? postAction(this.linkUrl.listAmount, params, this.linkHttpHead)
          : getAction(this.linkUrl.listAmount, params, this.linkHttpHead)
      obj.then((res) => {
        if (res.IsSuccess) {
          this.$emit('getListAmount', res.Data)
        } else {
          this.$message.warning(res.Msg)
        }
      })
    },
    // 操作项初始化
    setActionIndex() {
      if (this.tableInfo.actionIndex == null && this.columns.length > 0) {
        this.tableInfo.actionIndex = this.columns.length - 1
      }
    },
    clearSelected() {
      this.selectedRowKeys = []
      this.selectedRows = []
    },
    // 选择table项的事件
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      this.$emit('selectedRows', selectedRowKeys, selectedRows)
    },
    onSelect(record, selected, selectedRows) {
      this.$emit('onSelect', record, selected, selectedRows)
    },
    onSelectAll(selected, selectedRows, changeRows) {
      this.$emit('onSelectAll', selected, selectedRows, changeRows)
    },
    // table行的颜色
    rowClassNameFun(record, index) {
      if (this.tab.rowClassNameFun) {
        // 销货对抵
        if (record.PayableAmount < 0 || record.ResiduePayableAmount < 0) {
          return 'table-color-red-dust'
        }
      }
    },
    // 设置查询条件
    setQueryParam(model) {
      this.queryParam = model
    },
    // 设置是否有效
    onSetValid(item, checked) {
      this.$emit('onSetValid', item, checked)
    },
    // 操作
    operate(record, type) {
      this.$emit('operate', record, type)
    },
    // 点击列表的事件
    operateCilckList(record, type, text) {
      this.$emit('operate', record, type, text)
    },
    handlePagerChange(current, pageSize) {
      this.handleTableChange(Object.assign({}, this.ipagination, { current, pageSize }), null, {})
    },
    // 列表改变事件
    handleTableChange(a, b, c, d) {
      if (Object.keys(c).length > 0) {
        this.queryParam.SortFieldName = c['columnKey']
        this.ipagination.current = a.current
        // let sorterKey = c.column?c.column.sorterKey:null
        // this.queryParam.sorterKey = sorterKey
        //  父组件定义列 排序名称
        if (c.order == 'ascend') {
          // 升序
          this.isorter.order = 'asc'
          this.isorter.column = c.columnKey || 'createTime'
          this.queryParam.IsDescSort = false
        } else if (c.order == 'descend') {
          // 降序
          this.isorter.order = 'desc'
          this.isorter.column = c.columnKey || 'createTime'
          this.queryParam.IsDescSort = true
        } else {
          this.isorter.column = this.tab.initSortColumn || 'createTime'
          this.queryParam.IsDescSort = null
          this.queryParam.SortFieldName = null
          // this.queryParam.sorterKey = null
          delete this.queryParam.IsDescSort
          delete this.queryParam.SortFieldName
          // delete this.queryParam.sorterKey
        }
      }
      this.ipagination = a
      if (this.dataList && this.dataList.list) {
        this.$root.tableDataList = this.dataList
      } else {
        this.$root.tableDataList = null
      }

      if (this.isHandleTableChange) {
        this.$emit('handleTableChange')
      } else {
        this.loadData()
      }
    },
    // 状态切换
    changeTab(e) {
      this.$emit('changeTab', e.target.value, this.tab.statusKey)
    },
    getERPOutboundNo(ERPOutboundNo) {
      return ERPOutboundNo ? (ERPOutboundNo.indexOf(',') > -1 ? ERPOutboundNo.split(',') : [ERPOutboundNo]) : []
    },
    findCacheParent(p) {
      if (p && p.parent && p.parent.tag) {
        if (p.parent.tag.endsWith('keep-alive')) {
          return p.parent
        } else {
          return this.findCacheParent(p.parent)
        }
      }
    },
    /**
     * 删除编辑界面缓存数据
     * @param {*} pageName
     */
    clearEditPageChaceData(pageName) {
      if (!pageName) {
        console.log('pageName为空')
        return
      }
      let vnode = this.$root.cacheThis.$vnode
      if (!vnode) {
        console.log('没有找到vnode')
        return
      }
      let parent = this.findCacheParent(vnode)
      if (!parent) {
        // console.log('没有找到大的parent', this.$route)
        parent = this.findCacheParent(this.$parent.$vnode)
      }

      if (!parent) {
        console.log('没有找到parent')
        return
      }
      let cache = parent.componentInstance.cache
      let cacehKeys = parent.componentInstance.keys
      if (cache) {
        // console.log('删除这里', cache)
        let dKey = null
        Object.keys(cache).forEach((key, i, arr) => {
          if (cache[key] && cache[key].name && cache[key].name == pageName) {
            dKey = key
          }
        })
        let toast = '删除了'
        if (dKey) {
          delete cache[dKey]
          toast += 'cache /'
        }
        let index = cacehKeys.indexOf(dKey)
        if (index > -1) {
          cacehKeys.splice(index, 1)
          toast += ' 和 cacehKeys / ' + dKey + '缓存'
        } else {
          console.log('没在caceh中找到该页面直接进行重置')
          this.$root.$emit('reloadRouter')
        }
        if (toast != '删除了') {
          console.log(toast)
        }
      }
    },
  },
}
</script>

<style scoped lang="less">
.group-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
.operate-btn {
  margin-left: 10px;
}
.operate-btn:last-child {
  margin-right: 0px;
}
/deep/.table .ant-table {
  border: none;
  border-radius: 4px;
}
.tm {
  color: white;
  opacity: 0.01;
}
/* 设置table行的高度防止错位 */
// /deep/.ant-table-tbody tr {
//   height: 46px;
// }
/deep/ .ant-table-fixed-header .ant-table-scroll .ant-table-header {
  margin-bottom: 0px;
  padding-bottom: 0px;
  overflow: hidden;
}
</style>
