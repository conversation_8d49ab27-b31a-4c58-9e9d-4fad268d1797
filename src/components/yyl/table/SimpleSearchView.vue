<!--
 * @Author: LP
 * @Description: 公用列表界面搜索条件组件
 * @Date: 2023/07/06
-->
<template>
  <a-card :bodyStyle="{ padding: '12px 12px 0 12px' }" style="margin-bottom: 12px" :headStyle="{
      padding: title ? '10px 12px' : '0 0',
    }">
    <template slot="title" v-if="title">
      {{ title }}
    </template>
    <div class="table-page-search-wrapper customer-list">
      <a-form layout="inline">
        <a-row :gutter="10">
          <div v-for="(item, index) in searchParamsList" :key="index">
            <a-col :md="queryCol" v-if="isSearchMoreShow ? true : index < defaultLine * searchColumnsNumber - 1">
              <a-form-item :label="item.name" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <!-- 输入框 -->
                <!--例子 { name: '客户名称', type: 'input', value: '', key: 'Name', defaultVal: '', placeholder: '请输入客户名称' } -->
                <a-input v-if="item.type == senum.INPUT" :placeholder="item.placeholder ? item.placeholder : '请输入'" v-model="queryParam[item.key]" :maxLength="item.maxLength || 100" @change="
                    (e) => {
                      // if (item.isMust == true && !e.target.value) {
                      //   $message.warning('请输入' + item.name)
                      //   queryParam[item.key] = item.defaultValue
                      // }
                      $forceUpdate()
                    }
                  " />

                <!-- 时间选择框 -->
                <!--例子 { name: '创建时间', type: 'timeInput', value: '', key: ['StartTime', 'EndTime'], defaultVal: ['', ''], placeholder: ['开始日期', '结束日期'] } -->
                <a-range-picker v-if="item.type == senum.DATE" :allowClear="!item.cannotClear" v-model="item.rangeDate" :placeholder="item.placeholder ? item.placeholder : ['开始时间', '结束时间']" @change="(value, options) => onDateTimeChange(value, options, item.key, item.keyEnd,item)" />

                <!-- 选择框 本地数据 -->
                <!-- 例子 { name: '售后场景',type: 'select',value: '',key: 'AuditState',defaultVal: [{ title: '待审核', value: 1 },{ title: '审核通过', value: 2 },],placeholder: '请选择'}, -->
                <a-select v-if="item.type == senum.SELECT" :placeholder="item.placeholder ? item.placeholder : '请选择'" @change="
                    (val) => {
                      queryParam[item.key] = val
                      $forceUpdate()
                    }
                  " v-model="queryParam[item.key]">
                  <!-- <a-select-option value=""> 请选择 </a-select-option> -->
                  <a-select-option v-for="(r, rIndex) in item.items" :key="rIndex" :value="String(r.value)">
                    {{ r.label }}
                  </a-select-option>
                </a-select>

                <!-- 基础枚举版选择框 -->
                <!-- 例子 {name: '售后类型', type: 'selectBasicLink', vModel: 'SaleAfterType', dictCode: 'EnumSaleAfterType', defaultVal: '', placeholder: '请选择'}, -->
                <EnumSingleChoiceView v-if="item.type == senum.ENUM" style="width: 100%" :placeholder="item.placeholder ? item.placeholder : '请选择'" :dictCode="item.dictCode" :notOption="item.notOption" v-model="queryParam[item.key]" @change="
                    () => {
                      $forceUpdate()
                    }
                  " />

                <!--枚举多选 -->
                <EnumMultipleChoiceView v-if="item.type == senum.ENUM_M" style="width: 100%" :placeholder="item.placeholder ? item.placeholder : '请选择'" :dictCode="item.dictCode" :notOption="item.notOption" v-model="queryParam[item.key]" @change="
                    () => {
                      $forceUpdate()
                    }
                  " />

                <!-- 调用接口的单选框 -->
                <!-- 例子 {name: '售后场景2', type: 'selectBaseLink', vModel: 'SaleAfter', defaultVal: '',dataKey:{name: 'Name', value: 'Id'}, placeholder: '请选择', url: '/Application/List', httpHead: 'P34201'} -->
                <SingleChoiceView v-if="item.type == senum.RADIO" style="width: 100%" :placeholder="item.placeholder ? item.placeholder : '请选择'" :Url="item.url" :httpHead="item.httpHead" v-model="queryParam[item.key]" :dataKey="item.dataKey ? item.dataKey : { name: 'Name', value: 'Id' }" :httpParams="{ pageindex: 1, pagesize: 1000, ...(item.params || {}) }" :notOption="item.notOption" @change="
                    () => {
                      $forceUpdate()
                    }
                  " />

                <!--调用接口的多选框-->
                <MultipleChoiceView v-if="item.type == senum.CHECKBOX" style="width: 100%" :placeholder="item.placeholder ? item.placeholder : '请选择'" :Url="item.url" :httpHead="item.httpHead" v-model="queryParam[item.key]" :dataKey="item.dataKey ? item.dataKey : { name: 'Name', value: 'Id' }" :httpParams="{ pageindex: 1, pagesize: 1000, ...(item.params || {}) }" @change="
                    () => {
                      $forceUpdate()
                    }
                  " />

                <!--带搜索共的单选框 例子 {name: '售后场景', type: 'selectLink', key: 'SaleAfterSceneId',searchKey:'搜索参数名称' defaultVal: '',dataKey:{name: 'Name', value: 'Id'}, placeholder: '请选择', url: '/SaleAfterV2/GetSaleAfterSceneList', httpHead: ''} -->
                <SingleChoiceSearchView v-if="item.type == senum.RADIO_SEARCH" style="width: 100%" placeholder="请选择" :httpParams="item.params || {}" :httpHead="item.httpHead" :keyWord="item.searchKey" :Url="item.url" :dataKey="item.dataKey ? item.dataKey : { name: 'Name', value: 'Id' }" v-model="queryParam[item.key]" @change="
                    () => {
                      $forceUpdate()
                    }
                  " />

                <!--带搜索共的多选框 例子 {name: '售后场景', type: 'selectLink', key: 'SaleAfterSceneId', defaultVal: '', dataKey:{name: 'Name', value: 'Id'},placeholder: '请选择', url: '/SaleAfterV2/GetSaleAfterSceneList', httpHead: ''} -->
                <MultipleChoiceSearchView v-if="item.type == senum.CHECKBOX_SEARCH" style="width: 100%" placeholder="请选择" :httpParams="{ saleAfterTypes: '0,1,2' }" :httpHead="item.httpHead" :Url="item.url" :dataKey="item.dataKey ? item.dataKey : { name: 'Name', value: 'Id' }" v-model="queryParam[item.key]" @change="
                    () => {
                      $forceUpdate()
                    }
                  " />
                <SelectTreeView v-if="item.type == senum.SELECTTREE && refreshView" style="width: 100%" :placeholder="item.placeholder ? item.placeholder : '请选择'" :Url="item.url" v-model="queryParam[item.key]" :httpHead="item.httpHead" :disabled="item.disabled" :dataKey="item.dataKey" @change="
                    (record) => {
                      queryParam[item.key] = record.value
                      $forceUpdate()
                    }
                  " />

                <!-- 选择地区 -->
                <SelectAreaView style="width: 100%" v-if="item.type == senum.AREA && refreshView" v-model="queryParam[item.key]" @change="(val, node) => cityAreasChange(val, node, item)" />
              </a-form-item>
            </a-col>
          </div>

          <a-col :md="searchShowCol">
            <span style="float: right; overflow: hidden; margin-bottom: 12px" class="table-page-search-submitButtons">
              <a-button @click="searchReset()" icon="reload" style="margin-right: 8px" v-if="showReset">重置</a-button>
              <a-button type="primary" @click="searchQuery(null, 1)" icon="search">{{ searchText }}</a-button>

              <a-button v-if="showModeBtn" type="link" :icon="isSearchMoreShow ? 'up' : 'down'" @click="isSearchMoreShow = !isSearchMoreShow">{{ !isSearchMoreShow ? '展 开' : '收 起' }}</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-card>
</template>

<script>
import SEnum from './SearchItemEnum'
export default {
  name: 'SimpleSearchView',
  props: {
    /**
     * 搜索条件组件定义数据
     */
    searchParamsList: {
      type: Array,
      default: () => {
        return []
      },
    },
    /**
     * 一个输入框的col值
     */
    queryCol: {
      type: Number,
      default: () => {
        return 6
      },
    },
    /***
     * 默认展开几行
     */
    defaultLine: {
      type: Number,
      default: () => {
        return 2
      },
    },
    /**
     * 是否显示重置按钮
     */
    showReset: {
      type: Boolean,
      default: () => {
        return true
      },
    },
    /**
     * 查询按钮文本
     */
    searchText: {
      type: String,
      default: () => {
        return '查询'
      },
    },
    /**
     * 标题，默认空字符串-不显示
     */
    title: {
      type: String,
      default: () => {
        return ''
      },
    },
  },

  data() {
    return {
      labelCol: {
        xs: { span: 8 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 16 },
        sm: { span: 19 },
      },
      senum: SEnum,
      queryParam: {},
      cityAreas: [],
      // rangeDate:[],
      refreshView: true,
      isSearchMoreShow: false, //是否展示展开收起
      parent: null,
    }
  },
  computed: {
    searchShowCol() {
      if (this.showModeBtn && !this.isSearchMoreShow) {
        return this.queryCol
      }
      let num = 24 / this.queryCol
      let size = this.searchParamsList.length % num
      let count = size > 0 ? num - size : num
      console.log('num = ' + num + ' size = ' + size)
      return this.queryCol * count
    },
    showModeBtn() {
      let size = (24 / this.queryCol) * this.defaultLine
      const bool = this.searchParamsList.length >= size
      return bool
    },
    searchColumnsNumber() {
      return 24 / this.queryCol
    },
  },
  created() {
    this.findParent(this)
  },
  mounted() {
    this.initData()
    this.initDefaultValue(false)
  },
  activated() {
    if (this.parent && this.parent.searchViewInitData == true) {
      this.searchQuery()
    }
  },
  methods: {
    // 重置日期时间
    initData() {
      this.searchParamsList.forEach((item, index) => {
        item.dValue = null
        if (item.type == this.$SEnum.DATE) {
          if (!item.rangeDate) {
            this.$set(item, 'rangeDate', [])
            item.initRangeDate = []
          } else if (item.rangeDate && item.rangeDate.length > 0) {
            // 是否有默认值的时间
            item.initRangeDate = item.rangeDate || []
          }
        }
      })
    },
    /**
     * 获取查询参数
     */
    getQueryParam() {
      let param = JSON.parse(JSON.stringify(this.queryParam))
      Object.keys(param).forEach((key) => {
        let item = this.searchParamsList.find((item) => item.key == key)
        //dataType目前固定这俩货 int  boolean
        if (item && item.dataType) {
          param[key] = param[key] === '' ? '' : JSON.parse(param[key])
        }
      })
      return param
    },

    /**
     * 选择地址区域
     **/
    cityAreasChange(val, node, item) {
      if (!val) {
        return
      }
      this.cityAreas = val
      if (typeof node != 'undefined') {
        this.queryParam[item.key] = node[node.length - 1].code
      } else {
        this.queryParam[item.key] = null
      }
      this.$forceUpdate()
    },
    onDateTimeChange(dates, dateStrings, startKey, endKey, item) {
      const isArry = dates.length == 2
      const searchStartkey = endKey ? startKey : startKey + 'Begin'
      const searchEndkey = endKey ? endKey : startKey + 'End'
      this.queryParam[searchStartkey] = isArry ? dateStrings[0] : null
      this.queryParam[searchEndkey] = isArry ? dateStrings[1] : null
      console.log(this.queryParam)
      this.$forceUpdate()
    },
    changeSingleChoice(val, txt, item) {
      this.$emit('changeSingleChoice', val, txt, item)
    },

    /**
     * 查询事件
     * @param {*扩展参数 包含页码 ipagination：{} 可添加其他属性} extendParams
     * @param {* 搜索类型 1查询点击 2重置点击 非1 2 else 其他} searchType
     */
    searchQuery(extendParams, searchType) {
      this.$emit('search', this.queryParam, extendParams, searchType)
    },
    searchReset() {
      if (this.parent) {
        this.parent.clearCurPageCacheSearchData()
      }
      this.$emit('reset')
      this.queryParam = {}
      this.searchParamsList.forEach((item, index) => {
        item.dValue = null
        if (item.type == this.senum.DATE && (!item.initRangeDate || item.initRangeDate.length === 0)) {
          item.rangeDate = []
        }
      })
      this.cityAreas = []
      this.initDefaultValue()
      this.refresh()
      this.$emit('search', this.queryParam, null, 2)
    },
    refresh() {
      this.refreshView = false
      this.$nextTick(() => {
        this.refreshView = true
      })
    },

    /**
     * 初始化查询条件默认值
     */
    initDefaultValue(isCaceh, callback) {
      this.searchParamsList.forEach((x) => {
        if (x.type != this.senum.DATE) {
          let value = isCaceh ? x.dValue : x.defaultValue
          if (value != undefined) {
            if (value instanceof String) {
              this.queryParam[x.key] = value
            } else if (value instanceof Boolean) {
              this.queryParam[x.key] = String(value)
            } else {
              this.queryParam[x.key] = value
            }
          }
        } else {
          if (x.initRangeDate && x.initRangeDate.length > 0 && x.initRangeDate.length == 2) {
            x.rangeDate = x.initRangeDate
            if (x.keyEnd) {
              this.queryParam[x.key] = x.initRangeDate[0]
              this.queryParam[x.keyEnd] = x.initRangeDate[1]
            } else {
              this.queryParam[x.key + 'Begin'] = x.initRangeDate[0]
              this.queryParam[x.key + 'End'] = x.initRangeDate[1]
            }
          }
        }
      })
      // console.log('初始化查询条件默认值', this.queryParam)
      this.$forceUpdate()
      callback && callback()
    },
    /**
     * 查找 $parent
     * @param {*} p
     */
    findParent(p) {
      if (p && p.$parent && p.$parent.IsListMixin != undefined) {
        this.parent = p.$parent
      } else {
        if (p.$parent) {
          this.findParent(p.$parent)
        }
      }
    },
  },
}
</script>

<style scoped lang="less">
/deep/.ant-card-head-title {
  padding: 0 0;
}
</style>
