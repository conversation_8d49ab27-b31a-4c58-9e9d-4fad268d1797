<!--
 * @Author: LP
 * @Description: 公用table组件
 * @Date: 2023/07/06
-->
<template>
  <a-card
    v-if="tab"
    :bordered="showCardBorder"
    :bodyStyle="{ padding: showCardBorder ? '12px' : '0' }"
    :headStyle="{
      padding:
        tabDataList.length > 0 ||
        (tab.tabTitles && tab.tabTitles.length > 0) ||
        (tab.operateBtns && tab.operateBtns.length > 0)
          ? '10px 16px'
          : '0 0',
    }"
  >
    <div slot="title" v-if="tabDataList.length > 0 || (tab.tabTitles && tab.tabTitles.length > 0)">
      <template v-if="tabDataList.length > 0">
        <a-radio-group v-model="tab.curStatus" @change="onTabChange">
          <a-radio-button v-for="(item, index) in tabDataList" :value="item[tab.tabValueKey || 'value']" :key="index"
            >{{ item.name != undefined ? item.name : item.Name }}({{
              item.count != undefined ? item.count : item.Count
            }})</a-radio-button
          >
        </a-radio-group>
      </template>
      <template v-else-if="tab.tabTitles && tab.tabTitles.length > 0">
        <span style="margin-right: 20px" v-for="(title, index) in tab.tabTitles" :key="index">{{ title }}</span>
      </template>
    </div>
    <div slot="extra" v-if="tab.operateBtns && tab.operateBtns.length > 0">
      <slot name="operateBtnBefore"></slot>
      <template v-if="tab.operateBtns.length > 0">
        <template v-for="(item, index) in tab.operateBtns">
          <YYLButton
            v-if="item.isShow == undefined || item.isShow == true"
            :key="index"
            :menuId="item.id"
            :loading="item.loading"
            :text="item.name"
            :type="item.type"
            :icon="item.icon"
            v-show="!item.isHide"
            :class="{ 'operate-btn': index == 0 }"
            @click="onOperateClick(item.key)"
          />
        </template>
      </template>
      <slot name="operateBtnAfter"></slot>
    </div>
    <!-- 数据列表 -->
    <a-table
      :bordered="tab.bordered == true ? true : false"
      ref="table"
      :rowKey="tab.rowKey ? tab.rowKey : (record, index) => index"
      :class="tab.bordered == true ? 'table' : ''"
      :columns="
        tab.filterColumns ? columns.filter((item) => item.isShow == undefined || item.isShow(item) === true) : columns
      "
      :size="'middle'"
      :dataSource="dataList"
      :pagination="tab.showPagination == false || !parent ? false : parent.ipagination"
      :loading="parent ? parent.loading : false"
      :expandedRowKeys.sync="expandedRowKeys"
      :rowClassName="tab.rowClassName"
      :rowSelection="
        tab.selectType
          ? {
              selectedRowKeys: parent ? parent.selectedRowKeys : [],
              onChange: (sRowKeys, sRows) => {
                if (parent && parent.onSelectChange) {
                  parent.onSelectChange(sRowKeys, sRows, tab.rowKey || 'Id')
                }
              },
              onSelect: (record, selected, selectedRows) => {
                if (parent && parent.onOneSelectChange) {
                  parent.onOneSelectChange(record, selected, selectedRows, tab.rowKey || 'Id')
                }
              },
              type: tab.selectType,
              onSelectAll: (selected, selectedRows, changeRows) => {
                if (parent && parent.onSelectAllChange) {
                  parent.onSelectAllChange(selected, selectedRows, changeRows, tab.rowKey || 'Id')
                }
              },
            }
          : null
      "
      @change="(pag, filters, sorter) => onTableChange(pag, filters, sorter)"
      :scroll="{
        y: tab.scrollY ? tab.scrollY : this.$root.tableHeight,
        x: tab.scrollX ? tab.scrollX : tableWidth(),
      }"
    >
      <!-- 普通文本 -->
      <span slot="component" slot-scope="text">
        <j-ellipsis :value="typeof text == 'number' ? '' + text : text || '--'" :length="100" />
      </span>
      <!-- 数字 -->
      <span slot="number" slot-scope="text">
        <j-ellipsis :value="text == null ? '0' : text >= 0 || text < 0 ? '' + text : '--'" :length="10" />
      </span>
      <!-- 序号 -->
      <span slot="Order" slot-scope="text, record, index">
        {{ getOrder(record, index) }}
      </span>
      <!-- 布尔值 -->
      <template slot="bool" slot-scope="text, record, index, column">
        <span v-if="[true, 'true'].includes(text)">是</span>
        <span v-else-if="[false, 'false'].includes(text)"> 否</span>
        <span v-else>{{ text == 'undefined' ? '--' : text }}</span>
      </template>
      <!-- 状态 -->
      <span slot="state" slot-scope="text, record, index, column">
        <!-- <a-tag v-if="text" :color="column.colors || ''">{{ text }}</a-tag>
        <span v-else>--</span> -->
        <j-ellipsis :value="text || '--'" :length="100" />
      </span>
      <!-- 价格 precision 保留机位小数 默认俩位-->
      <span slot="price" slot-scope="text, record, index, column">
        <j-ellipsis
          :value="
            text == undefined || text == null
              ? '--'
              : (text < 0 ? '-' : '') +
                '￥' +
                Number(text < 0 ? String(text).replace('-', '') : text).toFixed(column.precision || 2)
          "
        />
      </span>
      <!-- 百分比 -->
      <span slot="percent" slot-scope="text, record, index, column">
        <j-ellipsis :value="text || text == 0 ? text + '%' : '--'" />
      </span>
      <!-- 日期 -->
      <span slot="date" slot-scope="text">
        <j-ellipsis :value="text ? moment(text).format('YYYY-MM-DD') : ' -- '" :length="100" />
      </span>
      <!-- 输入框 size="small"-->
      <span slot="input" slot-scope="text, record, index, column">
        <a-input
          placeholder="请输入"
          v-model="record[column.dataIndex]"
          :maxLength="50"
          style="width: 100%"
          @blur="() => (column.onBlur ? column.onBlur(null, column.dataIndex, record, index) : {})"
          @pressEnter="(e) => (column.onEnter ? column.onEnter(e, column.dataIndex, record, index) : {})"
          @change="
            (e) => (column.onInputChange ? column.onInputChange(e.target.value, column.dataIndex, record, index) : {})
          "
        />
      </span>
      <!-- 输入框 size="small"-->
      <span slot="inputNumber" slot-scope="text, record, index, column">
        <a-input-number
          style="width: 100%"
          v-model="record[column.dataIndex]"
          placeholder="请输入"
          :min="column.min || 0"
          :max="column.max || 999999999"
          :disabled="
            column.isDisabled !== undefined
              ? column.isDisabled(text, record, index, column)
              : column.disabled == true
              ? true
              : false
          "
          :precision="column.precision >= 0 ? column.precision : 2"
          @blur="() => (column.onBlur ? column.onBlur(null, column.dataIndex, record, index) : {})"
          @pressEnter="(e) => (column.onEnter ? column.onEnter(e, column.dataIndex, record, index) : {})"
          @change="(e) => (column.onInputChange ? column.onInputChange(e, column.dataIndex, record, index) : {})"
        />
      </span>
      <!-- 是否活动 -->
      <template slot="isValid" slot-scope="text, record, index, column">
        <a-switch
          v-if="!column.menuId || (column.menuId && parent.checkBtnPermissions(column.menuId))"
          v-model="record.IsValid"
          :loading="record.loading"
          @change="(checked, event) => onSetValid(record, checked, event)"
        />
        <span v-else>{{ text ? column.trueText || '是' : column.falseText || '否' }}</span>
      </template>
      <!-- 设置序号 -->
      <template slot="sort" slot-scope="text, record, index, column">
        <a-input-number v-model="record.Sort" v-if="record.isSortEdit" @keydown.enter="onSetSort(record)" />
        <span v-else>{{ text }}</span>
        <a style="margin-left: 6px">
          <a-icon
            type="edit"
            title="编辑"
            v-if="!record.isSortEdit"
            @click="
              () => {
                record.isSortEdit = true
                $forceUpdate()
              }
            "
          />
          <a-icon type="enter" v-else @click="onSetSort(record)" title="确定" />
        </a>
      </template>
      <!-- 自定义插槽 -->
      <span
        :slot="item"
        v-for="(item, index) in tab.customSlot || []"
        :key="index"
        slot-scope="text, record, index, column"
      >
        <slot :name="item" :record="record" :text="text" :index="index" :column="column"></slot>
      </span>
      <!-- 操作 -->
      <span slot="action" slot-scope="text, record, index, column">
        <span v-for="(item, i) in column.actionBtns || []" :key="i">
          <!-- 根据条件隐藏显示 -->
          <span style="margin: 0 5px; line-height: 18px" v-if="actionBtnIsShow(item, record)">
            <a
              @click="onActionClick(record, item.name, index)"
              :disabled="typeof item.disabled === 'function' ? item.disabled(record) : item.disabled"
            >
              <a-icon v-if="item.icon" :type="getIcon(record, item)" />
              {{ item.name }}
            </a>
          </span>
        </span>
        <span
          class="tm"
          v-if="column.actionBtns.filter((btn) => btn.isShow == undefined || btn.isShow(record) == true).length == 0"
          >-</span
        >
      </span>
    </a-table>
    <!-- 底部按钮 -->
    <slot name="bottomView"></slot>
  </a-card>
</template>

<script>
import moment from 'moment'
const JEllipsis = () => import('@/components/jeecg/JEllipsis')
export default {
  name: 'TableView',
  mixins: [],
  components: {
    JEllipsis,
  },
  props: {
    /**
     * table顶部左边tab状态数据
     */
    tabDataList: {
      type: Array,
      default: () => {
        return [
          // {
          //   name: '全部',
          //   value: 'all',
          //   count: '',
          //   key:'',//数量对应字段
          // }
        ]
      },
    },
    /**
     * 整个table列表的配置项-必填
     */
    tab: {
      type: Object,
      required: true,
      default: () => {
        return {
          curStatus: 'all', //tab默认值，当前状态值
          tabStatusKey: '', //tab切换查询条件属性值
          tabStatusList: [],
          tabTitles: [], //tab左上角显示的文字提醒 字符串数组  和tabStatusList互斥
          operateBtns: [
            // {
            //   name: '申请付款',
            //   type: 'primary',
            //   icon: 'plus',
            //   key: '申请付款',
            //   id: '697e3315-8aee-4e70-8f94-e4f992100212'  //按钮 id
            // }
          ], //右上角按钮集合
          bordered: true, //是否显示表格的边框
          rowKey: '',
          showPagination: true, //是否显示分页
          selectType: '', //选择类型 radio单选 checkbox多选 空为不支持选择
          filterColumns: null, //是否筛选columns
        }
      },
    },

    /**
     * 是否显示边框
     */
    showCardBorder: {
      type: Boolean,
      default: false,
    },
    /**
     * 表头
     */
    columns: {
      type: Array,
      required: true,
      default: () => {
        return [
          // {
          //   //常规设置之外、如下
          //   colors: [], //state 和 yesno状态显示的颜色值
          //   menuId: '', //列配置的菜单id
          //   trueText: '是', //isValid 无权限 时true的显示文本
          //   falseText: '否', //isValid 无权限 时false的显示文本
          //   actionBtns: [
          //     // {
          //     //   name: '详情',
          //     //   id: '0b79bc0f-c260-49ed-8034-3208b2fe0294', //按钮 id
          //     //   isShow: record => {  //是否显示的验证方法
          //     //     return true
          //     //   }
          //     // }
          //   ] //列表按钮集合
          // }
        ]
      },
    },
    /**
     * table数据
     */
    dataList: {
      type: Array,
      required: true,
      default: () => {
        return []
      },
    },
    // parent: {
    //   type: Object,
    //   default: () => {
    //     return null
    //   },
    // },
  },
  data() {
    return {
      parent: null,
      expandedRowKeys: [],
    }
  },
  watch: {
    //深度监听 测试可用
    // tabDataList: {
    //   handler(newVal, oldVal) {
    //     console.log('tabDataList 变化了 刷新界面哦')
    //     this.refreshTabDataView()
    //   },
    //   deep: true
    // }
  },
  filters: {},
  computed: {
    actionBtnIsShow() {
      return function (item, record) {
        //是否有按钮权限优先判断
        if (this.parent && !this.parent.checkBtnPermissions(item.id)) {
          return false
        }
        if (item.isShow) {
          // 特殊条件显示隐藏
          if (item.isShow(record) === true) {
            return true
          } else {
            return false
          }
        } else {
          // 未设置条件直接显示按钮
          return true
        }
      }
    },
  },
  mounted() {
    this.findParent(this)
  },
  created() {},
  activated() {
    this.initLoadData()
  },
  methods: {
    moment,
    loadTableData(pageIndex) {
      if (this.parent) {
        this.parent.loadData(pageIndex)
      }
    },
    initLoadData() {
      if (!this.parent) {
        this.findParent(this)
      }
      if (this.parent.useParentActivated) {
        return
      }
      const name = this.$route.name
      let reg = /List$/
      if (reg.test(name)) {
        this.parent.loadData()
      }
    },
    initExpandedRowKeys(key) {
      this.expandedRowKeys = []
      this.expandedRowKeys.push(key)
    },

    tableLoadBefore() {
      if (
        this.tab &&
        this.tab.tabStatusKey &&
        this.tab.curStatus != null &&
        this.tab.curStatus != undefined &&
        this.parent
      ) {
        this.parent.queryParam[this.tab.tabStatusKey] = this.tab.curStatus
      }
    },
    onTabChange() {
      this.$emit('onTabChange', this.tab.curStatus)
      // if (this.parent) {
      //   this.parent.searchQuery(null, null, 1)
      // }
      this.loadTableData(1)
    },
    getOrder(record, index) {
      if (this.parent && this.parent.ipagination.current > 1) {
        return (this.parent.ipagination.current - 1) * this.parent.ipagination.pageSize + (parseInt(index) + 1)
      } else {
        return parseInt(index) + 1
      }
    },
    findParent(p) {
      if (p && p.$parent && p.$parent.IsListMixin != undefined) {
        this.parent = p.$parent
      } else {
        this.findParent(p.$parent)
      }
    },
    onTableChange(pag, filters, sorter) {
      if (this.parent) {
        this.parent.handleTableChange(pag, filters, sorter)
      }
    },
    /**
     * 右上角操作事件
     * @param {*} key
     */
    onOperateClick(key) {
      this.$emit('operateClick', key)
    },
    /**
     * table列action点击事件
     * @param {*关键字} key
     * @param {*数据} record
     * @param {*当前列索引} index
     */
    onActionClick(record, key, index) {
      // this.$root.clearCurPageCache = true
      this.$emit('actionClick', record, key, index)
    },
    // 设置是否有效
    onSetValid(item, checked) {
      this.$emit('onSetValid', item, checked)
    },
    onSetSort(record) {
      if (this.parent) {
        this.parent.handleSortBlur(record)
      }
    },
    getIcon(record, action) {
      const { icon } = action
      if (typeof icon === 'function') {
        return icon(record)
      }

      return icon
    },
    /**
     * 刷新状态tab界面
     */
    refreshTabDataView() {
      // this.tabCountIsChange = false
      // this.$nextTick(() => {
      //   this.tabCountIsChange = true
      // })
    },
    tableWidth() {
      let w = 0
      this.columns.forEach((x) => {
        if (x.width >= 0) {
          w += x.width
        }
      })
      if (w == 0 || w < 1300) {
        w = '100%'
      }

      // console.log('table width = ' + w)
      return w
    },
    findCacheParent(p) {
      if (p && p.parent && p.parent.tag) {
        if (p.parent.tag.endsWith('keep-alive')) {
          return p.parent
        } else {
          return this.findCacheParent(p.parent)
        }
      }
    },
    /**
     * 删除编辑界面缓存数据
     * @param {*} pageName
     */
    clearEditPageChaceData(pageName) {
      if (!pageName) {
        console.log('pageName为空')
        return
      }
      let vnode = this.$root.cacheThis.$vnode
      if (!vnode) {
        console.log('没有找到vnode')
        return
      }
      let parent = this.findCacheParent(vnode)
      if (!parent) {
        // console.log('没有找到大的parent', this.$route)
        parent = this.findCacheParent(this.$parent.$vnode)
      }

      if (!parent) {
        console.log('没有找到parent')
        return
      }
      let cache = parent.componentInstance.cache
      let cacehKeys = parent.componentInstance.keys
      if (cache) {
        // console.log('删除这里', cache)
        let dKey = null
        Object.keys(cache).forEach((key, i, arr) => {
          if (cache[key] && cache[key].name && cache[key].name == pageName) {
            dKey = key
          }
        })
        let toast = '删除了'
        if (dKey) {
          delete cache[dKey]
          toast += 'cache /'
        }
        let index = cacehKeys.indexOf(dKey)
        if (index > -1) {
          cacehKeys.splice(index, 1)
          toast += ' 和 cacehKeys / ' + dKey + '缓存'
        } else {
          console.log('没在caceh中找到该页面直接进行重置')
          this.$root.$emit('reloadRouter')
        }
        if (toast != '删除了') {
          console.log(toast)
        }
      }
    },
  },
}
</script>

<style scoped lang="less">
.group-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
.operate-btn {
  margin-left: 10px;
}
.operate-btn:last-child {
  margin-right: 0px;
}
/deep/.table .ant-table {
  border: none;
  border-radius: 4px;
}
/deep/.ant-card-head-title {
  padding: 0 0;
}
.tm {
  color: white;
  opacity: 0.01;
}
/deep/ .ant-table-fixed-header .ant-table-scroll .ant-table-header {
  margin-bottom: 0px;
  padding-bottom: 0px;
  overflow: hidden;
}
</style>
