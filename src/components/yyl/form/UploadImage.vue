<!--
 * @Description: 图片上传组件
 * @Version: 1.0
 * @Author: Harmoni
 * @Date: 2025-02-19 16:45:18
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-03-25 09:53:44
-->

<template>
  <div class="custom-tailwind">
    <a-upload
      list-type="picture-card"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :multiple="multiple"
      :accept="accept"
      @preview="handlePreview"
      @change="handleChange"
    >
      <div v-if="fileList.length < max">
        <a-icon type="plus" />
        <div class="ant-upload-text">
          上传图片
        </div>
      </div>
    </a-upload>
    
    <!-- 图片预览弹窗 -->
    <a-modal :visible="previewVisible" :width="750" :footer="null" @cancel="previewVisible = false">
      <div class="w-full" v-viewer="{
        inline: true,
        fullscreen: true,
        navbar: false,
        title: false,
        button: false,
        toolbar: {
          zoomIn: true,
          zoomOut: true,
          reset: true,
          rotateLeft: true,
          rotateRight: true,
          flipHorizontal: true,
          flipVertical: true,
        }}" style="height: 750px">
        <img 
          v-for="(src, index) in previewImageArr"
          key="index"
          class="w-full h-full"
          :src="src" 
          :id="`img${index}`" 
          style="display: none;" 
          alt="预览图片"/>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'

export default {
  name: 'UploadImage',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    max: { // 最大上传数量
      type: Number,
      default: 1 // 默认1张
    },
    accept: { // 可接受的文件类型
      type: String,
      default: 'image/*' // 默认只接受图片
    },
    maxFileSize: { // 最大单张上传图片大小（MB）
      type: Number,
      default: 20 // 默认20MB
    },
    multiple: { // 是否支持多图上传
      type: Boolean,
      default: false // 默认单图上传模式
    },
    bucketName: { // 桶名
      type: String,
      default: window._CONFIG.AliOssConfig['BaseBucketName']
    },
    dir: { // 保存路径
      type: String,
      default: window._CONFIG.AliOssConfig['CommonDir']
    },
    onChangeCallback: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      previewVisible: false,
      previewImageArr: [],
      hasGetWebUploadPolicy: false // 是否获取过 OSS web 上传代理
    }
  },
  computed: {
    fileList: {
      get() {
        console.log('fileList', this.value)
        return this.value
      },
      set(val) {
        this.$emit('change', val)
      }
    }
  },
  methods: {
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/')
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      const isOverLimit = file.size / 1024 / 1024 > this.maxFileSize
      if (isOverLimit) {
        this.$message.error(`图片必须小于 ${this.maxFileSize}MB!--${file.name}`)
        return false
      }
      return true
    },
    handleChange({ file, fileList }) { // 图片列表改变
      if (!file) return

      const { status } = file
      if (status === 'uploading') { // 上传图片
        this.handleUpload(file)
      } else if (status === 'removed') { // 删除图片
        this.fileList = fileList
      }
    },
    async getWebUploadPolicy() { // 获取 OSS web 上传代理信息
      const { Data } =  await getAction('/Oss/GetWebUploadPolicy', { bucketName: this.bucketName, dir: this.dir }, 'OSS' )
      if (!Data || !Data.signature) return

      const realBucketName = Data.bucketName || this.bucketName
      this.realBucketName = realBucketName
      !window._CONFIG.AliOssConfig[realBucketName] && (window._CONFIG.AliOssConfig[realBucketName] = {})
      !window._CONFIG.AliOssConfig[realBucketName][this.dir] && (window._CONFIG.AliOssConfig[realBucketName][this.dir] = {})

      const { policy, accessid: OSSAccessKeyId, signature, host, expire, callback } = Data
      Object.assign(window._CONFIG.AliOssConfig[realBucketName][this.dir], { 
        OSSAccessKeyId,
        policy,
        signature,
        host,
        expire,
        callback,
        dir: Data.dir || this.dir
      })

      this.hasGetWebUploadPolicy = true
    },
    async handleUpload(file) { // 图片上传
      !this.hasGetWebUploadPolicy && await this.getWebUploadPolicy()

      const formData = new FormData()
      const { OSSAccessKeyId, policy, signature, callback, dir, host } = window._CONFIG.AliOssConfig[this.realBucketName][this.dir]

      formData.set('OSSAccessKeyId', OSSAccessKeyId)
      formData.set('Signature', signature)
      formData.set('policy', policy)
      formData.set('callback', callback)
      formData.set('key', dir + '/' + new Date().getTime() + '_' + file.name)
      formData.set('success_action_status', '200')
      formData.set('file', file.originFileObj)

      this.$http.post(host, formData).then((res) => {
        const { Status, FileName, FileUrl, FileId, Msg } = res
        if (Status !== 'OK') {
          this.$message.error('文件上传失败' + (Msg || ''))
          return
        }
        this.fileList.push({
          uid: FileId,
          name: FileName,
          status: 'done',
          url: FileUrl
        })
      }, (error) => {
        window._CONFIG.AliOssConfig[bName][Dir] = undefined
        this.$message.error('文件上传出错' + (error.Msg || ''))
      })
    },
    handlePreview(file) {
      this.previewImageArr = [file.url || file.thumbUrl]
      this.previewVisible = true
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ant-upload-list-item {
  padding: 0 !important;
}
</style>