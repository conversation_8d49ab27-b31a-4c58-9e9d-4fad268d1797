<!--
 * @Description: 详情展示组件
 * @Version: 1.0
 * @Author: Harmon<PERSON>
 * @Date: 2025-03-10 19:53:43
 * @LastEditors: Harmoni
 * @LastEditTime: 2025-03-25 09:52:22
-->

<template>
  <div>
    <a-descriptions v-bind="$attrs" class="custom-tailwind">
      <template v-for="item in fields" >
        <a-descriptions-item 
          v-if="isShow(item)"
          :key="item.key"
          v-bind="item"
        >
          <template v-if="item.type === 'img'">
            <img 
              v-for="(item, index) in getImages(item)"
              :key="index"
              class="mr-8 mb-8 object-cover cursor-pointer"
              :src="item"
              :style="{ width: item.width || '100px', height: item.width || '100px' }"
              alt=""
              @click="onPreview(item)">
          </template>
          <template v-else-if="item.type === 'custom'">
            <slot :name="item.key"></slot>
          </template>
          <template v-else>
            {{ getValue(item) }}
          </template>
        </a-descriptions-item>
      </template>
    </a-descriptions>

    <!-- 图片预览弹窗 -->
    <a-modal :visible="previewVisible" :width="750" :footer="null" @cancel="previewVisible = false">
      <div class="w-full" v-viewer="{
        inline: true,
        fullscreen: true,
        navbar: false,
        title: false,
        button: false,
        toolbar: {
          zoomIn: true,
          zoomOut: true,
          reset: true,
          rotateLeft: true,
          rotateRight: true,
          flipHorizontal: true,
          flipVertical: true,
        }}" style="height: 750px">
        <img 
          v-for="(src, index) in previewImageArr"
          key="index"
          class="w-full h-full"
          :src="src" 
          :id="`img${index}`"
          style="display: none;"
          alt="预览图片"/>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { isArray } from 'lodash-es'

export default {
  name: 'YYLDetails',
  props: {
    fields: {
      type: Array,
      default: () => ([])
    },
    detailsData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      previewVisible: false, // 是否显示图片预览弹窗
      previewImageArr: [],
    }
  },
  methods: {
    onPreview(url) {
      this.previewImageArr = [url]
      this.previewVisible = true
    },
    getValue(item) {
      const { key, getVal } = item
      const value = this.detailsData[key]
      if (typeof getVal === 'function') {
        return getVal(value)
      }

      return value || '--'
    },
    getImages(item) {
      const urls = this.getValue(item)
      if (urls !== '--' && !isArray(urls)) {
        return [urls]
      }
      return urls
    },
    isShow(item) { // 获取布尔值
      const { isShow } = item
      if (typeof isShow === 'function') {
        return isShow(item)
      } else if (typeof isShow === 'boolean') {
        return isShow
      }
      return true
    },
  }
}
</script>