<!--
 * @Description: Form 表单
 * @Version: 1.0
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-02-11 14:17:02
 * @LastEditors: haolin.hu
 * @LastEditTime: 2025-05-15 14:44:16
-->

<template>
  <a-form-model
    ref="formViewRef"
    class="custom-tailwind yyl-form"
    :class="[{'mb-20 border-b-grey-90': isSearchMode}]"
    :model="formData"
    :rules="formRules"
    :disabled="disabled"
    :layout="isSearchMode ? 'inline' : layout"
    :labelCol="computedLabelCol"
    :wrapperCol="computedWrapperCol"
    v-bind="$attrs"
  >
    <a-row :gutter="16" type="flex" align="top" >
      <template v-for="(item, index) in formFields" >
        <a-col 
          v-if="getBooleanValue(item, 'isShow')" 
          :md="getItemCol(item, 'md')"
          :sm="getItemCol(item, 'sm')"
          :key="item.key"
        >
          <slot v-if="item.type === 'wholeCustom'" :name="item.key" />

          <a-form-model-item
            v-else
            :class="{'inline-extra': item.inlineExtra}"
            :prop="item.key"
            :label="item.label"
            :style="{ width: item.col || (isSearchMode ? '25%' : 'auto')}"
            v-bind="item"
          >
            <template slot="label">
              <slot :name="item.key + '_label'"></slot>
            </template>
            <!-- input -->
            <a-input
              v-if="item.type == 'input'"
              v-model="formData[item.key]"
              v-bind="item"
              :disabled="getBooleanValue(item, 'disabled')"
              :placeholder="item.placeholder || '请输入'"
              @change="onEvent(item, 'onChange', $event)"
              >
              <template v-if="item.isShowPrefix" slot="prefix">
                <slot :name="item.key + '_prefix'"></slot>
              </template>
              <template v-if="item.isShowSuffix" slot="suffix">
                <slot :name="item.key + '_suffix'"></slot>
              </template>
              <template v-if="item.isShowAddonBefore" slot="addonBefore">
                <slot :name="item.key + '_addonBefore'"></slot>
              </template>
              <template v-if="item.isShowAddonAfter" slot="addonAfter">
                <slot :name="item.key + '_addonAfter'"></slot>
              </template>
            </a-input>
            <!-- textarea -->
            <a-textarea
              v-if="item.type == 'textarea'"
              v-model="formData[item.key]"
              v-bind="item"
              :disabled="getBooleanValue(item, 'disabled')"
              :placeholder="item.placeholder || '请输入'"
              @change="onEvent(item, 'onChange', $event)"
            />
            <!-- number -->
            <a-input-number
              v-if="item.type === 'number'"
              v-model="formData[item.key]"
              v-bind="item"
              :disabled="getBooleanValue(item, 'disabled')"
              :placeholder="item.placeholder || '请输入'"
              @change="onEvent(item, 'onChange', $event)"
            />
            <!-- checkbox -->
            <a-checkbox-group
              v-if="item.type === 'checkbox'"
              v-model="formData[item.key]"
              :options="item.options"
              :disabled="getBooleanValue(item, 'disabled')"
              @change="onEvent(item, 'onChange', $event)" 
            />
            <!-- radio -->
            <a-radio-group
              v-if="item.type === 'radio'"
              v-model="formData[item.key]"
              :options="item.options"
              :disabled="getBooleanValue(item, 'disabled')"
              @change="onEvent(item, 'onChange', $event)"
            />
            <!-- select: 初始化时 传入options -->
            <a-select
              v-if="item.type === 'select'"
              v-model="formData[item.key]"
              v-bind="item"
              :disabled="getBooleanValue(item, 'disabled')"
              :placeholder="item.placeholder || '请选择'"
              @search="onEvent(item, 'onSearch', $event)"
              @change="onEvent(item, 'onChange', $event)"
            >
              <a-spin v-if="getBooleanValue(item, 'loading')" slot="notFoundContent" size="small" />
              <a-select-option
                v-for="option in item.options"
                :key="option.value"
                :value="option.value"
                :label="option.label"
                :disabled="option.disabled"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
            <!-- remoteSelect: 通过接口 获取 options -->
            <RemoteSelect
              v-if="item.type === 'remoteSelect'"
              v-model="formData[item.key]"
              v-bind="item"
              :disabled="getBooleanValue(item, 'disabled')"
              @change="(val, option) => onEvent(item, 'onChange', val, option)"
            />
            <!-- switch -->
            <a-switch
              v-if="item.type === 'switch'"
              v-model="formData[item.key]"
              :disabled="getBooleanValue(item, 'disabled')"
              :loading="getBooleanValue(item, 'loading')"
              @change="onEvent(item, 'onChange', $event)"
            />
            <!-- datePicker -->
            <a-date-picker
              v-if="item.type === 'datePicker'"
              v-model="formData[item.key]"
              v-bind="item"
              :disabled="getBooleanValue(item, 'disabled')"
              @change="onEvent(item, 'onChange', $event)"
              @ok="onEvent(item, 'onOk', $event)"
            />
            <!-- dateRange -->
            <a-range-picker
              v-if="item.type === 'dateRange'"
              v-model="formData[item.key]"
              v-bind="item"
              :disabled="getBooleanValue(item, 'disabled')"
              :format="item.format || 'YYYY-MM-DD'"
              @change="onEvent(item, 'onChange', $event)"
              @ok="onEvent(item, 'onOk', $event)"
            />
            <!-- upload -->
            <UploadImage 
              v-if="item.type === 'upload'" 
              v-model="formData[item.key]"
              v-bind="item"
            />

            <!-- custom -->
            <slot v-if="item.type === 'custom'" :name="item.key" />
            
            <template v-if="item.isShowExtra" slot="extra">
              <slot :name="item.key + '_extra'"></slot>
            </template>

            <template v-if="item.isShowHelp" slot="help">
              <slot :name="item.key + '_help'"></slot>
            </template>
          </a-form-model-item>
        </a-col>
      </template>

      <a-col v-if="isSearchMode" :md="getSearchBtnColSpan()" :sm="24">
        <a-form-model-item :wrapperCol="{ span: 24 }">
          <div class="h-40 flex justify-end items-center">
            <!-- 自定义按钮插槽 -->
            <slot name="buttons"></slot>
            <a-button class="mr-12" @click="onReset()">重置</a-button>
            <a-button type="primary" @click="onSearch()">查询</a-button>
          </div>
        </a-form-model-item>  
      </a-col>
    </a-row>
  </a-form-model>
</template>

<script>
export default {
  name: 'YYLForm',
  model: {
    prop: 'formValue',
    event: 'change',
  },
  props: {
    formValue: { // 绑定的表单数据
      type: Object,
      default: () => ({})
    },
    disabled: { // 是否禁用
      type: Boolean,
      default: false
    },
    formFields: { // 表单字段
      type: Array,
      default: () => []
    },
    formRules: { // 表单验证规则
      type: Object,
      default: () => ({})
    },
    isSearchMode: { // 是否为搜索表单模式
      type: Boolean,
      default: false
    },
    layout: { // 表单布局
      type: String,
      default: 'horizontal',
      validator:  (value) => {
        return ['horizontal', 'vertical', 'inline'].includes(value)
      }
    },
    labelCol: { // 标签布局配置
      type: Object,
      default: null,
    },
    wrapperCol: { // 内容布局配置
      type: Object,
      default: null,
    },
    col: { // 栅格布局配置
      type: Object,
      default: () => ({}),
    }
  },
  data() {
    return {}
  },
  computed: {
    formData: {
      get() {
        return this.formValue
      },
      set(val) {
        this.$emit('change', val)
      }
    },
    computedLabelCol() {
      if (this.layout === 'vertical') return null
      if (this.labelCol) return this.labelCol

      let labelCol = { span: 4 }
      if (this.layout === 'inline') {
        labelCol = { xs: 8, sm: 5 }
      }
      if (this.isSearchMode) {
        labelCol = {}
      }
      return labelCol
    },
    computedWrapperCol() {
      if (this.layout === 'vertical') return null
      if (this.wrapperCol) return this.wrapperCol

      let wrapperCol = { span: 19 }
      if (this.layout === 'inline') {
        wrapperCol = {}
      }
      if (this.isSearchMode) {
        wrapperCol = { xs: 16, sm: 18 }
      }
      return wrapperCol
    }
  },
  methods: {
    getBooleanValue(item, key) { // 获取布尔值
      const matchValue = item[key]
      const defaultValueMap = {
        loading: false,
        disabled: false,
        isShow: true,
        allowClear: true
      }

      if (typeof matchValue === 'function') {
        return matchValue(item)
      } else {
        return typeof matchValue === 'undefined' 
          ? defaultValueMap[key]
          : !!matchValue
      }
    },
    getItemCol(item, key) { // 获取栅格属性
      const matchValue = item[key]
      if (matchValue) return matchValue

      let defaultColConfig = { md: 24, sm: 24 }
      if ( this.layout === 'inline') {
        defaultColConfig = { md: 8, sm: 12 }
      }
      if (this.isSearchMode) {
        defaultColConfig = { md: 6, sm: 12 }
      }
      return this.col[key] || defaultColConfig[key]
    },
    getSearchBtnColSpan() {
      const total = this.formFields.reduce((total, item) => {
        const col = this.getItemCol(item, 'md')
        return total + col
      }, 0)

      const ceil = Math.ceil(total / 24)
      const remainder = total % 24

      const span = (24 * ceil) - remainder

      return span >= 4 ? span : 24
    },
    onEvent(item, eventName, val, option) { // 事件
      if (typeof item[eventName] === 'function') {
        if (item.type === 'select' && eventName === 'onChange') {
          const { options } = item
          item[eventName](val, options && options.length ? options.find(item => item.value === val) : {})
        } else {
          item[eventName](val, option)
        }
      }

      if (eventName === 'onChange') {
        this.$refs.formViewRef.clearValidate(item.key)
      }
    },
    onFormatter(item, eventName, val) { // 格式化
      if (typeof item[eventName] === 'function') {
        return item[eventName](val)
      } else {
        return val
      }
    },
    validate(callback) { // 表单校验
      this.$refs.formViewRef.validate(callback)
    },
    validateField(fields, callback) { // 对部分表单字段进行校验
      this.$refs.formViewRef.validateField(fields, callback)
    },
    resetFields() { // 重置表单
      this.$refs.formViewRef.resetFields()
    },
    clearValidate(key) { // 清除校验
      this.$refs.formViewRef.clearValidate(key)
    },
    onReset() {
      this.resetFields()
      this.$nextTick(() => {
        this.$emit('reset')
      })
    },
    onSearch() {
      this.$emit('search')
    }
  }
}
</script>

<style lang="scss">
.yyl-form {
  &.ant-form-inline {
    .ant-form-item {
      display: inline-flex !important;
    }

    .ant-form-item-control-wrapper {
      flex-grow: 1;
    }
  }
  .ant-form-item {
    width: 100% !important;
    &.inline-extra  {
      position: relative;
      .ant-form-extra {
        position: absolute;
        top: 0;
        left: 100%;
      }

      .ant-form-item-control {
        display: flex;
        .ant-form-item-children {
          flex-grow: 1;
        }
      }
    }
    .ant-input-number {
      min-width: 100px;
      width: 100%;
    }
  }
  .ant-calendar-picker {
    width: 100%;
  }
}
</style>
