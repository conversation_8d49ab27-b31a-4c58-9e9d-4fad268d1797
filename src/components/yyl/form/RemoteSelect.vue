<!--
 * @Description: 选择框组件 options 数据需要从接口获取
 * @Version: 1.0
 * @Author: Harmon<PERSON>
 * @Date: 2025-02-12 11:15:54
 * @LastEditors: haolin.hu
 * @LastEditTime: 2025-05-12 17:25:59
-->

<template>
  <a-select
    v-model="bindValue"
    :disabled="disabled"
    :placeholder="placeholder"
    :allowClear="allowClear"
    v-bind="$attrs"
    @focus="onFocus"
    @search="val => getOptions(val, 'search')"
    @change="onEvent('onChange', $event)"
  >
    <a-spin v-if="loading" slot="notFoundContent" size="small" />
    <a-select-option
      v-for="option in options"
      :key="option.value"
      :value="option.value"
      :label="option.label"
      :disabled="getDisabledVal(option)"
    >
      {{ option.label }}
    </a-select-option>
  </a-select>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'RemoteSelect',
  model: {
    prop: 'value',
    event: 'select'
  },
  props: {
    value: {
      type: [String, Array, Number],
      default: undefined
    },
    disabled: {
      type: Boolean,
      default: false
    },
    hideItems: { // 需要隐藏的option value
      type: Array,
      default: () => []
    },
    whenDisableCanGetOptions: { // 当禁用选择时，是否请求options列表
      type: Boolean,
      default: false
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    requestInfo: {
      type: Object,
      default: () => ({}),
      required: true,
      validator: (value) => {
        if (!['url', 'httpHead', 'method', 'valueKey', 'labelKey'].every(key => key in value)) {
          console.error('requestInfo 必须包含 url, httpHead, method, params, valueKey, labelKey 属性')
          return false
        }
        return true
      }
    },
    onlyOneAutoSelect: { // 只有一个选项时，是否自动选中
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    reloadDataOnFocus: { // 是否每次聚焦都重新请求数据
      type: Boolean,
      default: false
    },
    defaultSearchWord: { // 默认搜索关键字
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      options: [],
      optionsMap: new Map(), // options 的map格式数据，用于选中时获取数据
      isChangeBySelect: false, // 值改变是否是主动选择导致的
      isInit: true, // 是否初次调用获取options的接口
      isFirstFocus: true, // 是否初次 focus
    }
  },
  computed: {
    bindValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('select', val)
      }
    },
    needUpdateOptions() {
      return [ this.disabled, this.requestInfo.params ]
    }
  },
  watch: {
    needUpdateOptions: {
      handler(newVal, oldVal) {
        if ((!this.disabled || this.whenDisableCanGetOptions)) {
          const isChange = JSON.stringify(newVal) !== JSON.stringify(oldVal)
          isChange && this.getOptions()
        }
      },
      immediate: true,
      deep: true
    },
    bindValue(val) {
      !this.isChangeBySelect && this.$emit('change', val, this.optionsMap.get(val))
    }
  },
  methods: {
    getOptions(searchKeyword, type, needPush = false) {
      const { searchKey } = this.$attrs
      if (type === 'search' && !searchKey) return
      
      this.loading = true
      const { url, httpHead, method, valueKey, labelKey, params = {} } = this.requestInfo
      const requestAction = method === 'GET' ? getAction : postAction

      let searchWord = this.isInit && this.defaultSearchWord
        ? this.defaultSearchWord
        : searchKeyword

      const queryParams = { ...params, ...(searchWord && searchKey ? { [searchKey]: searchWord } : {}) }
      requestAction(url, queryParams, httpHead).then(({ IsSuccess, Data }) => {
        this.loading = false

        if (!IsSuccess) {
          this.options = []
          return
        }

        let filteredData = Data
        if (this.hideItems.length) {
          const hideItemSet = new Set(this.hideItems)
          filteredData = Data.filter(item => !hideItemSet.has(item[valueKey]))
        }

        const optionsMap = new Map(filteredData.map(item => [item[valueKey], item]))
        const options = filteredData.map(item => {
          return {
            ...item,
            value: item[valueKey],
            label: typeof labelKey === 'object' ? item[labelKey[0]] || item[labelKey[1]] : item[labelKey],
          }
        })

        if (needPush) {
          options.forEach(item => {
            this.optionsMap.set(item.value, item)
          })
          this.options.push(...options)
        } else {
          this.optionsMap = optionsMap
          this.options = options
        }
        
        if (this.onlyOneAutoSelect && this.options.length === 1) {
          const firstValue = this.options[0].value
          this.bindValue = firstValue
          this.onEvent('onChange', firstValue, this.optionsMap.get(firstValue))
        }

        if (this.bindValue && this.isInit) {
          this.$emit('init', this.bindValue, this.optionsMap.get(this.bindValue))
        }

        this.isInit = false
        this.$forceUpdate()
      })
    },
    onFocus() {
      if (this.isFirstFocus && this.defaultSearchWord) {
        this.getOptions('', '', true)
        this.isFirstFocus = false
        return
      }
      if (!this.reloadDataOnFocus) return
      this.getOptions()
    },
    getDisabledVal(option) { // 获取 option disable 值
      const { isOptionDisabled } = this.$attrs
      if (!isOptionDisabled) return false
      if (typeof isOptionDisabled === 'function') {
        return isOptionDisabled(option)
      }
      if (typeof isOptionDisabled === 'boolean') {
        return isOptionDisabled
      }

      return false
    },
    onEvent(eventName, val) { // 事件
      this.isChangeBySelect = true
      if (eventName === 'onChange') {
        this.$emit('change', val, this.optionsMap.get(val))
        !val && this.getOptions()
      }
      setTimeout(() => {
        this.isChangeBySelect = false
      }, 100)
    },
  }
}
</script>