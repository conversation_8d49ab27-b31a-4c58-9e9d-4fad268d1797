<template>
  <a-button
    v-if="checkBtnPermissions(menuId)"
    :type="type"
    class="mr8"
    :loading="loading"
    :icon="icon"
    :size="size"
    :disabled="disabled"
    @click="onYYLBtnClick"
    >{{ text }}</a-button
  >
</template>
<!-- 使用示例
    <YYLButton
            menuId="XXXXXXXXXXXXX"
            text="XXXXXX"
            type="primary"
            :loading="confirmLoading"
            @click="onClick"
          />
 -->
<script>
import Vue from 'vue'
import { EditMixin } from '@/mixins/EditMixin'
export default {
  name: 'YYLButton',
  mixins: [EditMixin],
  props: {
    /**
     * 按钮显示文本--必填
     */
    text: {
      type: String,
      required: true,
      default: () => {
        return ''
      },
    },
    /**
     * 按钮id(菜单配置的菜单id)--必填
     */
    menuId: {
      type: String,
      default: () => {
        return undefined
      },
    },

    /**
     * 按钮类型 --非必填，默认不设
     * 值可为-primary dashed danger link或不设
     */
    type: {
      type: String,
      default: () => {
        return ''
      },
    },

    icon: {
      type: String,
      default: () => {
        return ''
      },
    },
    size: {
      type: String,
      default: () => {
        return 'default'
      },
    },
    /**
     * 按钮加载状态--非必填，默认false
     */
    loading: {
      type: Boolean,
      default: () => {
        return false
      },
    },
    /**
     * 是否把menuId作为参数传给后端
     */
    addHeader: {
      type: Boolean,
      default: () => {
        return true
      },
    },
    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: () => {
        return false
      },
    },
  },
  data() {
    return {}
  },
  methods: {
    onYYLBtnClick(e) {
      //把按钮的菜单id存起来、调用接口要使用
      // if (this.menuId && this.addHeader) {
      //   Vue.ls.set('MENU_ID', this.menuId)
      // }
      this.$emit('click')
    },
  },
}
</script>

<style></style>
