<template>
  <a-form :form="form" layout="vertical" v-if="IsRefresh">
    <a-row :getter="20" style="text-align: left;" v-for="(attribute, i) in attributeGroups" :key="attribute.Id">
      <a-col :md="24">
        <!-- 文本 视频 文档  -->
        <template
          v-if="
            attribute.EnumAttributeFormat == 1 ||
              attribute.EnumAttributeFormat == 6 ||
              attribute.EnumAttributeFormat == 7
          "
        >
          <a-form-item :label="`${attribute.AttributeName}：`">
            <a-input
              placeholder="请输入"
              v-decorator="[
                `attributeDtos[${i}].AttributeValue`,
                {
                  initialValue: attribute.AttributeValue,
                  rules: [
                    {
                      required: attribute.IsRequired,
                      message: `请输入${attribute.AttributeName}!`,
                      pattern: attribute.RegularForm
                    }
                  ]
                }
              ]"
            />
            <!-- <a-checkbox
              @change="
                (checkedValue) => {
                  onSearchKeyChange(checkedValue, i);
                }
              "
              :checked="attribute.IsSearchKey"
              style="margin-top: 6px;"
            >
              是否参与搜索
            </a-checkbox> -->
          </a-form-item>
        </template>
        <!-- 段落 -->
        <template v-if="attribute.EnumAttributeFormat == 2">
          <a-form-item :label="`${attribute.AttributeName}：`">
            <a-textarea
              placeholder="请输入"
              v-decorator="[
                `attributeDtos[${i}].AttributeValue`,
                {
                  initialValue: attribute.AttributeValue,
                  rules: [
                    {
                      required: attribute.IsRequired,
                      message: `请选择${attribute.AttributeName}!`
                    }
                  ]
                }
              ]"
              :auto-size="{ minRows: 2, maxRows: 5 }"
              :maxLength="300"
            />
            <!-- <a-checkbox
              @change="
                (checkedValue) => {
                  onSearchKeyChange(checkedValue, i);
                }
              "
              :checked="attribute.IsSearchKey"
              style="margin-top: 6px;"
            >
              是否参与搜索
            </a-checkbox> -->
          </a-form-item>
        </template>
        <!-- 数字 -->
        <template v-if="attribute.EnumAttributeFormat == 3">
          <a-form-item :label="`${attribute.AttributeName}：`">
            <a-input-number
              placeholder="请输入"
              v-decorator="[
                `attributeDtos[${i}].AttributeValue`,
                {
                  initialValue: attribute.AttributeValue,
                  rules: [
                    {
                      required: attribute.IsRequired,
                      message: `请选择${attribute.AttributeName}!`
                    }
                  ]
                }
              ]"
              style="width: 100%;"
            />
            <!-- <a-checkbox
              @change="
                (checkedValue) => {
                  onSearchKeyChange(checkedValue, i);
                }
              "
              :checked="attribute.IsSearchKey"
              style="margin-top: 6px;"
            >
              是否参与搜索
            </a-checkbox> -->
          </a-form-item>
        </template>
        <!-- 日期 -->
        <template v-if="attribute.EnumAttributeFormat == 4">
          <a-form-item :label="`${attribute.AttributeName}：`">
            <a-date-picker
              style="width: 100%;"
              placeholder="请选择"
              format="YYYY-MM-DD"
              @change="
                (date, dateString) => {
                  onDateChange(date, dateString, i)
                }
              "
              v-decorator="[
                `attributeDtos[${i}].AttributeValue`,
                {
                  initialValue: attribute.AttributeValue?moment(attribute.AttributeValue):null,
                  rules: [
                    {
                      type: 'object',
                      required: attribute.IsRequired,
                      message: `请输入${attribute.AttributeName}!`,
                      pattern: attribute.RegularForm
                    }
                  ]
                }
              ]"
            />
            <!-- <a-checkbox
              @change="
                (checkedValue) => {
                  onSearchKeyChange(checkedValue, i);
                }
              "
              :checked="attribute.IsSearchKey"
              style="margin-top: 6px;"
            >
              是否参与搜索
            </a-checkbox> -->
          </a-form-item>
        </template>
        <!-- 图片 -->
        <template v-if="attribute.EnumAttributeFormat == 5">
          <a-form-item :label="`${attribute.AttributeName}：`">
            <upload
              :bg="attribute.AttributeValue"
              listType="text"
              btn-type="default"
              @change="url => onUrlChange(url, i)"
              @clear="url => onClear(i)"
              style="margin-right: 10px;"
              :bName="GoodsbName"
              :dir="GoodsDir"
            ></upload>
            <!-- <br />
            <ImageControl
              style="margin-top: 10px;"
              v-if="attribute.AttributeValue"
              :src="attribute.AttributeValue"
              :width="200"
            >
            </ImageControl> -->
            <!-- <span v-else>未上传</span> -->
            <a-input
              type="hidden"
              v-decorator="[
                `attributeDtos[${i}].AttributeValue`,
                {
                  initialValue: attribute.AttributeValue,
                  rules: [{ required: attribute.IsRequired, message: `请上传图片!` }]
                }
              ]"
            />
            <!-- <a-checkbox
              @change="
                (checkedValue) => {
                  onSearchKeyChange(checkedValue, i);
                }
              "
              :checked="attribute.IsSearchKey"
              style="margin-top: 6px;"
            >
              是否参与搜索
            </a-checkbox> -->
          </a-form-item>
        </template>
        <!-- 单选 -->
        <template v-if="attribute.EnumAttributeFormat == 8">
          <a-form-item :label="`${attribute.AttributeName}：`">
            <a-radio-group
              @change="
                value => {
                  onRadioChange(value, i)
                }
              "
              v-decorator="[
                `attributeDtos[${i}].AttributeValue`,
                {
                  initialValue: attribute.CheckedValue,
                  rules: [
                    {
                      required: attribute.IsRequired,
                      message: `请选择${attribute.AttributeName}!`
                    }
                  ]
                }
              ]"
            >
              <a-radio
                v-for="(v, j) in JSON.parse(attribute.AttributeValue || '[]')"
                :key="'option' + j"
                :value="v.Name"
              >
                {{ v.Name }}</a-radio
              >
            </a-radio-group>
            <a-input
              type="hidden"
              v-decorator="[
                `attributeDtos[${i}].AttributeValue`,
                {
                  initialValue: attribute.CheckedValue,
                  rules: [
                    {
                      required: attribute.IsRequired,
                      message: `请选择${attribute.AttributeName}!`
                    }
                  ]
                }
              ]"
            />
            <!-- <a-checkbox
              @change="
                (checkedValue) => {
                  onSearchKeyChange(checkedValue, i);
                }
              "
              :checked="attribute.IsSearchKey"
              style="margin-top: 6px;"
            >
              是否参与搜索
            </a-checkbox> -->
          </a-form-item>
        </template>
        <!-- 复选 -->
        <template v-if="attribute.EnumAttributeFormat == 9">
          <a-form-item :label="`${attribute.AttributeName}：`">
            <a-checkbox-group
              @change="
                checkedValue => {
                  onCheckboxChange(checkedValue, i)
                }
              "
              v-decorator="[
                `attributeDtos[${i}].AttributeValue`,
                {
                  rules: [
                    {
                      required: attribute.IsRequired,
                      message: `请选择${attribute.AttributeName}!`
                    }
                  ]
                }
              ]"
            >
              <!-- initialValue: attribute.AttributeValue, -->
              <a-row>
                <a-col :md="8" v-for="(option, j) in JSON.parse(attribute.AttributeValue || '[]')" :key="'option' + j">
                  <a-checkbox :value="option.Name"> {{ option.Name }}</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
            <!-- <a-input
              type="hidden"
              
            /> -->
            <!-- <a-checkbox
              @change="
                (checkedValue) => {
                  onSearchKeyChange(checkedValue, i);
                }
              "
              :checked="attribute.IsSearchKey"
              style="margin-top: 6px;"
            >
              是否参与搜索
            </a-checkbox> -->
          </a-form-item>
        </template>
      </a-col>
    </a-row>
    <!-- <a-button v-if="true" @click="returnArray">测试</a-button> -->
  </a-form>
</template>

<script>
import moment from 'moment'
import pick from 'lodash.pick'
const { getAction, putAction, postAction } = require('@/api/manage')
const { EditMixin } = require('@/mixins/EditMixin')
// const upload = () => import("@/components/aocao/upload");
const ImageControl = () => import('@/components/yyl/components/ImageControl')
export default {
  name: 'AttributeControl',
  props: {
    attributeGroups: {
      type: Array,
      default() {
        return []
      }
    }
  },
  mixins: [EditMixin],
  components: { ImageControl },
  data() {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      labelCol1: {
        xs: { span: 24 },
        sm: { span: 4 }
      },
      wrapperCol1: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      inputCol: 8,
      IsRefresh: true,
      GoodsbName: window._CONFIG.AliOssConfig['GoodsBucketName'],
      GoodsDir: window._CONFIG.AliOssConfig['GoodsDir'],
      form: this.$form.createForm(this),
      url: {}
    }
  },
  created() {
    this.init()
  },
  methods: {
    moment,
    //初始化属性值
    init(forceUpdate) {
      // console.log(this.attributeGroups);
      this.$nextTick(() => {
        this.attributeGroups.forEach((item, i) => {
          if (item.EnumAttributeFormat == 8) {
            let AttrValue = JSON.parse(item.AttributeValue || '[]')
            item.CheckedValue = (AttrValue.filter(v => v.IsCheck) || []).length
              ? AttrValue.filter(v => v.IsCheck)[0].Name
              : null
          }
          this.form.setFieldsValue(pick(item.AttributeValue, `attributeGroups[${i}].AttributeValue`))
          if (forceUpdate) {
            this.$forceUpdate()
          } else {
            this.IsRefresh = false
            this.$nextTick(() => {
              this.IsRefresh = true
            })
          }
        })
      })
    },
    //日期
    onDateChange(date, dateString, i) {
      let obj = {}
      obj[`attributeDtos[${i}].AttributeValue`] = date

      this.form.setFieldsValue(obj)
      this.attributeGroups[i].AttributeValue = dateString
    },
    //单选
    onRadioChange(e, i) {
      let value = e.target.value
      let AttributeValue = JSON.parse(this.attributeGroups[i].AttributeValue || '[]')
      AttributeValue.map(item => {
        item.IsCheck = value == item.Name ? true : false
      })

      let obj = {}
      obj[`attributeDtos[${i}].AttributeValue`] = value
      this.form.setFieldsValue(obj)
      this.attributeGroups[i].AttributeValue = JSON.stringify(AttributeValue)
    },
    //多选
    onCheckboxChange(checkedValue, i) {
      let AttributeValue = JSON.parse(this.attributeGroups[i].AttributeValue || '[]')
      AttributeValue.map(item => {
        item.IsCheck = checkedValue.findIndex(v => v == item.Name) >= 0 ? true : false
      })
      let obj = {}
      obj[`attributeDtos[${i}].AttributeValue`] = checkedValue.join(',')
      this.form.setFieldsValue(obj)
      this.attributeGroups[i].AttributeValue = JSON.stringify(AttributeValue)
    },
    // //是否参与搜索
    // onSearchKeyChange(e, i) {
    //   // console.log(e.target.checked);
    //   // let IsSearchKey = this.attributeGroups[i].IsSearchKey;
    //   // AttributeValue.map((item) => {
    //   //   item.IsCheck =
    //   //     checkedValue.findIndex((v) => v == item.Name) >= 0 ? true : false;
    //   // });
    //   let obj = {};
    //   obj[`attributeDtos[${i}].IsSearchKey`] = e.target.checked;
    //   this.form.setFieldsValue(obj);
    //   this.attributeGroups[i].IsSearchKey = e.target.checked;
    // },
    //下拉
    onSelectChange(value, option, i) {
      // console.log(value, option);
      const text = option.componentOptions.children[0].text
      let obj = {}
      obj[`attributeDtos[${i}].AttributeValue`] = `${value}|${text}`
      this.form.setFieldsValue(obj)
    },
    //上传文件
    onUrlChange(uploadUrl, i) {
      let obj = {}
      let url = null
      if (typeof uploadUrl == 'object') {
        url = uploadUrl.FileId
      } else {
        url = uploadUrl
      }
      obj[`attributeDtos[${i}].AttributeValue`] = url
      this.attributeGroups[i].AttributeValue = url
      this.form.setFieldsValue(obj)
    },
    onClear(i) {
      let obj = {}
      obj[`attributeDtos[${i}].AttributeValue`] = null
      this.attributeGroups[i].AttributeValue = null
      this.form.setFieldsValue(obj)
    },
    //表单验证，返回数据
    returnArray() {
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          let formData = Object.assign({}, this.attributeGroups)
          const attributeDtos = values['attributeDtos']

          attributeDtos.forEach((value, i, array) => {
            if (
              formData[i].EnumAttributeFormat != 5 &&
              formData[i].EnumAttributeFormat != 8 &&
              formData[i].EnumAttributeFormat != 9
            ) {
              formData[i].AttributeValue = value.AttributeValue
            }
          })
          // console.log("formData", formData);

          return formData
        }
      })
    }
  }
}
</script>

<style></style>
