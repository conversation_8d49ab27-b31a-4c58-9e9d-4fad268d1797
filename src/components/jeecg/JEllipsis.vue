<template>
  <a-tooltip placement="topLeft">
    <template slot="title">
      <span>{{value}}</span>
    </template>
    {{ value | ellipsis(length) }}
  </a-tooltip>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON>',
  props: {
    value: {
      type: [String, Number],
      required: false,
    },
    length: {
      type: Number,
      required: false,
      default: 25,
    }
  }
}
</script>

<style scoped>
</style>
