<template>
  <div class="tinymce-editor">
    <editor v-if="showEditor" v-model="myValue" :init="init" :disabled="disabled" @onClick="onClick"></editor>
  </div>
</template>

<script>
import Vue from 'vue'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { ListMixin } from '@/mixins/ListMixin'

import tinymce from 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver'
import 'tinymce/icons/default'
import 'tinymce/plugins/image'
import 'tinymce/plugins/link'
import 'tinymce/plugins/media'
import 'tinymce/plugins/table'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/contextmenu'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/colorpicker'
import 'tinymce/plugins/textcolor'
import 'tinymce/plugins/code'
export default {
  mixins: [ListMixin],
  components: {
    Editor
  },
  props: {
    value: {
      type: String,
      required: false
    },
    triggerChange: {
      type: Boolean,
      default: false,
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    plugins: {
      type: [String, Array],
      default: 'link lists image code table colorpicker textcolor wordcount contextmenu'
    },
    toolbar: {
      type: [String, Array],
      default:
        'bold italic underline strikethrough | fontsizeselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent blockquote | undo redo | link unlink image code | removeformat'
    },
    // 存储桶
    bName: {
      type: String,
      default: window._CONFIG.AliOssConfig['BaseBucketName']
    },
    // 文件夹
    dir: {
      type: String,
      default: window._CONFIG.AliOssConfig['CommonDir']
    }
  },
  data() {
    return {
      Dir: this.dir, // 存储桶
      BName: this.bName, // 文件夹
      showEditor: false,
      //初始化配置
      init: {
        language_url: './tinymce/langs/zh_CN.js',
        language: 'zh_CN',
        skin_url: './tinymce/skins/ui/oxide',
        content_css: './tinymce/skins/content/default/content.min.css',
        height: 300,
        convert_urls: false,
        plugins: this.plugins,
        toolbar: this.toolbar,
        branding: false,
        menubar: false,
        images_upload_handler: (blobInfo, success, failure) => {
          var formdata1 = new FormData()

          formdata1.append('OSSAccessKeyId', window._CONFIG.AliOssConfig[this.BName][this.Dir].OSSAccessKeyId)
          formdata1.append('policy', window._CONFIG.AliOssConfig[this.BName][this.Dir].policy)
          formdata1.append('callback', window._CONFIG.AliOssConfig[this.BName][this.Dir].callback)
          formdata1.append('Signature', window._CONFIG.AliOssConfig[this.BName][this.Dir].signature)
          formdata1.append(
            'key',
            window._CONFIG.AliOssConfig[this.BName][this.Dir].dir +
              '/' +
              new Date().getTime() +
              '_' +
              blobInfo.filename()
          )
          formdata1.append('isup', '1')
          formdata1.append('file', blobInfo.blob(), blobInfo.filename())
          formdata1.append('success_action_status', '200')
          // let config = {
          //   headers: {
          //     "Content-Type": "multipart/form-data",
          //     Authorization: "Bearer " + this.accessToken,
          //   },
          // };
          this.$http.post(window._CONFIG.AliOssConfig[this.BName][this.Dir].host, formdata1).then(res => {
            if (res && res.Status == 'OK') {
              let url = res.FileUrl
              success(url)
            } else {
              failure('上传失败！')
            }
          })
        }
      },
      myValue: this.value
    }
  },
  // 计算属性
  computed: {
    accessToken() {
      return Vue.ls.get(ACCESS_TOKEN)
    }
  },
  created() {
    this.getWebUploadPolicy(this.BName, this.Dir, res => {
      this.BName = res
    })
  },
  mounted() {
    this.$nextTick(() => {
      this.showEditor = true
    })
    /* setTimeout(() => {
      this.showEditor = true
    }) */
    tinymce.init({})
  },
  methods: {
    onClick(e) {
      this.$emit('onClick', e, tinymce)
    },
    //可以添加一些自己的自定义事件，如清空内容
    clear() {
      this.myValue = ''
    }
  },
  watch: {
    value(newValue) {
      //  console.log(newValue)
      this.myValue = newValue
    },
    myValue(newValue) {
      // console.log(newValue)
      if (this.triggerChange) {
        //console.log(1)
        this.$emit('change', newValue)
      } else {
        //console.log(2)
        this.$emit('input', newValue)
      }
    }
  }
}
</script>
<style scoped></style>
