<template>
  <div>
    <keep-alive v-if="keepAlive" :exclude="$store.state.user.excludePageList">
      <router-view />
    </keep-alive>
    <router-view v-if="!keepAlive" />
  </div>
</template>

<script>
export default {
  name: 'RouteView',
  data() {
    return {
      excludePageList: [], //sort==99的界面 排除缓存
      otherExcludePageList: [], //其他非99的界面，也要排除缓存的手动加入
    }
  },
  computed: {
    keepAlive() {
      return this.$route.meta.keepAlive
    },
  },
  mounted() {
    this.$root.cacheThis = this
    console.log('进入路由')
  },
  methods: {

  },
}
</script>

