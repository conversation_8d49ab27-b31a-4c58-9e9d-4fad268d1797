<template>
  <global-layout @dynamicRouterShow="dynamicRouterShow" :refreshMenu="refreshMenu">
    <template v-if="showTabs">
      <contextmenu :itemList="menuItemList" :visible.sync="menuVisible" @select="onMenuSelect" />
      <a-tabs @contextmenu.native="(e) => onContextmenu(e)" v-if="multipage" :active-key="activePage" class="tab-layout-tabs" style="height: 52px" :hide-add="true" type="editable-card" @change="changePage" @edit="editPage">
        <a-tab-pane :id="page.fullPath" :key="page.fullPath" v-for="page in getList()">
          <span slot="tab" :key="titleKey" :pagekey="page.fullPath">{{ page.meta.title }}</span>
        </a-tab-pane>
      </a-tabs>
      <div class="page-content">
        <transition name="slide-fade">
          <router-view v-if="routerKey" />
        </transition>
      </div>
    </template>
    <template v-else>
      <div style="margin: 12px 12px 0">
        <transition name="page-toggle">
          <keep-alive v-if="multipage">
            <router-view />
          </keep-alive>
          <router-view v-else />
        </transition>
      </div>
    </template>
  </global-layout>
</template>

<script>
import GlobalLayout from '@/components/page/GlobalLayout'
import Contextmenu from '@/components/menu/Contextmenu'
import { mixin, mixinDevice } from '@/utils/mixin.js'

const indexKey = '/Home'

export default {
  name: 'TabLayout',
  components: {
    GlobalLayout,
    Contextmenu,
  },
  mixins: [mixin, mixinDevice],
  data() {
    return {
      pageList: [],
      linkList: [],
      activePage: '',
      menuVisible: false,
      routerKey: true,
      refreshMenu: null,
      titleKey: 0,
      menuItemList: [
        { key: '1', icon: 'arrow-left', text: '关闭左侧' },
        { key: '2', icon: 'arrow-right', text: '关闭右侧' },
        { key: '3', icon: 'close', text: '关闭其它' },
      ],
    }
  },
  computed: {
    multipage() {
      //判断如果是手机模式，自动切换为单页面模式
      if (this.isMobile()) {
        return false
      } else {
        return this.$store.state.app.multipage
      }
    },
    showTabs() {
      return this.$store.state.config.showTabs
    },
  },
  created() {
    if (this.$route.path != indexKey) {
      this.pageList.push({
        name: 'Home',
        path: indexKey,
        fullPath: indexKey,
        meta: {
          icon: 'home',
          title: '首页',
          keepAlive: true,
        },
      })
      this.linkList.push(indexKey)
    }
    this.pageList.push(this.$route)
    this.linkList.push(this.$route.fullPath)
    this.activePage = this.$route.fullPath
    this.refreshMenu = this.$route.fullPath
  },
  mounted() {
    let _this = this //很关键是要把父路由的vue实例赋给_this
    // 使用$root属性挂载该方法
    _this.$root.$on('getPageList', function (callBack) {
      callBack && callBack(_this.pageList)
    })
    // 设置标题
    _this.$root.$on('setPageTitle', function (index, title, page) {
      _this.setPageTitle(index, title, page)
    })
    // 关闭窗口
    _this.$root.$on('removeTabs', function (key, type, backPath) {
      _this.remove(key, type, backPath)
    })
    // 关闭窗口并返回主页
    _this.$root.$on('removeTabsAndBack', function (key, indexUrl) {
      _this.removeAndBack(key, indexUrl)
    })
    // 重置路由数据
    _this.$root.$on('reloadRouter', function (callBack) {
      _this.routerKey = false
      console.log('重置路由数据')
      _this.$nextTick(() => {
        _this.routerKey = true
        // _this.$root.$off('setThis')
      })
    })

    // console.log('排除的页面',this.$store.state.user.excludePageList)
  },
  watch: {
    $route: function (newRoute, oldRoute) {
      // 详情编辑页面入口进入重新加载
      if (this.showTabs) {
        let activeIndex = this.linkList.indexOf(this.activePage)
        this.isAlive = newRoute.meta.keepAlive
        let isContain = this.arrContain(this.pageList, newRoute.path)
        if (!this.multipage) {
          this.linkList = [newRoute.fullPath]
          this.pageList = [Object.assign({}, newRoute)]
        } else if (!isContain.bool) {
          if (activeIndex != -1 && activeIndex != 0) {
            this.linkList.splice(activeIndex + 1, 0, newRoute.fullPath)
            this.pageList.splice(activeIndex + 1, 0, Object.assign({}, newRoute))
          } else {
            this.linkList.push(newRoute.fullPath)
            this.pageList.push(Object.assign({}, newRoute))
          }
        } else if (isContain.bool) {
          let oldIndex = isContain.index
          if (oldIndex != activeIndex && newRoute.meta.hidden) {
            this.linkList.splice(oldIndex, 1)
            this.pageList.splice(oldIndex, 1)
            this.linkList.splice(activeIndex + 1, 0, newRoute.fullPath)
            this.pageList.splice(activeIndex + 1, 0, Object.assign({}, newRoute))
          } else {
            this.linkList.splice(oldIndex, 1, newRoute.fullPath)
            this.pageList.splice(oldIndex, 1, Object.assign({}, newRoute, { meta: this.pageList[oldIndex].meta }))
          }
        }
      }
      this.activePage = newRoute.fullPath
    },
    activePage: function (key) {
      if (this.showTabs) {
        let that = this
        let index = this.linkList.lastIndexOf(key)
        let waitRouter = this.pageList[index]
        this.$router.push(Object.assign({}, waitRouter))
      } else {
        this.changeRouteView(key)
      }
    },
    multipage: function (newVal) {
      if (!newVal) {
        this.linkList = [this.$route.fullPath]
        this.pageList = [this.$route]
      }
    },
  },
  methods: {
    /**
     * @description: 数组中元素是否存在
     * @param {arr,item}
     */
    arrContain(arr, item) {
      let err = {
        bool: false,
        index: -1,
      }
      arr.map((t, i) => {
        if (t.path == item) {
          err = {
            bool: true,
            index: i,
          }
        }
      })
      return err
    },
    init(callBack, page, queryParam) {
      callBack && callBack(page, queryParam)
    },
    changePage(key) {
      // console.log('标签点击事件')
      this.activePage = key
    },
    editPage(key, action) {
      if (this.showTabs) {
        if (action == 'remove' && key) {
          //清除当前关闭页面的缓存数据
          let pagePathName = key.substring(1, key.length)
          this.cacheUtil.deleteListData(pagePathName)
        }

        if (action == 'remove' && this.$root.confirmRemove) {
          this.confirmRemove(key, this.$root.confirmRemove)
          this.$root.confirmRemove = null
          return
        }
      }
      this[action](key)
    },
    setPageTitle(index, title, page) {
      if (page) {
        this.pageList.find((item, rIndex) => {
          if (page == item.fullPath) {
            index = rIndex
          }
        })
      }
      if (!index && index !== 0) {
        return
      }
      this.pageList[index].meta.title = title
      this.titleKey++
      if (this.titleKey == 100) {
        this.titleKey = 0
      }
    },
    remove(key, type, backPath) {
      // if (key == indexKey) {
      //   this.$message.warning('首页不能关闭!')
      //   return
      // }

      if (this.pageList.length === 1) {
        this.$message.warning('这是最后一页，不能再关闭了啦')
        return
      }
      this.pageList = this.pageList.filter((item) => item.fullPath !== key)
      let index = this.linkList.indexOf(key)
      this.linkList = this.linkList.filter((item) => item !== key)
      index = index >= this.linkList.length ? this.linkList.length - 1 : index

      if (type == 'back') {
        // let goBackPage = this.pageList.length > 1 ? this.pageList[this.pageList.length - 2].path : null
        let backPathIndex = this.pageList.findIndex((item) => item.path == backPath)
        // let goBackIndex = backPathIndex - this.pageList.length
        // console.log('关闭页面', this.pageList, key, type, goBackPage, backPath, backPathIndex, goBackIndex, this.activePage)
        this.activePage = backPathIndex > -1 ? this.pageList[backPathIndex].path : this.linkList[index]
        // this.$router.go(goBackIndex)
      } else {
        this.backPagePath = ''
        this.activePage = this.linkList[index]
      }
    },
    confirmRemove(key, info) {
      if (key == indexKey) {
        this.$message.warning('首页不能关闭!')
        return
      }
      if (this.pageList.length === 1) {
        this.$message.warning('这是最后一页，不能再关闭了啦')
        return
      }
      let that = this
      this.$confirm({
        title: info.title ? info.title : '温馨提示',
        content: info.content ? info.content : '',
        onOk() {
          that.pageList = that.pageList.filter((item) => item.fullPath !== key)
          let index = that.linkList.indexOf(key)
          that.linkList = that.linkList.filter((item) => item !== key)
          index = index >= that.linkList.length ? that.linkList.length - 1 : index
          that.activePage = that.linkList[index]
        },
        onCancel() { },
      })
    },
    removeAndBack(key, indexUrl) {
      if (key == indexKey) {
        this.$message.warning('首页不能关闭!')
        return
      }
      if (this.pageList.length === 1) {
        this.$message.warning('这是最后一页，不能再关闭了啦')
        return
      }
      this.pageList = this.pageList.filter((item) => item.fullPath !== key)
      let index = this.linkList.indexOf(key)
      this.linkList = this.linkList.filter((item) => item !== key)
      index = index >= this.linkList.length ? this.linkList.length - 1 : index
      this.activePage = this.linkList[index]
      this.$root.clickBtnRouterPush = true

      this.$router.replace({
        path: indexUrl,
        // query: {
        //   refresh: true
        // }
      })
    },
    onContextmenu(e) {
      const pagekey = this.getPageKey(e.target)
      if (pagekey !== null) {
        e.preventDefault()
        this.menuVisible = true
      }
    },
    getPageKey(target, depth) {
      depth = depth || 0
      if (depth > 2) {
        return null
      }
      let pageKey = target.getAttribute('pagekey')
      pageKey =
        pageKey || (target.previousElementSibling ? target.previousElementSibling.getAttribute('pagekey') : null)
      return pageKey || (target.firstElementChild ? this.getPageKey(target.firstElementChild, ++depth) : null)
    },
    onMenuSelect(key, target) {
      let pageKey = this.getPageKey(target)
      switch (key) {
        case '1':
          this.clearCacheSearchDataByKeyAndType(pageKey, 1, () => {
            this.closeLeft(pageKey)
          })

          break
        case '2':
          this.clearCacheSearchDataByKeyAndType(pageKey, 2, () => {
            this.closeRight(pageKey)
          })

          break
        case '3':
          this.clearCacheSearchDataByKeyAndType(pageKey, 3, () => {
            this.closeOthers(pageKey)
          })
          break
        default:
          break
      }
    },
    closeOthers(pageKey) {
      let index = this.linkList.indexOf(pageKey)
      if (pageKey == indexKey) {
        this.linkList = this.linkList.slice(index, index + 1)
        this.pageList = this.pageList.slice(index, index + 1)
        this.activePage = this.linkList[0]
      } else {
        let indexContent = this.pageList.slice(0, 1)[0]
        this.linkList = this.linkList.slice(index, index + 1)
        this.pageList = this.pageList.slice(index, index + 1)
        this.linkList.unshift(indexKey)
        this.pageList.unshift(indexContent)
        this.activePage = this.linkList[1]
      }
    },
    closeLeft(pageKey) {
      if (pageKey == indexKey) {
        return
      }
      let tempList = [...this.pageList]
      let indexContent = tempList.slice(0, 1)[0]
      let index = this.linkList.indexOf(pageKey)
      this.linkList = this.linkList.slice(index)
      this.pageList = this.pageList.slice(index)
      this.linkList.unshift(indexKey)
      this.pageList.unshift(indexContent)
      if (this.linkList.indexOf(this.activePage) < 0) {
        this.activePage = this.linkList[0]
      }
    },
    closeRight(pageKey) {
      let index = this.linkList.indexOf(pageKey)
      this.linkList = this.linkList.slice(0, index + 1)
      this.pageList = this.pageList.slice(0, index + 1)
      if (this.linkList.indexOf(this.activePage < 0)) {
        this.activePage = this.linkList[this.linkList.length - 1]
      }
    },
    //update-begin-author:taoyan date:20190430 for:动态路由title显示配置的菜单title而不是其对应路由的title
    dynamicRouterShow(key, title, pKey, curTabMenuList, rootPath) {
      if (this.showTabs) {
        let keyIndex = this.linkList.indexOf(key)
        if (keyIndex >= 0) {
          let currRouter = this.pageList[keyIndex]
          let meta = Object.assign({}, currRouter.meta, { title: title })
          this.pageList.splice(keyIndex, 1, Object.assign({}, currRouter, { meta: meta }))
        }
      } else {
        //findKey是因为需要区分诊宝倍和好药优选，找到正确的路由。他俩的path是一致的 所以加上跟菜单的path来区分
        let findKey = key
        if (rootPath && (rootPath.indexOf('/zbb') != -1 || rootPath.indexOf('/hyyx') != -1)) {
          findKey = key + rootPath
        }
        let keyIndex = this.linkList.lastIndexOf(findKey)
        if (keyIndex >= 0) {
          let currRouter = this.pageList[keyIndex]
          let meta = Object.assign({}, currRouter.meta, { title: title })
          this.pageList.splice(keyIndex, 1, Object.assign({}, currRouter, { meta: meta }))
        } else {
          if (curTabMenuList && curTabMenuList.length && pKey) {
            let curTabMenu = curTabMenuList.filter((t) => t.path == pKey)
            if (curTabMenu && curTabMenu.length && curTabMenu[0].children && curTabMenu[0].children.length && key) {
              let curChildrenMenu = curTabMenu[0].children.filter((v) => v.path == key)
              let currRouter = {
                ...curChildrenMenu[0],
                fullPath: findKey,
              }
              let meta = Object.assign({}, currRouter.meta, { title: title })
              this.pageList.push(Object.assign({}, currRouter, { meta: meta }))

              this.linkList.push(findKey)
            }
          }
        }
        this.activePage = findKey
      }
    },
    //update-end-author:taoyan date:20190430 for:动态路由title显示配置的菜单title而不是其对应路由的title
    changeRouteView(key) {
      let index = this.linkList.lastIndexOf(key)
      let waitRouter = this.pageList[index]
      // console.log(this.linkList)
      if (waitRouter) {
        if (waitRouter.meta.configParams || (waitRouter.query && Object.keys(waitRouter.query).length)) {
          let queryObj = waitRouter.query || {}
          let configParamsObj = waitRouter.meta.configParams || {}
          console.log('base pushParams')
          this.$router.push(
            Object.assign({}, waitRouter, {
              query: { ...configParamsObj, ...queryObj },
              params: { ...configParamsObj, ...queryObj },
            })
          )
        } else {
          console.log('base pushNone')
          this.$router.push(Object.assign({}, waitRouter))
        }
      }
    },
    /**
     * 更具pageKey和类型删除缓存数据
     * @param {*} pageKey
     * @param {*1关闭左侧 2关闭右侧 3关闭其他} type
     */
    clearCacheSearchDataByKeyAndType(pageKey, type, callBack) {
      if (!pageKey) {
        return
      }
      let pagePathlist = []
      if (type == 3) {
        this.pageList.forEach((item) => {
          if (item.path && item.path != pageKey && item.name && item.name != 'home') {
            pagePathlist.push(item.name)
          }
        })
      } else if (type == 1) {
        let index = this.pageList.findIndex((item) => {
          return item.path == pageKey
        })
        if (index > -1) {
          this.pageList.forEach((item, i, arr) => {
            if (i < index && item.path && item.path != pageKey && item.name && item.name != 'home') {
              pagePathlist.push(item.name)
            }
          })
        }
      } else if (type == 2) {
        let index = this.pageList.findIndex((item) => {
          return item.path == pageKey
        })
        if (index > -1) {
          this.pageList.forEach((item, i, arr) => {
            if (i > index && item.path && item.path != pageKey && item.name && item.name != 'home') {
              pagePathlist.push(item.name)
            }
          })
        }
      }
      this.cacheUtil.deleteListDataByKeys(pagePathlist)

      callBack && callBack()
    },
    getList() {
      let list = this.pageList.length > 1 ? this.pageList.filter((x) => x.name != 'Home') : this.pageList
      return list
    },
  },
}
</script>

<style lang="scss">
.page-transition-enter {
  opacity: 0;
}

.page-transition-leave-active {
  opacity: 0;
}

.page-transition-enter .page-transition-container,
.page-transition-leave-active .page-transition-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

/*美化弹出Tab样式*/
.ant-tabs-nav-container {
  margin-top: 4px;
}

/* 修改 ant-tabs 样式 */
.tab-layout-tabs.ant-tabs {
  border-bottom: 1px solid #ccc;
  border-left: 1px solid #ccc;
  background-color: white;
  padding: 0 10px;

  .ant-tabs-bar {
    margin: 4px 0 0;
    border: none;
  }
}

.ant-tabs {
  &.ant-tabs-card .ant-tabs-tab {
    padding: 0 24px !important;
    background-color: white !important;
    margin-right: 10px !important;

    .ant-tabs-close-x {
      width: 12px !important;
      height: 12px !important;
      opacity: 0 !important;
      cursor: pointer !important;
      font-size: 12px !important;
      margin: 0 !important;
      position: absolute;
      top: 36%;
      right: 6px;
    }

    &:hover .ant-tabs-close-x {
      opacity: 1 !important;
    }
  }
}

.ant-tabs.ant-tabs-card > .ant-tabs-bar {
  .ant-tabs-tab {
    border: none !important;
    border-bottom: 1px solid transparent !important;
  }

  // .ant-tabs-tab-active {
  //   border-color: #1890ff !important;
  //   background-color: #f0f2f5 !important;
  // }
  .ant-tabs-tab-active {
    background: #f0f2f5 !important;
    .ant-tabs-close-x {
      opacity: 1 !important;
    }
  }
}
.tab-layout-tabs.ant-tabs .ant-tabs-bar {
  margin: 11px 0 0 !important;
}
.tab-layout-tabs.ant-tabs {
  border-bottom: 1px solid #f0f2f5 !important;
}
.page-content {
  background: white;
  height: calc(100vh - 130px);
  max-height: calc(100vh - 130px);
  overflow: auto;
  margin: 10px 10px 0 10px;
  overflow-y: auto;
  overflow-x: hidden;
}
/* 设置滚动条轨道的样式 */
.page-content::-webkit-scrollbar {
  width: 4px;
  background-color: #f5f5f5;
}

/* 滚动条滑块的样式 */
.page-content::-webkit-scrollbar-thumb {
  background-color: #666666;
  border-radius: 10px;
}

/* 滚动条滑块在悬停状态下的样式 */
.page-content::-webkit-scrollbar-thumb:hover {
  background-color: #808080;
}
</style>

