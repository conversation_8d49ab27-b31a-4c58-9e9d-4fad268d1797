import { getAction } from '@/api/manage'
/**
 * 项目配置
 */
export default {
  name: 'projectConfig',
  state: {
    isShowHeadTab: false, //是否显示顶部tab 如电商管理后台样式和菜单配置  false则如医贸端样式和菜单配置
    showTabs: true, //页面内容页是否多页签方式展示
    samePathList: ['/baseData/Goods/MedicalTrade/MedicalTradeGoodsList'] //相同路径集合 例如 ['/baseData/Goods/MedicalTrade/MedicalTradeGoodsList'](医贸商品的相关界面path相同)
  },
  getters: {
    isShowHeadTab(state) {
      return state.isShowHeadTab
    },
    showTabs(state) {
      return state.showTabs
    }
  },
  mutations: {
    resetUser(state) {
      state.userId = ''
      state.workState = 1
      state.chatList = []
      state.count = 0
      state.curCon = null
    }
  },
  actions: {
    // setDoctorWorkState({ commit },state){
    //   commit('setWorkState',state)
    // },
    // /**
    //  * 更改用户工作状态
    //  * @param {*工作状态} state  1 休息 2 工作
    //  * @returns
    //  */
    // changeWorkState({ commit },params){
    //   const state = params.state;
    //   return new Promise((resolve,reject)=>{
    //     // if(rapp.IsLogined()){
    //       getAction('/Doctor/Doctor/PhysicianUpdateWorkState',{doctorWorkState:state},'DOCTOR_USECASE').then(res=>{
    //         commit('setWorkState',state)
    //         resolve(true)
    //         // if(state==1){
    //         //   //退出im
    //         //   params.logoutIm && params.logoutIm();
    //         //   commit('setWorkState',state)
    //         //   resolve(true)
    //         // }else{
    //         //   params.loginIm && params.loginIm();
    //         //   resolve(true)
    //         // }
    //       }).catch(err=>{
    //         reject(err)
    //       })
    //     // }
    //   })
    // },
  }
}
