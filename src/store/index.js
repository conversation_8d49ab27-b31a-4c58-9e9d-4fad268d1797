import Vue from 'vue'
import Vuex from 'vuex'

import getters from './getters'
import app from './modules/app'
import config from './modules/projectConfig'
import user from './modules/user'

// 导入页面构建器store模块

Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    app,
    user,
    config,
    // 页面构建器模块
    pageBuilder: {
      namespaced: true,
      state: {
        initialized: false
      },
      mutations: {
        SET_INITIALIZED(state, initialized) {
          state.initialized = initialized
        }
      },
      actions: {
        async initialize({ commit, dispatch }) {
          try {
            // 初始化各个模块
            await Promise.all([
              dispatch('components/loadYYLComponents'),
              dispatch('designer/initializeDesigner'),
              dispatch('pages/loadPageList')
            ])
            commit('SET_INITIALIZED', true)
          } catch (error) {
            console.error('页面构建器初始化失败:', error)
            throw error
          }
        }
      },
      modules: {
        designer: require('../views/page-builder/store/modules/designer').default,
        components: require('../views/page-builder/store/modules/components').default,
        pages: require('../views/page-builder/store/modules/pages').default
      }
    }
  },
  state: {
    queryParam: '',
    pagination: '',
    includeArray:[],
  },
  mutations: {
    receiveQueryParam(state, payload) {
      // 将A组件的数据存放于state
      state.queryParam = payload.queryParam
    },
    setPagination(state, payload) {
      state.pagination = payload.pagination
    },
    includeArray(state, payload) {
      state.includeArray = payload.includeArray
    },
  },
  actions: {

  },
  getters
})
