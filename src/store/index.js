import Vue from 'vue'
import Vuex from 'vuex'

import app from './modules/app'
import user from './modules/user'
import getters from './getters'
import config from './modules/projectConfig'

Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    app,
    user,
    config
  },
  state: {
    queryParam: '',
    pagination: '',
    includeArray:[],
  },
  mutations: {
    receiveQueryParam(state, payload) {
      // 将A组件的数据存放于state
      state.queryParam = payload.queryParam
    },
    setPagination(state, payload) {
      state.pagination = payload.pagination
    },
    includeArray(state, payload) {
      state.includeArray = payload.includeArray
    },
  },
  actions: {

  },
  getters
})