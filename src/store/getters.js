import Vue from 'vue'
import { USER_INFO } from '@/store/mutation-types'
const getters = {
  device: state => state.app.device,
  theme: state => state.app.theme,
  color: state => state.app.color,
  token: state => state.user.token,
  avatar: state => {
    state.user.avatar =
      Vue.ls.get(USER_INFO) && Vue.ls.get(USER_INFO).HeaderImgUrl
        ? Vue.ls.get(USER_INFO).HeaderImgUrl
        : '/head-icon.png'
    return state.user.avatar
  },
  username: state => {
    state.user.username = Vue.ls.get(USER_INFO).LoginName
    return state.user.username
  },
  nickname: state => {
    state.user.realname = Vue.ls.get(USER_INFO).NickName || Vue.ls.get(USER_INFO).Name
    return state.user.realname
  },
  welcome: state => state.user.welcome,
  userInfo: state => {
    state.user.info = Vue.ls.get(USER_INFO)
    return state.user.info
  }
}

export default getters

