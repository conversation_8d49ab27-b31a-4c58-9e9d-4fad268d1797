/*列表上方操作按钮区域*/
.ant-card-body .table-operator {
  margin-bottom: 18px;
}

/** Button按钮间距 */
.table-operator .ant-btn {
  margin-right: 6px;
}

/*列表td的padding设置 可以控制列表大小*/
.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

/*列表页面弹出modal*/
.ant-modal-cust-warp {
  height: 100%;
}

/*弹出modal Y轴滚动条*/
.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto;
}

/*弹出modal 先有content后有body 故滚动条控制在body上*/
.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden;
}

/*列表中有图片的加这个样式 参考用户管理*/
.anty-img-wrap {
  height: 25px;
  position: relative;
}

.anty-img-wrap>img {
  max-height: 100%;
}

/*列表中范围查询样式*/
.query-group-cust {
  width: calc(50% - 10px);
}

.query-group-split-cust:before {
  content: "~";
  width: 20px;
  display: inline-block;
  text-align: center;
}

/* 色彩选择器修复CSS */
.m-colorPicker .box {
  width: 220px !important;
  z-index: 999 !important;
}

.m-colorPicker .tColor li {
  border: 1px solid #666 !important;
}

/* 补充样式,by tonydai */
.ant-form-item {
  margin-bottom: 12px !important;
}

.ant-spin-dot-spin {
  animation: antRotate 0.8s infinite linear;
}

.ant-spin-dot i {
  background-color: #52c41a !important;
}

.ant-tooltip-inner {
  max-width: 300px !important;
  text-align: center;
}

/* 强制换行 */
td div.break {
  word-break: break-all;
}

/* 患者 */
.patient .ant-tabs-bar {
  margin: 0;
}

.patient .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
  border-bottom: 2px solid #1890ff !important;
}

.txt {
  word-break: break-all;
}

// hl
.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.ml15 {
  margin-left: 15px;
}

.pl0 {
  padding-left: 0 !important;
}

.pr0 {
  padding-right: 0 !important;
}

.pr15 {
  padding-right: 15px !important;
}

.pt0 {
  padding-top: 0 !important;
}

.pb0 {
  padding-bottom: 0 !important;
}

.pAll {
  padding: 0 !important;
}

.c1890ff {
  color: #1890ff !important;
}

.cFF3939 {
  color: #ff3939 !important;
}

.flex {
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  display: -webkit-box;
  display: -webkit-flex;
}

.align-items-center {
  -moz-align-items: center;
  -ms-align-items: center;
  -o-align-items: center;
  align-items: center;
  -webkit-align-items: center;
}

.fll {
  float: left;
}

.flr {
  float: right;
}

.mr10 {
  margin-right: 10px;
}

.ml8 {
  margin-left: 8px;
}

.mr8 {
  margin-right: 8px;
}

.mr5 {
  margin-right: 10px;
}

.curp {
  cursor: pointer;
}

/*解决table文本超长和固定显示不完整BUG*/
.ant-table-fixed-left table,
.ant-table-fixed-right table {
  width: min-content !important;
}

// 面包屑多标签激活页签底色
.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
  background: #f0f2f5 !important;
}

// 基本信息前面一个竖条样式展示
.info {
  font-size: 16px;

  .vertical {
    display: inline-block;
    width: 5px;
    height: 15px;
    background: #1890ff;
    margin-right: 5px;
  }
}

.example {
  text-align: center;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  margin-bottom: 20px;
  padding: 30px 50px;
  margin: 20px 0;
}

.table-page-search-submitButtons {
  float: right;
  overflow: hidden;
  margin-bottom: 16px;
  /* .ant-btn + .ant-btn{
    margin-left:8px;
  } */
}

.module-title {
  font-size: 16px;
  font-weight: 600 !important
}

.bgWhite {
  background-color: white;
}

.affix {
  width: 100%;
  float: right;
  text-align: right;
  margin-bottom: 5px;
}
.affix>div{
  background: white;
  bottom: 9px !important;
}

.pd1016 {
  padding: 10px 16px;
}

.w50-center {
  width: 50%;
  margin: 0 auto;
}

.mt5 {
  margin-top: 5px;
}

.mt10 {
  margin-top: 10px;
}

.mt15 {
  margin-top: 15px;
}

.mt20 {
  margin-top: 20px;
}

.mb10 {
  margin-bottom: 10px;
}

.mb5 {
  margin-bottom: 5px;
}

.mb15 {
  margin-bottom: 15px;
}

.mb20 {
  margin-bottom: 20px;
}

.mr16 {
  margin-right: 16px;
}

.cred {
  color: red;
}

.empty-loading {
  width: 100%;
  height: 50vh;
  text-align: center;
  align-items: center;
  justify-content: center;
  display: flex;
}

.pd10 {
  padding: 10px 10px;
}

.c999 {
  color: #999999;
}

.flc {
  display: flex;
  flex-direction: column;
}

.hcenter {
  text-align: center;
  justify-content: center;
}

.pr16 {
  padding-right: 16px;
}

.rflex {
  display: flex;
  flex-direction: row;
}

.flexr {
  display: flex;
  flex-direction: row-reverse;
}

.cflex {
  display: flex;
  flex-direction: column;
}

.flex1 {
  flex: 1;
}

.tcenter {
  text-align: center;
}

.vcenter {
  align-items: center;
}

.center {
  align-items: center;
  justify-content: center;
}

/* 紧凑样式 */
.ant-table {
  line-height: 1;
}

.ant-table-pagination.ant-pagination {
  margin: 15px 0;
}

.ant-card-body {
  padding: 16px;
}

.ant-card-head-title {
  padding: 10px 0;
}