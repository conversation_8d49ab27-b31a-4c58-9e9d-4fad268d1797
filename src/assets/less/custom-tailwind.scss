@use "sass:math";
@use "sass:map";
@use "sass:meta";
@use "sass:list";

.custom-tailwind {
  $grey: rgba(0, 0, 0, 0.45);
  $grey-90: hsl(0, 0%, 90%); // #e8e8e8
  $red: hsla(0, 100%, 61%, 1); // #FF3939
  $blue: #1890ff;
  $blue-95: hsla(200, 100%, 95%, 1); // #e6f7ff
  $black: rgba(0, 0, 0, 0.85);

  $text-colors: (
    "text-red": $red,
    "text-grey": $grey,
    "text-blue": $blue,
    "text-black": $black
  );
  // 文字颜色
  @each $class, $color in $text-colors {
    .#{$class} {
      color: $color;
    }
    .#{$class}-important {
      color: $color !important;
    }
  }

  $bg-colors: (
    "bg-red": $red,
    "bg-blue-95": $blue-95,
    "bg-white": #fff,
  );
  // 背景颜色
  @each $class, $color in $bg-colors {
    .#{$class} {
      background: $color;
    }
  }

  $font-size: (
    "text-12": 12px,
    "text-14": 14px,
    "text-16": 16px,
    "text-18": 18px,
    "text-20": 20px,
    "text-32": 32px,
  );
  // 字号
  @each $class, $size in $font-size {
    .#{$class} {
      font-size: $size;
    }
    .#{$class}-important {
      font-size: $size !important;
    }
  }

  $border-width: 2;
  $border-color: (
    "grey-90": $grey-90,
    "blue": $blue,
  );
  $border-position: (
    "border": border,
    "border-t": border-top,
    "border-r": border-right,
    "border-b": border-bottom,
    "border-l": border-left,
  );

  @each $class, $property in $border-position {
    // 示例：border-t-grey-96
    @each $color, $val in $border-color {
      .#{$class}-#{$color} {
        #{$property}: 1px solid $val;
      }

      // 示例：border-t-grey-96-2
      @each $width in $border-width {
        .#{$class}-#{$color}-#{$width} {
          #{$property}: #{$width}px solid $val;
        }
      }
    }
  }

  $space-map: (
    "margin": (
      class: m,
      property: margin,
    ),
    "margin-x": (
      class: mx,
      property: margin-left margin-right,
    ),
    "margin-y": (
      class: my,
      property: margin-top margin-bottom,
    ),
    "margin-left": (
      class: ml,
      property: margin-left,
    ),
    "margin-right": (
      class: mr,
      property: margin-right,
    ),
    "margin-top": (
      class: mt,
      property: margin-top,
    ),
    "margin-bottom": (
      class: mb,
      property: margin-bottom,
    ),
    "padding": (
      class: p,
      property: padding,
    ),
    "padding-x": (
      class: px,
      property: padding-left padding-right,
    ),
    "padding-y": (
      class: py,
      property: padding-top padding-bottom,
    ),
    "padding-left": (
      class: pl,
      property: padding-left,
    ),
    "padding-right": (
      class: pr,
      property: padding-right,
    ),
    "padding-top": (
      class: pt,
      property: padding-top,
    ),
    "padding-bottom": (
      class: pb,
      property: padding-bottom,
    ),
  );
  // margin/padding （0-80之间的双数）
  @for $i from 0 through 40 {
    @each $name, $map-value in $space-map {
      @each $key, $value in $map-value {
        $class: map.get($map-value, class);
        $properties: map.get($map-value, property);

        @if meta.type-of($properties) == "string" {
          $properties: list.append((), $properties);
        }

        .#{$class}-#{$i * 2} {
          @each $property in $properties {
            #{$property}: #{$i * 2}px;
          }
        }

        .-#{$class}-#{$i * 2} {
          @each $property in $properties {
            #{$property}: #{$i * -2}px;
          }
        }
      }
    }
  }

  .mx-auto {
    margin-right: auto;
    margin-left: auto;
  }

  .mr-auto {
    margin-right: auto;
  }

  .ml-auto {
    margin-left: auto;
  }

  $radius-size: (
    "2": 2px,
    "4": 4px,
    "full": 999px
  );
  $radius-position: (
    "rounded": (
      class: "rounded",
      property: border-radius,
    ),
    "rounded-t": (
      class: "rounded-t",
      property: border-top-right-radius border-top-left-radius,
    ),
    "rounded-b": (
      class: "rounded-b",
      property: border-bottom-right-radius border-bottom-left-radius,
    ),
    "rounded-l": (
      class: "rounded-l",
      property: border-top-left-radius border-bottom-left-radius,
    ),
    "rounded-r": (
      class: "rounded-r",
      property: border-top-right-radius border-bottom-right-radius,
    ),
    "rounded-tr": (
      class: "rounded-tr",
      property: border-top-right-radius,
    ),
    "rounded-tl": (
      class: "rounded-tl",
      property: border-top-left-radius,
    ),
    "rounded-br": (
      class: "rounded-br",
      property: border-bottom-right-radius,
    ),
    "rounded-bl": (
      class: "rounded-bl",
      property: border-bottom-left-radius,
    ),
  );

  // radius
  @each $size, $val in $radius-size {
    @each $name, $map-value in $radius-position {
      @each $key, $value in $map-value {
        $class: map.get($map-value, class);
        $properties: map.get($map-value, property);

        @if meta.type-of($properties) == "string" {
          $properties: list.append((), $properties);
        }

        .#{$class}-#{$size} {
          @each $property in $properties {
            #{$property}: #{$val};
          }
        }
      }
    }
  }

  $leading: (
    "none": 1,
    "tight": 1.375,
    "sung": 1.5,
    "normal": 1.625,
    "relaxed": 2,
    "loose": 2.5,
  );

  @each $class, $val in $leading {
    .leading-#{$class} {
      line-height: $val;
    }
  }

  $position: 'absolute', 'relative', 'fixed', 'sticky';
  // position
  @each $val in $position {
    .#{$val} {
      position: #{$val};
    }
  }

  $box: (
    'w': 'width', 
    'h': 'height'
  );
  // width/height/（0-1000 之间的 10的倍数）
  @for $i from 0 through 100 {
    @each $class, $property in $box {
      .#{$class}-#{$i * 10} {
        #{$property}: #{$i * 10}px;
      }
    }
  }

  $space: 'top', 'bottom', 'left', 'right';
  // left/top/right/bottom（0-80 之间的 双数）
  @for $i from 0 through 40 {
    @each $property in $space {
      .#{$property}-#{$i * 2} {
        #{$property}: #{$i * 2}px;
      }
    }
  }
  // 负位置值
  $-space: 'left', 'top', 'right', 'bottom';
  // 负位置值（left/top/right/bottom）（-0 至 -80 之间的 双数）
  @for $i from 0 through 40 {
    @each $property in $-space {
      .-#{$property}-#{$i * 2} {
        #{$property}: -#{$i * 2}px;
      }
    }
  }

  $custom: (
    "display": (
      "display-hidden": none,
      "flex": flex,
      "block": block,
      "inline-block": inline-block,
      "inline-flex": inline-flex
    ),
    "width": (
      "w-screen": 100vw,
      "w-full": 100%,
      "w-fit": fit-content,
    ),
    "height": (
      "h-screen": 100vh,
      "h-full": 100%,
      "h-1":1px,
      "h-fit": fit-content,
    ),
    "min-height": (
      "min-h-screen": 100vh,
      "min-h-full": 100%,
    ),
    "flex-wrap": (
      "flex-wrap": wrap,
      "flex-nowrap": nowrap,
    ),
    "justify-content": (
      "justify-start": flex-start,
      "justify-end": flex-end,
      "justify-center": center,
      "justify-between": space-between,
      "justify-around": space-around,
    ),
    "align-items": (
      "items-start": flex-start,
      "items-end": flex-end,
      "items-center": center,
    ),
    "flex": (
      "flex-1": 1,
    ),
    "flex-shrink": (
      "shrink-0": 0,
      "shrink-1": 1,
    ),
    "flex-grow": (
      "grow-0": 0,
      "grow-1": 1,
    ),
    "flex-direction": (
      "flex-col": column
    ),
    "text-decoration": (
      "underline": underline,
      "line-through": line-through,
    ),
    "text-align": (
      "text-left": left,
      "text-center": center,
      "text-right": right,
    ),
    "white-space": (
      "whitespace-nowrap": nowrap,
    ),
    "font-weight": (
      "font-bold": bold,
      "font-normal": normal,
    ),
    "object-fit": (
      "object-cover": cover,
      "object-contain": contain,
    ),
    "overflow": (
      "overflow-hidden": hidden,
      "overflow-auto": auto,
      "overflow-scroll": scroll,
    ),
    "overflow-x": (
      "overflow-x-scroll": scroll,
      "overflow-x-hidden": hidden,
    ),
    "overflow-y": (
      "overflow-y-scroll": scroll,
    ),
    "box-sizing" : (
      "box-border": border-box,
      "box-content": content-box,
    ),
    "cursor": (
      "cursor-pointer": pointer
    )
  );
  // 常用属性
  @each $property, $map-value in $custom {
    @each $class, $value in $map-value {
      .#{$class} {
        #{$property}: $value;
      }
    }
  }
  // 透明度(opacity-5 至opacity-100，值为 0.05 - 1) / 层级（z-1 - z-20）
  @for $i from 0 through 20 {
    .opacity-#{$i * 5} {
      opacity: math.div($i, 20);
    }
    .z-#{$i * 10} {
      z-index: #{$i * 10};
    }

    .z-#{$i} {
      z-index: #{$i};
    }
  }
  // 单行文字超出省略号
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  // 两行文字超出省略号
  .overflow-ellipsis-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}